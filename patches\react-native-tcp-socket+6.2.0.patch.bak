diff --git a/node_modules/react-native-tcp-socket/android/build/.transforms/7e42c2dd1f5897a973aa8a9cbd394a95/results.bin b/node_modules/react-native-tcp-socket/android/build/.transforms/7e42c2dd1f5897a973aa8a9cbd394a95/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/.transforms/7e42c2dd1f5897a973aa8a9cbd394a95/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-tcp-socket/android/build/.transforms/7e42c2dd1f5897a973aa8a9cbd394a95/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-tcp-socket/android/build/.transforms/7e42c2dd1f5897a973aa8a9cbd394a95/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/generated/source/buildConfig/debug/com/asterinet/react/tcpsocket/BuildConfig.java b/node_modules/react-native-tcp-socket/android/build/generated/source/buildConfig/debug/com/asterinet/react/tcpsocket/BuildConfig.java
new file mode 100644
index 0000000..320a375
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/generated/source/buildConfig/debug/com/asterinet/react/tcpsocket/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.asterinet.react.tcpsocket;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.asterinet.react.tcpsocket";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..ea31b96
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.asterinet.react.tcpsocket" >
+
+    <uses-sdk android:minSdkVersion="26" />
+
+    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
+    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..dd9976d
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.asterinet.react.tcpsocket",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-tcp-socket/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-tcp-socket/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-tcp-socket/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-tcp-socket/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-tcp-socket/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..2d96e5b
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Jan 15 09:52:40 MYT 2025
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..19f0580
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..45b83cc
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\debug\jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..db031fc
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\debug\shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..0febb93
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\build\intermediates\shader_assets\debug\out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/BuildConfig.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/BuildConfig.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/KeystoreInfo.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/KeystoreInfo.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/ResolvableOption.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/ResolvableOption.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/SSLCertificateHelper$BlindTrustManager.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/SSLCertificateHelper$BlindTrustManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/SSLCertificateHelper.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/SSLCertificateHelper.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpEventListener.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpEventListener.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocket.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocket.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient$1.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient$1.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient$TcpReceiverTask.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient$TcpReceiverTask.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketClient.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$1.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$1.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$2.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$2.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$3.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$3.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$4.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$4.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$5.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$5.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$6.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$6.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$CurrentNetwork.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule$CurrentNetwork.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketModule.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketPackage.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketPackage.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketServer$TcpListenTask.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketServer$TcpListenTask.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketServer.class b/node_modules/react-native-tcp-socket/android/build/intermediates/javac/debug/classes/com/asterinet/react/tcpsocket/TcpSocketServer.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-tcp-socket/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-tcp-socket/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..e79d795
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,14 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.asterinet.react.tcpsocket" >
+4
+5    <uses-sdk android:minSdkVersion="26" />
+6
+7    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
+7-->C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:3:5-79
+7-->C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:3:22-76
+8    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
+8-->C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:4:5-79
+8-->C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:4:22-76
+9
+10</manifest>
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-tcp-socket/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..ea31b96
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.asterinet.react.tcpsocket" >
+
+    <uses-sdk android:minSdkVersion="26" />
+
+    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
+    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-tcp-socket/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-tcp-socket/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-tcp-socket/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..e679dce
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+com.asterinet.react.tcpsocket
diff --git a/node_modules/react-native-tcp-socket/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-tcp-socket/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..fc44beb
--- /dev/null
+++ b/node_modules/react-native-tcp-socket/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:1:1-5:12
+INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:1:1-5:12
+	package
+		ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:2:11-50
+		INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml
+	xmlns:android
+		ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:1:11-69
+uses-permission#android.permission.ACCESS_NETWORK_STATE
+ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:3:5-79
+	android:name
+		ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:3:22-76
+uses-permission#android.permission.CHANGE_NETWORK_STATE
+ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:4:5-79
+	android:name
+		ADDED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml:4:22-76
+uses-sdk
+INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml
+INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from C:\Users\<USER>\Documents\koodooapps\koodoo-merchant-v2-sunmi\node_modules\react-native-tcp-socket\android\src\main\AndroidManifest.xml
diff --git a/node_modules/react-native-tcp-socket/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-tcp-socket/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-tcp-socket/react-native-tcp-socket.podspec b/node_modules/react-native-tcp-socket/react-native-tcp-socket.podspec
index ed7e37b..c7b81d5 100644
--- a/node_modules/react-native-tcp-socket/react-native-tcp-socket.podspec
+++ b/node_modules/react-native-tcp-socket/react-native-tcp-socket.podspec
@@ -16,6 +16,6 @@ Pod::Spec.new do |s|
   s.requires_arc = true
 
   s.dependency "React-Core"
-  s.dependency "CocoaAsyncSocket"
+  s.dependency "CocoaAsyncSocket", :modular_headers => true
 
 end
