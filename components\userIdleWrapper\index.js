
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    PanResponder,
    Platform,
} from 'react-native';
// import FastImage from 'react-native-fast-image';
// import Colors from '../../constant/Colors';
// import { getCachedUrlContent, getImageFromFirebaseStorage } from '../../util/common';
// import { useFocusEffect } from '@react-navigation/native';
import UserInactivity from '@conodene/react-native-user-inactivity';
import API from '../../constant/API';
import { ROLE_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE_PARSED } from '../../constant/common';
import { CommonStore } from '../../store/commonStore';
import { MerchantStore } from '../../store/merchantStore';
import { UserStore } from '../../store/userStore';
import ApiClient from '../../util/ApiClient';
import * as User from '../../util/User';
import { ScrollView, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { isOutletDisplay, logToFile } from '../../util/common';
import moment from 'moment';
import { OutletStore } from '../../store/outletStore';
import TablePaymentSummary from '../../screen/components/tablePaymentSummary';
import AsyncImage from '../asyncImage';

const UserIdleWrapper = props => {
    const {
        // source,
        // item,
        // style,
        // hideLoading,

        disabled,
        screenName,

        children,
    } = props;

    console.log(`userIdleWrapper - ${screenName ? screenName : 'N/A'}`);
    console.log(`disabled: ${disabled}`);

    // const panResponder = React.useRef(
    //     PanResponder.create({
    //         onStartShouldSetPanResponderCapture: () => {
    //             // if (global.uanI > 0) {
    //             //     resetInactivityTimeout();
    //             // }
    //             resetInactivityTimeout();
    //         },
    //     })
    // ).current;

    const firebaseUid = UserStore.useState(s => s.firebaseUid);
    const merchantId = UserStore.useState(s => s.merchantId);
    const role = UserStore.useState(s => s.role);

    const currOutlet = MerchantStore.useState(s => s.currOutlet);
    const currOutletId = MerchantStore.useState(s => s.currOutletId);

    const merchantLogo = MerchantStore.useState((s) => s.logo);

    const isUserActive = CommonStore.useState(s => s.isUserActive);

    const odData = OutletStore.useState(s => s.odData);

    console.log(`isUserActive: ${isUserActive}`);

    useEffect(() => {
        if (!isUserActive) {
            logToFile('user not active | sign out');

            if (
                (global.currOutlet && global.currOutlet.noSignoutI)
                ||
                global.noSignoutI
            ) {
                // do nothing

                global.udiData.nis1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                logToFile(`UIW | no idle signout 1`);
            }
            else {
                global.udiData.is1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                logToFile(`UIW | idle signout 1`);

                signOutUser();
            }
        }
    }, [isUserActive]);

    ///////////////////////////////////////////////

    const signOutUser = async () => {
        UserStore.update((s) => {
            s.avatar = '';
            s.dob = null;
            s.email = '';
            s.gender = '';
            s.name = '';
            s.number = '';
            s.outletId = '';
            s.race = '';
            s.state = '';
            s.uniqueName = '';
            s.updatedAt = null;
            s.merchantId = '';
            s.role = '';
            s.refreshToken = '';
            s.firebaseUid = '';
            s.privileges = [];
            s.screensToBlock = [];
        });

        const merchantId = await AsyncStorage.getItem('merchantId');
        const currOutletId = await AsyncStorage.getItem('currOutletId');

        // clock out employee
        const bodyClockOut = {
            employeeId: firebaseUid,
            logoutTime: Date.now(),

            merchantId,
            outletId: currOutletId,
        };

        if (role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) {
            // 2022-06-16 - No need first
            // ApiClient.POST(API.updateUserClockInOut, bodyClockOut).then(
            //     (result) => {
            //         console.log('updateUserClockOut', result);
            //     },
            // );
        }
        else {
            // if waiter, clock out manually outside
        }

        const tokenFcm = await AsyncStorage.getItem('tokenFcm');

        await AsyncStorage.multiRemove([
            'accessToken',
            'userData',
            'refreshToken',

            'merchantLogoUrl',

            // 'isAskedBluetooth',

            // 'lastActivity',
        ]);

        global.signInAlready = false;

        const body = {
            role,
            merchantId,
            outletId: currOutlet.uniqueId,
            tokenFcm,
        };

        // Token.clear();
        // User.setlogin(false);
        // User.getRefreshMainScreen();

        logToFile('App.js | signOutUser');

        ApiClient.POST(API.logoutUser, body).then((result) => {
            User.setlogin(false);
            User.setMerchantId(null);
            User.setUserData(null);
            User.setUserId(null);
            User.setRefreshToken(null);
            User.setOutletId(null);
            User.getRefreshMainScreen();

            // CommonStore.replace(initialCommonStore);
            // MerchantStore.replace(initialMerchantStore);
            // OutletStore.replace(initialOutletStore);
            // NotificationStore.replace(initialNotificationStore);
            // UserStore.replace(initialUserStore);
            // PageStore.replace(initialPageStore);

            global.funcSwitchShowApp(false);
        });
    };

    ///////////////////////////////////////////////

    const resetInactivityTimeout = () => {
        if (global.uanI > 0) {
            global.isUserActiveNow = true;

            if (global.uanTimerId) {
                clearTimeout(global.uanTimerId);
            }

            global.uanTimerId = setTimeout(() => {
                global.isUserActiveNow = false;

                console.log(`global.isUserActiveNow: ${global.isUserActiveNow}`);
            }, global.uanI * 1000);
        }
    };

    ///////////////////////////////////////////////

    return (
        // <>
        //     {
        //         (!disabled
        //             && currOutlet
        //             && currOutlet.userIdleSignOutOptions
        //             && currOutlet.userIdleSignOutOptions !== USER_IDLE_SIGN_OUT_EVENT_TYPE.NEVER)
        //             ?
        //             <UserInactivity
        //                 isActive={isUserActive}
        //                 timeForInactivity={(USER_IDLE_SIGN_OUT_EVENT_TYPE_PARSED[currOutlet.userIdleSignOutOptions] || 30) * 60 * 1000}
        //                 // timeForInactivity={5000}
        //                 onAction={isActive => {
        //                     CommonStore.update(s => {
        //                         s.isUserActive = isActive;
        //                     });
        //                 }}
        //                 onStartShouldSetPanResponderCaptureCallback={resetInactivityTimeout}
        //             >
        //                 {children}
        //             </UserInactivity>
        //             :
        //             <>
        //                 {
        //                     disabled
        //                         ?
        //                         <></>
        //                         :
        //                         <>
        //                             {
        //                                 global.uanI > 0
        //                                     // false
        //                                     ?
        //                                     <UserInactivity
        //                                         isActive
        //                                         timeForInactivity={24 * 60 * 1000}
        //                                         // timeForInactivity={5000}
        //                                         onAction={isActive => {

        //                                         }}
        //                                         onStartShouldSetPanResponderCaptureCallback={resetInactivityTimeout}
        //                                     >
        //                                         {children}
        //                                     </UserInactivity>
        //                                     :
        //                                     <>{children}</>
        //                             }
        //                         </>
        //                 }
        //             </>
        //     }
        // </>
        <>
            {
                isOutletDisplay()
                    ?
                    <>
                        {
                            (odData && (odData.renderPaymentSummary || odData.renderReceipt))
                                ?
                                <>
                                    {children}
                                </>
                                :
                                <ScrollView
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                    }}
                                    contentContainerStyle={{
                                        width: '100%',
                                        height: '100%',

                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                    {merchantLogo ?
                                        <AsyncImage
                                            source={{ uri: merchantLogo }}
                                            style={{
                                                width: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                height: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                marginBottom: 10,
                                                marginTop: 0,
                                                alignSelf: 'center',
                                            }} />
                                        :
                                        <Image
                                            style={[{
                                                width: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                height: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                            }]}
                                            resizeMode="contain"
                                            source={require('../../assets/image/logo.png')}
                                        />
                                    }
                                </ScrollView>
                        }
                    </>
                    :
                    <>{children}</>
            }
        </>
    );
};

export default UserIdleWrapper;