import React, { useState, Component, useEffect } from 'react'

import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, KeyboardAvoidingView, Dimensions, ActivityIndicator } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'
import ResetPasswordScreen from './ResetPasswordScreen';
import CheckBox from '@react-native-community/checkbox';
//import CheckBox from 'react-native-check-box';
import Switch from 'react-native-switch-pro';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import NetInfo from "@react-native-community/netinfo";
import { APP_TYPE } from '../constant/common';
import { isTablet } from 'react-native-device-detection';
import Orientation from 'react-native-orientation-locker';
import { fixRNDimensions } from '../util/common';

const LOGIN_SCREEN_STATE = {
    LOGIN: 0,
    RESET_PASSWORD: 1,
};

const LoginScreen = props => {
    const {
        checkLogin,
    } = props;

    // this.goToLoginState = this.goToLoginState.bind(this);

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [checked, setChecked] = useState(false);
    const [loadingModal, setLoadingModal] = useState(false);
    const [screenState, setScreenState] = useState(LOGIN_SCREEN_STATE.LOGIN);

    const switchMerchant = CommonStore.useState(s => s.switchMerchant);
    const isAuthenticating = CommonStore.useState(s => s.isAuthenticating);

    // useEffect(() => {
    //     Orientation.lockToPortrait(fixRNDimensions);
    // }, []);

    // function here
    const login = async () => {
        if (!email || !password) {
            Alert.alert('Login failed', "Empty email or password", [
                { text: "OK", onPress: () => { } }
            ],
                { cancelable: false });

            return;
        }

        NetInfo.fetch().then(async state => {
            console.log("Connection type", state.type);
            console.log("Is connected?", state.isInternetReachable);
            console.log(state);

            if (state.isInternetReachable) {
                // setLoading(true);
                CommonStore.update(s => {
                    s.isAuthenticating = true;
                });
                try {
                    let res = await auth().signInWithEmailAndPassword(
                        email,
                        password,
                    );

                    let user = res.user;
                    let firebaseToken = await user.getIdToken();

                    await AsyncStorage.setItem('firebaseToken', firebaseToken);

                    // ApiClient.GET(API.getToken + firebaseToken).then(async (result) => {
                    ApiClient.GET(API.getToken + firebaseToken + '&app=' + APP_TYPE.WAITER).then(async (result) => {
                        // setLoading(true);

                        if (result.merchantId) {
                            if (result.token) {
                                setLoadingModal(true)

                                Token.setToken(result.token);
                                Token.setRefreshToken(result.refreshToken);
                                await AsyncStorage.setItem('accessToken', result.token);
                                ApiClient.GET(API.userAdmin).then(async (userData) => {
                                    User.setUserData(userData);
                                    User.setName(userData.name);
                                    User.setRefreshToken(userData.refreshToken);
                                    User.setUserId(userData.firebaseUid);
                                    User.setlogin(true);
                                    User.setMerchantId(userData.merchantId);
                                    User.setOutletId(userData.outletId);

                                    AsyncStorage.setItem(
                                        'email',
                                        email
                                    );
                                    AsyncStorage.setItem(
                                        'password',
                                        password
                                    );
                                    AsyncStorage.setItem(
                                        'loggedIn',
                                        "true"
                                    );
                                    AsyncStorage.setItem(
                                        'userData',
                                        JSON.stringify(userData)
                                    );

                                    ////////////////////////////////////

                                    await AsyncStorage.setItem(
                                        'merchantId',
                                        userData.merchantId
                                    );

                                    await AsyncStorage.setItem(
                                        'refreshToken',
                                        userData.refreshToken
                                    );

                                    await AsyncStorage.setItem(
                                        'role',
                                        userData.role
                                    );

                                    await AsyncStorage.setItem(
                                        'firebaseUid',
                                        userData.firebaseUid
                                    );

                                    UserStore.update(s => {
                                        s.firebaseUid = userData.firebaseUid;
                                        s.merchantId = userData.merchantId;

                                        s.role = userData.role;
                                    });

                                    MerchantStore.update(s => {
                                        s.currOutletId = userData.outletId;
                                    });

                                    // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                                    //     User.setQueueStatus(result.queueStatus);
                                    //     User.setRefreshCurrentAction(result.reservationStatus);

                                    //     checkLogin(true);
                                    // });

                                    checkLogin(true);

                                    CommonStore.update(s => {
                                        s.isAuthenticating = true;
                                    });
                                });
                            }
                            else {
                                Alert.alert('Login failed', "Invalid merchant account", [
                                    { text: "OK", onPress: () => { } }
                                ],
                                    { cancelable: false });

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });
                            }
                        } else {
                            Alert.alert('Login failed', "Invalid merchant account", [
                                { text: "OK", onPress: () => { } }
                            ],
                                { cancelable: false });

                            CommonStore.update(s => {
                                s.isAuthenticating = false;
                            });
                        }
                    });

                } catch (error) {
                    console.error(error);

                    Alert.alert('Login failed', "Invalid email or password", [
                        { text: "OK", onPress: () => { } }
                    ],
                        { cancelable: false });
                    // setLoading(false);
                    CommonStore.update(s => {
                        s.isAuthenticating = false;
                    });
                }
            }
            else {
                Alert.alert('Error', 'Unable to connect to the Internet');

                CommonStore.update(s => {
                    s.isAuthenticating = false;
                });
            }
        });
    }

    const goToLoginState = () => {
        setScreenState(LOGIN_SCREEN_STATE.LOGIN);
    }

    // function end    

    return (
        <>
            {
                screenState === LOGIN_SCREEN_STATE.LOGIN &&
                <View style={styles.container}>
                    <View style={{ paddingHorizontal: 30, flex: 1, justifyContent: "center" }}>
                        <View style={{ alignSelf: 'center', width: '100%' }}>
                            <Image style={styles.logo} resizeMode="contain" source={require('../assets/image/logo_2.png')} />
                            <Text style={styles.logoTxt}>Unlimited Perks</Text>
                            <Text style={styles.loginTxt}>Login</Text>
                            <Text style={styles.description}>Please login to continue</Text>

                            <TextInput
                                editable={!isAuthenticating}
                                underlineColorAndroid={Colors.fieldtBgColor}
                                clearButtonMode='while-editing'
                                style={styles.textInput}
                                placeholder="Email"
                                keyboardType='email-address'
                                autoCapitalize="none"
                                autoCompleteType="off"
                                onChangeText={(text) => { setEmail(text.trim()) }}
                                value={email}
                            />
                            <TextInput
                                editable={!isAuthenticating}
                                underlineColorAndroid={Colors.fieldtBgColor}
                                clearButtonMode='while-editing'
                                style={styles.textInput}
                                placeholder="Password"
                                secureTextEntry={true}
                                onChangeText={(text) => { setPassword(text.trim()) }}
                                value={password}
                            />
                            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 20 }}>
                                <CheckBox
                                    style={[styles.checkBox, { transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }]}
                                    //center
                                    //rightText='Remember me'
                                    checkedIcon='dot-circle-o'
                                    uncheckedIcon='circle-o'
                                    checked={checked}
                                // checkBoxColor={Colors.fieldtBgColor}
                                // uncheckedCheckBoxColor={Colors.tabGrey}
                                // checkedCheckBoxColor={Colors.primaryColor}
                                // isChecked={checked}
                                // onClick={() => {setChecked(!checked) }}
                                />
                                <Text style={{
                                    fontSize: 15,
                                    color: Colors.descriptionColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 0,
                                    marginTop: -2,
                                }}>
                                    Remember me
                                </Text>
                            </View>


                            <Modal
                                supportedOrientations={['landscape', 'portrait']}
                                style={{ flex: 1 }}
                                visible={loadingModal}
                                transparent={true}
                                animationType={'slide'}
                            >
                                <View style={styles.modalContainer}>
                                    <View style={styles.modalViewImport}>
                                        <ActivityIndicator color={Colors.whiteColor} size={'large'} />
                                    </View>
                                </View>
                            </Modal>

                            <KeyboardAvoidingView behavior="padding">

                                <TouchableOpacity disabled={isAuthenticating} onPress={() => { login(); }}>
                                    <View style={[Styles.button, {
                                        marginTop: 50,
                                        marginBottom: 40,
                                    }]}>
                                        {isAuthenticating ?
                                            <Text style={{
                                                color: '#ffffff',
                                                fontSize: 22,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                LOADING...
                                            </Text>
                                            :
                                            <Text style={{
                                                color: '#ffffff',
                                                fontSize: 22,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                LOGIN
                                            </Text>
                                        }
                                    </View>
                                </TouchableOpacity>

                                <View style={styles.resetContainer}>
                                    <Text style={{
                                        color: Colors.fieldtTxtColor,
                                        fontSize: 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}>Forgot Password? </Text>
                                    <TouchableOpacity disabled={isAuthenticating} onPress={() => { setScreenState(LOGIN_SCREEN_STATE.RESET_PASSWORD) }}>
                                        <Text style={{
                                            color: Colors.primaryColor,
                                            fontSize: 15,
                                            fontFamily: 'NunitoSans-Regular',
                                        }}>Reset it</Text>
                                    </TouchableOpacity>
                                </View>

                                <View style={[styles.resetContainer, {
                                    marginTop: 20,
                                }]}>
                                    {/* <Text style={{
                                        color: Colors.fieldtTxtColor,
                                        fontSize: 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}>Forgot Password? </Text> */}
                                    <TouchableOpacity disabled={isAuthenticating} onPress={async () => {
                                        // setScreenState(LOGIN_SCREEN_STATE.RESET_PASSWORD)                                        

                                        await AsyncStorage.setItem('switchMerchant', '1');

                                        CommonStore.update(s => {
                                            s.switchMerchant = true;
                                        });
                                    }}>
                                        <Text style={{
                                            color: Colors.primaryColor,
                                            fontSize: 15,
                                            fontFamily: 'NunitoSans-Regular',
                                        }}>Switch To Merchant</Text>
                                    </TouchableOpacity>
                                </View>

                            </KeyboardAvoidingView>
                        </View>
                    </View>

                    {/* <View style={[styles.switchContainer, {
                        bottom: Dimensions.get('window').height * 0.05,
                    }]}>
                        <Switch
                            onSyncPress={
                                async (value) => {
                                    CommonStore.update(s => {
                                        s.switchMerchant = true;
                                    });

                                    await AsyncStorage.setItem('switchMerchant', '1');
                                }
                            }
                            // onChange={() => this.switchQueueStatus(this)}
                            value={switchMerchant}
                            circleColorActive={Colors.primaryColor}
                            circleColorInactive={Colors.fieldtTxtColor}
                            backgroundActive='#dddddd'
                        />
                    </View> */}
                </View>
            }
            {screenState === LOGIN_SCREEN_STATE.RESET_PASSWORD &&
                <ResetPasswordScreen goToLoginState={goToLoginState} />
            }
        </>

        // <View style={styles.container}>
        //             <View style={{ paddingHorizontal: 30, flex: 1, justifyContent: "center" }}>
        //                 <View style={{ alignSelf: 'center', width: '100%' }}>
        //                     <Image style={styles.logo} resizeMode="contain" source={require('../assets/image/logo_2.png')} />
        //                     <Text style={styles.logoTxt}>Unlimited Perks</Text>
        //                     <Text style={styles.loginTxt}>Login</Text>
        //                     <Text style={styles.description}>Please login to continue</Text>
        //                     <TextInput
        //                         editable={!this.state.loading}
        //                         underlineColorAndroid={Colors.fieldtBgColor}
        //                         clearButtonMode='while-editing'
        //                         style={styles.textInput}
        //                         placeholder="Email"
        //                         keyboardType='email-address'
        //                         autoCapitalize="none"
        //                         autoCompleteType="off"
        //                         onChangeText={(text) => { this.setState({ email: text.trim() }) }}
        //                         value={this.state.email}
        //                     />
        //                     <TextInput
        //                         editable={!this.state.loading}
        //                         underlineColorAndroid={Colors.fieldtBgColor}
        //                         clearButtonMode='while-editing'
        //                         style={styles.textInput}
        //                         placeholder="Password"
        //                         secureTextEntry={true}
        //                         onChangeText={(text) => { this.setState({ password: text.trim() }) }}
        //                         value={this.state.password}
        //                     />
        //                     <KeyboardAvoidingView behavior="padding">
        //                         <TouchableOpacity disabled={this.state.loading} onPress={() => { this.login() }}>
        //                             <View style={[Styles.button, { marginTop: 50 }]}>
        //                                 <Text style={{ color: '#ffffff', fontSize: 18 }}>{this.state.loading ? "LOADING..." : "LOGIN"}</Text>
        //                             </View>
        //                         </TouchableOpacity>

        //                         <View style={styles.resetContainer}>
        //                             <Text style={{ color: Colors.fieldtTxtColor }}>Forgot Password? </Text>
        //                             <TouchableOpacity disabled={this.state.loading} 
        //                             // onPress={() => { this.setState({ screenState: LOGIN_SCREEN_STATE.RESET_PASSWORD }) }}
        //                             >
        //                                 <Text style={{ color: Colors.primaryColor }}>Reset it</Text>
        //                             </TouchableOpacity>
        //                         </View>

        //                     </KeyboardAvoidingView>
        //                 </View>
        //             </View>
        //         </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row'
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
        marginTop: 10,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
        fontFamily: 'NunitoSans-Regular',
    },
    loginTxt: {
        color: Colors.mainTxtColor,
        // fontWeight: "500",
        fontSize: 26,
        fontFamily: 'NunitoSans-Bold',
    },
    description: {
        color: Colors.descriptionColor,
        paddingVertical: 10,
        fontFamily: 'NunitoSans-Regular',
        fontSize: 16,
        marginBottom: 5,
        marginTop: -5,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 8,
        marginTop: 20,
        fontFamily: 'NunitoSans-Regular',
        fontSize: 16,
    },
    checkBox: {
        //borderWidth: StyleSheet.hairlineWidth,
        //borderColor: Colors.descriptionColor,

        //width: 30,
        //height: 10,
        //borderRadius: 10,
        //justifyContent: 'center',
        //alignItems: 'center',

        // marginRight: 5,
    },
    floatbtn: {
        zIndex: 1,
        position: 'absolute',
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3
    },
    loginImg: {
        width: undefined,
        height: '100%',
        resizeMode: 'cover'
    },
    resetContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 0,
        alignSelf: 'center'
    },

    switchContainer: {
        position: 'absolute',
        bottom: 0,
        display: 'flex',
        alignItems: 'center',
        width: '100%',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Dimensions.get('screen').width * 0.2,
        width: Dimensions.get('screen').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
})

export default LoginScreen;