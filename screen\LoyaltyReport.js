import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import GCalendar from '../assets/svg/GCalendar';
import FusionCharts from 'react-native-fusioncharts';

import {
  CHART_DATA,
  CHART_TYPE,
  FS_LIBRARY_PATH,
  CHART_Y_AXIS_DROPDOWN_LIST,
  CHART_FIELD_COMPARE_DROPDOWN_LIST,
  CHART_FIELD_NAME_DROPDOWN_LIST,
  CHART_FIELD_TYPE,
  CHART_FIELD_COMPARE_DICT,
  CHART_PERIOD,
} from '../constant/chart';
import {
  filterChartItems,
  getDataForChartDashboardTodaySales,
  getDataForSalesLineChart,
} from '../util/chart';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { autoScroll } from '@shopify/flash-list';
import { TempStore } from "../store/tempStore";

const LoyaltyReport = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  // const setState = () => { };

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // Reservation or Loyalty page useState
  const [isReservationPage, setIsReservationPage] = useState(false);

  // traffic drop down
  const [
    selectedChartDropdownValueLineChart,
    setSelectedChartDropdownValueLineChart,
  ] = useState(
    CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value,
  );

  // Reservation page useState
  const [guestNum, setGuestNum] = useState(46);
  const [estimatedRevenue, setEstimatedRevenue] = useState(23000.0);
  const [reservationsNum, setReservationsNum] = useState(19);
  const [walksInNum, setWalksInNum] = useState(0);
  const [cancellationsNum, setCancellationsNum] = useState(0);
  const [noShowsNum, setNoShowsNum] = useState(0);

  // traffic table data
  const [salesLineChart, setSalesLineChart] = useState({});

  const eventsChart = {
    dataPlotClick: (e, item) => {
      // console.log('test data plot');
    },
  };

  // search bar useState
  const [search, setSearch] = useState('');

  // Date time picker useState
  const [showDateTimeStartPicker, setShowDateTimeStartPicker] = useState(false);
  const [showDateTimeEndPicker, setShowDateTimeEndPicker] = useState(false);

  // Date time useState
  const [revStartDate, setRevStartDate] = useState(moment().startOf('month'));
  const [revEndDate, setRevEndDate] = useState(moment().endOf('month'));

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const [loyaltyCampaignDataDict, setLoyaltyCampaignDataDict] = useState({});
  const [loyaltyCampaignDataSummary, setLoyaltyCampaignDataSummary] = useState({
    loyaltyCampaignId: '',

    smsSent: 0,
    smsDelivered: 0,
    voucherSent: 0, // sent sms/voucher

    delivered: 0, // actual delivered sms to customer phone

    guestVisits: 0, // number of guest visits (even didn't use voucher)
    visitRate: 0,
    revenue: 0, // total spent of those guests that using voucher + didn't used voucher

    voucherRedeemTimes: 0, // voucher redemption times
    voucherRedeemRate: 0,
  });

  const [loyaltyCampaigns, setLoyaltyCampaigns] = useState([]);

  // Navigation bar
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const userName = UserStore.useState((s) => s.name);

  const loyaltyCampaignsRaw = OutletStore.useState(s => s.loyaltyCampaigns);
  const userTaggableVouchers = OutletStore.useState(s => s.userTaggableVouchers);
  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);
  // const allOutletsUserOrdersDone = OutletStore.useState(s => s.allOutletsUserOrdersDone);
  const allOutletsUserOrdersDone = TempStore.useState(s => s.allOutletUserOrderDoneProcessed);

  const currOutletId = MerchantStore.useState(s => s.currOutletId);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  /////////////////////////////////////////////////

  useEffect(() => {
    let loyaltyCampaignsTemp = [];

    loyaltyCampaignsTemp = loyaltyCampaignsRaw.filter(campaign => {
      if (search !== '') {
        const lowercaseSearch = search.toLocaleLowerCase();
        if (campaign.campaignName.toLocaleLowerCase().includes(lowercaseSearch)) {
          return true;
        }
        else if (campaign.campaignDescription.toLocaleLowerCase().includes(lowercaseSearch)) {
          return true;
        }
        else {
          return false;
        }
      }
      else {
        return true;
      }
    });

    setLoyaltyCampaigns(loyaltyCampaignsTemp);
  }, [loyaltyCampaignsRaw, search]);

  useEffect(() => {
    var loyaltyCampaignDataDictTemp = {};
    var loyaltyCampaignDataSummaryTemp = {
      loyaltyCampaignId: '',

      smsSent: 0,
      smsDelivered: 0,
      voucherSent: 0, // sent sms/voucher

      delivered: 0, // actual delivered sms to customer phone

      guestVisits: 0, // number of guest visits (even didn't use voucher)
      visitRate: 0,
      revenue: 0, // total spent of those guests that using voucher + didn't used voucher

      voucherRedeemTimes: 0, // voucher redemption times
      voucherRedeemRate: 0,
    };

    if (currOutletId) {
      var filteredUserOrders = allOutletsUserOrdersDone.filter((order) => {
        if (
          order.outletId === currOutletId &&
          moment(historyStartDate).isBefore(order.completedDate) &&
          moment(historyEndDate).isAfter(order.completedDate)
        ) {
          return true;
        } else {
          return false;
        }
      });

      for (var i = 0; i < loyaltyCampaigns.length; i++) {
        if (loyaltyCampaigns[i].campaignName) {
          var loyaltyCampaignData = {
            loyaltyCampaignId: loyaltyCampaigns[i].uniqueId,

            smsSent: 0,
            smsDelivered: 0,
            voucherSent: 0, // sent sms/voucher

            delivered: 0, // actual delivered sms to customer phone

            guestVisits: (loyaltyCampaigns[i].vGuestNum !== undefined) ? loyaltyCampaigns[i].vGuestNum : 0, // number of guest visits (even didn't use voucher)
            visitRate: 0,
            revenue: (loyaltyCampaigns[i].vRevenue !== undefined) ? loyaltyCampaigns[i].vRevenue : 0, // total spent of those guests that using voucher + didn't used voucher

            voucherRedeemTimes: 0, // voucher redemption times
            voucherRedeemRate: 0,
          };

          var linkedTaggableVoucher = taggableVouchers.find(voucher => voucher.uniqueId === loyaltyCampaigns[i].taggableVoucherId);

          if (linkedTaggableVoucher) {
            loyaltyCampaignData.smsSent = (loyaltyCampaigns[i].smsSent !== undefined) ? loyaltyCampaigns[i].smsSent : 0;
            // loyaltyCampaignData.smsDelivered = (linkedTaggableVoucher.smsSent !== undefined) ? linkedTaggableVoucher.smsSent : 0;
            loyaltyCampaignData.smsDelivered = (loyaltyCampaigns[i].smsDelivered !== undefined) ? loyaltyCampaigns[i].smsDelivered : 0;
            loyaltyCampaignData.voucherSent = (isFinite(linkedTaggableVoucher.voucherQuantityInitial) && isFinite(linkedTaggableVoucher.voucherQuantity)) ? (linkedTaggableVoucher.voucherQuantityInitial - linkedTaggableVoucher.voucherQuantity) : 0;

            loyaltyCampaignData.guestVisits += (linkedTaggableVoucher.cRedeem !== undefined) ? linkedTaggableVoucher.cRedeem : 0;

            loyaltyCampaignData.revenue += (linkedTaggableVoucher.cRevenue !== undefined) ? linkedTaggableVoucher.cRevenue : 0;

            loyaltyCampaignData.voucherRedeemTimes = (linkedTaggableVoucher.cRedeem !== undefined) ? linkedTaggableVoucher.cRedeem : 0;

            if (loyaltyCampaignData.voucherSent > 0) {
              loyaltyCampaignData.visitRate = (loyaltyCampaignData.guestVisits / loyaltyCampaignData.voucherSent * 100);
              loyaltyCampaignData.voucherRedeemRate = (loyaltyCampaignData.voucherRedeemTimes / loyaltyCampaignData.voucherSent * 100);
            }

            // if (linkedTaggableVoucher.cRedeem !== undefined &&
            //   isFinite(linkedTaggableVoucher.cRedeem)) {
            //   loyaltyCampaignData.guestVisits = linkedTaggableVoucher.cRedeem;
            // }
            // else {
            //   loyaltyCampaignData.guestVisits = filteredUserOrders.filter(userOrder => {
            //     if (userOrder.taggableVoucherId === linkedTaggableVoucher.uniqueId) {
            //       return true;
            //     }
            //     else {
            //       return false;
            //     }
            //   }).length;
            // }

            // if (linkedTaggableVoucher.cRevenue !== undefined &&
            //   isFinite(linkedTaggableVoucher.cRevenue)) {
            //   loyaltyCampaignData.revenue = linkedTaggableVoucher.cRevenue;
            // }
            // else {
            //   loyaltyCampaignData.revenue = filteredUserOrders.filter(userOrder => userOrder.taggableVoucherId === linkedTaggableVoucher.uniqueId)
            //     .reduce((accum, userOrder) => accum + userOrder.finalPrice, 0);
            // }
          }
          else {
            if (loyaltyCampaignData.voucherSent > 0) {
              loyaltyCampaignData.visitRate = (loyaltyCampaignData.guestVisits / loyaltyCampaignData.voucherSent * 100);
              loyaltyCampaignData.voucherRedeemRate = (loyaltyCampaignData.voucherRedeemTimes / loyaltyCampaignData.voucherSent * 100);
            }

            // loyaltyCampaignData.voucherSent = userTaggableVouchers.filter(voucher => voucher.loyaltyCampaignId === loyaltyCampaigns[i].uniqueId).length;
            // loyaltyCampaignData.guestVisits = filteredUserOrders.filter(userOrder => userOrder.loyaltyCampaignId === loyaltyCampaigns[i].uniqueId).length;

            // loyaltyCampaignData.revenue = filteredUserOrders.filter(userOrder => userOrder.loyaltyCampaignId === loyaltyCampaigns[i].uniqueId)
            //   .reduce((accum, userOrder) => accum + userOrder.finalPrice, 0);
          }

          // if (loyaltyCampaignData.guestVisits > 0) {
          //   loyaltyCampaignData.visitRate = +(loyaltyCampaignData.guestVisits / loyaltyCampaignData.delivered * 100).toFixed(2);
          // }

          // loyaltyCampaignData.revenue = filteredUserOrders.filter(userOrder => userOrder.loyaltyCampaignId === loyaltyCampaigns[i].uniqueId)
          //   .reduce((accum, userOrder) => accum + userOrder.finalPrice, 0);

          loyaltyCampaignDataDictTemp[loyaltyCampaignData.loyaltyCampaignId] = loyaltyCampaignData;

          /////////////////////////////////////////////

          loyaltyCampaignDataSummaryTemp.smsSent += loyaltyCampaignData.smsSent;
          loyaltyCampaignDataSummaryTemp.smsDelivered += loyaltyCampaignData.smsDelivered;
          loyaltyCampaignDataSummaryTemp.voucherSent += loyaltyCampaignData.voucherSent;

          loyaltyCampaignDataSummaryTemp.guestVisits += loyaltyCampaignData.guestVisits;

          loyaltyCampaignDataSummaryTemp.revenue += loyaltyCampaignData.revenue;

          loyaltyCampaignDataSummaryTemp.voucherRedeemTimes += loyaltyCampaignData.voucherRedeemTimes;

          if (loyaltyCampaignDataSummaryTemp.voucherSent > 0) {
            loyaltyCampaignDataSummaryTemp.visitRate = +(loyaltyCampaignDataSummaryTemp.guestVisits / loyaltyCampaignDataSummaryTemp.voucherSent * 100);
            loyaltyCampaignDataSummaryTemp.voucherRedeemRate = +(loyaltyCampaignDataSummaryTemp.voucherRedeemTimes / loyaltyCampaignDataSummaryTemp.voucherSent * 100);
          }

          // loyaltyCampaignDataSummaryTemp.delivered += loyaltyCampaignData.delivered;
          // loyaltyCampaignDataSummaryTemp.guestVisits += loyaltyCampaignData.guestVisits;
          // if (loyaltyCampaignDataSummaryTemp.guestVisits > 0) {
          //   loyaltyCampaignDataSummaryTemp.visitRate = +(loyaltyCampaignDataSummaryTemp.guestVisits / loyaltyCampaignDataSummaryTemp.delivered * 100).toFixed(2);
          // }
          // loyaltyCampaignDataSummaryTemp.revenue += loyaltyCampaignData.revenue;

          /////////////////////////////////////////////
        }
      }
    }

    setLoyaltyCampaignDataDict(loyaltyCampaignDataDictTemp);
    setLoyaltyCampaignDataSummary(loyaltyCampaignDataSummaryTemp);
  }, [
    currOutletId,

    loyaltyCampaigns,
    taggableVouchers,
    userTaggableVouchers,

    allOutletsUserOrdersDone,

    historyStartDate,
    historyEndDate,
  ]);

  /////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Loyalty Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const renderLoyalty = ({ item }) => {
    var loyaltyCampaignData = loyaltyCampaignDataDict[item.uniqueId] || {
      loyaltyCampaignId: '',

      smsSent: 0,
      smsDelivered: 0,
      voucherSent: 0, // sent sms/voucher

      delivered: 0, // actual delivered sms to customer phone

      guestVisits: 0, // number of guest visits (even didn't use voucher)
      visitRate: 0,
      revenue: 0, // total spent of those guests that using voucher + didn't used voucher

      voucherRedeemTimes: 0, // voucher redemption times
      voucherRedeemRate: 0,
    };

    if (loyaltyCampaignData.loyaltyCampaignId && item.campaignName) {
      return (
        <View style={styles.tableContentView}>
          <View style={{
            width: '20%',
          }}>
            <Text style={styles.tableContentTextFirst}>
              Active
            </Text>
            <Text style={styles.tableContentTextSecond}>
              {item.campaignName ? item.campaignName : 'N/A'}
            </Text>
            <Text style={styles.tableContentTextThird}>
              {item.campaignDescription ? item.campaignDescription : 'N/A'}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.smsSent}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`SMS`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.smsDelivered}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`SMS`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.voucherSent}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`Qty`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.guestVisits}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`Pax`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.visitRate.toFixed(1)}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`%`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.voucherRedeemTimes}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`Qty`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.voucherRedeemRate.toFixed(1)}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`%`}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', width: '10%' }}>
            <Text style={styles.tableContentTextBig}>
              {loyaltyCampaignData.revenue.toFixed(2)}
            </Text>
            <Text style={styles.tableContentTextSmall}>
              {`MYR`}
            </Text>
          </View>
        </View>
      );
    }
  }

  const renderLoyaltyInactive = ({ item }) => {
    return (
      <View style={styles.tableContentView}>

        <View>
          <Text style={styles.tableContentTextFirst2}>
            Inactive
          </Text>
          <Text style={styles.tableContentTextSecond}>
            Birthday
          </Text>
          <Text style={styles.tableContentTextThird}>
            7 days before birthday - 14 day expiration
          </Text>
        </View>

        <View style={{ justifyContent: 'center' }}>
          <Text style={styles.tableContentTextBig}>
            0
          </Text>
          <Text style={styles.tableContentTextSmall}>
            Total Delivered
          </Text>
        </View>
        <View style={{ justifyContent: 'center' }}>
          <Text style={styles.tableContentTextBig}>
            0
          </Text>
          <Text style={styles.tableContentTextSmall}>
            Total Visits
          </Text>
        </View>
        <View style={{ justifyContent: 'center' }}>
          <Text style={styles.tableContentTextBig}>
            0%
          </Text>
          <Text style={styles.tableContentTextSmall}>
            Visit Rate
          </Text>
        </View>
        <View style={{ justifyContent: 'center' }}>
          <Text style={styles.tableContentTextBig}>
            0
          </Text>
          <Text style={styles.tableContentTextSmall}>
            MYR Revenue
          </Text>
        </View>
      </View>
    )
  }

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>

        {/* Sidebar */}
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}

        {/* Modal start */}
        <DateTimePickerModal
          isVisible={showDateTimeStartPicker}
          mode={'date'}
          onConfirm={(text) => {
            // setRevStartDate(moment(text).valueOf());
            CommonStore.update(s => {
              s.historyStartDate = moment(text).valueOf();
            });
            setShowDateTimeStartPicker(false);
          }}
          onCancel={() => {
            setShowDateTimeStartPicker(false);
          }}
          maximumDate={moment(historyEndDate).toDate()}
        />

        <DateTimePickerModal
          isVisible={showDateTimeEndPicker}
          mode={'date'}
          onConfirm={(text) => {
            // setRevEndDate(moment(text));
            CommonStore.update(s => {
              s.historyEndDate = moment(text).valueOf();
            });
            setShowDateTimeEndPicker(false);
          }}
          onCancel={() => {
            setShowDateTimeEndPicker(false);
          }}
          minimumDate={moment(historyStartDate).toDate()}
        />
        {/* Modal End */}

        <View>
          {/* Top view */}
          <View style={[
            styles.topBar,
            switchMerchant
              ? {}
              : {
                height: windowHeight * 0.08,
                width: windowWidth * 0.92,
              }, {
                // display: 'none',
              }
          ]}>
            {/* <TouchableOpacity
            style={[
              styles.topBarButton,
              isReservationPage ? {} : { backgroundColor: Colors.whiteColor },
            ]}
            onPress={() => setIsReservationPage(true)}
          >
            <Text
              style={{
                textAlign: 'center',
              }}>
              Reservation
            </Text>
          </TouchableOpacity> */}

            {/* <TouchableOpacity
              style={[
                styles.topBarButton,
                isReservationPage ? { backgroundColor: Colors.whiteColor } : {},
              ]}
              onPress={() => setIsReservationPage(false)}
            >
              <Text
                style={{
                  textAlign: 'center',
                }}>
                Loyalty
              </Text>
            </TouchableOpacity> */}

            {/* Search Bar */}
            {/* Show or hide between the Page */}

            {!isReservationPage ?
              <View
                style={{
                  margin: 3,
                  backgroundColor: 'white',
                  borderRadius: 7,
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  height: 35,
                }}>
                <Icon
                  name="search"
                  size={13}
                  color={Colors.primaryColor}
                  style={{ marginLeft: 15 }}
                />
                <TextInput
                  style={[{
                    fontSize: 13,
                    fontFamily: 'NunitoSans-Regular',
                    paddingLeft: 5,
                    height: windowHeight * 0.18,
                  },
                  switchMerchant ? { width: 220 } : { width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowWidth * 0.38 : windowWidth * 0.43 },
                  ]}
                  clearButtonMode="while-editing"
                  placeholder=" Search for name / description"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  onChangeText={(text) => {
                    // setSearch(text.trim());
                    setSearch(text);
                  }}
                  value={search}
                />
              </View>
              : null}

            {/* Date time picker */}

            {/* <View
              style={{
                flexDirection: 'row',
                margin: switchMerchant ? 3 : 5,
                // marginLeft: 3.5,
                marginLeft: 'auto',
              }}>
              <View
                style={{
                  paddingHorizontal: 15,
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderRadius: 10,
                  paddingVertical: 7,
                  justifyContent: 'center',
                  backgroundColor: Colors.whiteColor,
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: 1,
                  height: 35,
                }}>
                <View
                  style={{ alignSelf: 'center', marginRight: 5 }}
                  onPress={() => {
                    setState({
                      pickerMode: 'date',
                      showDateTimePicker: true,
                    });
                  }}>
                  <GCalendar
                    width={switchMerchant ? 10 : 20}
                    height={switchMerchant ? 10 : 20}
                  />
                </View>
                <TouchableOpacity
                  onPress={() => {
                    setShowDateTimeStartPicker(true);
                    setShowDateTimeEndPicker(false);
                  }}
                  style={{
                    marginHorizontal: 4,
                  }}>
                  <Text
                    style={
                      switchMerchant
                        ? {
                          fontSize: 10,
                          fontFamily: 'NunitoSans-Regular',
                        }
                        : { fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14, fontFamily: 'NunitoSans-Regular' }
                    }>
                    {moment(historyStartDate).format('DD MMM yyyy')}
                  </Text>
                </TouchableOpacity>

                <Text
                  style={
                    switchMerchant
                      ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                      : { fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14, fontFamily: 'NunitoSans-Regular' }
                  }>
                  -
                </Text>

                <TouchableOpacity
                  onPress={() => {
                    setShowDateTimeStartPicker(false);
                    setShowDateTimeEndPicker(true);
                  }}
                  style={{
                    marginHorizontal: 4,
                  }}>
                  <Text
                    style={
                      switchMerchant
                        ? {
                          fontSize: 10,
                          fontFamily: 'NunitoSans-Regular',
                        }
                        : { fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14, fontFamily: 'NunitoSans-Regular' }
                    }>
                    {moment(historyEndDate).format('DD MMM yyyy')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View> */}
          </View>

          {/* Switch between reservation and loyalty page ternery */}
          {isReservationPage ?
            //  Reservation Page
            <View style={{}}>
              <ScrollView>
                {/* Top 2 view */}
                <View
                  style={{
                    flexDirection: 'row',
                  }}>

                  {/* Traffic table */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.6,
                      height: windowHeight * 0.4,
                      margin: 5,
                      justifyContent: 'space-around',
                      textAlign: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                    }}>
                    <View
                      style={{
                        paddingLeft: 10,
                      }}>
                      <Text>Traffic over time</Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignContent: 'center',
                      }}>
                      <Text
                        style={{
                          paddingLeft: 10,
                          paddingTop: 4,
                          color: 'gray',
                          textAlign: 'center',
                        }}>Guest</Text>
                      <Text
                        style={{
                          paddingLeft: 10,
                          paddingTop: 4,
                          fontSize: 15,
                          textAlign: 'center',
                        }}>{guestNum}</Text>
                      <Text
                        style={{
                          paddingLeft: 20,
                          paddingTop: 4,
                          color: 'gray',
                          textAlign: 'center',
                        }}>Estimated revenue</Text>
                      <Text
                        style={{
                          paddingLeft: 10,
                          paddingTop: 4,
                          fontSize: 15,
                          textAlign: 'center',
                        }}>MYR {estimatedRevenue}</Text>

                      <View
                        style={{
                          marginLeft: 'auto',
                          paddingRight: 10,
                          flexDirection: 'row',
                          height: 30,
                        }}>
                        <DropDownPicker
                          arrowColor={'black'}
                          arrowSize={switchMerchant ? 8 : 13}
                          arrowStyle={{ fontWeight: 'bold' }}
                          style={{
                            width: switchMerchant ? 80 : 180,
                            paddingVertical: 0,
                            borderRadius: 10,
                          }}
                          placeholderStyle={{ color: Colors.fieldtTxtColor }}
                          items={
                            CHART_Y_AXIS_DROPDOWN_LIST[
                            CHART_TYPE.DASHBOARD_LINE_CHART_SALES
                            ]
                          }
                          itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                          placeholder={'Select'}
                          onChangeItem={(item) => {
                            setSelectedChartDropdownValueLineChart(item.value);
                          }}
                          defaultValue={selectedChartDropdownValueLineChart}
                          dropDownMaxHeight={150}
                          dropDownStyle={{
                            width: switchMerchant ? 80 : 180,
                            height: switchMerchant ? 100 : 120,
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 10,
                            borderWidth: 1,
                            textAlign: 'left',
                            zIndex: 2,
                          }}
                          globalTextStyle={{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 12,
                            color: Colors.fontDark,
                            marginLeft: 5,
                          }}
                        />
                      </View>
                    </View>

                    <View
                      style={{
                        // backgroundColor: 'red',
                        zIndex: -1,
                        paddingLeft: 5,
                      }}>
                      <FusionCharts
                        type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                        width={
                          windowWidth <= 1133
                            ? switchMerchant
                              ? windowWidth * 0.8
                              : windowWidth *
                              (0.9 - Styles.sideBarWidth)
                            : windowWidth *
                            (0.66 - Styles.sideBarWidth)
                        }
                        height={
                          switchMerchant
                            ? windowHeight * 0.9
                            : windowHeight * 0.3
                        }
                        dataFormat={salesLineChart.dataFormat}
                        dataSource={salesLineChart.dataSource}
                        libraryPath={FS_LIBRARY_PATH}
                        events={eventsChart}
                      />
                    </View>

                  </View>

                  {/* Summary list */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.3,
                      height: windowHeight * 0.4,
                      margin: 5,
                      justifyContent: 'space-between',
                      textAlign: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                    }}>
                    <View
                      style={[styles.summaryListView,
                      { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                      <Text
                        style={styles.summaryListText}>
                        Estimated revenue
                      </Text>
                      <Text
                        style={styles.summaryListText}>
                        MYR {estimatedRevenue}
                      </Text>
                    </View>
                    <View
                      style={[styles.summaryListView,
                      { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                      <Text
                        style={styles.summaryListText}>
                        Reservations
                      </Text>
                      <Text
                        style={styles.summaryListText}>
                        {reservationsNum}
                      </Text>
                    </View>
                    <View
                      style={[styles.summaryListView,
                      { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                      <Text
                        style={styles.summaryListText}>
                        Walk-ins
                      </Text>
                      <Text
                        style={styles.summaryListText}>
                        {walksInNum}
                      </Text>
                    </View>
                    <View
                      style={[styles.summaryListView,
                      { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                      <Text
                        style={styles.summaryListText}>
                        Cancellations
                      </Text>
                      <Text
                        style={styles.summaryListText}>
                        {cancellationsNum}
                      </Text>
                    </View>
                    <View
                      style={{
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={styles.summaryListText}>
                        No-shows
                      </Text>
                      <Text
                        style={styles.summaryListText}>
                        {noShowsNum}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Bottom 3 view */}
                <View
                  style={{
                    flexDirection: 'row',
                  }}>

                  {/* Guest stat chart */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.296,
                      height: windowHeight * 0.30,
                      margin: 5,
                      justifyContent: 'space-around',
                      textAlign: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                    }}>
                    <View
                      style={{
                        paddingLeft: 10,
                      }}>
                      <Text>Repeat guest stats</Text>
                    </View>

                    <View
                      style={{
                        zIndex: -1,
                        paddingLeft: 5,
                      }}>
                      <FusionCharts
                        type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                        width={
                          windowWidth <= 1133
                            ? switchMerchant
                              ? windowWidth * 0.8
                              : windowWidth *
                              (0.9 - Styles.sideBarWidth)
                            : windowWidth *
                            (0.365 - Styles.sideBarWidth)
                        }
                        height={
                          switchMerchant
                            ? windowHeight * 0.9
                            : windowHeight * 0.25
                        }
                        dataFormat={salesLineChart.dataFormat}
                        dataSource={salesLineChart.dataSource}
                        libraryPath={FS_LIBRARY_PATH}
                        events={eventsChart}
                      />
                    </View>
                  </View>

                  {/* Turn time chart */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.296,
                      height: windowHeight * 0.30,
                      margin: 5,
                      justifyContent: 'space-around',
                      textAlign: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                    }}>
                    <View
                      style={{
                        paddingLeft: 10,
                      }}>
                      <Text>Turn time by Party size</Text>
                    </View>

                    <View
                      style={{
                        zIndex: -1,
                        paddingLeft: 5,
                      }}>
                      <FusionCharts
                        type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                        width={
                          windowWidth <= 1133
                            ? switchMerchant
                              ? windowWidth * 0.8
                              : windowWidth *
                              (0.9 - Styles.sideBarWidth)
                            : windowWidth *
                            (0.365 - Styles.sideBarWidth)
                        }
                        height={
                          switchMerchant
                            ? windowHeight * 0.9
                            : windowHeight * 0.25
                        }
                        dataFormat={salesLineChart.dataFormat}
                        dataSource={salesLineChart.dataSource}
                        libraryPath={FS_LIBRARY_PATH}
                        events={eventsChart}
                      />
                    </View>
                  </View>

                  {/* Guest feedback */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.3,
                      height: windowHeight * 0.30,
                      margin: 5,
                      justifyContent: 'space-around',
                      textAlign: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                    }}>
                    <View
                      style={{
                        paddingLeft: 10,
                      }}>
                      <Text>Overall Guest Feedback</Text>
                    </View>

                    <View
                      style={{
                        zIndex: -1,
                        paddingLeft: 5,
                      }}>
                      <FusionCharts
                        type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                        width={
                          windowWidth <= 1133
                            ? switchMerchant
                              ? windowWidth * 0.8
                              : windowWidth *
                              (0.9 - Styles.sideBarWidth)
                            : windowWidth *
                            (0.37 - Styles.sideBarWidth)
                        }
                        height={
                          switchMerchant
                            ? windowHeight * 0.9
                            : windowHeight * 0.25
                        }
                        dataFormat={salesLineChart.dataFormat}
                        dataSource={salesLineChart.dataSource}
                        libraryPath={FS_LIBRARY_PATH}
                        events={eventsChart}
                      />
                    </View>
                  </View>
                </View>
              </ScrollView>
            </View>
            :
            //  Loyalty Page
            <View style={{}}>
              {/* Bottom table */}
              <ScrollView style={{ height: windowHeight * 0.8, }}>
                {/* Active campaign table*/}
                <View
                  style={[{
                    margin: 5,
                    flexDirection: 'column',
                  },
                  switchMerchant ? { width: windowWidth * 0.77 } : { width: windowWidth * 0.91 },
                    // switchMerchant ? { width: windowWidth * 0.90 } : { width: windowWidth * 0.99 },
                  ]}>
                  {/* Table title */}
                  <View style={
                    [styles.tableTitleView,
                    switchMerchant
                      ? {}
                      : {
                        height: windowHeight * 0.1,
                      }
                    ]}>
                    <Text style={[styles.tableTitleTextFirst, {
                      width: '20%',
                    }]}>{`Active\nCampaign`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`SMS\nSent`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`SMS\nDelivered`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Voucher\nSent`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Guest\nVisit`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Visit\nRate`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Voucher\nRedeemed`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Redeem\nRate`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Total\nRevenue`}</Text>
                  </View>

                  {/* Table Summary */}
                  <View style={styles.tableSummaryView}>

                    <View style={{
                      width: '20%',
                    }}>
                      <Text style={styles.tableSummaryTextFirstBig}>
                        Summary
                      </Text>
                      <Text style={styles.tableSummaryTextFirstSmall}>
                        {`Total performance of your\ncurrent Autopilot Campaign`}
                      </Text>
                    </View>

                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.smsSent}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`SMS`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.smsDelivered}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`SMS`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.voucherSent}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`Qty`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.guestVisits}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`Pax`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.visitRate.toFixed(1)}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`%`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.voucherRedeemTimes}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`Qty`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.voucherRedeemRate.toFixed(1)}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`%`}
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig}>
                        {loyaltyCampaignDataSummary.revenue.toFixed(2)}
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        {`MYR`}
                      </Text>
                    </View>
                  </View>

                  {/* Table content */}
                  <View>
                    <FlatList
                      data={loyaltyCampaigns}
                      renderItem={renderLoyalty}
                    />
                  </View>
                  {/* <View style={styles.tableContentView}>

                  <View>
                    <Text style={styles.tableContentTextFirst}>
                      Active
                    </Text>
                    <Text style={styles.tableContentTextSecond}>
                      Birthday
                    </Text>
                    <Text style={styles.tableContentTextThird}>
                      7 days before birthday - 14 day expiration
                    </Text>
                  </View>

                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      2
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Total Delivered
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Total Visits
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0%
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Visit Rate
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      MYR Revenue
                    </Text>
                  </View>
                </View> */}
                </View>

                {/* Inactive campaign table*/}
                <View
                  style={[{
                    margin: 5,
                    flexDirection: 'column',

                    display: 'none',
                  },
                  switchMerchant ? { width: windowWidth * 0.77 } : { width: windowWidth * 0.91 },
                  ]}>
                  {/* Table title */}
                  <View style={
                    [styles.tableTitleView,
                    switchMerchant
                      ? {}
                      : {
                        height: windowHeight * 0.1,
                      }
                    ]}>
                    <Text style={[styles.tableTitleTextFirst, {
                      width: '20%',
                    }]}>{`Inactive\nCampaign`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`SMS\nSent`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`SMS\nDelivered`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Voucher\nSent`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Guest\nVisit`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Visit\nRate`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Voucher\nRedeemed`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Redeem\nRate`}</Text>
                    <Text style={[styles.tableTitleText, {
                      width: '10%',
                    }]}>{`Total\nRevenue`}</Text>
                  </View>

                  {/* Table Summary */}
                  <View style={styles.tableSummaryView}>

                    <View>
                      <Text style={styles.tableSummaryTextFirstBig2}>
                        Inactive Summary
                      </Text>
                      <Text style={styles.tableSummaryTextFirstSmall}>
                        {`Total performance of your\ncurrent Autopilot Campaign`}
                      </Text>
                    </View>

                    <View style={{ justifyContent: 'center', width: '10%' }}>
                      <Text style={styles.tableSummaryTextBig2}>
                        0
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        Total Delivered
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center' }}>
                      <Text style={styles.tableSummaryTextBig2}>
                        0
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        Total Visits
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center' }}>
                      <Text style={styles.tableSummaryTextBig2}>
                        0.0%
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        Visit Rate
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center' }}>
                      <Text style={styles.tableSummaryTextBig2}>
                        0.00
                      </Text>
                      <Text style={styles.tableSummaryTextSmall}>
                        MYR Revenue
                      </Text>
                    </View>
                  </View>

                  {/* Table content */}
                  {/* <View>
                  <FlatList
                    data={loyaltyCampaigns}
                    renderItem={renderLoyalty}
                  />
                </View> */}
                </View>
              </ScrollView>
            </View>
          }
        </View>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },

  topBar: {
    flexDirection: 'row',
    height: Dimensions.get('window').height * 0.11,
    width: Dimensions.get('window').width * 0.785,
    backgroundColor: Colors.lightGrey,
    justifyContent: 'flex-end',

    alignItems: 'center',

    paddingRight: 5,
  },
  topBarButton: {
    padding: 5,
    backgroundColor: Colors.lightGrey,
    // height: Dimensions.get('window').height * 0.0,
    width: Dimensions.get('window').width * 0.24,
    justifyContent: 'center',
  },
  tableTitleView: {
    backgroundColor: Colors.lightGrey,
    flexDirection: 'row',
    justifyContent: 'center',
    height: Dimensions.get('window').height * 0.08,
    alignContent: 'center',
    textAlign: 'center',
  },
  tableTitleTextFirst: {
    textAlign: 'center',
    paddingVertical: 5,
    paddingLeft: 5,
    // width: Dimensions.get('window').width * 0.3,

    alignSelf: 'center',
  },
  tableTitleText: {
    paddingVertical: 5,
    textAlign: 'center',
    // width: Dimensions.get('window').width * 0.11,

    alignSelf: 'center',
  },
  tableSummaryView: {
    paddingTop: 5,
    paddingBottom: 10,
    backgroundColor: Colors.whiteColor,
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    textAlign: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'grey',
  },
  tableSummaryTextFirstBig: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'lightseagreen',
    fontSize: 17,
  },
  tableSummaryTextFirstBig2: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'red',
    fontSize: 17,
  },
  tableSummaryTextFirstSmall: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'gray',
    fontSize: 10,
  },
  tableSummaryTextBig: {
    textAlign: 'center',
    color: 'lightseagreen',
    fontSize: 17,
    // width: Dimensions.get('window').width * 0.11,
  },
  tableSummaryTextBig2: {
    textAlign: 'center',
    color: 'red',
    fontSize: 17,
    // width: Dimensions.get('window').width * 0.11,
  },
  tableSummaryTextSmall: {
    textAlign: 'center',
    paddingTop: 5,
    color: 'gray',
    fontSize: 10,
    // width: Dimensions.get('window').width * 0.11,
  },
  tableContentView: {
    paddingTop: 5,
    paddingBottom: 10,
    backgroundColor: Colors.whiteColor,
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    textAlign: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'grey',
  },
  tableContentTextFirst: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'lightseagreen',
    fontSize: 10,
  },
  tableContentTextFirst2: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'red',
    fontSize: 10,
  },
  tableContentTextSecond: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'black',
    fontSize: 15,
  },
  tableContentTextThird: {
    textAlign: 'center',
    paddingTop: 5,
    // marginLeft: 5,
    // width: Dimensions.get('window').width * 0.3,
    color: 'gray',
    fontSize: 10,
  },
  tableContentTextBig: {
    textAlign: 'center',
    fontSize: 17,
    color: 'black',
    // width: Dimensions.get('window').width * 0.11,
  },
  tableContentTextSmall: {
    textAlign: 'center',
    paddingTop: 5,
    color: 'gray',
    fontSize: 10,
    // width: Dimensions.get('window').width * 0.11,
  },
  summaryListView: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  summaryListText: {
    padding: 15,
    margin: 10,
  }


});

export default LoyaltyReport;
