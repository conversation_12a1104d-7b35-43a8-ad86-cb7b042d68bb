import { Text } from "react-native-fast-text";
import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Modal,
    Dimensions,
    KeyboardAvoidingView,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import GCalendar from '../assets/svg/GCalendar'
import { TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import moment from 'moment'
import Feather from 'react-native-vector-icons/Feather';
import CheckBox from 'react-native-check-box';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import ApiClient from '../util/ApiClient';
import { CommonStore } from '../store/commonStore';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { parseValidPriceText } from '../util/common';
import { KeyboardAwareFlatList, KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { EXPAND_TAB_TYPE, } from '../constant/common';

const Campaign = props => {
    const {
        navigation,
    } = props;
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [days, setDays] = useState(false);
    const [days1, setDays1] = useState(false);
    const [loading, setLoading] = useState(false);
    const [showDistance, setShowDistance] = useState('');
    const [expiryPeriod, setExpiryPeriod] = useState(0);
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');
    const [pickDate, setPickDate] = useState('');
    const [merchantDisplay, setMerchantDisplay] = useState(false);
    const [CampaignList, setCampaignList] = useState(true);
    const [redemptionList, setRedemptionList] = useState(true);
    const [redemptionAdd, setRedemptionAdd] = useState(false);
    const [detail, setDetail] = useState([]);
    const [merchantInfo, setMerchantInfo] = useState([]);
    const [outlet, setOutlet] = useState([]);
    const [outletInfo, setOutletInfo] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [logo, setLogo] = useState('');
    const [cover, setCover] = useState('');
    const [outlets, setOutlets] = useState([]);
    const [outletId, setOutletId] = useState(null);
    const [myTextInput, setMyTextInput] = useState(React.createRef());
    const [start_time, setStart_time] = useState(false);
    const [end_time, setEnd_time] = useState(false);
    const [rev_time, setRev_time] = useState('');
    const [category, setCategory] = useState('');
    const [close, setClose] = useState('Closed');
    const [showNote, setShowNote] = useState(false);
    const [expandView, setExpandView] = useState(false);
    const [value, setValue] = useState('');
    const [extendOption, setExtendOption] = [];
    const [redemptionInfo, setRedemptionInfo] = useState([]);
    const [alloutlet, setAlloutlet] = useState([]);
    const [categoryOutlet, setCategoryOutlet] = useState([]);
    const [extend, setExtend] = useState([]);
    const [outletss, setOutletss] = useState([]);
    const [redemptionDetail, setRedemptionDetail] = useState([]);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');


    ///////////////////////////////////////////////////////////////////////

    const [bdId, setBdId] = useState('');

    const [bdOptions, setBdOptions] = useState([
        {
            optionsId: '',
            isActive: true,
            duration: 0,
            price: 0,
            unit: 'day',
        }
    ]);

    const [categoryDropdownList, setCategoryDropdownList] = useState([]);
    const [selectedCategoryList, setSelectedCategoryList] = useState([]);

    const [outletDropdownList, setOutletDropdownList] = useState([]);
    const [selectedOutletList, setSelectedOutletList] = useState([]);

    const [displayQrModal, setDisplayQrModal] = useState(false);
    const [selectedBeerDocket, setSelectedBeerDocket] = useState({});

    ///////////////////////////////////////////////////////////////////////

    const merchantId = UserStore.useState(s => s.merchantId);
    const userName = UserStore.useState(s => s.name);
    const userId = UserStore.useState(s => s.firebaseUid);
    const merchantName = MerchantStore.useState(s => s.name);
    const merchantLogo = MerchantStore.useState(s => s.logo);

    const beerDocketCategories = OutletStore.useState(s => s.beerDocketCategories);
    const beerDocketCategoriesDict = OutletStore.useState(s => s.beerDocketCategoriesDict);
    const beerDockets = OutletStore.useState(s => s.beerDockets);

    const allOutlets = MerchantStore.useState(s => s.allOutlets);
    const allOutletsDict = MerchantStore.useState(s => s.allOutletsDict);

    const selectedBeerDocketEdit = CommonStore.useState(s => s.selectedBeerDocketEdit);
    const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

    /////////////////////////////////////////////////////////////////////////  

    useEffect(() => {
        // console.log('================================');
        // console.log('selectedBeerDocketEdit');
        // console.log(selectedBeerDocketEdit);

        if (
            selectedBeerDocketEdit
        ) {
            // insert info

            setBdId(selectedBeerDocketEdit.uniqueId);
            setBdOptions(selectedBeerDocketEdit.bdOptions);
            setSelectedCategoryList(selectedBeerDocketEdit.bdCategories);
            setExpiryPeriod(selectedBeerDocketEdit.duration);
            setSelectedOutletList(selectedBeerDocketEdit.bdOutlets);
        }
        else {
            // designed to always mounted, thus need clear manually...

            setBdId('');
            setBdOptions([
                {
                    optionsId: '',
                    isActive: true,
                    duration: 0,
                    price: 0,
                    unit: 'day',
                }
            ]);
            setSelectedCategoryList([]);
            setExpiryPeriod(0);
            setSelectedOutletList([]);
        }
    }, [
        selectedBeerDocketEdit,
    ]);

    useEffect(() => {
        setCategoryDropdownList(beerDocketCategories.map(beerDocketCategory => ({ label: beerDocketCategory.name, value: beerDocketCategory.uniqueId })));

        // if (selectedCategoryId === '' && beerDocketCategories.length > 0) {
        //   setSelectedCategoryId(beerDocketCategories[0].uniqueId);
        // }
    }, [beerDocketCategories]);

    /////////////////////////////////////////////////////////////////////////

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //     tabBarVisible: false,
    // });

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={{
                    width: Dimensions.get('screen').width * 0.17,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View style={[{
                // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                // bottom: switchMerchant ? '2%' : 0,
                ...global.getHeaderTitleStyle(),
            },
            // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width * 0.12 } : {}
            ]}
            >
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Campaign
                </Text>
            </View>
        ),
        headerRight: () => (
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
            }}>
                {outletSelectDropdownView()}
                <View style={{
                    backgroundColor: 'white',
                    width: 0.5,
                    height: Dimensions.get('screen').height * 0.025,
                    opacity: 0.8,
                    marginHorizontal: 15,
                    bottom: -1,
                }}>
                </View>
                <TouchableOpacity onPress={() => {
                    if (global.currUserRole === 'admin') {
                        navigation.navigate('Setting');
                    }
                }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}
                >
                    <Text
                        style={{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}>
                        {userName}
                    </Text>
                    <View style={{
                        marginRight: 30,
                        width: Dimensions.get('screen').height * 0.05,
                        height: Dimensions.get('screen').height * 0.05,
                        borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'white',
                    }}>
                        <Image style={{
                            width: Dimensions.get('screen').height * 0.035,
                            height: Dimensions.get('screen').height * 0.035,
                            alignSelf: 'center',
                        }} source={require('../assets/image/profile-pic.jpg')} />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });


    const renderOptions = () => {
        const options = [];

        return <>
            {
                bdOptions.map((item, index) => {
                    return (
                        <View>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <View
                                    style={{
                                        //justifyContent: 'flex-end',
                                        //alignItems: 'center',
                                    }}>
                                    <CheckBox
                                        style={{
                                            paddingRight: 10,
                                            marginTop: 12,
                                        }}
                                        onClick={() => {
                                            // setState({
                                            //   isChecked11: !isChecked11,
                                            // });
                                            // check1(extentionCharges, extentionDuration)

                                            setBdOptions(bdOptions.map((bdOption, i) => (i === index ? {
                                                ...bdOption,
                                                isActive: !bdOption.isActive,
                                            } : bdOption)))
                                        }}
                                        checkBoxColor={Colors.fieldtBgColor}
                                        uncheckedCheckBoxColor={Colors.tabGrey}
                                        checkedCheckBoxColor={Colors.primaryColor}
                                        isChecked={item.isActive}
                                    />
                                </View>
                                <View style={{ justifyContent: 'center' }}>
                                    <View>
                                        <Text style={{ color: Colors.descriptionColor }}>
                                            RM:
                                        </Text>
                                        <TextInput
                                            underlineColorAndroid={Colors.fieldtBgColor}
                                            style={[styles.textInput8, { paddingLeft: 5, borderWidth: 1, borderColor: '#E5E5E5', height: 45 }]}
                                            placeholderStyle={{ alignContent: 'center' }}
                                            placeholder="Amount"
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            defaultValue={(item.price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                            onChangeText={(text) => {
                                                // const extendOption = extendOption;
                                                // const item = extendOption.find(
                                                //   (obj) => obj.id === extendOption.id,
                                                // );
                                                // item.price = text;
                                                // setState({ extendOption, extentionCharges: text });

                                                setBdOptions(parseValidPriceText(bdOptions.map((bdOption, i) => (i === index ? {
                                                    ...bdOption,
                                                    price: text.length > 0 ? parseFloat(text) : 0,
                                                } : bdOption))))
                                            }}
                                            // value={(value) => {
                                            //   const extendOption = extendOption;
                                            //   const item = extendOption.find(
                                            //     (obj) => obj.id === extendOption.id,
                                            //   );
                                            //   value = item.price;
                                            // }}
                                            value={item.price}
                                            ref={myTextInput}
                                            keyboardType={'decimal-pad'}
                                        />
                                    </View>
                                </View>
                                <View
                                    style={{
                                        marginHorizontal: 10,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        marginTop: 12,

                                    }}>
                                    <Text style={{ fontSize: 17 }}>For</Text>
                                </View>
                                <View style={{ marginRight: '3%' }}>
                                    <Text
                                        style={{
                                            color: Colors.descriptionColor,
                                            fontSize: 15,
                                        }}>
                                        Durations:
                                    </Text>
                                    <View style={[styles.textInput10, { height: 45, borderColor: '#E5E5E5', borderWidth: 1, width: 150 }]}>
                                        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                                            <TextInput
                                                underlineColorAndroid={Colors.fieldtBgColor}
                                                style={{}}
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                placeholder="Period"
                                                defaultValue={item.duration.toFixed(0)}
                                                onChangeText={(text) => {
                                                    // const extendOption = extendOption;
                                                    // const item = extendOption.find(
                                                    //   (obj) => obj.id === extendOption.id,
                                                    // );
                                                    // item.day = text;
                                                    // setState({ extendOption, extentionDuration: text });

                                                    setBdOptions(bdOptions.map((bdOption, i) => (i === index ? {
                                                        ...bdOption,
                                                        duration: text.length > 0 ? parseFloat(text) : 0,
                                                    } : bdOption)))
                                                }}
                                                // value={(value) => {
                                                //   const extendOption = extendOption;
                                                //   const item = extendOption.find(
                                                //     (obj) => obj.id === extendOption.id,
                                                //   );
                                                //   value = item.day;
                                                // }}
                                                value={item.duration}
                                                ref={myTextInput}
                                                keyboardType={'decimal-pad'}
                                            />
                                        </View>
                                        <View
                                            style={{
                                                flex: 1,
                                                flexDirection: 'row',
                                                borderLeftWidth: StyleSheet.hairlineWidth,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                            }}>
                                            <Text
                                                style={{
                                                    fontSize: 14,
                                                    // marginTop: 15,
                                                    // marginLeft: '5%',
                                                    textAlign: 'center',
                                                    color: Colors.descriptionColor,
                                                }}>
                                                {days1 == false ? 'Days' : 'Months'}
                                            </Text>

                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View >
                    );
                })
            }
        </>;
    }
    const createBeerDocket = () => {

        var body = {
            bdId: bdId,
            bdOptions: bdOptions,
            bdCategories: selectedCategoryList,
            duration: expiryPeriod,
            bdOutlets: selectedOutletList,
            merchantId: merchantId,
            merchantName: merchantName,
            merchantLogo: merchantLogo,

            bdCategoryNames: [],
            bdOutletNames: [],
            bdOutletCovers: [],
            bdOutletAddresses: [],

            quantity: 10,
        };

        for (var i = 0; i < selectedCategoryList.length; i++) {
            if (beerDocketCategoriesDict[selectedCategoryList[i]]) {
                body.bdCategoryNames.push(beerDocketCategoriesDict[selectedCategoryList[i]].name);
            }
        }

        for (var i = 0; i < selectedOutletList.length; i++) {
            if (allOutletsDict[selectedOutletList[i]]) {
                body.bdOutletNames.push(allOutletsDict[selectedOutletList[i]].name);
                body.bdOutletCovers.push(allOutletsDict[selectedOutletList[i]].cover);
                body.bdOutletAddresses.push(allOutletsDict[selectedOutletList[i]].address);
            }
        }

        if (selectedBeerDocketEdit === null) {
            ApiClient.POST(API.createBeerDocket, body, false).then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Promotion has been created',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    setRedemptionList(true);
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            });
        }
        else {
            ApiClient.POST(API.updateBeerDocket, body, false).then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Promotion has been updated',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    setRedemptionList(true);
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            });
        }
    }

    return (
        <View style={[styles.container, !isTablet() ? {
            transform: [
                { scaleX: 1 },
                { scaleY: 1 },
            ],
        } : {}]}>
            {/* <View style={[styles.sidebar, !isTablet() ? {
                width: Dimensions.get('screen').width * 0.08,
            } : {}, switchMerchant ? {
                // width: '10%'
            } : {}]}>
                <SideBar navigation={props.navigation} selectedTab={4} expandRedemption={true} />
            </View> */}
            <View style={styles.content}>

                {CampaignList ? (
                    <>
                        <View
                            style={(styles.container, {
                                backgroundColor: Colors.whiteColor,
                                // height: Dimensions.get('window').height - 120,

                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,

                                borderRadius: 8,
                            })}>
                            {redemptionList == true ? (

                                <View style={{
                                    height: Dimensions.get('window').height * 0.8,
                                }}>
                                    <View
                                        style={{
                                            marginBottom: 15,
                                        }}>
                                        <View style={{
                                            flexDirection: 'row',
                                            padding: 10,
                                            marginTop: 10,
                                            height: 60,
                                            //alignItems: 'center',
                                            justifyContent: 'space-between',
                                        }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ justifyContent: 'center', }}>
                                                    <Text style={styles.textSize}>Campaign </Text>
                                                </View>
                                            </View>
                                            <View style={{}}>
                                                <TouchableOpacity
                                                    style={styles.addNewView}
                                                    onPress={() => {
                                                        CommonStore.update(s => {
                                                            s.selectedBeerDocketEdit = null;
                                                        });

                                                        setRedemptionList(false);
                                                    }}>
                                                    <View style={styles.addButtonView}>
                                                        <View style={{}}>
                                                            <AntDesign
                                                                name="pluscircle"
                                                                size={20}
                                                                color={Colors.primaryColor}
                                                            />
                                                        </View>
                                                        <Text
                                                            style={{
                                                                marginLeft: 10,
                                                                color: Colors.primaryColor,
                                                                fontWeight: '600'
                                                            }}>
                                                            New Campaign
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                paddingVertical: 20,
                                                paddingHorizontal: 10,
                                                marginTop: 10,
                                                zIndex: -1,
                                            }}>
                                            <Text
                                                style={{
                                                    width: '20%',
                                                    //alignSelf: 'center',
                                                    color: '#757575',
                                                    marginRight: 5,
                                                }}>
                                                Name
                                            </Text>
                                            <Text
                                                style={{
                                                    width: '15%',
                                                    alignSelf: 'center',
                                                    color: '#757575',
                                                    marginRight: 5,
                                                }}>
                                                Created Date
                                            </Text>
                                            <Text
                                                style={{
                                                    width: '25%',
                                                    alignSelf: 'center',
                                                    color: '#757575',
                                                    marginRight: 5,
                                                }}>
                                                Number of Docket
                                            </Text>
                                            <Text
                                                style={{
                                                    width: '5%',
                                                    alignSelf: 'center',
                                                    color: '#757575',
                                                }}>
                                                {' '}
                                            </Text>
                                        </View>
                                        <View style={{ marginBottom: 20, zIndex: -1 }}>
                                            <ScrollView style={{
                                                //backgroundColor: 'red'
                                            }}>
                                                <FlatList />
                                                <View style={{ borderWidth: 1, borderColor: '#E5E5E5' }} />
                                            </ScrollView>
                                        </View>
                                    </View>
                                    <View style={{ marginTop: 10 }}></View>
                                </View >
                            ) : (
                                <View>
                                    <View style={{ zIndex: -1, flexDirection: 'row', justifyContent: 'space-between', marginLeft: 10 }}>
                                        <TouchableOpacity style={{ marginVertical: 20, flexDirection: 'row', alignContent: 'center', alignItems: 'center' }}
                                            onPress={() => {
                                                setRedemptionList(true);
                                            }}>
                                            <Feather name="chevron-left" size={30} color={Colors.primaryColor} />
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 17, color: Colors.primaryColor }}> Back </Text>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={{ justifyContent: 'center', marginHorizontal: 10 }}
                                            disabled={loading}
                                            onPress={() => {
                                                createBeerDocket();
                                            }}>
                                            <View
                                                style={[
                                                    styles.button3,
                                                    { justifyContent: 'center', width: 130 },
                                                ]}>
                                                <Text style={{ color: '#ffffff', fontSize: 20 }}>
                                                    {loading ? 'LOADING...' : 'Create'}
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View style={{ borderWidth: 1, borderColor: '#E5E5E5' }} />
                                    <KeyboardAwareScrollView style={{ marginBottom: 15, }}>
                                        <View style={{ flexDirection: 'row', flex: 1, marginTop: 20, marginBottom: 25 }}>
                                            <View style={{ justifyContent: 'center', width: '18%', marginLeft: 20 }}>
                                                <Text style={styles.textSize}>Campaign Name </Text>
                                            </View>
                                            <View style={{ height: 50, justifyContent: 'center', }}>
                                                <TextInput
                                                    placeholder={"Name"}
                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                    placeholderStyle={{ color: 'black' }}
                                                    style={{ backgroundColor: Colors.fieldtBgColor, width: 200 }} />
                                            </View>
                                        </View>
                                        <View style={{ flexDirection: 'row', width: '50%' }}>
                                            <View style={{ alignContent: 'center', justifyContent: 'center', height: 50, width: '35%', marginHorizontal: 1, marginLeft: 20 }}>
                                                <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 18 }}>Start Date</Text>
                                            </View>
                                            <View
                                                style={{
                                                    height: 50,
                                                    paddingHorizontal: 20,
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    marginBottom: 25,
                                                    width: "35%",
                                                    marginHorizontal: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: "center",
                                                    alignContent: 'center',
                                                    borderColor: '#E5E5E5',
                                                    borderWidth: 1,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderRadius: 12,
                                                    paddingLeft: 30,
                                                }}>
                                                <Text style={{
                                                    width: "90%",
                                                    fontFamily: "NunitoSans-Regular",
                                                    color: startDate ? 'black' : Colors.descriptionColor,
                                                }}>
                                                    {/* {startDate == '' ? "Start Date" : startDate} */}
                                                    {startDate ? moment(startDate).format('DD/MM/YYYY') : 'Start Date'}
                                                </Text>
                                                <TouchableOpacity
                                                    disabled={!(!loading)}
                                                    style={{
                                                        alignItems: 'center'
                                                    }}
                                                    onPress={() => {
                                                        setPickDate('startDate');
                                                    }}>
                                                    {/* <EvilIcons name="calendar" size={40} color={Colors.primaryColor} /> */}
                                                    <GCalendar width={20} height={20} />
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={{ flexDirection: 'row', width: '50%' }}>
                                            <View style={{ alignContent: 'center', justifyContent: 'center', height: 50, width: '35%', marginHorizontal: 1, marginLeft: 20 }}>
                                                <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 18, }}>End Date</Text>
                                            </View>
                                            <View
                                                style={{
                                                    height: 50,
                                                    paddingHorizontal: 20,
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderRadius: 5,
                                                    marginBottom: 20,
                                                    width: "35%",
                                                    marginHorizontal: 10,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: "center",
                                                    alignContent: 'center',
                                                    fontFamily: 'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderRadius: 12,
                                                    paddingLeft: 30,
                                                    borderColor: '#E5E5E5',
                                                    borderWidth: 1,
                                                }}>
                                                <Text style={{
                                                    width: "90%",
                                                    fontFamily: "NunitoSans-Regular",
                                                    color: endDate ? 'black' : Colors.descriptionColor,
                                                }}>
                                                    {/* {endDate == '' ? "End Date" : endDate} */}
                                                    {endDate ? moment(endDate).format('DD/MM/YYYY') : 'End Date'}
                                                </Text>
                                                <TouchableOpacity
                                                    disabled={!(!loading)}
                                                    style={{
                                                        alignItems: 'center'
                                                    }}
                                                    onPress={() => {
                                                        setPickDate('endDate');
                                                    }}>
                                                    {/* <EvilIcons name="calendar" size={40} color={Colors.primaryColor} /> */}
                                                    <GCalendar width={20} height={20} />
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={{ marginVertical: 20, height: 150 }}></View>
                                    </KeyboardAwareScrollView>
                                </View >
                            )

                            }
                        </View >
                    </>
                ) : null}
            </View >
        </View >
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    iosStyle: {
        paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    headerLogo1: {
        width: '100%',
        height: '100%',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.44,
        shadowRadius: 10.32,

        elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.fieldtBgColor,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regul`ar',
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
    },
    textInput8: {
        fontFamily: 'NunitoSans-Regular',
        width: 75,
        height: 50,
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },
    textInput9: {
        fontFamily: 'NunitoSans-Regular',
        width: 110,
        height: Platform.OS == 'ios' ? 30 : 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    textInput10: {
        fontFamily: 'NunitoSans-Regular',
        width: 200,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    button1: {
        width: '15%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    button2: {
        backgroundColor: Colors.primaryColor,
        width: '60%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        marginLeft: '2%',
    },
    button3: {
        backgroundColor: Colors.primaryColor,
        //width: '30%',
        height: 35,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        //marginBottom: 30,
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold'
    },
    viewContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 0,
        width: '100%',
        marginBottom: 15,
    },
    addButtonView: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
    },
    addButtonView1: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: 'center',
    },
    addNewView: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        //marginBottom: 65,
        //marginTop: 7,
        //width: '83%',
        alignSelf: 'flex-end',
    },
    addNewView1: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 10,
        alignItems: 'center',
    },
    closeView: {
        flexDirection: 'row',
        borderRadius: 5,
        borderColor: Colors.primaryColor,
        borderWidth: 1,
        width: 200,
        height: 40,
        alignItems: 'center',
        marginTop: 30,
        alignSelf: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Dimensions.get('screen').width * 0.4,
        width: Dimensions.get('screen').width * 0.4,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get('screen').width * 0.03,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.04,
        top: Dimensions.get('screen').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },
});
export default Campaign;