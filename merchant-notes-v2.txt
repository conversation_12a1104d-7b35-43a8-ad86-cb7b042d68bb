jscodeshift -t ./codemods/transform-inline-style-to-outer-style.js ./screen/TableScreen.js

./gradlew assembleRelease -x bundleReleaseJsAndAssets

adb -s "emulator-5554" pull /sdcard/Download/filename.ext ~/Desktop/

-------------------------------------------------

# 1. Clear Derived Data (Xcode build artifacts)
rm -rf ~/Library/Developer/Xcode/DerivedData/

# 2. Clean CocoaPods cache (removes downloaded pod versions)
pod cache clean --all

# 3. Delete Pods folder & Podfile.lock (local pod setup)
rm -rf Pods/ Podfile.lock

# 4. Reinstall pods fresh
pod install

# 5. Clean & Rebuild in Xcode
# - Product → Clean Build Folder (⌥⇧⌘K)
# - Product → Build (⌘B)

-------------------------------------------------

checking compiling js issue:

http://localhost:8085/index.bundle?platform=android&dev=true&minify=false&modulesOnly=false&runModule=true
