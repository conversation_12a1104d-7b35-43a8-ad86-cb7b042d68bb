import { Text } from "react-native-fast-text";
import React, { Component, useReducer } from 'react';
import { StyleSheet, Image, View, Alert, TouchableOpacity, Modal, Dimensions } from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import moment from 'moment';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Feather from 'react-native-vector-icons/Feather';
import { TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import GCalendar from '../assets/svg/GCalendar'
import ApiClient from '../util/ApiClient';
import {
  isTablet
} from '../util/common';

class ReportStockValue extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {
            currentDateTime: Date().toLocaleString(),
            sort: null,
            quantity: 1,
            list: [],
            page: 0,
            oriList: [],
            offset: 0,
            perPage: 5,
            pageCount: 0,
            currentPage: 1,
            day: false,
            pick: null,
            pick1: null,
            filter: false,
            filter1: false,
            filter2: false,
            filter3: false,
            supplier: "All Supplier",
            product: "All categories",
            sku: "All tags",
            item: [],
            total: [],
            lastSort: null,
            search: "",

        }
        // navigation.dangerouslyGetParent().setOptions({
        //     tabBarVisible: false
        // });
        navigation.setOptions({
            headerLeft: () => (
                <Image style={styles.headerLogo} resizeMode="contain" source={require('../assets/image/logo.png')} />
            ),
            // headerRight: () => (
            //     <TouchableOpacity onPress={async () => {
            //         await AsyncStorage.clear();
            //         User.setlogin(false);
            //         User.getRefreshMainScreen();
            //     }}><View style={{ flexDirection: 'row' }}><Text style={{ fontWeight: 'bold', marginRight: 10, color: Colors.whiteColor }}>{User.getName()} | </Text><Text style={{ fontWeight: 'bold', marginRight: 10, color: Colors.secondaryColor }}>Logout</Text></View></TouchableOpacity>
            // ),
            headerRight: () => (
                <TouchableOpacity>
                    <View style={{ flexDirection: 'row' }}>

                        <Text
                            style={{
                                fontWeight: 'bold',
                                marginRight: 10,
                                marginTop: 3,
                                color: Colors.whiteColor,
                            }}>
                            {User.getName()} |{' '}

                        </Text>

                        {/* <Text
                      style={{
                        fontWeight: 'bold',
                        marginRight: 10,
                        color: Colors.secondaryColor,
                      }}>
                      Logout
                    </Text> */}
                        <Image style={styles.circleIcon} source={require('../assets/image/profile-pic.jpg')} />
                    </View>

                </TouchableOpacity>
            ),
        });
    }
    componentDidMount() { this.getDetail() }

    // function here
    email1(param) {
        var body = {
            data: param,
            email: "<EMAIL>"
        }
        ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
            // console.log(result)
            if (result !== null) {

                Alert.alert(
                    'Success',
                    'Email has been sent',
                    [
                        { text: "OK", onPress: () => { } }
                    ],
                    { cancelable: false },
                );
            }
        });
    }

    download(param) {
        var body = {
            orderId: param,
        }
        ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
            // console.log(result)
            if (result !== null) {

                Alert.alert(
                    'Success',
                    'Data has been downloaded',
                    [
                        { text: "OK", onPress: () => { } }
                    ],
                    { cancelable: false },
                );
            }
        });
    }

    add = async () => {
        if (this.state.page + 1 < this.state.pageCount) {
            await this.setState({ page: this.state.page + 1, currentPage: this.state.currentPage + 1 })
            // console.log(this.state.page)
            var e = this.state.page
            this.next(e)
        }
    }

    next(e) {
        const offset = e * this.state.perPage;
        this.setState({ offset: offset })
        this.loadMoreData()
    }

    less = async () => {
        if (this.state.page > 0) {
            await this.setState({ page: this.state.page - 1, currentPage: this.state.currentPage - 1 })
            // console.log(this.state.page)
            var y = this.state.page
            this.pre(y)
        }
    }

    pre(y) {

        const offset = y * this.state.perPage;
        this.setState({ offset: offset })
        this.loadMoreData()

    }

    loadMoreData() {
        const data = this.state.oriList;
        const slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
        this.setState({ list: slice, pageCount: Math.ceil(data.length / this.state.perPage) })
    }

    getStockFilter() {
        ApiClient.GET(API.getStockFilter + 1 + "&querySupplier=&queryCategory=&queryCode=&queryStartDate&queryEndDate=").then((result) => {
            result.forEach((element) => {
                const outletTakeaway = result.filter(element => element.id === undefined)
                const outletTakeaway1 = result.filter(element => element.id !== undefined)
                this.setState({ item: outletTakeaway, total: outletTakeaway1 })

            });
            var data = this.state.total
            var slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
            this.setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / this.state.perPage) })
        });
    }

    getDetail() {
        ApiClient.GET(API.getStockSearch + 1 + '&queryName').then((result) => {
            // console.log('sdasdadad')
            // console.log(result)
            result.forEach((element) => {
                const outletTakeaway = result.filter(element => element.id === undefined)
                const outletTakeaway1 = result.filter(element => element.id !== undefined)
                this.setState({ item: outletTakeaway, total: outletTakeaway1 })

            });
            var data = this.state.total
            var slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
            this.setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / this.state.perPage) })
        });
    }

    getSearch() {
        ApiClient.GET(API.getStockSearch + 1 + '&queryName=' + this.state.search).then((result) => {
            // console.log('17171717171717')
            // console.log(result)
            result.forEach((element) => {
                const outletTakeaway = result.filter(element => element.id === undefined)
                const outletTakeaway1 = result.filter(element => element.id !== undefined)
                this.setState({ item: outletTakeaway, total: outletTakeaway1 })

            });
            var data = this.state.total
            var slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
            this.setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / this.state.perPage) })
        });
    }

    renderList = ({ item, index }) => (
        <TouchableOpacity onPress={() => { }}>
            {console.log("ITME, item", item)}
            {(index + 1) % 2 == 0 ? <View style={{ flexDirection: "row", paddingVertical: 10 }}>
                <Text style={{ fontSize: 12, width: "2%" }} ></Text>
                <Text style={{ fontSize: 12, width: "13%" }} >{item.name}</Text>
                <Text style={{ fontSize: 12, width: "2%" }} ></Text>
                <Text style={{ fontSize: 12, width: "9%" }} >{item.category.name}</Text>
                <Text style={{ marginLeft: 20, width: "12%" }}>{item.itemInventory.quantity}</Text>
                <Text style={{ marginLeft: 20, width: "8%" }}>{parseFloat(item.itemInventory.value).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "17%" }}>{parseFloat(item.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "14%" }}>{parseFloat(item.itemInventory.quantity * parseFloat(item.itemInventory.value)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "10%" }}>{parseFloat(item.itemInventory.quantity * parseFloat(item.price)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
            </View> : <View style={{ flexDirection: "row", paddingVertical: 10, backgroundColor: "#e8e8e8" }}>
                <Text style={{ fontSize: 12, width: "2%" }} ></Text>
                <Text style={{ fontSize: 12, width: "13%" }} >{item.name}</Text>
                <Text style={{ fontSize: 12, width: "2%" }} ></Text>
                <Text style={{ fontSize: 12, width: "9%" }} >{item.category.name}</Text>
                <Text style={{ marginLeft: 20, width: "12%" }}>{item.itemInventory.quantity}</Text>
                <Text style={{ marginLeft: 20, width: "8%" }}>{parseFloat(item.itemInventory.value).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "17%" }}>{parseFloat(item.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "14%" }}>{parseFloat(item.itemInventory.quantity * parseFloat(item.itemInventory.value)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ marginLeft: 20, width: "10%" }}>{parseFloat(item.itemInventory.quantity * parseFloat(item.price)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
            </View>}
        </TouchableOpacity>
    );


    changeClick() {
        if (this.state.day == true) {
            this.setState({ day: false })
        }
        else
            this.setState({ day: true })
    }

    changeClick1() {
        if (this.state.filter == true) {
            this.setState({ filter: false })
        }
        else
            this.setState({ filter: true })
    }

    changeClick2() {
        if (this.state.filter1 == true) {
            this.setState({ filter1: false })
        }
        else
            this.setState({ filter1: true })
    }

    changeClick3() {
        if (this.state.filter2 == true) {
            this.setState({ filter2: false })
        }
        else
            this.setState({ filter2: true })
    }

    changeClick4() {
        if (this.state.filter3 == true) {
            this.setState({ filter3: false })
        }
        else
            this.setState({ filter3: true })
    }

    sortingOrders(param) {

        // console.log("this.sate.saet", this.state.lastSort)
        if (param.value == 1) { //productname
            if (this.state.lastSort != null ? this.state.lastSort.value == param.value : null) {
                const list = this.state.list.sort((a, b) => b.name.localeCompare(a.name))
                this.setState({ list })

            } else {
                const list = this.state.list.sort((a, b) => a.name.localeCompare(b.name))
                this.setState({ list })
            }
        }
        if (param.value == 2) { //SKU
            if (this.state.lastSort != null ? this.state.lastSort.value == param.value : null) {
                const list = this.state.list.sort((a, b) => b.category.name.localeCompare(a.category.name))
                this.setState({ list })

            } else {
                const list = this.state.list.sort((a, b) => a.category.name.localeCompare(b.category.name))
                this.setState({ list })
            }
        }
        if (param.value == 3) { //quantity
            if (this.state.lastSort != null ? this.state.lastSort.value == param.value : null) {
                const list = this.state.list.sort((a, b) => b.itemInventory.quantity - a.itemInventory.quantity)
                this.setState({ list })

            } else {
                const list = this.state.list.sort((a, b) => a.itemInventory.quantity - b.itemInventory.quantity)
                this.setState({ list })
            }
        }
        if (param.value == 4) { //cost
            if (this.state.lastSort != null ? this.state.lastSort.value == param.value : null) {
                const list = this.state.list.sort((a, b) => b.itemInventory.value - a.itemInventory.value)
                this.setState({ list })

            } else {
                const list = this.state.list.sort((a, b) => a.itemInventory.value - b.itemInventory.value)
                this.setState({ list })
            }
        }
        if (param.value == 5) { //price
            if (this.state.lastSort != null ? this.state.lastSort.value == param.value : null) {
                const list = this.state.list.sort((a, b) => b.price - a.price)
                this.setState({ list })

            } else {
                const list = this.state.list.sort((a, b) => a.price - b.price)
                this.setState({ list })
            }
        }
        this.setState({ lastSort: param })
    }
    // function end

    render() {
        return (
            // <View style={styles.container}>
            //     <View style={styles.sidebar}>
            (<View style={[styles.container, !isTablet() ? {
                transform: [
                    { scaleX: 1 },
                    { scaleY: 1 },
                ],
            } : {}]}>
                {/* <View style={[styles.sidebar, !isTablet() ? {
                    width: Dimensions.get('screen').width * 0.4,
                } : {}]}>
                    <SideBar navigation={this.props.navigation} selectedTab={8} expandReport={true} expandSales={true} />
                </View> */}
                <View style={{ width: '82%' }}>
                    <DateTimePickerModal
                        isVisible={this.state.showDateTimePicker}
                        mode={this.state.pickerMode}
                        onConfirm={(text) => {
                            if (this.state.pick == 1) {
                                var date_ob = new Date(text);
                                let date = ("0" + date_ob.getDate()).slice(-2);
                                let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                let year = date_ob.getFullYear();
                                this.setState({ rev_date: year + "-" + month + "-" + date })
                            } else {
                                var date_ob = new Date(text);
                                let date = ("0" + date_ob.getDate()).slice(-2);
                                let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                let year = date_ob.getFullYear();
                                this.setState({ rev_date1: year + "-" + month + "-" + date })
                            }

                            this.setState({ showDateTimePicker: false })
                        }}
                        onCancel={() => {
                            this.setState({ showDateTimePicker: false })
                        }}
                    />
                    {this.state.day ?
                        <View style={{ position: 'absolute', width: "24%", backgroundColor: Colors.whiteColor, left: 440, top: 170, zIndex: 6000 }}>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { this.setState({ rev_date: moment(this.state.currentDateTime).format("DD MMM yyyy"), rev_date1: moment(this.state.currentDateTime).format("DD MMM yyyy") }) }}>
                                <Text style={{ color: Colors.whiteColor }}>Today</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ rev_date: moment(moment(this.state.currentDateTime).subtract(1, "day")).format("DD MMM yyyy"), rev_date1: moment(this.state.currentDateTime).format("DD MMM yyyy") }) }}>
                                <Text style={{ color: "#828282" }}>Yesterday</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ rev_date: moment(moment(this.state.currentDateTime).subtract(7, "day")).format("DD MMM yyyy"), rev_date1: moment(this.state.currentDateTime).format("DD MMM yyyy") }) }}>
                                <Text style={{ color: "#828282" }}>Last 7 days</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ rev_date: moment(moment(this.state.currentDateTime).subtract(1, "month")).format("DD MMM yyyy"), rev_date1: moment(this.state.currentDateTime).format("DD MMM yyyy") }) }}>
                                <Text style={{ color: "#828282" }}>Last 30 days</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ rev_date: moment(this.state.currentDateTime).startOf('month'), rev_date1: moment(this.state.currentDateTime).endOf("month") }) }}>
                                <Text style={{ color: "#828282" }}>This month</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ rev_date: moment(moment(this.state.currentDateTime).subtract(1, "month")).startOf('month'), rev_date1: moment(moment(this.state.currentDateTime).subtract(1, "month")).endOf("month") }) }}>
                                <Text style={{ color: "#828282" }}>Last month</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                <Text style={{ color: "#828282" }}>Custom range</Text>
                            </TouchableOpacity>
                            <View style={{ flexDirection: 'row' }}>
                                <View style={{ flex: 1, marginLeft: 25 }}>
                                    <Text style={{ color: "#828282" }}>From</Text>
                                </View>
                                <View style={{ flex: 1 }}>
                                    <Text style={{ color: "#828282" }}>To</Text>
                                </View>
                            </View>
                            <View style={{ flexDirection: 'row' }}>
                                <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                    onPress={() => { this.setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                    <Text style={{ fontSize: 12 }}>{moment(this.state.rev_date).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>
                                <View style={{ width: "8%" }}>
                                </View>
                                <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                    onPress={() => { this.setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                    <Text style={{ fontSize: 12 }}>{moment(this.state.rev_date1).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                    onPress={() => { this.setState({ day: false }) }}>
                                    <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                </TouchableOpacity>
                                <View style={{ width: "8%" }}>
                                </View>
                                <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                    onPress={() => { this.setState({ day: false }) }}>
                                    <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ height: 20 }}>
                            </View>
                        </View>
                        : null}

                    {this.state.filter ?
                        <View style={{ position: 'absolute', width: "50%", backgroundColor: Colors.whiteColor, left: 25, top: 170, zIndex: 6000, elevation: 2 }}>
                            <View style={{ padding: 10, borderBottomWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontSize: 16, marginLeft: 3 }}>Manage Filter</Text>
                            </View>
                            <View style={{ flexDirection: "row" }}>
                                <TouchableOpacity style={{ borderWidth: 2, borderColor: Colors.primaryColor, width: "30%", height: 35, marginTop: 15, marginLeft: 20, borderRadius: 5, backgroundColor: Colors.whiteColor, justifyContent: "center", alignItems: "center", flexDirection: "row" }} onPress={() => { this.changeClick2() }}>
                                    <Text style={{ color: "#ababab" }}>{this.state.supplier}</Text>
                                    <Feather name="chevron-down" size={30} color={"#ababab"} />
                                </TouchableOpacity>
                                <TouchableOpacity style={{ borderWidth: 2, borderColor: Colors.primaryColor, width: "30%", height: 35, marginTop: 15, marginLeft: 5, borderRadius: 5, backgroundColor: Colors.whiteColor, justifyContent: "center", alignItems: "center", flexDirection: "row" }} onPress={() => { this.changeClick3() }}>
                                    <Text style={{ marginRight: 15, color: "#ababab" }}>{this.state.product}</Text>
                                    <Feather name="chevron-down" size={30} color={"#ababab"} />
                                </TouchableOpacity>
                                <TouchableOpacity style={{ borderWidth: 2, borderColor: Colors.primaryColor, width: "30%", height: 35, marginTop: 15, marginLeft: 5, borderRadius: 5, backgroundColor: Colors.whiteColor, justifyContent: "center", alignItems: "center", flexDirection: "row" }} onPress={() => { this.changeClick4() }}>
                                    <Text style={{ marginRight: 15, color: "#ababab" }}>{this.state.sku}</Text>
                                    <Feather name="chevron-down" size={30} color={"#ababab"} />
                                </TouchableOpacity>
                            </View>
                            <View style={{ height: 50 }}></View>
                            <View style={{ flexDirection: "row", justifyContent: "center", alignItems: "center" }}>
                                <TouchableOpacity style={{ width: "30%", height: 30, backgroundColor: Colors.whiteColor, borderWidth: 1, justifyContent: "center", alignItems: "center", borderRadius: 5 }}
                                    onPress={() => { this.setState({ filter: false }) }}>
                                    <Text>Cancel</Text>
                                </TouchableOpacity>
                                <TouchableOpacity style={{ width: "30%", height: 30, backgroundColor: Colors.primaryColor, marginLeft: "3%", justifyContent: "center", alignItems: "center", borderRadius: 5 }}
                                    onPress={() => { this.getStockFilter(); this.setState({ filter: false }) }}>
                                    <Text style={{ color: Colors.whiteColor }}>Apply</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ height: 50 }}></View>

                            {this.state.filter1 ?
                                <View style={{ position: 'absolute', width: "30%", left: 22, top: 100, zIndex: 6000, elevation: 4, backgroundColor: Colors.whiteColor, height: 200 }}>
                                    <View style={{ padding: 10, borderBottomWidth: StyleSheet.hairlineWidth }}>
                                        <Text style={{ fontSize: 12, marginLeft: 3, color: "#ababab" }}>Supplier</Text>
                                    </View>
                                    <ScrollView style={{ height: '100%' }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "All Supplier" }) }}>
                                            <Text style={{ color: "#828282" }}>All Supplier</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "Noodles sdn bhd" }) }}>
                                            <Text style={{ color: "#828282" }}>Noodles sdn bhd</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "Noodles sdn bhd" }) }}>
                                            <Text style={{ color: "#828282" }}>Noodles sdn bhd</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "Noodles sdn bhd" }) }}>
                                            <Text style={{ color: "#828282" }}>Noodles sdn bhd</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "Noodles sdn bhd" }) }}>
                                            <Text style={{ color: "#828282" }}>Noodles sdn bhd</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ supplier: "Noodles sdn bhd" }) }}>
                                            <Text style={{ color: "#828282" }}>Noodles sdn bhd</Text>
                                        </TouchableOpacity>
                                    </ScrollView>
                                </View>
                                : null}
                            {this.state.filter2 ?
                                <View style={{ position: 'absolute', width: "30%", left: 170, top: 100, zIndex: 6000, elevation: 4, backgroundColor: Colors.whiteColor, height: 200 }}>
                                    <View style={{ padding: 10, borderBottomWidth: StyleSheet.hairlineWidth }}>
                                        <Text style={{ fontSize: 12, marginLeft: 3, color: "#ababab" }}>Product Category</Text>
                                    </View>
                                    <ScrollView style={{ height: '100%' }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ product: "Main Course" }) }}>
                                            <Text style={{ color: "#828282" }}>Main Course </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ product: "Desserts" }) }}>
                                            <Text style={{ color: "#828282" }}>Desserts</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ product: "Snacks" }) }}>
                                            <Text style={{ color: "#828282" }}>Snacks</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ product: "Beverages" }) }}>
                                            <Text style={{ color: "#828282" }}>Beverages</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ product: "Alcohol" }) }}>
                                            <Text style={{ color: "#828282" }}>Alcohol</Text>
                                        </TouchableOpacity>
                                    </ScrollView>
                                </View>
                                : null}
                            {this.state.filter3 ?
                                <View style={{ position: 'absolute', width: "30%", left: 320, top: 100, zIndex: 6000, elevation: 4, backgroundColor: Colors.whiteColor, height: 200 }}>
                                    <View style={{ padding: 10, borderBottomWidth: StyleSheet.hairlineWidth }}>
                                        <Text style={{ fontSize: 12, marginLeft: 3, color: "#ababab" }}>Product SKU</Text>
                                    </View>
                                    <ScrollView style={{ height: '100%' }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Main Course" }) }}>
                                            <Text style={{ color: "#828282" }}>Main course</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Rice" }) }}>
                                            <Text style={{ color: "#828282" }}>Rice</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Steak" }) }}>
                                            <Text style={{ color: "#828282" }}>Steak</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Salmon" }) }}>
                                            <Text style={{ color: "#828282" }}>Salmon</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Salad" }) }}>
                                            <Text style={{ color: "#828282" }}>Salad</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ sku: "Lamb" }) }}>
                                            <Text style={{ color: "#828282" }}>Lamb</Text>
                                        </TouchableOpacity>
                                    </ScrollView>
                                </View>
                                : null}

                        </View>
                        : null}

                    <Text style={{ marginTop: 30, marginLeft: 30, fontSize: 36, fontWeight: "700", fontFamily: 'NunitoSans-Bold' }}>Stock Value</Text>
                    {/* <Text style={{ marginTop: 10, marginLeft: 30, fontSize: 18, color: "#bababa", fontFamily: 'NunitoSans-Bold' }}>Date last updated on {moment(this.state.currentDateTime).format("DD MMM yyyy")}, {moment(this.state.currentDateTime).format("HH:mm a")}</Text> */}

                    <View style={{ flexDirection: "row", marginTop: 20, marginLeft: 30, justifyContent: 'space-between', width: '92.5%' }}>

                        <View style={{ marginRight: 10, flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingLeft: 30, borderRadius: 10, height: Dimensions.get('screen').height * 0.055 }}>
                            <View style={{ position: 'absolute', backgroundColor: Colors.whiteColor, width: 190, height: Dimensions.get('screen').height * 0.055, borderRadius: 10, }} />
                            <TouchableOpacity onPress={() => { this.changeClick1() }}>
                                <Text style={{ fontSize: 16, borderRightWidth: StyleSheet.hairlineWidth, paddingRight: 10, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>Filter</Text>
                            </TouchableOpacity>
                            <DropDownPicker
                                arrowColor={Colors.primaryColor}
                                arrowSize={23}
                                arrowStyle={{ fontWeight: 'bold' }}
                                style={{ width: Dimensions.get('screen').width * 0.1, borderWidth: 0 }}
                                items={[{ label: 'All Supplier', value: 1 }, { label: 'Date/Time', value: 2 }, { label: 'Name', value: 3 }, { label: 'Waiting Time', value: 4 }, { label: 'Payment Status', value: 5 }, { label: 'Total', value: 6 }]}
                                placeholder={"All Supplier"}
                                placeholderStyle={{ color: 'black' }}
                                onChangeItem={selectedSort => {
                                    this.setState({ sort: selectedSort })

                                }
                                }
                            />
                        </View>

                        <View style={{ marginRight: 10, flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingLeft: 30, borderRadius: 10, height: Dimensions.get('screen').height * 0.055 }}>
                            <View style={{ position: 'absolute', backgroundColor: Colors.whiteColor, width: 190, height: Dimensions.get('screen').height * 0.055, borderRadius: 10, }} />
                            <Text style={{ fontSize: 16, borderRightWidth: StyleSheet.hairlineWidth, paddingRight: 10, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>View By</Text>
                            <DropDownPicker
                                arrowColor={Colors.primaryColor}
                                arrowSize={23}
                                arrowStyle={{ fontWeight: 'bold' }}
                                style={{ width: Dimensions.get('screen').width * 0.1, borderWidth: 0 }}
                                items={[{ label: 'Product Name', value: 1 }, { label: 'SKU', value: 2 }, { label: 'Quantity', value: 3 }, { label: 'Cost', value: 4 }, { label: 'Price', value: 5 }]}
                                placeholder={"Product Name"}
                                placeholderStyle={{ color: 'black' }}
                                onChangeItem={selectedSort => {
                                    this.setState({ sort: selectedSort }),
                                        this.sortingOrders(selectedSort);

                                }
                                }
                            />
                        </View>

                        <TouchableOpacity style={{ marginRight: 10, width: 230, flexDirection: 'row', alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: Dimensions.get('screen').height * 0.055, backgroundColor: Colors.whiteColor }}
                            onPress={() => { this.changeClick() }}>
                            <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => { this.setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                {/* <EvilIcons name="calendar" size={30} color={Colors.primaryColor} /> */}
                                <GCalendar width={20} height={20} />
                            </TouchableOpacity>
                            <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(this.state.rev_date).format("DD MMM yyyy")} - {moment(this.state.rev_date1).format("DD MMM yyyy")}</Text>
                        </TouchableOpacity>

                        <TouchableOpacity style={{ marginRight: 10, width: 120, flexDirection: 'row', alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: Dimensions.get('screen').height * 0.055, backgroundColor: Colors.whiteColor }}
                            onPress={() => { this.download(this.state.list) }}>
                            <AntDesign name="download" size={20} color={Colors.primaryColor} />
                            <Text style={{ fontFamily: "NunitoSans-Regular", marginLeft: 10 }}>Download</Text>
                        </TouchableOpacity>

                        <TouchableOpacity style={{ width: 100, flexDirection: 'row', alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: Dimensions.get('screen').height * 0.055, backgroundColor: Colors.whiteColor }}
                            onPress={() => { this.email1(this.state.list) }}>
                            <AntDesign name="upload" size={20} color={Colors.primaryColor} />
                            <Text style={{ fontFamily: "NunitoSans-Regular", marginLeft: 10 }}>Email</Text>
                        </TouchableOpacity>

                    </View>

                    <View>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '95%' }}>
                            <View style={{ flexDirection: 'row', marginTop: 20, marginLeft: 30 }}>
                                <View style={{ width: "35%", height: 90, backgroundColor: "#63c99e", borderRadius: 5 }}>
                                    <Text style={{ marginTop: 5, color: Colors.whiteColor, fontSize: 35, fontWeight: '700', marginTop: 10, marginLeft: 20 }}>RM{this.state.item[0] === undefined ? 0 : this.state.item[0].totalValue}</Text>
                                    <Text style={{ color: Colors.whiteColor, marginLeft: 20, fontSize: 15 }}>Total Value</Text>
                                </View>
                                <View style={{ width: "35%", height: 90, backgroundColor: "#faca52", borderRadius: 5, marginLeft: 13 }}>
                                    <Text style={{ marginTop: 5, color: Colors.whiteColor, fontSize: 35, fontWeight: '700', marginTop: 10, marginLeft: 20 }}>{this.state.item[0] === undefined ? 0 : this.state.item[0].totalAmount}</Text>
                                    <Text style={{ color: Colors.whiteColor, marginLeft: 20, fontSize: 15 }}>Total Item(s)</Text>
                                </View>
                            </View>
                            {/* <View style={{ marginLeft: "10%", marginTop: 20 }}>
                                <TextInput
                                    editable={!this.state.loading}
                                    clearButtonMode="while-editing"
                                    style={styles.textInput}
                                    placeholder="🔍  search"
                                    onChangeText={(text) => {
                                        this.setState({ search: text.trim() });
                                        this.getSearch()
                                    }}
                                    value={this.state.search}
                                />
                                </View> */}
                            <View style={{ width: '20%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row', alignSelf: 'flex-end' }}>
                                <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginHorizontal: '5%' }} />
                                <TextInput
                                    editable={!this.state.loading}
                                    clearButtonMode="while-editing"
                                    //style={{ width: '50%' }}
                                    placeholder="Search"
                                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                    onChangeText={(text) => {
                                        this.setState({ search: text.trim() });
                                        this.getSearch()
                                    }}
                                    value={this.state.search}
                                />
                            </View>

                        </View>

                    </View>

                    <View style={{ marginTop: 20, marginLeft: 30, backgroundColor: Colors.whiteColor, width: "92%", height: 300 }}>

                        <View style={{ flexDirection: "row", paddingVertical: 30 }}>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "13%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>Product Name</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretup" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "9%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>SKU</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretup" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "12%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>Quantity On Hand</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretup" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "8%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>Cost (RM)</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "17%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>Price (tax-excluded) (RM)</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretup" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ flexDirection: "row", marginLeft: 20, width: "14%", borderRightWidth: StyleSheet.hairlineWidth }}>
                                <Text style={{ fontWeight: "700", fontSize: 12 }}>Cost X Quantity (RM)</Text>
                                <View style={{ marginTop: 2, marginLeft: 8 }}>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretup" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => { }}>
                                        <AntDesign name="caretdown" size={6} color={Colors.descriptionColor} />
                                    </TouchableOpacity>
                                </View>
                            </View>

                            <Text style={{ marginLeft: 20, width: "10%", fontWeight: "700", fontSize: 12 }}>Price X Quantity</Text>

                        </View>

                        <FlatList
                            data={this.state.list}
                            renderItem={this.renderList}
                            keyExtractor={(item, index) => String(index)}
                        />


                    </View>

                    <View style={{ marginTop: 20, marginLeft: "73%", flexDirection: "row" }}>
                        <Text style={{ marginRight: 10 }}>Page</Text>
                        <View style={{ borderWidth: 1, borderColor: Colors.descriptionColor, width: 60, alignItems: 'center', borderRadius: 5 }}>
                            <Text>{this.state.currentPage}</Text>
                        </View>
                        <Text>  of {this.state.pageCount}</Text>
                        <TouchableOpacity style={{ marginLeft: 10, width: 23, backgroundColor: Colors.primaryColor, alignItems: 'center', justifyContent: "center", borderTopLeftRadius: 5, borderBottomLeftRadius: 5 }}
                            onPress={() => { this.less() }}>
                            <EvilIcons name="chevron-left" size={20} color={Colors.whiteColor} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: 23, backgroundColor: Colors.primaryColor, alignItems: 'center', justifyContent: "center", borderTopRightRadius: 5, borderBottomRightRadius: 5 }} onPress={() => { this.add() }}>
                            <EvilIcons name="chevron-right" size={20} color={Colors.whiteColor} />
                        </TouchableOpacity>
                    </View>

                </View>
            </View>)
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row'
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    textInput: {
        width: 200,
        padding: 10,
        backgroundColor: Colors.whiteColor,
        borderRadius: 5,
        borderRadius: 5,
        elevation: 5,
        shadowColor: Colors.blackColor,
        shadowOpacity: 20, marginTop: 20
    },
    content: {
        padding: 16
    },
    confirmBox: {
        width: 400,
        height: 250,
        borderRadius: 10,
        backgroundColor: Colors.whiteColor,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center'
    },
});
export default ReportStockValue;
