/*
 * This class is used to generate TSC printer commands.
 * Note: All commands are based on the Gprinter Programming Manual.
 * Slight offsets may occur due to rounding of dot calculations based on DPI.
 * If issues arise with other printers, adjustments may be necessary based on their specific programming manuals.
 */

class TSCCommandGenerator {
	constructor(config = {}) {
		this.config = {
			dpi: 203,     // Printer DPI
			width: 70,    // Label Width (mm)
			height: 40,   // Label Height (mm)
			gap: 2,       // Label Gap (mm)
			direction: 1, // Print Direction
			tear: true,   // Tear On/Off

			lineHeight_3: 24, // Line Height for Text Size 3
			lineHeight_4: 32, // Line Height for Text Size 4
			lineHeight_5: 32, // Line Height for Text Size 4

			...config
		};

		this.labelDotWidth = Math.round(this.config.width * this.config.dpi / 25.4);
		this.labelDotHeight = Math.round(this.config.height * this.config.dpi / 25.4);
	}

	//=======================
	// TSC Command Generators (Private)
	//=======================

	generateInitializationCommand() {
		return `
      SIZE ${this.config.width} mm, ${this.config.height} mm
      GAP ${this.config.gap} mm, 0 mm
      DIRECTION ${this.config.direction}
      SET TEAR ${this.config.tear ? "ON" : "OFF"}
      CLS
    `;
	}

	generateTextCommand(textArray, startingX) {
		const lineHeights = {
			3: this.config.lineHeight_3,
			4: this.config.lineHeight_4,
			5: this.config.lineHeight_5
		};
		const gapSame = 5;
		const gapDiff = 10;

		const totalHeight = textArray.reduce((acc, item, index, arr) => {
			const currentHeight = lineHeights[item.size];
			if (arr.length === 1) {
				return currentHeight;
			}
			const gap = index > 0 ? (item.size !== arr[index - 1].size ? gapDiff : gapSame) : 0;
			return acc + currentHeight + gap;
		}, 0);

		// Calculate Starter y Position
		let y = Math.max(0, Math.round((this.labelDotHeight - totalHeight) / 2));

		// Generate Text Commands
		const commands = textArray.map((item, index, arr) => {
			const { size, content } = item;
			const command = `TEXT ${startingX},${y},${size},0,1,1,"${content}"`;

			// Update y position
			y += lineHeights[size];
			if (index < arr.length - 1) {
				y += arr[index + 1].size !== size ? gapDiff : gapSame;
			}

			return command;
		});

		return commands.join('\n');
	}

	generateQRCodeCommand(qrSize, content) {
		// Validate qrSize
		if (qrSize < 1 || qrSize > 10) {
			throw new Error('QR code size must be between 1 and 10');
		}

		// Validate content
		if (!content || content.trim() === '') {
			throw new Error('QR code content cannot be empty');
		}

		//const gap = 10; // Distance from QR code to the center of the label (Dots)
		const gap = this.labelDotWidth * 0.05;

		const qrCodeDotSize = Math.round(qrSize * this.config.dpi / 25.4);

		const x = Math.round(this.labelDotWidth / 2 - qrCodeDotSize - gap);
		const y = Math.round((this.labelDotHeight - qrCodeDotSize) / 2);

		return `QRCODE ${x},${y},${qrSize},0,M,"${content}"`;
	}

	generatePrintCommand(printQty) {
		return `PRINT ${printQty}`;
	}

	//=======================
	// Methods
	//=======================

	/**
	 * Command for generating Rack Label Command
	 * 
	 * @param {Object} labelData - Label data object
	 * @param {string} labelData.QR - QR code content
	 * @param {string} labelData.rackName - Rack name
	 * @param {number} labelData.printQty - Print quantity (default: 1)
	 * @returns {string} Generated TSC printer command
	 */
	generateRackLabelCommand(labelData) {
		const { QR, rackName, printQty = 1 } = labelData;

		// Data Not Empty Checking
		if (!QR || !rackName) {
			throw new Error('Missing required label data: labelData.QR or labelData.rackName');
		}

		const textArray = [
			{ size: 4, content: rackName },
		];

		const command = `
			${this.generateInitializationCommand()}
			${this.generateQRCodeCommand(5, QR)}
			${this.generateTextCommand(textArray, Math.round(this.labelDotWidth / 2))}
			${this.generatePrintCommand(printQty)}
		`.split('\n').map(line => line.trim()).join('\n').trim();

		// Return the generated command
		return command;
	}

	/**
	 * 
	 * @param {Object} labelData - Label data object
	 * @param {string} labelData.itemName - Item name
	 * @param {string} labelData.itemDesc - Item description
	 * @param {string} labelData.printQty - Print quantity (default: 1)
	 * @returns {string} Generated TSC printer command
	 */
	generateItemLabelCommand(labelData) {
		const { itemName, itemDesc, printQty = 1 } = labelData;

		// Data Not Empty Checking
		if (!itemName || !itemDesc) {
			throw new Error('Missing required label data: labelData.itemName or labelData.itemDesc');
		}

		// Return the generated command
		return `
			${this.generateInitializationCommand()}
			${this.generatePrintCommand(printQty)}
		`;
	}
}

export default TSCCommandGenerator;
