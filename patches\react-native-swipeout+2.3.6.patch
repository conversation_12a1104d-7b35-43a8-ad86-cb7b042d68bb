diff --git a/node_modules/react-native-swipeout/dist.zip b/node_modules/react-native-swipeout/dist.zip
new file mode 100644
index 0000000..2a82d83
Binary files /dev/null and b/node_modules/react-native-swipeout/dist.zip differ
diff --git a/node_modules/react-native-swipeout/dist/NativeButton.js b/node_modules/react-native-swipeout/dist/NativeButton.js
index 704a86e..408f2ec 100644
--- a/node_modules/react-native-swipeout/dist/NativeButton.js
+++ b/node_modules/react-native-swipeout/dist/NativeButton.js
@@ -1,84 +1,74 @@
-'use strict';
-
-Object.defineProperty(exports, "__esModule", {
-  value: true
-});
-
-var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };
-
-var _react = require('react');
-
-var _react2 = _interopRequireDefault(_react);
-
-var _propTypes = require('prop-types');
-
-var _propTypes2 = _interopRequireDefault(_propTypes);
-
-var _createReactClass = require('create-react-class');
-
-var _createReactClass2 = _interopRequireDefault(_createReactClass);
-
-var _reactNative = require('react-native');
-
-function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
-
-var styles = _reactNative.StyleSheet.create({
+import React from 'react';
+import PropTypes from 'prop-types';
+import createReactClass from 'create-react-class';
+
+import {
+  TouchableWithoutFeedback,
+  TouchableNativeFeedback,
+  TouchableHighlight,
+  Text,
+  StyleSheet,
+  Platform,
+  View,
+} from 'react-native';
+
+const styles = StyleSheet.create({
   button: {
     flexDirection: 'row',
     alignSelf: 'stretch',
-    justifyContent: 'center'
+    justifyContent: 'center',
   },
   textButton: {
     fontSize: 14,
-    alignSelf: 'center'
+    alignSelf: 'center',
   },
   opacity: {
-    opacity: 0.8
-  }
+    opacity: 0.8,
+  },
 });
 
-var NativeButton = (0, _createReactClass2.default)({
-  displayName: 'NativeButton',
+const NativeButton = createReactClass({
 
-
-  propTypes: _extends({}, _reactNative.TouchableWithoutFeedback.propTypes, {
-    textStyle: _propTypes2.default.any,
-    disabledStyle: _propTypes2.default.any,
-    children: _propTypes2.default.node.isRequired,
-    underlayColor: _propTypes2.default.string,
-    background: _propTypes2.default.any
-  }),
+  propTypes: {
+    // Extract parent props
+    ...TouchableWithoutFeedback.propTypes,
+    textStyle: PropTypes.any,
+    disabledStyle: PropTypes.any,
+    children: PropTypes.node.isRequired,
+    underlayColor: PropTypes.string,
+    background: PropTypes.any,
+  },
 
   statics: {
-    isAndroid: _reactNative.Platform.OS === 'android'
+    isAndroid: (Platform.OS === 'android'),
   },
 
-  getDefaultProps: function getDefaultProps() {
+  getDefaultProps: function () {
     return {
       textStyle: null,
       disabledStyle: null,
-      underlayColor: null
+      underlayColor: null,
     };
   },
 
-  _renderText: function _renderText() {
+  _renderText: function () {
     // If children is not a string don't wrapp it in a Text component
     if (typeof this.props.children !== 'string') {
       return this.props.children;
     }
 
-    return _react2.default.createElement(
-      _reactNative.Text,
-      { numberOfLines: 1, ellipsizeMode: _reactNative.Platform.OS === 'ios' ? 'clip' : 'tail', style: [styles.textButton, this.props.textStyle] },
-      this.props.children
+    return (
+      <Text numberOfLines={1} ellipsizeMode={Platform.OS === 'ios' ? 'clip' : 'tail'} style={[styles.textButton, this.props.textStyle]}>
+        {this.props.children}
+      </Text>
     );
   },
 
-  render: function render() {
-    var disabledStyle = this.props.disabled ? this.props.disabledStyle || styles.opacity : {};
+  render: function () {
+    const disabledStyle = this.props.disabled ? (this.props.disabledStyle || styles.opacity) : {};
 
     // Extract Button props
-    var buttonProps = {
+    let buttonProps = {
       accessibilityComponentType: this.props.accessibilityComponentType,
       accessibilityTraits: this.props.accessibilityTraits,
       accessible: this.props.accessible,
@@ -92,35 +82,35 @@ var NativeButton = (0, _createReactClass2.default)({
       onPressIn: this.props.onPressIn,
       onPressOut: this.props.onPressOut,
       onLongPress: this.props.onLongPress,
-      pressRetentionOffset: this.props.pressRetentionOffset
+      pressRetentionOffset: this.props.pressRetentionOffset,
     };
 
     // Render Native Android Button
     if (NativeButton.isAndroid) {
       buttonProps = Object.assign(buttonProps, {
-        background: this.props.background || _reactNative.TouchableNativeFeedback.SelectableBackground()
+        background: this.props.background || TouchableNativeFeedback.SelectableBackground(),
       });
 
-      return _react2.default.createElement(
-        _reactNative.TouchableNativeFeedback,
-        buttonProps,
-        _react2.default.createElement(
-          _reactNative.View,
-          { style: [styles.button, this.props.style, disabledStyle] },
-          this._renderText()
-        )
+      return (
+        <TouchableNativeFeedback
+          {...buttonProps}>
+          <View style={[styles.button, this.props.style, disabledStyle]}>
+            {this._renderText()}
+          </View>
+        </TouchableNativeFeedback>
       );
     }
 
     // Render default button
-    return _react2.default.createElement(
-      _reactNative.TouchableHighlight,
-      _extends({}, buttonProps, {
-        style: [styles.button, this.props.style, disabledStyle],
-        underlayColor: this.props.underlayColor }),
-      this._renderText()
+    return (
+      <TouchableHighlight
+        {...buttonProps}
+        style={[styles.button, this.props.style, disabledStyle]}
+        underlayColor={this.props.underlayColor}>
+        {this._renderText()}
+      </TouchableHighlight>
     );
   }
 });
 
-exports.default = NativeButton;
\ No newline at end of file
+export default NativeButton;
diff --git a/node_modules/react-native-swipeout/dist/index.js b/node_modules/react-native-swipeout/dist/index.js
index d4a976c..8f5f0a2 100644
--- a/node_modules/react-native-swipeout/dist/index.js
+++ b/node_modules/react-native-swipeout/dist/index.js
@@ -1,54 +1,37 @@
-'use strict';
+import tweenState from 'react-tween-state';
+import NativeButton from './NativeButton';
+import styles from './styles';
 
-Object.defineProperty(exports, "__esModule", {
-  value: true
-});
-
-var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };
-
-var _reactTweenState = require('react-tween-state');
-
-var _reactTweenState2 = _interopRequireDefault(_reactTweenState);
-
-var _NativeButton = require('./NativeButton');
-
-var _NativeButton2 = _interopRequireDefault(_NativeButton);
-
-var _styles = require('./styles');
-
-var _styles2 = _interopRequireDefault(_styles);
-
-var _react = require('react');
-
-var _react2 = _interopRequireDefault(_react);
-
-var _propTypes = require('prop-types');
+import React, {
+  Component,
+} from 'react';
+import PropTypes from 'prop-types';
+import createReactClass from 'create-react-class';
 
-var _propTypes2 = _interopRequireDefault(_propTypes);
+import {
+  PanResponder,
+  TouchableHighlight,
+  StyleSheet,
+  Text,
+  View,
+  // ViewPropTypes,
+} from 'react-native';
 
-var _createReactClass = require('create-react-class');
-
-var _createReactClass2 = _interopRequireDefault(_createReactClass);
-
-var _reactNative = require('react-native');
-
-function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
-
-var SwipeoutBtn = (0, _createReactClass2.default)({
-  displayName: 'SwipeoutBtn',
+import { ViewPropTypes } from 'deprecated-react-native-prop-types';
 
+const SwipeoutBtn = createReactClass({
 
   propTypes: {
-    backgroundColor: _propTypes2.default.string,
-    color: _propTypes2.default.string,
-    component: _propTypes2.default.node,
-    onPress: _propTypes2.default.func,
-    text: _propTypes2.default.node,
-    type: _propTypes2.default.string,
-    underlayColor: _propTypes2.default.string
+    backgroundColor: PropTypes.string,
+    color: PropTypes.string,
+    component: PropTypes.node,
+    onPress: PropTypes.func,
+    text: PropTypes.node,
+    type: PropTypes.string,
+    underlayColor: PropTypes.string,
   },
 
-  getDefaultProps: function getDefaultProps() {
+  getDefaultProps: function () {
     return {
       backgroundColor: null,
       color: null,
@@ -59,24 +42,26 @@ var SwipeoutBtn = (0, _createReactClass2.default)({
       disabled: false,
       text: 'Click me',
       type: '',
-      width: 0
+      width: 0,
     };
   },
 
-  render: function render() {
+  render: function () {
     var btn = this.props;
 
-    var styleSwipeoutBtn = [_styles2.default.swipeoutBtn];
+    var styleSwipeoutBtn = [styles.swipeoutBtn];
 
     //  apply "type" styles (delete || primary || secondary)
-    if (btn.type === 'delete') styleSwipeoutBtn.push(_styles2.default.colorDelete);else if (btn.type === 'primary') styleSwipeoutBtn.push(_styles2.default.colorPrimary);else if (btn.type === 'secondary') styleSwipeoutBtn.push(_styles2.default.colorSecondary);
+    if (btn.type === 'delete') styleSwipeoutBtn.push(styles.colorDelete);
+    else if (btn.type === 'primary') styleSwipeoutBtn.push(styles.colorPrimary);
+    else if (btn.type === 'secondary') styleSwipeoutBtn.push(styles.colorSecondary);
 
     //  apply background color
     if (btn.backgroundColor) styleSwipeoutBtn.push([{ backgroundColor: btn.backgroundColor }]);
 
     styleSwipeoutBtn.push([{
       height: btn.height,
-      width: btn.width
+      width: btn.width,
     }]);
 
     var styleSwipeoutBtnComponent = [];
@@ -84,61 +69,61 @@ var SwipeoutBtn = (0, _createReactClass2.default)({
     //  set button dimensions
     styleSwipeoutBtnComponent.push([{
       height: btn.height,
-      width: btn.width
+      width: btn.width,
     }]);
 
-    var styleSwipeoutBtnText = [_styles2.default.swipeoutBtnText];
+    var styleSwipeoutBtnText = [styles.swipeoutBtnText];
 
     //  apply text color
-    if (btn.color) styleSwipeoutBtnText.push({ color: btn.color });
-
-    return _react2.default.createElement(
-      _NativeButton2.default,
-      {
-        onPress: this.props.onPress,
-        underlayColor: this.props.underlayColor,
-        disabled: this.props.disabled,
-        style: [_styles2.default.swipeoutBtnTouchable, styleSwipeoutBtn],
-        textStyle: styleSwipeoutBtnText },
-      btn.component ? _react2.default.createElement(
-        _reactNative.View,
-        { style: styleSwipeoutBtnComponent },
-        btn.component
-      ) : btn.text
+    if (btn.color) styleSwipeoutBtnText.push({color: btn.color });
+
+    return (
+      <NativeButton
+        onPress={this.props.onPress}
+        underlayColor={this.props.underlayColor}
+        disabled={this.props.disabled}
+        style={[styles.swipeoutBtnTouchable, styleSwipeoutBtn]}
+        textStyle={styleSwipeoutBtnText}>
+        {
+          (btn.component ?
+            <View style={styleSwipeoutBtnComponent}>{btn.component}</View>
+            :
+            btn.text
+          )
+        }
+      </NativeButton>
     );
   }
 });
 
-var Swipeout = (0, _createReactClass2.default)({
-  displayName: 'Swipeout',
-
-  mixins: [_reactTweenState2.default.Mixin],
+const Swipeout = createReactClass({
+  mixins: [tweenState.Mixin],
 
   propTypes: {
-    autoClose: _propTypes2.default.bool,
-    backgroundColor: _propTypes2.default.string,
-    close: _propTypes2.default.bool,
-    left: _propTypes2.default.array,
-    onOpen: _propTypes2.default.func,
-    onClose: _propTypes2.default.func,
-    right: _propTypes2.default.array,
-    scroll: _propTypes2.default.func,
-    style: (_reactNative.ViewPropTypes || _reactNative.View.propTypes).style,
-    sensitivity: _propTypes2.default.number,
-    buttonWidth: _propTypes2.default.number,
-    disabled: _propTypes2.default.bool
+    autoClose: PropTypes.bool,
+    backgroundColor: PropTypes.string,
+    close: PropTypes.bool,
+    left: PropTypes.array,
+    onOpen: PropTypes.func,
+    onClose: PropTypes.func,
+    right: PropTypes.array,
+    scroll: PropTypes.func,
+    style: ViewPropTypes.style,
+    sensitivity: PropTypes.number,
+    buttonWidth: PropTypes.number,
+    disabled: PropTypes.bool,
   },
 
-  getDefaultProps: function getDefaultProps() {
+  getDefaultProps: function () {
     return {
       disabled: false,
       rowID: -1,
       sectionID: -1,
-      sensitivity: 50
+      sensitivity: 50,
     };
   },
 
-  getInitialState: function getInitialState() {
+  getInitialState: function () {
     return {
       autoClose: this.props.autoClose || false,
       btnWidth: 0,
@@ -150,87 +135,78 @@ var Swipeout = (0, _createReactClass2.default)({
       openedRight: false,
       swiping: false,
       tweenDuration: 160,
-      timeStart: null
+      timeStart: null,
     };
   },
 
-  componentWillMount: function componentWillMount() {
-    var _this = this;
-
-    this._panResponder = _reactNative.PanResponder.create({
-      onStartShouldSetPanResponder: function onStartShouldSetPanResponder(event, gestureState) {
-        return true;
-      },
-      onStartShouldSetPanResponderCapture: function onStartShouldSetPanResponderCapture(event, gestureState) {
-        return _this.state.openedLeft || _this.state.openedRight;
-      },
-      onMoveShouldSetPanResponderCapture: function onMoveShouldSetPanResponderCapture(event, gestureState) {
-        return Math.abs(gestureState.dx) > _this.props.sensitivity && Math.abs(gestureState.dy) <= _this.props.sensitivity;
-      },
+  componentWillMount: function () {
+    this._panResponder = PanResponder.create({
+      onStartShouldSetPanResponder: (event, gestureState) => true,
+      onStartShouldSetPanResponderCapture: (event, gestureState) =>
+        this.state.openedLeft || this.state.openedRight,
+      onMoveShouldSetPanResponderCapture: (event, gestureState) =>
+        Math.abs(gestureState.dx) > this.props.sensitivity &&
+        Math.abs(gestureState.dy) <= this.props.sensitivity,
       onPanResponderGrant: this._handlePanResponderGrant,
       onPanResponderMove: this._handlePanResponderMove,
       onPanResponderRelease: this._handlePanResponderEnd,
       onPanResponderTerminate: this._handlePanResponderEnd,
-      onShouldBlockNativeResponder: function onShouldBlockNativeResponder(event, gestureState) {
-        return false;
-      },
-      onPanResponderTerminationRequest: function onPanResponderTerminationRequest() {
-        return false;
-      }
+      onShouldBlockNativeResponder: (event, gestureState) => false,
+      onPanResponderTerminationRequest: () => false,
     });
   },
 
-  componentWillReceiveProps: function componentWillReceiveProps(nextProps) {
+  componentWillReceiveProps: function (nextProps) {
     if (nextProps.close) this._close();
     if (nextProps.openRight) this._openRight();
     if (nextProps.openLeft) this._openLeft();
   },
 
-  _handlePanResponderGrant: function _handlePanResponderGrant(e, gestureState) {
-    var _this2 = this;
-
+  _handlePanResponderGrant: function (e: Object, gestureState: Object) {
     if (this.props.disabled) return;
     if (!this.state.openedLeft && !this.state.openedRight) {
       this._callOnOpen();
     } else {
       this._callOnClose();
     }
-    this.refs.swipeoutContent.measure(function (ox, oy, width, height) {
-      var buttonWidth = _this2.props.buttonWidth || width / 5;
-      _this2.setState({
+    this.refs.swipeoutContent.measure((ox, oy, width, height) => {
+      let buttonWidth = this.props.buttonWidth || (width / 5);
+      this.setState({
         btnWidth: buttonWidth,
-        btnsLeftWidth: _this2.props.left ? buttonWidth * _this2.props.left.length : 0,
-        btnsRightWidth: _this2.props.right ? buttonWidth * _this2.props.right.length : 0,
+        btnsLeftWidth: this.props.left ? buttonWidth * this.props.left.length : 0,
+        btnsRightWidth: this.props.right ? buttonWidth * this.props.right.length : 0,
         swiping: true,
-        timeStart: new Date().getTime()
+        timeStart: (new Date()).getTime(),
       });
     });
   },
 
-  _handlePanResponderMove: function _handlePanResponderMove(e, gestureState) {
+  _handlePanResponderMove: function (e: Object, gestureState: Object) {
     if (this.props.disabled) return;
     var posX = gestureState.dx;
     var posY = gestureState.dy;
     var leftWidth = this.state.btnsLeftWidth;
     var rightWidth = this.state.btnsRightWidth;
-    if (this.state.openedRight) var posX = gestureState.dx - rightWidth;else if (this.state.openedLeft) var posX = gestureState.dx + leftWidth;
+    if (this.state.openedRight) var posX = gestureState.dx - rightWidth;
+    else if (this.state.openedLeft) var posX = gestureState.dx + leftWidth;
 
     //  prevent scroll if moveX is true
     var moveX = Math.abs(posX) > Math.abs(posY);
     if (this.props.scroll) {
-      if (moveX) this.props.scroll(false);else this.props.scroll(true);
+      if (moveX) this.props.scroll(false);
+      else this.props.scroll(true);
     }
     if (this.state.swiping) {
       //  move content to reveal swipeout
       if (posX < 0 && this.props.right) {
-        this.setState({ contentPos: Math.min(posX, 0) });
+        this.setState({ contentPos: Math.min(posX, 0) })
       } else if (posX > 0 && this.props.left) {
-        this.setState({ contentPos: Math.max(posX, 0) });
+        this.setState({ contentPos: Math.max(posX, 0) })
       };
     }
   },
 
-  _handlePanResponderEnd: function _handlePanResponderEnd(e, gestureState) {
+  _handlePanResponderEnd: function (e: Object, gestureState: Object) {
     if (this.props.disabled) return;
     var posX = gestureState.dx;
     var contentPos = this.state.contentPos;
@@ -250,7 +226,7 @@ var Swipeout = (0, _createReactClass2.default)({
     if (this.state.openedLeft) var openLeft = posX + openX > openX;
 
     //  reveal swipeout on quick swipe
-    var timeDiff = new Date().getTime() - this.state.timeStart < 200;
+    var timeDiff = (new Date()).getTime() - this.state.timeStart < 200;
     if (timeDiff) {
       var openRight = posX < -openX / 10 && !this.state.openedLeft;
       var openLeft = posX > openX / 10 && !this.state.openedRight;
@@ -270,51 +246,44 @@ var Swipeout = (0, _createReactClass2.default)({
     if (this.props.scroll) this.props.scroll(true);
   },
 
-  _tweenContent: function _tweenContent(state, endValue) {
+  _tweenContent: function (state, endValue) {
     this.tweenState(state, {
-      easing: _reactTweenState2.default.easingTypes.easeInOutQuad,
+      easing: tweenState.easingTypes.easeInOutQuad,
       duration: endValue === 0 ? this.state.tweenDuration * 1.5 : this.state.tweenDuration,
-      endValue: endValue
+      endValue: endValue,
     });
   },
 
-  _rubberBandEasing: function _rubberBandEasing(value, limit) {
-    if (value < 0 && value < limit) return limit - Math.pow(limit - value, 0.85);else if (value > 0 && value > limit) return limit + Math.pow(value - limit, 0.85);
+  _rubberBandEasing: function (value, limit) {
+    if (value < 0 && value < limit) return limit - Math.pow(limit - value, 0.85);
+    else if (value > 0 && value > limit) return limit + Math.pow(value - limit, 0.85);
     return value;
   },
 
   //  close swipeout on button press
-  _autoClose: function _autoClose(btn) {
+  _autoClose: function (btn) {
     if (this.state.autoClose) this._close();
     var onPress = btn.onPress;
     if (onPress) onPress();
   },
 
-  _open: function _open(contentPos, direction) {
-    var left = direction === 'left';
-    var _props = this.props,
-        sectionID = _props.sectionID,
-        rowID = _props.rowID,
-        onOpen = _props.onOpen;
-
+  _open: function (contentPos, direction) {
+    const left = direction === 'left';
+    const { sectionID, rowID, onOpen } = this.props;
     onOpen && onOpen(sectionID, rowID, direction);
     this._tweenContent('contentPos', contentPos);
     this.setState({
-      contentPos: contentPos,
+      contentPos,
       openedLeft: left,
       openedRight: !left,
-      swiping: false
+      swiping: false,
     });
   },
 
-  _close: function _close() {
-    var _props2 = this.props,
-        sectionID = _props2.sectionID,
-        rowID = _props2.rowID,
-        onClose = _props2.onClose;
-
+  _close: function () {
+    const { sectionID, rowID, onClose } = this.props;
     if (onClose && (this.state.openedLeft || this.state.openedRight)) {
-      var direction = this.state.openedRight ? 'right' : 'left';
+      const direction = this.state.openedRight ? 'right' : 'left';
       onClose(sectionID, rowID, direction);
     }
     this._tweenContent('contentPos', 0);
@@ -322,32 +291,30 @@ var Swipeout = (0, _createReactClass2.default)({
     this.setState({
       openedRight: false,
       openedLeft: false,
-      swiping: false
+      swiping: false,
     });
   },
 
-  _callOnClose: function _callOnClose() {
+  _callOnClose: function () {
     if (this.props.onClose) this.props.onClose(this.props.sectionID, this.props.rowID);
   },
 
-  _callOnOpen: function _callOnOpen() {
+  _callOnOpen: function () {
     if (this.props.onOpen) this.props.onOpen(this.props.sectionID, this.props.rowID);
   },
 
-  _openRight: function _openRight() {
-    var _this3 = this;
-
-    this.refs.swipeoutContent.measure(function (ox, oy, width, height) {
-      var btnWidth = _this3.props.buttonWidth || width / 5;
-
-      _this3.setState({
-        btnWidth: btnWidth,
-        btnsRightWidth: _this3.props.right ? btnWidth * _this3.props.right.length : 0
-      }, function () {
-        _this3._tweenContent('contentPos', -_this3.state.btnsRightWidth);
-        _this3._callOnOpen();
-        _this3.setState({
-          contentPos: -_this3.state.btnsRightWidth,
+  _openRight: function () {
+    this.refs.swipeoutContent.measure((ox, oy, width, height) => {
+      let btnWidth = this.props.buttonWidth || (width / 5);
+
+      this.setState({
+        btnWidth,
+        btnsRightWidth: this.props.right ? btnWidth * this.props.right.length : 0,
+      }, () => {
+        this._tweenContent('contentPos', -this.state.btnsRightWidth);
+        this._callOnOpen();
+        this.setState({
+          contentPos: -this.state.btnsRightWidth,
           openedLeft: false,
           openedRight: true,
           swiping: false
@@ -356,20 +323,18 @@ var Swipeout = (0, _createReactClass2.default)({
     });
   },
 
-  _openLeft: function _openLeft() {
-    var _this4 = this;
-
-    this.refs.swipeoutContent.measure(function (ox, oy, width, height) {
-      var btnWidth = _this4.props.buttonWidth || width / 5;
-
-      _this4.setState({
-        btnWidth: btnWidth,
-        btnsLeftWidth: _this4.props.left ? btnWidth * _this4.props.left.length : 0
-      }, function () {
-        _this4._tweenContent('contentPos', _this4.state.btnsLeftWidth);
-        _this4._callOnOpen();
-        _this4.setState({
-          contentPos: _this4.state.btnsLeftWidth,
+  _openLeft: function () {
+    this.refs.swipeoutContent.measure((ox, oy, width, height) => {
+      let btnWidth = this.props.buttonWidth || (width / 5);
+
+      this.setState({
+        btnWidth,
+        btnsLeftWidth: this.props.left ? btnWidth * this.props.left.length : 0,
+      }, () => {
+        this._tweenContent('contentPos', this.state.btnsLeftWidth);
+        this._callOnOpen();
+        this.setState({
+          contentPos: this.state.btnsLeftWidth,
           openedLeft: true,
           openedRight: false,
           swiping: false
@@ -378,11 +343,11 @@ var Swipeout = (0, _createReactClass2.default)({
     });
   },
 
-  render: function render() {
+  render: function () {
     var contentWidth = this.state.contentWidth;
     var posX = this.getTweeningValue('contentPos');
 
-    var styleSwipeout = [_styles2.default.swipeout, this.props.style];
+    var styleSwipeout = [styles.swipeout, this.props.style];
     if (this.props.backgroundColor) {
       styleSwipeout.push([{ backgroundColor: this.props.backgroundColor }]);
     }
@@ -394,95 +359,89 @@ var Swipeout = (0, _createReactClass2.default)({
       left: {
         left: 0,
         overflow: 'hidden',
-        width: Math.min(limit * (posX / limit), limit)
-      }
+        width: Math.min(limit * (posX / limit), limit),
+      },
     };
     var styleRightPos = {
       right: {
         left: Math.abs(contentWidth + Math.max(limit, posX)),
-        right: 0
-      }
+        right: 0,
+      },
     };
     var styleContentPos = {
       content: {
-        transform: [{ translateX: this._rubberBandEasing(posX, limit) }]
-      }
+        transform: [{ translateX: this._rubberBandEasing(posX, limit) }],
+      },
     };
 
-    var styleContent = [_styles2.default.swipeoutContent];
+    var styleContent = [styles.swipeoutContent];
     styleContent.push(styleContentPos.content);
 
-    var styleRight = [_styles2.default.swipeoutBtns];
+    var styleRight = [styles.swipeoutBtns];
     styleRight.push(styleRightPos.right);
 
-    var styleLeft = [_styles2.default.swipeoutBtns];
+    var styleLeft = [styles.swipeoutBtns];
     styleLeft.push(styleLeftPos.left);
 
     var isRightVisible = posX < 0;
     var isLeftVisible = posX > 0;
 
-    return _react2.default.createElement(
-      _reactNative.View,
-      { style: styleSwipeout },
-      _react2.default.createElement(
-        _reactNative.View,
-        _extends({
-          ref: 'swipeoutContent',
-          style: styleContent,
-          onLayout: this._onLayout
-        }, this._panResponder.panHandlers),
-        this.props.children
-      ),
-      this._renderButtons(this.props.right, isRightVisible, styleRight),
-      this._renderButtons(this.props.left, isLeftVisible, styleLeft)
+    return (
+      <View style={styleSwipeout}>
+        <View
+          ref="swipeoutContent"
+          style={styleContent}
+          onLayout={this._onLayout}
+          {...this._panResponder.panHandlers}
+        >
+          {this.props.children}
+        </View>
+        {this._renderButtons(this.props.right, isRightVisible, styleRight)}
+        {this._renderButtons(this.props.left, isLeftVisible, styleLeft)}
+      </View>
     );
   },
 
-  _onLayout: function _onLayout(event) {
-    var _event$nativeEvent$la = event.nativeEvent.layout,
-        width = _event$nativeEvent$la.width,
-        height = _event$nativeEvent$la.height;
-
+  _onLayout: function (event) {
+    var { width, height } = event.nativeEvent.layout;
     this.setState({
       contentWidth: width,
-      contentHeight: height
+      contentHeight: height,
     });
   },
 
-  _renderButtons: function _renderButtons(buttons, isVisible, style) {
+  _renderButtons: function (buttons, isVisible, style) {
     if (buttons && isVisible) {
-      return _react2.default.createElement(
-        _reactNative.View,
-        { style: style },
-        buttons.map(this._renderButton)
-      );
+      return (<View style={style}>
+        {buttons.map(this._renderButton)}
+      </View>);
     } else {
-      return _react2.default.createElement(_reactNative.View, null);
+      return (
+        <View />
+      );
     }
   },
 
-  _renderButton: function _renderButton(btn, i) {
-    var _this5 = this;
-
-    return _react2.default.createElement(SwipeoutBtn, {
-      backgroundColor: btn.backgroundColor,
-      color: btn.color,
-      component: btn.component,
-      disabled: btn.disabled,
-      height: this.state.contentHeight,
-      key: i,
-      onPress: function onPress() {
-        return _this5._autoClose(btn);
-      },
-      text: btn.text,
-      type: btn.type,
-      underlayColor: btn.underlayColor,
-      width: this.state.btnWidth
-    });
+  _renderButton: function (btn, i) {
+    return (
+      <SwipeoutBtn
+        backgroundColor={btn.backgroundColor}
+        color={btn.color}
+        component={btn.component}
+        disabled={btn.disabled}
+        height={this.state.contentHeight}
+        key={i}
+        onPress={() => this._autoClose(btn)}
+        text={btn.text}
+        type={btn.type}
+        underlayColor={btn.underlayColor}
+        width={this.state.btnWidth}
+      />
+    );
   }
-});
+})
 
-Swipeout.NativeButton = _NativeButton2.default;
+Swipeout.NativeButton = NativeButton;
 Swipeout.SwipeoutButton = SwipeoutBtn;
 
-exports.default = Swipeout;
\ No newline at end of file
+export default Swipeout;
diff --git a/node_modules/react-native-swipeout/dist/styles.js b/node_modules/react-native-swipeout/dist/styles.js
index 9075ce3..78f6100 100644
--- a/node_modules/react-native-swipeout/dist/styles.js
+++ b/node_modules/react-native-swipeout/dist/styles.js
@@ -1,29 +1,25 @@
-'use strict';
+import {
+  StyleSheet,
+} from 'react-native';
 
-Object.defineProperty(exports, "__esModule", {
-  value: true
-});
-
-var _reactNative = require('react-native');
-
-var styles = _reactNative.StyleSheet.create({
+const styles = StyleSheet.create({
   swipeout: {
     backgroundColor: '#dbddde',
-    overflow: 'hidden'
+    overflow: 'hidden',
   },
   swipeoutBtnTouchable: {
-    flex: 1
+    flex: 1,
   },
   swipeoutBtn: {
     alignItems: 'center',
     backgroundColor: '#b6bec0',
     flex: 1,
     justifyContent: 'center',
-    overflow: 'hidden'
+    overflow: 'hidden',
   },
   swipeoutBtnText: {
     color: '#fff',
-    textAlign: 'center'
+    textAlign: 'center',
   },
   swipeoutBtns: {
     bottom: 0,
@@ -31,18 +27,19 @@ var styles = _reactNative.StyleSheet.create({
     flexDirection: 'row',
     position: 'absolute',
     right: 0,
-    top: 0
+    top: 0,
+  },
+  swipeoutContent: {
   },
-  swipeoutContent: {},
   colorDelete: {
-    backgroundColor: '#fb3d38'
+    backgroundColor: '#fb3d38',
   },
   colorPrimary: {
     backgroundColor: '#006fff'
   },
   colorSecondary: {
     backgroundColor: '#fd9427'
-  }
-});
+  },
+})
 
-exports.default = styles;
\ No newline at end of file
+export default styles;
diff --git a/node_modules/react-native-swipeout/src/index.js b/node_modules/react-native-swipeout/src/index.js
index 749a209..8f5f0a2 100644
--- a/node_modules/react-native-swipeout/src/index.js
+++ b/node_modules/react-native-swipeout/src/index.js
@@ -14,9 +14,11 @@ import {
   StyleSheet,
   Text,
   View,
-  ViewPropTypes,
+  // ViewPropTypes,
 } from 'react-native';
 
+import { ViewPropTypes } from 'deprecated-react-native-prop-types';
+
 const SwipeoutBtn = createReactClass({
 
   propTypes: {
@@ -106,7 +108,7 @@ const Swipeout = createReactClass({
     onClose: PropTypes.func,
     right: PropTypes.array,
     scroll: PropTypes.func,
-    style: (ViewPropTypes || View.propTypes).style,
+    style: ViewPropTypes.style,
     sensitivity: PropTypes.number,
     buttonWidth: PropTypes.number,
     disabled: PropTypes.bool,
