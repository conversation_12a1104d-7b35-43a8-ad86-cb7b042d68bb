@echo off
setlocal enabledelayedexpansion

set "JAVA_17_PATH=C:\Program Files\Java\jdk-17"
set "JAVA_11_PATH=C:\Program Files\Java\jdk-11.0.12"

:menu
cls
echo Java Version Manager
echo ---------------------
echo 1. Set Java 17
echo 2. Set Java 11
echo 3. Show current Java version
echo 4. Exit
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto set_java_17
if "%choice%"=="2" goto set_java_11
if "%choice%"=="3" goto show_version
if "%choice%"=="4" goto end

echo Invalid choice. Please try again.
pause
goto menu

:set_java_17
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
setx JAVA_HOME "C:\Program Files\Java\jdk-17"
call :update_path "%JAVA_17_PATH%"
echo Java 17 has been set as the current Java version.
pause
goto menu

:set_java_11
set "JAVA_HOME=C:\Program Files\Java\jdk-11.0.12"
setx JAVA_HOME "C:\Program Files\Java\jdk-11.0.12"
call :update_path "%JAVA_11_PATH%"
echo Java 11 has been set as the current Java version.
pause
goto menu

:show_version
java -version
pause
goto menu

:update_path
set "new_path=%~1\bin"
set "updated_path=!new_path!"
for %%i in ("%PATH:;=";"%") do (
    set "part=%%~i"
    if not "!part:Java=!"=="!part!" (
        rem Skip Java paths
    ) else (
        set "updated_path=!updated_path!;!part!"
    )
)
setx PATH "!updated_path!" /M
set "PATH=!updated_path!"
echo %JAVA_HOME%
goto :eof

:end
echo Goodbye!
exit /b