import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
  isTablet, listenToCurrOutletIdReservationChanges
} from '../util/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common';
import CheckBox from 'react-native-check-box';
import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import moment, { isDate } from 'moment';
// import Barcode from 'react-native-barcode-builder';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { qrUrl } from '../constant/env';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";

const SettingReservationScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [keyboardHeight] = useKeyboard();

  const week = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  const [selectedCutOffTime, setSelectedCutOffTime] = useState('11:59 PM');

  const [cutOffTime, setCutOffTime] = useState([
    { label: '8:30 AM', value: '8:30 AM' },
    { label: '9:00 AM', value: '9:00 AM' },
    { label: '9:30 AM', value: '9:30 AM' },
    { label: '10:00 AM', value: '10:00 AM' },
    { label: '10:30 AM', value: '10:30 AM' },
    { label: '11:00 AM', value: '11:00 AM' },
    { label: '11:30 AM', value: '11:30 AM' },
    { label: '12:00 PM', value: '12:00 PM' },
    { label: '12:30 PM', value: '12:30 PM' },
    { label: '1:00 PM', value: '1:00 PM' },
    { label: '1:30 PM', value: '1:30 PM' },
    { label: '2:00 PM', value: '2:00 PM' },
    { label: '2:30 PM', value: '2:30 PM' },
    { label: '3:00 PM', value: '3:00 PM' },
    { label: '3:30 PM', value: '3:30 PM' },
    { label: '4:00 PM', value: '4:00 PM' },
    { label: '4:30 PM', value: '4:30 PM' },
    { label: '5:00 PM', value: '5:00 PM' },
    { label: '5:30 PM', value: '5:30 PM' },
    { label: '6:00 PM', value: '6:00 PM' },
    { label: '6:30 PM', value: '6:30 PM' },
    { label: '7:00 PM', value: '7:00 PM' },
    { label: '7:30 PM', value: '7:30 PM' },
    { label: '8:00 PM', value: '8:00 PM' },
    { label: '8:30 PM', value: '8:30 PM' },
    { label: '9:00 PM', value: '9:00 PM' },
    { label: '9:30 PM', value: '9:30 PM' },
    { label: '10:00 PM', value: '10:00 PM' },
  ]);

  const [selfCollect, setSelfCollect] = useState(true);
  const [openHourPickerVisible, setOpenHourPickerVisible] = useState(false);
  const [closeHourPickerVisible, setCloseHourPickerVisible] = useState(false);
  const [openHour, setOpenHour] = useState('');
  const [closeHour, setCloseHour] = useState('');
  const [isChecked2, setIsChecked2] = useState(false);
  const [isChecked3, setIsChecked3] = useState(false);
  const [isChecked4, setIsChecked4] = useState(false);
  const [isChecked5, setIsChecked5] = useState(false);
  const [isChecked6, setIsChecked6] = useState(false);
  const [isChecked7, setIsChecked7] = useState(false);
  const [isChecked8, setIsChecked8] = useState(false);
  const [isChecked9, setIsChecked9] = useState(false);
  const [isChecked10, setIsChecked10] = useState(false);
  const [isChecked11, setIsChecked11] = useState(false);
  const [isChecked12, setIsChecked12] = useState(false);
  const [isChecked13, setIsChecked13] = useState(false);
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [amount, setAmount] = useState('');
  const [hourStart, setHourStart] = useState('');
  const [hourEnd, setHourEnd] = useState('');
  const [days, setDays] = useState(false);
  const [days1, setDays1] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDistance, setShowDistance] = useState('');
  const [expiryPeriod, setExpiryPeriod] = useState('');
  const [extentionCharges, setExtentionCharges] = useState('');
  const [extentionDuration, setExtentionDuration] = useState('');
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [merchantDisplay, setMerchantDisplay] = useState(false);
  const [shift, setShift] = useState(false);
  const [tax, setTax] = useState(false);
  const [sample, setSample] = useState(false);
  const [redemption, setRedemption] = useState(false);
  const [redemptionList, setRedemptionList] = useState(false);
  const [redemptionAdd, setRedemptionAdd] = useState(false);
  const [order, setOrder] = useState(true);
  const [previousState, setPreviousState] = useState(false);
  const [receipt, setReceipt] = useState([]);
  const [detail, setDetail] = useState([]);
  const [merchantInfo, setMerchantInfo] = useState([]);
  const [outlet, setOutlet] = useState([]);
  const [outletInfo, setOutletInfo] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [merInfo, setMerInfo] = useState([]);
  // const [merchantId, setMerchantId] = useState([]);
  const [show, setShow] = useState(false);
  const [showModal3, setShowModal3] = useState(false);
  const [showModal4, setShowModal4] = useState(false);
  const [showModal5, setShowModal5] = useState(false);
  const [closingAmount, setClosingAmount] = useState('');
  const [options, setOptions] = useState([]);
  const [shift1, setShift1] = useState([]);
  const [status, setStatus] = useState(false);
  const [logo, setLogo] = useState('');
  const [cover, setCover] = useState('');
  const [name, setName] = useState('');
  const [tname, setTname] = useState('');
  const [rate, setRate] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [payment, setPayment] = useState('');
  const [time, setTime] = useState('');
  const [statue, setStatue] = useState('');
  const [status1, setStatus1] = useState(false);
  const [outlets, setOutlets] = useState([]);
  const [outletId, setOutletId] = useState(null);
  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [start_time, setStart_time] = useState(false);
  const [end_time, setEnd_time] = useState(false);
  const [rev_time, setRev_time] = useState('');
  const [category, setCategory] = useState('');
  const [close, setClose] = useState('Closed');
  const [showNote, setShowNote] = useState(false);
  const [expandView, setExpandView] = useState(false);
  const [value, setValue] = useState('');
  const [extendOption, setExtendOption] = useState([
    { optionId: 1, price: 20, day: 7, days: false },
  ]);
  const [redemptionInfo, setRedemptionInfo] = useState([]);
  const [alloutlet, setAlloutlet] = useState([]);
  const [discount, setDiscount] = useState('');
  const [amount1, setAmount1] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [extend, setExtend] = useState([]);
  const [outletss, setOutletss] = useState([]);
  const [redemptionDetail, setRedemptionDetail] = useState([]);
  const [outletInfo1, setOutletInfo1] = useState([]);
  const [category1, setCategory1] = useState([]);
  // const [merchantName, setMerchantName] = useState('');
  const [addOutletName, setAddOutletName] = useState('');
  const [addOutletWindow, setAddOutletWindow] = useState(false);
  const [taxList, setTaxList] = useState([]);
  const [note1, setNote1] = useState('');
  const [note2, setNote2] = useState('');
  const [note3, setNote3] = useState('');
  const [openings, setOpenings] = useState([]);
  const [editOpeningIndex, setEditOpeningIndex] = useState(0);

  const [temp, setTemp] = useState('');
  const [tempMinSpent, setTempMinSpent] = useState('');

  //////////////////////////////////////////////////////////////////////////
  const [reservationAvailablityHour, setReservationAvailablityHour] =
    useState(0);
  const [reservationAvailablityMinute, setReservationAvailablityMinute] =
    useState(0);
  const [deposit, setDeposit] = useState(0);
  const [price, setPrice] = useState(0);
  const [termCondition, setTermCondition] = useState('');
  // toggle
  const [depositSwitch, setDepositSwitch] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////

  const [deliveryDistance, setDeliveryDistance] = useState('1');
  const [freeDeliveyAboveAmountValue, setFreeDeliveyAboveAmountValue] =
    useState('');
  const [freeDeliveyAboveAmountFlag, setFreeDeliveyAboveAmountFlag] =
    useState(false);
  const [discountOrderAboveAmountValue, setDiscountOrderAboveAmountValue] =
    useState('');
  const [
    discountOrderAboveAmountThreshold,
    setDiscountOrderAboveAmountThreshold,
  ] = useState('');

  const [deliveryPrice, setDeliveryPrice] = useState(0);
  const [pickUpPrice, setPickUpPrice] = useState(0);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const [newReservationStatus, setNewReservationStatus] = useState(false);

  const [data] = useState(['DEPOSIT', 'RESERVATION FEES']);
  const [selectedData, setSelectedData] = useState('');

  const [daysBefore, setDaysBefore] = useState('0');
  const [maxPax, setMaxPax] = useState('20');
  const [minSpent, setMinSpent] = useState('0');

  const [printKdOsBeforeMinutes, setPrintKdOsBeforeMinutes] = useState('60');

  /////////////////////////////////////////////

  // 2024-03-06 - ReservationConfig support

  const reservationConfig = OutletStore.useState(s => s.reservationConfig);

  /////////////////////////////////////////////

  const reservationLink = `${qrUrl}outlet/${currOutlet ? currOutlet.subdomain : ''}/reservation`;

  const handleLinkPress = () => {
    Linking.openURL(reservationLink);
  };

  const getOutlet = () => {
    // ApiClient.GET(API.outlet + User.getOutletId())
    //   .then((result) => {
    //     setState({switchState: result[0].queueStatus});
    //   })
    //   .catch((err) => {
    //     // console.log(err);
    //   });
  };

  const switchQueueStatus = (value) => {
    // self.onButtonClickHandler();

    var body = {
      // outletId: User.getOutletId()
      outletId: currOutlet.uniqueId,
      queueStatus: value,
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    ApiClient.POST(API.switchQueueStatus, body)
      .then((result) => {
        if (result.status) {
          setTimeout(() => {
            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }, 1000);
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  useEffect(() => {
    const selectedTargetOutlet = allOutlets.find(
      (outlet) => outlet.uniqueId === selectedTargetOutletId,
    );

    if (selectedTargetOutlet) {
      setDeliveryDistance(
        selectedTargetOutlet.deliveryDistance
          ? selectedTargetOutlet.deliveryDistance.toFixed(0)
          : '1',
      );
      setFreeDeliveyAboveAmountValue(
        selectedTargetOutlet.freeDeliveyAboveAmountValue
          ? selectedTargetOutlet.freeDeliveyAboveAmountValue.toFixed(2)
          : '0',
      );
      setFreeDeliveyAboveAmountFlag(
        selectedTargetOutlet.freeDeliveyAboveAmountFlag
          ? selectedTargetOutlet.freeDeliveyAboveAmountFlag
          : false,
      );
      setDiscountOrderAboveAmountValue(
        selectedTargetOutlet.discountOrderAboveAmountValue
          ? selectedTargetOutlet.discountOrderAboveAmountValue.toFixed(2)
          : '0',
      );
      setDiscountOrderAboveAmountThreshold(
        selectedTargetOutlet.discountOrderAboveAmountThreshold
          ? selectedTargetOutlet.discountOrderAboveAmountThreshold.toFixed(2)
          : '0',
      );

      setDeliveryPrice(
        selectedTargetOutlet.deliveryPrice
          ? parseFloat(selectedTargetOutlet.deliveryPrice).toFixed(2)
          : 0,
      );
      setPickUpPrice(
        selectedTargetOutlet.pickUpPrice
          ? parseFloat(selectedTargetOutlet.pickUpPrice).toFixed(2)
          : 0,
      );
    }
  }, [selectedTargetOutletId]);

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      })),
    );

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  //////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      // means done loading

      let daysBeforeTemp = '0';
      let maxPaxTemp = '20';

      daysBeforeTemp = currOutlet.daysBefore !== undefined ? currOutlet.daysBefore : '0';
      maxPaxTemp = currOutlet.outletMaxPax !== undefined ? currOutlet.outletMaxPax : '20';

      setDaysBefore(daysBeforeTemp);
      setMaxPax(maxPaxTemp);
    }
  }, [currOutlet]);

  useEffect(() => {
    if (reservationConfig !== null) {
      // means done loading

      let cutOffTimeTemp = '10:00 PM';
      let minSpentTemp = '0';
      let printKdOsBeforeMinutesTemp = '60';

      if (reservationConfig && reservationConfig.uniqueId) {
        cutOffTimeTemp = reservationConfig.cutOffTime;
        minSpentTemp = reservationConfig.minSpent.toFixed(2);
        printKdOsBeforeMinutesTemp = reservationConfig.printKdOsBeforeMinutes !== undefined ? reservationConfig.printKdOsBeforeMinutes.toFixed(0) : 60;
      }

      setSelectedCutOffTime(cutOffTimeTemp);
      setMinSpent(minSpentTemp);
      setPrintKdOsBeforeMinutes(printKdOsBeforeMinutesTemp);
    }
  }, [reservationConfig]);

  //////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Reservation Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {

  //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
  //     setState({ outletInfo: result });
  //     result.map((element) => {
  //       setState({
  //         outletId: element.id,
  //         outletName: element.name,
  //         merchantName: element.merchant.name
  //       });
  //     });
  //   });

  //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
  //     setState({ redemptionInfo: result });
  //   });

  //   outlet()
  //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
  //     if (result !== undefined) {
  //       setState({ categoryOutlet: result });
  //     }

  //   });
  // }

  const orderFunc = () => {
    setLoading(true)
    // var body = {
    //   merchantId: User.getMerchantId(),
    //   isAllOutlet: isChecked6 == true ? '1' : null,
    //   outletId: outletId,
    //   deliveryDistance: showDistance,
    //   deliveryFee: isChecked6 == true ? amount : amount1,
    //   deliveryHourStart: hourStart,
    //   deliveryHourEnd: hourEnd,
    //   deliveryPrice: isChecked8 == true ? value1 : 0,
    //   pickUpPrice: isChecked9 == true ? value2 : 0,
    //   fireorder: status1,
    //   category: category,
    // };

    var body = {
      merchantId,
      outletId: selectedTargetOutletId,
      deliveryDistance: +parseFloat(deliveryDistance).toFixed(2),
      freeDeliveyAboveAmountValue: +parseFloat(
        freeDeliveyAboveAmountValue,
      ).toFixed(2),
      freeDeliveyAboveAmountFlag,
      discountOrderAboveAmountValue: +parseFloat(
        discountOrderAboveAmountValue,
      ).toFixed(2),
      discountOrderAboveAmountThreshold: +parseFloat(
        discountOrderAboveAmountThreshold,
      ).toFixed(2),
      deliveryPrice,
      pickUpPrice,

      daysBefore,
      maxPax,
    };

    // console.log(body);
    APILocal.updateOutletOrderDetails({ body }).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'Updated the settings successfully.',
          [
            {
              text: 'OK',
              onPress: () => { setLoading(false) },
            },
          ],
          { cancelable: false },
        );
      }
    });
  };

  const reservationConfigFunc = () => {
    var body = {
      cutOffTime: selectedCutOffTime,
      minSpent: parseFloat(minSpent),
      printKdOsBeforeMinutes: parseFloat(printKdOsBeforeMinutes),

      outletId: currOutlet.uniqueId,
      merchantId: currOutlet.merchantId,

      reservationConfigId: (reservationConfig && reservationConfig.uniqueId) ? reservationConfig.uniqueId : '',
    };

    if (reservationConfig === null) {
      // means snapshot still haven't run

      Alert.alert(
        'Info',
        'System is still trying to retrieve the reservation settings, please try again later.',
      );

      return;
    }

    if (reservationConfig && reservationConfig.uniqueId === undefined) {
      // means new create first

      setLoading(true);

      APILocal.createReservationSettings({ body }).then((result) => {

        if (result && result.status === 'success') {
          setLoading(false);
        }
      });
    }
    else if (reservationConfig && reservationConfig.uniqueId) {
      // can update

      setLoading(true);

      APILocal.updateReservationSettings({ body }).then((result) => {

        if (result && result.status === 'success') {
          setLoading(false);
        }
      });
    }
  };

  const _logout = async () => {
    await AsyncStorage.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();
  };

  const addSection = () => {
    setState({ showNote: true });
  };

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>

    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={10}
            expandSettings
          />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal>
            <View style={styles.content}>
              {order ? (
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    // height: windowHeight - 120,
                    height: '100%',
                    width: windowWidth * 0.87,
                    alignSelf: 'center',

                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 1,
                    elevation: 3,
                    borderRadius: 5,

                    // borderRadius: 8,
                  }}>
                  <ScrollView
                    contentContainerStyle={{
                      // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                    }}>
                    <View
                      style={
                        {
                          // marginBottom: 15,
                          // borderBottomWidth: StyleSheet.hairlineWidth,
                        }
                      }>
                      <View
                        style={{ flexDirection: 'row', padding: 30, zIndex: -3 }}>
                        <View style={{ flex: 3 }}>
                          {/* <View style={{ flexDirection: 'row', flex: 1, width: '100%', }}>
                      {/* left */}
                          {/* <View style={{ justifyContent: 'center', flex: 1 }}>
                        <Text style={[styles.textSize, {fontSize: switchMerchant ? 10 : 16}]}>Choose Outlet </Text>
                      </View> */}
                          {/* <View style={{ flex: 1 }}>
                        <View
                          style={{
                            width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26,
                            height: 50,
                            justifyContent: 'center',
                            marginLeft: '5.3%',

                          }}>
                          <DropDownPicker
                            // items={outletInfo.map((item) => ({
                            //   label: item.name, //<= after hayden change you need to change it to item.name
                            //   value: item.id,
                            // }))}
                            // defaultValue={outletId}
                            labelStyle={{fontSize: switchMerchant ? 8 : 16}}
                            containerStyle={{ height: 40 }}
                            // placeholder="Choose outlet"
                            items={targetOutletDropdownList}
                            defaultValue={selectedTargetOutletId}
                            placeholderStyle={{ color: 'black' }}
                            style={{ backgroundColor: '#fafafa' }}
                            itemStyle={{
                              justifyContent: 'flex-start', marginLeft: 5,
                            }}
                            dropDownStyle={{ backgroundColor: '#fafafa' }}
                            onChangeItem={(item) => {
                              // setState({ outletId: item.value });
                              // setOutletId(item.value);
                              setSelectedTargetOutletId(item.value)
                            }}
                          />
                        </View>
                      </View> */}
                          {/* <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              width: '100%',
                            }}>
                            {/* left *
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Availability{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.112,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.1
                                        : windowWidth * 0.11,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="0"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(reservationAvailablityHour)
                                    setReservationAvailablityHour('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (reservationAvailablityHour == '') {
                                      setReservationAvailablityHour(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setReservationAvailablityHour(text);
                                  }}
                                  value={reservationAvailablityHour}
                                  keyboardType={'decimal-pad'}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  paddingRight: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={[{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }, windowWidth <= 1823 &&
                                    windowWidth >= 1820
                                    ? { marginRight: windowWidth * 0.01 } : {},]}>
                                  Hour
                                </Text>
                              </View>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.112,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.1
                                        : windowWidth * 0.112,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="0"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(reservationAvailablityMinute)
                                    setReservationAvailablityMinute('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (reservationAvailablityMinute == '') {
                                      setReservationAvailablityMinute(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setReservationAvailablityMinute(text);
                                  }}
                                  value={reservationAvailablityMinute}
                                  keyboardType={'decimal-pad'}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Minute
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          {/* <View
                        style={{
                          justifyContent: 'center',
                          flex: 1,
                          marginLeft: '5%',
                        }}> */}
                          {/* <CheckBox
                          style={{
                            padding: 10,
                          }}
                          onClick={() => {
                            // setState({
                            //   isChecked6: !isChecked6,
                            // });
                            setIsChecked6(!isChecked6);
                          }}
                          checkBoxColor={Colors.fieldtBgColor}
                          uncheckedCheckBoxColor={Colors.tabGrey}
                          checkedCheckBoxColor={Colors.primaryColor}
                          isChecked={isChecked6}
                          rightText={'All Outlets'}
                          rightTextStyle={{
                            fontSize: 15,
                            fontWeight: 'bold',
                            color: Colors.descriptionColor,
                          }}
                        /> */}
                          {/* </View> */}
                          {/* </View> */}

                          {/* <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left *
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                flex: switchMerchant ? 0.87 : 0.88,
                                flexDirection: 'row',
                              }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingTop: switchMerchant ? 15 : 10,
                                    },
                                  ]}>
                                  Deposit{' '}
                                </Text>
                              </View>
                              <View
                                style={{
                                  justifyContent: 'center',
                                  flex: 1,
                                  flexDirection: 'column',
                                }}>
                                <Switch
                                  value={depositSwitch}
                                  onSyncPress={(value) => {
                                    // setState({ newReservationStatus: item }, function  = () => {
                                    //   switchQueueStatus();
                                    // });
                                    // switchQueueStatus(value);
                                    setDepositSwitch(!depositSwitch);
                                  }}
                                  width={42}
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                                <Text
                                  style={[
                                    {
                                      fontSize: 16,
                                      marginTop: 0,
                                      color: currOutlet && currOutlet.reservationStatus
                                        ? Colors.primaryColor
                                        : Colors.fieldtTxtColor,
                                      textAlign: 'center',
                                      right: Platform.OS == 'ios' ? 1 : 0,
                                      paddingRight: switchMerchant
                                        ? '75%'
                                        : windowWidth <=
                                          1823 &&
                                          windowWidth >= 1820
                                          ? '90%'
                                          : '83%',
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}>
                                  {depositSwitch ? 'ON' : 'OFF'}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginRight: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  marginLeft: windowWidth <= 1823 &&
                                    windowWidth >= 1820
                                    ? 14 : 0,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="0"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(deposit)
                                    setDeposit('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (deposit == '') {
                                      setDeposit(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setDeposit(parseValidPriceText(text));
                                  }}
                                  value={deposit}
                                  keyboardType={'decimal-pad'}
                                  disabled={!depositSwitch}
                                />
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          {/* <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -4,
                            }}>
                            {/* left *
                            <View style={{ justifyContent: 'center', flex: 0.91 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Price{' '}
                                </Text>
                              </View>
                            </View>
                            <View
                              style={{
                                marginRight: switchMerchant ? 5 : 5,
                                justifyContent: 'center',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                  color: 'black',
                                  opacity: 1,
                                  justifyContent: 'center',
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  marginLeft: windowWidth <= 1823 &&
                                    windowWidth >= 1820
                                    ? 10 : 0,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="0"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(price)
                                    setPrice('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (price == '') {
                                      setPrice(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setPrice(parseValidPriceText(text));
                                  }}
                                  value={price}
                                  keyboardType={'decimal-pad'}
                                />
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Allow Reservation{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="3"
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(daysBefore)
                                  //   setDaysBefore('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (daysBefore == '') {
                                  //     setDaysBefore(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setDaysBefore(text);
                                  }}
                                  value={daysBefore}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Days Before
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }} />
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Max Pax{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="20"
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(maxPax)
                                  //   setMaxPax('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (maxPax == '') {
                                  //     setMaxPax(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setMaxPax(text);
                                  }}
                                  value={maxPax}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Pax
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }} />
                          </View>

                          {/* Cut Off Time */}
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -2,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Cut Off Time{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <RNPickerSelect
                                  useNativeAndroidPickerStyle={false}
                                  arrowSize={20}
                                  containerStyle={{
                                    height: 40,
                                    width: '60%',
                                    borderRadius: 15,
                                    zIndex: 1000,
                                  }}
                                  style={{
                                    placeholder: { color: 'black', backgroundColor: Colors.fieldtBgColor, },
                                    inputIOS: {
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      borderWidth: 1,
                                      backgroundColor: Colors.whiteColor,
                                      color: 'black',
                                      borderRadius: 5,
                                      height: switchMerchant ? 35 : 40,
                                      paddingLeft: 10,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                    },
                                    inputAndroid: {
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      borderWidth: 1,
                                      backgroundColor: Colors.whiteColor,
                                      color: 'black',
                                      borderRadius: 5,
                                      height: switchMerchant ? 35 : 40,
                                      paddingLeft: 10,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                    }, iconContainer: {
                                      top: 2,
                                      bottom: 2,
                                      left: '90%',
                                      justifyContent: 'center',
                                      borderWidth: 0,
                                    },

                                    chevronContainer: {
                                      display: 'none',
                                    },
                                    chevronDown: {
                                      display: 'none',
                                    },
                                    chevronUp: {
                                      display: 'none',
                                    },
                                  }}
                                  Icon={() => {
                                    return (
                                      <>
                                        <Entypo
                                          name="triangle-down"
                                          size={16}
                                        />
                                      </>
                                    );
                                  }}
                                  //placeholderStyle={{ color: 'black', height: 40, }}
                                  dropDownStyle={{
                                    zIndex: 1000,
                                    height: windowHeight * 0.22,
                                  }}
                                  items={cutOffTime}
                                  defaultValue={'10:00 PM'}
                                  onValueChange={(item) => {
                                    setSelectedCutOffTime(item);
                                  }}
                                  placeholder={{
                                    //label: '11:30 PM',
                                    //value: '11:30 PM',
                                    label: selectedCutOffTime,
                                    value: selectedCutOffTime,
                                  }}
                                  /*placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}*/
                                  value={selectedCutOffTime}
                                //onChangeItem={(item) => setSelectedCutOffTime(item.value)}

                                //itemStyle={{ justifyContent: 'flex-start', paddingLeft: 5 }}

                                //onChangeItem={(item) => {
                                //setSelectedOutletTableId(item.value);
                                //}}
                                //defaultValue={}
                                />

                                {/*<TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="20"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    //setTemp(maxPax)
                                    //setMaxPax('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    //if (maxPax == '') {
                                      //setMaxPax(temp);
                                    //}
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    //setMaxPax(text);
                                  }}
                                  //value={maxPax}
                                />*/}
                              </View>
                              {/*<View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Pax
                                </Text>
                                </View>*/}
                            </View>
                            <View style={{ flex: 1 }} />
                          </View>


                          {/* Min Spent */}
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Min Spent{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldBgColor}
                                  keyboardType="numeric"
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40, }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="0"
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTempMinSpent(minSpent)
                                    setMinSpent('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (minSpent == '') {
                                      setMinSpent(tempMinSpent);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setMinSpent(text);
                                  }}
                                  value={minSpent}
                                />
                              </View>
                              {/*<View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Pax
                                </Text>
                                </View>*/}
                            </View>
                            <View style={{ flex: 1 }} />
                          </View>

                          {/* Print KD/OS Before */}
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Print KD/OS Before{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="3"
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(printKdOsBeforeMinutes)
                                  //   setPrintKdOsBeforeMinutes('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (printKdOsBeforeMinutes == '') {
                                  //     setPrintKdOsBeforeMinutes(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setPrintKdOsBeforeMinutes(text);
                                  }}
                                  value={printKdOsBeforeMinutes}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center',
                                  }}>
                                  Minutes
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }} />
                          </View>

                          {/* Reservation Link */}
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 0.5 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Reservation Link{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TouchableOpacity onPress={handleLinkPress}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                      color: 'blue',
                                      opacity: 1,
                                      justifyContent: 'center',
                                      textDecorationLine: 'underline',
                                    }}>
                                    {reservationLink}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                              <View style={{ flex: 1, }} />
                            </View>

                          </View>

                          {/* <View
                            style={{
                              flexDirection: 'row',
                              flex: 2,
                              marginTop: 30,
                              zIndex: -3,
                            }}>
                            {/* left *
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text
                                  style={[
                                    styles.textSize,
                                    { fontSize: switchMerchant ? 10 : 16 },
                                  ]}>
                                  Terms & Conditions{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.24
                                    : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { backgroundColor: Colors.fieldtBgColor },
                                    { fontSize: switchMerchant ? 10 : 16 },
                                    {
                                      width: switchMerchant
                                        ? windowWidth * 0.24
                                        : windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{ color: 'black', height: 40 }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholder="no T&C"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(termCondition)
                                    setTermCondition('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (termCondition == '') {
                                      setTermCondition(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setTermCondition(text);
                                  }}
                                  value={termCondition}
                                />
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 30, zIndex: -2 }}>
                      <View style={{ justifyContent: 'center', flex: 1 }}>
                        <Text style={styles.textSize}>Delivery Fee: </Text>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 2 }}>
                          <CheckBox
                            style={{
                              paddingVertical: 10,
                            }}
                            onClick={() => {
                              // setState({
                              //   isChecked7: !isChecked7,
                              // });
                              // setIsChecked7(isChecked7);
                              setFreeDeliveyAboveAmountFlag(!freeDeliveyAboveAmountFlag);
                            }}
                            checkBoxColor={Colors.fieldtBgColor}
                            uncheckedCheckBoxColor={Colors.tabGrey}
                            checkedCheckBoxColor={Colors.primaryColor}
                            isChecked={freeDeliveyAboveAmountFlag}
                            rightText={'Free Delivery Above RM:'}
                            rightTextStyle={{
                              fontSize: 13,
                              fontFamily: 'NunitoSans-SemiBold',
                              color: Colors.descriptionColor,
                            }}
                          />
                        </View>
                        <View style={{ flex: 1, height: 35, alignSelf: 'center' }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center' }]}
                            placeholder="RM"
                            onChangeText={(text) => {
                              // setState({ amount: text });
                              // setAmount(text);
                              setFreeDeliveyAboveAmountValue(text);
                            }}
                            value={freeDeliveyAboveAmountValue}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}
                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 10, alignItems: 'center', zIndex: -2 }}>
                      <View style={{ flex: 1 }}>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{}}>
                          <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                            Discount (RM):
                          </Text>
                        </View>
                        <View style={{ height: 35, paddingHorizontal: 5 }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center' }]}
                            placeholder="Amount"
                            onChangeText={(text) => {
                              // setState({ discount: text });
                              // setDiscount(text);
                              setDiscountOrderAboveAmountValue(text);
                            }}
                            value={discountOrderAboveAmountValue}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>

                        <View style={{ marginLeft: 15, }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 13,
                              fontFamily: 'NunitoSans-Regular'
                            }}>
                            For Order Above (RM):
                          </Text>
                        </View>

                        <View style={{ height: 35, paddingHorizontal: 5 }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center', }]}
                            placeholder="Amount"
                            onChangeText={(text) => {
                              // setState({ amount1: text });
                              // setAmount1(text);
                              setDiscountOrderAboveAmountThreshold(text);
                            }}
                            value={discountOrderAboveAmountThreshold}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 10 }}>
                      <View
                        style={{ justifyContent: 'center', flex: 1 }}></View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 2 }}>
                          <TouchableOpacity style={styles.addNewView}>
                            <View style={styles.addButtonView1}>
                              <View style={{ marginLeft: '15%' }}>
                                <AntDesign
                                  name="pluscircle"
                                  size={20}
                                  color={Colors.primaryColor}
                                />
                              </View>
                              <Text
                                style={{
                                  marginLeft: 10,
                                  color: Colors.primaryColor,
                                }}>
                                Add Other
                                </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 30 }}>
                      <View style={{ justifyContent: 'center', flex: 1 }}>
                        <Text style={styles.textSize}>Delivery Hours: </Text>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 3 }}>
                          <View>
                            <View style={{ marginLeft: 25 }}>
                              <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                                Session 1
                              </Text>
                            </View>
                            <View style={styles.textInput9}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholder="FROM"
                                onChangeText={(text) => {
                                  // setState({ hourStart: text });
                                  setHourStart(text);
                                }}
                                value={hourStart}
                                ref={myTextInput}
                              />

                            </View>
                          </View>
                        </View>
                        <View style={{ flex: 1, justifyContent: 'center', marginTop: 5 }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 15,
                            }}>
                            TO
                            </Text>
                        </View>
                        <View style={{ flex: 2 }}>
                          <View>
                            <View style={{ marginLeft: 25 }}>
                              <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                                Session 2
                              </Text>
                            </View>
                            <View style={styles.textInput9}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholder="END"
                                onChangeText={(text) => {
                                  // setState({ hourEnd: text });
                                  setHourEnd(text);
                                }}
                                value={hourEnd}
                                ref={myTextInput}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View style={{ flex: 1, flexDirection: 'row', alignSelf: 'center' }}>

                      <TouchableOpacity style={styles.addNewView}>
                        <AntDesign
                          name="pluscircle"
                          size={20}
                          color={Colors.primaryColor}
                        />
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            paddingHorizontal: 5
                          }}>
                          Add Sessions
                                </Text>
                      </TouchableOpacity>

                    </View> */}
                        </View>
                      </View>
                    </View>
                    <View
                      style={
                        {
                          // marginBottom: 15,
                          // borderBottomWidth: StyleSheet.hairlineWidth,
                        }
                      }>
                      {/* <View style={{ flexDirection: 'row', padding: 25 }}> */}
                      {/* <View style={{ flex: 3 }}> */}
                      {/* <View> */}
                      {/* <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                        }}>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={styles.textSize}>
                            Delivery Price:{'    '}
                          </Text>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                          <View style={{ flex: 12 }}>
                            <CheckBox
                              style={{
                                padding: 10,
                              }}
                              onClick={() => {
                                // setState({
                                //   isChecked8: !isChecked8,
                                // });
                                setIsChecked8(!isChecked8);
                              }}
                              checkBoxColor={Colors.fieldtBgColor}
                              uncheckedCheckBoxColor={Colors.tabGrey}
                              checkedCheckBoxColor={Colors.primaryColor}
                              isChecked={isChecked8}
                              rightText={'Same as Dine-in Price'}
                              rightTextStyle={{
                                fontSize: 15,
                                fontWeight: 'bold',
                                color: Colors.descriptionColor,
                              }}
                            />
                          </View>
                          <View style={{ flex: 1 }}>
                            <NumericInput
                              value={deliveryPrice}
                              onChange={(value) =>
                                // setState({ value1: value })
                                // setValue1(value)
                                setDeliveryPrice(value),
                                // console.log('pressed')
                              }
                              minValue={0}
                              maxValue={50}
                              onLimitReached={(isMax, msg) =>
                                console.log(isMax, msg)
                              }
                              totalWidth={180}
                              totalHeight={40}
                              iconSize={25}
                              step={1}
                              valueType="real"
                              rounded
                              textColor={Colors.primaryColor}
                              iconStyle={{ color: 'white' }}
                              // rightButtonBackgroundColor={'red'}
                              rightButtonBackgroundColor={Colors.primaryColor}
                              leftButtonBackgroundColor={'grey'}
                            />
                          </View>
                        </View>
                         <View style={{ flex: 1 }}></View>
                      </View> */}

                      {/* <View
                        style={{
                          flexDirection: 'row',
                          //flex: 1,
                          //marginTop: 15,
                          justifyContent: 'flex-start',
                          marginLeft: 5
                        }}> */}
                      {/* <View style={{ justifyContent: 'center' }}>
                          <Text style={styles.textSize}>
                            Takeaway Charges:{' '}
                          </Text>
                        </View> */}
                      {/* <View style={{ flexDirection: 'row' }}> */}
                      {/* <View style={{ flex: 12 }}>
                            <CheckBox
                              style={{
                                padding: 10,
                              }}
                              onClick={() => {
                                // setState({
                                //   isChecked9: !isChecked9,
                                // });
                                setIsChecked9(!isChecked9);
                              }}
                              checkBoxColor={Colors.fieldtBgColor}
                              uncheckedCheckBoxColor={Colors.tabGrey}
                              checkedCheckBoxColor={Colors.primaryColor}
                              isChecked={isChecked9}
                              rightText={'Same as Dine-in Price'}
                              rightTextStyle={{
                                fontSize: 15,
                                fontWeight: 'bold',
                                color: Colors.descriptionColor,
                              }}
                            />
                          </View> */}
                      {/* <View style={{ flex: 1 }}> */}
                      {/* <TextInput
                              underlineColorAndroid={Colors.fieldtBgColor}
                              style={styles.textInputTakeawayCharges}
                              placeholderStyle={{ color: 'black' }}
                              itemStyle={{ justifyContent: 'flex-start', marginLeft: 10 }}
                              placeholder="0"
                              onChangeText={(text) => {

                              }}
                            /> */}
                      {/* <NumericInput
                              value={pickUpPrice}
                              onChange={(value) =>
                                // setState({ value2: value })
                                // setValue2(value)
                                setPickUpPrice(value)
                              }
                              minValue={0}
                              onLimitReached={(isMax, msg) =>
                                console.log(isMax, msg)
                              }
                              totalWidth={180}
                              totalHeight={40}
                              iconSize={25}
                              step={1}
                              valueType="real"
                              rounded
                              textColor={Colors.primaryColor}
                              iconStyle={{ color: 'white' }}
                              rightButtonBackgroundColor={Colors.primaryColor}
                              leftButtonBackgroundColor={'grey'}
                            /> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* <View style={{ flex: 1 }}></View> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* right */}
                      {/* <View style={{ flex: 2 }}></View>
                </View> */}
                    </View>

                    {/* <View
                style={{
                  marginBottom: 15,
                }}>
                <View style={{ flexDirection: 'row', padding: 30 }}>
                  <View style={{ flex: 3 }}>
                    <View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1 }}>
                          <Text style={styles.textSize}>Fire Order: </Text>
                        </View>
                        <View style={{ flex: 1, flexDirection: 'row' }}>
                          <Switch
                            style={{
                              //flexDirection: 'row',
                              width: 50,
                              marginRight: 20,
                              transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                            }}
                            value={status1}
                            onSyncPress={(value) =>
                              // setState({ status1: status1 })
                              setStatus1(value)
                            }
                            circleColorActive={Colors.primaryColor}
                            circleColorInactive={Colors.primaryColor}
                            backgroundActive={Colors.lightPrimary}
                          />
                          <Text
                            style={{
                              fontSize: 20,
                              color: Colors.primaryColor,
                            }}>
                            {status1 ? 'Yes' : 'No'}
                          </Text>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          marginTop: 5,
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1 }}>
                          <Text style={styles.textSize}>Categories: </Text>
                        </View>
                        <View style={{ flex: 1, flexDirection: 'row' }}>
                          <View style={{ flex: 2 }}>
                            <View
                              style={{
                                width: 200,
                                height: 50,
                                justifyContent: 'center',
                                marginLeft: '5%',
                              }}>
                              <DropDownPicker
                                items={categoryOutlet.map((item) => ({
                                  label: item.categoryName, //<= after hayden change you need to change it to item.name
                                  value: item.categoryId,
                                }))}
                                containerStyle={{ height: 40 }}
                                placeholder="Choose Categories"
                                placeholderStyle={{ color: 'black' }}
                                style={{ backgroundColor: '#fafafa' }}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row-reverse'
                                }}
                                defaultValue={'24'}
                                multiple={true}
                                customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                dropDownStyle={{ backgroundColor: '#fafafa' }}
                                onChangeItem={(item) => {
                                  // setState({
                                  //   category: item.value
                                  // });
                                  setCategory(item.value);
                                }}
                              />
                            </View>
                          </View>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                    </View>
                  </View>
                  <View style={{ flex: 2 }}></View>
                </View>
              </View> */}
                    <View style={{ alignItems: 'center', zIndex: -5 }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: 35,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={loading}
                        onPress={() => {
                          orderFunc();
                          reservationConfigFunc();
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {loading ? 'LOADING...' : 'SAVE'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginTop: 20 }} />
                  </ScrollView>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default SettingReservationScreen;
