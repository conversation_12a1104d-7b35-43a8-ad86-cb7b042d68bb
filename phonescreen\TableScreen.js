import React, { Component, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal,
  FlatList,
  TouchableOpacityBase,
  Button,
  ActivityIndicator,
  Dimensions,
  Platform
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AIcon from 'react-native-vector-icons/AntDesign';
import EIcon from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import { ClipPath } from 'react-native-svg';
import Plus from '../assets/svg/Plus.svg';
import QRCode from 'react-native-qrcode-svg';
import moment from 'moment';
// import { back } from 'react-native/Libraries/Animated/src/Easing';
import OrderModal from './components/OrderModal';
import * as Cart from '../util/Cart';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import {
  DELAY_LONG_PRESS_TIME,
  ORDER_TYPE,
  USER_ORDER_STATUS,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import RNPickerSelect from 'react-native-picker-select';
import { naturalCompare } from '../util/common';
import { qrUrl } from '../constant/env';

const TableScreen = (props) => {
  const { navigation } = props;

  const [sectionArea, setSectionArea] = useState([]);
  const [currentSectionArea, setCurrentSectionArea] = useState(null);
  const [addSectionAreaModel, setAddSectionAreaModel] = useState(false);
  const [addTableModal, setAddTableModal] = useState(false);
  const [deleteTableModal, setDeleteTableModal] = useState(false);
  const [preventDeleteTableModal, setPreventDeleteTableModal] = useState(false);
  const [seatingModal, setSeatingModal] = useState(false);
  const [selectedSlotId, setSelectedSlotId] = useState(null);
  const [displayQrModal, setDisplayQrModal] = useState(false);
  const [viewTableOrderModal, setViewTableOrderModal] = useState(false);
  const [orderDisplayIndividual, setOrderDisplayIndividual] = useState(false);
  const [updateTableModal, setUpdateTableModal] = useState(false);
  const [joinTableModal, setJoinTableModal] = useState(false);
  const [availableSeats, setAvailableSeats] = useState([]);
  const [seatingPax, setSeatingPax] = useState(0);
  const [totalTables, setTotalTables] = useState(0);
  const [changeTable, setChangeTable] = useState(false);
  const [tableCapacity, setTableCapacity] = useState('');
  const [tableCode, setTableCode] = useState('');
  const [tableList, setTableList] = useState('');
  const [changeTableId, setChangeTableId] = useState({});
  const [joinTableId, setJoinTableId] = useState('');
  // const [onRemove, setOnRemove] = useState('');
  const [allTable, setAllTable] = useState([]);

  const [filtered, setFiltered] = useState([]);

  const [selectedTableId, setSelectedTableId] = useState('');
  const [selectedTableCode, setSelectedTableCode] = useState('');
  const [selectedCapacity, setSelectedCapacity] = useState('');
  const [selectedTableOrder, setSelectedTableOrder] = useState('');
  const [selectedTableCustomers, setSelectedTableCustomers] = useState([]);
  const [selectedTableSeats, setSelectedTableSeats] = useState('');

  const [inputSectionName, setInputSectionName] = useState('');

  const [inputTableCode, setInputTableCode] = useState('');
  const [inputTablePax, setInputTablePax] = useState(0);

  const [filteredOutletTables, setFilteredOutletTables] = useState([]);
  const [
    filteredOutletTablesForRendered,
    setFilteredOutletTablesForRendered,
  ] = useState([]);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const [joinTableItems, setJoinTableItems] = useState([]);

  const [showGenericQRCode, setShowGenericQRCode] = useState(false);

  const [splitAmount, setSplitAmount] = useState(0);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const outletSections = OutletStore.useState((s) => s.outletSections);
  const outletSectionsDict = OutletStore.useState((s) => s.outletSectionsDict);

  const outletTables = OutletStore.useState((s) => s.outletTables);
  const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);
  const outletTablesCodeDict = OutletStore.useState(
    (s) => s.outletTablesCodeDict,
  );

  const userOrders = OutletStore.useState((s) => s.userOrders);
  const userOrdersDict = OutletStore.useState((s) => s.userOrdersDict);
  const userOrdersTableDict = OutletStore.useState(
    (s) => s.userOrdersTableDict,
  );

  const selectedOutletSection = CommonStore.useState(
    (s) => s.selectedOutletSection,
  );

  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable,
  );
  const userCart = CommonStore.useState((s) => s.userCart);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const name = UserStore.useState((s) => s.name);

  useEffect(() => {
    setTimeout(() => {
      setRefreshRate(new Date());
    }, 30000);
  }, [refreshRate]);

  useEffect(() => {
    const joinTableItemsTemp = filteredOutletTables
      .filter((table) => {
        if (
          userOrdersTableDict[table.uniqueId] === undefined &&
          table.uniqueId !== selectedOutletTable.uniqueId
          // !table.joinedTables
        ) {
          if (
            selectedOutletTable.joinedTables &&
            selectedOutletTable.joinedTables.length === 3
          ) {
            if (table.joinedTables && table.joinedTables.length >= 2) {
              return false;
            } else {
              return true;
            }
          } else if (
            selectedOutletTable.joinedTables &&
            selectedOutletTable.joinedTables.length === 2
          ) {
            if (table.joinedTables && table.joinedTables.length >= 3) {
              return false;
            } else {
              return true;
            }
          } else if (!selectedOutletTable.joinedTables) {
            if (table.joinedTables && table.joinedTables.length >= 4) {
              return false;
            } else {
              return true;
            }
          } else {
            return true;
          }
        }
      })
      .map((table) => {
        return { label: table.code, value: table.uniqueId };
      });

    setJoinTableItems(joinTableItemsTemp);

    if (
      // joinTableId === '' &&
      joinTableItemsTemp.length > 0
    ) {
      setJoinTableId(joinTableItemsTemp[0].value);
    }
  }, [filteredOutletTables, userOrdersTableDict, selectedOutletTable]);

  useEffect(() => {
    if (joinTableItems.length > 0) {
      const joinTableItem = filteredOutletTables.find(
        (item) => item.uniqueId === joinTableId,
      );

      if (joinTableItem && selectedOutletTable) {
        var pendingCodes = [];

        if (joinTableItem.joinedTables) {
          for (var i = 0; i < joinTableItem.joinedTables.length; i++) {
            pendingCodes.push(joinTableItem.joinedTables[i].code);
          }
        } else {
          pendingCodes.push(joinTableItem.code);
        }

        if (selectedOutletTable.joinedTables) {
          for (var i = 0; i < selectedOutletTable.joinedTables.length; i++) {
            pendingCodes.push(selectedOutletTable.joinedTables[i].code);
          }
        } else {
          pendingCodes.push(selectedOutletTable.code);
        }

        pendingCodes.sort((a, b) => {
          // return a.localeCompare(b, 'en', {
          //     numeric: true,
          //     sensitivity: 'base',
          // });

          return naturalCompare(a, b);
        });

        setInputTableCode(pendingCodes.join(''));
      }
    }
  }, [joinTableId, selectedOutletTable]);

  useEffect(() => {
    if (
      outletSections.length > 0 &&
      selectedOutletSection.uniqueId === undefined
    ) {
      CommonStore.update((s) => {
        s.selectedOutletSection = outletSections[0];
      });
    }
  }, [outletSections.length]);

  useEffect(() => {
    if (selectedOutletSection.uniqueId) {
      var filteredOutletTablesTemp = outletTables.filter(
        (table) => table.outletSectionId === selectedOutletSection.uniqueId,
      );

      filteredOutletTablesTemp.sort((a, b) => {
        if (a.joinedTables && b.joinedTables) {
          return b.joinedTables.length - a.joinedTables.length;
        } else if (a.joinedTables && !b.joinedTables) {
          return -1;
        } else if (!a.joinedTables && b.joinedTables) {
          return 1;
        } else if (!a.joinedTables && !b.joinedTables) {
          return 0;
        }
      });

      const MAX_COL_NUM = 4;

      var filteredOutletTablesTempFirstPass = [];

      if (filteredOutletTablesTemp.length > 0) {
        filteredOutletTablesTempFirstPass = [filteredOutletTablesTemp[0]];

        if (filteredOutletTablesTemp.length > 1) {
          var colRemaining = MAX_COL_NUM;
          if (filteredOutletTablesTemp[0].joinedTables) {
            colRemaining -= filteredOutletTablesTemp[0].joinedTables.length;
          } else {
            colRemaining -= 1;
          }

          for (var i = 1; i < filteredOutletTablesTemp.length; i++) {
            const currCol = i % MAX_COL_NUM;

            const currTable = filteredOutletTablesTemp[i];

            console.log(currTable);

            if (colRemaining <= 0) {
              // start with new row

              colRemaining = MAX_COL_NUM;

              filteredOutletTablesTempFirstPass.push(currTable);
            } else if (colRemaining > 0) {
              if (!currTable.joinedTables) {
                // squeeze unjoined table

                filteredOutletTablesTempFirstPass.push(currTable);
              } else if (
                currTable.joinedTables &&
                currTable.joinedTables.length <= colRemaining
              ) {
                // squeeze remaining tables

                filteredOutletTablesTempFirstPass.push(currTable);
              } else {
                for (var j = 0; j < colRemaining; j++) {
                  filteredOutletTablesTempFirstPass.push({
                    emptyLayout: true,
                  });
                }

                colRemaining = MAX_COL_NUM;

                filteredOutletTablesTempFirstPass.push(currTable);
              }
            }

            if (currTable.joinedTables) {
              colRemaining -= currTable.joinedTables.length;
            } else {
              colRemaining -= 1;
            }
          }
        }
      }

      setFilteredOutletTables(filteredOutletTablesTemp);
      setFilteredOutletTablesForRendered(filteredOutletTablesTempFirstPass);

      // setFilteredOutletTables(filteredOutletTablesTemp.filter(a => a.joinedTables === undefined));
    }

    if (selectedOutletTable && selectedOutletTable.uniqueId) {
      CommonStore.update((s) => {
        s.selectedOutletTable =
          outletTables.find(
            (table) => table.uniqueId === selectedOutletTable.uniqueId,
          ) || {};
      });
    }
  }, [selectedOutletSection, outletTables]);

  useEffect(() => {
    if (userCart && userCart.userId) {
      if (moment().diff(moment(userCart.updatedAt), 'minute') >= 1) {
        deleteUserCart(userCart.uniqueId);
      } else {
        setDisplayQrModal(false);
        setViewTableOrderModal(false);
        navigation.navigate('OutletMenu', {
          params: {
            outletData: {},
            orderType: 0,
            test: {},
            navFrom: 'DINEIN',
          },
        });
      }
    }
  }, [userCart]);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
        }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: Platform.OS == 'android' ? 9 : 10,
            opacity: 0.8,
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              //marginTop: -3,
              marginBottom: Platform.OS == 'android' ? 2 : 0,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -2,
        }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          Table
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={() => {
            props.navigation.navigate('Profile');
          }}>
          <Image
            style={{
              width: 32,
              height: 32,
              marginTop: 8,
              marginRight: 25,
            }}
            source={require('../assets/image/drawer.png')}
          />
        </TouchableOpacity>
      </View>
    ),
  });

  // getSectionArea();
  // getAllSectionTable()
  // setInterval(() => {
  //     getSectionArea()
  //     getAllSectionTable()
  // }, 7000)

  const setState = () => { };

  const deleteUserCart = async () => {
    const body = {
      userCartId: userCart.uniqueId,
    };

    ApiClient.POST(API.deleteUserCart, body).then((result) => {
      if (result && result.status === 'success') {
        console.log('ok');
      }
    });
  };

  const completePayment = () => {
    var body = {
      orderId: orderId,
    };
    ApiClient.POST(API.orderDonePayment, body).then((result) => {
      if (result) {
        setState({ refresh: true });
      }
    });
  };

  const calculateColor = (time) => {
    if (time >= 20) {
      return Colors.tabRed;
    } else if (time < 20 && time >= 15) {
      return Colors.tabYellow;
    } else if (time < 15 || !time) {
      return Colors.tabGrey;
    }
  };

  const calculateColorText = (time) => {
    if (time >= 20) {
      return Colors.whiteColor;
    } else if (time < 20 && time >= 15) {
      return Colors.blackColor;
    } else if (time < 15 || !time) {
      return Colors.whiteColor;
    }
  };

  const getSectionArea = () => {
    ApiClient.GET(API.tableSection + User.getOutletId())
      .then((result) => {
        if (currentSectionArea != null) {
          setState({
            sectionArea: result,
          });
          getTableBySection(currentSectionArea);
        } else {
          setState({
            sectionArea: result,
            currentSectionArea: result[0].section,
          });
          getTableBySection(result[0].section);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const splitUserOrderBill = async () => {
    var payableAmount = 0;
    var paidAmount = 0;

    if (
      userOrdersTableDict[selectedOutletTable.uniqueId] &&
      userOrdersTableDict[selectedOutletTable.uniqueId][0]
    ) {
      payableAmount =
        userOrdersTableDict[selectedOutletTable.uniqueId][0].finalPrice;

      if (
        userOrdersTableDict[selectedOutletTable.uniqueId][0].splitAmountList
      ) {
        paidAmount = userOrdersTableDict[
          selectedOutletTable.uniqueId
        ][0].splitAmountList.reduce(
          (accu, splitAmount) => accu + splitAmount.amount,
          0,
        );

        if (payableAmount === paidAmount) {
          Alert.alert('Info', 'This order has been fully paid');
          return;
        }
      }
    }

    if (splitAmount <= 0) {
      Alert.alert('Info', 'Split amount must be greater than 0');
      return;
    }

    var body = {
      orderId: userOrdersTableDict[selectedOutletTable.uniqueId][0].uniqueId,
      splitAmount: splitAmount,
    };

    ApiClient.POST(API.splitUserOrderBill, body)
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Info', 'Bill splitted successfully');
        } else {
          Alert.alert('Failed to split bill');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const deleteOutletTable = async () => {
    var body = {
      outletId: currOutletId,
      tableId: selectedOutletTable.uniqueId,
    };

    ApiClient.POST(API.deleteOutletTable, body)
      .then((result) => {
        if (result && result.status === 'success') {
          setSeatingModal(false);

          Alert.alert('Info', 'Table has been removed');
        } else {
          Alert.alert('Failed to remove table');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const addSectionArea = () => {
    // var body = {
    //     outletId: User.getOutletId(),
    //     section: section
    // }
    // ApiClient.POST(API.postSection, body).then(result => {

    //     if (result.success.length > 0) {
    //         getSectionArea()
    //         setState({ addSectionAreaModel: false })
    //         Alert.alert("Section has been added")
    //     } else {
    //         Alert.alert("Error adding section")
    //     }
    // }).catch(err => { console.log(err) })

    var body = {
      outletId: currOutletId,
      sectionName: inputSectionName,
    };

    ApiClient.POST(API.createOutletSection, body)
      .then((result) => {
        if (result && result.uniqueId) {
          setAddSectionAreaModel(false);
          Alert.alert('Section has been added.');
        } else {
          Alert.alert('Error adding section.');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const tableExchange = () => {
    var body = {
      table1: selectedOutletTable.uniqueId,
      table2: changeTableId,
    };

    console.log(body);

    ApiClient.POST(API.tableExchange, body)
      .then((result) => {
        if (result.status === 'success') {
          // setState({
          //     changeTableId: '',
          //     changeTable: false,
          //     displayQrModal: false,
          //     viewTableOrderModal: false
          // })
          // getTableBySection(currentSectionArea)

          Alert.alert('Success', 'Tables have been switched');

          setChangeTableId('');
          setChangeTable(false);

          setDisplayQrModal(false);
          setViewTableOrderModal(false);
        } else {
          Alert.alert('Error', 'Please make sure there are empty seats available.');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const deleteSectionArea = (sectionName) => {
    var body = {
      outletId: User.getOutletId(),
      section: sectionName,
    };
    ApiClient.POST(API.deleteSectionTable, body)
      .then((result) => {
        if (result.success) {
          getSectionArea();
          Alert.alert('Section has been removed');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const deleteTable = (selectedTableId) => {
    var body = {
      tableId: selectedTableId,
    };
    ApiClient.POST(API.deleteTable + selectedTableId, body, false)
      .then((result) => {
        if (result) {
          getTableBySection(currentSectionArea);
          setState({ deleteTableModal: false });
          Alert.alert('Success', 'Table has been removed');
          console.log('deleteTable', result);

          setState({
            tableCapacity: '',
            tableCode: '',
            selectedSlotId: '',
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getTableBySection = (sectionName) => {
    ApiClient.GET(
      API.sectionTable +
      '?outletId=' +
      User.getOutletId() +
      '&sectionName=' +
      sectionName,
    )
      .then((result) => {
        const tableBySlots = result.sort((a, b) => a.slot - b.slot);
        var i = 0;
        const totalTables = result.filter((table) => table.code != null);
        const seatedTables = result.filter((table) => table.seated != 0);

        var totalOccupiedSeats = 0;
        for (const tables of seatedTables) {
          totalOccupiedSeats = totalOccupiedSeats + tables.seated;
        }
        var totalSeats = 0;
        for (const tables of result) {
          totalSeats = totalSeats + tables.capacity;
        }
        const tableList = tableBySlots
          .filter((item) => item.code !== null)
          .map((table) => {
            return {
              label: table.code,
              value: table.id,
            };
          });

        setState({
          tableSlots: tableBySlots,
          tableList: tableList,
          totalTables: totalTables,
          seatedTables: seatedTables.length,
          totalOccupiedSeats: totalOccupiedSeats,
          totalSeats: totalSeats,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getAllSectionTable = () => {
    ApiClient.GET(
      API.getAllSectionTable + '?outletId=' + User.getOutletId(),
    ).then((result) => {
      const allTableFilter = [];
      for (const section of result) {
        for (const table of section) {
          if (table.capacity > 0) {
            allTableFilter.push({ label: table.code, value: table.id });
          }
        }
      }
      setState({ allTable: allTableFilter });
    });
  };

  const onRemove = (id) => (e) => {
    setorderInfo(orderInfo.filter((orderInfo) => orderInfo.id !== id));
  };

  const addTable = () => {
    if (
      // !selectedSlotId ||
      // !tableCapacity ||
      // !tableCode
      !inputTableCode ||
      !inputTablePax ||
      !selectedOutletSection
    ) {
      Alert.alert('Error', 'Please fill in the information');
      return;
    } else {
      const hasSame = outletTables.some(
        (table) => table.code === inputTableCode,
      );
      if (hasSame) {
        Alert.alert('Error', 'Existing table code found');
        return;
      }

      if (inputTablePax <= 0) {
        Alert.alert('Error', 'Capacity must be more than 0');
        return;
      }

      var body = {
        // tableId: selectedSlotId,
        capacity: inputTablePax,
        code: inputTableCode,
        outletSectionId: selectedOutletSection.uniqueId,
        outletId: currOutletId,
      };

      // ApiClient.POST(API.postTable, body, false).then((result) => {
      //     if (result.success) {
      //         getTableBySection(currentSectionArea)
      //         setState({ addTableModal: false })
      //         console.log("OOOO", result)
      //         console.log("CODE", tableCode)
      //     }
      //     else if (result.findDuplicate)
      //         Alert.alert(
      //             'Error',
      //             result.findDuplicate
      //         )
      // }).catch(err => console.log(err));

      ApiClient.POST(API.createOutletTable, body, false)
        .then((result) => {
          if (result && result.uniqueId) {
            setAddTableModal(false);
          } else {
            Alert.alert('Error', result.message);

            // setAddTableModal(false);
          }
        })
        .catch((err) => console.log(err));
    }
  };

  const updateTable = () => {
    if (
      // !selectedSlotId ||
      // !tableCapacity ||
      // !tableCode
      !inputTablePax ||
      !selectedOutletSection
    ) {
      Alert.alert('Error', 'Please fill in the information');
      return;
    } else {
      if (inputTablePax <= 0) {
        Alert.alert('Error', 'Capacity must be more than 0');
        return;
      } else if (inputTablePax < selectedOutletTable.seated) {
        Alert.alert('Error', 'New capacity must be more than the current seated pax');
        return;
      }

      var body = {
        tableId: selectedOutletTable.uniqueId,
        newCapacity: inputTablePax,
      };

      console.log(body);

      ApiClient.POST(API.updateOutletTable, body, false)
        .then((result) => {
          if (result && result.status === 'success') {
            setUpdateTableModal(false);

            Alert.alert('Success', 'Table has been updated');
          } else {
            Alert.alert('Error', result.message);

            // setAddTableModal(false);
          }
        })
        .catch((err) => console.log(err));
    }
  };

  const joinTable = () => {
    if (
      // !selectedSlotId ||
      // !tableCapacity ||
      // !tableCode
      !inputTableCode ||
      !joinTableId
    ) {
      Alert.alert('Error', 'Please fill in the information');
      return;
    } else {
      const hasSame = outletTables.some(
        (table) => table.code === inputTableCode,
      );
      if (hasSame) {
        Alert.alert('Error', 'Existing table code found');
        return;
      }

      var body = {
        tableCode: inputTableCode,
        joinTableIdList: [joinTableId],
        tableId: selectedOutletTable.uniqueId,

        outletSectionId: selectedOutletSection.uniqueId,
        outletId: currOutletId,
      };

      console.log(body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      ApiClient.POST(API.joinOutletTable, body, false)
        .then((result) => {
          if (result && result.status === 'success') {
            setJoinTableModal(false);
            setSeatingModal(false);

            Alert.alert('Success', 'Tables have been joined');
          } else {
            Alert.alert('Error', result.message);
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        })
        .catch((err) => console.log(err));
    }
  };

  const splitTable = () => {
    var body = {
      tableCode: inputTableCode,
      // joinTableIdList: [joinTableId],
      tableId: selectedOutletTable.uniqueId,

      outletSectionId: selectedOutletSection.uniqueId,
      outletId: currOutletId,
    };

    console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    ApiClient.POST(API.splitOutletTable, body, false)
      .then((result) => {
        if (result && result.status === 'success') {
          // setJoinTableModal(false);
          setSeatingModal(false);

          Alert.alert('Success', 'Table has been splitted');
        } else {
          Alert.alert('Error', result.message);
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      })
      .catch((err) => console.log(err));
  };

  const occupyingSeats = (seatedPax) => {
    if (!selectedOutletTable || !seatingPax) {
      Alert.alert('Error', 'Please fill in the information');
      return;
    } else {
      var body = {
        tableId: selectedOutletTable.uniqueId,
        pax: seatedPax,
        outletId: currOutletId,
      };

      ApiClient.POST(API.addCustomer, body, false).then((result) => {
        // if (result.outletId == User.getOutletId()) {
        //     getTableBySection(currentSectionArea)
        //     setState({ seatingModal: false })
        //     Alert.alert("Table seated!")
        // }

        if (result && result.status === 'success') {
          setSeatingModal(false);
          Alert.alert('Success', 'Table has been seated');
        }
      });
    }
  };

  const getQrCode = (selectedTableId) => {
    var body = {
      tableId: selectedTableId,
    };
    ApiClient.POST(API.qrTableGenerate, body, false)
      .then((result) => {
        setState({ qrData: result });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getOrderByTable = (tableId) => {
    ApiClient.GET(API.tableOrder + tableId)
      .then((result) => {
        setState({
          orderInfo: result,
          orderId: result.id,
          tableOrder: result.orderItems,
          total: result.finalPrice,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const addSectionButton = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          setState({ addSectionAreaModel: true });
        }}>
        <View
          style={[
            styles.sectionAreaButton,
            {
              width: Dimensions.get('screen').height * 0.04,
              height: Dimensions.get('screen').height * 0.037,
              marginRight: 6,
            },
          ]}>
          <Text style={styles.sectionAreaButtonTxt}> + </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const checkOrderItem = (id) => {
    ApiClient.POST(API.orderDeliver + id)
      .then((result) => {
        if (result === true) getTableWithDetails();
      })
      .catch((err) => Alert('Error', 'Something went wrong'));
  };

  const renderSection = ({ item }) => {
    return (
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
          onPress={() => {
            // setState({ currentSectionArea: item.section
            // });
            // getTableBySection(item.section)

            CommonStore.update((s) => {
              s.selectedOutletSection = item;

              s.timestamp = Date.now();
            });
          }}>
          <View
            style={[
              styles.sectionAreaButton,
              item.uniqueId == selectedOutletSection.uniqueId
                ? { backgroundColor: Colors.primaryColor }
                : null,
            ]}>
            <Text
              style={[
                styles.sectionAreaButtonTxt,
                item.uniqueId == selectedOutletSection.uniqueId
                  ? { color: Colors.whiteColor }
                  : null,
              ]}>
              {item.sectionName}
            </Text>

            <TouchableOpacity
              style={{ position: 'absolute', right: 5 }}
              onPress={() => {
                // deleteSectionArea(item)

                // disabled first, need confirm about this behaviour

                Alert.alert('Error', 'Invalid privileges.');
              }}
            // onValueChange={() => { checkOrderItem(item.id) }}
            >
              <EIcon
                style={{
                  marginRight: 5,
                }}
                name={'cross'}
                size={18}
                color={
                  item.uniqueId == selectedOutletSection.uniqueId
                    ? '#FFFFFF'
                    : '#4E9F7D'
                }
              />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderTableLayout = ({ item, index }) => {
    // console.log('renderTableLayout');
    // console.log(item);
    // console.log(item.code != null && item.capacity != null && item.seated == 0);
    // console.log(item.seated > 0 &&
    //     // filtered.length == 0) { //seated but no orders
    //     userOrdersTableDict[item.uniqueId] === undefined);
    // console.log(item.seated > 0 &&
    //     // filtered.length > 0) { //seated with orders
    //     userOrdersTableDict[item.uniqueId] !== undefined);

    var orderEstimatedTime = 0;
    var orderStatus = '';
    var orderDate = '';

    if (
      userOrdersTableDict[item.uniqueId] &&
      userOrdersTableDict[item.uniqueId][0]
    ) {
      orderEstimatedTime =
        (moment().valueOf() -
          userOrdersTableDict[item.uniqueId][0].estimatedPreparedDate) /
        (1000 * 60);
      var hrs = Math.floor(orderEstimatedTime / 60);
      var mins = Math.floor(orderEstimatedTime % 60);

      orderStatus = userOrdersTableDict[item.uniqueId][0].orderStatus;
      orderDate = userOrdersTableDict[item.uniqueId][0].orderDate;
    }

    console.log(orderStatus);

    // const filtered = item.orders.filter(i => i.status == 1 || i.status == 2)
    if (item.actionButton === true) {
      //no table
      return (
        // <TouchableOpacity
        //     style={styles.tableSlotDisplay}
        //     onPress={() => { setState({ addTableModal: true, selectedSlotId: item.id }) }}>
        //     <Plus height={Dimensions.get('screen').width * 0.06} width={Dimensions.get('screen').width * 0.06} />
        // </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tableSlotDisplay,
            {
              // flex: 0.5,
            },
          ]}
          onPress={() => {
            // setState({ addTableModal: true, selectedSlotId: item.id })
            setAddTableModal(true);
          }}>
          <Plus height={Dimensions.get('screen').width * 0.06} width={Dimensions.get('screen').width * 0.06} />
        </TouchableOpacity>
      );
    } else if (item.emptyLayout === true) {
      // empty layout

      return (
        // <TouchableOpacity
        //     style={styles.tableSlotDisplay}
        //     onPress={() => { setState({ addTableModal: true, selectedSlotId: item.id }) }}>
        //     <Plus height={Dimensions.get('screen').width * 0.06} width={Dimensions.get('screen').width * 0.06} />
        // </TouchableOpacity>

        <View
          style={[
            styles.tableSlotDisplay,
            {
              // flex: 0.5,
              opacity: 0,
            },
          ]}>
          <Plus height={Dimensions.get('screen').width * 0.06} width={Dimensions.get('screen').width * 0.06} />
        </View>
      );
    } else if (item.code != null && item.capacity != null && item.seated == 0) {
      // not seated tables
      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              // ...((item.joinedTables) && {
              //     flex: 1,
              // }),
              // ...((item.joinedTables) && {
              //     width: Dimensions.get('screen').width * 0.215 * (item.joinedTables.length),
              // }),
              ...(item.joinedTables && {
                width:
                  item.joinedTables.length == 2
                    ? Dimensions.get('screen').width * 0.215 * item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? Dimensions.get('screen').width * 0.221 * item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? Dimensions.get('screen').width * 0.223 * item.joinedTables.length
                        : Dimensions.get('screen').width * 0.215,
              }),
            },
          ]}
          onPress={() => {
            // setState({
            //     selectedTableId: item.id,
            //     selectedTableCode: item.code,
            //     selectedCapacity: item.capacity,
            //     seatingModal: true
            // });

            console.log('pressed');

            CommonStore.update((s) => {
              s.selectedOutletTable = item;
            });

            // setSeatingPax(0);
            setSeatingModal(true);
          }}
          onLongPress={() => {
            console.log('long pressed');

            setState({
              deleteTableModal: true,
              selectedTableCode: item.code,
              selectedTableId: item.id,
            });
          }}
          delayLongPress={DELAY_LONG_PRESS_TIME}>
          <View
            style={[
              styles.emptyTableDisplay,
              {
                // paddingHorizontal: 6,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                // ...((item.joinedTables) && {
                //     width: Dimensions.get('screen').width * 0.215 * (item.joinedTables.length),
                // }),
                ...(item.joinedTables && {
                  width:
                    item.joinedTables.length == 2
                      ? Dimensions.get('screen').width * 0.215 * item.joinedTables.length
                      : item.joinedTables.length == 3
                        ? Dimensions.get('screen').width * 0.221 * item.joinedTables.length
                        : item.joinedTables.length == 4
                          ? Dimensions.get('screen').width * 0.223 * item.joinedTables.length
                          : Dimensions.get('screen').width * 0.215,
                }),
              },
              // changeTable ? {
              //     height: Dimensions.get('screen').width * 0.23,
              //     width: Dimensions.get('screen').width * 0.23,
              //     position: 'absolute',
              //     elevation: 50,
              //     borderWidth: 3,
              //     borderColor: Colors.primaryColor,
              //     zIndex: 10
              // } : null
            ]}>
            <View style={{ flex: 1, justifyContent: 'space-between' }}>
              <View
                style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text>-</Text>
                <Text style={styles.tableCode}>{item.code}</Text>
              </View>
              <View style={{ alignItems: 'center' }}>
                <Text style={[styles.tableCode, { fontSize: 9 }]}>
                  Seats: {item.seated}/{item.capacity}
                </Text>
              </View>
              <View style={{ alignItems: 'flex-end' }}>
                <Text>-</Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else if (
      item.seated > 0 &&
      // filtered.length == 0) { //seated but no orders
      userOrdersTableDict[item.uniqueId] === undefined
    ) {
      var hours = Math.floor(moment().diff(item.updatedAt, 'minute') / 60);
      var minutes = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              backgroundColor: Colors.tabGrey,

              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,

              // ...((item.joinedTables) && {
              //     width: Dimensions.get('screen').width * 0.215 * (item.joinedTables.length),
              // }),
              ...(item.joinedTables && {
                width:
                  item.joinedTables.length == 2
                    ? Dimensions.get('screen').width * 0.215 * item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? Dimensions.get('screen').width * 0.220 * item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? Dimensions.get('screen').width * 0.223 * item.joinedTables.length
                        : Dimensions.get('screen').width * 0.215,
              }),
            },
          ]}
          onPress={() => {
            // setState({
            //     selectedTableId: item.id,
            //     selectedTableCode: item.code,
            //     seatedModal: true,
            //     displayQrModal: true
            // });
            // getQrCode(item.id)

            CommonStore.update((s) => {
              s.selectedOutletTable = item;
            });

            // setSeatingPax(0);
            // setSeatingModal(true);
            setDisplayQrModal(true);
          }}
          onLongPress={() => {
            setState({
              changeTable: !changeTable,
              selectedTableCode: item.code,
              selectedTableId: item.id,
              selectedTableSeated: item.seated,
            });
          }}
          delayLongPress={DELAY_LONG_PRESS_TIME}>
          <View style={{ flex: 1, justifyContent: 'space-between' }}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text
                style={{
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: 7,
                }}>
                {moment(item.updatedAt).format('hh:mma')}
              </Text>
              <Text style={[styles.tableCode, { color: Colors.whiteColor }]}>
                {item.code}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text
                style={[
                  styles.tableCode,
                  { fontSize: 11, color: Colors.whiteColor, top: -2 },
                ]}>
                {/* Seats: {item.seated}/{item.capacity} */}
                {/* {`${moment().diff(item.updatedAt, 'minute')}mins`} */}
                {moment().diff(item.updatedAt, 'minute') < 60
                  ? `${minutes} mins`
                  : `${hours}hrs:${minutes}mins`}
              </Text>
            </View>
            <View
              style={{
                alignItems: 'flex-end',
                top: -3,
              }}>
              <Text
                style={{
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 9,
                }}>
                Seats: {item.seated}/{item.capacity}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else if (
      item.seated > 0 &&
      // filtered.length > 0) { //seated with orders
      userOrdersTableDict[item.uniqueId] !== undefined
    ) {
      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            // need reimplement
            // filtered[0].status != 2 ? { backgroundColor: ((Math.trunc(item.estimateTime)) < 15 && filtered.status != 2) ? Colors.tabGrey : ((Math.trunc(item.estimateTime) >= 15) && (Math.trunc(item.estimateTime) < 20) && filtered.status != 2) ? Colors.tabYellow : Colors.tabRed, padding: 10 } : { backgroundColor: Colors.primaryColor }
            {
              backgroundColor:
                orderEstimatedTime >= 10 && orderEstimatedTime < 20
                  ? Colors.tabYellow
                  : orderEstimatedTime >= 20
                    ? Colors.tabRed
                    : Colors.primaryColor,
            },
            {
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,

              // ...((item.joinedTables) && {
              //     width: Dimensions.get('screen').width * 0.215 * (item.joinedTables.length),
              // }),
              ...(item.joinedTables && {
                width:
                  item.joinedTables.length == 2
                    ? Dimensions.get('screen').width * 0.215 * item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? Dimensions.get('screen').width * 0.215 * item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? Dimensions.get('screen').width * 0.214 * item.joinedTables.length
                        : Dimensions.get('screen').width * 0.215,
              }),
            },
          ]}
          onPress={() => {
            // setState({
            //     selectedTableId: item.id,
            //     selectedTableCode: item.code,
            //     viewTableOrderModal: true,

            //     selectedTableOrder: item.orders[0].id,
            //     selectedTableSeats: item.capacity,
            //     selectedTableCustomers: item.seated,
            // });
            // getOrderByTable(item.id)
            // getQrCode(item.id)

            CommonStore.update((s) => {
              s.selectedOutletTable = item;
            });

            setViewTableOrderModal(true);
          }}
          onLongPress={() => {
            setState({
              changeTable: !changeTable,
              selectedTableCode: item.code,
              selectedTableId: item.id,
              selectedTableSeated: item.seated,
            });
          }}
          delayLongPress={DELAY_LONG_PRESS_TIME}>
          <View style={{ flex: 1, justifyContent: 'space-between' }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                height: '33%',
              }}>
              <Text
                style={{
                  // fontSize: Dimensions.get('screen').width * 0.025,
                  fontSize: 7,
                  fontFamily: 'NunitoSans-SemiBold',
                  // color: calculateColorText(item.estimateTime)
                  color:
                    orderEstimatedTime >= 10 && orderEstimatedTime < 20
                      ? Colors.blackColor
                      : orderEstimatedTime >= 20
                        ? Colors.whiteColor
                        : Colors.whiteColor,
                }}>
                {/* {moment(item.orders[item.orders.length - 1].orderedAt).format('LT')} */}
                {moment(orderDate).format('hh:mma')}
              </Text>
              <Text
                style={[
                  styles.tableCode,
                  {
                    // color: calculateColorText(item.estimateTime)
                    color:
                      orderEstimatedTime >= 10 && orderEstimatedTime < 20
                        ? Colors.blackColor
                        : orderEstimatedTime >= 20
                          ? Colors.whiteColor
                          : Colors.whiteColor,
                    // fontSize: 15
                  },
                ]}>
                {item.code}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              {orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED ? (
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    alignSelf: 'center',
                    fontSize: 11,
                    // fontSize: item.estimateTime.toString().length < 4 ? 13 : 11,
                    // color: (Math.trunc(item.estimateTime)) < 15 ? Colors.whiteColor : (Math.trunc(item.estimateTime) >= 15 && Math.trunc(item.estimateTime) < 20) ? Colors.blackColor : Colors.whiteColor,
                    color:
                      orderEstimatedTime >= 10 && orderEstimatedTime < 20
                        ? Colors.blackColor
                        : orderEstimatedTime >= 20
                          ? Colors.whiteColor
                          : Colors.whiteColor,
                    top: -2,
                  }}>
                  {/* {Math.trunc(item.estimateTime)}mins */}
                  {/* {item.id} */}
                  {/* {orderEstimatedTime < 0 ? 0 : orderEstimatedTime.toFixed(0)}mins */}
                  {/* {hrs}hrs:{mins}mins */}
                  {orderEstimatedTime < 0
                    ? '0 mins'
                    : `${orderEstimatedTime}` < 60
                      ? `${mins} mins`
                      : `${hrs}hrs:${mins}mins`}
                </Text>
              ) : (
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    alignSelf: 'center',
                    fontSize: 11,
                    // color: (Math.trunc(item.estimateTime)) < 15 ? Colors.whiteColor : (Math.trunc(item.estimateTime) >= 15 && Math.trunc(item.estimateTime) < 20) ? Colors.blackColor : Colors.whiteColor,
                    color:
                      orderEstimatedTime >= 10 && orderEstimatedTime < 20
                        ? Colors.blackColor
                        : orderEstimatedTime >= 20
                          ? Colors.whiteColor
                          : Colors.whiteColor,
                    top: -2,
                  }}>
                  Served
                  {/* {item.id} */}
                </Text>
              )}
            </View>
            <View
              style={{
                alignItems: 'flex-end',
                // justifyContent: 'center',
                // height: "33%",
                top: -3,
              }}>
              <Text
                style={{
                  fontSize: 9,
                  // color: calculateColorText(item.estimateTime)
                  color:
                    orderEstimatedTime >= 10 && orderEstimatedTime < 20
                      ? Colors.blackColor
                      : orderEstimatedTime >= 20
                        ? Colors.whiteColor
                        : Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Seats: {item.seated}/{item.capacity}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  };

  const renderTableOrder = ({ item }) => {
    var styleStripEffect = {};
    if (item.priceToPay !== undefined && item.priceToPay === item.price) {
      styleStripEffect = {
        textDecorationLine: 'line-through',
      };
    }

    return (
      <>
        {item.addOns !== undefined &&
          (item.priceToPay === undefined ||
            (item.priceToPay !== undefined && item.priceToPay === item.price)) ? (
          <View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                marginBottom: 0,
              }}>
              <View style={{ flex: 2, alignItems: 'flex-start', }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                  }, styleStripEffect}>
                  {item.name}
                </Text>
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                  }, styleStripEffect}>
                  x {item.quantity}
                </Text>
              </View>
              <View style={{ flex: 1.5, justifyContent: 'space-between', flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                  }}>
                  RM{''}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                  }, styleStripEffect}>
                  {(item.price || 0).toFixed(2)}
                </Text>
              </View>
            </View>

            {item.remarks && item.remarks.length > 0 ? (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 0,
                }}>
                <View style={{ flex: 2, alignItems: 'flex-start' }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-SemiBold',
                      fontSize: Dimensions.get('screen').width * 0.034,
                    }, styleStripEffect}>
                    -{item.remarks}
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'center' }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: Dimensions.get('screen').width * 0.034,
                    }, styleStripEffect}></Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: Dimensions.get('screen').width * 0.034,
                    }}></Text>
                </View>
              </View>
            ) : (
              <></>
            )}

            <View
              style={{
                flexDirection: 'column',
                justifyContent: 'flex-start',
                marginTop: 0,
                marginBottom: 15,
              }}>
              {item.addOns.map((o) => {
                return (
                  <View style={{ flexDirection: 'row', width: '100%' }}>
                    {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                        fontSize: 12,
                      }, styleStripEffect}>
                      {o.name}:
                    </Text>
                    {o.choiceNames.map((choice) => {
                      return (
                        <Text
                          style={{
                            marginLeft: 5,
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.fieldtTxtColor,
                            fontSize: 12,
                          }, styleStripEffect}>
                          {choice}
                        </Text>
                        // <Text>I am the lioldmkkwnxcsjkasjnchjaiksdncejkajs</Text>
                      );
                    })}
                  </View>
                );
              })}
            </View>
          </View>
        ) : (
          <></>
        )}
      </>
    );
  };

  const proceedToCheckout = async () => {

    if (selectedOutletTable) {
      /////////////////////////////////////////////////
      // check if all orders paid already

      var isAllPaid = true;

      var userOrdersList = [
        ...userOrdersTableDict[selectedOutletTable.uniqueId],
      ];

      for (var i = 0; i < userOrdersList.length; i++) {
        for (var j = 0; j < userOrdersList[i].cartItems.length; j++) {
          if (userOrdersList[i].cartItems[j].priceToPay === undefined) {
            isAllPaid = false;
            break;
          }
        }

        if (!isAllPaid) {
          break;
        }
      }

      if (!isAllPaid) {
        Alert.alert('Info', "The orders of this table has not been fully paid yet.");
        return;
      }

      /////////////////////////////////////////////////
      CommonStore.update((s) => {
        s.isLoading = true;
      });


      if (selectedOutletTable) {
        const body = {
          tableId: selectedOutletTable.uniqueId,
        };

        ApiClient.POST(API.checkoutOutletTable, body).then((result) => {
          if (result) {
            Alert.alert('Success', 'Table has been checked out');
          }

          setViewTableOrderModal(false);
        });
      }

      CommonStore.update((s) => {
        s.selectedOrder = {};
      });
    }
  };

  return (
    <>
      <View
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'center',
          position: 'relative',
          width: '100%',
          height: '100%',
        }}>
        {/* <View style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',

                    paddingHorizontal: 12,
                }}>
                    <ScrollView
                        horizontal={true}
                    >
                        
                    </ScrollView>

                    <View style={{
                        position: 'absolute',
                        top: 16,
                        right: 16,

                        backgroundColor: '#F7F7F7',
                        borderRadius: 6,

                        elevation: 0,
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,

                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',

                        paddingHorizontal: 6,
                        paddingVertical: 6,
                    }}>
                        <MaterialIcons name={'add'} size={24} color={Colors.primaryColor} />
                    </View>
                </View> */}

        {/* ////////////////////////////////////////////////////////////// */}

        {/* ////////////////////////////////////////////////////////////// */}

        <View
          style={{
            // flex: 0.8,
            justifyContent: 'center',
            flexDirection: 'row',
            alignItems: 'center',
            width: '83%',
            marginRight: '14%'
          }}>
          <FlatList
            contentContainerStyle={styles.sectionAreaFlatList}
            data={outletSections}
            renderItem={renderSection}
            keyExtractor={(item, index) => String(index)}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            style={{
              top: -30,
              // backgroundColor: 'red',
              width: '100%',
              // width: 50,
              height: 100,
            }}
            contentContainerStyle={{
              // width: '100%',

              // marginRight: 100,

              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              paddingTop: 30,
              paddingLeft: 5,
              paddingRight: 70,
            }}
          />
        </View>

        <TouchableOpacity
          onPress={() => {
            setAddSectionAreaModel(true);
          }}
          style={{
            position: 'absolute',
            top: 16,
            right: 16,

            backgroundColor: '#F7F7F7',
            borderRadius: 6,

            elevation: 0,
            shadowOpacity: 0,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 3,

            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',

            paddingHorizontal: 6,
            paddingVertical: 6,
          }}>
          <MaterialIcons name={'add'} size={24} color={Colors.primaryColor} />
        </TouchableOpacity>
        {/* </View> */}

        <View
          style={{
            marginTop: -12,
            paddingHorizontal: 8,
            paddingLeft: Dimensions.get('screen').width * 0.04,
            width: '100%',
            height: '78%',
            // height: Dimensions.get('screen').height * 0.5,
            // backgroundColor: 'blue',
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}>
          <FlatList
            // data={filteredOutletTables.concat({ actionButton: true })}
            data={filteredOutletTablesForRendered.concat({ actionButton: true })}
            // extraData={filteredOutletTables.concat({ actionButton: true })}
            showsVerticalScrollIndicator={false}
            renderItem={renderTableLayout}
            keyExtractor={(item, index) => String(index)}
            // numColumns={4}
            // horizontal={true}
            contentContainerStyle={{
              // backgroundColor: 'red',
              paddingBottom: 35,
              flexDirection: 'row',
              flexWrap: 'wrap',
              // backgroundColor: 'blue',
              // justifyContent: 'center',
            }}
            keyboardShouldPersistTaps="handled"
            maxToRenderPerBatch={1}
          />
        </View>

        <View
          style={{
            position: 'absolute',
            bottom: 0,
            backgroundColor: 'white',
            height: '10%',
            width: Dimensions.get('screen').width,

            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',

            elevation: 20,
            zIndex: 20,

            shadowOpacity: 0,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
          }}>
          <View
            style={{
              backgroundColor: 'white',
              alignItems: 'center',
              flexDirection: 'row',
            }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.06,
                }}>
                {filteredOutletTables.length}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: Dimensions.get('screen').width * 0.03,
                }}>
                TABLES
              </Text>
            </View>

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.06,
                }}>
                {filteredOutletTables.reduce((acc, table) => {
                  return acc + table.seated;
                }, 0)}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: Dimensions.get('screen').width * 0.03,
                }}>
                SEATS
              </Text>
            </View>

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.06,
                }}>
                {filteredOutletTables.reduce((acc, table) => {
                  return acc + (table.capacity - table.seated);
                }, 0)}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: Dimensions.get('screen').width * 0.03,
                }}>
                VACANT
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* //////////////////////////////////////////////////////////////////////////////// */}

      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={addSectionAreaModel} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').width * 0.7,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setAddSectionAreaModel(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Add Section</Text>
              <Text style={styles.modalDescText}>Fill in the section name</Text>
            </View>
            <View style={styles.modalBody}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={styles.modalBodyText}>Name </Text>
                <TextInput
                  onChangeText={(sectionName) => {
                    setInputSectionName(sectionName);
                  }}
                  style={{
                    width: Dimensions.get('screen').width * 0.4,
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    marginLeft: 10,
                    borderRadius: 8,
                  }}
                  placeholder={'E.g: Floor 1'}
                  textAlign={'center'}
                  maxLength={12}
                />
              </View>
            </View>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={() => {
                addSectionArea();
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={addTableModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').height * 0.4,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setAddTableModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Add Table</Text>
              <Text style={styles.modalDescText}>
                Fill in the table details
              </Text>
            </View>
            <View style={styles.modalBody}>
              <View
                style={{ flexDirection: 'row', width: '100%', marginBottom: 15 }}>
                <Text style={styles.modalBodyText}>Code </Text>
                <TextInput
                  onChangeText={(code) => {
                    setInputTableCode(code);
                  }}
                  style={{
                    width: Dimensions.get('screen').width * 0.4,
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    marginLeft: 10,
                    borderRadius: 8,
                  }}
                  placeholder={'E.g: A1'}
                  textAlign={'center'}
                  maxLength={12}
                />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: '100%',
                }}>
                <Text style={styles.modalBodyText}>Pax </Text>
                <TextInput
                  onChangeText={(pax) => {
                    setInputTablePax(pax.length > 0 ? parseInt(pax) : 0);
                  }}
                  style={{
                    width: Dimensions.get('screen').width * 0.4,
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    marginLeft: 10,
                    borderRadius: 8,
                  }}
                  placeholder={'E.g: 6'}
                  keyboardType={'numeric'}
                  textAlign={'center'}
                  maxLength={12}
                />
              </View>
            </View>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={() => {
                addTable();
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={deleteTableModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalView}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setState({ deleteTableModal: false });
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalBody}>
              <Text style={styles.modalTitleText}>
                Delete Table {selectedTableCode}?{' '}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={() => {
                deleteTable(selectedTableId);
              }}
              onValueChange={() => {
                checkOrderItem(item.id);
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Confirm
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={seatingModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={{
              width: Dimensions.get('screen').width * 0.8,
              height: Dimensions.get('screen').height * 0.6,
              backgroundColor: Colors.whiteColor,
              borderRadius: Dimensions.get('screen').width * 0.07,
              padding: Dimensions.get('screen').width * 0.07,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setSeatingModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Seating</Text>
              <View
                style={[
                  styles.tableSlotDisplay,
                  {
                    // width: Dimensions.get('screen').width * 0.25,
                    ...(selectedOutletTable.code &&
                      selectedOutletTable.code.length && {
                      width: 27 * selectedOutletTable.code.length,
                    }),
                    minWidth: 100,
                    height: Dimensions.get('screen').width * 0.25,
                    paddingTop: 0,
                    paddingBottom: 0,
                  },
                ]}>
                <Text
                  style={[
                    styles.modalTitleText,
                    { fontSize: Dimensions.get('screen').width * 0.09 },
                  ]}>
                  {selectedOutletTable.code}
                </Text>
              </View>
            </View>
            <View
              style={[styles.modalBody, { width: '100%', alignItems: 'center' }]}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: '100%',
                }}>
                <Text
                  style={[
                    styles.modalBodyText,
                    { flex: 1, fontFamily: 'NunitoSans-Bold' },
                  ]}>
                  Table {selectedOutletTable.code}
                </Text>
                <View
                  style={{
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    flex: 0.99,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: Colors.fieldtTxtColor,
                  }}>
                  <TouchableOpacity
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: Dimensions.get('screen').width * 0.065,
                      height: Dimensions.get('screen').width * 0.065,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onPress={() => {
                      seatingPax >= 1 ? setSeatingPax(seatingPax - 1) : null;
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: 10,
                        color: Colors.whiteColor,
                      }}>
                      -
                    </Text>
                  </TouchableOpacity>
                  <View
                    style={{ alignItems: 'center', justifyContent: 'center' }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.primaryColor,
                      }}>
                      {seatingPax}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: Dimensions.get('screen').width * 0.065,
                      height: Dimensions.get('screen').width * 0.065,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onPress={() => {
                      seatingPax <
                        selectedOutletTable.capacity - selectedOutletTable.seated
                        ? setSeatingPax(seatingPax + 1)
                        : null;
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: 10,
                        color: Colors.whiteColor,
                      }}>
                      +
                    </Text>
                  </TouchableOpacity>
                </View>
                <View style={{ flex: 1, alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-SemiBold' }}>
                    Capacity{' '}
                    {selectedOutletTable.capacity - selectedOutletTable.seated}
                  </Text>
                </View>
              </View>
            </View>

            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: '100%',
              }}>
              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={() => {
                  setInputTablePax(selectedOutletTable.capacity);
                  setUpdateTableModal(true);
                }}>
                <Text
                  style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                  Edit
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={() => {
                  occupyingSeats(seatingPax);
                }}>
                <Text
                  style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                  Seated
                </Text>
              </TouchableOpacity>
            </View>

            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: '100%',
                marginTop: 15,
                // display: selectedOutletTable.joinedTables ? 'none' : 'flex',
              }}>
              {!selectedOutletTable.joinedTables ||
                (selectedOutletTable.joinedTables &&
                  selectedOutletTable.joinedTables.length < 4) ? (
                <TouchableOpacity
                  style={styles.modalSaveButton}
                  onPress={() => {
                    // setInputTablePax(selectedOutletTable.capacity);
                    // setUpdateTableModal(true);

                    // setInputTableCode('');
                    setJoinTableModal(true);
                  }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.primaryColor },
                    ]}>
                    Join
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  disabled={true}
                  style={[
                    styles.modalSaveButton,
                    {
                      opacity: 0,
                    },
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.primaryColor },
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
              )}

              {selectedOutletTable.joinedTables ? (
                <TouchableOpacity
                  style={styles.modalSaveButton}
                  onPress={() => {
                    // setInputTablePax(selectedOutletTable.capacity);
                    // setUpdateTableModal(true);

                    if (
                      userOrdersTableDict[selectedOutletTable.uniqueId] &&
                      userOrdersTableDict[selectedOutletTable.uniqueId].length >
                      0
                    ) {
                      Alert.alert(
                        'Info',
                        `Please clear all current order(s) on this table`,
                        [
                          {
                            text: 'OK',
                            onPress: () => {
                              splitTable();
                            },
                          },
                        ],
                        { cancelable: false },
                      );
                    } else {
                      Alert.alert(
                        'Confirmation',
                        `Are you sure you want to split table ${selectedOutletTable.code}?`,
                        [
                          {
                            text: 'YES',
                            onPress: () => {
                              splitTable();
                            },
                          },
                        ],
                        { cancelable: false },
                      );
                    }

                    // setInputTableCode('');
                    // setJoinTableModal(true);
                  }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.primaryColor },
                    ]}>
                    Split
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  disabled={true}
                  style={[
                    styles.modalSaveButton,
                    {
                      opacity: 0,
                    },
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.primaryColor },
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: '100%',
                marginTop: 15,
              }}>
              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={() => {
                  Alert.alert(
                    'Info',
                    `Are you sure you want to remove table ${selectedOutletTable.code}?`,
                    [
                      {
                        text: 'NO',
                        onPress: () => {
                          // deleteOutletTable();
                        },
                      },
                      {
                        text: 'YES',
                        onPress: () => {
                          deleteOutletTable();
                        },
                      },
                    ],
                    { cancelable: false },
                  );
                }}>
                <Text style={[styles.modalDescText, { color: Colors.tabRed }]}>
                  Delete
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                disabled={true}
                style={[
                  styles.modalSaveButton,
                  {
                    opacity: 0,
                  },
                ]}
                onPress={() => { }}>
                <Text
                  style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                  Seated
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={displayQrModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').height * 0.58,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setDisplayQrModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 22,
                }}>
                Table {selectedTableCode}
              </Text>
            </View>
            <View style={styles.modalBody}>
              {showGenericQRCode ? (
                <QRCode
                  // value={JSON.stringify(qrData)}
                  // value={selectedOutletTable.uniqueId ? selectedOutletTable.uniqueId : ''}
                  // value={selectedOutletTable.uniqueId ? JSON.stringify({
                  //   uniqueId: selectedOutletTable.uniqueId,
                  //   outletId: selectedOutletTable.outletId,
                  //   code: selectedOutletTable.code,
                  //   pax: selectedOutletTable.seated,

                  //   waiterId: firebaseUid,
                  //   waiterName: name,
                  // }) : ''}
                  value={
                    selectedOutletTable.uniqueId
                      ? `${qrUrl}new-order-generic/${selectedOutletTable.outletId}/${selectedOutletTable.uniqueId}/${selectedOutletTable.code}`
                      : ''
                  }
                  size={Dimensions.get('screen').width * 0.55}
                  logoBackgroundColor="transparent"
                />
              ) : (
                <QRCode
                  // value={JSON.stringify(qrData)}
                  // value={selectedOutletTable.uniqueId ? selectedOutletTable.uniqueId : ''}
                  // value={selectedOutletTable.uniqueId ? JSON.stringify({
                  //   uniqueId: selectedOutletTable.uniqueId,
                  //   outletId: selectedOutletTable.outletId,
                  //   code: selectedOutletTable.code,
                  //   pax: selectedOutletTable.seated,

                  //   waiterId: firebaseUid,
                  //   waiterName: name,
                  // }) : ''}
                  value={
                    selectedOutletTable.uniqueId
                      ? `${qrUrl}new-order/${selectedOutletTable.outletId}/${selectedOutletTable.uniqueId}/${selectedOutletTable.code}/${selectedOutletTable.seated}/${firebaseUid}`
                      : ''
                  }
                  size={Dimensions.get('screen').width * 0.55}
                  logoBackgroundColor="transparent"
                />
              )}
            </View>
            {/* <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 20,
              }}>
              <Button
                title={
                  showGenericQRCode
                    ? 'Switch to Dynamic QR'
                    : 'Switch To Generic QR'
                }
                color={Colors.primaryColor}
                onPress={() => {
                  setShowGenericQRCode(!showGenericQRCode);
                }}
              />
            </View> */}

            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
                marginTop: 20,
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: Dimensions.get('screen').width * 0.55,
                  height: 35,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setShowGenericQRCode(!showGenericQRCode);
                }}>
                <Text style={{ color: Colors.whiteColor }}>
                  {
                    showGenericQRCode
                      ? 'SWITCH TO DYNAMIC QR'
                      : 'SWITCH TO  GENERIC QR'
                  }
                </Text>
              </TouchableOpacity>
            </View>

            {/* <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
              <Button
                title="Switch Table"
                color={Colors.primaryColor}
                size={Dimensions.get('screen').width * 0.8}
                onPress={() => {
                  setChangeTable(true);
                }}
              />
              // <OrderModal
              //                   tableId={selectedTableId}
              //                   currentOrderId={selectedTableOrder}
              //                   close={() =>
              //                       setState({ displayQrModal: false, viewTableOrderModal: false })
              //                   }
              //                   navigationProp={props.navigation}
              //               />
            </View> */}

            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: Dimensions.get('screen').width * 0.55,
                  height: 35,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setChangeTable(true);
                }}>
                <Text style={{ color: Colors.whiteColor }}>
                  SWITCH TABLE
                </Text>
              </TouchableOpacity>
            </View>

            {/* <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
            <Button
                title="Order"
                color={Colors.primaryColor}
                onPress={() => {
                  // setState({ displayQrModal: false, viewTableOrderModal: false })

                  // Cart.setTableNumber(selectedTableId)
                  // Cart.setCurrentOrderId(selectedTableOrder)
                  // props.navigation.navigate('OutletMenu', {
                  //     params: {
                  //         outletData: {},
                  //         orderType: 0,
                  //         test: {},
                  //         navFrom: 'DINEIN',
                  //     }
                  // });

                  setDisplayQrModal(false);
                  setViewTableOrderModal(false);
                  navigation.navigate('OutletMenu', {
                    params: {
                      outletData: {},
                      orderType: 0,
                      test: {},
                      navFrom: 'DINEIN',
                    },
                  });
                }}
              />
               </View> */}

            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: Dimensions.get('screen').width * 0.55,
                  height: 35,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setDisplayQrModal(false);
                  setViewTableOrderModal(false);
                  navigation.navigate('OutletMenu', {
                    params: {
                      outletData: {},
                      orderType: 0,
                      test: {},
                      navFrom: 'DINEIN',
                    },
                  });
                }}>
                <Text style={{ color: Colors.whiteColor }}>
                  ORDER
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={changeTable} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').height * 0.4,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                // setState({ changeTable: false });
                setChangeTable(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Switch Table</Text>
            </View>
            <View style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
              <Text style={styles.modalBodyText}>
                {/* Current Table: {selectedTableCode} */}
                {`Current Table: ${selectedOutletTable.code}`}
              </Text>
              <Text style={styles.modalBodyText}>TO</Text>
              <View
                style={{
                  // backgroundColor: 'red',
                  width: 200,
                  marginBottom: 20,
                }}>
                {/* {console.log('outletTablesDropdown')}
                                {console.log(outletTables.map(table => {
                                        console.log('outletTables');
                                        console.log(userOrdersTableDict[table.uniqueId]);
                                        console.log(selectedOutletTable.uniqueId);

                                        if (userOrdersTableDict[table.uniqueId] === undefined && 
                                            table.uniqueId !== selectedOutletTable.uniqueId) {
                                            return { label: table.code, value: table.uniqueId };
                                        }                                        
                                    }))} */}

                {/* <DropDownPicker
                                    items={outletTables.filter(table => {
                                        if (userOrdersTableDict[table.uniqueId] === undefined &&
                                            table.uniqueId !== selectedOutletTable.uniqueId) {
                                            return true;
                                        }
                                    }).map(table => {
                                        return { label: table.code, value: table.uniqueId };
                                    })}
                                    placeholder={"Select "}
                                    placeholderStyle={{ color: 'black', textAlign: 'center' }}
                                    labelStyle={{ color: 'black', textAlign: 'center' }}
                                    containerStyle={{ height: 50, width: 120 }}
                                    dropDownStyle={{ height: 100, width: 120 }}
                                    onChangeItem={item => {
                                        setChangeTableId(item.value)
                                    }}
                                /> */}

                <RNPickerSelect
                  placeholder={{}}
                  style={Styles.rnPickerSelectStyle}
                  items={outletTables
                    .filter((table) => {
                      if (
                        userOrdersTableDict[table.uniqueId] === undefined &&
                        table.uniqueId !== selectedOutletTable.uniqueId
                      ) {
                        return true;
                      }
                    })
                    .map((table) => {
                      return { label: table.code, value: table.uniqueId };
                    })}
                  onValueChange={(value) => {
                    setChangeTableId(value);
                  }}
                />
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.modalSaveButton,
                {
                  zIndex: -100,
                },
              ]}
              onPress={() => {
                tableExchange();
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={viewTableOrderModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                width: Dimensions.get('screen').width * 0.9,
                height: Dimensions.get('screen').height * 0.8,
                paddingVertical: 25,
                paddingHorizontal: 15,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                // setState({ viewTableOrderModal: false, displayQrModal: false });
                setViewTableOrderModal(false);
                setDisplayQrModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={[styles.modalTitle, { zIndex: -1 }]}>
              <Text style={styles.modalTitleText}>
                Table {selectedTableCode}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-around',
                  width: '100%',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text
                  ellipsizeMode={'tail'}
                  numberOfLines={1}
                  style={[
                    styles.modalTitleText,
                    {
                      fontSize: Dimensions.get('screen').width * 0.04,
                      marginBottom: 0,
                      width: '30%',
                    },
                  ]}>
                  #
                  {userOrdersTableDict[selectedOutletTable.uniqueId] &&
                    userOrdersTableDict[selectedOutletTable.uniqueId][0]
                    ? userOrdersTableDict[selectedOutletTable.uniqueId][0]
                      .orderId
                    : 'N/A'}
                </Text>
                <Text
                  style={[
                    styles.modalTitleText,
                    { fontSize: Dimensions.get('screen').width * 0.04, marginBottom: 0 },
                  ]}>
                  Seats: {selectedOutletTable.seated}/
                  {selectedOutletTable.capacity}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    setDisplayQrModal(true);
                  }}
                  style={{ position: 'absolute', right: 10 }}>
                  <Image
                    style={{
                      height: Dimensions.get('screen').width * 0.1,
                      width: Dimensions.get('screen').width * 0.1,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/qr.png')}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                width: Dimensions.get('screen').width * 0.9,
                height: '10%',
                marginTop: 20,
                flexDirection: 'row',
              }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: orderDisplayIndividual
                    ? Colors.lightPrimary
                    : Colors.primaryColor,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setOrderDisplayIndividual(false);
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: orderDisplayIndividual
                      ? Colors.blackColor
                      : Colors.whiteColor,
                  }}>
                  Summary
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: orderDisplayIndividual
                    ? Colors.primaryColor
                    : Colors.lightPrimary,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setOrderDisplayIndividual(true);
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: orderDisplayIndividual
                      ? Colors.whiteColor
                      : Colors.blackColor,
                  }}>
                  Individual
                </Text>
              </TouchableOpacity>
            </View>
            <View style={[styles.modalBody, { width: '100%', marginTop: 20 }]}>
              {orderDisplayIndividual &&
                userOrdersTableDict[selectedOutletTable.uniqueId] &&
                userOrdersTableDict[selectedOutletTable.uniqueId].length > 0 ? (
                <FlatList
                  style={{ width: '100%', height: '100%' }}
                  // data={tableOrder}
                  data={userOrdersTableDict[
                    selectedOutletTable.uniqueId
                  ].reduce((accu, order) => accu.concat(order.cartItems), [])}
                  renderItem={renderTableOrder}
                  keyExtractor={(item, index) => String(index)}
                />
              ) : null}

              {!orderDisplayIndividual &&
                userOrdersTableDict[selectedOutletTable.uniqueId] &&
                userOrdersTableDict[selectedOutletTable.uniqueId].length > 0 ? (
                <FlatList
                  style={{ width: '100%', height: '100%' }}
                  // data={tableOrder}
                  data={userOrdersTableDict[
                    selectedOutletTable.uniqueId
                  ].reduce((accu, order) => accu.concat(order.cartItems), [])}
                  renderItem={renderTableOrder}
                  keyExtractor={(item, index) => String(index)}
                />
              ) : null}
            </View>
            <View
              style={{ borderWidth: 1, borderColor: '#E5E5E5', width: '100%' }}
            />

            <View
              style={{
                justifyContent: 'space-between',
                width: Dimensions.get('screen').width * 0.45,
                flexDirection: 'row',
                alignSelf: 'center',
                paddingTop: 10,
                alignSelf: 'flex-end',
                marginBottom: 15,
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.04,
                  color: Colors.descriptionColor,
                  width: '45%',
                }}>
                TAX (6%):
              </Text>
              {userOrdersTableDict[selectedOutletTable.uniqueId] ? (
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.04,
                    color: Colors.descriptionColor,
                    width: '50%',
                  }}>
                  RM{' '}
                  {userOrdersTableDict[selectedOutletTable.uniqueId]
                    .reduce(
                      (accu, order) =>
                        // accu + order.totalPrice + order.tax,
                        accu + order.tax,
                      0,
                    )
                    .toFixed(2)}
                  {/* 0 */}
                </Text>
              ) : null}
            </View>


            {/* Align RM */}
            {/* <View
              style={{
                flexDirection: 'row',
                paddingTop: 10,
                alignSelf: 'flex-end',
              }}>
              <View style={{ flex: 2,alignItems: 'flex-start',}}/>
              <View style={{ flex: 1,}}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.descriptionColor,
                    textAlign:'right',
                    paddingRight:5
                  }}>
                  TAX (6%):
                </Text>
              </View>
              
              {userOrdersTableDict[selectedOutletTable.uniqueId] ? (
                <View style={{ flex: 1.5, justifyContent:'space-between', flexDirection:'row'}}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.descriptionColor,
                  }}>
                  RM{''}
                  </Text>
                  <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.descriptionColor,
                    textAlign:'right'
                  }}>
                  {userOrdersTableDict[selectedOutletTable.uniqueId]
                    .reduce(
                      (accu, order) =>
                        // accu + order.totalPrice + order.tax,
                        accu + order.tax,
                      0,
                    )
                    .toFixed(2)}
                  // 0 
                </Text>
                </View>
              ) : null}
            </View>
             */}

            <View
              style={{
                flex: 0.2,
                justifyContent: 'space-between',
                width: Dimensions.get('screen').width * 0.45,
                flexDirection: 'row',
                alignSelf: 'flex-end',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.04,
                  color: Colors.primaryColor,
                  width: '45%',
                }}>
                TOTAL:
              </Text>
              {userOrdersTableDict[selectedOutletTable.uniqueId] ? (
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.04,
                    color: Colors.primaryColor,
                    width: '50%',
                  }}>
                  RM
                  {userOrdersTableDict[selectedOutletTable.uniqueId]
                    .reduce(
                      (accu, order) => accu + order.totalPrice + order.tax,
                      0,
                    )
                    .toFixed(2)}
                  {/* 0 */}
                </Text>
              ) : null}
            </View>

            {/* Align RM */}
            {/* <View
              style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
              }}>
               <View style={{ flex: 2, alignItems: 'flex-start',}}/>
               <View style={{ flex: 1,  }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.primaryColor,
                    textAlign: 'right',
                    paddingRight:5
                  }}>
                  TOTAL: 
                </Text>
              </View>

              {userOrdersTableDict[selectedOutletTable.uniqueId] ? (
                <View style={{ flex: 1.5, justifyContent:'space-between', flexDirection:'row'}}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.primaryColor,
                  }}>
                  RM{''}
                  </Text>
                  <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: Dimensions.get('screen').width * 0.034,
                    color: Colors.primaryColor,
                    textAlign:'right'
                  }}>
                  {userOrdersTableDict[selectedOutletTable.uniqueId]
                    .reduce(
                      (accu, order) => accu + order.totalPrice + order.tax,
                      0,
                    )
                    .toFixed(2)}
                  //0
                </Text>
                </View>
              ) : null}
            </View> */}

            <View style={{ flex: 0.2 }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  borderRadius: 8,
                  //width: Dimensions.get('screen').width * 0.4,
                  //height: Dimensions.get('screen').width * 0.1,
                  width: 130,
                  height: 38,
                  alignItems: 'center',
                  justifyContent: 'center',
                  //borderRadius: Dimensions.get('screen').width * 0.015,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                }}
                onPress={async () => {
                  // no function yet
                  // do checking here
                  // const order = new Promise(async (resolve, reject) => {
                  //     await ApiClient.GET(API.tableOrder + tableId).then((result) => {

                  //         // setState({
                  //         //     orderInfo: result,
                  //         //     orderId: result.id,
                  //         //     tableOrder: result.orderItems,
                  //         //     total: result.finalPrice,

                  //         // })

                  //         resolve(result);
                  //     }).catch(err => {
                  //         console.log(err);
                  //         reject(err);
                  //     });
                  // });

                  // if (order && order.id && order.orderItems.length > 0) {
                  //     setState({
                  //         deleteTableModal: true,
                  //         viewTableOrderModal: false
                  //     });
                  // }
                  // else {
                  //     setState({
                  //         deleteTableModal: false,
                  //         viewTableOrderModal: false,
                  //         preventDeleteTableModal: true,
                  //     });
                  // }

                  console.log('test');

                  proceedToCheckout();
                }}>
                <Modal
                  supportedOrientations={['landscape', 'portrait']}
                  style={{ flex: 1 }}
                  visible={deleteTableModal}
                  transparent={true}>
                  <View style={styles.modalContainer}>
                    <View style={styles.modalView}>
                      <TouchableOpacity
                        style={styles.closeButton}
                        onPress={() => {
                          setState({ deleteTableModal: false });
                        }}>
                        <AIcon
                          name="closecircle"
                          size={25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={styles.modalBody}>
                        <Text style={styles.modalTitleText}>
                          Delete Table {selectedTableCode}?{' '}
                        </Text>
                      </View>
                      <TouchableOpacity
                        style={styles.modalSaveButton}
                        onPress={() => {
                          deleteTable(selectedTableId);
                        }}
                        onValueChange={() => {
                          checkOrderItem(item.id);
                          completePayment();
                        }}>
                        <Text
                          style={[
                            styles.modalDescText,
                            { color: Colors.primaryColor },
                          ]}>
                          Confirm
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </Modal>

                <Modal
                  supportedOrientations={['landscape', 'portrait']} supportedOrientations={['landscape', 'portrait']}
                  style={{ flex: 1 }}
                  visible={preventDeleteTableModal}
                  transparent={true}>
                  <View style={styles.modalContainer}>
                    <View style={styles.modalView}>
                      <Text>Can't Delete</Text>
                      {/* <TouchableOpacity
                                                style={styles.closeButton}
                                                onPress={() => {
                                                    setState({ deleteTableModal: false });
                                                }}>
                                                <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                            </TouchableOpacity>
                                            <View style={styles.modalBody}>
                                                <Text style={styles.modalTitleText}>Delete Table {selectedTableCode}? </Text>
                                            </View>
                                            <TouchableOpacity
                                                style={styles.modalSaveButton}
                                                onPress={() => { deleteTable(selectedTableId) }}
                                                onValueChange={() => { checkOrderItem(item.id);completePayment() }}>
                                                    
                                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Confirm</Text>
                                            </TouchableOpacity> */}
                    </View>
                  </View>
                </Modal>

                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.descriptionColor,
                  }}>
                  CHECKOUT
                </Text>
              </TouchableOpacity>
            </View>

            {/* <View style={{
                            width: '100%',
                            height: 1,
                            backgroundColor: Colors.descriptionColor,
                        }}>
                        </View> */}

            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-around',
                marginTop: 30,
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: 14,
                }}>
                Amount To Pay
              </Text>
              <TextInput
                onChangeText={(text) => {
                  if (text.length > 0) {
                    var amount = parseFloat(text);

                    var payableAmount = 0;
                    var paidAmount = 0;

                    if (
                      userOrdersTableDict[selectedOutletTable.uniqueId] &&
                      userOrdersTableDict[selectedOutletTable.uniqueId][0]
                    ) {
                      payableAmount =
                        userOrdersTableDict[selectedOutletTable.uniqueId][0]
                          .finalPrice;

                      if (
                        userOrdersTableDict[selectedOutletTable.uniqueId][0]
                          .splitAmountList
                      ) {
                        paidAmount = userOrdersTableDict[
                          selectedOutletTable.uniqueId
                        ][0].splitAmountList.reduce(
                          (accu, splitAmount) => accu + splitAmount.amount,
                          0,
                        );

                        payableAmount = +(payableAmount - paidAmount).toFixed(
                          2,
                        );
                      }
                    }

                    amount = amount > payableAmount ? payableAmount : amount;
                    amount = amount < 0 ? 0 : amount;

                    setSplitAmount(amount);
                  } else {
                    setSplitAmount(0);
                  }
                }}
                style={{
                  width: 100,
                  height: 38,
                  backgroundColor: Colors.fieldtBgColor,
                  marginLeft: 10,
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                }}
                placeholder={'Split Amount'}
                textAlign={'center'}
                maxLength={12}
                keyboardType={'number-pad'}
                defaultValue={splitAmount.toFixed(2)}
              />
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  width: 70,
                  height: 38,
                  // paddingVertical: 15,
                  // paddingMargin: 25,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: Dimensions.get('screen').width * 0.015,
                  marginLeft: 15,
                }}
                onPress={async () => {
                  splitUserOrderBill();
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.descriptionColor,
                  }}>
                  SPLIT
                </Text>
              </TouchableOpacity>
            </View>

            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: 25,
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 16,
                  color: Colors.primaryColor,
                  marginLeft: 15,
                }}>{`Paid Amount: RM ${(userOrdersTableDict[
                  selectedOutletTable.uniqueId
                ] &&
                  userOrdersTableDict[selectedOutletTable.uniqueId][0] &&
                  userOrdersTableDict[selectedOutletTable.uniqueId][0]
                    .splitAmountList
                  ? userOrdersTableDict[
                    selectedOutletTable.uniqueId
                  ][0].splitAmountList.reduce(
                    (accu, splitAmount) => accu + splitAmount.amount,
                    0,
                  )
                  : 0
                ).toFixed(2)}`}</Text>
            </View>
          </View>
        </View>
      </Modal>

      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={updateTableModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').height * 0.3,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setUpdateTableModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Update Table</Text>
              <Text style={styles.modalDescText}>
                Fill in the table details
              </Text>
            </View>
            <View style={styles.modalBody}>
              {/* <View style={{ flexDirection: 'row', width: '100%', marginBottom: 15, }}>
                                <Text style={styles.modalBodyText}>Code </Text>
                                <TextInput
                                    onChangeText={(code) => { setInputTableCode(code) }}
                                    style={{ width: Dimensions.get('screen').width * 0.4, height: 40, backgroundColor: Colors.fieldtBgColor, marginLeft: 10, borderRadius: 8 }}
                                    placeholder={"E.g: A1"}
                                    textAlign={'center'}
                                    maxLength={12}
                                />
                            </View> */}
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: '100%',
                }}>
                <Text style={styles.modalBodyText}>Pax </Text>
                <TextInput
                  onChangeText={(pax) => {
                    setInputTablePax(pax.length > 0 ? parseInt(pax) : 0);
                  }}
                  style={{
                    width: Dimensions.get('screen').width * 0.4,
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    marginLeft: 10,
                    borderRadius: 8,
                  }}
                  placeholder={'E.g: 6'}
                  keyboardType={'numeric'}
                  textAlign={'center'}
                  maxLength={12}
                  value={inputTablePax.toFixed(0)}
                />
              </View>
            </View>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={() => {
                updateTable();
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={joinTableModal} transparent={true}>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: Dimensions.get('screen').height * 0.4,
              },
            ]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setJoinTableModal(false);
              }}>
              <AIcon
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={styles.modalTitleText}>Join Tables</Text>
              <Text style={styles.modalDescText}>
                Fill in the table details
              </Text>
            </View>
            <View style={styles.modalBody}>
              {/* <View style={{ flexDirection: 'row', width: '100%', marginBottom: 15, }}>
                                <Text style={styles.modalBodyText}>Code </Text>
                                <TextInput
                                    onChangeText={(code) => { setInputTableCode(code) }}
                                    style={{ width: Dimensions.get('screen').width * 0.4, height: 40, backgroundColor: Colors.fieldtBgColor, marginLeft: 10, borderRadius: 8 }}
                                    placeholder={"E.g: A1"}
                                    textAlign={'center'}
                                    maxLength={12}
                                />
                            </View> */}
              <View
                style={{
                  flexDirection: 'row',
                  width: '80%',
                  marginBottom: 20,
                  alignItems: 'center',
                }}>
                <Text style={styles.modalBodyText}>Code</Text>
                <TextInput
                  editable={false}
                  onChangeText={(code) => {
                    setInputTableCode(code);
                  }}
                  style={{
                    width: Dimensions.get('screen').width * 0.4,
                    height: 40,
                    backgroundColor: Colors.fieldtBgColor,
                    marginLeft: 10,
                    borderRadius: 8,
                  }}
                  placeholder={'E.g: A1'}
                  textAlign={'center'}
                  maxLength={12}
                  defaultValue={inputTableCode}
                />
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: '80%',
                  marginBottom: 20,
                  alignItems: 'center',
                }}>
                <Text style={styles.modalBodyText}>Table</Text>

                <View
                  style={{
                    // backgroundColor: 'red',
                    width: Dimensions.get('screen').width * 0.4,
                  }}>
                  <RNPickerSelect
                    placeholder={{}}
                    style={Styles.rnPickerSelectStyle}
                    // use tables of current floor
                    items={joinTableItems}
                    value={joinTableId}
                    onValueChange={(value) => {
                      setJoinTableId(value);
                    }}
                  />
                </View>
              </View>
            </View>
            <TouchableOpacity
              disabled={isLoading}
              style={styles.modalSaveButton}
              onPress={() => {
                joinTable();
              }}>
              <Text
                style={[styles.modalDescText, { color: Colors.primaryColor }]}>
                Join
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* //////////////////////////////////////////////////////////////////////////////////////////// */}

      <View
        style={{
          flex: 1,
          backgroundColor: Colors.fieldtBgColor,
          display: 'none',
        }}>
        {changeTable ? (
          <View
            style={{
              position: 'absolute',
              width: Dimensions.get('screen').width,
              height: Dimensions.get('screen').height,
              backgroundColor: Colors.modalBgColor,
              // zIndex: 1,
              opacity: 0.99,
            }}
          />
        ) : null}

        <View
          style={{
            flex: 0.8,
            justifyContent: 'center',
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <FlatList
            contentContainerStyle={styles.sectionAreaFlatList}
            data={sectionArea}
            renderItem={renderSection}
            keyExtractor={(item, index) => String(index)}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            ListFooterComponent={addSectionButton}
          />
        </View>
        <View
          style={{
            flex: 8,
            alignItems: 'center',
          }}>
          <FlatList
            // data={tableSlots}
            data={[]}
            showsVerticalScrollIndicator={false}
            renderItem={renderTableLayout}
            keyExtractor={(item, index) => String(index)}
            numColumns={4}
          />
        </View>
        <View style={{ flex: 1 }}>
          <View
            style={{
              backgroundColor: 'white',
              alignItems: 'center',
              flexDirection: 'row',
            }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.06,
                }}>
                {/* {seatedTables} */}
                <Text
                  style={{
                    color: Colors.fieldtTxtColor,
                    fontSize: Dimensions.get('screen').width * 0.06,
                  }}>
                  /{totalTables.length}
                </Text>
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: Dimensions.get('screen').width * 0.03,
                }}>
                TABLES
              </Text>
            </View>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: Dimensions.get('screen').width * 0.06,
                }}>
                {/* {totalOccupiedSeats} */}
                <Text
                  style={{
                    color: Colors.fieldtTxtColor,
                    fontSize: Dimensions.get('screen').width * 0.06,
                  }}>
                  {/* /{totalSeats} */}
                </Text>
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: Dimensions.get('screen').width * 0.03,
                }}>
                SEATS
              </Text>
            </View>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
  },
  logo: {
    width: 300,
    height: 67,
    alignSelf: 'center',
    marginTop: 10,
  },
  logoTxt: {
    color: Colors.descriptionColor,
    fontSize: 20,
    letterSpacing: 7,
    marginTop: 10,
    alignSelf: 'center',
    marginBottom: 40,
    fontFamily: 'NunitoSans-Regular',
  },
  sectionAreaButton: {
    marginLeft: 10,
    // width: Dimensions.get('screen').width * 0.28,
    backgroundColor: Colors.whiteColor,
    height: Dimensions.get('screen').height * 0.05,
    borderRadius: 8,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',

    paddingLeft: 15,

    paddingRight: 35,

    elevation: 0,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  sectionAreaButtonTxt: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 14,
    color: Colors.primaryColor,
    textAlign: 'center',
  },
  sectionAreaFlatList: {
    height: '100%',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').width * 1,
    width: Dimensions.get('screen').width * 0.8,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('screen').width * 0.07,
    padding: Dimensions.get('screen').width * 0.07,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.04,
    top: Dimensions.get('screen').width * 0.04,
  },
  modalTitle: {
    alignItems: 'center',
  },
  modalBody: {
    flex: 0.8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    marginBottom: 10,
    fontSize: 22,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 16,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 16,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.fieldtBgColor,
    height: 45,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  tableSlotDisplay: {
    width: Dimensions.get('screen').width * 0.2,
    height: Dimensions.get('screen').width * 0.2,
    margin: 6,
    borderRadius: Dimensions.get('screen').width * 0.02,
    padding: Dimensions.get('screen').width * 0.01,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: Colors.fieldtTxtColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTableDisplay: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('screen').width * 0.2,
    height: Dimensions.get('screen').width * 0.2,
    margin: 6,
    borderRadius: Dimensions.get('screen').width * 0.02,
    padding: Dimensions.get('screen').width * 0.015,
  },
  tableCode: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 14,
  },
});
export default TableScreen;
