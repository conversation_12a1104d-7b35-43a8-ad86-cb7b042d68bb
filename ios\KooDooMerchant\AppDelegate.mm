#import <UserNotifications/UserNotifications.h>
#import <RNCPushNotificationIOS.h>

#import "AppDelegate.h"
#import <Firebase.h>

#import <React/RCTBundleURLProvider.h>

#import "Orientation.h"

#import <Foundation/Foundation.h>
#import <FirebaseCrashlytics/FirebaseCrashlytics.h>
#import <signal.h>
#import <execinfo.h>

@implementation AppDelegate

//////////////////////////////////////////////////

// 2025-03-09 - Crash capturing

// Define the handler function first
void uncaughtExceptionHandler(NSException *exception) {
  // Log to console
  NSLog(@"CRASH: %@", exception);
  NSLog(@"Stack Trace: %@", [exception callStackSymbols]);
  
  // Convert callStackSymbols to an array of frame strings
  NSArray *callStackSymbols = [exception callStackSymbols];
  
  // Report to Crashlytics
  FIRExceptionModel *model = [FIRExceptionModel exceptionModelWithName:[exception name]
                                                                reason:[exception reason]];
  [[FIRCrashlytics crashlytics] recordExceptionModel:model];
}

// For signal crashes
void SignalHandler(int signalValue) {
    NSMutableArray *callStack = [NSMutableArray array];
    void *backtraceFrames[128];
    int frameCount = backtrace(backtraceFrames, 128);
    char **frameStrings = backtrace_symbols(backtraceFrames, frameCount);
    
    if (frameStrings) {
        for (int i = 0; i < frameCount; i++) {
            [callStack addObject:[NSString stringWithUTF8String:frameStrings[i]]];
        }
        free(frameStrings);
    }
    
    NSString *reason = [NSString stringWithFormat:@"Signal %d was raised.", signalValue];
    
    // Create the exception model with the updated API
    FIRExceptionModel *model = [FIRExceptionModel exceptionModelWithName:@"Fatal Signal"
                                                                  reason:reason];
    [[FIRCrashlytics crashlytics] recordExceptionModel:model];
    
    // Re-raise the signal so the default handler can deal with it
    signal(signalValue, SIG_DFL);
    raise(signalValue);
}

// Setup method that should be called in didFinishLaunchingWithOptions
void SetupCrashHandlers(void) {
    // For Objective-C exceptions
    NSSetUncaughtExceptionHandler(&uncaughtExceptionHandler);
    
    // For signal crashes
    signal(SIGABRT, SignalHandler);
    signal(SIGILL, SignalHandler);
    signal(SIGSEGV, SignalHandler);
    signal(SIGFPE, SignalHandler);
    signal(SIGBUS, SignalHandler);
    signal(SIGPIPE, SignalHandler);
}

//////////////////////////////////////////////////

// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
 [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}
// Required for the notification event. You must call the completion handler after handling the remote notification.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}
// Required for the registrationError event.
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
 [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
}
// Required for localNotification event
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler
{
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
}

//////////////////////////////////////////////////

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  [FIRApp configure];

  self.moduleName = @"KooDooMerchant";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  self.initialProps = @{};

  // Set JSC memory limits
  NSString *jsHeapSize = @"256"; // MB
  [[NSUserDefaults standardUserDefaults] setObject:jsHeapSize forKey:@"JSCMemoryLimit"];

  // return [super application:application didFinishLaunchingWithOptions:launchOptions];

  // Define UNUserNotificationCenter
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = self;

  // Set the uncaught exception handler
  NSSetUncaughtExceptionHandler(&uncaughtExceptionHandler);
  
  // Call the crash handlers setup function
  SetupCrashHandlers();

  // return YES;
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

//Called when a notification is delivered to a foreground app.
-(void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions options))completionHandler
{
  completionHandler(UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionBadge);
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self bundleURL];
}

- (NSURL *)bundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

//////////////////////////////////////////////////

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
  return [Orientation getOrientation];
}

//////////////////////////////////////////////////

@end
