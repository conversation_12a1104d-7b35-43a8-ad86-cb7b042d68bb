import { appSchema, tableSchema } from '@nozbe/watermelondb'
import { Collections } from '../constant/firebase';

export default appSchema({
    version: 3,
    tables: [
        tableSchema({
            name: Collections.RazerPayoutTransaction,
            columns: [
                {
                    "name": "bankAccountName",
                    "type": "string"
                },

                {
                    "name": "bankAccountNumber",
                    "type": "string"
                },

                {
                    "name": "bankCode",
                    "type": "string"
                },

                {
                    "name": "bankType",
                    "type": "string"
                },

                {
                    "name": "bqDt",
                    "type": "string"
                },

                {
                    "name": "clientEmail",
                    "type": "string"
                },

                {
                    "name": "clientId",
                    "type": "string"
                },

                {
                    "name": "clientName",
                    "type": "string"
                },

                {
                    "name": "clientPhone",
                    "type": "string"
                },

                {
                    "name": "companyName",
                    "type": "string"
                },

                {
                    "name": "contactEmail",
                    "type": "string"
                },

                {
                    "name": "contactMobile",
                    "type": "string"
                },

                {
                    "name": "country",
                    "type": "string"
                },

                {
                    "name": "createdAt",
                    "type": "number"
                },

                {
                    "name": "deletedAt",
                    "type": "number"
                },

                {
                    "name": "isSettledOnSameDay",
                    "type": "boolean"
                },

                {
                    "name": "merchantId",
                    "type": "string"
                },

                {
                    "name": "merchantLogo",
                    "type": "string"
                },

                {
                    "name": "merchantName",
                    "type": "string"
                },

                {
                    "name": "outletCover",
                    "type": "string"
                },

                {
                    "name": "outletCycleFunds",
                    "type": "number"
                },

                {
                    "name": "outletCycleKoodooPayoutsActual",
                    "type": "number"
                },

                {
                    "name": "outletCycleKoodooPayoutsExpected",
                    "type": "number"
                },

                {
                    "name": "outletCycleMerchantOverdueAmounts",
                    "type": "number"
                },

                {
                    "name": "outletCycleMerchantPayoutsActual",
                    "type": "number"
                },

                {
                    "name": "outletCycleMerchantPayoutsExpected",
                    "type": "number"
                },

                {
                    "name": "outletCycleMerchantPendingAmounts",
                    "type": "number"
                },

                {
                    "name": "outletCycleMerchantPendingRefundOrdersAmount",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "outletCycleMerchantRefundOrdersAmount",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "outletCycleMerchantToReturnByKoodooFeeAmount",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "outletCycleRazerPayouts",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "outletId",
                    "type": "string"
                },

                {
                    "name": "outletName",
                    "type": "string"
                },

                {
                    "name": "overdueAmountBackup",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "payeeID",
                    "type": "number"
                },

                {
                    "name": "payoutFee",
                    "type": "number"
                },

                {
                    "name": "picFullName",
                    "type": "string"
                },

                {
                    "name": "picNRICPassport",
                    "type": "string"
                },

                {
                    "name": "prevOverdueAmount",
                    "type": "number"
                },

                {
                    "name": "prevPendingAmount",
                    "type": "number"
                },

                {
                    "name": "prevStockUpAmount",
                    "type": "number"
                },

                {
                    "name": "processingRate",
                    "type": "number"
                },

                {
                    "name": "razerMerchantMassId",
                    "type": "number"
                },

                {
                    "name": "razerMerchantReferenceId",
                    "type": "string"
                },

                {
                    "name": "remarks",
                    "type": "string"
                },

                {
                    "name": "startCreatedAt",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "stockUpAmount",
                    "type": "number"
                },

                {
                    "name": "stockUpAmountBackup",
                    "type": "number",
                    "isOptional": true,
                },

                {
                    "name": "uniqueId",
                    "type": "string"
                },

                {
                    "name": "updatedAt",
                    "type": "number"
                },

                {
                    "name": "userOrdersFigures",
                    "type": "string",
                    "isOptional": true,
                },

                {
                    "name": "v",
                    "type": "string",
                    "isOptional": true,
                }
            ]
        }),
    ]
});
