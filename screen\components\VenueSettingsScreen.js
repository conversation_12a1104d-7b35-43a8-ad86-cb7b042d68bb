import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../../constant/Colors';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../../constant/API';
import * as User from '../../util/User';
import * as Cart from '../../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../../util/common';
import { MerchantStore } from '../../store/merchantStore';
import { OutletStore } from '../../store/outletStore';
import { CommonStore } from '../../store/commonStore';
import { UserStore } from '../../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
} from '../../constant/common';
import { useKeyboard } from '../../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../../util/common';
import { qrUrl } from '../../constant/env';
import { printUserOrder } from '../../util/printer';
import { Collections } from '../../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';

const VenueSettingScreen = (props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // show use state
  const [showVenueMain, setShowVenueMain] = useState(false);

  // switch use state

  // dropdownpicker use state

  //useEffect start

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
      }}>
      <View
        style={{
          height: windowHeight * 0.064,
          backgroundColor: '#ffffff',
          width: '100%',
          justifyContent: 'center',
        }}>
        <Text
          style={{
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            fontSize: switchMerchant ? 20 : 26,
          }}>
          Venue Settings
        </Text>
      </View>
      <View
        style={{
          paddingTop: windowHeight * 0.027,
          paddingHorizontal: 20,
        }}>
        {/* <View>
          <TouchableOpacity
            onPress={() => {
              // store into asyncStorage
              CommonStore.update((s) => {
                s.venueSettingPage = 'room';
              });
              // navigate to venue setting page
              props.navigation.navigate('VenueSettingsModalScreen');
            }}
            style={{
              paddingHorizontal: windowWidth * 0.01,
              borderBottomWidth: 1,
              borderBottomColor: '#EBEDEF',
              width: '100%',
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              backgroundColor: '#ffffff',
              paddingVertical: windowHeight * 0.02,
            }}>
            <View
              style={{
                // borderWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Room setup
              </Text>
              <Plus
                name="chevron-right"
                size={20}
                color={Colors.darkBgColor}
                style={{}}
              />
            </View>
          </TouchableOpacity>
        </View> */}
        <View>
          <TouchableOpacity
            onPress={() => {
              // CommonStore.update((s) => {
              //   s.venueSettingPage = 'table';
              // });
              props.navigation.navigate('VenueSettingsTableSetupScreen');
            }}
            style={{
              paddingHorizontal: windowWidth * 0.01,
              borderBottomWidth: 1,
              borderBottomColor: '#EBEDEF',
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              width: '100%',
              backgroundColor: '#ffffff',
              paddingVertical: windowHeight * 0.02,
            }}>
            <View
              style={{
                // borderWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Table setup
              </Text>
              <Plus
                name="chevron-right"
                size={20}
                color={Colors.darkBgColor}
                style={{}}
              />
            </View>
          </TouchableOpacity>
        </View>
        <View>
          <TouchableOpacity
            onPress={() => {
              // CommonStore.update((s) => {
              //   s.venueSettingPage = 'tableCombinations';
              // });
              props.navigation.navigate('VenueSettingsCombinationScreen');
            }}
            style={{
              paddingHorizontal: windowWidth * 0.01,
              borderBottomWidth: 1,
              borderBottomColor: '#EBEDEF',
              width: '100%',
              backgroundColor: '#ffffff',
              paddingVertical: windowHeight * 0.02,
            }}>
            <View
              style={{
                // borderWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Table Combinations
              </Text>
              <Plus
                name="chevron-right"
                size={20}
                color={Colors.darkBgColor}
                style={{}}
              />
            </View>
          </TouchableOpacity>
        </View>
        <View>
          <TouchableOpacity
            onPress={() => {
              // CommonStore.update((s) => {
              //   s.venueSettingPage = 'reservation';
              // });
              props.navigation.navigate('VenueSettingsReservationScreen');
            }}
            style={{
              paddingHorizontal: windowWidth * 0.01,
              borderBottomWidth: 1,
              borderBottomColor: '#EBEDEF',
              borderBottomLeftRadius: 10,
              borderBottomRightRadius: 10,
              width: '100%',
              backgroundColor: '#ffffff',
              paddingVertical: windowHeight * 0.02,
            }}>
            <View
              style={{
                // borderWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Reservation Availability
              </Text>
              <Plus
                name="chevron-right"
                size={20}
                color={Colors.darkBgColor}
                style={{}}
              />
            </View>
          </TouchableOpacity>
        </View>
        {/* <View>
          <TouchableOpacity
            onPress={() => {
              CommonStore.update((s) => {
                s.venueSettingPage = 'floor';
              });
              props.navigation.navigate('VenueSettingsModalScreen');
            }}
            style={{
              paddingHorizontal: windowWidth * 0.01,
              width: '100%',
              borderBottomColor: '#EBEDEF',
              borderBottomLeftRadius: 10,
              borderBottomRightRadius: 10,
              backgroundColor: '#ffffff',
              paddingVertical: windowHeight * 0.02,
            }}>
            <View
              style={{
                // borderWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Floor Management
              </Text>
              <Plus
                name="chevron-right"
                size={20}
                color={Colors.darkBgColor}
                style={{}}
              />
            </View>
          </TouchableOpacity>
        </View> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#EBEDEF',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default VenueSettingScreen;
