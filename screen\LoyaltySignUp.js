import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import AntDesign from 'react-native-vector-icons/AntDesign';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import GCalendar from '../assets/svg/GCalendar'
import {
  isTablet
} from '../util/common';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import { EXPAND_TAB_TYPE, } from '../constant/common';

const LoyaltySignUp = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: 'center',
            // alignItems: 'center',
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
          }}>
          Loyalty Sign Up
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [birthday, setBirthday] = useState(
    moment(),
  );
  const [address, setAddress] = useState('');
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const [showDOBPicker, setShowDOBPicker] = useState(false); //For DOB

  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: "column" }}>
        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}
    >
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: windowWidth * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={6}
          expandCRM
        />
      </View> */}
      <ScrollView
      // horizontal={true}
      >
        <View style={{ marginLeft: 10, alignItems: 'center', justifyContent: 'center', }}>
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 10 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                First Name:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end' }}>
              <TextInput
                value={firstName}
                onChangeText={(text) => { setFirstName(text) }}
                style={[styles.textInput,]}
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode="while-editing"
                placeholder='First Name'
              />
            </View>
          </View>
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Last Name:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end' }}>
              <TextInput
                value={lastName}
                onChangeText={(text) => { setLastName(text) }}
                style={[styles.textInput,]}
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode="while-editing"
                placeholder='Last Name'
              />
            </View>
          </View>
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Phone:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end' }}>
              <TextInput
                value={phone}
                onChangeText={(text) => { setPhone(text) }}
                style={[styles.textInput,]}
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode="while-editing"
                placeholder='Phone Number'
                keyboardType='numeric'
              />
            </View>
          </View>
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Birthday:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end' }}>
              <TouchableOpacity
                style={[styles.textInput, { justifyContent: 'center' }]}
                onPress={() => {
                  setShowDOBPicker(true);
                }}
              >
                <View style={{ flexDirection: 'row' }}>
                  <GCalendar
                    width={switchMerchant ? 10 : 20}
                    height={switchMerchant ? 10 : 20}
                    style={{ marginRight: 5 }}
                  />
                  <Text
                    style={{
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    {moment(birthday).format('DD MMM yyyy')}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Address:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end' }}>
              <GooglePlacesAutocomplete
                placeholder={address !== '' ? address : '📍 Type your address here'}
                placeholderTextColor={Platform.select({
                  ios: '#a9a9a9',
                })}
                minLength={2} // minimum length of text to search
                autoFocus={false}
                returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                listViewDisplayed="false" // true/false/undefined
                fetchDetails
                renderDescription={(row) => renderSearch(row)} // custom description render
                onPress={(data, details = null) => {
                  // 'details' is provided when fetchDetails = true
                  // console.log("data", data);
                  // // console.log("details", details);
                  // // console.log("data.description", data.description);
                  // // console.log("details.geometry.location", details.geometry.location);
                  setAddress(details.formatted_address)
                }}
                getDefaultValue={() => address}
                query={{
                  // available options: https://developers.google.com/places/web-service/autocomplete
                  //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
                  // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
                  // key: 'AIzaSyABX4LTqTLQGg_b3jFOH8Z6_H5CDqn8tbc',
                  key: 'AIzaSyAcXc3dCqZwxETpxBaw-4lTBxS482FvhwU',
                  language: 'en', // language of the results
                  types: 'address', // default: 'geocode'
                  components: 'country:my',
                }}
                styles={{
                  textInputContainer: {
                    width: windowWidth * 0.7,
                    borderWidth: 1,
                    borderRadius: 5,
                    borderColor: '#E5E5E5',
                    //alignSelf: 'center',
                    //marginTop: 20,
                  },
                  textInput: {
                    backgroundColor: Colors.fieldtBgColor,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                    height: switchMerchant ? 35 : 40,
                    marginBottom: 0,
                    // backgroundColor: 'red',
                  },
                  description: {
                    // fontWeight: 'bold',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 14,
                  },
                  predefinedPlacesDescription: {
                    color: '#1faadb',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  },
                  listView: {
                    //backgroundColor: 'red',
                    width: windowWidth * 0.7,
                    borderColor: '#E5E5E5',
                    borderWidth: 1,
                    borderRadius: 5,
                    // height: 800,
                  },
                  container: {
                    //backgroundColor: 'red',
                  },
                }}
                enablePoweredByContainer={false}
                //currentLocation={false}
                currentLocationLabel="Current location"
                nearbyPlacesAPI="GooglePlacesSearch"
                GoogleReverseGeocodingQuery={{}}
                GooglePlacesSearchQuery={{
                  rankby: 'distance',
                }}
                filterReverseGeocodingByTypes={[
                  'locality',
                  'administrative_area_level_3',
                ]}
                debounce={200}
                keepResultsAfterBlur
              />
            </View>
          </View>
          <View style={{ flex: 1 }}>
            <TouchableOpacity
              onPress={() => { }}
              style={{
                marginTop: 30,
                backgroundColor:
                  Colors.primaryColor,
                width: windowWidth * 0.08,
                height: windowHeight * 0.08,
                // height: 60,
                alignSelf: "center",
                alignItems: 'center',
                borderRadius: 10,
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  alignSelf: 'center',
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 14
                }}
              >
                Sign Up
              </Text>
            </TouchableOpacity>
          </View>
          <DateTimePickerModal
            isVisible={showDOBPicker}
            mode={'date'}
            onConfirm={(date) => {
              setBirthday(moment(date).startOf('day'));

              setShowDOBPicker(false);
            }}
            onCancel={() => {
              setShowDOBPicker(false);
            }}
          />
        </View>
      </ScrollView>
    </View>

  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    // width: Dimensions.get('window').width * 0.85,
    // height: windowHeight,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: Dimensions.get('window').width * 0.7,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: 200,
    width: 500,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
});

export default LoyaltySignUp;