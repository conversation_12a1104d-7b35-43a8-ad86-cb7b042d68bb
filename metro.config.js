/* Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const {
  resolver: { sourceExts, assetExts },
} = getDefaultConfig(__dirname);

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  resolver: {
    assetExts: assetExts.filter(ext => ext !== 'svg'),
    sourceExts: [...sourceExts, 'svg'],
  },
};

module.exports = mergeConfig(defaultConfig, config);

///////////////////////////////////////////////////////////////

// const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

// /**
//  * Metro configuration
//  * https://reactnative.dev/docs/metro
//  *
//  * @type {import('metro-config').MetroConfig}
//  */
// const config = {};

// // module.exports = mergeConfig(getDefaultConfig(__dirname), config);

// module.exports = (async () => {
//     const {
//         resolver: {
//             sourceExts,
//             assetExts
//         }
//     } = await getDefaultConfig();

//     return {
//         transformer: {
//             // babelTransformerPath: require.resolve("react-native-svg-transformer"),
//             // experimentalImportSupport: false,
//             // inlineRequires: false,
//             babelTransformerPath: require.resolve('react-native-svg-transformer'),
//             getTransformOptions: async () => ({
//                 transform: {
//                     experimentalImportSupport: false,
//                     inlineRequires: false,
//                 },
//             }),
//         },
//         resolver: {
//             assetExts: assetExts.filter(ext => ext !== "svg"),
//             sourceExts: [...sourceExts, "svg", 'jsx', 'js', 'ts', 'tsx']
//         }
//     };
// })();

///////////////////////////////////////////////////////////////
