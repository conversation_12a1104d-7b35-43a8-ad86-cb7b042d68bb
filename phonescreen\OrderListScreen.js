import React, { Component, useState, useEffect } from 'react'
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, Dimensions } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'
import { FlatList, Swipeable } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import TableBar from './components/TableBar';
import CheckBox from '@react-native-community/checkbox';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { ORDER_TYPE, USER_ORDER_STATUS } from '../constant/common';
import moment from 'moment';

const OrderListScreen = props => {
  const {
    navigation,
  } = props;

  const [orderId, setOrderId] = useState('');
  const [param, setParam] = useState(1);
  const [showItem, setShowItem] = useState({});
  const [count, setCount] = useState('');
  const [value, setValue] = useState([]);
  const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});
  const [item, setItem] = useState({});
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);

  const setState = () => { };

  const [dineInOrders, setDineInOrders] = useState([]);

  const userOrders = OutletStore.useState(s => s.userOrders);
  const userOrdersDict = OutletStore.useState(s => s.userOrdersDict);
  const userOrdersTableDict = OutletStore.useState(s => s.userOrdersTableDict);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const outletTablesDict = OutletStore.useState(s => s.outletTablesDict);

  // testing start here
  const [selectedOrderItemCheckDict, setSelectedOrderItemCheckDict] = useState({});
  const [selectedOrderItemCancelledCheckDict, setSelectedOrderItemCancelledCheckDict] = useState({});
  const [selectedOrder, setSelectedOrder] = useState({});
  const outletTables = OutletStore.useState(s => s.outletTables);
  const [outletTablesActive, setOutletTablesActive] = useState([]);
  const [selectedOrderItemList, setSelectedOrderItemList] = useState([]);
  const [selectedOrderItemCancelledList, setSelectedOrderItemCancelledList] = useState([]);


  useEffect(() => {
    // var dineInOrdersTemp = [];

    var userOrdersTemp = userOrders.filter(order => order.orderType === ORDER_TYPE.DINEIN &&
      (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING));

    userOrdersTemp.sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder);

    setDineInOrders(userOrdersTemp);
  }, [userOrders]);

  // Testing useEffect
  useEffect(() => {
    var tempOutletTablesActive = [];

    for (var i = 0; i < outletTables.length; i++) {
      if (userOrdersTableDict[outletTables[i].uniqueId] !== undefined) {
        var userOrdersItemNum = 0;

        for (var j = 0; j < userOrdersTableDict[outletTables[i].uniqueId].length; j++) {
          if (userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
            userOrdersTableDict[outletTables[i].uniqueId][j].orderStatus === USER_ORDER_STATUS.ORDER_PREPARING) {
            for (var k = 0; k < userOrdersTableDict[outletTables[i].uniqueId][j].cartItems.length; k++) {
              userOrdersItemNum += userOrdersTableDict[outletTables[i].uniqueId][j].cartItems[k].quantity;
            }
          }
        }

        if (userOrdersItemNum > 0) {
          // push to active table list, if the table had active orders

          tempOutletTablesActive.push({
            outletTableId: outletTables[i].uniqueId,
            userOrdersItemNum: userOrdersItemNum,
          });
        }
      }
    }

    tempOutletTablesActive.sort((a, b) => (a.userOrdersItemNum > b.userOrdersItemNum) ? -1 : 1);

    setOutletTablesActive(tempOutletTablesActive);

    console.log('tempOutletTablesActive');
    console.log(tempOutletTablesActive);

    if (selectedOrder && selectedOrder.tableId && selectedOrder.cartItems && selectedOrder.cartItems.length > 0 &&
      userOrdersTableDict[selectedOrder.tableId] && userOrdersTableDict[selectedOrder.tableId].length > 0) {
      // update the check list based on changes from db

      var tempSelectedOrderItemList = [];
      var tempSelectedOrderItemCancelledList = [];

      if (userOrdersTableDict[selectedOrder.tableId] !== undefined) {
        var userOrdersItemNum = 0;

        for (var j = 0; j < userOrdersTableDict[selectedOrder.tableId].length; j++) {
          for (var k = 0; k < userOrdersTableDict[selectedOrder.tableId][j].cartItems.length; k++) {
            tempSelectedOrderItemList.push(userOrdersTableDict[selectedOrder.tableId][j].cartItems[k]);
          }

          if (userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled) {
            for (var k = 0; k < userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled.length; k++) {
              tempSelectedOrderItemCancelledList.push(userOrdersTableDict[selectedOrder.tableId][j].cartItemsCancelled[k]);
            }
          }
        }
      }

      setSelectedOrderItemList(tempSelectedOrderItemList);
      setSelectedOrderItemCancelledList(tempSelectedOrderItemCancelledList);
      setSelectedOrder(userOrdersTableDict[selectedOrder.tableId][0]);
    }
    else {
      setSelectedOrderItemList([]);
      setSelectedOrderItemCancelledList([]);
      setSelectedOrder({});
    }
  }, [outletTables, userOrdersTableDict]);

  const cancelUserOrder = () => {
    var body = {
      orderItemList: Object.entries(selectedOrderItemCheckDict).map(([key, value]) => value),
      orderId: selectedOrder.uniqueId,
    };

    ApiClient.POST(API.cancelUserOrderItem, body).then(result => {
      if (result && result.status === 'success') {
        Alert.alert('Success', 'Cancel successfully');

        if (result.data.length <= 0) {
          setShowItem({});
        }
      }
      else {
        Alert.alert('Error', 'Failed to cancel');
      }
    }).catch(err => Alert.alert('Error', 'Something went wrong'));
  };

  const undoUserOrderCancelled = () => {
    var body = {
      orderItemList: Object.entries(selectedOrderItemCancelledCheckDict).map(([key, value]) => value),
      orderId: selectedOrder.uniqueId,
    };

    ApiClient.POST(API.undoUserOrderItemCancelled, body).then(result => {
      if (result && result.status === 'success') {
        Alert.alert('Success', 'Undo successfully');

        if (result.data.length <= 0) {
          setShowItem({});
        }
      }
      else {
        Alert.alert('Error', 'Failed to cancel');
      }
    }).catch(err => Alert.alert('Error', 'Something went wrong'));
  };

  //end here

  // useEffect(() => {
  //   setTimeout(() => {
  //     setRefreshRate(new Date());

  //     checkOvertimeOrders();
  //   }, 30000);

  //   checkOvertimeOrders();
  // }, [refreshRate]);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={() => { props.navigation.goBack(); }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: Platform.OS == 'android' ? 9 : 10,
            opacity: 0.8,
          }}>
          <Icon
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              //marginTop: -3,
              marginBottom: Platform.OS == 'android' ? 2 : 0,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -2,
      }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          Order
        </Text>
      </View>
    ),
    headerRight: () => (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => { props.navigation.navigate('Profile') }}>
          <Image style={{
            width: 32,
            height: 32,
            marginTop: 8,
            marginRight: 25,
          }} source={require('../assets/image/drawer.png')} />
        </TouchableOpacity>
      </View>
    ),
  });

  // To refresh
  // componentDidMount = () => {
  //   getOrderHistory();
  //   setInterval(() => { getOrderHistory() }, 5000)
  // }

  const getOrderHistory = () => {
    ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(result => {
      const newList = []
      for (const order of result) {
        //console.log("ORDER",order.type)
        if (order.type == "Dine In") {
          newList.push(order)
        }
      }
      setState({ orderId: newList, count: newList[0].orderItems.length })
      // CommonStore.update(s => {
      //     s.orderTables = result;
      // });
    }).catch(err => { console.log(err) })
  }

  const showOrderItem = (item) => {
    if (item.uniqueId !== showItem.uniqueId)
      setShowItem(item);
    else
      setShowItem({});
  }
  //comment first
  // const checkOrderItem = (item, orderItem) => {
  //   var body = {
  //     itemId: item.itemId,
  //     cartItemDate: item.cartItemDate,
  //     orderId: orderItem.uniqueId,
  //   };

  //   ApiClient.POST(API.orderDeliver, body).then(result => {
  //     // if (result === true)
  //     // getOrderHistory()
  //     //console.log('getOrderHistory');
  //     //console.log(getOrderHistory);
  //   }).catch(err => Alert('Error', 'Something went wrong'))
  //   //.catch(err => {console.log(err)})
  // }


  const uncheck = (item, orderItem) => {
    var body = {
      itemId: item.itemId,
      cartItemDate: item.cartItemDate,
      orderId: orderItem.uniqueId,
    };

    ApiClient.POST(API.orderDeliverUndo, body).then(result => {
      // if (result === true)
      // getOrderHistory()
      //console.log('getOrderHistory');
      //console.log(getOrderHistory);
    }).catch(err => Alert('Error', 'Something went wrong'))
    //.catch(err => {console.log(err)})
  }

  // start here (uncheckOrderItem)
  const uncheckOrderItem = (item, orderItem) => {
    console.log('uncheck!');

    setSelectedOrderItemCheckDict({
      ...selectedOrderItemCheckDict,
      [item.itemId + item.cartItemDate.toString()]: false,
    });
  }

  const checkOrderItem = (item, orderItem) => {
    console.log('check!');

    setSelectedOrderItemCancelledCheckDict({});

    setSelectedOrderItemCheckDict({
      ...selectedOrderItemCheckDict,
      [item.itemId + item.cartItemDate + orderId.toString()]: {
        itemId: item.itemId,
        cartItemDate: item.cartItemDate,
        orderId: orderItem.uniqueId,
      },
    });
  }

  const uncheckOrderItemCancelled = (item, orderItem) => {
    console.log('uncheck!');

    setSelectedOrderItemCancelledCheckDict({
      ...selectedOrderItemCancelledCheckDict,
      [item.itemId + item.cartItemDate.toString()]: false,
    });
  }

  const checkOrderItemCancelled = (item, orderItem) => {
    console.log('check!');
    setSelectedOrderItemCheckDict({});

    setSelectedOrderItemCancelledCheckDict({
      ...selectedOrderItemCancelledCheckDict,
      [item.itemId + item.cartItemDate.toString()]: {
        itemId: item.itemId,
        cartItemDate: item.cartItemDate,
      },
    });
  }

  //end here


  const renderOrderItems = ({ item }, orderItem) => {
    // var userOrdersItemNum = 0;

    // for (var i = 0; i < item.cartItems.length; k++) {
    //   userOrdersItemNum += item.cartItems[i].quantity;
    // }

    return (
      // <View style={styles.bottomPart}>
      //   <View style={[styles.topPart, { flex: 0.5 },]}>
      //     <CheckBox value={item.isChecked} onValueChange={() => {
      //       item.isChecked ? uncheck(item.id) : checkOrderItem(item.id)
      //     }} />
      //   </View>
      //   <View style={[styles.topPart, { flex: 1, alignItems: 'center', backgroundColor: 'red', width: 200 }]}>
      //     <Text style={{ fontFamily: 'NunitoSans-Bold', width: '100%' }}>{item.name}</Text>
      //     {item.remarks && item.remarks.length > 0 ? <Text style={{ fontSize: 15, color: 'grey' }}>{item.remarks}</Text> : <></>}
      //   </View>
      //   <View style={styles.topPart}><Text style={{ color: Colors.fieldtBgColor }}>x{item.quantity}</Text></View>
      //   <View style={styles.topPart}><Text style={{ fontFamily: 'NunitoSans-Bold' }}>RM{item.price}</Text></View>
      // </View>

      <View>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          // backgroundColor: 'red',
          marginBottom: 5,
          marginTop: 5,
          marginLeft: 10,
          marginRight: 15,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'blue',
          }}>
            {/* <CheckBox style={{ transform:[{ scaleX: 0.8 },{scaleY: 0.8 }], marginTop: 3 }}
              value={item.isChecked} onValueChange={() => {
              item.isChecked ? uncheck(item, orderItem) : checkOrderItem(item, orderItem)
            }} /> */}

            <CheckBox value={selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()] !== false && selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
              if (selectedOrderItemCheckDict[item.itemId + item.cartItemDate.toString()]) {
                uncheckOrderItem(item, orderItem);
              }
              else {
                checkOrderItem(item, orderItem);
              }
            }} />

            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15, marginLeft: 5, width: '80%' }}>{item.name}</Text>

            <Text style={{ color: '#747474', fontFamily: 'NunitoSans-Bold', marginLeft: 10, }}>x{item.quantity}</Text>
          </View>

        </View>

        {item.remarks && item.remarks.length > 0 ?
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'red',
            // marginBottom: 5,
            // marginTop: 5,
            marginLeft: 10,
            marginRight: 15,
            marginTop: -15,
            marginBottom: 5,
          }}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              // backgroundColor: 'blue',
            }}>

              <CheckBox style={{
                opacity: 0,
              }}
              />
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 14, marginLeft: 5 }}>{item.remarks}</Text>
            </View>
          </View>
          : <></>
        }

        {item.addOns.map((addOnChoice, i) => {
          return (
            <View
              style={{
                marginTop: item.remarks && item.remarks.length > 0 ? -8 : -10,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                marginLeft: 15,
                marginRight: 15,
                marginTop: -15,
                marginBottom: 5,
              }}>
              <CheckBox style={{
                opacity: 0,
                color: 'red',
              }} />
              <View
                style={{
                  width: '70%',
                  flexDirection: 'row',
                }}>

                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      width: '35%',
                      //backgroundColor: 'red',
                      // marginLeft: 5,
                    }
                  ]}>
                  {`${addOnChoice.name}:`}
                </Text>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      width: '65%',
                      //backgroundColor: 'green',
                      // marginLeft: 5,
                    }
                  ]}>
                  {`${addOnChoice.choiceNames[0]}`}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  //backgroundColor: 'blue',
                  width: '15%',
                }}>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      // width: '20%',
                      textAlign: 'center',
                    }
                  ]}>
                  {`${addOnChoice.quantities
                    ? `x${addOnChoice.quantities[0]}`
                    : ''
                    }`}
                </Text>
              </View>
              {/* <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '17%',
                  alignItems: 'center',
                  marginHorizontal: 1,
                }}>
                <Text
                  style={{ fontSize: 13, color: Colors.fieldtTxtColor, }}>
                  RM
                </Text>
                <Text
                  style={{ fontSize: 13, color: Colors.fieldtTxtColor, }}>
                  {addOnChoice.prices[0]
                    .toFixed(2)
                    .replace(
                      /(\d)(?=(\d{3})+(?!\d))/g,
                      '$1,',
                    )}
                </Text>
              </View> */}
            </View>
          );
        })}
      </View>
    );
  };

  //start here
  const renderOrderItemsCancelled = (item, orderItem) => {

    return (
      <View>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          // backgroundColor: 'red',
          marginBottom: 5,
          marginTop: 5,
          marginLeft: 10,
          marginRight: 15,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'blue',
          }}>


            <CheckBox value={selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()] !== false && selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()] !== undefined} onValueChange={(value) => {
              if (selectedOrderItemCancelledCheckDict[item.itemId + item.cartItemDate.toString()]) {
                uncheckOrderItemCancelled(item, orderItem);
              }
              else {
                checkOrderItemCancelled(item, orderItem);
              }
            }} />

            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15, marginLeft: 5, }}>{item.name}</Text>

            <Text style={{ color: '#747474', fontFamily: 'NunitoSans-Bold', marginLeft: 10, }}>x{item.quantity}</Text>
          </View>

        </View>

        {item.remarks && item.remarks.length > 0 ?
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'red',
            // marginBottom: 5,
            // marginTop: 5,
            marginLeft: 10,
            marginRight: 15,
            marginTop: -15,
            marginBottom: 5,
          }}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              // backgroundColor: 'blue',
            }}>

              <CheckBox style={{
                opacity: 0,
              }} />
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 14, marginLeft: 5 }}>{item.remarks}</Text>
            </View>
          </View>
          : <></>
        }
        {item.addOns.map((addOnChoice, i) => {
          return (
            <View
              style={{
                marginTop: item.remarks && item.remarks.length > 0 ? -8 : -10,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                marginLeft: 15,
                marginRight: 15,
                marginTop: -15,
                marginBottom: 5,
              }}>
              <CheckBox style={{
                opacity: 0,
                color: 'red',
              }} />
              <View
                style={{
                  width: '70%',
                  flexDirection: 'row',
                }}>

                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      width: '35%',
                      //backgroundColor: 'red',
                      // marginLeft: 5,
                    }
                  ]}>
                  {`${addOnChoice.name}:`}
                </Text>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      width: '65%',
                      //backgroundColor: 'green',
                      // marginLeft: 5,
                    }
                  ]}>
                  {`${addOnChoice.choiceNames[0]}`}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  //backgroundColor: 'blue',
                  width: '15%',
                }}>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: 13,
                      color: Colors.fieldtTxtColor,
                      // width: '20%',
                      textAlign: 'center',
                    }
                  ]}>
                  {`${addOnChoice.quantities
                    ? `x${addOnChoice.quantities[0]}`
                    : ''
                    }`}
                </Text>
              </View>
              {/* <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '17%',
                  alignItems: 'center',
                  marginHorizontal: 1,
                }}>
                <Text
                  style={{ fontSize: 13, color: Colors.fieldtTxtColor, }}>
                  RM
                </Text>
                <Text
                  style={{ fontSize: 13, color: Colors.fieldtTxtColor, }}>
                  {addOnChoice.prices[0]
                    .toFixed(2)
                    .replace(
                      /(\d)(?=(\d{3})+(?!\d))/g,
                      '$1,',
                    )}
                </Text>
              </View> */}
            </View>
          );
        })}
      </View>


    );
  }

  const checkOvertimeOrders = async () => {
    for (var i = 0; i < dineInOrders.length; i++) {
      const waitingTime = ((moment().valueOf() - dineInOrders[i].estimatedPreparedDate) / (1000 * 60));

      if (waitingTime >= 300) {
        // await cancelOrder(dineInOrders[i], false);
      }
    }
  };

  const cancelOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      tableId: param.tableId,
    };
    ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then((result) => {
      if (result && result.status === 'success') {
        if (showAlert) {
          Alert.alert(
            'Success',
            'The order has been cancelled',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setModalCancelVisibility(false);
        }
      } else {
        if (showAlert) {
          Alert.alert(
            "Failed",
            'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setModalCancelVisibility(false);
        }
      }
    });
  }

  const prioritizeOrder = (param) => {
    var body = {
      orderId: param
    };
    ApiClient.POST(API.prioritizeOrder, body, false).then((result) => {
      if (result !== null) {
        Alert.alert(
          'Success',
          'The order has been prioritized',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        // setState({ visible: false, visible1: false });
        setVisible1(false);
      } else {
        Alert.alert(
          "Failed",
          'Your request has failed',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        // setState({ visible: false, visible1: false });
        setVisible1(false);
      }
    });
  }

  const rightAction = (item) => {
    return (
      <TouchableOpacity
        style={{
          height: "100%",
          width: "20%",
          pointerEvents: 'box-none',
          justifyContent: 'center',
          alignItems: 'center',
          alignContent: 'center',
          alignSelf: "center",
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          borderTopRightRadius: 10,
          borderBottomRightRadius: 10,
        }}
        onPress={() => {
          // item.customTable == "TAKE AWAY" ?
          //   setState({
          //     currToPrioritizeOrder: item,
          //     visible: true
          //   }) :
          //   setState({
          //     currToPrioritizeOrder: item,
          //     visible1: true
          //   });

          // setCurrToPrioritizeOrder(item);
          // setVisible1(true);

          prioritizeOrder(item.uniqueId);
        }}
      >
        <MaterialCommunityIcons
          name="message-alert-outline"
          size={30}
          color={Colors.whiteColor}
          style={{ marginTop: 10 }}
        />
        <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{item.customTable == "TAKE AWAY" ? "Prioritize Takeaway" : "Prioritize Order"}</Text>
      </TouchableOpacity>
    )
  };

  var orderIdFont = 14;

  if (Dimensions.get('screen').width <= 360) {
    orderIdFont = 12;
    //console.log(Dimensions.get('screen').width)
  }

  const orderIdTextScale = {
    fontSize: orderIdFont,
  };

  var orderIdFont = 12;

  if (Dimensions.get('screen').width <= 360) {
    orderIdFont = 11;
    //console.log(Dimensions.get('screen').width)
  }

  const orderTimeTextScale = {
    fontSize: orderIdFont,
  };

  var orderPriceFont = 14;

  if (Dimensions.get('screen').width <= 360) {
    orderPriceFont = 12;
    //console.log(Dimensions.get('screen').width)
  }

  const orderPriceTextScale = {
    fontSize: orderPriceFont,
  };

  const renderOrder = ({ item }) => {
    const orderEstimatedTime = ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60));

    return (
      <View style={[styles.insideFlat, {
        borderWidth: 1,
        // borderColor: item.estimateTime > 20 ? Colors.tabRed : (item.estimateTime < 20 && item.estimateTime >= 15) ? Colors.tabYellow : Colors.tabGrey
      }]}>
        <Swipeable
          renderRightActions={() => rightAction(item)}
        >
          <View style={{
            flexDirection: 'row',
            padding: 10,
            // paddingLeft: 10, 
            // paddingTop: 10, 
            // paddingBottom: 10, 
            alignSelf: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#E5E4E2'
          }}>
            <View style={[styles.topPart, {}]}>
              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15 }}>
                Table
              </Text>
              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15, paddingLeft: 3 }}>
                {/* {!item.table ? null : item.table.code} */}
                {outletTablesDict[item.tableId].code}
                {/* {'Table'} */}
              </Text>
            </View>
            <View style={[styles.topPart, { flex: 2, alignItems: 'center', justifyContent: 'center', }]}>
              <Text ellipsizeMode='tail' numberOfLines={1} style={[orderIdTextScale, { fontFamily: 'NunitoSans-SemiBold', }]}>Order #{item.orderId}</Text>
              {orderEstimatedTime < 300 ?
                <Text style={[orderTimeTextScale, { fontFamily: 'NunitoSans-SemiBold' }]}>Waiting time :
                  {/* <Text style={{ fontFamily: 'NunitoSans-Bold' }}>{item.estimateTime}mins</Text> */}
                  {` ${orderEstimatedTime < 0 ? 0 : orderEstimatedTime.toFixed(0)}mins`}
                </Text>
                :
                <Text style={{ fontSize: 12, fontFamily: 'NunitoSans-SemiBold', color: Colors.tabRed }}>
                  {/* <Text style={{ fontFamily: 'NunitoSans-Bold' }}>{item.estimateTime}mins</Text> */}
                  {'Over Time Order'}
                </Text>
              }
            </View>
            <View style={{
              justifyContent: 'center',
              bottom: 1,
              marginRight: '2.5%',
            }}>
              <View style={styles.topPart}>
                <Text style={[orderPriceTextScale, { color: Colors.primaryColor, fontFamily: 'NunitoSans-Bold' }]}>RM{item.totalPrice.toFixed(2)}</Text>
              </View>
            </View>

            <View style={{
              opacity: item.isPrioritizedOrder ? 100 : 0,
              justifyContent: 'center',
              bottom: 1,
              marginRight: '2%',
            }}>
              <FontAwesome name={'star'} size={18} color={Colors.primaryColor} />
            </View>

            <TouchableOpacity onPress={() => { showOrderItem(item) }}>
              <View style={styles.topPart}>
                <Icon name={showItem.uniqueId === item.uniqueId ? 'chevron-up' : 'chevron-down'} size={25} style={{ color: item.cartItems[0] == undefined ? 'grey' : Colors.primaryColor }} />
              </View>
            </TouchableOpacity>
          </View>

          {/* start here */}

          {
            showItem.uniqueId === item.uniqueId ?
              <View>
                <FlatList
                  // data={item.orderItems}
                  data={item.cartItems}
                  renderItem={(listItem) => renderOrderItems(listItem, item)}
                  keyExtractor={(item, index) => String(index)} />


                {/* Testing reject and cancel */}



                <ScrollView
                  contentContainerStyle={{ width: Dimensions.get('window').width }}
                >
                  {selectedOrderItemList.map((x) => {
                    return renderOrderItems(x, selectedOrder);
                  })}

                  {selectedOrderItemCancelledList.map((x) => {
                    return renderOrderItemsCancelled(x, selectedOrder);
                  })}
                </ScrollView>

                {(Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ||
                  Object.values(selectedOrderItemCancelledCheckDict).find(item => item && item.itemId)) &&

                  <View style={{
                    bottom: 0,
                    // backgroundColor: 'red',
                    backgroundColor: Colors.fieldtBgColor,
                    width: '100%',
                    height: Dimensions.get('screen').height * 0.08,

                    flexDirection: 'row',
                    alignItems: 'center',

                    paddingBottom: 20,
                    paddingTop: 20,
                    paddingHorizontal: 50,
                  }}>
                    <TouchableOpacity style={{
                      width: '45%',
                      height: Dimensions.get('screen').height * 0.05,

                      alignItems: 'center',
                      justifyContent: 'center',

                      backgroundColor: Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ? Colors.tabRed : Colors.primaryColor,
                      borderRadius: 8,

                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 2.22,
                      elevation: 3,
                    }} onPress={() => {
                      if (Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId)) {
                        cancelUserOrder();
                      }
                      else {
                        undoUserOrderCancelled();
                      }
                    }}>
                      <Text style={{
                        color: Colors.whiteColor,
                        fontFamily: 'NunitoSans-SemiBold',
                        fontSize: 16,
                      }}>
                        {Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ? 'Reject' : 'Undo'}
                      </Text>
                    </TouchableOpacity>

                    <View style={{
                      width: '10%',
                    }}>

                    </View>

                    <TouchableOpacity style={{
                      width: '45%',
                      height: Dimensions.get('screen').height * 0.05,

                      alignItems: 'center',
                      justifyContent: 'center',

                      backgroundColor: Colors.lightGrey,
                      borderRadius: 8,

                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 2.22,
                      elevation: 3,
                    }} onPress={() => {
                      setSelectedOrderItemCheckDict({});
                      setSelectedOrderItemCancelledCheckDict({});
                    }}>
                      <Text style={{
                        color: Colors.fontDark,
                        fontFamily: 'NunitoSans-SemiBold',
                        fontSize: 16,
                      }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>

                }
              </View>
              : null


          }


        </Swipeable>
      </View>
    );
  };

  const renderModal = (item) => {
    return (
      <View>
        {item && item.uniqueId &&
          <>
            {/* <Modal
              style={{ flex: 1 }}
              visible={visible}
              transparent={true}
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: Dimensions.get('window').height,
                }}>
                <View style={styles.confirmBox}>
                  <View style={{ marginTop: 40 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 18,
                      }}>
                      Prioritize Takeaway
                  </Text>
                  </View>
                  <View style={{ marginTop: 20 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: "80%",
                        alignSelf: "center"
                      }}>
                      Priotize Takeaway for {item.user == null ? "" : item.user.name}, Order#{item.customTable == "TAKE AWAY" ? "T" : ""}{item.id}
                    </Text>
                  </View>
                  <View style={{ height: 45 }}></View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }}>

                  </View>
                  <View
                    style={{
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 250,
                      height: 40,
                      alignContent: 'center',
                      flexDirection: "row",
                      marginTop: 40,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        prioritizeOrder(item.uniqueId)
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.primaryColor }}>
                        Confirm
                    </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.descriptionColor }}>
                        Cancel
                    </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal> */}
            <Modal
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible1}
              transparent={true}
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: Dimensions.get('window').height,
                }}>
                <View style={styles.confirmBox}>
                  <View style={{ marginTop: 40 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 18,
                      }}>
                      Prioritize Order
                    </Text>
                  </View>
                  <View style={{
                    marginTop: 20,
                  }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: "80%",
                        alignSelf: "center"
                      }}>
                      {/* Priotize Order for Table{item.tableId === null ? "" : outletTablesDict[item.tableId].code}, Order#{item.uniqueId} */}
                      Priotize Order for Table{selectedOrder.tableId === null ? "" : outletTablesDict[selectedOrder.tableId].code}, Order#{selectedOrder.uniqueId}
                    </Text>
                  </View>
                  <View style={{ height: 45 }}></View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      // zIndex: 6000,
                    }}>

                  </View>

                  <View
                    style={{
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 250,
                      height: 40,
                      alignContent: 'center',
                      flexDirection: "row",
                      marginTop: 40,
                      zIndex: 6000,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        prioritizeOrder(item.uniqueId)
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.primaryColor }}>
                        Confirm
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => {
                        // setState({ visible1: false });
                        setVisible1(false);
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.descriptionColor }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
          </>
        }
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {renderModal(currToPrioritizeOrder)}

      {/* <View style={{ flex: 2.2, backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 20, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}> */}
      <View style={{ flex: 1.8, backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 15, paddingVertical: 15, paddingBottom: 5, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}>
        <TableBar orderTables={orderId} />
      </View>
      <View style={{ flex: 8 }}>
        <FlatList style={{ paddingVertical: 10, paddingTop: 20 }}
          // data={orderId}
          data={dineInOrders}
          renderItem={renderOrder}
          keyExtractor={(item, index) => String(index)} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    fontFamily: 'NunitoSans-Regular'
  },
  insideFlat: {
    // paddingHorizontal: 4,
    paddingLeft: 4,
    marginBottom: 20,
    marginHorizontal: 10,
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',

  },
  topFlat: {
    backgroundColor: 'white',
    shadowColor: 'grey',
    shadowRadius: 3,
    shadowOpacity: 20,
    shadowOffset: { width: 1, height: 1 },
    margin: 10,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: 60,

  },
  topPart: {
    justifyContent: 'center',
    flex: 1,
    alignSelf: 'center',
  },


  bottomPart: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 10,
    alignItems: 'center',
  },
  primaryFont: {
    fontFamily: 'NunitoSans-Regular'
  },
  smallCircleFont: {
    color: 'white',
    fontFamily: 'NunitoSans-Bold',
    fontSize: 15
  },
  circleIcon: {
    width: '50%',
    height: "50%",
    resizeMode: 'contain'
  },
  smallCircle: {
    width: 25,
    height: 25,
    backgroundColor: Colors.primaryColor,
    borderRadius: 12.5,
    position: 'absolute',
    top: -5,
    right: -5,
    alignItems: 'center',
    justifyContent: 'center'
  },

  confirmBox: {
    width: 350,
    height: 280,
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },

})
export default OrderListScreen


