npm install --save @charles-johnson/react-native-ping @conodene/react-native-thermal-receipt-printer-image-qr @conodene/react-native-user-inactivity "@dr.pogodin/react-native-fs" @nozbe/watermelondb @react-native-async-storage/async-storage @react-native-clipboard/clipboard @react-native-community/checkbox @react-native-community/netinfo @react-native-community/push-notification-ios @react-native-firebase/analytics @react-native-firebase/app @react-native-firebase/auth @react-native-firebase/crashlytics @react-native-firebase/database @react-native-firebase/firestore @react-native-firebase/messaging @react-native-firebase/perf @react-native-firebase/storage  @shopify/flash-list "@zach.codes/react-calendar" aws-sdk axios base64-arraybuffer bignumber.js buffer crypto-js eventemitter3 hashids iconv-lite js-coroutines molpay-mobile-xdk-reactnative-beta moment moment-timezone nanoid pullstate react-native-big-calendar react-native-calendar-picker react-native-calendars react-native-canvas react-native-chart-kit react-native-check-box react-native-device-detection react-native-device-info react-native-document-picker react-native-draggable react-native-draggable-flatlist react-native-dropdown-picker react-native-fast-image react-native-fast-text react-native-file-logger react-native-gesture-handler react-native-get-random-values react-native-gifted-chat react-native-google-places-autocomplete react-native-html-to-pdf react-native-image-picker react-native-keyboard-aware-scroll-view react-native-modal-datetime-picker react-native-orientation-locker react-native-picker-select react-native-progress-circle react-native-push-notification react-native-qrcode-svg react-native-safe-area-context react-native-screens react-native-simple-radio-button react-native-smooth-picker react-native-spin-picker react-native-svg react-native-swipeout react-native-switch react-native-switch-pro react-native-table-component react-native-tablet-switcher react-native-vector-icons react-native-walkthrough-tooltip react-native-webview react-native-zip-archive rn-fetch-blob rn-qr-generator string-pixel-width xlsx react-native-reanimated

// for fixed version (api break compatibility)
npm install --save fusioncharts@3.18.0 @react-navigation/bottom-tabs@6.6.1 @react-navigation/native@6.1.18 @react-navigation/stack@6.4.1 react-native-dropdown-picker@3.8.3 react-native-fusioncharts@4.1.2

npm install --save-dev react-native-svg-transformer

////////////////////////////////////////////////////

for ios, need the additional steps:

https://www.npmjs.com/package/@react-native-community/push-notification-ios
https://rnfirebase.io/
https://rnfirebase.io/messaging/usage/ios-setup
https://reactnavigation.org/docs/stack-navigator (optional)
https://www.npmjs.com/package/molpay-mobile-xdk-reactnative-beta
https://www.npmjs.com/package/react-native-file-logger (might be needed)
https://www.npmjs.com/package/react-native-fusioncharts
https://www.npmjs.com/package/react-native-vector-icons

*2024-09-18 added:
https://www.npmjs.com/package/react-native-nfc-manager

////////////////////////////////////////////////////

for android:

https://www.npmjs.com/package/react-native-device-info // just in case got issue

(might needed to refer back)
https://www.npmjs.com/package/react-native-screens
https://github.com/software-mansion/react-native-screens/issues/17

////////////////////////////////////////////////////

additional configs:

https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/installation
https://github.com/Rapsssito/react-native-background-actions/blob/HEAD/INSTALL.md

////////////////////////////////////////////////////

build steps:

# Android apps
npx react-native run-android

# iOS apps
cd ios/
pod install --repo-update
cd ..
npx react-native run-ios

////////////////////////////////////////////////////

Link to view bundle JS (if got Compiling JS failed issue):

http://localhost:8085/index.bundle?platform=android&dev=true&minify=false&modulesOnly=false&runModule=true

////////////////////////////////////////////////////

change xcode version

xcodebuild -version (check version)
sudo xcode-select -s /Applications/Xcode14.2.app

////////////////////////////////////////////////////

// 2024-08-19 - android build error on (./gradlew clean build) (not enough memory?)

w/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:477:23 'setter for savePassword: Boolean' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:478:23 'setter for saveFormData: Boolean' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:548:23 'setter for allowFileAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:649:65 Unchecked cast: ArrayList<Any!> to List<Map<String, String>>
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:670:23 'setter for saveFormData: Boolean' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:705:36 Parameter 'viewWrapper' is never used
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:10:75 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:21:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:22:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:12:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:23:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:24:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:10:89 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:27:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:28:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java

> Task :rn-qr-generator:compileReleaseJavaWithJavac
Note: D:\Users\pc\Documents\GitHub\koodooapps\koodoo-merchant-v2\node_modules\rn-qr-generator\android\src\main\java\com\gevorg\reactlibrary\RNQrGeneratorModule.java uses unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.

> Task :react-native-webview:compileReleaseJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.

> Task :shopify_flash-list:compileReleaseKotlin
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/@shopify/flash-list/android/src/main/kotlin/com/shopify/reactnative/flash_list/AutoLayoutShadow.kt:87:13 Name shadowed: actualScrollOffset
w: file:///D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-v2/node_modules/@shopify/flash-list/android/src/main/kotlin/com/shopify/reactnative/flash_list/AutoLayoutView.kt:16:44 'RCTEventEmitter' is deprecated. Deprecated in Java

> Task :app:collectReleaseDependencies FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:collectReleaseDependencies'.
> Java heap space

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.6/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD FAILED in 9m 16s
2243 actionable tasks: 1995 executed, 248 up-to-date
PS D:\Users\pc\Documents\GitHub\koodooapps\koodoo-merchant-v2\android>

////////////////////////////////////////////////////

npx stack-beautifier .\android\app\build\generated\sourcemaps\react\release\index.android.bundle.map -t 20240812-stacktrace.txt

////////////////////////////////////////////////////

For more details, see https://developer.android.com/studio/write/lint#snapshot

  Lint found 6 errors, 6 warnings. First failure:

  D:\Users\pc\Documents\GitHub\koodooapps\koodoo-merchant-v2\node_modules\molpay-mobile-xdk-reactnative-beta\android\build.gradle:20: Error: Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX... [GradleCompatible]
      implementation 'com.android.support:support-v4:28.0.0'
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  The full lint text report is located at:
    D:\Users\pc\Documents\GitHub\koodooapps\koodoo-merchant-v2\node_modules\molpay-mobile-xdk-reactnative-beta\android\build\intermediates\lint_intermediate_text_report\debug\lint-results-debug.txt

////////////////////////////////////////////////////