import React, { Component } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import * as Cart from '../util/Cart';
import Draggable from 'react-native-draggable';
import Back from 'react-native-vector-icons/EvilIcons';
import { FlatList } from 'react-native-gesture-handler';
import Close from 'react-native-vector-icons/AntDesign';
import * as User from '../util/User';

class OutletMenuScreen extends Component {
  constructor({ navigation, props, route }) {
    const { outletData, orderType, test, navFrom } = route.params;
    console.log("orderType", orderType)
    super(props)
    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity style={{ marginTop: Platform.OS == 'ios' ? 20 : 30 }} onPress={() => { this.props.navigation.goBack() }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View>
              <Back
                name="chevron-left"
                size={30}
                color={Colors.whiteColor}
              />
            </View>
            <Text
              style={{
                color: Colors.whiteColor,
                fontSize: 14,
                textAlign: 'center',
                fontFamily: "NunitoSans-Regular"
              }}>
              Back
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => (
        <View style={{ marginRight: 15 }}>
        </View>
      ),
      headerTitle: () => (
        <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: Platform.OS == 'ios' ? 20 : 30 }}>
          <Text
            style={{
              fontSize: 18,
              textAlign: 'center',
              fontFamily: "NunitoSans-Bold",
              color: Colors.whiteColor
            }}>
            Dine In
          </Text>
        </View>
      ),
      headerTintColor: Colors.blackColor,
    });
    this.state = {
      outletData: outletData,
      outletMenu: [],
      category: '',
      menu: [],
      cartIcon: false,
      reverse: false,
      cartItem: [],
      categoryOutlet: [],
      test: test,
      currentMenu: [],
      productList2: [],
      productList: [],
      choice: null,
      categoryIndex: 0,
      navFrom: navFrom,

      isInfoTabHitTop: false,
      onStartVisible: false,
      cartWarning: false,
      cartProceed: []
    };
  }

  componentDidMount() {
    // this.category();
    // this.refresh();
    // this.refreshMenu()
    console.log("outlet menu", Cart.getTableNumber())

    if (this.state.outletData && this.state.outletData.id) {
      ApiClient.GET(API.outlet2 + this.state.outletData.id).then((result) => {
        // console.log(result)
        this.setState({ outletData: result })

        if (result) {
          this.category(result.id);
          this.refresh(result.id);
          this.refreshMenu(result.id);
        }
      });
    }
    else {
      ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
        // console.log(result)
        this.setState({ outletData: result })

        if (result) {
          this.category(result.id);
          this.refresh(result.id);
          this.refreshMenu(result.id);
        }
      });
    }

    // menu item
    // ApiClient.GET(API.merchantMenu + this.state.outletData.id).then((result) => {
    //   // console.log(result)
    //   if (result.length > 0) {
    //     this.setState({ category: result[0].category, menu: result[0].items })
    //   }
    //   this.setState({ outletMenu: result })
    // });

    // setInterval(() => {
    //   this.cartCount();
    //   this.getCartItem();
    // }, 1000);
    setInterval(() => {
      this.cartCount();
      this.getCartItem();

    }, 5000);

  }



  // function here

  getCartItem() {
    this.setState({ cartItem: Cart.getCartItem() })
    // console.log(this.state.cartItem)
  }

  cartCount() {
    if (Cart.getCartItem() !== null) {
      if (Cart.getCartItem().length > 0) {
        this.setState({ cartIcon: true })
      }
      else {
        this.setState({ cartIcon: false })
      }
    }
    else {
      this.setState({ cartIcon: false })
    }
  }

  goToCart() {
    if (Cart.getCartItem().length > 0) {
      console.log("GOTOCART", Cart.getTableNumber())
      this.props.navigation.navigate("Cart", { test: this.state.test, outletData: this.state.outletData });
    } else {
      Alert.alert("Info", "No item in your cart at the moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }

  }
  category(optionalId) {
    const requestId = optionalId ? optionalId : this.state.outletData.id;

    ApiClient.GET(API.activeMenu + requestId).then((result) => {
      const tmpCategories = {};
      for (const category of result) {
        const categoryName = category.name
        const categoryId = category.id
        if (!tmpCategories[categoryName]) {
          tmpCategories[categoryName] = {
            label: categoryName,
            value: categoryId,
          };
        }

      }
      const categories = Object.values(tmpCategories);
      this.setState({ categoryOutlet: categories, category: categories[0].label });

    }).catch(err => {
      console.log("Error")
      console.log(err)
    });
  }

  refresh(optionalId) {
    const requestId = optionalId ? optionalId : this.state.outletData.id;

    ApiClient.GET(API.merchantMenu + requestId).then((result) => {
      if (result != undefined && result.length > 0) {
        var productListRaw = [];

        result.forEach((element) => {
          console.log(element.items);
          productListRaw = productListRaw.concat(element.category);
          const activeItem = productListRaw.filter(item => item.active == 1)
          this.setState({ productList: productListRaw, productList2: activeItem, }, () => { });
        });
      }
    });
  }
  refreshMenu(optionalId) {
    const requestId = optionalId ? optionalId : this.state.outletData.id;

    ApiClient.GET(API.activeMenu + requestId).then((result) => {
      const category = result.filter(i => i.name == this.state.category)
      category.map(i => {
        // const newList = []
        // for (const item of i.items){
        //   if(item.name !== ""){
        //     newList.push(item)
        //   }
        // }
        // this.setState({ currentMenu: newList })
        this.setState({ currentMenu: i.items })


      })
      // }

      // }else{
      //   this.setState({ currentMenu: result });
      // }

    });
  }

  refresh() {
    this.setState({ refresh: true });
  }
  renderMenu = ({ item }) => {
    let quantity = 0;
    //const cartItem = this.state.cartItem.find(obj => obj.itemId === item.id);
    const cartItem = Cart.getCartItem().filter(obj => obj.itemId === item.id);
    if (cartItem) {
      for (const obj of cartItem) {
        quantity = parseInt(quantity) + parseInt(obj.quantity)
      }
      //quantity = cartItem.quantity;
    }
    return (
      <TouchableOpacity onPress={() => {
        if (this.checkCartOutlet()) {
          this.setState({ cartWarning: true, })
        } else {
          this.props.navigation.navigate('MenuItemDetails', { refresh: this.refresh.bind(this), menuItem: item, outletData: this.state.outletData })
        }

      }}>
        <View style={{ flexDirection: 'row', paddingHorizontal: 20, paddingBottom: 5, paddingTop: 10, }}>
          <View style={{ flexDirection: 'row', alignContent: 'center', alignItems: 'center', width: "65%" }}>
            <View style={{ backgroundColor: Colors.secondaryColor, width: 60, height: 60, borderRadius: 10 }}>
              <Image source={{ uri: item.image }} style={{ width: 60, height: 60, borderRadius: 10 }} />
            </View>
            <View style={{ marginLeft: 15 }}>
              <Text numberOfLines={1} style={{ fontSize: 15, textTransform: 'uppercase', fontFamily: "NunitoSans-Bold" }}>{item.name}</Text>
              <Text style={{ color: Colors.primaryColor, fontFamily: "NunitoSans-Bold", paddingTop: 5, fontSize: 15 }}>RM{parseFloat(item.price).toFixed(2)}</Text>
            </View>
          </View>
          <View style={{ flexDirection: 'row', width: "20%", marginLeft: 60 }}>
            <View style={{ backgroundColor: "#e3e1e1", width: 67, height: 24, borderRadius: 10, justifyContent: "center", alignSelf: "center" }}>
              <TouchableOpacity onPress={() => {
                if (this.checkCartOutlet()) {
                  this.setState({ cartWarning: true, cartProceed: item })
                } else {
                  this.props.navigation.navigate('MenuItemDetails', { refresh: this.refresh.bind(this), menuItem: item, outletData: this.state.outletData })
                }

              }}>

                <Text style={{ alignSelf: "center", color: "#8f8f8f", fontSize: 11, fontFamily: "NunitoSans-Bold" }}>{quantity > 0 ? quantity : "Add"}</Text>

              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    )

  }

  // onContainerScroll = e => {
  //   console.log(Dimensions.get('screen').width);
  //   console.log(e.nativeEvent.contentOffset.y);
  //   console.log('---------------------------')

  //   if (e.nativeEvent.contentOffset.y * 2 >= Dimensions.get('screen').width) {
  //     console.log('hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: true,
  //     // });
  //   }
  //   else {
  //     console.log('not hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: false,
  //     // });
  //   }
  // }
  isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    const paddingToBottom = 20;
    return layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom;
  };

  nextCategory() {
    const catLength = this.state.categoryOutlet.length
    if (this.state.categoryIndex == catLength - 1) {
      this.setState({
        category: this.state.categoryOutlet[0],
        categoryIndex: 0,
        // menu: this.state.choice[index].category,

      })
    } else {
      this.setState({
        category: this.state.categoryOutlet[this.state.categoryIndex + 1],
        categoryIndex: this.state.categoryIndex + 1,
        // menu: this.state.choice[index].category,

      })
    }
    this.refreshMenu();
  }

  checkCartOutlet() {
    const outletId = this.state.outletData.id
    console.log(Cart.getOutletId() != null)
    if (outletId != Cart.getOutletId() && Cart.getOutletId() != null) {
      return true
    }
    return false
  }
  // function end

  render() {
    return (
      <View style={styles.container}>
        <Modal
          style={{ flex: 1 }}
          visible={this.state.cartWarning}
          transparent={true}
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: Dimensions.get('window').height,
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  this.setState({ cartWarning: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-start',
                    padding: 14,
                  }}>
                  <Close name="close" size={35} color={"#b0b0b0"} />
                </View>
              </TouchableOpacity>
              <View style={{ marginBottom: 10 }}>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: '700',
                    fontSize: 18,
                  }}>
                  You are entering a different outlet
                    </Text>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: '700',
                    fontSize: 14,
                  }}>
                  Your existing cart items will be cleared if you proceed. Are you sure?
                    </Text>
              </View>
              <View
                style={{
                  alignSelf: 'center',
                  marginTop: 30,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 250,
                  height: 40,
                  alignContent: 'center',
                  marginTop: 40
                }}>
                <TouchableOpacity
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    borderRadius: 10,
                    height: 60,
                    marginTop: 30,
                    alignSelf: 'center',
                  }} onPress={() => {
                    this.setState({ cartWarning: false, })
                    Cart.clearCart();
                    this.props.navigation.navigate('MenuItemDetails', { refresh: this.refresh.bind(this), menuItem: this.state.cartProceed, outletData: this.state.outletData })
                  }}>
                  <Text style={{ fontSize: 28, color: Colors.whiteColor }}>
                    Proceed
                      </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => { this.setState({ visible: false }) }}
                  style={{
                    backgroundColor: Colors.secondaryColor,
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    borderRadius: 10,
                    height: 60,
                    marginTop: 20
                  }} onPress={() => { this.setState({ cartWarning: false, }) }}>
                  <Text style={{ fontSize: 28, color: Colors.whiteColor }}>
                    Take me back
                      </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        <ScrollView showsHorizontalScrollIndicator={false}
          // onScroll={this.onContainerScroll}
          onScroll={({ nativeEvent }) => {
            if (this.isCloseToBottom(nativeEvent)) {
              //console.log("HIT BOTTOM")
              //this.nextCategory()
            }
          }}
          stickyHeaderIndices={[1]}
        >
          {this.state.outletData && this.state.outletData.cover &&
            <Image
              source={{ uri: this.state.outletData.cover }}
              style={styles.outletCover}
            />
          }

          <View>
            <ScrollView
              showsHorizontalScrollIndicator={false}
              alwaysBounceHorizontal={true}
              horizontal={true}
              style={[styles.infoTab, {
                // ...!this.state.isInfoTabHitTop && { position: 'absolute' },
                // ...!this.state.isInfoTabHitTop && { top: 120 },
              }]}
            // stickyHeaderIndices={[0]} 
            >
              {this.state.categoryOutlet.map((item, index) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        category: item.label,
                        categoryIndex: index,
                        // menu: this.state.choice[index].category,

                      })
                      this.refreshMenu();
                    }}>
                    <View
                      style={[
                        styles.category,
                        {
                          borderBottomColor:
                            this.state.category == item.label
                              ? Colors.primaryColor
                              : null,
                          borderBottomWidth:
                            this.state.category == item.label ? 3 : 0,
                        },
                      ]}>
                      <Text
                        style={{
                          textTransform: 'capitalize',
                          paddingVertical: 12,
                          fontFamily: this.state.category == item.label
                            ? "NunitoSans-Bold"
                            : "NunitoSans-Regular",
                          color: this.state.category == item.label
                            ? Colors.primaryColor
                            : Colors.blackColor,
                          fontSize: 15,
                        }}>
                        {item.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>

          <View>
            <FlatList
              data={this.state.currentMenu}
              extraData={this.state.currentMenu}
              renderItem={this.renderMenu}
              keyExtractor={(item, index) => index} />
          </View>
          <View style={{ minHeight: 100 }} />
        </ScrollView>

        {this.state.cartIcon ?
          <Draggable
            shouldReverse={this.state.reverse}
            renderSize={100}
            renderColor={Colors.secondaryColor}
            isCircle
            x={270}
            y={470}
            onShortPressRelease={() => { this.goToCart(), this.cartCount() }}>
            <View style={{ width: 60, height: 60, justifyContent: "center" }}>
              <View style={{ alignSelf: "center" }}>
                <Ionicons name="cart-outline" size={45} />
              </View>
              <View style={styles.cartCount}>

                <Text style={{ color: Colors.whiteColor, fontSize: 10, fontFamily: "NunitoSans-Regular" }}>{Cart.getCartItem().length}</Text>
              </View>
            </View>
          </Draggable>
          : null}




      </View>
    )
  }
}

const styles = StyleSheet.create({
  confirmBox: {
    width: 350,
    height: 350,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
  },
  outletCover: {
    width: '100%',
    alignSelf: 'center',
    height: undefined,
    aspectRatio: 2,
    borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
  workingHourTab: {
    padding: 16,
    flexDirection: 'row',
  },
  outletAddress: {
    textAlign: 'center',
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: 'row',
    marginTop: 20,
  },
  actionView: {
    width: Dimensions.get('screen').width / 4,
    height: Dimensions.get('screen').width / 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
  category: {
    width: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatCartBtn: {
    zIndex: 2,
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: 'absolute',
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default OutletMenuScreen;
