import firestore from '@react-native-firebase/firestore';
import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet, Image, // View,
  Alert, TouchableOpacity, TextInput, Dimensions, Modal as ModalComponent,
  PermissionsAndroid, ActivityIndicator, useWindowDimensions, Platform, TouchableWithoutFeedback, InteractionManager
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import AIcon from 'react-native-vector-icons/AntDesign';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import DropDownPicker from 'react-native-dropdown-picker';
import moment from 'moment';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Styles from '../constant/Styles';
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'rn-fetch-blob';
import {
  extractImageNameFromUrl,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  parseImagePickerResponse,
  uploadImageToFirebaseStorage
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import {
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  PURCHASE_ORDER_STATUS,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import {
  autofitColumns,
  convertArrayToCSV,
  getImageFromFirebaseStorage,
  getPathForFirebaseStorageFromBlob,
  uploadFileToFirebaseStorage,
  parseValidPriceText,
} from '../util/common';
import XLSX from 'xlsx';
import { zip } from 'react-native-zip-archive';
const RNFS = require('@dr.pogodin/react-native-fs');
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import {
  EMAIL_REPORT_TYPE,
  EXPAND_TAB_TYPE,
  ROLE_TYPE,
} from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import Ionicon from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Switch from 'react-native-switch-pro';
import {
  EFFECTIVE_DAY_DROPDOWN_LIST1,
  EFFECTIVE_DAY,
} from '../constant/promotions';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import APILocal from '../util/apiLocalReplacers';
import { FlashList } from "@shopify/flash-list";
import { NestableScrollContainer, NestableDraggableFlatList } from 'react-native-draggable-flatlist';
import RadioForm, { RadioButton, RadioButtonInput, RadioButtonLabel } from 'react-native-simple-radio-button';
import Tooltip from 'react-native-walkthrough-tooltip';
import CheckBox from '@react-native-community/checkbox';
import { useKeyboard } from '../hooks';
import { Collections } from '../constant/firebase';
import { launchImageLibrary } from 'react-native-image-picker';
import AsyncImage from '../components/asyncImage';

const View = require(
  'react-native/Libraries/Components/View/ViewNativeComponent'
).default;

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ProductCategoryScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  //var DismissKeyboard = require('dismissKeyboard'); 

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [purchaseOrder, setPurchaseOrder] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}]);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [keyboardHeight] = useKeyboard();
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [search, setSearch] = useState('');

  const [loading, setLoading] = useState(false);

  const [deleteModal, setDeleteModal] = useState(false);

  const [editCategory, setEditCategory] = useState(false);

  ///////////////////////////////////////////////////////////////////

  const [categoryUniqueId, setCategoryUniqueId] = useState('');
  const [categoryName, setCategoryName] = useState('');

  ///////////////////////////////////////////////////////////////////

  // hide menu support

  const [hideInOrderTypes, setHideInOrderTypes] = useState('');

  ///////////////////////////////////////////////////////////////////

  const [supplierItems, setSupplierItems] = useState([
    {
      supplyItemId: '',
      name: '',
      sku: '',
      unit: '',
      price: 0,
    },
  ]);

  ///////////////////////////////////////////////////////////////////

  // 2023-02-09 - Print KD multiple times

  const [printKDNum, setPrintKDNum] = useState(1);

  ///////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState('');

  const [supplierDropdownList, setSupplierDropdownList] = useState([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState('');

  const [poStatus, setPoStatus] = useState(PURCHASE_ORDER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [supplyItemDropdownList, setSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      supplyItemId: '',
      name: '',
      sku: '',
      quantity: 0,
      orderQuantity: 0,
      receivedQuantity: 0,
      price: 0,
      totalPrice: 0,
    },
  ]);

  const [selectedSupplier, setSelectedSupplier] = useState({
    taxRate: 0,
  });

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [categoryProductQuantity, setCategoryProductQuantity] = useState(0);

  const [expandViewDict, setExpandViewDict] = useState({});

  const [expandThreeDots, setExpandThreeDots] = useState({}); //Use to expand the view when three dots are tapped

  const outletSupplyItemsSkuDict = CommonStore.useState(
    (s) => s.outletSupplyItemsSkuDict,
  );

  const supplyItems = CommonStore.useState((s) => s.supplyItems);
  const supplyItemsDict = CommonStore.useState((s) => s.supplyItemsDict);
  const suppliers = CommonStore.useState((s) => s.suppliers);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const [importModal, setImportModal] = useState(false);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const userName = UserStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  // const outletsItemAddOnDict = CommonStore.useState((s) => s.outletsItemAddOnDict);
  // const outletsItemAddOnChoiceDict = CommonStore.useState((s) => s.outletsItemAddOnChoiceDict);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const selectedOutletCategoryEdit = CommonStore.useState(
    (s) => s.selectedOutletCategoryEdit,
  );

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // 2022-08-16 - Add start and end time support for availability

  const [effectiveStartTime, setEffectiveStartTime] = useState(moment().startOf('day').toDate());
  const [effectiveEndTime, setEffectiveEndTime] = useState(moment().endOf('day').toDate());
  const [showEffectiveStartTimePicker, setShowEffectiveStartTimePicker] = useState(false);
  const [showEffectiveEndTimePicker, setShowEffectiveEndTimePicker] = useState(false);
  const [isAvailableDayActive, setIsAvailableDayActive] = useState(false);
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] = useState([EFFECTIVE_DAY_DROPDOWN_LIST1[0].value]);
  const [effectiveDays, setEffectiveDays] = useState(moment().day());
  const [isActive, setIsActive] = useState(true);

  /////////////////////////////////////////////////////////////////////////////////

  const [selectedPrinterAreaList, setSelectedPrinterAreaList] = useState([]);
  const [printerAreaDropdownList, setPrinterAreaDropdownList] = useState([]);

  const outletPrinters = OutletStore.useState((s) => s.outletPrinters.concat(s.sunmiPrinters).concat(s.iminPrinters).concat(s.blePrinters));

  /////////////////////////////////////////////////////////////////////////////////

  const [selectedHideOutletSectionIdList, setSelectedHideOutletSectionIdList] = useState([]);
  const [outletSectionDropdownList, setOutletSectionDropdownList] = useState([]);

  const outletSections = OutletStore.useState((s) => s.outletSections);

  /////////////////////////////////////////////////////////////////////////////////

  /* 6 march 2023 tag */

  const [selectedUserTagList, setSelectedUserTagList] = useState([]);
  const [userTagList, setUserTagList] = useState([]);
  const [userTagDropdownList, setUserTagDropdownList] = useState([]);
  const [searchingUserTagText, setSearchingUserTagText] = useState('');
  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const taguserId = UserStore.useState((s) => s.firebaseUid);
  const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);
  const [tagModal, setTagModal] = useState(false);

  const [noManualDisc, setNoManualDisc] = useState(false);
  const [excludePromoVoucher, setExcludePromoVoucher] = useState(false);

  // Image
  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [isClearProductImage, setIsClearProductImage] = useState(false);

  const [itemCustomTax, setItemCustomTax] = useState('DEFAULT');
  const outletCustomTax = OutletStore.useState((s) => s.outletCustomTax);

  const customTaxDropdownList = React.useMemo(() => {
    const defaultOption = {
      label: 'Default',
      value: 'DEFAULT',
    };

    const customTaxOptions = outletCustomTax.map(tax => ({
      label: `${tax.name} (${tax.rate * 100}%)`,
      value: tax.uniqueId,
    }));

    return [defaultOption, ...customTaxOptions];
  }, [outletCustomTax]);

  /////////////////////////////////////////////////////////////////////////////////
  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          .filter((item) => {
            var isExisted = false;

            for (var i = 0; i < userTagList.length; i++) {
              if (userTagList[i].uniqueId === item.uniqueId) {
                isExisted = true;
                break;
              }
            }

            return !isExisted;
          })
          .map((item) => {
            return { label: item.name, value: item.uniqueId };
          }),
      );
    }
  }, [crmUserTags, userTagList]);
  /////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (selectedOutletCategoryEdit) {
      // insert info

      if (!isClearProductImage) {
        setImage(selectedOutletCategoryEdit.image)
      }
      setCategoryUniqueId(selectedOutletCategoryEdit.uniqueId);
      setCategoryName(selectedOutletCategoryEdit.name);
      setHideInOrderTypes(selectedOutletCategoryEdit.hideInOrderTypes || []);
      setIsAvailableDayActive(selectedOutletCategoryEdit.isAvailableDayActive || false,);

      setPrintKDNum(selectedOutletCategoryEdit.printKDNum ? selectedOutletCategoryEdit.printKDNum : 1);

      setSelectedUserTagList(
        selectedOutletCategoryEdit.crmUserTagIdList
          ? selectedOutletCategoryEdit.crmUserTagIdList
          : [],
      );
      setSearchingUserTagText('');

      var effectiveTypeOptionsTemp = [];

      if (selectedOutletCategoryEdit && selectedOutletCategoryEdit.effectiveTypeOptions &&
        selectedOutletCategoryEdit.effectiveTypeOptions.length > 0) {
        for (var i = 0; i < selectedOutletCategoryEdit.effectiveTypeOptions.length; i++) {
          if (
            EFFECTIVE_DAY_DROPDOWN_LIST1.find(
              (item) => item.value === selectedOutletCategoryEdit.effectiveTypeOptions[i],
            )
          ) {
            effectiveTypeOptionsTemp.push(selectedOutletCategoryEdit.effectiveTypeOptions[i]);
          }
        }
        if (selectedOutletCategoryEdit.isAvailableDayActive === true) {
          setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
        }
      }

      setEffectiveStartTime(selectedOutletCategoryEdit.effectiveStartTime ? moment(selectedOutletCategoryEdit.effectiveStartTime).toDate() : moment().startOf('day').toDate());
      setEffectiveEndTime(selectedOutletCategoryEdit.effectiveEndTime ? moment(selectedOutletCategoryEdit.effectiveEndTime).toDate() : moment().endOf('day').toDate());

      setSelectedPrinterAreaList(selectedOutletCategoryEdit.printerAreaList ? selectedOutletCategoryEdit.printerAreaList : []);

      setSelectedHideOutletSectionIdList(selectedOutletCategoryEdit.hideOutletSectionIdList ? selectedOutletCategoryEdit.hideOutletSectionIdList : []);

      setNoManualDisc(selectedOutletCategoryEdit.noManualDisc ? selectedOutletCategoryEdit.noManualDisc : false);

      setExcludePromoVoucher(selectedOutletCategoryEdit.excludePromoVoucher ? selectedOutletCategoryEdit.excludePromoVoucher : false);

      setItemCustomTax(selectedOutletCategoryEdit.customTaxId || 'DEFAULT');
    } else {
      // designed to always mounted, thus need clear manually...

      setImage('');
      setIsImageChanged(false);

      setCategoryUniqueId('');
      setCategoryName('');
      setHideInOrderTypes([]);
      setEffectiveStartTime(moment().startOf('day').toDate());
      setEffectiveEndTime(moment().startOf('day').toDate());
      setIsAvailableDayActive(false);
      setSelectedEffectiveTypeOptions[EFFECTIVE_DAY_DROPDOWN_LIST1[0].value];

      setSelectedPrinterAreaList([]);

      setSelectedHideOutletSectionIdList([]);

      setPrintKDNum(1);

      setSelectedUserTagList([]);
      setSearchingUserTagText('');

      setNoManualDisc(false);

      setExcludePromoVoucher(false);

      setItemCustomTax('DEFAULT');
    }
  }, [selectedOutletCategoryEdit]);

  useEffect(() => {
    setSupplierDropdownList(
      suppliers.map((supplier) => ({
        label: supplier.name,
        value: supplier.uniqueId,
      })),
    );

    if (selectedSupplierId === '' && suppliers.length > 0) {
      setSelectedSupplierId(suppliers[0].uniqueId);
    }
  }, [suppliers]);

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      })),
    );

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setSupplyItemDropdownList(
      supplyItems.map((supplyItem) => ({
        label: supplyItem.name,
        value: supplyItem.uniqueId,
      })),
    );

    if (
      supplyItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].supplyItemId === ''
    ) {
      setPoItems([
        {
          supplyItemId: supplyItems[0].uniqueId,
          name: supplyItems[0].name,
          sku: supplyItems[0].sku,
          quantity: outletSupplyItemsSkuDict[supplyItems[0].sku]
            ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity
            : 0, // check if the supply item sku for this outlet existed
          orderQuantity: 0,
          receivedQuantity: 0,
          price: supplyItems[0].price,
          totalPrice: 0,
        },
      ]);
    } else if (
      poItems[0].supplyItemId !== '' &&
      Object.keys(supplyItemsDict).length > 0
    ) {
      var poItemsTemp = [...poItems];

      for (var i = 0; i < poItemsTemp.length; i++) {
        const supplyItem = supplyItemsDict[poItemsTemp[i].supplyItemId];

        if (supplyItem) {
          poItemsTemp[i] = {
            ...poItemsTemp[i],
            quantity: outletSupplyItemsSkuDict[supplyItem.sku]
              ? outletSupplyItemsSkuDict[supplyItem.sku].quantity
              : 0, // check if the supply item sku for this outlet existed | might changed in real time
            price: supplyItem.price, // might changed in real time
          };
        }
      }

      setPoItems(poItemsTemp);
    }
  }, [
    supplyItems,
    supplyItemsDict,
    // outletSupplyItemsSkuDict
  ]);

  useEffect(() => {
    if (suppliers.length > 0 && selectedSupplierId !== '') {
      setSelectedSupplier(
        suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId),
      );
    }
  }, [suppliers, selectedSupplierId]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(
    //   poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0),
    // );
    setSubtotal(
      poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0),
    );
  }, [poItems]);

  useEffect(() => {
    // console.log('taxTotal');
    // console.log(subtotal * selectedSupplier.taxRate);
    setTaxTotal(subtotal * selectedSupplier.taxRate);
  }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log(subtotal - discountTotal + taxTotal);
    setFinalTotal(subtotal - discountTotal + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);

  useEffect(() => {
    var uniquePrinterAreaNameList = [];
    var uniquePrinterAreaList = [];

    for (var i = 0; i < outletPrinters.length; i++) {
      var name = '';

      if (outletPrinters[i].area) {
        name = outletPrinters[i].area;
      } else if (outletPrinters[i].name) {
        name = outletPrinters[i].name;
      }

      if (name && !uniquePrinterAreaList.includes(name)) {
        uniquePrinterAreaNameList.push(name);
        uniquePrinterAreaList.push(outletPrinters[i]);
      }
    }

    const printerAreaDropdownListTemp = uniquePrinterAreaList.map((item) => ({
      label: item.area || item.name,
      // value: item.uniqueId,
      value: item.area || item.name,
    }));

    setPrinterAreaDropdownList(printerAreaDropdownListTemp);

    if (selectedOutletCategoryEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(printerAreaDropdownListTemp.length);

      if (printerAreaDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedPrinterAreaList([printerAreaDropdownListTemp[0].value]);
        setSelectedPrinterAreaList(printerAreaDropdownListTemp.map(printerArea => printerArea.value));
      } else {
        console.log('make blank');

        setSelectedPrinterAreaList([]);
      }
    } else {
      var selectedPrinterAreaListTemp = [];

      if (
        selectedOutletCategoryEdit.printerAreaList &&
        selectedOutletCategoryEdit.printerAreaList.length > 0
      ) {
        for (var i = 0; i < selectedOutletCategoryEdit.printerAreaList.length; i++) {
          var isPrinterAreaMissing = false;

          const printerArea = selectedOutletCategoryEdit.printerAreaList[i];

          for (var j = 0; j < printerAreaDropdownListTemp.length; j++) {
            if (printerAreaDropdownListTemp[j].value === printerArea) {
              isPrinterAreaMissing = true;
              break;
            }
          }

          if (!isPrinterAreaMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
          } else {
            selectedPrinterAreaListTemp.push(printerArea);
          }
        }
      }

      setSelectedPrinterAreaList(selectedPrinterAreaListTemp);
    }
  }, [outletPrinters, selectedOutletCategoryEdit]);

  useEffect(() => {
    const outletSectionDropdownListTemp = outletSections.map((item) => ({
      label: item.sectionName,
      // value: item.uniqueId,
      value: item.uniqueId,
    }));

    setOutletSectionDropdownList(outletSectionDropdownListTemp);

    if (selectedOutletCategoryEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(outletSectionDropdownListTemp.length);

      if (outletSectionDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedHideOutletSectionIdList(outletSectionDropdownListTemp);
        setSelectedHideOutletSectionIdList([]);
      } else {
        console.log('make blank');

        setSelectedHideOutletSectionIdList([]);
      }
    } else {
      var selectedOutletSectionListTemp = [];

      if (
        selectedOutletCategoryEdit.hideOutletSectionIdList &&
        selectedOutletCategoryEdit.hideOutletSectionIdList.length > 0
      ) {
        for (var i = 0; i < selectedOutletCategoryEdit.hideOutletSectionIdList.length; i++) {
          var isOutletSectionMissing = true;

          const outletSectionId = selectedOutletCategoryEdit.hideOutletSectionIdList[i];

          for (var j = 0; j < outletSectionDropdownListTemp.length; j++) {
            if (outletSectionDropdownListTemp[j].value === outletSectionId) {
              isOutletSectionMissing = false;
              break;
            }
          }

          if (!isOutletSectionMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
            selectedOutletSectionListTemp.push(outletSectionId);
          } else {

          }
        }
      }

      setSelectedHideOutletSectionIdList(selectedOutletSectionListTemp);
    }
  }, [outletSections, selectedOutletCategoryEdit]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Manage Product
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder();

  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'KooDoo Merchant Storage Permission',
          message: 'KooDoo Merchant App needs access to your storage ',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log('Storage permission granted');
      } else {
        // console.log('Storage permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const renderOrderItem = ({ item, index }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    //var quantity = 0;

    // var totalQuantity = 0;

    // for (var i = 0; i < outletItems.length; i++) {
    //   //// console.log('hello')
    //   if (outletItems[i].categoryId === item.uniqueId) {
    //     //// console.log('123hihi')
    //     totalQuantity += 1;
    //   }
    // }

    // setCategoryProductQuantity(totalQuantity);

    return (
      <TouchableOpacity
        onPress={() => {
          // CommonStore.update(s => {
          //   s.selectedOutletCategoryEdit = item;
          // });

          // setPurchaseOrder(false);
          // setAddPurchase(true);

          /* CommonStore.update(s => {
          s.selectedOutletCategoryEdit = item;

          s.timestamp = Date.now;
        }, () => {
          navigation.navigate('Product');
        }); */

          CommonStore.update((s) => {
            s.selectedOutletCategoryEdit = item;
            s.selectedProductEdit = item;
            s.timestamp = Date.now;
          });
          navigation.navigate('Product');
        }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
          }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
            }}>{`${item.name}`}</Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
            }}>
            {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join('\n')
              : '-'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
              color: Colors.primaryColor,
            }}>
            {item.totalItemQuantity}
            {/* {outletItems.filter(outletItem => outletItem.categoryId === item.uniqueId).length} */}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
            }}>
            {item.createdAt
              ? moment(item.createdAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
            }}>
            {item.updatedAt
              ? moment(item.updatedAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>

          {expandThreeDots[item.uniqueId] == true ? (
            <View
              style={{
                //position: 'absolute',
                width: 110,
                height: 33,
                marginLeft: -110,
                zIndex: 1,
                flexDirection: 'column',
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                //borderColor: '#E7E7E7',
                borderColor: Colors.highlightColor,
                borderRadius: 7,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 2,
              }}>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  height: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  // CommonStore.update(s => {
                  //   s.selectedPromotionEdit = item;
                  // });

                  // navigation.navigate('NewCampaign');

                  CommonStore.update((s) => {
                    s.selectedOutletCategoryEdit = item;
                    s.timestamp = Date.now;
                  });

                  setPurchaseOrder(false);
                  setAddPurchase(true);

                  // expandThreeDotsFunc(item);
                }}>
                <View style={{ width: '30%', paddingLeft: 12 }}>
                  {/* <MaterialIcons name='edit' size={17} color='darkgreen' /> */}
                  <FontAwesome5
                    name="edit"
                    size={17}
                    color={Colors.primaryColor}
                  />
                </View>
                <View style={{ width: '70%' }}>
                  <Text style={{ marginLeft: 5 }}>Edit</Text>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}

          <View
            style={{
              width: '4%',
              flexDirection: 'row',
              // paddingRight: 20,
              // backgroundColor: 'red'
            }}>
            <TouchableOpacity
              style={{
                marginTop: 0,
                alignSelf: 'flex-start',
                alignItems: 'flex-start',
              }}
              onPress={() => {
                // expandThreeDotsFunc(item)

                CommonStore.update((s) => {
                  s.selectedOutletCategoryEdit = item;
                });

                setPurchaseOrder(false);
                setAddPurchase(true);
                setEditCategory(true);
              }}>
              {/* <Entypo name='dots-three-vertical' size={25} color={Colors.tabGrey} style={{ alignSelf: 'flex-start' }} /> */}
              <FontAwesome5
                name="edit"
                style={{
                  bottom: 1,
                }}
                size={22}
                color={Colors.primaryColor}
              />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderOrderItemSort = useCallback(({ item, drag, index }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    //var quantity = 0;

    // var totalQuantity = 0;

    // for (var i = 0; i < outletItems.length; i++) {
    //   //// console.log('hello')
    //   if (outletItems[i].categoryId === item.uniqueId) {
    //     //// console.log('123hihi')
    //     totalQuantity += 1;
    //   }
    // }

    // setCategoryProductQuantity(totalQuantity);

    return (
      <TouchableOpacity
        delayLongPress={1000}
        onLongPress={drag}
      >
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
          }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
            }}>{`${item.name}`}</Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
            }}>
            {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join('\n')
              : '-'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
              color: Colors.primaryColor,
            }}>
            {item.totalItemQuantity}
            {/* {outletItems.filter(outletItem => outletItem.categoryId === item.uniqueId).length} */}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
            }}>
            {item.createdAt
              ? moment(item.createdAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
            }}>
            {item.updatedAt
              ? moment(item.updatedAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>
          <View
            style={{
              width: '4%',
              flexDirection: 'row',
              // paddingRight: 20,
              // backgroundColor: 'red'
            }}>
            <View
              style={{
                marginTop: 0,
                alignSelf: 'flex-start',
                alignItems: 'flex-start',
              }}>
              {/* <Entypo name='dots-three-vertical' size={25} color={Colors.tabGrey} style={{ alignSelf: 'flex-start' }} /> */}
              <Ionicon
                name="menu"
                style={{
                  bottom: 1,
                }}
                size={22}
                color={Colors.primaryColor}
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  });

  const renderOrderItemDelete = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          isChecked(item)
        }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 16,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
          }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
              marginTop: 5,
            }}>{`${item.name}`}</Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '21%',
              color: Colors.primaryColor,
              marginTop: 5,
            }}>
            {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join('\n')
              : '-'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
              color: Colors.primaryColor,
              marginTop: 5,
            }}>
            {item.totalItemQuantity}
            {/* {outletItems.filter(outletItem => outletItem.categoryId === item.uniqueId).length} */}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
              marginTop: 5,
            }}>
            {item.createdAt
              ? moment(item.createdAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '18%',
              marginTop: 5,
            }}>
            {item.updatedAt
              ? moment(item.updatedAt).format('DD MMM YYYY')
              : 'N/A'}
          </Text>

          <View
            style={{
              width: '4%',
              flexDirection: 'row',
              // paddingRight: 20,
              // backgroundColor: 'red'
            }}>
            <View
              style={{
                marginTop: 0,
                alignSelf: 'flex-start',
                alignItems: 'flex-start',
              }}>
              {/* <Entypo name='dots-three-vertical' size={25} color={Colors.tabGrey} style={{ alignSelf: 'flex-start' }} /> */}
              <CheckBox
                disabled
                value={deleteList.find((findItem) => findItem.uniqueId === item.uniqueId) ? true : false}
                onValueChange={() => {
                  isChecked(item)
                }}
                style={{
                  // bottom: 1,
                  // height: 25,
                }}
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSupplierItems = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          alignItems: 'center',
          // height: (windowWidth * 0.1) * 3,
        }}>
        <View
          style={{
            width: '20%',
            // marginLeft: 50,
            marginLeft: '-1%',
          }}>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 150,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'Item name'}
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={'default'}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      name: text,
                      isChanged: true,
                    }
                    : supplierItem,
                ),
              );
            }}
            value={supplierItems[index].name}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: '20%',
            // marginLeft: 50,
            // backgroundColor: 'blue',
            marginLeft: '-1%',
          }}>
          <TextInput
            editable={item.supplyItemId === ''}
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 150,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'SKU'}
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={'default'}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      sku: text,
                      isChanged: true,
                    }
                    : supplierItem,
                ),
              );
            }}
            value={supplierItems[index].sku}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: '20%',
            // marginLeft: 50,
            // backgroundColor: 'blue',
            // marginLeft: '-1%',
          }}>
          <TextInput
            editable={item.supplyItemId === ''}
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'Unit'}
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={'default'}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      unit: text,
                      isChanged: true,
                    }
                    : supplierItem,
                ),
              );
            }}
            value={supplierItems[index].unit}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: '20%',
            // marginLeft: 50,
            // backgroundColor: 'blue',
            // marginLeft: '-1%',
          }}>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'Price'}
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={'decimal-pad'}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      price: text,
                      isChanged: true,
                    }
                    : supplierItem,
                ),
              );
            }}
            value={supplierItems[index].price}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: '10%',
            // marginLeft: 50,
            // backgroundColor: 'blue',
          }} />

        <TouchableOpacity
          style={{ marginLeft: 10 }}
          onPress={() => {
            setSupplierItems([
              ...supplierItems.slice(0, index),
              ...supplierItems.slice(index + 1),
            ]);
          }}>
          <Icon name="trash-2" size={20} color="#eb3446" />
        </TouchableOpacity>
      </View>
    );
  };

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email,
    };
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     Alert.alert(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  // const createStockOrder = () => {
  //   var body = {
  //     poId: poId,
  //     poItems: poItems,
  //     supplierId: selectedSupplierId,
  //     status: poStatus,
  //     outletId: selectedTargetOutletId,
  //     tax: taxTotal,
  //     discount: discountTotal,
  //     totalPrice: subtotal,
  //     finalTotal: finalTotal,
  //     estimatedArrivalDate: date,

  //     merchantId: merchantId,
  //     remarks: '',
  //   };

  //   ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
  //     if (result && result.uniqueId) {
  //       Alert.alert(
  //         'Success',
  //         'Purchase order created successfully',
  //         [
  //           { text: "OK", onPress: () => { props.navigation.goBack() } }
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   });
  // }

  const createOutletItemCategory = async () => {
    // console.log('on createOutletItemCategory');
    try {
      if (categoryName !== '') {
        if (selectedOutletCategoryEdit === null) {
          ///////////////////////////////////
          // upload image

          var outletItemImagePath = '';
          var outletItemIdLocal = selectedOutletCategoryEdit
            ? selectedOutletCategoryEdit.commonId
            : uuidv4();

          if (image && imageType) {
            // outletItemIdLocal = selectedOutletCategoryEdit.uniqueId;

            outletItemImagePath = await uploadImageToFirebaseStorage(
              {
                uri: image,
                type: imageType,
              },
              `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/image${imageType}`,
            );
          }

          var body = {
            categoryName,
            outletId: currOutletId,
            merchantId,

            hideInOrderTypes,

            // isActive: effectiveDays == selectedEffectiveTypeOptions ? true : false,
            isActive: true,
            effectiveTypeOptions: selectedEffectiveTypeOptions,
            effectiveStartTime: moment(effectiveStartTime).valueOf(),
            effectiveEndTime: moment(effectiveEndTime).valueOf(),
            isAvailableDayActive,

            printerAreaList: selectedPrinterAreaList,

            printKDNum,

            crmUserTagIdList: selectedUserTagList ? selectedUserTagList : [],
            searchingUserTagText,

            hideOutletSectionIdList: selectedHideOutletSectionIdList ? selectedHideOutletSectionIdList : [],

            uid: userId,

            noManualDisc: noManualDisc,

            excludePromoVoucher,

            customTaxId: itemCustomTax !== 'DEFAULT' ? itemCustomTax : null,

            image: outletItemImagePath,
          };

          // console.log(body);

          //ApiClient.POST(API.createOutletItemCategory, body).then((result) => {
          APILocal.createOutletItemCategory(body).then((result) => {
            if (result && result.status === 'success') {
              Alert.alert(
                'Success',
                'Product category has been created',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      //isActiv    // props.navigation.goBack()
                      setEditPurchase(false);
                      setAddPurchase(false);
                      setPurchaseOrder(true);
                    },
                  },
                ],
                { cancelable: false },
              );
            } else {
              Alert.alert(
                'Error',
                'Unable to create the product category, please try again.',
              );
            }
          });
        } else {

          var outletItemImagePath = '';
          var outletItemIdLocal = selectedOutletCategoryEdit.uniqueId;

          if (image && imageType && isImageChanged) {
            // outletItemIdLocal = selectedOutletCategoryEdit.uniqueId;

            let imageName = 'image';
            if (selectedOutletCategoryEdit.image) {
              let resultImg = extractImageNameFromUrl(selectedOutletCategoryEdit.image);

              if (resultImg) {
                imageName = 'image' + (resultImg.number + 1);
              }
            }

            outletItemImagePath = await uploadImageToFirebaseStorage(
              {
                uri: image,
                type: imageType,
              },
              `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/${imageName}${imageType}`,
            );
          }

          var body = {
            categoryName,
            outletId: currOutletId,
            hideInOrderTypes,

            uniqueId: selectedOutletCategoryEdit.uniqueId,

            // isActive: isActive !== undefined ? isActive : true,
            isActive: true,
            effectiveTypeOptions: selectedEffectiveTypeOptions,
            effectiveStartTime: moment(effectiveStartTime).valueOf(),
            effectiveEndTime: moment(effectiveEndTime).valueOf(),
            isAvailableDayActive,

            printerAreaList: selectedPrinterAreaList,

            printKDNum,

            crmUserTagIdList: selectedUserTagList ? selectedUserTagList : [],
            searchingUserTagText,

            hideOutletSectionIdList: selectedHideOutletSectionIdList ? selectedHideOutletSectionIdList : [],

            uid: userId,

            noManualDisc: noManualDisc,

            excludePromoVoucher,

            image: outletItemImagePath,
            editCategoryImage: selectedOutletCategoryEdit.image ? selectedOutletCategoryEdit.image : '',

            customTaxId: itemCustomTax !== 'DEFAULT' ? itemCustomTax : null,
          };

          // console.log(body);

          //ApiClient.POST(API.updateOutletItemCategory, body).then((result) => {
          APILocal.updateOutletItemCategory(body).then((result) => {
            if (result && result.status === 'success') {
              Alert.alert(
                'Success',
                'Product category has been updated',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // props.navigation.goBack()
                      setEditPurchase(false);
                      setAddPurchase(false);
                      setPurchaseOrder(true);
                    },
                  },
                ],
                { cancelable: false },
              );
            } else {
              Alert.alert('Error', 'Failed to update Product Category');
            }
          });
        }
      } else {
        Alert.alert('Error', 'Please fill in all information:\nCategory Name');
      }
    } catch (error) {
      console.log('create error')
      console.error(error);
    }
  };

  /* tag */
  const createCRMUserTagOrAddCRMUserTagToCategory = async () => {
    var body = {
      crmUserTagIdList: selectedUserTagList,
      merchantId,

      outletItemCategoryId: selectedOutletCategoryEdit.uniqueId,

      searchingUserTagText,
    };

    // clear searching text after submit
    setSearchingUserTagText('');

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(
    //   API.createCRMUserTagOrAddCRMUserTagToProduct,
    //   body,
    //   false,
    // )
    APILocal.createCRMUserTagOrAddCRMUserTagToCategory({
      body,
      uid: taguserId,
    }).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'Tag(s) has been saved to the category',
          [
            {
              text: 'OK',
              onPress: () => {
                // navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );

        if (result.outletItemCategory) {
          CommonStore.update((s) => {
            s.selectedOutletCategoryEdit = result.outletItemCategory;
          });
        }

        setTagModal(false);
      }

      CommonStore.update((s) => {
        s.isLoading = false;
      });
    });
  };
  const renderProductTag = ({ item }) => {
    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: 'darkgreen',
          borderRadius: 5,
          padding: 5,
          alignSelf: 'baseline',
          justifyContent: 'space-between',
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: 10,
          marginBottom: 5,
        }}>
        <Text style={{ fontWeight: '500', color: 'green' }}>TESTING</Text>
        <TouchableOpacity
          style={{ marginLeft: 5 }}
          onPress={() => {
            Alert.alert(
              'Info',
              `Are you sure you want to remove this tag?`,
              [
                { text: 'NO', onPress: () => { }, style: 'cancel' },
                {
                  text: 'YES',
                  onPress: () => { },
                },
              ],
              { cancelable: false },
            );
          }}>
          <AntDesign name="closecircle" color={Colors.fieldtTxtColor} />
        </TouchableOpacity>
      </View>
    );
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    var allOutletsStr = allOutlets.map((item) => item.name).join(';');

    // var taxName = currOutletTaxes[0].name;
    var taxName = 'SST';

    var excelColumn = {
      Name: 'Fried noodle',
      Description: 'Hong Kong style with dark soy sauce',
      SKU: 'FN001',
      Price: '12.99',
      'Prepare Time (seconds)': '900',
      Currency: 'MYR',
      Category: 'Dry Noodles',
      Outlet: allOutletsStr,
      Tax: `${taxName}:0.06`,
      Variant: 'Size:Regular/0.00|Large/3.00;Flavour:Original/0.00|Spicy/0.00',
      Addon:
        'Toppings:More fried wonton strips/0/1/2.00|More shrimps/0/1/3.00;Specials:Boiled egg/0/5/2.00',
      // 'Variant': '',
      // 'Addon': '',
      'Image Label (png, jpg, jpeg)': 'Fried_noodle',
      Printers: 'K1',
      'Stock Count': '10',
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      Name: 'Fried rice',
      Description: 'Special fried rice',
      SKU: 'FN002',
      Price: '14.99',
      'Prepare Time (seconds)': '900',
      Currency: 'MYR',
      Category: 'Rice',
      Outlet: allOutletsStr,
      Tax: `${taxName}:0.06`,
      // 'Variant': 'Size:Regular 0.00,Large 3.00;Flavour:Original 0.00,Spicy 0.00',
      // 'Addon': 'Toppings:More fried wonton strips 0 1 2.00,More shrimps 0 1 3.00;Specials:Boiled egg 0 5 2.00',
      Variant: '',
      Addon: '',
      'Image Label (png, jpg, jpeg)': 'Fried_rice',
      Printers: 'K2,Cashier',
      'Stock Count': '0',
    };
    excelTemplate.push(excelColumn2);

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;
  };

  const convertTemplateToExcelFormatMenu = async (tempFolderName, tempFolderPath) => {
    var excelTemplate = [];
    var excelTemplateComposite = [];

    var promises = [];

    // const tempFolderName = `koodoo-menu-export-${moment().format(
    //   'YYYY-MM-DD-HH-mm-ss',
    // )}`;

    // const tempFolderName = paramFolderName;

    // const tempFolderPath = `${Platform.OS === 'ios'
    //   ? RNFS.DocumentDirectoryPath
    //   : RNFS.DownloadDirectoryPath
    //   }/${tempFolderName}`;

    let exists = await RNFS.exists(tempFolderPath);
    if (exists) {
      // exists call delete
      await RNFS.unlink(tempFolderPath);
      // console.log('Previous Temp File Deleted', tempFolderPath);
    } else {
      // console.log('Previous Temp File Not Available', tempFolderPath);
    }

    RNFS.mkdir(tempFolderPath);

    var allOutletsStr = allOutlets.map((item) => item.name).join(';');

    // var taxName = currOutletTaxes[0].name;
    var taxName = 'SST';

    for (let i = 0; i < outletItems.length; i++) {
      const outletItem = outletItems[i];

      const foundCategory = outletCategories.find(category => {
        if (category.uniqueId === outletItem.categoryId) {
          return true;
        }
      });

      let variantStr = '';
      let addonStr = '';
      const foundVariantAddonList = allOutletsItemAddOnDict[outletItem.uniqueId];

      if (foundVariantAddonList) {
        for (let j = 0; j < foundVariantAddonList.length; j++) {
          const variantAddon = foundVariantAddonList[j];

          let isVariant = false;
          if (variantAddon.minSelect !== undefined && variantAddon.maxSelect !== undefined) {
            isVariant = true;
          }

          const variantAddonChoiceList = allOutletsItemAddOnChoiceDict[variantAddon.uniqueId];

          if (variantAddonChoiceList && variantAddonChoiceList.length > 0) {
            if (isVariant) {
              variantStr += `${variantAddon.name}:`;
            }
            else {
              addonStr += `${variantAddon.name}:`;
            }

            for (let k = 0; k < variantAddonChoiceList.length; k++) {
              const variantAddonChoice = variantAddonChoiceList[k];

              if (isVariant) {
                variantStr += `${variantAddonChoice.name}/${variantAddonChoice.price.toFixed(2)}`;
              }
              else {
                addonStr += `${variantAddonChoice.name}/${variantAddonChoice.minSelect.toFixed(0)}/${variantAddonChoice.maxSelect.toFixed(0)}/${variantAddonChoice.price.toFixed(2)}`;
              }

              if (isVariant) {
                if (k === variantAddonChoiceList.length - 1) {
                  variantStr += `;`;
                }
                else {
                  variantStr += `|`;
                }
              }
              else if (k === variantAddonChoiceList.length - 1) {
                addonStr += `;`;
              }
              else {
                addonStr += `|`;
              }
            }
          }
        }
      }

      var excelColumn = {
        Name: `${outletItem.name} *`,
        Description: outletItem.description,
        SKU: outletItem.skuMerchant,
        Price: outletItem.price,
        'Prepare Time (seconds)': '900',
        Currency: 'MYR',
        Category: foundCategory && foundCategory.name ? `${foundCategory.name} *` : '',
        Outlet: allOutletsStr,
        Tax: `${taxName}:0.06`,
        Variant: variantStr,
        Addon: addonStr,
        'Image Label (png, jpg, jpeg)': `${outletItem.uniqueId}`,
        Printers: outletItem.printerAreaList.join(`,`),
        'Stock Count': outletItem.stockCount ? outletItem.stockCount.toFixed(0) : '',
      };

      excelTemplate.push(excelColumn);

      //////////////////////////////////////////

      // if (outletItem.image) {
      //   const tempImageFolderName = `images/${outletItem.uniqueId}`;
      //   const tempImageFolderPath = `${Platform.OS === 'ios'
      //     ? RNFS.DocumentDirectoryPath
      //     : RNFS.DownloadDirectoryPath
      //     }/${tempFolderName}/${tempImageFolderName}`;

      //   RNFS.mkdir(tempImageFolderPath);

      //   var templateImageUrl = '';

      //   // await new Promise((resolve, reject) => {
      //   //   if (
      //   //     outletItem.image.startsWith('http') ||
      //   //     outletItem.image.startsWith('file')
      //   //   ) {
      //   //     templateImageUrl = outletItem.image;

      //   //     resolve();
      //   //   } else {
      //   //     getImageFromFirebaseStorage(outletItem.image, (parsedUrl) => {
      //   //       templateImageUrl = parsedUrl;

      //   //       resolve();
      //   //     });
      //   //   }
      //   // });

      //   templateImageUrl = outletItem.image;

      //   var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
      //   tempImageFileName = tempImageFileName.split('?')[0];

      //   const tempImageFilePath = `${Platform.OS === 'ios'
      //     ? RNFS.DocumentDirectoryPath
      //     : RNFS.DownloadDirectoryPath
      //     }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

      //   const downloadJob = RNFS.downloadFile({
      //     fromUrl: templateImageUrl,
      //     toFile: tempImageFilePath,
      //   });

      //   // await downloadJob.promise;
      //   promises.push(downloadJob.promise);
      // } else {
      //   // just do nothing

      //   excelColumn['Image Label (png, jpg, jpeg)'] = ``;
      // }
    }

    return await Promise.all(promises).then(() => {
      return excelTemplate;
    });
  };

  const convertTemplateToExcelFormatMenuComposite = async (tempFolderName, tempFolderPath) => {
    var excelTemplate = [];
    var excelTemplateComposite = [];

    var promises = [];

    // const tempFolderName = `koodoo-menu-export-${moment().format(
    //   'YYYY-MM-DD-HH-mm-ss',
    // )}`;

    // const tempFolderName = paramFolderName;

    // const tempFolderPath = `${Platform.OS === 'ios'
    //   ? RNFS.DocumentDirectoryPath
    //   : RNFS.DownloadDirectoryPath
    //   }/${tempFolderName}`;

    let exists = await RNFS.exists(tempFolderPath);
    if (exists) {
      // exists call delete
      await RNFS.unlink(tempFolderPath);
      // console.log('Previous Temp File Deleted', tempFolderPath);
    } else {
      // console.log('Previous Temp File Not Available', tempFolderPath);
    }

    RNFS.mkdir(tempFolderPath);

    var allOutletsStr = allOutlets.map((item) => item.name).join(';');

    // var taxName = currOutletTaxes[0].name;
    var taxName = 'SST';

    const outletItemsNew = outletItems;

    for (let i = 0; i < outletItemsNew.length; i++) {
      const outletItem = outletItemsNew[i];

      const foundCategory = outletCategories.find(category => {
        if (category.uniqueId === outletItem.categoryId) {
          return true;
        }
      });

      if (outletItem.stockLinkItems && outletItem.stockLinkItems.length > 0) {
        for (let j = 0; j < outletItem.stockLinkItems.length; j++) {
          const currStockLinkItem = outletItem.stockLinkItems[j];

          if (currStockLinkItem.outletSupplyItemId && currStockLinkItem.quantityUsage > 0) {
            // consume

            let osi = null;
            if (global.outletSupplyItemsDict[currStockLinkItem.outletSupplyItemId]) {
              osi = global.outletSupplyItemsDict[currStockLinkItem.outletSupplyItemId];
            }

            var excelColumn = {
              Name: `${outletItem.name} *`,
              Description: outletItem.description,
              SKU: outletItem.skuMerchant,
              // Price: outletItem.price,
              // 'Prepare Time (seconds)': '900',
              // Currency: 'MYR',
              Category: foundCategory && foundCategory.name ? `${foundCategory.name} *` : '',
              Outlet: allOutletsStr,
              // Tax: `${taxName}:0.06`,
              // Variant: variantStr,
              // Addon: addonStr,
              // 'Image Label (png, jpg, jpeg)': `${outletItem.uniqueId}`,
              // Printers: outletItem.printerAreaList.join(`,`),
              // 'Stock Count': outletItem.stockCount ? outletItem.stockCount.toFixed(0) : '',
            };

            excelTemplate.push(excelColumn);
          }
        }
      }

      var excelColumn = {
        Name: `${outletItem.name} *`,
        Description: outletItem.description,
        SKU: outletItem.skuMerchant,
        // Price: outletItem.price,
        // 'Prepare Time (seconds)': '900',
        // Currency: 'MYR',
        Category: foundCategory && foundCategory.name ? `${foundCategory.name} *` : '',
        Outlet: allOutletsStr,
        // Tax: `${taxName}:0.06`,
        // Variant: variantStr,
        // Addon: addonStr,
        // 'Image Label (png, jpg, jpeg)': `${outletItem.uniqueId}`,
        // Printers: outletItem.printerAreaList.join(`,`),
        // 'Stock Count': outletItem.stockCount ? outletItem.stockCount.toFixed(0) : '',
      };

      excelTemplate.push(excelColumn);

      //////////////////////////////////////////

      let variantStr = '';
      let addonStr = '';
      const foundVariantAddonList = allOutletsItemAddOnDict[outletItem.uniqueId];

      if (foundVariantAddonList) {
        for (let j = 0; j < foundVariantAddonList.length; j++) {
          const variantAddon = foundVariantAddonList[j];

          let isVariant = false;
          if (variantAddon.minSelect !== undefined && variantAddon.maxSelect !== undefined) {
            isVariant = true;
          }

          const variantAddonChoiceList = allOutletsItemAddOnChoiceDict[variantAddon.uniqueId];

          if (variantAddonChoiceList && variantAddonChoiceList.length > 0) {
            if (isVariant) {
              variantStr += `${variantAddon.name}:`;
            }
            else {
              addonStr += `${variantAddon.name}:`;
            }

            for (let k = 0; k < variantAddonChoiceList.length; k++) {
              const variantAddonChoice = variantAddonChoiceList[k];

              if (isVariant) {
                variantStr += `${variantAddonChoice.name}/${variantAddonChoice.price.toFixed(2)}`;
              }
              else {
                addonStr += `${variantAddonChoice.name}/${variantAddonChoice.minSelect.toFixed(0)}/${variantAddonChoice.maxSelect.toFixed(0)}/${variantAddonChoice.price.toFixed(2)}`;
              }

              if (isVariant) {
                if (k === variantAddonChoiceList.length - 1) {
                  variantStr += `;`;
                }
                else {
                  variantStr += `|`;
                }
              }
              else if (k === variantAddonChoiceList.length - 1) {
                addonStr += `;`;
              }
              else {
                addonStr += `|`;
              }
            }
          }
        }
      }

      //////////////////////////////////////////

      // if (outletItem.image) {
      //   const tempImageFolderName = `images/${outletItem.uniqueId}`;
      //   const tempImageFolderPath = `${Platform.OS === 'ios'
      //     ? RNFS.DocumentDirectoryPath
      //     : RNFS.DownloadDirectoryPath
      //     }/${tempFolderName}/${tempImageFolderName}`;

      //   RNFS.mkdir(tempImageFolderPath);

      //   var templateImageUrl = '';

      //   // await new Promise((resolve, reject) => {
      //   //   if (
      //   //     outletItem.image.startsWith('http') ||
      //   //     outletItem.image.startsWith('file')
      //   //   ) {
      //   //     templateImageUrl = outletItem.image;

      //   //     resolve();
      //   //   } else {
      //   //     getImageFromFirebaseStorage(outletItem.image, (parsedUrl) => {
      //   //       templateImageUrl = parsedUrl;

      //   //       resolve();
      //   //     });
      //   //   }
      //   // });

      //   templateImageUrl = outletItem.image;

      //   var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
      //   tempImageFileName = tempImageFileName.split('?')[0];

      //   const tempImageFilePath = `${Platform.OS === 'ios'
      //     ? RNFS.DocumentDirectoryPath
      //     : RNFS.DownloadDirectoryPath
      //     }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

      //   const downloadJob = RNFS.downloadFile({
      //     fromUrl: templateImageUrl,
      //     toFile: tempImageFilePath,
      //   });

      //   // await downloadJob.promise;
      //   promises.push(downloadJob.promise);
      // } else {
      //   // just do nothing

      //   excelColumn['Image Label (png, jpg, jpeg)'] = ``;
      // }
    }

    return await Promise.all(promises).then(() => {
      return excelTemplate;
    });
  };

  const exportTemplate = async () => {
    try {
      const excelTemplate = convertTemplateToExcelFormat();

      const tempFolderName = `koodoo-product-template-${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}`;
      const tempFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}`;

      const tempImageFolderName = 'images/Fried_noodle';
      const tempImageFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName}`;

      const tempImageFolderName2 = 'images/Fried_rice';
      const tempImageFolderPath2 = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName2}`;

      let exists = await RNFS.exists(tempFolderPath);
      if (exists) {
        // exists call delete
        await RNFS.unlink(tempFolderPath);
        // console.log('Previous Temp File Deleted', tempFolderPath);
      } else {
        // console.log('Previous Temp File Not Available', tempFolderPath);
      }

      RNFS.mkdir(tempFolderPath);
      RNFS.mkdir(tempImageFolderPath);
      RNFS.mkdir(tempImageFolderPath2);

      var templateImageUrl = '';

      // download merchant logo as example image file

      if (merchantLogo) {
        await new Promise((resolve, reject) => {
          if (
            merchantLogo.startsWith('http') ||
            merchantLogo.startsWith('file')
          ) {
            templateImageUrl = merchantLogo;

            resolve();
          } else {
            getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
              templateImageUrl = parsedUrl;

              resolve();
            });
          }
        });

        var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
        tempImageFileName = tempImageFileName.split('?')[0];

        const tempImageFilePath = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

        const downloadJob = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath,
        });

        await downloadJob.promise;

        const tempImageFilePath2 = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;

        const downloadJob2 = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath2,
        });

        await downloadJob2.promise;

        // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        var excelFile = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/koodoo-product-template.xlsx`;
        var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
        var excelWorkBook = XLSX.utils.book_new();

        excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

        XLSX.utils.book_append_sheet(
          excelWorkBook,
          excelWorkSheet,
          'Product Template',
        );

        const workBookData = XLSX.write(excelWorkBook, {
          type: 'binary',
          bookType: 'xlsx',
        });

        RNFS.writeFile(excelFile, workBookData, 'ascii')
          .then(async (success) => {
            // console.log(`wrote file ${excelFile}`);

            // zip the folder

            const tempZipPath = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : RNFS.DownloadDirectoryPath
              }/${tempFolderName}.zip`;

            let exists = await RNFS.exists(tempZipPath);
            if (exists) {
              // exists call delete
              await RNFS.unlink(tempZipPath);
              // console.log('Previous Zip File Deleted');
            } else {
              // console.log('Previous Zip File Not Available');
            }

            zip(tempFolderPath, tempZipPath)
              .then(async (path) => {
                // console.log(`zip completed at ${path}`);

                let exists = await RNFS.exists(tempFolderPath);
                if (exists) {
                  // exists call delete
                  await RNFS.unlink(tempFolderPath);
                  // console.log('Clean Temp folder File Deleted');
                } else {
                  // console.log('Clean Temp folder File Not Available');
                }

                Alert.alert(
                  'Success',
                  `Sent to ${tempZipPath}`,
                  [{ text: 'OK', onPress: () => { } }],
                  { cancelable: false },
                );
              })
              .catch((error) => {
                console.error(error);

                Alert.alert(
                  'Error',
                  'Failed to export template \nTry deleting the old file and try again',
                );
              });
          })
          .catch((err) => {
            // console.log(err.message);

            Alert.alert(
              'Error',
              'Failed to export template \nTry deleting the old file and try again',
            );
          });
      } else {
        Alert.alert(
          'Info',
          'Please set the merchant logo first before proceed.',
        );
      }
    } catch (ex) {
      console.error(ex);

      Alert.alert(
        'Error',
        'Failed to export template \nTry deleting the old file and try again',
      );
    }
  };

  const exportTemplateMenu = async () => {
    try {
      const tempFolderName = `koodoo-product-template-${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}`;

      const tempFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}`;

      const excelTemplate = await convertTemplateToExcelFormatMenu(tempFolderName, tempFolderPath);

      // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
      var excelFile = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/koodoo-product-template.xlsx`;

      console.log(excelTemplate);

      var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
      var excelWorkBook = XLSX.utils.book_new();

      excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

      XLSX.utils.book_append_sheet(
        excelWorkBook,
        excelWorkSheet,
        'Product Template',
      );

      const workBookData = XLSX.write(excelWorkBook, {
        type: 'binary',
        bookType: 'xlsx',
      });

      RNFS.writeFile(excelFile, workBookData, 'ascii')
        .then(async (success) => {
          // console.log(`wrote file ${excelFile}`);

          // zip the folder

          const tempZipPath = `${Platform.OS === 'ios'
            ? RNFS.DocumentDirectoryPath
            : RNFS.DownloadDirectoryPath
            }/${tempFolderName}.zip`;

          let exists = await RNFS.exists(tempZipPath);
          if (exists) {
            // exists call delete
            await RNFS.unlink(tempZipPath);
            // console.log('Previous Zip File Deleted');
          } else {
            // console.log('Previous Zip File Not Available');
          }

          zip(tempFolderPath, tempZipPath)
            .then(async (path) => {
              // console.log(`zip completed at ${path}`);

              let exists = await RNFS.exists(tempFolderPath);
              if (exists) {
                // exists call delete
                await RNFS.unlink(tempFolderPath);
                // console.log('Clean Temp folder File Deleted');
              } else {
                // console.log('Clean Temp folder File Not Available');
              }

              Alert.alert(
                'Success',
                `Sent to ${tempZipPath}`,
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
            })
            .catch((error) => {
              console.error(error);

              Alert.alert('Error', ex);

              // Alert.alert(
              //   'Error',
              //   'Failed to export template \nTry deleting the old file and try again',
              // );
            });
        })
        .catch((err) => {
          // console.log(err.message);

          Alert.alert('Error', ex);

          // Alert.alert(
          //   'Error',
          //   'Failed to export template \nTry deleting the old file and try again',
          // );
        });

    } catch (ex) {
      console.error(ex);

      Alert.alert('Error', ex);

      // Alert.alert(
      //   'Error',
      //   'Failed to export template \nTry deleting the old file and try again',
      // );
    }
  };

  const importTemplateData = async () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    try {
      var res = null;
      if (Platform.OS === 'ios') {
        res = await DocumentPicker.pick({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }
      else {
        res = await DocumentPicker.pickSingle({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }
      // console.log('res');
      // console.log(res);

      // RNFetchBlob.fs.readFile(res.uri, 'base64').then(async data => {
      //   // upload to firebase storage

      //   var referenceId = uuidv4();
      //   var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      //   await uploadFileToFirebaseStorage(data, referencePath);
      // });

      var referenceId = uuidv4();
      var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      var translatedPath = '';
      if (Platform.OS === 'ios') {
        translatedPath = await getPathForFirebaseStorageFromBlob(res[0]);
      }
      else {
        translatedPath = await getPathForFirebaseStorageFromBlob(res);
      }

      // console.log('translatedPath');
      // console.log(translatedPath);

      if (Platform.OS === 'ios') {
        if (translatedPath && translatedPath.fileCopyUri) {
          translatedPath = translatedPath.fileCopyUri;
        }
      }

      await uploadFileToFirebaseStorage(translatedPath, referencePath);

      const body = {
        zipId: referenceId,
        zipPath: referencePath,

        userId: firebaseUid,
        merchantId,
        outletId: currOutletId,
      };

      ApiClient.POST(API.batchCreateOutletItems, body)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Submitted to upload queue, we will notify you once the process is done',
            );
          } else {
            Alert.alert('Error', 'Failed to import the data');
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        })
        .catch((err) => {
          // console.log(err);

          Alert.alert('Error', 'Failed to import the data');

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    } catch (err) {
      CommonStore.update((s) => {
        s.isLoading = false;
      });

      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        console.error(err);

        Alert.alert('Error', 'Failed to import the data');
      }
    }
  };

  const deleteOutletItemCategory = () => {
    var body = {
      outletItem: outletItemsUnsorted.filter((item) => item.categoryId === categoryUniqueId),
      allOutletsItemAddOnDict,
      allOutletsItemAddOnChoiceDict,

      categoryName,
      outletId: currOutletId,
      hideInOrderTypes,

      uniqueId: selectedOutletCategoryEdit.uniqueId,
    };

    console.log('deleting body', body);

    APILocal.deleteOutletItemCategory({
      body,
      uid: userId,
    })
      .then((result) => {
        if (result && result.status === 'success') {
          setTimeout(() => {
            Alert.alert(
              'Success',
              'Product category has been removed',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // props.navigation.goBack()
                    // setEditPurchase(false);
                    // setAddPurchase(false);
                    // setPurchaseOrder(true);
                  },
                },
              ],
              { cancelable: false },
            );
          }, 500);

          setDeleteModal(false);
          setPurchaseOrder(true);
          setEditPurchase(false);
          setAddPurchase(false);
        } else {
          Alert.alert('Error', 'Failed to remove Product category');
        }
      });
  };

  // image handle
  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        if (responseParsed.width > 512 || responseParsed.height > 512) {
          Alert.alert(
            "Image's width and height must be same or less than 512x512.",
          );
          return;
        }

        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  };

  //Greg - Sorting

  const userId = UserStore.useState((s) => s.firebaseUid);

  const [sortingHelp, setSortingHelp] = useState(false);
  const [isSavingList, setIsSavingList] = useState(false);
  const [draggingScroll, setDraggingScroll] = useState(true);
  const [changeLayout, setChangeLayout] = useState(false);
  const [startDragging, setStartDragging] = useState(false);
  var sortingTemp = {}

  const [sortedCategory, setSortedCategory] = useState([]);
  const [sortedCategoryOrderIndexIdList, setSortedCategoryOrderIndexIdList] = useState({});
  const [originalSortedCategoryOrderIndexIdList, setOriginalSortedCategoryOrderIndexIdList] = useState({});

  useEffect(() => {
    if (isMounted) {
      var filteredOutletCategorySort = outletCategories.slice()

      filteredOutletCategorySort.sort((a, b) => {
        return (
          (sortedCategoryOrderIndexIdList[a.uniqueId]
            ? sortedCategoryOrderIndexIdList[a.uniqueId]
            : a.orderIndex
              ? a.orderIndex
              : outletCategories.length) -
          (sortedCategoryOrderIndexIdList[b.uniqueId]
            ? sortedCategoryOrderIndexIdList[b.uniqueId]
            : b.orderIndex
              ? b.orderIndex
              : outletCategories.length)
        );
      });

      filteredOutletCategorySort = filteredOutletCategorySort.map(category => {
        var totalItemQuantity = 0;

        for (var i = 0; i < outletItems.length; i++) {
          //// console.log('hello')
          if (outletItems[i].categoryId === category.uniqueId) {
            //// console.log('123hihi')
            totalItemQuantity += 1;
          }
        }

        return {
          ...category,
          totalItemQuantity,
        };
      });

      if (search !== '') {
        const searchLowerCase = search.toLowerCase();

        filteredOutletCategorySort = filteredOutletCategorySort.filter((item) => {
          if (
            item.name.toLowerCase().includes(searchLowerCase)
          ) {
            return true;
          } else if (
            item.totalItemQuantity.toString().includes(searchLowerCase)
          ) {
            return true;
          } else {
            return false;
          }
        });
      }

      setSortedCategory(filteredOutletCategorySort);
    }
  }, [
    outletCategories,
    sortedCategoryOrderIndexIdList,

    outletItems,

    search,

    isMounted,
  ]);

  const updateOutletCategoryOrderIndex = () => {
    var sortedCategoryOrderIndexList = Object.entries(sortedCategoryOrderIndexIdList).map(
      ([key, value]) => ({
        outletCategoryId: key,
        orderIndex: value,
      }),
    );

    var body = {
      sortedCategoryOrderIndexIdList: sortedCategoryOrderIndexList,
    };

    // console.log(body);

    // CommonStore.update((s) => {
    //   s.isLoading = true;
    // });

    APILocal.updateOutletCategoryOrderIndex({ body, uid: userId })
      // ApiClient.POST(API.updateOutletItemOrderIndex, body)
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Menu has been updated.');
        } else {
          Alert.alert('Error', 'Failed to update the menu.');
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });

        setSortedCategoryOrderIndexIdList({});
      });
    setChangeLayout(false);
  };

  //Greg - Hide Category

  const [hideModal, setHideModal] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState(null);
  const [hideHelp, setHideHelp] = useState(false);

  const [customTimeSelectStart, setCustomTimeSelectStart] = useState(false);
  const [customTimeSelectEnd, setCustomTimeSelectEnd] = useState(false);
  const hideCategoryChoice = [
    // {
    //   label: '2 HOURS',
    //   value: 'HOURS_2',
    // },
    // {
    //   label: '6 HOURS',
    //   value: 'HOURS_6',
    // },
    // {
    //   label: 'END OF THE DAY',
    //   value: 'END_DAY',
    // },
    // {
    //   label: 'CUSTOM TIME',
    //   value: 'CUSTOM_TIME',
    // },
    // {
    //   label: 'FOREVER',
    //   value: 'FOREVER',
    // },
  ];

  const [customStartTime, setCustomStartTime] = useState(moment().startOf('day').toDate());
  const [customEndTime, setCustomEndTime] = useState(moment().endOf('day').toDate());

  const [isHidden, setIsHidden] = useState(false);

  //Greg - Multi Delete

  const role = UserStore.useState((s) => s.role);

  const [deleteOn, setDeleteOn] = useState(false);
  const [deleteHelp, setDeleteHelp] = useState(false);
  const [deleteList, setDeleteList] = useState([]);

  const isChecked = (item) => {
    var selectedDeleteItems = sortedCategory.filter(
      (o) => o.uniqueId === item.uniqueId,
    );

    // setDeleteList2(...deleteList2, selectedDeleteItems2)
    console.log('D ITEM', selectedDeleteItems);
    const convertToObj = sortedCategory.find((o) => {
      return selectedDeleteItems.map(item => item.uniqueId).includes(o.uniqueId);
    });
    console.log('convertToObj', convertToObj);

    const itemExists = deleteList.find((o) => {
      return selectedDeleteItems.map(item => item.uniqueId).includes(o.uniqueId);
    });

    if (itemExists) {
      setDeleteList(deleteList.filter((item) => item.uniqueId !== itemExists.uniqueId));
      // setDeleteList({
      //   ...deleteList,
      //   [item.uniqueId]: deleteList[item.uniqueId] ? false : true
      // });
    }
    else {
      setDeleteList([...deleteList, convertToObj]);
    }
  };

  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);

  const allOutletsItemAddOnDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnDict,
  );

  const allOutletsItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceDict,
  );

  // useEffect(() => {
  //   console.log('deleteList', deleteList);
  // }, [deleteList]);

  const deleteSelected = () => {
    for (var i = 0; i < deleteList.length; i++) {
      console.log('IN LOOP', i)

      var body = {
        outletItem: outletItemsUnsorted.filter((item) => item.categoryId === deleteList[i].uniqueId),
        allOutletsItemAddOnDict,
        allOutletsItemAddOnChoiceDict,

        categoryName: deleteList[i].name,
        outletId: deleteList[i].outletId,
        hideInOrderTypes: deleteList[i].hideInOrderTypes,

        uniqueId: deleteList[i].uniqueId,
      };

      console.log('deleting body', body)

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      // ApiClient.POST(API.deleteOutletItemCategory, body)
      APILocal.deleteOutletItemCategory({
        body,
        uid: userId,
      });
    }

    setTimeout(() => {
      Alert.alert('Success', `${deleteList.length} ${deleteList.length > 1 ? 'categories' : 'category'} have been deleted`);
    }, 1000);

    CommonStore.update((s) => {
      s.isLoading = false;
    });

    setDeleteList([]);
    setDeleteOn(false);
  }

  // 2025-02-18 - publish items to other delivery partner (grabfood, etc)

  const publishItemsToOD = () => {
    if (currOutlet.odGrabMID) {
      var body = {
        odGrabMID: currOutlet.odGrabMID,
      };

      // console.log(body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      ApiClient.POST(API.grabUpdateMenuNotification, body)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert('Success', 'Menu has been published.');
          } else {
            Alert.alert('Error', 'Failed to publish the menu.');
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
    else {
      Alert.alert('Info', 'Please contact your account manager to link your Grab Merchant ID first.');
    }
  };

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            switchMerchant
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={2}
            expandProduct
          />
        </View> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          scrollEnabled={!switchMerchant ? (changeLayout ? false : true) : true}>
          {/* <ScrollView horizontal={true}> */}
          <View
            style={[
              styles.content,
              {
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
              },
              Platform.OS == 'ios' ?
                {
                  paddingTop: -16,
                  marginTop: 16,
                } : {},
            ]}>
            <ScrollView
              scrollEnabled={!switchMerchant ? (changeLayout ? false : true) : true}
              nestedScrollEnabled>
              <DateTimePickerModal
                locale="en_GB"
                isVisible={showEffectiveStartTimePicker}
                mode={'time'}
                date={
                  effectiveStartTime
                }
                onConfirm={(dt) => {
                  setShowEffectiveStartTimePicker(false);
                  setShowEffectiveEndTimePicker(false);
                  setEffectiveStartTime(moment(dt).toDate());
                }}
                onCancel={() => {
                  setShowEffectiveStartTimePicker(false);
                }}
              />

              <DateTimePickerModal
                locale="en_GB"
                isVisible={showEffectiveEndTimePicker}
                mode={'time'}
                date={
                  effectiveEndTime
                }
                onConfirm={(dt) => {
                  setShowEffectiveStartTimePicker(false);
                  setShowEffectiveEndTimePicker(false);
                  setEffectiveEndTime(moment(dt).toDate());
                }}
                onCancel={() => {
                  setShowEffectiveEndTimePicker(false);
                }}
              />
              <ModalView
                style={
                  {
                    // flex: 1
                  }
                }
                visible={importModal}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'slide'}>
                <View style={styles.modalContainer}>
                  <View style={[styles.modalView, {
                    height: Dimensions.get('window').width * 0.2,
                    width: Dimensions.get('window').width * 0.3,

                    padding: Dimensions.get('window').width * 0.03,

                    ...getTransformForModalInsideNavigation(),
                  }]}>
                    <TouchableOpacity
                      style={[styles.closeButton, {
                        right: windowWidth * 0.02,
                        top: windowWidth * 0.02,
                      },]}
                      onPress={() => {
                        // setState({ changeTable: false });
                        setImportModal(false);
                      }}>
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View style={styles.modalTitle}>
                      <Text
                        style={
                          switchMerchant
                            ? {
                              fontFamily: 'NunitoSans-Bold',
                              textAlign: 'center',
                              fontSize: 16,
                            }
                            : [styles.modalTitleText]
                        }>
                        Upload Options
                      </Text>
                    </View>
                    <View
                      style={{
                        alignItems: 'center',
                        top: switchMerchant ? '20%' : '10%',
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 180 : 240,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        }}
                        onPress={() => {
                          importTemplateData();
                        }}
                        disabled={isLoading}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {isLoading ? 'Loading...' : 'UPLOAD TEMPLATE'}
                        </Text>

                        {isLoading ? (
                          <ActivityIndicator
                            style={{
                              marginLeft: 5,
                            }}
                            color={Colors.whiteColor}
                            size={'small'}
                          />
                        ) : (
                          <></>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 180 : 240,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={async () => {
                          // exportTemplate();
                          await exportTemplateMenu();
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          EXPORT TEMPLATE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>

              <View
                style={
                  switchMerchant
                    ? { width: windowWidth * 0.79 }
                    : { witdh: windowWidth * 0.87 }
                }>
                {!addPurchase ? (
                  <View
                    style={
                      switchMerchant
                        ? {
                          flexDirection: 'row',
                          marginBottom: Platform.OS == 'ios' ? 0 : 10,
                          justifyContent: 'flex-end',
                          width: '100%',
                        }
                        : {
                          flexDirection: 'row',
                          marginBottom: Platform.OS == 'ios' ? 0 : 10,
                          justifyContent: 'flex-end',
                          width: windowWidth * 0.87,
                          alignSelf: 'center',
                          marginHorizontal: 30,
                        }
                    }>
                    {/* <View
            style={{
              flexDirection: 'row',
              backgroundColor: '#ffffff',
              justifyContent: 'center',
              padding: 18,
            }}> */}

                    {/* </View> */}

                    <View style={{ flexDirection: 'row' }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 10,
                          }}
                          onPress={async () => {
                            // Update the outlet document with empty ID
                            await firestore().collection(Collections.Outlet).doc(currOutletId).update({
                              odGrabCatalogId: '',
                            });

                            publishItemsToOD();
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            {/* <Icon
                              name="upload"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            /> */}
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              PUBLISH
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 10,
                          }}
                          onPress={() => {
                            setImportModal(true);
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Icon
                              name="upload"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              BATCH UPLOAD
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 10,
                          }}
                          onPress={() => {
                            CommonStore.update((s) => {
                              s.selectedProductEdit = null;

                              s.timestamp = Date.now();
                            });

                            InteractionManager.runAfterInteractions(() => {
                              props.navigation.navigate('ProductAdd');
                            });
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <AntDesign
                              name="pluscircle"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              PRODUCT
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 10,
                          }}
                          onPress={() => {
                            CommonStore.update((s) => {
                              s.selectedOutletCategoryEdit = null;
                            });
                            setPurchaseOrder(false);
                            setAddPurchase(true);
                            setEditCategory(false);
                            //setNewCategoryName('');
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <AntDesign
                              name="pluscircle"
                              size={switchMerchant ? 10 : 20}
                              color={Colors.whiteColor}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CATEGORY
                            </Text>
                          </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            CommonStore.update((s) => {
                              s.selectedOutletCategoryEdit = null;

                              s.timestamp = Date.now;
                            });

                            navigation.navigate('Product');
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              ALL PRODUCTS
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ) : (
                  <></>
                )}
                {!addPurchase ? (
                  <View
                    style={
                      switchMerchant
                        ? {
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          marginTop: 10,
                        }
                        : {
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          marginTop: 20,
                          width: windowWidth * 0.87,
                          alignSelf: 'center',
                          marginHorizontal: 30,
                        }
                    }>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingTop: 10,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {/* {orderList.length} */}
                        {outletCategories.length}
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: windowWidth * 0.008,
                        }}>
                        {outletCategories.length > 1 ? 'Categories' : 'Category'}
                      </Text>
                    </View>
                    <View
                      style={[
                        { flexDirection: 'row' },
                        !isTablet()
                          ? {
                            marginLeft: 0,
                          }
                          : {},
                      ]}>
                      {changeLayout ?
                        <View style={{ marginRight: 10, marginTop: 10 }}>
                          <Tooltip
                            isVisible={sortingHelp}
                            content={<Text style={{
                              //fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                              Press and hold to drag a Category
                            </Text>
                            }
                            placement="top"
                            onClose={() => setSortingHelp(false)}
                          >
                            <TouchableOpacity
                              onPress={() => setSortingHelp(true)}
                              style={styles.touchable}>
                              <Icon
                                name="help-circle"
                                size={switchMerchant ? 10 : 20}
                                style={{ color: Colors.primaryColor }}
                              />
                            </TouchableOpacity>
                          </Tooltip>
                        </View>
                        : <></>}
                      {deleteOn ?
                        <View style={{ marginRight: 10, marginTop: 10 }}>
                          <Tooltip
                            isVisible={deleteHelp}
                            content={
                              <Text style={{
                                //fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                                Select a Category to delete
                              </Text>
                            }
                            placement="top"
                            onClose={() => setDeleteHelp(false)}
                          >
                            <TouchableOpacity
                              onPress={() => setDeleteHelp(true)}
                              style={styles.touchable}>
                              <Icon
                                name="help-circle"
                                size={switchMerchant ? 10 : 20}
                                style={{ color: Colors.primaryColor }}
                              />
                            </TouchableOpacity>
                          </Tooltip>
                        </View>
                        : <></>}
                      {deleteOn || changeLayout ?
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.fieldtBgColor,
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,

                            marginRight: 10,
                          }}
                          onPress={() => {
                            if (deleteOn) {
                              setDeleteOn(false);
                              setDeleteList([]);
                            }
                            else {
                              setSortedCategoryOrderIndexIdList({ ...originalSortedCategoryOrderIndexIdList });
                              setChangeLayout(false);
                            }
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                            <Text
                              style={{
                                color: Colors.descriptionColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CANCEL
                            </Text>
                          </View>
                        </TouchableOpacity>
                        : <></>}

                      {(role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) ?
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: changeLayout ? 'grey' : '#eb3446',
                            backgroundColor: changeLayout ? 'grey' : '#eb3446',
                            borderRadius: 5,
                            //width: 160,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,

                            marginRight: 10,
                          }}
                          disabled={changeLayout}
                          onPress={() => {
                            if (!deleteOn) {
                              setDeleteOn(!deleteOn);
                            }
                            else if (deleteList.length <= 0) {
                              Alert.alert('Error', 'No items selected')
                            }
                            else {
                              Alert.alert('Warning', `Are you sure you want to delete ${deleteList.length} item(s)`,
                                [
                                  {
                                    text: 'Yes',
                                    onPress: () => {
                                      Alert.alert('Warning', `Please note that this procedure cannot be undone. \nWould you like to proceed?`,
                                        [
                                          {
                                            text: 'Yes',
                                            onPress: () => {
                                              deleteSelected();
                                            },
                                          },
                                          {
                                            text: 'No',
                                            onPress: () => { },
                                          },
                                        ],
                                        { cancelable: true },
                                      );
                                    },
                                  },
                                  {
                                    text: 'No',
                                    onPress: () => { },
                                  },
                                ],
                                { cancelable: true },
                              );
                            }
                          }}>
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Icon name="trash-2" size={20} color={Colors.whiteColor} />
                          </View>
                        </TouchableOpacity>
                        : <></>}

                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: deleteOn ? 'grey' : Colors.primaryColor,
                          backgroundColor: deleteOn ? 'grey' : '#4E9F7D',
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                          marginRight: 10,
                        }}
                        disabled={deleteOn}
                        onPress={() => {
                          if (!changeLayout) {
                            setOriginalSortedCategoryOrderIndexIdList({ ...sortedCategoryOrderIndexIdList });
                          }
                          
                          setChangeLayout(true);

                          if (changeLayout) {
                            updateOutletCategoryOrderIndex();
                          }
                        }}>
                        <View
                          style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            <MaterialIcons name="sort" size={20} color={Colors.whiteColor} />
                          </Text>
                        </View>
                      </TouchableOpacity>
                      <View
                        style={{
                          width: switchMerchant ? 200 : 250,
                          height: switchMerchant ? 35 : 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          //marginRight: switchMerchant ? 0 : 0,
                        }}>
                        <Icon
                          name="search"
                          size={switchMerchant ? 13 : 18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                        <TextInput
                          editable={!loading}
                          underlineColorAndroid={Colors.whiteColor}
                          style={{
                            width: switchMerchant ? 180 : 220,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                            height: 45,
                          }}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          onChangeText={(text) => {
                            setSearch(text);
                          }}
                          value={search}
                        />
                      </View>
                    </View>
                  </View>
                ) : (
                  <></>
                )}
              </View>

              {purchaseOrder ? (
                <View style={{ paddingTop: 0 }}>
                  {/* <View> */}

                  <View
                    style={
                      [
                        switchMerchant ? styles.list1_PhoneAdjustment : styles.list1,
                        {
                          height: Dimensions.get('window').height * 0.62,
                          width: Dimensions.get('window').width * 0.87,
                        }
                      ]
                    }
                  // style={{
                  //   borderRadius: 5,
                  //   borderWidth: 1, backgroundColor: Colors.whiteColor, borderColor: '#E5E5E5', marginVertical: 10,     shadowOpacity: 0,
                  //               shadowColor: '#000',
                  //               shadowOffset: {
                  //                 width: 0,
                  //                 height: 2,
                  //               },
                  //               shadowOpacity: 0.22,
                  //               shadowRadius: 3.22,
                  //               elevation: 3,

                  //           }}
                  >
                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        flexDirection: 'row',
                        paddingVertical: 20,
                        paddingHorizontal: 20,
                        marginTop: switchMerchant ? -20 : -10,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        borderRadius: 5,
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '21%',
                          alignSelf: 'center',
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Category Name
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '21%',
                          alignSelf: 'center',
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Do Not Display
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '18%',
                          alignSelf: 'center',
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Product Quantity
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '18%',
                          alignSelf: 'center',
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Created Date
                      </Text>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          width: '19%',
                          alignSelf: 'center',
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Modified Date
                      </Text>
                    </View>

                    {changeLayout ?
                      <View style={{ paddingBottom: 55, }}>
                        <NestableScrollContainer>
                          <NestableDraggableFlatList
                            data={sortedCategory.map(category => {
                              var totalItemQuantity = 0;

                              for (var i = 0; i < outletItems.length; i++) {
                                //// console.log('hello')
                                if (outletItems[i].categoryId === category.uniqueId) {
                                  //// console.log('123hihi')
                                  totalItemQuantity += 1;
                                }
                              }

                              return {
                                ...category,
                                totalItemQuantity,
                              };
                            }).filter((item) => {
                              // var totalQuantity = 0;

                              // for (var i = 0; i < outletItems.length; i++) {
                              //   //// console.log('hello')
                              //   if (outletItems[i].categoryId === item.uniqueId) {
                              //     //// console.log('123hihi')
                              //     totalQuantity += 1;
                              //   }
                              // }

                              if (search !== '') {
                                const searchLowerCase = search.toLowerCase();

                                if (
                                  item.name.toLowerCase().includes(searchLowerCase)
                                ) {
                                  return true;
                                } else if (
                                  item.totalItemQuantity.toString().includes(searchLowerCase)
                                ) {
                                  return true;
                                } else {
                                  return false;
                                }
                              } else {
                                return true;
                              }
                            })}
                            /* extraData={outletCategories.filter(item => {
                              if (search !== '') {
                                return item.name.toLowerCase().includes(search.toLowerCase());
                              }
                              else {
                                return true;
                              }
                            })} */
                            nestedScrollEnabled
                            renderItem={renderOrderItemSort}
                            keyExtractor={(item, index) =>
                              `draggable-category-${item.uniqueId}`
                            }
                            showsVerticalScrollIndicator={false}
                            //scrollEnabled={true}
                            //keyboardShouldPersistTaps="handled"
                            maxToRenderPerBatch={1}
                            // onDragBegin={() => {
                            //   setDraggingScroll(false);
                            //   setStartDragging(true);
                            // }}
                            onDragEnd={({ data, from, to }) => {
                              setStartDragging(false);
                              setDraggingScroll(true);

                              sortingTemp = {
                                ...sortedCategoryOrderIndexIdList,
                              };

                              for (var i = 0; i < data.length; i++) {
                                sortingTemp = {
                                  ...sortingTemp,
                                  [data[i].uniqueId]: i + 1,
                                };
                              }

                              setIsSavingList(true);
                              setSortedCategoryOrderIndexIdList(sortingTemp)
                            }}
                          />
                        </NestableScrollContainer>
                      </View>
                      :
                      <ScrollView
                        scrollEnabled={!switchMerchant}
                        nestedScrollEnabled>
                        {deleteOn ?
                          <FlatList
                            data={
                              sortedCategory
                              // sortedCategory.map(category => {
                              //   var totalItemQuantity = 0;

                              //   for (var i = 0; i < outletItems.length; i++) {
                              //     //// console.log('hello')
                              //     if (outletItems[i].categoryId === category.uniqueId) {
                              //       //// console.log('123hihi')
                              //       totalItemQuantity += 1;
                              //     }
                              //   }

                              //   return {
                              //     ...category,
                              //     totalItemQuantity,
                              //   };
                              // }).filter((item) => {
                              //   // var totalQuantity = 0;

                              //   // for (var i = 0; i < outletItems.length; i++) {
                              //   //   //// console.log('hello')
                              //   //   if (outletItems[i].categoryId === item.uniqueId) {
                              //   //     //// console.log('123hihi')
                              //   //     totalQuantity += 1;
                              //   //   }
                              //   // }

                              //   if (search !== '') {
                              //     const searchLowerCase = search.toLowerCase();

                              //     if (
                              //       item.name.toLowerCase().includes(searchLowerCase)
                              //     ) {
                              //       return true;
                              //     } else if (
                              //       item.totalItemQuantity.toString().includes(searchLowerCase)
                              //     ) {
                              //       return true;
                              //     } else {
                              //       return false;
                              //     }
                              //   } else {
                              //     return true;
                              //   }
                              // })
                            }
                            /* extraData={outletCategories.filter(item => {
                              if (search !== '') {
                                return item.name.toLowerCase().includes(search.toLowerCase());
                              }
                              else {
                                return true;
                              }
                            })} */
                            nestedScrollEnabled
                            renderItem={renderOrderItemDelete}
                            keyExtractor={(item, index) => String(index)}
                          />
                          :
                          <FlatList
                            data={
                              sortedCategory
                              // sortedCategory.map(category => {
                              //   var totalItemQuantity = 0;

                              //   for (var i = 0; i < outletItems.length; i++) {
                              //     //// console.log('hello')
                              //     if (outletItems[i].categoryId === category.uniqueId) {
                              //       //// console.log('123hihi')
                              //       totalItemQuantity += 1;
                              //     }
                              //   }

                              //   return {
                              //     ...category,
                              //     totalItemQuantity,
                              //   };
                              // }).filter((item) => {
                              //   // var totalQuantity = 0;

                              //   // for (var i = 0; i < outletItems.length; i++) {
                              //   //   //// console.log('hello')
                              //   //   if (outletItems[i].categoryId === item.uniqueId) {
                              //   //     //// console.log('123hihi')
                              //   //     totalQuantity += 1;
                              //   //   }
                              //   // }

                              //   if (search !== '') {
                              //     const searchLowerCase = search.toLowerCase();

                              //     if (
                              //       item.name.toLowerCase().includes(searchLowerCase)
                              //     ) {
                              //       return true;
                              //     } else if (
                              //       item.totalItemQuantity.toString().includes(searchLowerCase)
                              //     ) {
                              //       return true;
                              //     } else {
                              //       return false;
                              //     }
                              //   } else {
                              //     return true;
                              //   }
                              // })
                            }
                            /* extraData={outletCategories.filter(item => {
                              if (search !== '') {
                                return item.name.toLowerCase().includes(search.toLowerCase());
                              }
                              else {
                                return true;
                              }
                            })} */
                            nestedScrollEnabled
                            renderItem={renderOrderItem}
                            keyExtractor={(item, index) => String(index)}
                          />
                        }

                        <View style={{ height: 100 }} />
                      </ScrollView>
                    }
                  </View>

                  {/* </View> */}
                </View>
              ) : null}

              {addPurchase ? (
                <View
                  style={{
                    height: switchMerchant
                      ? windowHeight - 100
                      : windowHeight - 200,
                    width: switchMerchant
                      ? windowWidth * 0.86
                      : windowWidth * 0.89,
                  }}>
                  <View>
                    <ModalView
                      supportedOrientations={['landscape', 'portrait']}
                      style={{ flex: 1 }}
                      visible={deleteModal}
                      transparent
                      animationType="slide">
                      <View style={styles.modalContainer}>
                        <View
                          style={[
                            styles.modalView1,
                            {
                              height: windowHeight * 0.25,

                              width: Dimensions.get('window').width * 0.4,
                              padding: Dimensions.get('window').width * 0.035,
                              paddingTop: Dimensions.get('window').width * 0.05,

                              ...getTransformForModalInsideNavigation(),
                            },
                          ]}>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setDeleteModal(false);
                            }}>
                            <AIcon
                              name="closecircle"
                              size={25}
                              color={Colors.fieldtTxtColor}
                            />
                          </TouchableOpacity>
                          <View style={{}}>
                            <Text
                              style={{
                                fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 18,
                              }}>
                              {/* Please Key In The Authorization Code */}
                              {`Are you sure you want to remove this category ${selectedOutletCategoryEdit
                                ? selectedOutletCategoryEdit.name
                                : ''
                                } ?`}
                            </Text>
                          </View>
                          {/* <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                      <Text style={{ fontWeight: '700', fontFamily: 'Nunitosans-Regular', fontSize: 16, }}>
                        Authorization Code:
                      </Text>
                      <TextInput
                        style={{
                          borderColor: '#E5E5E5',
                          borderWidth: 1,
                          borderRadius: 5,
                          marginLeft: 15,
                          width: 150,
                          height: 35,
                          padding: 5,
                        }}

                      />
                    </View> */}
                          <View style={{}}>
                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.tabRed,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onPress={() => {
                                deleteOutletItemCategory();
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'white',
                                  fontWeight: '500',
                                }}>
                                DELETE
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </ModalView>

                    <DateTimePickerModal
                      locale="en_GB"
                      isVisible={customTimeSelectStart}
                      mode={'datetime'}
                      date={
                        customStartTime
                      }
                      onConfirm={(dt) => {
                        setCustomTimeSelectStart(false);
                        setCustomTimeSelectEnd(true);
                        setCustomStartTime(moment(dt).toDate());
                      }}
                      onCancel={() => {
                        setCustomTimeSelectStart(false);
                      }}
                    />

                    <DateTimePickerModal
                      locale="en_GB"
                      isVisible={customTimeSelectEnd}
                      mode={'datetime'}
                      date={
                        customEndTime
                      }
                      onConfirm={(dt) => {
                        setCustomTimeSelectStart(false);
                        setCustomTimeSelectEnd(false);
                        setCustomEndTime(moment(dt).toDate());
                      }}
                      onCancel={() => {
                        setCustomTimeSelectEnd(false);
                      }}
                    />
                    <ModalView
                      supportedOrientations={['landscape', 'portrait']}
                      style={{ flex: 1 }}
                      visible={hideModal}
                      transparent
                      animationType="slide">
                      <View style={styles.modalContainer}>
                        <View
                          style={[
                            styles.modalView1,
                            {
                              height: windowHeight * 0.6,
                              width: Dimensions.get('window').width * 0.5,

                              padding: Dimensions.get('window').width * 0.035,
                              paddingTop: Dimensions.get('window').width * 0.05,

                              ...getTransformForModalInsideNavigation(),
                            },
                          ]}>
                          <Text
                            style={{
                              fontWeight: '700',
                              fontFamily: 'Nunitosans-Bold',
                              fontSize: 30,
                            }}>
                            {`${selectedOutletCategoryEdit
                              ? selectedOutletCategoryEdit.name
                              : ''
                              } is Out of Stock`}
                          </Text>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setHideModal(false);
                              setSelectedChoice(null);
                            }}>
                            <AIcon
                              name="closecircle"
                              size={25}
                              color={Colors.fieldtTxtColor}
                            />
                          </TouchableOpacity>
                          <View style={{}}>
                            <Text
                              style={{
                                //fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 14,
                                color: 'gray'
                              }}>
                              <Text>Please confirm that </Text>
                              <Text style={{ fontWeight: '700' }}>{selectedOutletCategoryEdit ? selectedOutletCategoryEdit.name : ''}</Text>
                              <Text> is out of stock</Text>
                            </Text>
                          </View>

                          <View style={{ marginTop: 20, justifyContent: 'flex-start', width: '100%', flexDirection: 'row' }}>
                            <Text
                              style={{
                                //fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 16,
                              }}>
                              Select your preferred time:
                            </Text>
                            <View style={{ marginLeft: 10, marginTop: 1, }}>
                              <Tooltip
                                isVisible={hideHelp}
                                content={<Text>This category will be hidden from users</Text>}
                                placement="top"
                                onClose={() => setHideHelp(false)}
                              >
                                <TouchableOpacity
                                  onPress={() => setHideHelp(true)}
                                  style={styles.touchable}>
                                  <Icon
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 20}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>

                          <View style={{
                            marginTop: 20,
                          }}>
                            <RadioForm formHorizontal animation>
                              {/* To create radio buttons, loop through your array of options */}
                              {hideCategoryChoice.map((obj, i) => (
                                <RadioButton labelHorizontal key={i}>
                                  <RadioButtonInput
                                    obj={obj}
                                    //index={i}
                                    isSelected={selectedChoice === obj.value}
                                    onPress={(item) => {
                                      setSelectedChoice(obj.value)

                                      if (item === 'CUSTOM_TIME') {
                                        setCustomTimeSelectStart(true);
                                      }
                                    }}
                                    borderWidth={1}
                                    buttonInnerColor={Colors.primaryColor}
                                    buttonOuterColor={'#000'}
                                    buttonSize={14}
                                    buttonOuterSize={22}
                                    buttonStyle={{}}
                                    buttonWrapStyle={{ marginLeft: Platform.OS == 'ios' ? 5 : 10 }}
                                    style={{ marginTop: 20 }}
                                  />
                                  <Text style={{
                                    fontSize: 14,
                                    color: 'black',
                                    fontFamily: 'Nunitosans-Regular',
                                    marginLeft: 5,
                                    marginTop: 1
                                  }}>
                                    {obj.label}
                                  </Text>
                                </RadioButton>
                              ))}
                            </RadioForm>
                          </View>

                          {selectedChoice === 'CUSTOM_TIME' ?
                            <View style={{
                              marginTop: 5,
                            }}>
                              <Text
                                style={{
                                  color: 'gray',
                                  fontFamily: 'Nunitosans-Regular',
                                  fontSize: 14,
                                }}>
                                {`Selected Time: ${moment(customStartTime).format('DD MMM YYYY hh:mm A')} - ${moment(customEndTime).format('DD MMM YYYY hh:mm A')}`}
                              </Text>
                            </View>
                            : <></>
                          }

                          <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 20, }}>
                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.primaryColor,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',

                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              }}
                              onPress={() => {
                                if (selectedChoice == null) {
                                  Alert.alert('Error', 'Please select your preferred time to continue',
                                    [{
                                      text: 'OK',
                                    },
                                    {
                                      text: 'CANCEL',
                                      onPress: () => {
                                        setHideModal(false);
                                      },
                                    },
                                    ],
                                  )
                                }
                                else {
                                  setIsHidden(true);
                                  setHideModal(false);
                                }
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'white',
                                  fontWeight: '500',
                                }}>
                                CONFIRM
                              </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.fieldtBgColor,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: 10,

                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              }}
                              onPress={() => {
                                setHideModal(false);
                                setSelectedChoice(null);
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'grey',
                                  fontWeight: '500',
                                }}>
                                CANCEL
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </ModalView>

                    <ModalView
                      supportedOrientations={['landscape', 'portrait']}
                      style={{ flex: 1 }}
                      visible={visible}
                      transparent
                      animationType="slide">
                      <View
                        style={{
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                          minHeight: windowHeight,
                        }}>
                        <View style={styles.confirmBox}>
                          <TouchableOpacity
                            onPress={() => {
                              setState({ visible: false });
                            }}>
                            <View
                              style={{
                                alignSelf: 'flex-end',
                                padding: 16,
                              }}>
                              {/* <Close name="closecircle" size={25} /> */}
                              <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                              />
                            </View>
                          </TouchableOpacity>
                          <View>
                            <Text
                              style={{
                                textAlign: 'center',
                                fontWeight: '700',
                                fontSize: 28,
                              }}>
                              Purchase Order
                            </Text>
                          </View>
                          <View style={{ marginTop: 20 }}>
                            <Text
                              style={{
                                textAlign: 'center',
                                color: Colors.descriptionColor,
                                fontSize: 25,
                              }}>
                              Fill In The Email Information
                            </Text>
                          </View>
                          <View
                            style={{
                              backgroundColor: 'white',
                              alignSelf: 'center',
                              flexDirection: 'row',
                            }}>
                            <Text style={{ fontSize: 20, marginTop: 70 }}>
                              Email:
                            </Text>
                            <View
                              style={{
                                marginTop: 60,
                                backgroundColor: '#f7f5f5',
                                marginLeft: 10,
                              }}>
                              <TextInput
                                editable={!loading}
                                underlineColorAndroid={Colors.fieldtBgColor}
                                clearButtonMode="while-editing"
                                style={styles.textCapacity}
                                placeholder="<EMAIL>"
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                onChangeText={(text) => {
                                  setState({ Email: text });
                                }}
                                value={Email}
                              />
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignSelf: 'center',
                              marginTop: 20,
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '50%',
                              alignContent: 'center',
                              zIndex: 6000,
                            }} />
                          <View
                            style={{
                              alignSelf: 'center',
                              marginTop: 20,
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: 250,
                              height: 40,
                              alignContent: 'center',
                              flexDirection: 'row',
                              marginTop: 40,
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                email(), setState({ visible: false });
                              }}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: '60%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                alignContent: 'center',
                                borderRadius: 10,
                                height: 60,
                              }}>
                              <Text
                                style={{
                                  fontSize: 28,
                                  color: Colors.primaryColor,
                                }}>
                                Send
                              </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                setState({ visible: false });
                              }}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: '60%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                alignContent: 'center',
                                borderRadius: 10,
                                height: 60,
                                marginLeft: 30,
                              }}>
                              <Text
                                style={{
                                  fontSize: 28,
                                  color: Colors.primaryColor,
                                }}>
                                No
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </ModalView>
                    {tagModal ? (
                      <View
                        style={{
                          flex: 1,
                          position: 'absolute',
                          //backgroundColor: 'white',

                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,

                          borderRadius: 8,
                          top: '20%',
                          left: '10%',
                          zIndex: 5,
                        }}
                      // visible={tagModal}
                      // transparent={true}
                      >
                        <View
                          style={[
                            styles.modalContainer,
                            {
                              backgroundColor: 'transparent',

                              backgroundColor: 'white',

                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,

                              borderWidth: 1,
                              borderRadius: 8,
                              top:
                                Platform.OS === 'ios' && keyboardHeight > 0
                                  ? -keyboardHeight * 0.3
                                  : 0,
                            },
                          ]}>
                          <View
                            style={[
                              styles.tagModalView,
                              {
                                height: switchMerchant ? 200 : 250,
                                width: switchMerchant ? 300 : 415,
                                //height: windowWidth * 0.3,
                                // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                backgroundColor: 'transparent',
                              },
                            ]}>
                            <TouchableOpacity
                              style={styles.closeButton}
                              onPress={() => {
                                setTagModal(false);
                              }}>
                              {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                              <AntDesign
                                name="closecircle"
                                size={switchMerchant ? 15 : 25}
                                color={Colors.fieldtTxtColor}
                              />
                            </TouchableOpacity>

                            <View style={{ marginBottom: 2 }}>
                              <Text
                                style={{
                                  fontWeight: '700',
                                  fontSize: switchMerchant ? 10 : 21,
                                }}>
                                Tags
                              </Text>
                            </View>

                            <View
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                //width: 180,
                                height: switchMerchant ? 35 : 40,
                                borderRadius: 5,
                                paddingVertical: 3,
                                paddingLeft: 5,
                                marginVertical: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                width: '100%',
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  width: '60%',
                                  zIndex: 1,
                                }}>
                                <AntDesign
                                  name="tags"
                                  size={switchMerchant ? 17 : 20}
                                  style={{ color: 'black' }}
                                />
                                {/* <TextInput
                                placeholder='Enter Tag(s)'
                                style={{
                                  padding: 5,
                                }}
                              /> */}
                                {
                                  (
                                    selectedUserTagList.every((val) =>
                                      userTagDropdownList
                                        .map((tag) => tag.value)
                                        .includes(val)
                                    )
                                    ||
                                    selectedUserTagList.length === 0
                                  )
                                    ?
                                    <DropDownPicker
                                      style={{
                                        height: switchMerchant ? 35 : 35,
                                        width: switchMerchant ? 150 : 185,
                                        marginLeft: 5,
                                        paddingVertical: 0,
                                        borderColor: 'darkgreen',
                                        fontSize: switchMerchant ? 11 : 14,
                                      }}
                                      dropDownStyle={{
                                        marginLeft: 5,
                                        marginTop: 1,
                                        borderWidth: 1,
                                        // borderColor: 'darkgreen',
                                        width: switchMerchant ? 150 : 185,
                                      }}
                                      arrowSize={switchMerchant ? 15 : 20}
                                      arrowStyle={{
                                        paddingVertical: switchMerchant ? 0 : -10,
                                        alignSelf: 'center',
                                      }}
                                      // items={[{ label: 'Shampoo', value: 1 }, { label: 'Conditioner', value: 2 }, { label: 'Hair mask', value: 3 }]}
                                      items={userTagDropdownList}
                                      placeholder={'Select tag(s)'}
                                      multipleText={'%d tag(s) selected'}
                                      labelStyle={{ fontSize: switchMerchant ? 10 : 14 }}
                                      onChangeItem={(items) => {
                                        setSelectedUserTagList(items);
                                      }}
                                      defaultValue={selectedUserTagList}
                                      multiple
                                      searchable
                                      dropDownMaxHeight={130}
                                      onSearch={(text) => {
                                        setSearchingUserTagText(text);
                                      }}
                                      customTickIcon={(press) => (
                                        <Ionicon
                                          name={'checkbox-outline'}
                                          color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                          }
                                          size={switchMerchant ? 17 : 25}
                                        />
                                      )}
                                    />
                                    :
                                    <></>
                                }
                              </View>

                              <View
                                style={{
                                  width: '30%',
                                  flexDirection: 'row',
                                  justifyContent: 'flex-end',
                                  alignItems: 'center',
                                  //zIndex: -1,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    backgroundColor: Colors.primaryColor,
                                    width: 70,
                                    height: 35,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: 5,

                                    flexDirection: 'row',
                                  }}
                                  disabled={isLoading}
                                  onPress={createCRMUserTagOrAddCRMUserTagToCategory}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 17,
                                      fontWeight: '600',
                                      color: 'white',
                                    }}>
                                    {isLoading ? '' : 'SAVE'}
                                  </Text>

                                  {isLoading ? (
                                    <ActivityIndicator
                                      color={Colors.whiteColor}
                                      size={'small'}
                                    />
                                  ) : (
                                    <></>
                                  )}
                                </TouchableOpacity>
                              </View>
                            </View>

                            {/* Choosen Tags Start */}
                            <View style={{ minHeight: 40, maxHeight: 140, zIndex: -1 }}>
                              <ScrollView
                                style={{
                                  //marginRight: 12,
                                  zIndex: -1,
                                  minHeight: 70,
                                }}
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                showsVerticalScrollIndicator={false}>
                                <FlatList
                                  data={userTagList}
                                  //numRows={2}
                                  numColumns={3}
                                  renderItem={renderProductTag}
                                  keyExtractor={(item, index) => String(index)}
                                  style={{
                                    paddingVertical: 5,
                                    paddingHorizontal: 5,
                                  }}
                                  //horizontal={true}
                                  showsHorizontalScrollIndicator={false}
                                />
                              </ScrollView>
                            </View>
                            {/* Choosen Tags End */}
                          </View>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}

                    <TouchableOpacity
                      style={{
                        marginBottom: switchMerchant ? 0 : 0,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        marginTop: 5,
                        paddingLeft: switchMerchant
                          ? 0
                          : Platform.OS === 'ios'
                            ? '2%'
                            : '2%',
                      }}
                      onPress={() => {
                        // setState({
                        //   lowStockAlert: false,
                        //   purchaseOrder: true,
                        //   stockTransfer: false,
                        //   stockTake: false,
                        //   addPurchase: false,
                        //   editPurchase: false,
                        //   addStockTransfer: false,
                        //   addStockTake: false,
                        // })
                        setPurchaseOrder(true);
                        setAddPurchase(false);
                      }}>
                      <Icon
                        name="chevron-left"
                        size={switchMerchant ? 20 : 30}
                        color={Colors.primaryColor}
                      />
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 14 : 17,
                          color: Colors.primaryColor,
                          marginBottom: Platform.OS === 'ios' ? 0 : 2,
                          marginLeft: '-0.5%',
                        }}>
                        {' '}
                        Back{' '}
                      </Text>
                    </TouchableOpacity>

                    <ScrollView
                      nestedScrollEnabled
                      contentContainerStyle={{
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      style={
                        switchMerchant
                          ? {
                            // justifyContent: 'center',
                            // alignItems: 'center',
                            //width: windowWidth * 0.79,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            backgroundColor: Colors.whiteColor,
                            minHeight: windowHeight * 0.1,
                            marginTop: switchMerchant ? 10 : 20,
                            marginHorizontal: 0,
                            marginBottom: 30,
                            //alignSelf: 'center',
                            borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                          }
                          : {
                            // justifyContent: 'center',
                            // alignItems: 'center',
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            backgroundColor: Colors.whiteColor,
                            //width: windowWidth * 0.89,
                            minHeight: windowHeight * 0.1,
                            marginTop: 30,
                            marginHorizontal: 30,
                            marginBottom: 30,
                            //alignSelf: 'center',
                            borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                          }
                      }>
                      {/* <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end" }}>
                  <DropDownPicker
                    items={[
                      {
                        label: '🖨️  Print P.O',
                        value: 'Print P.O',
                      },
                      {
                        label: '📧  Email P.O',
                        value: 'Chicken',
                      },
                      {
                        label: '📤  Export Labels',
                        value: 'Export Labels',
                      },
                      {
                        label: '❌  Cancel P.O',
                        value: 'Cancel P.O',
                      },
                      {
                        label: '🗑️  Delete P.O',
                        value: 'Delete P.O',
                      },
                    ]}
                    defaultValue={choice2}
                    placeholder=""
                    containerStyle={{ height: 30 }}
                    style={{ backgroundColor: '#FAFAFA' }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                    onChangeItem={(item) =>
                      setState({
                        choice2: item.value,
                      })
                    }
                  />
                </View> */}
                      <View style={{}}>
                        <View
                          style={{
                            alignSelf: 'flex-end',
                            marginTop: switchMerchant ? 10 : 20,
                            position: 'absolute',
                            zIndex: 10000,
                          }}>
                          {editCategory ? (
                            <>
                              <View
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.tabRed,
                                  backgroundColor: Colors.tabRed,
                                  borderRadius: 5,
                                  width: switchMerchant ? 100 : 130,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    width: 130,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}
                                  onPress={() => {
                                    setDeleteModal(true);
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    DELETE
                                  </Text>
                                </TouchableOpacity>
                              </View>

                              {/* <View
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.secondaryColor,
                                  backgroundColor: Colors.secondaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant ? 100 : 130,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,

                                  marginTop: 10
                                }}>
                                <TouchableOpacity
                                  style={{
                                    width: 130,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}
                                  onPress={() => {
                                    setHideModal(true);
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    HIDE
                                  </Text>
                                </TouchableOpacity>
                              </View> */}
                            </>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 130,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginTop: switchMerchant ? 10 : 10,
                            }}>
                            <TouchableOpacity
                              style={{
                                width: 130,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onPress={createOutletItemCategory}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {editCategory ? 'SAVE' : 'ADD'}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View style={{ marginTop: 20 }}>
                          <Text
                            style={{
                              alignSelf: 'center',
                              marginTop: 30,
                              fontSize: switchMerchant ? 20 : 40,
                              fontWeight: 'bold',
                            }}>
                            {editCategory
                              ? 'Edit Product Category'
                              : 'Add Product Category'}
                          </Text>
                          <Text
                            style={{
                              alignSelf: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              color: '#adadad',
                            }}>
                            Fill In The Product Category Information
                          </Text>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            // justifyContent: 'space-evenly',
                            marginTop: 60,
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View style={{ width: '50%' }}>
                            <View style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: '2%',
                            }}>
                              <Text style={{
                                fontSize: switchMerchant ? 15 : 20,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                                Product Photo
                              </Text>

                              {/* Remove Photo */}
                              <TouchableOpacity
                                onPress={() => {
                                  setImage('');
                                  setImageType('');
                                  setIsClearProductImage(true);
                                }}
                                style={{
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                }}
                              >
                                <Ionicons name="reload" color={Colors.primaryColor} size={24} />
                              </TouchableOpacity>
                            </View>

                            <TouchableOpacity onPress={() => { handleChoosePhoto() }}>
                              <View style={{
                                width: '80%',
                                aspectRatio: 16 / 9,
                                backgroundColor: Colors.fieldtBgColor,
                                borderWidth: 1,
                                borderRadius: 5,
                                borderColor: '#E5E5E5',
                                justifyContent: 'center',
                                alignItems: 'center',
                                // marginBottom: 10,
                              }}>
                                {image ? (
                                  <>
                                    <AsyncImage
                                      source={{ uri: image }}
                                      item={selectedOutletCategoryEdit}
                                      style={{
                                        width: '100%',
                                        height: '100%'
                                      }}
                                    />

                                    <View style={{ position: 'absolute', bottom: 5, right: 5 }}>
                                      <FontAwesome5 name="edit" size={switchMerchant ? 17 : 23} color={Colors.primaryColor} />
                                    </View>
                                  </>
                                ) : (
                                  <Icon
                                    name="upload"
                                    size={switchMerchant ? 80 : 100}
                                    color="lightgrey"
                                  />
                                )}
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View style={{ width: '50%', top: 20, }}>
                            <View
                              style={{
                                flexDirection: 'column',
                                // flex: 1,
                                // alignItems: 'center',
                                // justifyContent: 'space-around',
                                // width: '50%',
                                marginBottom: 15,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  // width: '40%',
                                  textAlign: 'left',
                                  marginBottom: 5,
                                }}>
                                Category Name
                              </Text>

                              {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                              <TextInput
                                editable
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: '60%',
                                  height: switchMerchant ? 35 : 40,
                                  borderRadius: 5,
                                  padding: 5,
                                  marginVertical: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingLeft: 10,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                // placeholder={'50'}
                                placeholder={'Category Name'}
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                keyboardType={'default'}
                                onChangeText={(text) => {
                                  setCategoryName(text);
                                }}
                                value={categoryName}
                              />
                            </View>

                            <View
                              style={{
                                flexDirection: 'column',
                                // flex: 1,
                                // alignItems: 'center',
                                // width: '50%',
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  // width: '40%',
                                  textAlign: 'left',
                                  marginBottom: 10,
                                }}>
                                Do Not Display
                              </Text>

                              {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                              <View
                                style={{
                                  width: '70%',
                                  zIndex: 1000,
                                }}>
                                <DropDownPicker
                                  containerStyle={{
                                    height: switchMerchant ? 35 : 40,
                                    zIndex: 2,
                                  }}
                                  arrowColor={'black'}
                                  arrowSize={20}
                                  arrowStyle={{ fontWeight: 'bold' }}
                                  labelStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  style={{
                                    width: switchMerchant ? 130 : 230,
                                    paddingVertical: 0,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderRadius: 10,
                                  }}
                                  placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                    zIndex: 2,
                                  }}
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  placeholder="Order Type"
                                  multipleText={'%d type(s) selected'}
                                  customTickIcon={(press) => (
                                    <Ionicons
                                      name={'checkbox-outline'}
                                      color={
                                        press
                                          ? Colors.fieldtBgColor
                                          : Colors.primaryColor
                                      }
                                      size={25}
                                    />
                                  )}
                                  onChangeItem={(items) => {
                                    setHideInOrderTypes(items);
                                  }}
                                  defaultValue={hideInOrderTypes}
                                  multiple
                                  dropDownMaxHeight={150}
                                  dropDownStyle={{
                                    width: switchMerchant ? 130 : 230,
                                    height: 90,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    textAlign: 'left',
                                    zIndex: 2,
                                    fontSize: switchMerchant ? 11 : 14,
                                  }}
                                />
                              </View>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            //marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: -10,
                          }}>
                          <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '20%',
                                textAlign: 'left',
                              }}>
                              Available On
                            </Text>
                            <View
                              style={[
                                styles.textInput4,
                                {
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  height: switchMerchant ? 35 : 40,
                                  width: '30%',
                                },
                              ]}>
                              <DropDownPicker
                                containerStyle={{
                                  height: switchMerchant ? 35 : 40,
                                  zIndex: 2,
                                }}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: !isAvailableDayActive
                                    ? Colors.descriptionColor
                                    : 'black',
                                }}
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  fontSize: switchMerchant ? 11 : 14,
                                }}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 14,
                                }}
                                items={EFFECTIVE_DAY_DROPDOWN_LIST1}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  marginLeft: 5,
                                  zIndex: 3,
                                }}
                                placeholder={'Monday'}
                                multiple
                                multipleText={'%d day(s) selected'}
                                customTickIcon={(press) => (
                                  <Ionicon
                                    name={'checkbox-outline'}
                                    color={
                                      !isAvailableDayActive
                                        ? Colors.descriptionColor
                                        : press
                                          ? Colors.fieldtBgColor
                                          : Colors.primaryColor
                                    }
                                    size={25}
                                  />
                                )}
                                onChangeItem={(items) => {
                                  setSelectedEffectiveTypeOptions(items);
                                }}
                                defaultValue={selectedEffectiveTypeOptions}
                                dropDownMaxHeight={150}
                                dropDownStyle={{
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  height: 90,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  textAlign: 'left',
                                  zIndex: 3,
                                }}
                                globalTextStyle={{
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                disabled={!isAvailableDayActive}
                              />
                            </View>

                            <View style={{}}>
                              <TouchableOpacity
                                disabled={!isAvailableDayActive}
                                onPress={() => {
                                  setShowEffectiveStartTimePicker(true);
                                  setShowEffectiveEndTimePicker(false);
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Regular',
                                      color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    {moment(effectiveStartTime).format('hh:mm A')}
                                  </Text>
                                  <MaterialIcons
                                    name="keyboard-arrow-down"
                                    size={switchMerchant ? 15 : 20}
                                    style={{
                                      color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                                      paddingLeft: 5,
                                    }}
                                  />
                                </View>
                              </TouchableOpacity>
                            </View>

                            <View style={{ marginLeft: switchMerchant ? 10 : 10 }}>
                              <TouchableOpacity
                                disabled={!isAvailableDayActive}
                                onPress={() => {
                                  setShowEffectiveStartTimePicker(false);
                                  setShowEffectiveEndTimePicker(true);
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Regular',
                                      color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    {moment(effectiveEndTime).format('hh:mm A')}
                                  </Text>
                                  <MaterialIcons
                                    name="keyboard-arrow-down"
                                    size={switchMerchant ? 15 : 20}
                                    style={{
                                      color: !isAvailableDayActive ? Colors.descriptionColor : Colors.blackColor,
                                      paddingLeft: 5,
                                    }}
                                  />
                                </View>
                              </TouchableOpacity>
                            </View>

                            <View style={{ marginLeft: switchMerchant ? 10 : 20 }}>
                              <Switch
                                width={42}
                                style={{}}
                                value={isAvailableDayActive}
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                                onSyncPress={(value) => {
                                  setIsAvailableDayActive(value);
                                }}
                              />
                            </View>
                          </View>
                        </View>

                        {/* ////////////////////////////// */}
                        {/* 2023-3-2 - Tag */}
                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            // justifyContent: 'space-evenly',
                            marginTop: 50,
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            alignItems: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center', zIndex: -11, }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '20%',
                                textAlign: 'left',
                              }}>
                              Tags:
                            </Text>
                            <View style={{ width: switchMerchant ? '40%' : '30%', }}>
                              {selectedOutletCategoryEdit &&
                                selectedOutletCategoryEdit.crmUserTagIdList &&
                                selectedOutletCategoryEdit.crmUserTagIdList.length > 0 ? (
                                <View
                                  style={{
                                    alignItems: 'baseline',
                                    alignSelf: 'baseline',
                                    flexDirection: 'row',
                                    flexWrap: 'wrap',
                                  }}>
                                  {selectedOutletCategoryEdit &&
                                    selectedOutletCategoryEdit.crmUserTagIdList &&
                                    selectedOutletCategoryEdit.crmUserTagIdList.map(
                                      (userTagId, i) => {
                                        var tagText = 'N/A';

                                        if (crmUserTagsDict[userTagId]) {
                                          tagText = crmUserTagsDict[userTagId].name;
                                        }
                                        return (
                                          <View
                                            style={{
                                              alignItems: 'baseline',
                                              marginRight: 5,
                                              alignSelf: 'baseline',
                                              flexDirection: 'row',
                                              marginBottom: 5,
                                            }}>
                                            <Text
                                              style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                borderColor: 'green',
                                                borderWidth: 1,
                                                borderRadius: 5,
                                                fontWeight: '500',
                                                color: Colors.blackColor,
                                                padding: 3,
                                                alignItems: 'baseline',
                                              }}>
                                              {tagText}
                                            </Text>
                                          </View>
                                        );
                                      },
                                    )}
                                </View>
                              ) : (
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    // width: switchMerchant ? '60%' : '50%',
                                    paddingBottom: Platform.OS == 'android' ? 0 : 6,
                                    color: Colors.descriptionColor,
                                  }}>
                                  No Tag Yet
                                </Text>
                              )}
                            </View>

                            <View style={{ marginLeft: switchMerchant ? 10 : 20 }}>
                              {selectedOutletCategoryEdit !== null ? (
                                <TouchableOpacity
                                  style={{
                                    marginLeft: 8,
                                    alignSelf: 'flex-start',
                                    zIndex: 999,
                                  }}
                                  onPress={() => {
                                    setTagModal(true);
                                  }}>
                                  <AntDesign
                                    name="tagso"
                                    size={switchMerchant ? 17 : 24}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              ) : (
                                <></>
                              )}
                            </View>
                          </View>
                        </View>
                        {/* ////////////////////////////// */}

                        {/* 2022-11-10 - Printer area */}

                        {
                          printerAreaDropdownList.length > 0 ||
                            (selectedPrinterAreaList.length > 0 &&
                              printerAreaDropdownList.includes(
                                selectedPrinterAreaList[0],
                              ))
                            ?
                            (
                              <View
                                style={{
                                  flexDirection: 'row',
                                  marginTop: 10,
                                  justifyContent: 'space-evenly',
                                  marginTop: 50,
                                  marginBottom: 40,
                                  width: '90%',
                                  alignSelf: 'center',
                                  marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                                }}>
                                <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      width: '20%',
                                      textAlign: 'left',
                                    }}>
                                    Printer Area
                                  </Text>
                                  <View
                                    style={[
                                      styles.textInput4,
                                      {
                                        width: switchMerchant
                                          ? windowWidth * 0.1
                                          : windowWidth * 0.15,
                                        height: switchMerchant ? 35 : 40,
                                        width: '30%',
                                      },
                                    ]}>
                                    {(
                                      selectedPrinterAreaList.every((val) =>
                                        printerAreaDropdownList
                                          .map((area) => area.value)
                                          .includes(val)
                                      )
                                      ||
                                      selectedPrinterAreaList.length === 0
                                    ) ? (
                                      <DropDownPicker
                                        containerStyle={{
                                          height: switchMerchant ? 35 : 40,
                                          zIndex: 2,
                                        }}
                                        arrowColor={'black'}
                                        arrowSize={20}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        labelStyle={{
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        style={{
                                          width: switchMerchant ? 130 : 230,
                                          paddingVertical: 0,
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderRadius: 10,
                                        }}
                                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                        itemStyle={{
                                          justifyContent: 'flex-start',
                                          marginLeft: 5,
                                          zIndex: 2,
                                        }}
                                        items={printerAreaDropdownList}
                                        placeholder="Choose printer area"
                                        multipleText={'%d printer area(s) selected'}
                                        customTickIcon={(press) => (
                                          <Ionicon
                                            name={'checkbox-outline'}
                                            color={
                                              press
                                                ? Colors.fieldtBgColor
                                                : Colors.primaryColor
                                            }
                                            size={25}
                                          />
                                        )}
                                        onChangeItem={(items) => {
                                          setSelectedPrinterAreaList(items);
                                        }}
                                        defaultValue={selectedPrinterAreaList}
                                        multiple
                                        dropDownMaxHeight={150}
                                        dropDownStyle={{
                                          width: switchMerchant ? 130 : 230,
                                          height: 90,
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          textAlign: 'left',
                                          zIndex: 2,
                                          fontSize: switchMerchant ? 11 : 14,
                                        }}
                                      />
                                    ) : (
                                      <></>
                                    )}
                                  </View>
                                </View>
                              </View>
                            )
                            :
                            <></>
                        }

                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginTop: 50,
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                              // justifyContent: 'space-around',
                              width: '50%',
                            }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '40%',
                                textAlign: 'left',
                              }}>
                              Print KD Times
                            </Text>

                            {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                            <View
                              style={{
                                width: '50%',
                                zIndex: 1000,
                              }}>
                              <DropDownPicker
                                containerStyle={{
                                  height: switchMerchant ? 35 : 40,
                                  zIndex: 2,
                                }}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                style={{
                                  width: switchMerchant ? 130 : 230,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                }}
                                placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  marginLeft: 5,
                                  zIndex: 2,
                                }}
                                items={[
                                  {
                                    label: '1',
                                    value: 1,
                                  },
                                  {
                                    label: '2',
                                    value: 2,
                                  },
                                  {
                                    label: '3',
                                    value: 4,
                                  },
                                  {
                                    label: '5',
                                    value: 6,
                                  },
                                  {
                                    label: '7',
                                    value: 7,
                                  },
                                  {
                                    label: '8',
                                    value: 8,
                                  },
                                  {
                                    label: '9',
                                    value: 9,
                                  },
                                  {
                                    label: '10',
                                    value: 10,
                                  },
                                ]}
                                placeholder="Print Times"
                                // multipleText={'%d type(s) selected'}
                                // customTickIcon={(press) => (
                                //   <Ionicons
                                //     name={'checkbox-outline'}
                                //     color={
                                //       press
                                //         ? Colors.fieldtBgColor
                                //         : Colors.primaryColor
                                //     }
                                //     size={25}
                                //   />
                                // )}
                                onChangeItem={(item) => {
                                  // setHideInOrderTypes(items);
                                  setPrintKDNum(item.value);
                                }}
                                defaultValue={printKDNum}
                                // multiple={true}
                                dropDownMaxHeight={150}
                                dropDownStyle={{
                                  width: switchMerchant ? 130 : 230,
                                  height: 90,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  textAlign: 'left',
                                  zIndex: 2,
                                  fontSize: switchMerchant ? 11 : 14,
                                }}
                              />
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                              width: '50%',

                              // backgroundColor: 'blue',
                            }} />
                        </View>

                        {
                          outletSectionDropdownList.length > 0
                            ?
                            (
                              <View
                                style={{
                                  flexDirection: 'row',
                                  marginTop: 10,
                                  justifyContent: 'space-evenly',
                                  marginTop: 50,
                                  marginBottom: 40,
                                  width: '90%',
                                  alignSelf: 'center',
                                  marginLeft: windowWidth * Styles.sideBarWidth * 0.5,

                                  zIndex: 100,
                                }}>
                                <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      width: '20%',
                                      textAlign: 'left',
                                    }}>
                                    Hide For Zone
                                  </Text>
                                  <View
                                    style={[
                                      styles.textInput4,
                                      {
                                        width: switchMerchant
                                          ? windowWidth * 0.1
                                          : windowWidth * 0.15,
                                        height: switchMerchant ? 35 : 40,
                                        width: '30%',
                                      },
                                    ]}>
                                    {(
                                      selectedHideOutletSectionIdList.every((val) =>
                                        outletSectionDropdownList
                                          .map((area) => area.value)
                                          .includes(val)
                                      )
                                      ||
                                      selectedHideOutletSectionIdList.length === 0
                                    ) ? (
                                      <DropDownPicker
                                        containerStyle={{
                                          height: switchMerchant ? 35 : 40,
                                          zIndex: 2,
                                        }}
                                        arrowColor={'black'}
                                        arrowSize={20}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        labelStyle={{
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        style={{
                                          width: switchMerchant ? 130 : 230,
                                          paddingVertical: 0,
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderRadius: 10,
                                        }}
                                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                        itemStyle={{
                                          justifyContent: 'flex-start',
                                          marginLeft: 5,
                                          zIndex: 2,
                                        }}
                                        items={outletSectionDropdownList}
                                        placeholder="Zone(s) to hide"
                                        multipleText={'%d zone(s) selected'}
                                        customTickIcon={(press) => (
                                          <Ionicon
                                            name={'checkbox-outline'}
                                            color={
                                              press
                                                ? Colors.fieldtBgColor
                                                : Colors.primaryColor
                                            }
                                            size={25}
                                          />
                                        )}
                                        onChangeItem={(items) => {
                                          setSelectedHideOutletSectionIdList(items);
                                        }}
                                        defaultValue={selectedHideOutletSectionIdList}
                                        multiple
                                        dropDownMaxHeight={150}
                                        dropDownStyle={{
                                          width: switchMerchant ? 130 : 230,
                                          height: 90,
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          textAlign: 'left',
                                          zIndex: 2,
                                          fontSize: switchMerchant ? 11 : 14,
                                        }}
                                      />
                                    ) : (
                                      <></>
                                    )}
                                  </View>
                                </View>
                              </View>
                            )
                            :
                            <></>
                        }

                        <View
                          style={{
                            flexDirection: 'row',
                            //marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginTop: 50,
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: -11,
                          }}>
                          <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '20%',
                                textAlign: 'left',
                              }}>
                              Tax
                            </Text>
                            <View
                              style={[
                                styles.textInput4,
                                {
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  height: switchMerchant ? 35 : 40,
                                  width: '30%',
                                },
                              ]}>
                              <DropDownPicker
                                containerStyle={{
                                  height: switchMerchant ? 35 : 40,
                                  zIndex: 2,
                                }}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                style={{
                                  width: switchMerchant ? 130 : 230,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                }}
                                placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  marginLeft: 5,
                                  zIndex: 2,
                                }}
                                items={customTaxDropdownList}
                                placeholder="Select Custom Tax"
                                onChangeItem={(item) => {
                                  setItemCustomTax(item.value);
                                }}
                                defaultValue={itemCustomTax}
                                dropDownMaxHeight={150}
                                dropDownStyle={{
                                  width: switchMerchant ? 130 : 230,
                                  height: 90,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  textAlign: 'left',
                                  zIndex: 2,
                                  fontSize: switchMerchant ? 11 : 14,
                                }}
                              />
                            </View>

                            <View style={{ marginLeft: switchMerchant ? 10 : 10 }}>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginTop: 50,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '20%',
                                textAlign: 'left',
                              }}>
                              {'Excluding From\nManual Discount'}
                            </Text>
                            <View
                              style={[
                                styles.textInput4,
                                {
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  height: switchMerchant ? 35 : 40,
                                  width: '30%',
                                },
                              ]}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  flex: 1,
                                  alignItems: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    marginRight: 20,
                                    bottom: -2,
                                  }}
                                  value={noManualDisc}
                                  onSyncPress={(statusTemp) =>
                                    setNoManualDisc(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtBgColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginTop: 50,
                            marginBottom: 200,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                width: '20%',
                                textAlign: 'left',
                              }}>
                              {'Excluding From\nVoucher & Promotion'}
                            </Text>
                            <View
                              style={[
                                styles.textInput4,
                                {
                                  width: switchMerchant
                                    ? windowWidth * 0.1
                                    : windowWidth * 0.15,
                                  height: switchMerchant ? 35 : 40,
                                  width: '30%',
                                },
                              ]}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  flex: 1,
                                  alignItems: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    marginRight: 20,
                                    bottom: -2,
                                  }}
                                  value={excludePromoVoucher}
                                  onSyncPress={(statusTemp) =>
                                    setExcludePromoVoucher(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtBgColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>
                          </View>
                        </View>

                        {/* ////////////////////////////// */}
                      </View>

                      {/* <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>

                  <View
                    style={{
                      backgroundColor: Colors.tabRed,
                      width: 180,
                      height: 40,
                      marginVertical: 15,
                      marginRight: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity onPress={() => {
                      setDeleteModal(true)
                    }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                          fontWeight: '600',
                        }}>
                        DELETE
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 180,
                      height: 40,
                      marginVertical: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity onPress={createOutletItemCategory}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                          fontWeight: '600',
                        }}>
                        SAVE
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View> */}

                      {/* <View
                        style={{
                          flexDirection: 'row',
                          backgroundColor: '#ffffff',
                          justifyContent: 'center',
                          padding: 18,
                          height: switchMerchant ? 20 : 100,
                        }}>
                      </View> */}
                    </ScrollView>
                  </View>
                </View>
              ) : null}
            </ScrollView>
          </View >
          {/* </ScrollView> */}
        </ScrollView >
      </View >
    </UserIdleWrapper >)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list1: {
    backgroundColor: Colors.whiteColor,
    // height: Dimensions.get('window').height * 0.7,
    height: Dimensions.get('window').height * 0.62,
    width: Dimensions.get('window').width * 0.87,
    marginTop: 30,
    marginHorizontal: 30,
    marginBottom: 30,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  list1_PhoneAdjustment: {
    backgroundColor: Colors.whiteColor,
    minHeight: Dimensions.get('window').height * 0.01,
    width: Dimensions.get('window').width * 0.79,
    marginTop: 30,
    //marginHorizontal: 30,
    marginBottom: 30,
    //alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  ContainerList: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    minHeight: Dimensions.get('window').height * 0.01,
    borderRadius: 5,
    //padding: windowWidth * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 30,
    marginHorizontal: 30,
    marginBottom: 30,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: Platform.OS == 'ios' ? Dimensions.get('window').height * 0.06 : 40,
    paddingVertical: 5,
    paddingHorizontal: 10,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset:
      Platform.OS == 'ios'
        ? {
          width: 0,
          height: 0,
        }
        : {
          width: 0,
          height: 7,
        },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset:
      Platform.OS == 'ios'
        ? {
          width: 0,
          height: 0,
        }
        : {
          width: 0,
          height: 7,
        },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center',
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // modalView: {
  //   height: windowWidth * 0.22,
  //   width: windowWidth * 0.48,
  //   backgroundColor: Colors.whiteColor,
  //   borderRadius: windowWidth * 0.03,
  //   padding: windowWidth * 0.03,
  //   paddingHorizontal: windowWidth * 0.02,
  //   alignItems: 'center',
  //   justifyContent: 'space-between',
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    height: Dimensions.get('window').width * 0.25,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.035,
    paddingTop: Dimensions.get('window').width * 0.05,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  textInput4: {
    borderRadius: 10,
  },
  tagModalView: {
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
  },
});
export default ProductCategoryScreen;
