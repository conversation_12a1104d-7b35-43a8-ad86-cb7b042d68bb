var orderType = 0; // 0- dine in, 1 -take away
var tableNumber = null;
var outletId = null;
var cartItem = [];
var refreshCartPage = null;
var tax = 5;
var currentOrderId = null;

var voucher = null;
var payment = null;

export function getOrderType() {
    return orderType;
}

export function setOrderType(param) {
    orderType = param
}

export function getTableNumber() {
    return tableNumber;
}

export function setTableNumber(param) {
    tableNumber = param
}

export function getTax() {
    return tax;
}

export function setTax(param) {
    tax = param
}

export function getOutletId() {
    return outletId;
}

export function setOutletId(param) {
    outletId = param
}

export function setCartItem(data) {
    cartItem.push(data)
}

export function getCartItem() {
    return cartItem;
}

export function getCurrentOrderId() {
    return currentOrderId;
}

export function setCurrentOrderId(param) {
    currentOrderId = param
}

export function clearCart() {
    cartItem = [];
    tableNumber = null;
    outletId = null;
    tax = 5;
    currentOrderId = null
}

export function setRefreshCartPage(param) {
    refreshCartPage = param
}

export function getRefreshCartPage() {
    if(refreshCartPage != null){
        return refreshCartPage()
    }else{
        return true;
    }
}

export function deleteCartItem(id) {
    var list = cartItem;
    var newList = [];
    if (list.length > 1) {
        list.forEach(element => {
            if (element.itemId != id) {
                newList.push(element);
                cartItem = newList;
            }
        });
    } else {
        if (list[0].itemId == id) {
            cartItem = [];
            clearCart();
        }
    }
}

export function getVoucher() {
    return voucher;
}

export function setVoucher(param) {
    voucher = param;
}

export function getPayment() {
    return payment;
}

export function setPayment(param) {
    payment = param;
}
