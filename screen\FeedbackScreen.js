import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal as ModalComponent,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Footer from './footer';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import ProgressCircle from 'react-native-progress-circle';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const FeedbackScreen = (props) => {
  // dummy data
  const dummyFeedback = [
    {
      name: 'Ryan Lee',
      date: '12th April to 18 April 2021',
      email: '<EMAIL>',
      currentDate: '14th April 2021',
      week: 'Week 15',
      day: 'Wednesday',
      contact: '60179792022',
      customer: '2',
      foodDrink: 5,
      atmosphere: 4,
      service: 4,
      value: 4,
    },
    {
      name: 'ABCD',
      date: '12th April to 18 April 2021',
      email: '<EMAIL>',
      currentDate: '15th April 2021',
      week: 'Week 16',
      day: 'Wednesday',
      contact: '**********',
      customer: '2',
      foodDrink: 2,
      atmosphere: 1,
      service: 3,
      value: 2,
    },
    {
      name: 'EFGH',
      date: '12th April to 18 April 2021',
      email: '<EMAIL>',
      currentDate: '16th April 2021',
      week: 'Week 16',
      day: 'Wednesday',
      contact: '**********',
      customer: '2',
      foodDrink: 1,
      atmosphere: 1,
      service: 1,
      value: 1,
    },
  ];

  const { navigation } = props;
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [search, setSearch] = useState('');

  // set left side tags filter
  const [showLastSevenDays, setShowLastSevenDays] = useState(false);
  const [showOneToThreeStars, setShowOneToThreeStars] = useState(false);
  const [showFourToFiveStars, setShowFourToFiveStars] = useState(false);

  // rating details for overall feedback
  const [foodRate, setFoodRate] = useState([1, 0, 0, 0, 0]);
  const [atmosphereRate, setAtmosphereRate] = useState([0, 1, 0, 0, 0]);
  const [serviceRate, setServiceRate] = useState([0, 1, 0, 0, 0]);
  const [valueRate, setValueRate] = useState([0, 1, 0, 0, 0]);

  // set right side view modal
  const [selectedFeedback, setSelectedFeedback] = useState('');
  const [showOverall, setShowOverall] = useState(false);
  const [showFeedbackDetails, setShowFeedbackDetails] = useState(false);
  const [feedbackSelected, setFeedbackSelected] = useState(0);
  const [showGuestProfile, setShowGuestProfile] = useState(false);
  const [guestSelected, setGuestSelected] = useState('');

  // Filter modal
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filterByReservedDate, setFilterByReservedDate] = useState(false);
  const [filterByVisitedDate, setFilterByVisitedDate] = useState(false);

  // const [] = useState(dummyFeedback);
  // useEffect
  // show last seven days and show overall first
  useEffect(() => {
    // For left side tags
    setShowLastSevenDays(true);
    setShowOneToThreeStars(false);
    setShowFourToFiveStars(false);

    // For right side views
    setShowOverall(false);
    setShowFeedbackDetails(false);
    setShowGuestProfile(true);

    // For filter modal
    setFilterByReservedDate(true);
    setFilterByVisitedDate(false);
  }, []);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // Header of the page
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Feedback
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // FlatList's render Item
  const renderSummary = (item, index) => {
    var overallValue = (
      (item.item.foodDrink +
        item.item.atmosphere +
        item.item.service +
        item.item.value) /
      4
    ).toFixed(1);
    // // console.log(`showOneToThreeStars ${showOneToThreeStars}`)
    // // console.log(`showFourToFiveStars ${showFourToFiveStars}`)
    // // console.log(`showLastSevenDays ${showLastSevenDays}`)
    // // console.log(item.item.foodDrink)
    // // console.log(item.item.atmosphere)
    // // console.log(item.item.service)
    // // console.log(item.item.value)
    // // console.log(overallValue.toFixed(1));
    // // console.log(overallValue);
    if (showOneToThreeStars && overallValue < 4) {
      // Show feedback with 1 to 3 stars
      // // console.log('Ryan should be shown');
      return (
        // Card View
        (<View
          style={{
            height: Dimensions.get('screen').height * 0.18,
            width: Dimensions.get('screen').width * 0.28,
            padding: Dimensions.get('screen').width * 0.001,
            marginLeft: Dimensions.get('screen').width * 0.003,
            // marginHorizontal: Dimensions.get('screen').width * 0.015,
            marginTop: Dimensions.get('screen').height * 0.01,
            //   justifyContent:
          }}>
          <TouchableOpacity
            onPress={() => {
              setFeedbackSelected(item.index);
              setShowOverall(false);
              setShowFeedbackDetails(true);
              // // console.log(item.index);
            }}>
            {/* Card header */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#E5E8E8',
                justifyContent: 'space-between',
                height: '30%',
                padding: 10,
              }}>
              {/* Date */}
              <Text>{item.item.date}</Text>

              {/* Week number */}
              <Text>{item.item.week}</Text>
            </View>

            {/* Card body */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: 'white',
                justifyContent: 'space-between',
                height: '70%',
                width: '100%',
                paddingTop: 10,
                paddingHorizontal: 10,
                // borderWidth:1,
              }}>
              {/* Customer details */}
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{ fontSize: 18 }}>{item.item.name}</Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text>{`${item.item.day}, `}</Text>
                  <Text>{item.item.currentDate}</Text>
                </View>

                <View style={{ flexDirection: 'row', paddingVertical: 15 }}>
                  {/* person icon here */}
                  <Icon
                    name="person-outline"
                    size={16}
                    color="black"
                    style={{}}
                  />
                  <Text>{item.item.customer}</Text>
                </View>
              </View>
              {/* Progress */}
              <View style={{ alignSelf: 'center', paddingHorizontal: 5 }}>
                <ProgressCircle
                  percent={(parseFloat(`${overallValue}`) / 5.0) * 100}
                  radius={30}
                  borderWidth={5}
                  color="#2ECC71"
                  shadowColor="#999"
                  bgColor="#fff">
                  <Text style={{ fontSize: 18 }}>{overallValue}</Text>
                </ProgressCircle>
              </View>
              {/* Rating details */}
              <View>
                {/* Food/Drinks */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Food/Drinks: </Text>
                  <Text>{item.item.foodDrink}</Text>
                </View>
                {/* Atmosphere */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Atmosphere: </Text>
                  <Text>{item.item.atmosphere}</Text>
                </View>
                {/* Service */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Service: </Text>
                  <Text>{item.item.service}</Text>
                </View>
                {/* Value */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Value: </Text>
                  <Text>{item.item.value}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>)
      );
    } else if (showFourToFiveStars && overallValue >= 4 && overallValue <= 5) {
      // Show feedback with 4 to 5 stars
      // // console.log('ABCD should be shown');
      return (
        // Card View
        (<View
          style={{
            height: Dimensions.get('screen').height * 0.18,
            width: Dimensions.get('screen').width * 0.28,
            padding: Dimensions.get('screen').width * 0.001,
            marginLeft: Dimensions.get('screen').width * 0.003,
            // marginHorizontal: Dimensions.get('screen').width * 0.015,
            marginTop: Dimensions.get('screen').height * 0.01,
            //   justifyContent:
          }}>
          <TouchableOpacity
            onPress={() => {
              setFeedbackSelected(item.index);
              setShowOverall(false);
              setShowFeedbackDetails(true);
              // // console.log(item.index);
            }}>
            {/* Card header */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#E5E8E8',
                justifyContent: 'space-between',
                height: '30%',
                padding: 10,
              }}>
              {/* Date */}
              <Text>{item.item.date}</Text>

              {/* Week number */}
              <Text>{item.item.week}</Text>
            </View>

            {/* Card body */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: 'white',
                justifyContent: 'space-between',
                height: '70%',
                width: '100%',
                paddingTop: 10,
                paddingHorizontal: 10,
                // borderWidth:1,
              }}>
              {/* Customer details */}
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{ fontSize: 18 }}>{item.item.name}</Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text>{`${item.item.day}, `}</Text>
                  <Text>{item.item.currentDate}</Text>
                </View>

                <View style={{ flexDirection: 'row', paddingVertical: 15 }}>
                  {/* person icon here */}
                  <Icon
                    name="person-outline"
                    size={16}
                    color="black"
                    style={{}}
                  />
                  <Text>{item.item.customer}</Text>
                </View>
              </View>
              {/* Progress */}
              <View style={{ alignSelf: 'center', paddingHorizontal: 5 }}>
                <ProgressCircle
                  percent={(parseFloat(`${overallValue}`) / 5.0) * 100}
                  radius={30}
                  borderWidth={5}
                  color="#2ECC71"
                  shadowColor="#999"
                  bgColor="#fff">
                  <Text style={{ fontSize: 18 }}>{overallValue}</Text>
                </ProgressCircle>
              </View>
              {/* Rating details */}
              <View>
                {/* Food/Drinks */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Food/Drinks: </Text>
                  <Text>{item.item.foodDrink}</Text>
                </View>
                {/* Atmosphere */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Atmosphere: </Text>
                  <Text>{item.item.atmosphere}</Text>
                </View>
                {/* Service */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Service: </Text>
                  <Text>{item.item.service}</Text>
                </View>
                {/* Value */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Value: </Text>
                  <Text>{item.item.value}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>)
      );
    } else if (
      showLastSevenDays &&
      // For testing purpose (need to change to date now - data date <= 7)
      item.item.currentDate === '16th April 2021'
    ) {
      // Show last 7 days
      // // console.log('EFGH should be shown');
      return (
        // Card View
        (<View
          style={{
            height: Dimensions.get('screen').height * 0.18,
            width: Dimensions.get('screen').width * 0.28,
            padding: Dimensions.get('screen').width * 0.001,
            marginLeft: Dimensions.get('screen').width * 0.003,
            // marginHorizontal: Dimensions.get('screen').width * 0.015,
            marginTop: Dimensions.get('screen').height * 0.01,
            //   justifyContent:
          }}>
          <TouchableOpacity
            onPress={() => {
              setFeedbackSelected(item.index);
              setShowOverall(false);
              setShowFeedbackDetails(true);
              // // console.log(item.index);
            }}>
            {/* Card header */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#E5E8E8',
                justifyContent: 'space-between',
                height: '30%',
                padding: 10,
              }}>
              {/* Date */}
              <Text>{item.item.date}</Text>

              {/* Week number */}
              <Text>{item.item.week}</Text>
            </View>

            {/* Card body */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: 'white',
                justifyContent: 'space-between',
                height: '70%',
                width: '100%',
                paddingTop: 10,
                paddingHorizontal: 10,
                // borderWidth:1,
              }}>
              {/* Customer details */}
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{ fontSize: 18 }}>{item.item.name}</Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text>{`${item.item.day}, `}</Text>
                  <Text>{item.item.currentDate}</Text>
                </View>

                <View style={{ flexDirection: 'row', paddingVertical: 15 }}>
                  {/* person icon here */}
                  <Icon
                    name="person-outline"
                    size={16}
                    color="black"
                    style={{}}
                  />
                  <Text>{item.item.customer}</Text>
                </View>
              </View>
              {/* Progress */}
              <View style={{ alignSelf: 'center', paddingHorizontal: 5 }}>
                <ProgressCircle
                  percent={(parseFloat(`${overallValue}`) / 5.0) * 100}
                  radius={30}
                  borderWidth={5}
                  color="#2ECC71"
                  shadowColor="#999"
                  bgColor="#fff">
                  <Text style={{ fontSize: 18 }}>{overallValue}</Text>
                </ProgressCircle>
              </View>
              {/* Rating details */}
              <View>
                {/* Food/Drinks */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Food/Drinks: </Text>
                  <Text>{item.item.foodDrink}</Text>
                </View>
                {/* Atmosphere */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Atmosphere: </Text>
                  <Text>{item.item.atmosphere}</Text>
                </View>
                {/* Service */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Service: </Text>
                  <Text>{item.item.service}</Text>
                </View>
                {/* Value */}
                <View
                  style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <Text>Value: </Text>
                  <Text>{item.item.value}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>)
      );
    }
    return null;
  };

  // Function to render the rating details under overall section
  const setRating = (rateArray) => {
    let total = 0;
    for (let num of rateArray) {
      total += num;
    }
    return (
      <View>
        {/* Rate 5 stars */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
          <Text>5</Text>
          {rateArray[0] > 0 ? (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <View
                style={{
                  marginHorizontal: 5,
                  width: Dimensions.get('screen').width * 0.21,
                  paddingVertical: 2,
                }}>
                <View
                  style={{
                    justifyContent: 'flex-start',
                    backgroundColor: '#239B56',
                    width: (rateArray[0] / total) * 100 + '%',
                    height: 15,
                  }}></View>
              </View>

              <Text>{rateArray[0]}</Text>
            </View>
          ) : (
            <View>
              <Text style={{ paddingHorizontal: 5 }}>0</Text>
            </View>
          )}
        </View>
        {/* Rate 4 stars */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
          <Text>4</Text>
          {rateArray[1] > 0 ? (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <View
                style={{
                  marginHorizontal: 5,
                  width: Dimensions.get('screen').width * 0.21,
                  paddingVertical: 2,
                }}>
                <View
                  style={{
                    justifyContent: 'flex-start',
                    backgroundColor: '#28B463',
                    width: (rateArray[1] / total) * 100 + '%',
                    height: 15,
                  }}></View>
              </View>

              <Text>{rateArray[1]}</Text>
            </View>
          ) : (
            <View>
              <Text style={{ paddingHorizontal: 5 }}>0</Text>
            </View>
          )}
        </View>
        {/* Rate 3 stars */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
          <Text>3</Text>
          {rateArray[2] > 0 ? (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <View
                style={{
                  marginHorizontal: 5,
                  width: Dimensions.get('screen').width * 0.21,
                  paddingVertical: 2,
                }}>
                <View
                  style={{
                    justifyContent: 'flex-start',
                    backgroundColor: '#2ECC71 ',
                    width: (rateArray[2] / total) * 100 + '%',
                    height: 15,
                  }}></View>
              </View>
              <Text>{rateArray[2]}</Text>
            </View>
          ) : (
            <View>
              <Text style={{ paddingHorizontal: 5 }}>0</Text>
            </View>
          )}
        </View>
        {/* Rate 2 stars */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
          <Text>2</Text>
          {rateArray[3] > 0 ? (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <View
                style={{
                  marginHorizontal: 5,
                  width: Dimensions.get('screen').width * 0.21,
                  paddingVertical: 2,
                }}>
                <View
                  style={{
                    justifyContent: 'flex-start',
                    backgroundColor: '#52BE80',
                    width: (rateArray[3] / total) * 100 + '%',
                    height: 15,
                  }}></View>
              </View>
              <Text>{rateArray[3]}</Text>
            </View>
          ) : (
            <View>
              <Text style={{ paddingHorizontal: 5 }}>0</Text>
            </View>
          )}
        </View>
        {/* Rate 1 stars */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
          <Text>1</Text>
          {rateArray[4] > 0 ? (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <View
                style={{
                  marginHorizontal: 5,
                  width: Dimensions.get('screen').width * 0.21,
                  paddingVertical: 2,
                }}>
                <View
                  style={{
                    justifyContent: 'flex-start',
                    backgroundColor: '#82E0AA',
                    width: (rateArray[4] / total) * 100 + '%',
                    height: 15,
                  }}></View>
              </View>
              <Text>{rateArray[4]}</Text>
            </View>
          ) : (
            <View>
              <Text style={{ paddingHorizontal: 5 }}>0</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Main return
  return (
    // Screen View
    (<View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      {/* Start modal */}
      {/* Filter modal */}
      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={showFilterModal}
        transparent={true}
        animationType="slide">
        <KeyboardAvoidingView
          behavior={'padding'}
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: Dimensions.get('window').height,
          }}>
          <View style={styles.confirmBox}>
            <View
              style={{
                position: 'absolute',
                padding: 10,
                left: Dimensions.get('screen').width * 0.015,
                top: Dimensions.get('screen').height * 0.018,
                zIndex: 1000,
              }}>
              <TouchableOpacity style={{}} onPress={() => { }}>
                <View style={{}}>
                  <Text>Reset</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                width: '100%',
                height: Dimensions.get('screen').height * 0.08,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.whiteColor,
                borderTopRightRadius: 12,
                borderTopLeftRadius: 12,
              }}>
              <Text>Filter Options</Text>
            </View>
            <View
              style={{
                position: 'absolute',
                padding: 10,
                right: Dimensions.get('screen').width * 0.015,
                top: Dimensions.get('screen').height * 0.018,
                zIndex: 1000,
              }}>
              <TouchableOpacity
                style={{}}
                onPress={() => {
                  setShowFilterModal(false);
                }}>
                <View style={{}}>
                  <Text>Done</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                padding: Dimensions.get('screen').height * 0.01,
                // borderWidth:1
              }}>
              <View>
                <Text>Sort by</Text>
              </View>
              <View
                style={{
                  // borderWidth: 1,
                  // borderColor: '#3498DB',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  // width:'100%',
                  // paddingHorizontal: 15,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setFilterByReservedDate(true);
                    setFilterByVisitedDate(false);
                  }}>
                  <View
                    style={[
                      filterByReservedDate ? { backgroundColor: '#3498DB' } : {},
                      {
                        borderWidth: 1,
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        // width:'45%',
                      },
                    ]}>
                    <Text>Reserved Date</Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setFilterByReservedDate(false);
                    setFilterByVisitedDate(true);
                  }}>
                  <View
                    style={[
                      filterByVisitedDate ? { backgroundColor: '#3498DB' } : {},
                      {
                        borderWidth: 1,
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        // width:'100%',
                      },
                    ]}>
                    <Text>Visited Date</Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <Text>Filter</Text>
              </View>
            </View>
            {/* Filter option */}
            <View>
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    backgroundColor: 'white',
                    padding: 10,
                    borderBottomWidth: 1,
                    borderBottomColor: '#BDC3C7',
                  }}>
                  <View>
                    <Text>Date Range</Text>
                  </View>
                  <View>
                    <Text>All</Text>
                  </View>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: 'white',
                  padding: 10,
                  borderBottomWidth: 1,
                  borderBottomColor: '#BDC3C7',
                }}>
                <View>
                  <Text>Rating</Text>
                </View>
                <View>
                  <Text>All</Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: 'white',
                  padding: 10,
                  borderBottomWidth: 1,
                  borderBottomColor: '#BDC3C7',
                }}>
                <View>
                  <Text>Recommendation</Text>
                </View>
                <View>
                  <Text>All</Text>
                </View>
              </View>
            </View>
            <View
              style={{
                padding: Dimensions.get('screen').height * 0.01,
                // borderWidth:1
              }}>
              <Text>
                If no filter is selected then all feedback will be shown.
              </Text>
            </View>
          </View>
        </KeyboardAvoidingView>
      </ModalView>
      {/* End modal */}
      {/* Side bar content */}
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: Dimensions.get('screen').width * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}
      <View
        style={{
          flex: 1,
          position: 'absolute',
          bottom: Dimensions.get('screen').height * 0.1,
        }}>
        <Footer />
      </View>
      {/* Feedback page content */}
      <View>
        {/* Header container */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: Dimensions.get('screen').width * 0.92,
            height: Dimensions.get('screen').width * 0.05,
            padding: 15,
            backgroundColor: '#E5E5E5',
          }}>
          <View style={{ justifyContent: 'space-between', flexDirection: 'row' }}>
            {/* Search bar */}
            <View
              style={[
                {
                  width: Dimensions.get('screen').width * 0.27,
                  height: 40,
                  backgroundColor: 'white',
                  borderRadius: 5,
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  borderWidth: 1,

                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                },
                switchMerchant
                  ? {
                    height: 35,
                    width: 200,
                    // top: Dimensions.get('screen').height * -0.075,
                  }
                  : {},
              ]}>
              {switchMerchant ? (
                <Icon
                  name="search"
                  size={10}
                  color={Colors.primaryColor}
                  style={{ marginLeft: 15 }}
                />
              ) : (
                <Icon
                  name="search"
                  size={18}
                  color={Colors.primaryColor}
                  style={{ marginLeft: 15 }}
                />
              )}
              {switchMerchant ? (
                <TextInput
                  // editable={!loading}
                  style={[
                    {
                      width: 220,
                      fontSize: 15,
                      fontFamily: 'NunitoSans-Regular',
                      paddingLeft: 5,
                      height: 45,
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                        // borderWidth:1,
                        width: 180,
                        height: 40,
                      }
                      : {},
                  ]}
                  clearButtonMode="while-editing"
                  placeholder=" Search Feedback"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  onChangeText={(text) => {
                    setSearch(text);
                  }}
                  value={search}
                />
              ) : (
                <TextInput
                  // editable={!loading}
                  underlineColorAndroid={Colors.whiteColor}
                  style={[
                    {
                      width: 220,
                      fontSize: 15,
                      fontFamily: 'NunitoSans-Regular',
                      paddingLeft: 5,
                      height: 45,
                    },
                    switchMerchant
                      ? {
                        fontSize: 8,
                        // borderWidth:1,
                        width: Dimensions.get('screen').width * 0.17,
                        height: Dimensions.get('screen').height * 0.18,
                      }
                      : {},
                  ]}
                  clearButtonMode="while-editing"
                  placeholder=" Search Feedback"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  onChangeText={(text) => {
                    setSearch(text);
                  }}
                  value={search}
                />
              )}
            </View>

            {/* Back button */}
            {!showOverall ? (
              <View>
                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginHorizontal: 10,
                  }}
                  onPress={() => {
                    setShowOverall(true);
                    setShowFeedbackDetails(false);
                    setShowGuestProfile(false);
                  }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Plus
                      name="chevron-left"
                      size={20}
                      color={Colors.whiteColor}
                      style={{}}
                    />
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      BACK
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ) : null}
          </View>

          {/* Filter button */}
          <View>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.primaryColor,
                backgroundColor: '#4E9F7D',
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
                marginHorizontal: 10,
              }}
              onPress={() => {
                setShowFilterModal(true);
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <AntDesign
                  name="filter"
                  size={switchMerchant ? 10 : 20}
                  color={Colors.whiteColor}
                />
                <Text
                  style={{
                    color: Colors.whiteColor,
                    marginLeft: 5,
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  FILTER
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        {/* Body */}
        <View style={{ flexDirection: 'row' }}>
          {/* Left container */}
          <View
            style={{
              marginLeft: -2,
              width: Dimensions.get('screen').width * 0.29,
              height: Dimensions.get('screen').width * 0.47,
              borderWidth: 0.001,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 5,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,

              elevation: 3,
            }}>
            {/* Header */}
            <View style={{ backgroundColor: '#E5E7E9', width: '98.5%' }}>
              {/* Categories container */}
              <View
                style={{
                  justifyContent: 'space-around',
                  flexDirection: 'row',
                  height: Dimensions.get('screen').width * 0.04,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setShowLastSevenDays(true);
                    setShowOneToThreeStars(false);
                    setShowFourToFiveStars(false);
                  }}
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderRadius: 9999,
                    alignItems: 'center',
                    marginHorizontal: 10,
                    marginVertical: 5,
                    borderColor: '#A569BD',
                  }}>
                  <Text style={{ color: '#A569BD' }}>Last 7 days</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShowLastSevenDays(false);
                    setShowOneToThreeStars(true);
                    setShowFourToFiveStars(false);
                  }}
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderRadius: 9999,
                    alignItems: 'center',
                    marginHorizontal: 10,
                    marginVertical: 5,
                    borderColor: '#F1C40F',
                  }}>
                  <Text style={{ color: '#F1C40F' }}>1-3 stars</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShowLastSevenDays(false);
                    setShowOneToThreeStars(false);
                    setShowFourToFiveStars(true);
                  }}
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderRadius: 9999,
                    alignItems: 'center',
                    marginHorizontal: 10,
                    marginVertical: 5,
                    borderColor: '#58D68D',
                  }}>
                  <Text style={{ color: '#58D68D' }}>4-5 stars</Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* Body */}
            <View>
              <FlatList
                style={{
                  marginTop: 10,
                }}
                nestedScrollEnabled={true}
                data={dummyFeedback}
                renderItem={renderSummary}
                keyExtractor={(item, index) => String(index)}
              />
            </View>
          </View>
          {/* Right container */}
          {/* Showing overall view */}
          {showOverall ? (
            <View
              style={{
                width: Dimensions.get('screen').width * 0.63,
                height: Dimensions.get('screen').height * 0.76,
                alignItems: 'center',
                paddingTop: 10,
                // borderWidth:1
              }}>
              {/* Header container */}
              <View
                style={{
                  width: Dimensions.get('screen').width * 0.62,
                  height: Dimensions.get('screen').height * 0.04,
                }}>
                <View
                  style={{
                    padding: 7,
                    backgroundColor: '#E5E8E8',
                    alignItems: 'center',
                    height: '100%',
                    width: '100%',
                  }}>
                  <Text>10 February 2021 - 04 December 2021</Text>
                </View>
              </View>
              {/* Body container */}
              <View
                style={{
                  backgroundColor: 'white',
                  width: Dimensions.get('screen').width * 0.62,
                  height: Dimensions.get('screen').height * 0.68,
                  alignItems: 'center',
                }}>
                {/* Top */}
                <View>
                  <Text
                    style={{ alignSelf: 'center', fontSize: 30, padding: 10 }}>
                    {'Overall'}
                  </Text>
                  <ProgressCircle
                    percent={(4.3 / 5.0) * 100}
                    radius={60}
                    borderWidth={10}
                    color="#2ECC71"
                    shadowColor="#999"
                    bgColor="#fff">
                    <Text style={{ fontSize: 18 }}>{'4.3'}</Text>
                  </ProgressCircle>
                </View>
                {/* Rating details */}
                <View style={{ paddingTop: 15 }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: Dimensions.get('screen').width * 0.6,
                      height: Dimensions.get('screen').height * 0.2,
                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        width: Dimensions.get('screen').width * 0.25,
                        padding: 10,
                      }}>
                      <Text>Food/Drinks</Text>
                      {setRating(foodRate)}
                    </View>
                    <View
                      style={{
                        width: Dimensions.get('screen').width * 0.25,
                        padding: 10,
                      }}>
                      <Text>Atmosphere</Text>
                      {setRating(atmosphereRate)}
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: Dimensions.get('screen').width * 0.6,
                      height: Dimensions.get('screen').height * 0.2,
                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        width: Dimensions.get('screen').width * 0.25,
                        padding: 10,
                      }}>
                      <Text>Service</Text>
                      {setRating(serviceRate)}
                    </View>
                    <View
                      style={{
                        width: Dimensions.get('screen').width * 0.25,
                        padding: 10,
                      }}>
                      <Text>Value</Text>
                      {setRating(valueRate)}
                    </View>
                  </View>
                </View>
              </View>
            </View>
          ) : null}

          {/* Showing feedback details when feedback in a flatlist selected */}
          {showFeedbackDetails ? (
            <View
              style={{
                width: Dimensions.get('screen').width * 0.63,
                height: Dimensions.get('screen').height * 0.76,
                alignItems: 'center',
                paddingTop: 10,

                // borderWidth:1
              }}>
              {/* whole details container */}
              <View>
                {/* Top part */}
                <View
                  style={{
                    // borderWidth: 1,
                    width: Dimensions.get('screen').width * 0.62,
                    height: Dimensions.get('screen').height * 0.3,
                    backgroundColor: '#E5E8E8',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-start',
                    }}>
                    {/* User pic and user details */}
                    <View
                      style={{
                        flex: 2,
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                      }}>
                      {/* Picture */}
                      <View
                        style={
                          {
                            // backgroundColor:'#E5E8E8',
                            // borderWidth:1,
                            // marginRight: 30,
                            // width: Dimensions.get('screen').height * 0.05,
                            // height: Dimensions.get('screen').height * 0.05,
                            // borderRadius:
                            //   Dimensions.get('screen').height * 0.05 * 0.5,
                            // alignItems: 'center',
                            // justifyContent: 'center',
                            // backgroundColor: 'white',
                          }
                        }>
                        <Image
                          style={{
                            borderRadius: Dimensions.get('screen').height * 0.1,
                            borderWidth: 1,
                            width: Dimensions.get('screen').height * 0.2,
                            height: Dimensions.get('screen').height * 0.2,
                            alignSelf: 'center',
                          }}
                          source={require('../assets/image/profile-pic.jpg')}
                        />
                      </View>
                      {/* user details */}
                      <View style={{ padding: '5%' }}>
                        {/* User name */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].name}`}</Text>
                        </View>
                        {/* User email */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].email}`}</Text>
                        </View>
                        {/* User name */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].name}`}</Text>
                        </View>
                        {/* User contact number */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].contact}`}</Text>
                        </View>
                        {/* User email */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].email}`}</Text>
                        </View>
                      </View>
                    </View>
                    {/* view profile button part */}
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'flex-end',
                        flexDirection: 'row',
                        padding: 20,
                        // borderWidth: 1,
                      }}>
                      <View>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            // borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: 150,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            setShowGuestProfile(true);
                            setShowFeedbackDetails(false);
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              VIEW PROFILE
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  {/* summary */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      padding: 20,
                    }}>
                    {/* Walk-ins */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>Walk-ins</Text>
                      </View>
                      <View>
                        <Text>0</Text>
                      </View>
                    </View>
                    {/* Cancellation */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>Cancellation</Text>
                      </View>
                      <View>
                        <Text>14</Text>
                      </View>
                    </View>
                    {/* No-shows */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>No-shows</Text>
                      </View>
                      <View>
                        <Text>4</Text>
                      </View>
                    </View>
                    {/* Total Visits */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>Total Visits</Text>
                      </View>
                      <View>
                        <Text>137</Text>
                      </View>
                    </View>
                    {/* Reservations */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>Reservations</Text>
                      </View>
                      <View>
                        <Text>142</Text>
                      </View>
                    </View>
                    {/* Avg turn time */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                      <View>
                        <Text>Avg turn time</Text>
                      </View>
                      <View>
                        <Text>1h 13m</Text>
                      </View>
                    </View>
                  </View>
                </View>
                {/* Bottom part */}
                <View
                  style={{
                    // borderWidth: 1,
                    width: Dimensions.get('screen').width * 0.62,
                    height: Dimensions.get('screen').height * 0.42,
                    backgroundColor: 'white',
                  }}>
                  {/* top row view */}
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        padding: 20,
                        // borderWidth:1
                      }}>
                      {/* Indoor */}
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          borderWidth: 1,
                          margin: 5,
                          padding: 5,
                          justifyContent: 'center',
                          borderColor: '#616A6B',
                        }}>
                        <View style={{ flexDirection: 'row' }}>
                          <Icon
                            name="person-outline"
                            size={16}
                            color="black"
                            style={{}}
                          />
                          <Text>2</Text>
                        </View>
                        <View>
                          <Text>Indoor</Text>
                        </View>
                      </View>
                      {/* Tables */}
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          borderWidth: 1,
                          margin: 5,
                          padding: 5,
                          justifyContent: 'center',
                          borderColor: '#616A6B',
                        }}>
                        <View>
                          <Text>Tables</Text>
                        </View>
                        <View>
                          <Text>1</Text>
                        </View>
                      </View>
                      {/* Visit Date */}
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          borderWidth: 1,
                          margin: 5,
                          padding: 5,
                          justifyContent: 'center',
                          borderColor: '#616A6B',
                        }}>
                        <View>
                          <Text>Visit Date</Text>
                        </View>
                        <View>
                          <Text>Tue, 13th April 2021</Text>
                        </View>
                      </View>
                      {/* Time */}
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          borderWidth: 1,
                          margin: 5,
                          padding: 5,
                          justifyContent: 'center',
                          borderColor: '#616A6B',
                        }}>
                        <View>
                          <Text>Time</Text>
                        </View>
                        <View>
                          <Text>6:00 PM</Text>
                        </View>
                      </View>
                      {/* Would Recommend */}
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          borderWidth: 1,
                          margin: 5,
                          padding: 5,
                          justifyContent: 'center',
                          borderColor: '#616A6B',
                        }}>
                        <View>
                          <Text>Would Recommend</Text>
                        </View>
                        <View>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: 110,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => { }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Recommend
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                  {/* middle row view */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      padding: 5,
                    }}>
                    {/* Reservation tags */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 0.5,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text>Reservation tags</Text>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 0.5,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          No reservation tags have been added
                        </Text>
                      </View>
                    </View>
                    {/* Reservation notes */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 0.5,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text>Reservation notes</Text>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 0.5,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          No reservation notes have been added
                        </Text>
                      </View>
                    </View>
                  </View>
                  {/* bottom row view about the rating speedometer */}
                  <View
                    style={{
                      padding: 20,
                      justifyContent: 'space-around',
                      flexDirection: 'row',
                    }}>
                    {/* Overall */}
                    <View>
                      <ProgressCircle
                        percent={(4.3 / 5.0) * 100}
                        radius={40}
                        borderWidth={5}
                        color="#2ECC71"
                        shadowColor="#999"
                        bgColor="#fff">
                        <Text style={{ fontSize: 18 }}>{'4.3'}</Text>
                      </ProgressCircle>
                    </View>
                    {/* Food/Drink */}
                    <View style={{ alignItems: 'center' }}>
                      <ProgressCircle
                        percent={(5 / 5.0) * 100}
                        radius={30}
                        borderWidth={5}
                        color="#239B56"
                        shadowColor="#999"
                        bgColor="#fff">
                        <Text style={{ fontSize: 18 }}>{'5'}</Text>
                      </ProgressCircle>
                      <Text>Food/Drink</Text>
                    </View>
                    {/* Atmosphere */}
                    <View style={{ alignItems: 'center' }}>
                      <ProgressCircle
                        percent={(4 / 5.0) * 100}
                        radius={30}
                        borderWidth={5}
                        color="#2ECC71"
                        shadowColor="#999"
                        bgColor="#fff">
                        <Text style={{ fontSize: 18 }}>{'4'}</Text>
                      </ProgressCircle>
                      <Text>Atmosphere</Text>
                    </View>
                    {/* Service */}
                    <View style={{ alignItems: 'center' }}>
                      <ProgressCircle
                        percent={(4 / 5.0) * 100}
                        radius={30}
                        borderWidth={5}
                        color="#2ECC71"
                        shadowColor="#999"
                        bgColor="#fff">
                        <Text style={{ fontSize: 18 }}>{'4'}</Text>
                      </ProgressCircle>
                      <Text>Service</Text>
                    </View>
                    {/* Value */}
                    <View style={{ alignItems: 'center' }}>
                      <ProgressCircle
                        percent={(4 / 5.0) * 100}
                        radius={30}
                        borderWidth={5}
                        color="#2ECC71"
                        shadowColor="#999"
                        bgColor="#fff">
                        <Text style={{ fontSize: 18 }}>{'4'}</Text>
                      </ProgressCircle>
                      <Text>Value</Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          ) : null}

          {/* Show guest profile */}
          {showGuestProfile ? (
            <View
              style={{
                width: Dimensions.get('screen').width * 0.63,
                height: Dimensions.get('screen').height * 0.76,
                alignItems: 'center',
                paddingTop: 10,

                // borderWidth:1
              }}>
              {/* whole details container */}
              <View>
                {/* Top part */}
                <View
                  style={{
                    // borderWidth: 1,
                    width: Dimensions.get('screen').width * 0.62,
                    height: Dimensions.get('screen').height * 0.66,
                    backgroundColor: 'white',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-start',
                      // borderWidth:1
                    }}>
                    {/* User pic and user details */}
                    <View
                      style={{
                        flex: 2,
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                      }}>
                      {/* Picture */}
                      <View
                        style={
                          {
                            // backgroundColor:'#E5E8E8',
                            // borderWidth:1,
                            // marginRight: 30,
                            // width: Dimensions.get('screen').height * 0.05,
                            // height: Dimensions.get('screen').height * 0.05,
                            // borderRadius:
                            //   Dimensions.get('screen').height * 0.05 * 0.5,
                            // alignItems: 'center',
                            // justifyContent: 'center',
                            // backgroundColor: 'white',
                          }
                        }>
                        <Image
                          style={{
                            borderRadius: Dimensions.get('screen').height * 0.1,
                            borderWidth: 1,
                            width: Dimensions.get('screen').height * 0.2,
                            height: Dimensions.get('screen').height * 0.18,
                            alignSelf: 'center',
                          }}
                          source={require('../assets/image/profile-pic.jpg')}
                        />
                      </View>
                      {/* user details */}
                      <View style={{ padding: '5%' }}>
                        {/* User name */}
                        <View>
                          <Text>{`${dummyFeedback[feedbackSelected].name}`}</Text>
                        </View>
                        {/* Temp 1 */}
                        <View>
                          <Text>{`-`}</Text>
                        </View>
                        {/* Temp 2 */}
                        <View>
                          <Text>{`-`}</Text>
                        </View>
                        {/* Temp 3 */}
                        <View>
                          <Text>{`-`}</Text>
                        </View>
                        {/* Temp 4 */}
                        <View>
                          <Text>{`-`}</Text>
                        </View>
                      </View>
                    </View>
                    {/* change guest button part */}
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'flex-end',
                        flexDirection: 'row',
                        padding: 20,
                        // borderWidth: 1,
                      }}>
                      <View>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            // borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: 150,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            setShowGuestProfile(true);
                            setShowFeedbackDetails(false);
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Change guest
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  {/* summary */}
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      padding: 10,
                      // borderWidth:1,
                    }}>
                    {/* Party size */}
                    <View
                      style={{
                        alignItems: 'center',
                        paddingHorizontal: 10,
                        // width: Dimensions.get('screen').width * 0.1,
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        marginHorizontal: 10,
                      }}>
                      <View>
                        <Text>Party size</Text>
                      </View>
                      <View>
                        <Text>2</Text>
                      </View>
                    </View>
                    {/* Time */}
                    <View
                      style={{
                        alignItems: 'center',
                        paddingHorizontal: 10,
                        // width: Dimensions.get('screen').width * 0.1,
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        marginHorizontal: 10,
                      }}>
                      <View>
                        <Text>Time</Text>
                      </View>
                      <View>
                        <Text>4:00 PM</Text>
                      </View>
                    </View>
                    {/* Indoor */}
                    <View
                      style={{
                        alignItems: 'center',
                        paddingHorizontal: 10,
                        // width: Dimensions.get('screen').width * 0.1,
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        marginHorizontal: 10,
                      }}>
                      <View>
                        <Text>Indoor</Text>
                      </View>
                      <View>
                        <Text>1+</Text>
                      </View>
                    </View>
                    {/* Turn time */}
                    <View
                      style={{
                        alignItems: 'center',
                        paddingHorizontal: 10,
                        // width: Dimensions.get('screen').width * 0.1,
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        marginHorizontal: 10,
                      }}>
                      <View>
                        <Text>Turn time</Text>
                      </View>
                      <View>
                        <Text>1h 30m</Text>
                      </View>
                    </View>
                  </View>
                  {/* Reservation tags and notes view */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      padding: 5,
                      marginVertical: 10,
                    }}>
                    {/* Reservation tags */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                          justifyContent: 'space-between',
                          flexDirection: 'row',
                        }}>
                        <Text>Reservation tags</Text>
                        <TouchableOpacity onPress={() => { }}>
                          <Text style={{ color: '#3498DB' }}>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          No reservation tags have been added
                        </Text>
                      </View>
                    </View>
                    {/* Reservation notes */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                          justifyContent: 'space-between',
                          flexDirection: 'row',
                        }}>
                        <Text>Reservation notes</Text>
                        <TouchableOpacity onPress={() => { }}>
                          <Text style={{ color: '#3498DB' }}>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          No reservation notes have been added
                        </Text>
                      </View>
                    </View>
                  </View>
                  {/* Created */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      padding: 5,
                      marginVertical: 10,
                    }}>
                    {/* Created */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                        }}>
                        <Text>Created</Text>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          2:19 PM 3 Dec 2021
                        </Text>
                        <View
                          style={{
                            borderWidth: 2,
                            borderRadius: Dimensions.get('screen').width * 0.01,
                            paddingVertical: 5,
                            paddingHorizontal: 15,
                          }}>
                          <TouchableOpacity onPress={() => { }}>
                            <Text>In-house</Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                    {/* Standardize style */}
                    <View style={{ width: '45%' }} />
                  </View>
                  {/* Manual entry */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      padding: 5,
                      marginVertical: 10,
                    }}>
                    {/* Manual entry */}
                    <View style={{ width: '45%' }}>
                      {/* Header */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                          justifyContent: 'space-between',
                          flexDirection: 'row',
                        }}>
                        <Text>Manual entry</Text>
                        <TouchableOpacity onPress={() => { }}>
                          <Text style={{ color: '#3498DB' }}>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      {/* Body */}
                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#616A6B',
                          padding: 8,
                          justifyContent: 'space-between',
                          flexDirection: 'row',
                        }}>
                        <Text style={{ color: '#616A6B' }}>
                          Bill amount manual entry
                        </Text>
                        <Text>RM0.00</Text>
                      </View>
                    </View>
                    {/* Standardize style */}
                    <View style={{ width: '45%' }} />
                  </View>
                  {/* Reservation details */}
                  <View></View>
                </View>
              </View>
            </View>
          ) : null}
        </View>
      </View>
    </View>)
  );
};

// Style sheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('screen').width * 0.4,
    minHeight: Dimensions.get('screen').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
    // borderWidth:1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default FeedbackScreen;
