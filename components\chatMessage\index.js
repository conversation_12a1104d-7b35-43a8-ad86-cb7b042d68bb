/* eslint-disable no-underscore-dangle, no-use-before-define */

import PropTypes from 'prop-types'
import React from 'react'
import { View, StyleSheet, Dimensions } from 'react-native'

import { Avatar, Day, utils } from 'react-native-gifted-chat'
import Colors from '../../constant/Colors'
import AsyncImage from '../asyncImage'
import ChatBubble from '../chatBubble'

import {
    TextPropTypes,
    ViewPropTypes,
} from 'deprecated-react-native-prop-types';

const { isSameUser, isSameDay } = utils

export default class ChatMessage extends React.Component {
    getInnerComponentProps() {
        const { containerStyle, ...props } = this.props
        return {
            ...props,
            position: 'left',
            isSameUser,
            isSameDay,
        }
    }

    getInnerComponentPropsAvatar() {
        const { containerStyle, ...props } = this.props
        return {
            ...props,
            position: 'left',
            function() { return true },
            function() { return true },
        }
    }

    renderDay() {
        if (this.props.currentMessage.createdAt) {
            const dayProps = this.getInnerComponentProps()
            if (this.props.renderDay) {
                return this.props.renderDay(dayProps)
            }
            return <Day {...dayProps} />
        }
        return null
    }

    renderBubble() {
        const bubbleProps = this.getInnerComponentProps()
        if (this.props.renderBubble) {
            return this.props.renderBubble(bubbleProps)
        }
        return <ChatBubble {...bubbleProps} />
    }

    renderAvatar() {
        var isFirstMessage = false;        

        if (!this.props.previousMessage._id) {
            isFirstMessage = true;
        }

        const avatarProps = this.getInnerComponentProps();

        let extraStyle
        if (
            isSameUser(this.props.currentMessage, this.props.previousMessage) &&
            isSameDay(this.props.currentMessage, this.props.previousMessage)
        ) {
            // Set the invisible avatar height to 0, but keep the width, padding, etc.

            // if (this.props.nextMessage.user !== undefined) {
            //     extraStyle = {                    
            //         borderWidth: 0
            //     };
            // }

            extraStyle = {                    
                borderWidth: 0,
                height: 0,
            };
        }

        if (
            isSameUser(this.props.currentMessage, this.props.nextMessage) &&
            isSameDay(this.props.currentMessage, this.props.nextMessage)
        ) {
            // extraStyle = {                
            //     borderWidth: 0
            // };
        }
        
        return (
            // <AsyncImage
            //     // {...avatarProps}
            //     // source={{
            //     //     uri: avatarProps.currentMessage.user.avatar,
            //     // }}
            //     style={{
            //         left: [styles.slackAvatar, avatarProps.imageStyle, extraStyle],
            //     }}
            // />
            (<Avatar
                {...avatarProps}
                showAvatarForEveryMessage={true}
                imageStyle={{
                    left: [styles.slackAvatar, avatarProps.imageStyle, extraStyle],
                }}

            />)
        );
    }

    render() {
        const marginBottom = isSameUser(
            this.props.currentMessage,
            this.props.nextMessage,
        )
            ? 2
            : 10

        return (
            <View>
                {this.renderDay()}
                <View
                    style={[
                        styles.container,
                        { marginBottom },
                        this.props.containerStyle,
                    ]}
                >
                    {this.renderAvatar()}
                    {this.renderBubble()}
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'flex-start',
        marginLeft: 8,
        marginRight: 0,
    },
    slackAvatar: {
        // The bottom should roughly line up with the first line of message text.
        height: 44,
        width: 44,
        borderRadius: 3,

        borderWidth: 1,
        borderColor: Colors.primaryColor,
        bottom: 7,
        marginRight: 6,
    },
})

ChatMessage.defaultProps = {
    renderAvatar: undefined,
    renderBubble: null,
    renderDay: null,
    currentMessage: {},
    nextMessage: {},
    previousMessage: {},
    user: {},
    containerStyle: {},
}

ChatMessage.propTypes = {
    renderAvatar: PropTypes.func,
    renderBubble: PropTypes.func,
    renderDay: PropTypes.func,
    currentMessage: PropTypes.object,
    nextMessage: PropTypes.object,
    previousMessage: PropTypes.object,
    user: PropTypes.object,
    containerStyle: PropTypes.shape({
        left: ViewPropTypes.style,
        right: ViewPropTypes.style,
    }),
}