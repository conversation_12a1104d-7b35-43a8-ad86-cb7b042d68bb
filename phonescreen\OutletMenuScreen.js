import React, { Component, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import * as Cart from '../util/Cart';
import Draggable from 'react-native-draggable';
import Back from 'react-native-vector-icons/EvilIcons';
import { FlatList } from 'react-native-gesture-handler';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { ORDER_TYPE } from '../constant/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import AsyncImage from '../components/asyncImage';

/**
 * OutletMenuScreen
 * function
 * *display list of menu for purchase
 * *access to cart screen
 * 
 * route.params
 * *outletData: array of data of the current outlet 
 * *orderType: type of order being made (takeaway, pick up, dine in)
 * *test: ???
 * *navFrom: the screen stack this route is on 
 */

const OutletMenuScreen = props => {
  const {
    navigation,
    route
  } = props;

  // const { orderType } = route.params;
  const outletDataParam = route.params.outletData;
  const testParam = route.params.test;
  const navFromParam = route.params.navFrom;

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={() => { props.navigation.goBack(); }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: Platform.OS == 'android' ? 9 : 10,
            opacity: 0.8,
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              marginTop: -3,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -2,
      }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          {'Dine In'}
        </Text>
      </View>
    ),
    headerRight: () => (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => { props.navigation.navigate('Profile') }}>
          <Image style={{
            width: 32,
            height: 32,
            marginTop: 8,
            marginRight: 25,
          }} source={require('../assets/image/drawer.png')} />
        </TouchableOpacity>
      </View>
    ),
  });

  // const [outletData, setOutletData] = useState(outletDataParam);

  // const outletData = MerchantStore.useState(s => s.allOutlets[0]);
  const outletData = MerchantStore.useState(s => s.currOutlet);

  console.log('outletData');
  console.log(outletData);

  const [outletMenu, setOutletMenu] = useState([]);
  const [category, setCategory] = useState('');
  const [menu, setMenu] = useState([]);
  const [cartIcon, setCartIcon] = useState(false);
  const [reverse, setReverse] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [test, setTest] = useState(testParam);
  const [currentMenu, setCurrentMenu] = useState([]);
  const [productList2, setProductList2] = useState([]);
  const [productList, setProductList] = useState([]);
  const [choice, setChoice] = useState(null);
  const [categoryIndex, setCategoryIndex] = useState(0);
  const [navFrom, setNavFrom] = useState(navFromParam);
  const [isInfoTabHitTop, setIsInfoTabHitTop] = useState(false);
  const [onStartVisible, setOnStartVisible] = useState(false);
  const [cartWarning, setCartWarning] = useState(false);
  const [cartProceed, setCartProceed] = useState([]);

  const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);
  const selectedOutletItemCategories = CommonStore.useState(s => s.selectedOutletItemCategories);
  const selectedOutletItemCategory = CommonStore.useState(s => s.selectedOutletItemCategory);

  const cartItems = CommonStore.useState(s => s.cartItems);
  const orderType = CommonStore.useState(s => s.orderType);

  const outletItems = OutletStore.useState(s => s.outletItems);
  const outletCategories = OutletStore.useState(s => s.outletCategories);

  useEffect(() => {
    if (cartItems.length > 0) {
      setCartIcon(true);
    }
  }, [cartItems.length]);

  const setState = () => { };

  // useEffect(() => {
  //   categoryFunc();
  //   refresh();
  //   refreshMenu()
  //   // ApiClient.GET(API.outlet2 + outletData.id).then((result) => {
  //   //   // console.log(result)
  //   //   setState({ outletData: result })

  //   //   refreshMenu();
  //   // });
  //   // menu item
  //   // ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
  //   //   // console.log(result)
  //   //   if (result.length > 0) {
  //   //     setState({ category: result[0].category, menu: result[0].items })
  //   //   }
  //   //   setState({ outletMenu: result })
  //   // });

  //   // setInterval(() => {
  //   //   cartCount();
  //   //   getCartItem();
  //   // }, 1000);
  //   setInterval(() => {
  //     cartCount();
  //     getCartItem();
  //   }, 2500);
  // }, []);

  // function here

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() })
    // console.log(cartItem)
  }

  const cartCount = () => {
    if (Cart.getCartItem() !== null) {
      if (Cart.getCartItem().length > 0) {
        setState({ cartIcon: true })
      }
      else {
        setState({ cartIcon: false })
      }
    }
    else {
      setState({ cartIcon: false })
    }
  }

  const goToCart = () => {
    if (Cart.getCartItem().length > 0) {
      if (navFrom == "TAKEAWAY") {
        props.navigation.navigate('Cart', { screen: "Cart", params: { test: test, outletData: outletData, navFrom: navFrom } })
      }
      else {
        props.navigation.navigate("Cart", { test: test, outletData: outletData });
      }

    } else {
      Alert.alert("Info", "No items in your cart at the moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }
  }

  const onCartClicked = () => {
    if (cartItems.length > 0) {
      if (navFrom == "TAKEAWAY") {
        // props.navigation.navigate('Cart', { screen: "Cart", params: { test: test, outletData: outletData, navFrom: navFrom } })
        props.navigation.navigate('Cart', { test: test, outletData: outletData, navFrom: navFrom });
      }
      else {
        props.navigation.navigate("Cart", { test: test, outletData: outletData });
      }

    } else {
      Alert.alert("Info", "No items in your cart at the moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }
  };

  const categoryFunc = () => {

    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      const tmpCategories = {};
      for (const category of result) {
        const categoryName = category.name
        const categoryId = category.id
        if (!tmpCategories[categoryName]) {
          tmpCategories[categoryName] = {
            label: categoryName,
            value: categoryId,
          };
        }

      }
      const categories = Object.values(tmpCategories);
      setState({ categoryOutlet: categories, category: categories[0].label });

    }).catch(err => {
      console.log("Error")
      console.log(err)
    });
  }

  const refresh = () => {
    ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
      if (result != undefined && result.length > 0) {
        var productListRaw = [];

        result.forEach((element) => {
          console.log(element.items);
          productListRaw = productListRaw.concat(element.category);
          const activeItem = productListRaw.filter(item => item.active == 1)
          setState({ productList: productListRaw, productList2: activeItem, }, () => { });
        });
      }
    });
  }

  const refreshMenu = () => {
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      const category = result.filter(i => i.name == category)
      category.map(i => {
        // const newList = []
        // for (const item of i.items){
        //   if(item.name !== ""){
        //     newList.push(item)
        //   }
        // }
        // setState({ currentMenu: newList })
        setState({ currentMenu: i.items })


      })
      // }

      // }else{
      //   setState({ currentMenu: result });
      // }

    });
  }

  // const refresh = () => {
  //   setState({ refresh: true });
  // }

  const renderMenu = ({ item }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    const itemsInCart = cartItems.filter(obj => obj.itemId === item.uniqueId);
    if (itemsInCart) {
      for (const obj of itemsInCart) {
        quantity += parseInt(obj.quantity);
      }
    }

    var itemTitleFont = 16;

    if (Dimensions.get('screen').width <= 360) {
      itemTitleFont = 14;
      //console.log(Dimensions.get('screen').width)
    }

    const itemTitleTextScale = {
      fontSize: itemTitleFont,
    };

    if (item.categoryId === selectedOutletItemCategory.uniqueId) {
      return (
        <TouchableOpacity onPress={() => {
          if (checkCartOutlet()) {
            setState({ cartWarning: true, })
          } else {
            if (item.isActive) {
              CommonStore.update(s => {
                s.selectedOutletItem = item;
              });

              props.navigation.navigate('MenuItemDetails', { refresh: refresh.bind(this), menuItem: item, outletData: outletData })
            }
            else {
              Alert.alert(
                'Info',
                'Sorry, this product is not available for at the moment',
              );
            }
          }

        }}>
          <View style={{
            flexDirection: 'row',
            paddingHorizontal: 20,
            paddingBottom: 15,
            paddingTop: 10,
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}>
            <View style={{
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              width: "75%",
              display: 'flex',
              justifyContent: 'flex-start',
              // backgroundColor: 'blue',
            }}>
              <View style={[{
                backgroundColor: Colors.secondaryColor,
                // width: 60,
                // height: 60,
                width: Dimensions.get('screen').width * 0.22,
                height: Dimensions.get('screen').width * 0.22,
                borderRadius: 10,
              }, item.image ? {

              } : {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }]}>
                {item.image
                  ?
                  <AsyncImage source={{ uri: item.image }} item={item} style={{
                    // width: 60,
                    // height: 60,
                    width: Dimensions.get('screen').width * 0.22,
                    height: Dimensions.get('screen').width * 0.22,
                    borderRadius: 10
                  }} />
                  :
                  <Ionicons name="fast-food-outline" size={50} />
                }

                {
                  !item.isActive
                    ?
                    <View style={{
                      position: 'absolute',
                      zIndex: 3,
                    }}>
                      <View
                        style={{
                          // width: 120,
                          width: Dimensions.get('screen').width * 0.25,
                          left: -Dimensions.get('screen').width * 0.03,
                          padding: 0,
                          paddingLeft: Dimensions.get('screen').width * 0.02,
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: Colors.tabRed,
                          height: 20,
                          borderTopRightRadius: 10,
                          borderBottomRightRadius: 3,
                        }}>
                        <Text style={{
                          color: '#FFF',
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 10,
                          bottom: 1,
                        }}>Not available</Text>
                      </View>
                      <View
                        style={{
                          left: -Dimensions.get('screen').width * 0.03,
                          width: 0,
                          height: 0,
                          backgroundColor: 'transparent',
                          borderStyle: 'solid',
                          borderRightWidth: Dimensions.get('screen').width * 0.03,
                          borderTopWidth: Dimensions.get('screen').width * 0.03,
                          borderRightColor: 'transparent',
                          borderTopColor: 'red',
                          transform: [{ rotate: '90deg' }],
                        }}
                      />
                    </View>
                    :
                    <></>
                }
              </View>
              <View style={{
                marginLeft: 15,
                // flexDirection: 'row',
                // flexShrink: 1,
                width: '55%',
                // backgroundColor: 'red',
              }}>
                <Text
                  numberOfLines={3}
                  style={[itemTitleTextScale, {
                    //fontSize: 16,
                    textTransform:
                      'uppercase',
                    fontFamily: "NunitoSans-Bold",
                    // flexWrap: 'wrap',
                    // flex: 1,
                    // flexShrink: 1,    
                    // width: '100%',
                  }]}>{item.name}</Text>

                <Text style={{
                  color: Colors.primaryColor,
                  fontFamily: "NunitoSans-Bold",
                  paddingTop: 5,
                  fontSize: 16,
                }}>RM{parseFloat(item.price).toFixed(2)}</Text>
              </View>
            </View>

            <View style={{
              flexDirection: 'row',
              // width: "20%", 
              // marginLeft: 60 
            }}>
              <View style={{
                backgroundColor: "#e3e1e1",
                // width: 67, 
                // height: 24,

                width: 68,
                height: 26,

                // paddingVertical: 4,
                // paddingHorizontal: 20, 

                borderRadius: 10,
                justifyContent: "center",
                alignSelf: "center"
              }}>
                <TouchableOpacity onPress={() => {
                  if (checkCartOutlet()) {
                    setState({ cartWarning: true, cartProceed: item })
                  } else {
                    if (item.isActive) {
                      CommonStore.update(s => {
                        s.selectedOutletItem = item;
                      });

                      props.navigation.navigate('MenuItemDetails', { refresh: refresh.bind(this), menuItem: item, outletData: outletData })
                    }
                    else {
                      Alert.alert(
                        'Info',
                        'Sorry, this product is not available for at the moment',
                      );
                    }
                  }

                }}>
                  <Text style={{
                    alignSelf: "center",
                    color: "#8f8f8f",
                    fontSize: 13,
                    fontFamily: "NunitoSans-Bold"
                  }}>{quantity > 0 ? quantity : "Add"}</Text>

                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  }

  // onContainerScroll = e => {
  //   console.log(Dimensions.get('screen').width);
  //   console.log(e.nativeEvent.contentOffset.y);
  //   console.log('---------------------------')

  //   if (e.nativeEvent.contentOffset.y * 2 >= Dimensions.get('screen').width) {
  //     console.log('hit top');

  //     // setState({
  //     //   isInfoTabHitTop: true,
  //     // });
  //   }
  //   else {
  //     console.log('not hit top');

  //     // setState({
  //     //   isInfoTabHitTop: false,
  //     // });
  //   }
  // }
  const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    const paddingToBottom = 20;
    return layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom;
  };

  const nextCategory = () => {
    const catLength = categoryOutlet.length
    if (categoryIndex == catLength - 1) {
      setState({
        category: categoryOutlet[0],
        categoryIndex: 0,
        // menu: choice[index].category,

      })
    } else {
      setState({
        category: categoryOutlet[categoryIndex + 1],
        categoryIndex: categoryIndex + 1,
        // menu: choice[index].category,

      })
    }
    refreshMenu();
  }

  const checkCartOutlet = () => {
    const outletId = outletData.id
    console.log(Cart.getOutletId() != null)
    if (outletId != Cart.getOutletId() && Cart.getOutletId() != null) {
      return true
    }
    return false
  }

  // function end

  return (
    <View style={styles.container}>
      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={cartWarning}
        transparent={true}
        animationType="slide">
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: Dimensions.get('window').height,
          }}>
          <View style={styles.confirmBox}>
            <TouchableOpacity
              onPress={() => {
                setState({ cartWarning: false });
              }}>
              <View
                style={{
                  alignSelf: 'flex-start',
                  padding: 14,
                }}>
                <Close name="close" size={35} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10 }}>
              <Text
                style={{
                  textAlign: 'center',
                  fontWeight: '700',
                  fontSize: 18,
                }}>
                You are entering a different outlet
              </Text>
              <Text
                style={{
                  textAlign: 'center',
                  fontWeight: '700',
                  fontSize: 14,
                }}>
                Your existing cart items will be cleared if you proceed. Are you sure?
              </Text>
            </View>
            <View
              style={{
                alignSelf: 'center',
                marginTop: 30,
                justifyContent: 'center',
                alignItems: 'center',
                width: 250,
                height: 40,
                alignContent: 'center',
                marginTop: 40
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignContent: 'center',
                  borderRadius: 10,
                  height: 60,
                  marginTop: 30,
                  alignSelf: 'center',
                }} onPress={() => {
                  setState({ cartWarning: false, })
                  Cart.clearCart();

                  // CommonStore.update(s => {
                  //   s.selectedOutletItem = item;
                  // });

                  props.navigation.navigate('MenuItemDetails', { refresh: refresh.bind(this), menuItem: cartProceed, outletData: outletData })
                }}>
                <Text style={{ fontSize: 28, color: Colors.whiteColor }}>
                  Proceed
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => { setState({ visible: false }) }}
                style={{
                  backgroundColor: Colors.secondaryColor,
                  width: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignContent: 'center',
                  borderRadius: 10,
                  height: 60,
                  marginTop: 20
                }} onPress={() => { setState({ cartWarning: false, }) }}>
                <Text style={{ fontSize: 28, color: Colors.whiteColor }}>
                  Take me back
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <ScrollView showsHorizontalScrollIndicator={false}
        // onScroll={onContainerScroll}
        onScroll={({ nativeEvent }) => {
          if (isCloseToBottom(nativeEvent)) {
            //console.log("HIT BOTTOM")
            //nextCategory()
          }
        }}
        stickyHeaderIndices={[1]}
      >
        <Image
          source={{ uri: outletData.cover }}
          style={styles.outletCover}
        />

        <View style={{ flexDirection: 'row' }}>
          <View style={{
            position: 'absolute', alignItems: 'center',
            paddingVertical: 12,
            width: 20,
            height: 49,
            backgroundColor: Colors.highlightColor,
            opacity: 0.85,
            shadowColor: '#000',
            shadowOffset: {
              width: 1,
              height: 0.5,
            },
            shadowOpacity: 0.12,
            shadowRadius: 3.22,
          }}>
            <Entypo name='chevron-thin-left' size={20} color={Colors.primaryColor} style={{ marginLeft: -1, }} />
          </View>
          <View style={{
            position: 'absolute', alignItems: 'center',
            alignSelf: 'flex-end',
            paddingVertical: 12,
            width: 20,
            height: 49,
            backgroundColor: Colors.highlightColor,
            opacity: 0.85,
            shadowColor: '#000',
            shadowOffset: {
              width: 1,
              height: 0.5,
            },
            shadowOpacity: 0.12,
            shadowRadius: 3.22,
          }}>
            <Entypo name='chevron-thin-right' size={20} color={Colors.primaryColor} style={{ marginLeft: -1, }} />
          </View>
          <ScrollView
            showsHorizontalScrollIndicator={false}
            alwaysBounceHorizontal={true}
            horizontal={true}
            onScrollBeginDrag={() => console.log('Start')}
            contentContainerStyle={{
              paddingLeft: 20,
            }}
            style={[styles.infoTab, {
              zIndex: -1,
              // ...!isInfoTabHitTop && { position: 'absolute' },
              // ...!isInfoTabHitTop && { top: 120 },
            }]}
          // stickyHeaderIndices={[0]} 
          >

            {/* {categoryOutlet.map((item, index) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setState({
                      category: item.label,
                      categoryIndex: index,
                      // menu: choice[index].category,

                    })
                    refreshMenu();
                  }}>
                  <View
                    style={[
                      styles.category,
                      {
                        borderBottomColor:
                          category == item.label
                            ? Colors.primaryColor
                            : null,
                        borderBottomWidth:
                          category == item.label ? 3 : 0,
                      },
                    ]}>
                    <Text
                      style={{
                        textTransform: 'capitalize',
                        paddingVertical: 12,
                        fontFamily: category == item.label
                          ? "NunitoSans-Bold"
                          : "NunitoSans-Regular",
                        color: category == item.label
                          ? Colors.primaryColor
                          : Colors.blackColor,
                        fontSize: 15,
                      }}>
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })} */}

            {outletCategories.map((item, index) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    // setState({
                    //   category: item.label,
                    //   categoryIndex: index,
                    //   // menu: choice[index].category,

                    // })
                    // refreshMenu();

                    CommonStore.update(s => {
                      s.selectedOutletItemCategory = item;
                    });
                  }}>
                  <View
                    style={[
                      styles.category,
                      {
                        // borderBottomColor:
                        //   selectedOutletItemCategory.name == item.name
                        //     ? Colors.primaryColor
                        //     : null,
                        // borderBottomWidth:
                        //   selectedOutletItemCategory.name == item.name ? 3 : 0,
                      },
                    ]}>
                    <View style={{
                      borderBottomColor:
                        selectedOutletItemCategory.name == item.name
                          ? Colors.primaryColor
                          : null,
                      borderBottomWidth:
                        selectedOutletItemCategory.name == item.name ? 3 : 0,
                    }}>
                      <Text
                        style={{
                          textTransform: 'capitalize',
                          paddingVertical: 12,
                          fontFamily: selectedOutletItemCategory.name == item.name
                            ? "NunitoSans-Bold"
                            : "NunitoSans-Regular",
                          color: selectedOutletItemCategory.name == item.name
                            ? Colors.primaryColor
                            : Colors.mainTxtColor,
                          fontSize: 16,
                        }}>
                        {item.name}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        <View>
          <FlatList
            data={outletItems}
            extraData={outletItems}
            renderItem={renderMenu}
            keyExtractor={(item, index) => index}
            contentContainerStyle={{
              paddingLeft: 10,
              paddingRight: 10,
              paddingTop: 20,
            }}
          />
        </View>
        <View style={{ minHeight: 100 }} />
      </ScrollView>

      {cartIcon ?
        <Draggable
          shouldReverse={reverse}
          renderSize={100}
          renderColor={Colors.secondaryColor}
          isCircle
          x={270}
          y={470}
          // onShortPressRelease={() => { goToCart(), cartCount() }}
          // onShortPressRelease={onCartClicked}
          onPressOut={onCartClicked}
        >
          <View style={{ width: 60, height: 60, justifyContent: "center" }}>
            <View style={{ alignSelf: "center" }}>
              <Ionicons name="cart-outline" size={42} color={Colors.mainTxtColor} />
            </View>
            <View style={styles.cartCount}>

              {/* <Text style={{ color: Colors.whiteColor, fontSize: 10, fontFamily: "NunitoSans-Regular" }}>{Cart.getCartItem().length}</Text> */}
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: "NunitoSans-Bold"
              }}>{cartItems.length}</Text>
            </View>
          </View>
        </Draggable>
        : null}
    </View>
  )
}

const styles = StyleSheet.create({
  confirmBox: {
    width: 350,
    height: 350,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
  },
  outletCover: {
    width: '100%',
    alignSelf: 'center',
    height: undefined,
    aspectRatio: 2,
    // borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
  workingHourTab: {
    padding: 16,
    flexDirection: 'row',
  },
  outletAddress: {
    textAlign: 'center',
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: 'row',
    marginTop: 20,
  },
  actionView: {
    width: Dimensions.get('screen').width / 4,
    height: Dimensions.get('screen').width / 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
  category: {
    // width: 150,
    paddingHorizontal: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatCartBtn: {
    zIndex: 2,
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: 'absolute',
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default OutletMenuScreen;
