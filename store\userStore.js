import { Store } from 'pullstate';

export const UserStore = new Store({
    avatar: '',
    dob: null,
    email: '',
    gender: '',
    name: '',
    number: '',
    outletId: '',    
    race: '',  
    state: '',
    uniqueName: '',
    updatedAt: null,

    merchantId: '',
    role: '', // legacy
    refreshToken: '',
    firebaseUid: '',

    ///////////////////////////////////
    // Share from user app

    userAddresses: [],
    selectedUserAddress: null,

    userGroups: ['EVERYONE'],

    isAlphaUser: false, // show all hidden modules if true

    isBetaUser: false, // show all hidden modules if true, for beta one

    privileges: [],
    screensToBlock: [],

    pinNo: '',

    isMasterAccount: undefined,

    //////////////////////////////
    // 2025-06-26 - Invididual User Managed Category
    userManagedCategory: null,
    isIndividualShift: false,
});   
