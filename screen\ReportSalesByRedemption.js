import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    Switch,
    PlatForm,
    Modal as ModalComponent,
    KeyboardAvoidingView,
    Platform,
    TextInput,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import GCalendar from '../assets/svg/GCalendar'
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import moment from 'moment'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import Styles from '../constant/Styles';
import {
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload'
import Download from '../assets/svg/Download'
import RNFetchBlob from 'rn-fetch-blob'
import { listenToUserChangesMerchant, listenToMerchantIdChangesMerchant, listenToCurrOutletIdChangesWaiter, listenToAllOutletsChangesMerchant, listenToCommonChangesMerchant, listenToSelectedOutletItemChanges, convertArrayToCSV, listenToSelectedOutletTableIdChanges, requestNotificationsPermission } from '../util/common';
import XLSX from 'xlsx';
import { useKeyboard } from '../hooks';
import 'react-native-get-random-values';
import { CommonStore } from '../store/commonStore';
import { TempStore } from "../store/tempStore";
const { nanoid } = require('nanoid');
const RNFS = require('@dr.pogodin/react-native-fs');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ReportSalesByRedemption = props => {
    const {
        navigation,
    } = props;

    const [keyboardHeight] = useKeyboard();
    const [list, setList] = useState([]);
    const [page, setPage] = useState(0);
    const [name, setName] = useState('Product');
    const [visible, setVisible] = useState(false);
    const [visible1, setVisible1] = useState(false);
    const [isChecked, setIsChecked] = useState(false);
    const [isChecked1, setIsChecked1] = useState(false);
    const [endDate, setEndDate] = useState(new Date());
    const [startDate, setStartDate] = useState(new Date());
    const [oriList, setOriList] = useState([]);
    const [offset, setOffset] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [day, setDay] = useState(false);
    const [pick, setPick] = useState(null);
    const [pick1, setPick1] = useState(null);
    const [search, setSearch] = useState('');
    const [lists, setLists] = useState([]);
    const [list1, setList1] = useState(true);
    const [searchList, setSearchList] = useState(false);

    const [loading, setLoading] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');
    const [rev_date, setRev_date] = useState(moment().subtract(6, 'days').startOf('day'));
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()));

    const [redemptions, setRedemptions] = useState([]);

    const allOutletsUserOrdersDone = TempStore.useState(s => s.allOutletUserOrderDoneProcessed);
    // const allOutletsUserOrdersDone = OutletStore.useState(s => s.allOutletsUserOrdersDone);
    const outletItems = OutletStore.useState(s => s.outletItems);
    const outletCategories = OutletStore.useState(s => s.outletCategories);
    const outletCategoriesDict = OutletStore.useState(s => s.outletCategoriesDict);

    const currOutletId = MerchantStore.useState(s => s.currOutletId);
    const allOutlets = MerchantStore.useState(s => s.allOutlets);

    const userName = UserStore.useState(s => s.name);
    const merchantName = MerchantStore.useState(s => s.name);

    const [exportEmail, setExportEmail] = useState('');
    const [CsvData, setCsvData] = useState([]);

    const [showDetails, setShowDetails] = useState(false);
    const [redemptionsDetails, setRedemptionsDetails] = useState([]);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);
    useEffect(() => {
        if (currOutletId !== '' && allOutlets.length > 0) {

            var redemptionsDict = {};

            for (var i = 0; i < outletItems.length; i++) {


                if (outletCategoriesDict[outletItems[i].categoryId]) {
                    var productSaleRecord = {
                        summaryId: nanoid(),
                        productName: outletItems[i].name,
                        productCategory: outletCategoriesDict[outletItems[i].categoryId].name,
                        // productCategory: '',
                        // productSku: outletItems[i].sku,
                        productSku: outletItems[i].skuMerchant || 'N/A',
                        totalItems: 0,
                        totalSales: 0,
                        totalSalesReturn: 0,
                        totalDiscount: 0,
                        discount: 0,
                        itemNetSales: 0,
                        detailsList: [],
                    };

                    redemptionsDict[outletItems[i].uniqueId] = productSaleRecord;
                }
            }

            for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
                if (allOutletsUserOrdersDone[i].outletId === currOutletId &&
                    moment(rev_date).isSameOrBefore(allOutletsUserOrdersDone[i].completedDate) &&
                    moment(rev_date1).isAfter(allOutletsUserOrdersDone[i].completedDate)) {
                    for (var j = 0; j < allOutletsUserOrdersDone[i].cartItems.length; j++) {
                        const cartItem = allOutletsUserOrdersDone[i].cartItems[j];

                        redemptionsDict[cartItem.itemId].totalItems += cartItem.quantity;
                        redemptionsDict[cartItem.itemId].totalSales += cartItem.price * cartItem.quantity;
                        redemptionsDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
                        redemptionsDict[cartItem.itemId].itemNetSales += cartItem.price * cartItem.quantity;
                        redemptionsDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);
                    }
                }
            }

            const redemptionsTemp = Object.entries(redemptionsDict).map(([key, value]) => ({ ...value }));

            setRedemptions(redemptionsTemp);

            setCurrentPage(1);
            //setPageCount(Math.ceil(redemptionsTemp.length / perPage));

            const dummyData = [

                "Download Test"
            ]

            setState({ CsvData: dummyData })

        }
    }, [allOutletsUserOrdersDone, currOutletId, allOutlets, rev_date, rev_date1]);

    const setState = () => { };


    // navigation.dangerouslyGetParent().setOptions({
    //     tabBarVisible: false,
    // });

    navigation.setOptions({
        headerLeft: () => (
            <View style={{
                width: Dimensions.get('screen').width * 0.17,
                justifyContent: 'center',
                alignItems: 'center',
            }}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </View>
        ),
        headerTitle: () => (
            <View style={[{
                // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                // bottom: switchMerchant ? '2%' : 0,
                ...global.getHeaderTitleStyle(),
            },
            // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
            ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Redemption Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });


    const searchBarItem = () => {
        ApiClient.GET(
            API.salesBySkuSearchBar +
            1 +
            '&queryName=' +
            search,
        ).then((result) => {

            setState({ lists: result });
        });
    }

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const loadMoreData = () => {
        const data = oriList;
        const slice = data.slice(offset, offset + perPage)
        setState({ list: slice, pageCount: Math.ceil(data.length / perPage) })
    }

    // moment = async () => {
    //     const today = new Date();
    //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
    //     getDetail()
    // }

    const getDetail = () => {
        ApiClient.GET(API.getSalesBySku + 1 + '&startDate=' + startDate + '&endDate=' + endDate).then((result) => {
            var data = result
            var slice = data.slice(offset, offset + perPage)
            setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / perPage) })
        });
    }

    const decimal = (value) => {
        return value.toFixed(2);
    }


    const onItemSummaryClicked = item => {
        setRedemptionsDetails(item.detailsList);
        setShowDetails(true);

        // console.log('item.detailsList');
        // console.log(item.detailsList);
    };

    const renderItem = ({ item, index }) => (
        // (index + 1) % 2 == 0 ? 
        (<TouchableOpacity onPress={() => onItemSummaryClicked(item)} style={{
            backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
            paddingVertical: 10,
            paddingHorizontal: 3,
            paddingLeft: 1,
            borderColor: '#BDBDBD',
            borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
            borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
            // width: '100%',
        }}>
            {/* <View style={{ backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.fieldtBgColor, padding: 12 }}> */}
            <View style={{ flexDirection: 'row', }}>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productCategory}</Text>
                <View style={{ flex: 3 }}>
                    <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productSku}</Text>
                    {/* <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>-</Text> */}
                </View>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalItems}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{(item.totalSales).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{(item.totalSalesReturn).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{(item.totalDiscount).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.discount}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{(item.itemNetSales).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
            </View>
        </TouchableOpacity>)
    )

    // const renderItemDetails = ({ item, index }) => (
    //     (index + 1) % 2 == 0 ?
    //         <View style={{
    //             backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
    //             paddingVertical: 10,
    //             paddingHorizontal: 3,
    //             paddingLeft: 1,
    //             borderColor: '#BDBDBD',
    //             borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
    //             borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
    //         }}>
    //             <View style={{ flexDirection: 'row', }}>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.finalPrice).toFixed(2)}</Text>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{moment(item.createdAt).format('DD MMM hh:mma')}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.discount).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(isFinite(item.finalPrice / item.discount) ? item.finalPrice / item.discount * 100 : 0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.tax).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 1, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text>
    //                 {/* <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text> */}
    //             </View>
    //         </View>
    //         : <View style={{
    //             backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
    //             paddingVertical: 10,
    //             paddingHorizontal: 3,
    //             paddingLeft: 1,
    //             borderColor: '#BDBDBD',
    //             borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
    //             borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
    //         }}>
    //             <View style={{ flexDirection: 'row', }}>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.finalPrice).toFixed(2)}</Text>
    //                 <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{moment(item.createdAt).format('DD MMM hh:mma')}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.discount).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(isFinite(item.finalPrice / item.discount) ? item.finalPrice / item.discount * 100 : 0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.tax).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 1, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text>
    //                 <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text>
    //                 {/* <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text> */}
    //             </View>
    //         </View>
    // )


    const downloadCsv = () => {
        //if (redemptions && redemptions.dataSource && redemptions.dataSource.data) {
        //const csvData = convertArrayToCSV(redemptions.dataSource.data);
        const csvData = convertArrayToCSV(CsvData);

        const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
        // console.log("PATH", pathToWrite)
        RNFetchBlob.fs
            .writeFile(pathToWrite, csvData, 'utf8')
            .then(() => {
                // console.log(`wrote file ${pathToWrite}`);
                // wrote file /storage/emulated/0/Download/data.csv
                Alert.alert(
                    'Success',
                    `Send to ${pathToWrite}`,
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
            })
            .catch(error => console.error(error));
        //}        
    }

    //Haven't real real data for this page yet
    // const convertDataToExcelFormat = () => {
    //     var excelData = [];

    //     if (!showDetails) {
    //         for (var i = 0; i < redemptions.length; i++) {
    //             var excelRow = {
    //                 'Product Name': redemptions[i].productName,
    //                 'Product Category': redemptions[i].productCategory,
    //                 'Product SKU': redemptions[i].productSku,
    //                 'Total Items': parseFloat(redemptions[i].totalItems).toFixed(2),
    //                 'Total Sales': parseFloat(redemptions[i].totalSales).toFixed(2),
    //                 'Total Sales Return': parseFloat(redemptions[i].totalSalesReturn).toFixed(2),
    //                 'Total Discount': parseFloat(redemptions[i].totalDiscount).toFixed(2),
    //                 'Discount': parseFloat(redemptions[i].discount).toFixed(2),
    //                 'Item Net Sales': parseFloat(redemptions[i].itemNetSales).toFixed(2),
    //             };

    //             excelData.push(excelRow);
    //         }
    //     }
    //     else {
    //         for (var i = 0; i < transactionTypeSalesDetails.length; i++) {
    //             var excelRow = {
    //                 Have to change to product sales details
    //                 'Transaction Category': ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType],
    //                 'Sales (RM)': parseFloat(transactionTypeSalesDetails[i].finalPrice).toFixed(2),
    //                 'Transaction Date': moment(transactionTypeSalesDetails[i].createdAt).format('DD MMM hh:mma'),
    //                 'Total Discount (RM)': parseFloat(transactionTypeSalesDetails[i].discount).toFixed(2),
    //                 'Discount (%)': parseFloat(isFinite(transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount) ? transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount * 100 : 0).toFixed(2),
    //                 'Tax (RM)': parseFloat(transactionTypeSalesDetails[i].tax).toFixed(2),
    //                 'Tax (RM)': parseFloat(0).toFixed(2),
    //                 'GP (%)': parseFloat(0).toFixed(2),
    //                 'Net Sales (RM)': parseFloat(transactionTypeSalesDetails[i].totalPrice).toFixed(2),
    //             };

    //             excelData.push(excelRow);
    //         }
    //     }

    //     // console.log('excelData');
    //     // console.log(excelData);

    //     return excelData;
    // };

    const downloadExcel = () => {
        const excelData = convertDataToExcelFormat();

        var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
        var excelWorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(excelWorkBook, excelWorkSheet, "Product Sales Report");

        const workBookData = XLSX.write(excelWorkBook, {
            type: 'binary',
            bookType: 'xlsx',
        });

        RNFS.writeFile(excelFile, workBookData, 'ascii')
            .then((success) => {
                // console.log(`wrote file ${excelFile}`);

                Alert.alert(
                    'Success',
                    `Send to ${excelFile}`,
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
            })
            .catch((err) => {
                // console.log(err.message);
            });

    }


    // const emailRedemptions = () => {
    //     var body = {
    //         data: CsvData,
    //         //data: convertArrayToCSV(redemptions.dataSource.data),
    //         data: convertArrayToCSV(CsvData),
    //         email: exportEmail,
    //     };
    //     //API need to change
    //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
    //         if (result !== null) {
    //             Alert.alert(
    //                 'Success',
    //                 'Email sent to your inbox',
    //                 [{ text: 'OK', onPress: () => { } }],
    //                 { cancelable: false },
    //             );
    //         }
    //     });

    //     setVisible(false);
    // };

    const emailRedemptions = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: excelData,
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailRedemptions, body, false).then((result) => {
            if (result !== null) {
                Alert.alert(
                    'Success',
                    'Email has been sent',
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
            }
        });

        setVisible(false);
    };

    const changeClick = () => {
        if (day == true) {
            setState({ day: false })
        }
        else
            setState({ day: true })
    }

    var leftSpacing = '0%';

    if (Dimensions.get('screen').width >= 1280) {
        leftSpacing = '10%';
    }

    const leftSpacingScale = {
        marginLeft: leftSpacing,
    };

    return (
        <View style={[styles.container, !isTablet() ? {
            transform: [
                { scaleX: 1 },
                { scaleY: 1 },
            ],
        } : {}]}>
            {/* <View style={[styles.sidebar, !isTablet() ? {
                width: Dimensions.get('screen').width * 0.08,
            } : {}, switchMerchant ? {
                // width: '10%'
            } : {}]}>
                <SideBar navigation={props.navigation} selectedTab={8} expandReport={true} />
            </View> */}

            <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{
                    // flex: 1
                }}
                visible={exportModalVisibility}
                transparent={true}
                animationType={'slide'}
            >
                <View style={styles.modalContainer}>
                    <View style={[styles.modalView, {
                    }]}>
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                                // setState({ changeTable: false });
                                setExportModalVisibility(false);
                            }}>
                            <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                        </TouchableOpacity>
                        <View style={styles.modalTitle}>
                            <Text style={styles.modalTitleText}>
                                Download Options
                            </Text>
                        </View>
                        {/* <View style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
                            <Text style={styles.modalBodyText}>
                                Current Table: {selectedTableCode}
                            </Text>
                            <Text style={styles.modalBodyText}>
                                TO
                            </Text>
                            <View style={{
                                // backgroundColor: 'red',
                                width: 200,
                                marginBottom: 20,
                            }}>

                            </View>
                        </View> */}

                        <View style={{
                            alignItems: 'center',
                            top: '10%',
                        }}>
                            <TouchableOpacity
                                style={[styles.modalSaveButton, {
                                    zIndex: -1.
                                }]}
                                onPress={() => { downloadExcel() }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Export to Excel</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[styles.modalSaveButton, {
                                    zIndex: -1.
                                }]}
                                onPress={() => { downloadCsv() }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Export to CSV</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ModalView>


            <DateTimePickerModal
                isVisible={showDateTimePicker}
                mode={'date'}
                onConfirm={(text) => {
                    setRev_date(moment(text));
                    setShowDateTimePicker(false);
                }}
                onCancel={() => {
                    setShowDateTimePicker(false);
                }}
            />

            <DateTimePickerModal
                isVisible={showDateTimePicker1}
                mode={'date'}
                onConfirm={(text) => {
                    setRev_date1(moment(text));
                    setShowDateTimePicker1(false);
                }}
                onCancel={() => {
                    setShowDateTimePicker1(false);
                }}
            />

            <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide">

                <KeyboardAvoidingView
                    behavior="padding"
                    style={{
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: Dimensions.get('window').height,
                    }}>
                    <View style={styles.confirmBox}>

                        <Text style={{ fontSize: 24, justifyContent: "center", alignSelf: "center", marginTop: 40, fontFamily: 'NunitoSans-Bold' }}>Enter your email</Text>
                        <View style={{ justifyContent: "center", alignSelf: "center", alignContent: 'center', marginTop: 20, flexDirection: 'row', width: '80%' }}>
                            <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                                <Text style={{ color: Colors.descriptionColor, fontSize: 20, }}>
                                    email:
                                </Text>
                            </View>
                            <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInput8, { paddingLeft: 5 }]}
                                placeholder="Enter your email"
                                // style={{
                                //     // paddingLeft: 1,
                                // }}                                        
                                //defaultValue={extentionCharges}
                                onChangeText={(text) => {
                                    // setState({ exportEmail: text });
                                    setExportEmail(text);
                                }}
                                value={exportEmail}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            />
                        </View>

                        <View
                            style={{
                                alignSelf: 'center',
                                marginTop: 20,
                                justifyContent: 'center',
                                alignItems: 'center',
                                // width: 260,
                                width: Dimensions.get('screen').width * 0.2,
                                height: 60,
                                alignContent: 'center',
                                flexDirection: "row",
                                marginTop: 40,
                            }}>
                            <TouchableOpacity
                                onPress={emailRedemptions}
                                style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: '100%',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    alignContent: 'center',
                                    height: 60,
                                    borderBottomLeftRadius: 10,
                                    borderRightWidth: StyleSheet.hairlineWidth,
                                    borderTopWidth: StyleSheet.hairlineWidth
                                }}>
                                <Text style={{ fontSize: 22, color: Colors.primaryColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                    Email
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={() => {
                                    // setState({ visible: false });
                                    setVisible(false);
                                }}
                                style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: '100%',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    alignContent: 'center',
                                    height: 60,
                                    borderBottomRightRadius: 10,
                                    borderTopWidth: StyleSheet.hairlineWidth
                                }}>
                                <Text style={{ fontSize: 22, color: Colors.descriptionColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                    Cancel
                                </Text>
                            </TouchableOpacity>
                        </View>

                    </View>
                </KeyboardAvoidingView>

            </ModalView>

            <View style={[styles.content, {
                top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 1 : 0,
            }]}>
                <View style={{ flex: 1 }}>
                    <View>
                        <View style={{ marginTop: 10, flexDirection: 'row', justifyContent: 'space-between', alignContent: 'center' }}>
                            <Text style={{ fontWeight: 'bold', fontSize: 32 }}>Sales by Redemption</Text>
                            <View
                                style={[{
                                    // flex: 1,
                                    // alignContent: 'flex-end',
                                    // flexDirection: 'row',
                                    // marginRight: '-40%',
                                    // marginLeft: 310,
                                    // backgroundColor: 'red',
                                    // alignItems: 'flex-end',
                                    height: 40,
                                    marginTop: 10,

                                }, !isTablet() ? {
                                    marginLeft: 0,
                                } : {}]}>
                                <View style={{
                                    width: 250,
                                    height: 40,
                                    backgroundColor: 'white',
                                    borderRadius: 10,
                                    flexDirection: 'row',
                                    alignContent: 'center',
                                    alignItems: 'center',
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                }}>
                                    <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{
                                            width: 180,
                                            fontSize: 15,
                                            fontFamily: 'NunitoSans-Regular',
                                            height: 45
                                        }}
                                        clearButtonMode="while-editing"
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setSearch(text);
                                            // setList1(false);
                                            // setSearchList(true);
                                        }}
                                        value={search}
                                    />
                                </View>
                            </View>

                        </View>
                        <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text>
                    </View>
                    <View style={{ flexDirection: 'row', marginTop: 20, justifyContent: 'space-between' }}>

                        {/* <View
                            style={[{
                                // flex: 1,
                                // alignContent: 'flex-end',
                                // flexDirection: 'row',
                                // marginRight: '-40%',
                                // marginLeft: 310,
                                // backgroundColor: 'red',
                                // alignItems: 'flex-end',
                                width: '26%',
                                height: 40,

                            }, !isTablet() ? {
                                marginLeft: 0,
                            } : {}]}>
                            <View style={{
                                width: 200,
                                height: 40,
                                backgroundColor: 'white',
                                borderRadius: 10,
                                // marginLeft: '53%',
                                flexDirection: 'row',
                                alignContent: 'center',
                                alignItems: 'center',

                                //marginRight: Dimensions.get('screen').width * Styles.sideBarWidth,

                                position: 'absolute',
                                //right: '17%',

                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                            }}>
                                <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
                                <TextInput
                                    editable={!loading}
                                    underlineColorAndroid={Colors.whiteColor}
                                    style={{
                                        width: 180,
                                        fontSize: 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}
                                    clearButtonMode="while-editing"
                                    placeholder=" Search"
                                    onChangeText={(text) => {
                                        setSearch(text);
                                        // setList1(false);
                                        // setSearchList(true);
                                    }}
                                    value={search}
                                />
                            </View>
                        </View> */}

                        <TouchableOpacity
                            style={[leftSpacingScale, {
                                marginRight: 10,
                                paddingHorizontal: 15,
                                backgroundColor: Colors.whiteColor,
                                height: 40,
                                // width: 200, 
                                alignItems: 'center',
                                borderRadius: 7,
                                flexDirection: 'row',
                                justifyContent: 'center', shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,

                                opacity: !showDetails ? 0 : 100,
                            }]}
                            onPress={() => setShowDetails(false)}
                            disabled={!showDetails}
                        >
                            <AntDesign name="arrowleft" size={18} color={Colors.primaryColor} style={{
                                top: 1,
                                marginRight: -5,
                            }} />
                            <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Back to Summary</Text>
                        </TouchableOpacity>

                        <View style={{ flexDirection: 'row' }}>
                            <View style={[{
                                marginRight: 10, width: 230, flexDirection: 'row',
                                alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: 40,
                                backgroundColor: Colors.whiteColor,
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                            }]}
                            >
                                <View style={{ alignSelf: "center" }} onPress={() => { setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                    {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                                    <GCalendar width={20} height={20} />
                                </View>
                                <TouchableOpacity onPress={() => {
                                    setShowDateTimePicker(true);
                                    setShowDateTimePicker1(false);
                                }} style={{
                                    marginHorizontal: 4,
                                }}>
                                    <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(rev_date).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>

                                <Text style={{ fontFamily: "NunitoSans-Regular" }}>-</Text>

                                <TouchableOpacity onPress={() => {
                                    setShowDateTimePicker(false);
                                    setShowDateTimePicker1(true);
                                }} style={{
                                    marginHorizontal: 4,
                                }}>
                                    <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(rev_date1).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>

                            </View>

                            <TouchableOpacity
                                style={{
                                    marginRight: 15, paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}
                                // onPress={() => { downloadCsv() }}
                                onPress={() => { setExportModalVisibility(true) }}
                            >
                                <Download width={15} height={15} />
                                <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Download</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={{
                                    paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}
                                onPress={() => {
                                    // setState({
                                    //     visible: true
                                    // })
                                    setVisible(true);
                                }}
                            >
                                <Upload width={15} height={15} />
                                <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Email</Text>
                            </TouchableOpacity>
                        </View>

                    </View>

                    <View style={{ width: '100%', marginTop: 10 }}>
                        <View style={{
                            backgroundColor: Colors.whiteColor,
                            padding: 12,
                            height: '82%',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            borderRadius: 5,
                            shadowColor: '#000',
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.34,
                            shadowRadius: 3.32,
                            elevation: 1,
                        }}>
                            {!showDetails
                                ?
                                <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Redemption Name</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Redepmtion Category</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Redemption SKU</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Redemption Amount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <View>
                                                <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Redemption Discount</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                            </View>
                                        </View>
                                    </View>
                                    {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                    <View style={{ flexDirection: 'column' }}>
                                        <View>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Total Sale Return</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                </View> */}
                                    {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                    <View style={{ flexDirection: 'column' }}>
                                        <View>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Total Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                </View> */}
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <View>
                                                <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Discount</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                            </View>
                                        </View>
                                    </View>
                                    {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                    <View style={{ flexDirection: 'column' }}>
                                        <View>
                                            <Text numberOfLines={1} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Item Net Sales</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                </View> */}
                                </View>
                                :
                                <View style={{ marginTop: 10, flexDirection: 'row', }}>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Transaction Category</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Sales</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Transaction Date</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Total Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Tax</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Tax</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>GP</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Net Sales</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>

                                </View>
                            }

                            {!showDetails
                                ?
                                <FlatList
                                    // data={redemptions.filter(item => {
                                    //     if (search !== '') {
                                    //         return item.productName.toLowerCase().includes(search.toLowerCase());
                                    //     }
                                    //     else {
                                    //         return true;
                                    //     }
                                    // }).slice((currentPage - 1) * perPage, currentPage * perPage)}
                                    extraData={redemptions}
                                    renderItem={renderItem}
                                    keyExtractor={(item, index) => String(index)}
                                    style={{ marginTop: 10 }}
                                    initialNumToRender={8}
                                />
                                :
                                <FlatList
                                    // data={redemptionsDetails.filter(item => {
                                    //     if (search !== '') {
                                    //         return item.orderType.toLowerCase().includes(search.toLowerCase());
                                    //     }
                                    //     else {
                                    //         return true;
                                    //     }
                                    // }).slice((currentPage - 1) * perPage, currentPage * perPage)}
                                    // extraData={transactionTypeSales}
                                    renderItem={renderItemDetails}
                                    keyExtractor={(item, index) => String(index)}
                                    style={{ marginTop: 10 }}
                                />
                            }
                        </View>

                        <View style={{ marginLeft: '77%', flexDirection: 'row', marginTop: 10, width: '100%' }}>
                            <Text style={{ marginRight: '1%' }}>Page</Text>
                            <View style={{
                                borderWidth: 1,
                                borderColor: Colors.blackColor,
                                width: '6%',
                                // height: 25,                                
                                alignItems: 'center',
                                backgroundColor: Colors.whiteColor,
                            }}>
                                {console.log('currentPage')}
                                {console.log(currentPage)}

                                <TextInput
                                    onChangeText={(text) => {
                                        // setState({ exportEmail: text });
                                        var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                                        setCurrentPage(currentPageTemp > pageCount ? pageCount : (currentPageTemp < 1 ? 1 : currentPageTemp));
                                    }}
                                    placeholder={currentPage.toString()}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    style={{
                                        color: Colors.fontDark,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: 14,
                                        marginTop: Platform.OS === 'ios' ? 0 : -15,
                                        marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                        textAlign: 'center',
                                        width: '100%',
                                    }}
                                    value={currentPage.toString()}
                                    defaultValue={currentPage.toString()}
                                    keyboardType={'numeric'}
                                />
                            </View>
                            <Text style={{ marginLeft: '1%', marginRight: '1%' }}>of {pageCount}</Text>
                            <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { prevPage() }}>
                                <MaterialIcons name='keyboard-arrow-left' size={25} style={{ color: Colors.whiteColor }} />
                            </TouchableOpacity>
                            <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { nextPage() }}>
                                <MaterialIcons name='keyboard-arrow-right' size={25} style={{ color: Colors.whiteColor }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>


            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.fieldtBgColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get('screen').width * 0.4,
        height: Dimensions.get('screen').height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: 'space-between'
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Dimensions.get('screen').width * 0.2,
        width: Dimensions.get('screen').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.02,
        top: Dimensions.get('screen').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'

    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: "20%",
    },
    modalSaveButton: {
        width: Dimensions.get('screen').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
});

export default ReportSalesByRedemption;
