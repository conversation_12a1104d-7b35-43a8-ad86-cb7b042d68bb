import { Store } from 'pullstate';

export const LoyaltyStore = new Store({
    name: '',
    description: '',
    image: '',
    
    promoCode: '',
    promoCodeUsageLimit: '',
    promoDateStart: {},
    promoDateEnd: {},
    promoTimeStart: {},
    promoTimeEnd: {},

    promoType: {},

    effectiveDay: {},
    effectiveTimeStart: {},
    effectiveTimeEnd: {},
    effectiveType: {},
    effectiveTypeOption: [],

    orderType: {},
    outletIdList : [],
    // outletNameList: [],

    loyaltyCampaignType: {},
    loyaltyCampaignSendTime: {},
    loyaltyCampaignExpirationDays: {},
    loyaltyCriteriaList: [],

    targetSegmentGroupList: [],
    targetUserGroup: {},

    taggableVoucherId: {}, 

    isEditNotification: true,
    isEnableSellOnline: {},
    isPromoCodeUsageLimit: {},
    
    // commanId: '',
    //criteriaList: [],

    // currOuletTaxName: '',
    // currOutletTaxRate: '',
    // currOutletTaxId: '',
    // currOutletId: '',

    minSpend: '',
    // notification: {},
    notificationText: '',
    // batchList: [],

});