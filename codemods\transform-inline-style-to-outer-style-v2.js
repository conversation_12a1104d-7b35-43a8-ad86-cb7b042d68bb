/**
 * Codemod to convert inline styles to standalone style object properties.
 */

const j = require('jscodeshift');

// A unique key generator for style properties.
let styleCounter = 0;
const styleKeys = new Map();

const generateStyleKey = () => {
    return `style${styleCounter++}`;
};

const transformInlineStyles = (source) => {
    return j(source)
        .find(j.JSXElement)
        .forEach((path) => {
            const { node } = path;

            // Skip RNPickerSelect components
            const openingElementName = node.openingElement.name.name;
            if (openingElementName === 'RNPickerSelect') {
                return;
            }

            // Find all attributes that have inline styles
            const styleAttr = node.openingElement.attributes.find(attr => 
                attr.name.name === 'style'
            );

            if (styleAttr) {
                const styleValue = styleAttr.value;

                // Check if the style is a conditional expression
                if (j(styleValue).isConditionalExpression()) {
                    // Check if the condition involves Platform.OS
                    const hasPlatformCheck = j(styleValue).find(j.MemberExpression, {
                        object: { name: 'Platform' },
                        property: { name: 'OS' }
                    }).length > 0;

                    if (!hasPlatformCheck) {
                        return; // If not a Platform.OS condition, skip transformation
                    }
                }

                // Create or retrieve a unique key for the style
                const styleKey = styleKeys.get(styleValue) || generateStyleKey();
                styleKeys.set(styleValue, styleKey);

                // Build combinedStyles object
                const combinedStylesKey = 'combinedStyles';
                const combinedStylesObject = j.objectExpression(
                    Array.from(styleKeys.entries()).map(([style, key]) => {
                        return j.property('init', j.identifier(key), style);
                    })
                );

                // Add the combinedStyles object at the bottom of the file
                const body = path.parent.value.body;
                body.push(j.variableDeclaration('const', [j.variableDeclarator(j.identifier(combinedStylesKey), combinedStylesObject)]));

                // Replace the inline style attribute with a reference to combinedStyles
                const newStyleAttr = j.jsxAttribute(
                    j.jsxIdentifier('style'),
                    j.jsxExpressionContainer(
                        j.memberExpression(
                            j.identifier(combinedStylesKey),
                            j.identifier(styleKey)
                        )
                    )
                );
                
                // Remove the old style attribute and add the new one
                node.openingElement.attributes = node.openingElement.attributes.filter(attr => attr !== styleAttr);
                node.openingElement.attributes.push(newStyleAttr);
            }
        })
        .toSource();
};

module.exports = function(fileInfo, api) {
    const j = api.jscodeshift;
    try {
        return transformInlineStyles(fileInfo.source);
    } catch (error) {
        console.error(`Transformation error in file ${fileInfo.path}: ${error.message}`);
        return fileInfo.source; // Return original source in case of error
    }
};
