import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Switch,
  Modal as ModalComponent,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { <PERSON>List, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import { getAddOnChoicePrice, getAddOnChoiceQuantity, getCartItemPriceWithoutAddOn, getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, getTransformForModalInsideNavigation, getTransformForScreenInsideNavigation, isTablet, logEventAnalytics } from '../util/common';
import FusionCharts from 'react-native-fusioncharts';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import GCalendar from '../assets/svg/GCalendar';
import RNFetchBlob from 'rn-fetch-blob';
import {
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
  sortReportDataList,
  generateEmailReport,
} from '../util/common';
import {
  filterChartItems,
  getDataForChartReportProductSales,
  getDataForChartReportUpsellSales,
} from '../util/chart';
import {
  CHART_DATA,
  CHART_TYPE,
  FS_LIBRARY_PATH,
  CHART_Y_AXIS_DROPDOWN_LIST,
  CHART_FIELD_COMPARE_DROPDOWN_LIST,
  CHART_FIELD_NAME_DROPDOWN_LIST,
  CHART_FIELD_TYPE,
  CHART_FIELD_COMPARE_DICT,
  CHART_PERIOD,
  CHART_X_AXIS_DROPDOWN_LIST,
} from '../constant/chart';
import {
  EMAIL_REPORT_TYPE,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  ORDER_TYPE,
  EXPAND_TAB_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  ORDER_TYPE_DROP_DOWN_LIST,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE
} from '../constant/common';
import XLSX from 'xlsx';
import 'react-native-get-random-values';
const { nanoid } = require('nanoid');
import { v4 as uuidv4 } from 'uuid';
const RNFS = require('@dr.pogodin/react-native-fs');
import DropDownPicker from 'react-native-dropdown-picker';
import RNPickerSelect from 'react-native-picker-select';
import Feather from 'react-native-vector-icons/Feather';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import { TempStore } from "../store/tempStore";
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ReportSalesUpselling = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [keyboardHeight] = useKeyboard();
  const [list, setList] = useState([]);
  const [page, setPage] = useState(0);
  const [name, setName] = useState('Upselling');
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isChecked1, setIsChecked1] = useState(false);
  const [endDate, setEndDate] = useState(new Date());
  const [startDate, setStartDate] = useState(new Date());
  const [oriList, setOriList] = useState([]);
  const [offset, setOffset] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [detailsPageCount, setDetailsPageCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
  const [pageReturn, setPageReturn] = useState(1);
  const [day, setDay] = useState(false);
  const [pick, setPick] = useState(null);
  const [pick1, setPick1] = useState(null);
  const [search, setSearch] = useState('');
  const [lists, setLists] = useState([]);
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);

  const [pushPagingToTop, setPushPagingToTop] = useState(false);

  const [loading, setLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(
    isTablet() ? false : true,
  );
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [showDateTimePickerFilter, setShowDateTimePickerFilter] =
    useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [rev_date, setRev_date] = useState(
    moment().subtract(6, 'days').startOf('day'),
  );
  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf('day'),
  );

  const todayUserOrders = TempStore.useState((s) => s.allOutletUserOrderDoneProcessed);

  const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
  const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const [productSales, setProductSales] = useState([]);
  const [productSalesSku, setProductSalesSku] = useState([]);
  const [selectedFilterProductList, setSelectedFilterProductList] = useState([]);
  const [selectedFilterChannelList, setSelectedFilterChannelList] = useState([ORDER_TYPE_DROP_DOWN_LIST[0].value, ORDER_TYPE_DROP_DOWN_LIST[1].value]);

  ////////////////////////////////////////////////////////////////////////////

  // 2022-02-23 - Upselling reports related

  const [productSalesUpselling, setProductSalesUpselling] = useState([]);
  const [selectedFilterProductId, setSelectedFilterProductId] = useState('');
  const [selectedFilterProductIdUpselling, setSelectedFilterProductIdUpselling] = useState('');

  const [outletItemsDropdownList, setOutletItemsDropdownList] = useState([]);
  const [outletItemsUpsellingDropdownList, setOutletItemsUpsellingDropdownList] = useState([]);

  const upsellingCampaigns = OutletStore.useState(s => s.upsellingCampaigns);

  ////////////////////////////////////////////////////////////////////////////

  const [productSalesChart, setProductSalesChart] = useState({});

  const [filteredOutletItems, setFilteredOutletItems] = useState([]);
  const [filteredOutletItemsUpselling, setFilteredOutletItemsUpselling] = useState([]);

  const [selectedItemSummary, setSelectedItemSummary] = useState({});

  const [expandDetailsDict, setExpandDetailsDict] = useState({});
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  // const allOutletsUserOrdersDone = OutletStore.useState(
  //   (s) => s.allOutletsUserOrdersDone,
  // );
  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [exportEmail, setExportEmail] = useState('');
  const [CsvData, setCsvData] = useState([]);

  const [showDetails, setShowDetails] = useState(false);
  const [productSalesDetails, setProductSalesDetails] = useState([]);

  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [detailsTitle, setDetailsTitle] = useState('');

  const [expandSelection, setExpandSelection] = useState(
    props.expandSelection === undefined ? false : props.expandSelection,
  );
  const [filterTapped, setFilterTapped] = useState(
    props.threeDotsTapped === undefined ? 0 : props.threeDotsTapped,
  );

  const [expandGroupBy, setExpandGroupBy] = useState(
    props.expandGroupBy === undefined ? false : props.expandGroupBy,
  );
  const [groupByTapped, setGroupByTapped] = useState(
    props.threeDotsTapped === undefined ? 0 : props.threeDotsTapped,
  );

  const [selectedChartDropdownValue, setSelectedChartDropdownValue] = useState(
    CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0].value,
  );
  const [selectedChartFilterQueries, setSelectedChartFilterQueries] = useState([
    {
      fieldNameKey:
        CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
          .value,
      fieldNameType:
        CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
          .fieldType,
      fieldCompare:
        CHART_FIELD_COMPARE_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
          .value,
      fieldDataValue: null,
      fieldSpecial:
        CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
          .special,
    },
  ]);
  const [salesBarChartPeriod, setSalesBarChartPeriod] = useState(
    CHART_PERIOD.THIS_WEEK,
  );

  const [selectedChartDropdownValueX, setSelectedChartDropdownValueX] =
    useState(
      CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0].value,
    );

  const [appliedChartFilterQueries, setAppliedChartFilterQueries] = useState(
    [],
  );

  const [currReportSummarySort, setCurrReportSummarySort] = useState('');
  const [currReportDetailsSort, setCurrReportDetailsSort] = useState('');

  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const pageStack = CommonStore.useState((s) => s.pageStack);

  const merchantId = UserStore.useState((s) => s.merchantId);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isCsv, setIsCsv] = useState(false);
  const [isExcel, setIsExcel] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [saleTip, setSaleTip] = useState(false);
  const [netSaleTip, setNetSaleTip] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);

  const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);

  ////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (isMounted) {
      if (global.isLoadingReportData) {
        return
      }

      setIsLoadingChart(true);

      // const result = getDataForChartReportProductSales(allOutlets, allOutletsUserOrdersDone, rev_date, rev_date1);
      const result = getDataForChartReportUpsellSales(
        productSalesSku,
        filteredOutletItems,
        allOutlets,
        historyStartDate,
        historyEndDate,
        selectedChartDropdownValue,
        selectedChartDropdownValueX,

        productSales,
        productSalesUpselling,
        selectedFilterProductId,
        selectedFilterProductIdUpselling,

        filteredOutletItemsUpselling,

        reportDisplayType,
        reportOutletShifts,
      );

      if (result && result.chartData) {
        setProductSalesChart(result.chartData);
      }

      setIsLoadingChart(false);
    }
  }, [
    // allOutlets,
    // allOutletsUserOrdersDone,

    // productSalesSku,
    filteredOutletItems,
    historyStartDate,
    historyEndDate,
    selectedChartDropdownValue,
    selectedChartDropdownValueX,

    productSales,
    productSalesUpselling,
    selectedFilterProductId,
    selectedFilterProductIdUpselling,

    filteredOutletItemsUpselling,

    isMounted,

    reportDisplayType,
    reportOutletShifts,
  ]);

  const todayUserOrdersRealTime = OutletStore.useState(s => s.allOutletsUserOrdersDoneRealTime.filter(o => o.createdAt >= moment().startOf('day').valueOf()));
  // const payoutTransactions = OutletStore.useState(s => s.payoutTransactions.filter(p => p.v >= '3')); // only check for v3
  // const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend.filter(p => p.v >= '3')); // only check for v3

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

  const selectedOutletList = CommonStore.useState(s => s.reportOutletIdList);
  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState("");
  const isMasterAccount = UserStore.useState(s => s.isMasterAccount);
  const [allOutlets, setAllOutlets] = useState([]);

  useEffect(() => {
    setAllOutlets(allOutletsRaw.filter(outlet => {
      if (outlet.uniqueId === currOutletId || isMasterAccount) {
        return true;
      }
      else {
        return false;
      }
    }));
  }, [allOutletsRaw, currOutletId, isMasterAccount]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
    if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);

      // // setSelectedOutletList([currOutletId]);
      // CommonStore.update((s) => {
      //   s.reportOutletIdList = [currOutletId];
      // })
    }
  }, [allOutlets, currOutletId]);

  useEffect(() => {
    var outletItemsDropdownListTemp = [];
    var outletItemsUpsellingDropdownListTemp = [];

    for (var i = 0; i < outletItems.length; i++) {
      var isValid = false;

      for (var j = 0; j < upsellingCampaigns.length; j++) {
        if (upsellingCampaigns[j].productList.find(product => product.productId === outletItems[i].uniqueId)) {
          isValid = true;
          break;
        }
      }

      if (isValid) {
        outletItemsUpsellingDropdownListTemp.push({
          label: outletItems[i].name,
          value: outletItems[i].uniqueId,
        });
      }

      outletItemsDropdownListTemp.push({
        label: outletItems[i].name,
        value: outletItems[i].uniqueId,
      });
    }

    setOutletItemsUpsellingDropdownList(outletItemsUpsellingDropdownListTemp);
    setOutletItemsDropdownList(outletItemsDropdownListTemp);

    if (
      // selectedFilterProductIdUpselling === '' ||
      outletItemsUpsellingDropdownListTemp.length > 0 &&
      !outletItemsUpsellingDropdownListTemp.find(option => option.value === selectedFilterProductIdUpselling)) {
      setSelectedFilterProductIdUpselling(outletItemsUpsellingDropdownListTemp[0].value);
    }

    if (
      // selectedFilterProductId === '' ||
      outletItemsDropdownListTemp.length > 0 &&
      !outletItemsDropdownListTemp.find(option => option.value === selectedFilterProductId)) {
      setSelectedFilterProductId(outletItemsDropdownListTemp[0].value);
    }
  }, [outletItems, upsellingCampaigns]);

  const [isLoadingChart, setIsLoadingChart] = useState(true);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const isChartLoading = useMemo(() => {
      return isLoadingChart || global.isLoadingReportData;
  }, [isLoadingChart, global.isLoadingReportData]);

  const isDataLoading = useMemo(() => {
      return isLoadingData || global.isLoadingReportData;
  }, [isLoadingData, global.isLoadingReportData]);

  useEffect(() => {
    if (global.reportSalesUpsellingTableTimerId) {
      clearTimeout(global.reportSalesUpsellingTableTimerId);
    }

    global.reportSalesUpsellingTableTimerId = setTimeout(() => {
      if (isMounted) {
        setIsLoadingData(true);

        if (
          currOutletId !== '' &&
          allOutlets.length > 0 &&
          outletCategories.length > 0 &&
          outletItems.length > 0 &&
          Object.keys(outletCategoriesDict).length > 0
        ) {
          //   setHistoryOrders(allOutletsUserOrdersDone.filter(order => order.outletId === currOutletId));

          // console.log('outletCategoriesDict');
          // console.log(outletCategoriesDict);

          // console.log('report outlet items');

          var productSalesDict = {};
          var productSalesSkuDict = {};

          var productSalesUpsellingDict = {};

          var processedCartItemDict = {};

          var filteredOutletItemsTemp = [];
          var filteredOutletItemsUpsellingTemp = [];

          for (var i = 0; i < outletItems.length; i++) {
            // console.log(outletItems[i].categoryId);
            // console.log(outletCategoriesDict[outletItems[i].categoryId]);

            var isValid = false;

            if (selectedFilterProductList.length > 0) {
              if (selectedFilterProductList.includes(outletItems[i].uniqueId)) {
                isValid = true;
              }
            }
            else {
              isValid = true;
            }

            if (isValid) {
              if (outletCategoriesDict[outletItems[i].categoryId]) {
                var productSaleRecord = {
                  productId: outletItems[i].uniqueId,
                  summaryId: nanoid(),
                  productName: outletItems[i].name,
                  productCategory:
                    outletCategoriesDict[outletItems[i].categoryId].name,
                  // productCategory: '',
                  // productSku: outletItems[i].sku,
                  productSku: outletItems[i].skuMerchant || 'N/A',
                  totalItems: 0,
                  totalSales: 0,
                  totalSalesReturn: 0,
                  totalDiscount: 0,
                  discount: 0,
                  itemNetSales: 0,
                  paymentDetails: '',
                  detailsList: [],
                };

                productSalesDict[outletItems[i].uniqueId] = productSaleRecord;
                productSalesSkuDict[outletItems[i].name] = productSaleRecord; // change to sku in future
              }
            }
          }

          /////////////////////////////////////////////////////////

          // 2023-02-14 - For the past orders (those that inside daily transactions)

          // for (var transactionIndex = 0; transactionIndex < global.payoutTransactions.length; transactionIndex++) {
          //   const { userOrdersFigures } = global.payoutTransactions[transactionIndex];

          //   for (var i = 0; i < userOrdersFigures.length; i++) {
          //     if (
          //       moment(historyStartDate).isSameOrBefore(
          //         userOrdersFigures[i].createdAt,
          //       ) &&
          //       moment(historyEndDate).isAfter(userOrdersFigures[i].createdAt) &&
          //       (filterAppType.includes(userOrdersFigures[i].appType) || filterAppType.length === 0)
          //     ) {
          //       /////////////////////////////////////////
          //       // apply filter here

          //       // const isValid = filterChartItems(
          //       //   todayUserOrders[i],
          //       //   appliedChartFilterQueries,
          //       // );

          //       const isValid = true;

          //       /////////////////////////////////////////

          //       if (
          //         isValid &&
          //         // todayUserOrders[i].outletId === currOutletId &&
          //         // ((moment(historyStartDate).isSameOrBefore(
          //         //   userOrdersFigures[i].createdAt,
          //         // ) &&
          //         //   moment(historyEndDate).isAfter(
          //         //     userOrdersFigures[i].createdAt,
          //         //   )))
          //         // &&
          //         selectedFilterChannelList.includes(userOrdersFigures[i].orderType)
          //       ) {
          //         for (
          //           var j = 0;
          //           j < userOrdersFigures[i].cartItems.length;
          //           j++
          //         ) {
          //           const cartItem = userOrdersFigures[i].cartItems[j];

          //           ////////////////////////////////////////////////////////////

          //           if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.id] === undefined) {
          //             // create one first

          //             var matchedItem = outletItems.find(o => o.uniqueId === cartItem.id);

          //             if (matchedItem && outletCategoriesDict[matchedItem.categoryId]) {
          //               var productSaleUpsellingRecord = {
          //                 productId: matchedItem.uniqueId,
          //                 summaryId: nanoid(),
          //                 productName: matchedItem.name,
          //                 productCategory:
          //                   outletCategoriesDict[matchedItem.categoryId].name,
          //                 // productCategory: '',
          //                 // productSku: outletItems[i].sku,
          //                 productSku: matchedItem.skuMerchant || 'N/A',
          //                 totalItems: 0,
          //                 totalSales: 0,
          //                 totalSalesReturn: 0,
          //                 totalDiscount: 0,
          //                 discount: 0,
          //                 itemNetSales: 0,
          //                 paymentDetails: '',
          //                 detailsList: [],
          //               };

          //               productSalesUpsellingDict[cartItem.id] = productSaleUpsellingRecord;
          //               // productSalesSkuDict[outletItems[i].name] = productSaleUpsellingRecord; // change to sku in future
          //             }
          //           }

          //           ////////////////////////////////////////////////////////////

          //           if ((cartItem.upsellingCampaignId === '' || cartItem.upsellingCampaignId === undefined) && productSalesDict[cartItem.id]) {
          //             // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

          //             if (
          //               // !productSalesDict[cartItem.itemId].detailsList.find(
          //               //   (order) =>
          //               //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
          //               // )

          //               // !processedCartItemDict[
          //               // cartItem.itemId + cartItem.cartItemDate
          //               // ]
          //               true
          //             ) {
          //               // add if not existed

          //               // processedCartItemDict[
          //               //   cartItem.itemId + cartItem.cartItemDate
          //               // ] = true;

          //               ///////////////////////////////////////////////////////////////////

          //               var cartItemTax = 0;
          //               var cartItemSc = 0;
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
          //               // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

          //               var cartItemRatioTax = 0;
          //               var cartItemRatioSc = 0;

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioTax = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemTax = ((userOrdersFigures[i].tax || 0) *
          //                   cartItemRatioTax) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioSc = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioSc = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemSc = ((userOrdersFigures[i].sc || 0) *
          //                   cartItemRatioSc) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemSc = 0;
          //               }

          //               // var cartItemTax =
          //               //   ((allOutletsUserOrdersDone[i].tax || 0) *
          //               //     cartItemRatioTax) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemRatioSc =
          //               //   (cartItem.price /
          //               //     allOutletsUserOrdersDone[i].totalPrice) *
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemSc =
          //               //   ((allOutletsUserOrdersDone[i].sc || 0) *
          //               //     cartItemRatioSc) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;

          //               ///////////////////////////////////////////////////////////////////

          //               productSalesDict[cartItem.id].totalItems +=
          //                 cartItem.qty;
          //               productSalesDict[cartItem.id].totalSales +=
          //                 cartItem.price + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0,);
          //               //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
          //               // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesDict[cartItem.id].totalDiscount += userOrdersFigures[i].disc + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesDict[cartItem.id].totalDiscount += userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesDict[cartItem.id].itemNetSales +=
          //                 cartItem.price + cartItemTax + cartItemSc;

          //               // if (
          //               //   userOrdersFigures[i].paymentDetails &&
          //               //   userOrdersFigures[i].paymentDetails.channel
          //               // ) {
          //               //   if (
          //               //     OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
          //               //       (paymentMethod) =>
          //               //         paymentMethod.value.channel ===
          //               //         userOrdersFigures[i].paymentDetails.channel,
          //               //     )
          //               //   ) {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Offline';
          //               //   } else {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Online';
          //               //   }
          //               // }

          //               //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
          //               //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

          //               // need change to sku in future
          //               // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
          //               // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

          //               filteredOutletItemsTemp.push({
          //                 ...cartItem,
          //                 orderCompletedDate:
          //                   userOrdersFigures[i].createdAt,
          //                 outletId: global.payoutTransactions[transactionIndex].outletId,

          //                 totalItems: cartItem.qty,
          //                 totalSales: cartItem.price,
          //                 paymentDetails: cartItem.paymentDetails ? cartItem.paymentDetails : null,
          //                 // totalSalesReturn: cartItem.price * cartItem.quantity,
          //                 itemNetSales: cartItem.price,
          //                 discount: 0,
          //                 totalDiscount: 0,

          //                 itemName: productSalesDict[cartItem.id].productName,
          //                 quantity: cartItem.qty,
          //               });

          //               var accumDiscount = userOrdersFigures[i].cartItems.reduce((accum, c) => accum + (c.disc ? c.disc : 0), 0);

          //               productSalesDict[cartItem.id].detailsList.push({
          //                 ...userOrdersFigures[i],
          //                 discountPercentage: parseFloat(
          //                   isFinite(
          //                     accumDiscount /
          //                     (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                       accumDiscount),
          //                   )
          //                     ? (accumDiscount /
          //                       (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                         accumDiscount)) *
          //                     100
          //                     : 0,
          //                 ),
          //                 itemPrice: cartItem.price,
          //                 // itemTax: cartItem.price * currOutlet.taxRate,
          //                 itemTax: cartItemTax,
          //                 itemSc: cartItemSc,
          //                 //salesReturn:
          //               });

          //               if (selectedFilterProductId === '') {
          //                 setSelectedFilterProductId(cartItem.id);
          //               }
          //             }
          //           }
          //           else if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.id]) {
          //             // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

          //             if (
          //               // !productSalesDict[cartItem.itemId].detailsList.find(
          //               //   (order) =>
          //               //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
          //               // )

          //               // !processedCartItemDict[
          //               // cartItem.itemId + cartItem.cartItemDate
          //               // ]
          //               true
          //             ) {
          //               // add if not existed

          //               // processedCartItemDict[
          //               //   cartItem.itemId + cartItem.cartItemDate
          //               // ] = true;

          //               ///////////////////////////////////////////////////////////////////

          //               var cartItemTax = 0;
          //               var cartItemSc = 0;
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
          //               // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

          //               var cartItemRatioTax = 0;
          //               var cartItemRatioSc = 0;

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioTax = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemTax = ((userOrdersFigures[i].tax || 0) *
          //                   cartItemRatioTax) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioSc = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioSc = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemSc = ((userOrdersFigures[i].sc || 0) *
          //                   cartItemRatioSc) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemSc = 0;
          //               }

          //               // var cartItemTax =
          //               //   ((allOutletsUserOrdersDone[i].tax || 0) *
          //               //     cartItemRatioTax) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemRatioSc =
          //               //   (cartItem.price /
          //               //     allOutletsUserOrdersDone[i].totalPrice) *
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemSc =
          //               //   ((allOutletsUserOrdersDone[i].sc || 0) *
          //               //     cartItemRatioSc) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;

          //               ///////////////////////////////////////////////////////////////////

          //               productSalesUpsellingDict[cartItem.id].totalItems +=
          //                 cartItem.qty;
          //               productSalesUpsellingDict[cartItem.id].totalSales +=
          //                 cartItem.price + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0,);
          //               //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
          //               // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesUpsellingDict[cartItem.id].totalDiscount += userOrdersFigures[i].disc + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesUpsellingDict[cartItem.id].totalDiscount += userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesUpsellingDict[cartItem.id].itemNetSales +=
          //                 cartItem.price + cartItemTax + cartItemSc;

          //               // if (
          //               //   userOrdersFigures[i].paymentDetails &&
          //               //   userOrdersFigures[i].paymentDetails.channel
          //               // ) {
          //               //   if (
          //               //     OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
          //               //       (paymentMethod) =>
          //               //         paymentMethod.value.channel ===
          //               //         userOrdersFigures[i].paymentDetails.channel,
          //               //     )
          //               //   ) {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Offline';
          //               //   } else {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Online';
          //               //   }
          //               // }

          //               //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
          //               //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

          //               // need change to sku in future
          //               // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
          //               // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

          //               ///////////////////////////////////////////////////////////

          //               // hide first

          //               filteredOutletItemsUpsellingTemp.push({
          //                 ...cartItem,
          //                 orderCompletedDate:
          //                   userOrdersFigures[i].createdAt,
          //                 outletId: global.payoutTransactions[transactionIndex].outletId,

          //                 totalItems: cartItem.qty,
          //                 totalSales: cartItem.price,
          //                 paymentDetails: cartItem.paymentDetails ? cartItem.paymentDetails : null,
          //                 // totalSalesReturn: cartItem.price * cartItem.quantity,
          //                 itemNetSales: cartItem.price,
          //                 discount: 0,
          //                 totalDiscount: 0,

          //                 itemName: productSalesUpsellingDict[cartItem.id].productName,
          //                 quantity: cartItem.qty,
          //               });

          //               ///////////////////////////////////////////////////////////

          //               var accumDiscount = userOrdersFigures[i].cartItems.reduce((accum, c) => accum + (c.disc ? c.disc : 0), 0);

          //               productSalesUpsellingDict[cartItem.id].detailsList.push({
          //                 ...userOrdersFigures[i],
          //                 discountPercentage: parseFloat(
          //                   isFinite(
          //                     accumDiscount /
          //                     (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                       accumDiscount),
          //                   )
          //                     ? (accumDiscount /
          //                       (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                         accumDiscount)) *
          //                     100
          //                     : 0,
          //                 ),
          //                 itemPrice: cartItem.price,
          //                 // itemTax: cartItem.price * currOutlet.taxRate,
          //                 itemTax: cartItemTax,
          //                 itemSc: cartItemSc,
          //                 //salesReturn:
          //               });

          //               if (selectedFilterProductIdUpselling === '') {
          //                 setSelectedFilterProductIdUpselling(cartItem.id);
          //               }
          //             }
          //           }
          //         }

          //         // if (todayUserOrders[i].cartItemsCancelled) {
          //         //   for (
          //         //     var k = 0;
          //         //     k < todayUserOrders[i].cartItemsCancelled.length;
          //         //     k++
          //         //   ) {
          //         //     const cartItem =
          //         //       todayUserOrders[i].cartItemsCancelled[k];

          //         //     if (productSalesDict[cartItem.itemId]) {
          //         //       productSalesDict[cartItem.itemId].totalSalesReturn +=
          //         //         cartItem.price;

          //         //       // do for cancelled items
          //         //       // filteredOutletItemsTemp.push({
          //         //       //   ...cartItem,
          //         //       //   orderCompletedDate:
          //         //       //     allOutletsUserOrdersDone[i].createdAt,
          //         //       //   outletId: allOutletsUserOrdersDone[i].outletId,

          //         //       //   totalItems: cartItem.quantity,
          //         //       //   totalSales: 0,
          //         //       //   totalSalesReturn: cartItem.price,
          //         //       //   itemNetSales: 0,
          //         //       //   discount: 0,
          //         //       //   totalDiscount: 0,
          //         //       // });
          //         //     }
          //         //   }
          //         // }
          //       }
          //     }
          //   }
          // }

          // for (var transactionIndex = 0; transactionIndex < global.payoutTransactionsExtend.length; transactionIndex++) {
          //   const { userOrdersFigures } = global.payoutTransactionsExtend[transactionIndex];

          //   for (var i = 0; i < userOrdersFigures.length; i++) {
          //     if (
          //       moment(historyStartDate).isSameOrBefore(
          //         userOrdersFigures[i].createdAt,
          //       ) &&
          //       moment(historyEndDate).isAfter(userOrdersFigures[i].createdAt) &&
          //       (filterAppType.includes(userOrdersFigures[i].appType) || filterAppType.length === 0)
          //     ) {
          //       /////////////////////////////////////////
          //       // apply filter here

          //       // const isValid = filterChartItems(
          //       //   todayUserOrders[i],
          //       //   appliedChartFilterQueries,
          //       // );

          //       const isValid = true;

          //       /////////////////////////////////////////

          //       if (
          //         isValid &&
          //         // todayUserOrders[i].outletId === currOutletId &&
          //         // ((moment(historyStartDate).isSameOrBefore(
          //         //   userOrdersFigures[i].createdAt,
          //         // ) &&
          //         //   moment(historyEndDate).isAfter(
          //         //     userOrdersFigures[i].createdAt,
          //         //   )))
          //         // &&
          //         selectedFilterChannelList.includes(userOrdersFigures[i].orderType)
          //       ) {
          //         for (
          //           var j = 0;
          //           j < userOrdersFigures[i].cartItems.length;
          //           j++
          //         ) {
          //           const cartItem = userOrdersFigures[i].cartItems[j];

          //           ////////////////////////////////////////////////////////////

          //           if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.id] === undefined) {
          //             // create one first

          //             var matchedItem = outletItems.find(o => o.uniqueId === cartItem.id);

          //             if (matchedItem && outletCategoriesDict[matchedItem.categoryId]) {
          //               var productSaleUpsellingRecord = {
          //                 productId: matchedItem.uniqueId,
          //                 summaryId: nanoid(),
          //                 productName: matchedItem.name,
          //                 productCategory:
          //                   outletCategoriesDict[matchedItem.categoryId].name,
          //                 // productCategory: '',
          //                 // productSku: outletItems[i].sku,
          //                 productSku: matchedItem.skuMerchant || 'N/A',
          //                 totalItems: 0,
          //                 totalSales: 0,
          //                 totalSalesReturn: 0,
          //                 totalDiscount: 0,
          //                 discount: 0,
          //                 itemNetSales: 0,
          //                 paymentDetails: '',
          //                 detailsList: [],
          //               };

          //               productSalesUpsellingDict[cartItem.id] = productSaleUpsellingRecord;
          //               // productSalesSkuDict[outletItems[i].name] = productSaleUpsellingRecord; // change to sku in future
          //             }
          //           }

          //           ////////////////////////////////////////////////////////////

          //           if ((cartItem.upsellingCampaignId === '' || cartItem.upsellingCampaignId === undefined) && productSalesDict[cartItem.id]) {
          //             // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

          //             if (
          //               // !productSalesDict[cartItem.itemId].detailsList.find(
          //               //   (order) =>
          //               //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
          //               // )

          //               // !processedCartItemDict[
          //               // cartItem.itemId + cartItem.cartItemDate
          //               // ]
          //               true
          //             ) {
          //               // add if not existed

          //               // processedCartItemDict[
          //               //   cartItem.itemId + cartItem.cartItemDate
          //               // ] = true;

          //               ///////////////////////////////////////////////////////////////////

          //               var cartItemTax = 0;
          //               var cartItemSc = 0;
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
          //               // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

          //               var cartItemRatioTax = 0;
          //               var cartItemRatioSc = 0;

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioTax = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemTax = ((userOrdersFigures[i].tax || 0) *
          //                   cartItemRatioTax) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioSc = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioSc = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemSc = ((userOrdersFigures[i].sc || 0) *
          //                   cartItemRatioSc) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemSc = 0;
          //               }

          //               // var cartItemTax =
          //               //   ((allOutletsUserOrdersDone[i].tax || 0) *
          //               //     cartItemRatioTax) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemRatioSc =
          //               //   (cartItem.price /
          //               //     allOutletsUserOrdersDone[i].totalPrice) *
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemSc =
          //               //   ((allOutletsUserOrdersDone[i].sc || 0) *
          //               //     cartItemRatioSc) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;

          //               ///////////////////////////////////////////////////////////////////

          //               productSalesDict[cartItem.id].totalItems +=
          //                 cartItem.qty;
          //               productSalesDict[cartItem.id].totalSales +=
          //                 cartItem.price + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0,);
          //               //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
          //               // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesDict[cartItem.id].totalDiscount += userOrdersFigures[i].disc + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesDict[cartItem.id].totalDiscount += userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesDict[cartItem.id].itemNetSales +=
          //                 cartItem.price + cartItemTax + cartItemSc;

          //               // if (
          //               //   userOrdersFigures[i].paymentDetails &&
          //               //   userOrdersFigures[i].paymentDetails.channel
          //               // ) {
          //               //   if (
          //               //     OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
          //               //       (paymentMethod) =>
          //               //         paymentMethod.value.channel ===
          //               //         userOrdersFigures[i].paymentDetails.channel,
          //               //     )
          //               //   ) {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Offline';
          //               //   } else {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Online';
          //               //   }
          //               // }

          //               //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
          //               //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

          //               // need change to sku in future
          //               // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
          //               // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

          //               filteredOutletItemsTemp.push({
          //                 ...cartItem,
          //                 orderCompletedDate:
          //                   userOrdersFigures[i].createdAt,
          //                 outletId: global.payoutTransactionsExtend[transactionIndex].outletId,

          //                 totalItems: cartItem.qty,
          //                 totalSales: cartItem.price,
          //                 paymentDetails: cartItem.paymentDetails ? cartItem.paymentDetails : null,
          //                 // totalSalesReturn: cartItem.price * cartItem.quantity,
          //                 itemNetSales: cartItem.price,
          //                 discount: 0,
          //                 totalDiscount: 0,

          //                 itemName: productSalesDict[cartItem.id].productName,
          //                 quantity: cartItem.qty,
          //               });

          //               var accumDiscount = userOrdersFigures[i].cartItems.reduce((accum, c) => accum + (c.disc ? c.disc : 0), 0);

          //               productSalesDict[cartItem.id].detailsList.push({
          //                 ...userOrdersFigures[i],
          //                 discountPercentage: parseFloat(
          //                   isFinite(
          //                     accumDiscount /
          //                     (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                       accumDiscount),
          //                   )
          //                     ? (accumDiscount /
          //                       (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                         accumDiscount)) *
          //                     100
          //                     : 0,
          //                 ),
          //                 itemPrice: cartItem.price,
          //                 // itemTax: cartItem.price * currOutlet.taxRate,
          //                 itemTax: cartItemTax,
          //                 itemSc: cartItemSc,
          //                 //salesReturn:
          //               });

          //               if (selectedFilterProductId === '') {
          //                 setSelectedFilterProductId(cartItem.id);
          //               }
          //             }
          //           }
          //           else if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.id]) {
          //             // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

          //             if (
          //               // !productSalesDict[cartItem.itemId].detailsList.find(
          //               //   (order) =>
          //               //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
          //               // )

          //               // !processedCartItemDict[
          //               // cartItem.itemId + cartItem.cartItemDate
          //               // ]
          //               true
          //             ) {
          //               // add if not existed

          //               // processedCartItemDict[
          //               //   cartItem.itemId + cartItem.cartItemDate
          //               // ] = true;

          //               ///////////////////////////////////////////////////////////////////

          //               var cartItemTax = 0;
          //               var cartItemSc = 0;
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
          //               // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
          //               // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

          //               var cartItemRatioTax = 0;
          //               var cartItemRatioSc = 0;

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioTax = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemTax = ((userOrdersFigures[i].tax || 0) *
          //                   cartItemRatioTax) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemTax = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemRatioSc = (cartItem.price /
          //                   userOrdersFigures[i].totalPrice) *
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemRatioSc = 0;
          //               }

          //               if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
          //                 cartItemSc = ((userOrdersFigures[i].sc || 0) *
          //                   cartItemRatioSc) /
          //                   userOrdersFigures[i].userOrderPriceBeforeCommission;
          //               }
          //               else {
          //                 cartItemSc = 0;
          //               }

          //               // var cartItemTax =
          //               //   ((allOutletsUserOrdersDone[i].tax || 0) *
          //               //     cartItemRatioTax) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemRatioSc =
          //               //   (cartItem.price /
          //               //     allOutletsUserOrdersDone[i].totalPrice) *
          //               //   allOutletsUserOrdersDone[i].finalPrice;
          //               // var cartItemSc =
          //               //   ((allOutletsUserOrdersDone[i].sc || 0) *
          //               //     cartItemRatioSc) /
          //               //   allOutletsUserOrdersDone[i].finalPrice;

          //               ///////////////////////////////////////////////////////////////////

          //               productSalesUpsellingDict[cartItem.id].totalItems +=
          //                 cartItem.qty;
          //               productSalesUpsellingDict[cartItem.id].totalSales +=
          //                 cartItem.price + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0,);
          //               //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
          //               // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesUpsellingDict[cartItem.id].totalDiscount += userOrdersFigures[i].disc + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesUpsellingDict[cartItem.id].totalDiscount += userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
          //               productSalesUpsellingDict[cartItem.id].itemNetSales +=
          //                 cartItem.price + cartItemTax + cartItemSc;

          //               // if (
          //               //   userOrdersFigures[i].paymentDetails &&
          //               //   userOrdersFigures[i].paymentDetails.channel
          //               // ) {
          //               //   if (
          //               //     OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
          //               //       (paymentMethod) =>
          //               //         paymentMethod.value.channel ===
          //               //         userOrdersFigures[i].paymentDetails.channel,
          //               //     )
          //               //   ) {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Offline';
          //               //   } else {
          //               //     productSalesDict[cartItem.id].paymentDetails =
          //               //       'Online';
          //               //   }
          //               // }

          //               //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
          //               //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

          //               // need change to sku in future
          //               // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
          //               // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
          //               // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

          //               ///////////////////////////////////////////////////////////

          //               // hide first

          //               filteredOutletItemsUpsellingTemp.push({
          //                 ...cartItem,
          //                 orderCompletedDate:
          //                   userOrdersFigures[i].createdAt,
          //                 outletId: global.payoutTransactionsExtend[transactionIndex].outletId,

          //                 totalItems: cartItem.qty,
          //                 totalSales: cartItem.price,
          //                 paymentDetails: cartItem.paymentDetails ? cartItem.paymentDetails : null,
          //                 // totalSalesReturn: cartItem.price * cartItem.quantity,
          //                 itemNetSales: cartItem.price,
          //                 discount: 0,
          //                 totalDiscount: 0,

          //                 itemName: productSalesUpsellingDict[cartItem.id].productName,
          //                 quantity: cartItem.qty,
          //               });

          //               ///////////////////////////////////////////////////////////

          //               var accumDiscount = userOrdersFigures[i].cartItems.reduce((accum, c) => accum + (c.disc ? c.disc : 0), 0);

          //               productSalesUpsellingDict[cartItem.id].detailsList.push({
          //                 ...userOrdersFigures[i],
          //                 discountPercentage: parseFloat(
          //                   isFinite(
          //                     accumDiscount /
          //                     (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                       accumDiscount),
          //                   )
          //                     ? (accumDiscount /
          //                       (userOrdersFigures[i].userOrderPriceBeforeCommission +
          //                         accumDiscount)) *
          //                     100
          //                     : 0,
          //                 ),
          //                 itemPrice: cartItem.price,
          //                 // itemTax: cartItem.price * currOutlet.taxRate,
          //                 itemTax: cartItemTax,
          //                 itemSc: cartItemSc,
          //                 //salesReturn:
          //               });

          //               if (selectedFilterProductIdUpselling === '') {
          //                 setSelectedFilterProductIdUpselling(cartItem.id);
          //               }
          //             }
          //           }
          //         }

          //         // if (todayUserOrders[i].cartItemsCancelled) {
          //         //   for (
          //         //     var k = 0;
          //         //     k < todayUserOrders[i].cartItemsCancelled.length;
          //         //     k++
          //         //   ) {
          //         //     const cartItem =
          //         //       todayUserOrders[i].cartItemsCancelled[k];

          //         //     if (productSalesDict[cartItem.itemId]) {
          //         //       productSalesDict[cartItem.itemId].totalSalesReturn +=
          //         //         cartItem.price;

          //         //       // do for cancelled items
          //         //       // filteredOutletItemsTemp.push({
          //         //       //   ...cartItem,
          //         //       //   orderCompletedDate:
          //         //       //     allOutletsUserOrdersDone[i].createdAt,
          //         //       //   outletId: allOutletsUserOrdersDone[i].outletId,

          //         //       //   totalItems: cartItem.quantity,
          //         //       //   totalSales: 0,
          //         //       //   totalSalesReturn: cartItem.price,
          //         //       //   itemNetSales: 0,
          //         //       //   discount: 0,
          //         //       //   totalDiscount: 0,
          //         //       // });
          //         //     }
          //         //   }
          //         // }
          //       }
          //     }
          //   }
          // }

          /////////////////////////////////////////////////////////

          // 2023-02-14 - For today 6:00am orders until now

          // const todayUserOrders = todayUserOrdersRealTime.filter(o => o.createdAt >= historyStartDate && o.createdAt <= historyEndDate);

          for (var i = 0; i < todayUserOrders.length; i++) {
            if (
              moment(historyStartDate).isSameOrBefore(
                todayUserOrders[i].createdAt,
              ) &&
              moment(historyEndDate).isAfter(todayUserOrders[i].createdAt) 
              // &&
              // (filterAppType.includes(todayUserOrders[i].appType) || filterAppType.length === 0)
            ) {
              /////////////////////////////////////////
              // apply filter here

              // const isValid = filterChartItems(
              //   todayUserOrders[i],
              //   appliedChartFilterQueries,
              // );

              const isValid = true;

              /////////////////////////////////////////

              if (
                isValid
                // &&
                // todayUserOrders[i].outletId === currOutletId &&
                // ((moment(historyStartDate).isSameOrBefore(
                //   todayUserOrders[i].createdAt,
                // ) &&
                //   moment(historyEndDate).isAfter(
                //     todayUserOrders[i].createdAt,
                //   )))
                &&
                selectedFilterChannelList.includes(todayUserOrders[i].orderType)
              ) {
                for (
                  var j = 0;
                  j < todayUserOrders[i].cartItems.length;
                  j++
                ) {
                  const cartItem = todayUserOrders[i].cartItems[j];

                  ////////////////////////////////////////////////////////////

                  if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.itemId] === undefined) {
                    // create one first

                    var matchedItem = outletItems.find(o => o.uniqueId === cartItem.itemId);

                    if (matchedItem && outletCategoriesDict[matchedItem.categoryId]) {
                      var productSaleUpsellingRecord = {
                        productId: matchedItem.uniqueId,
                        summaryId: nanoid(),
                        productName: matchedItem.name,
                        productCategory:
                          outletCategoriesDict[matchedItem.categoryId].name,
                        // productCategory: '',
                        // productSku: outletItems[i].sku,
                        productSku: matchedItem.skuMerchant || 'N/A',
                        totalItems: 0,
                        totalSales: 0,
                        totalSalesReturn: 0,
                        totalDiscount: 0,
                        discount: 0,
                        itemNetSales: 0,
                        paymentDetails: '',
                        detailsList: [],
                      };

                      productSalesUpsellingDict[cartItem.itemId] = productSaleUpsellingRecord;
                      // productSalesSkuDict[outletItems[i].name] = productSaleUpsellingRecord; // change to sku in future
                    }
                  }

                  ////////////////////////////////////////////////////////////

                  if ((cartItem.upsellingCampaignId === '' || cartItem.upsellingCampaignId === undefined)
                    && productSalesDict[cartItem.itemId]) {
                    // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

                    if (
                      // !productSalesDict[cartItem.itemId].detailsList.find(
                      //   (order) =>
                      //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
                      // )
                      // !processedCartItemDict[
                      // cartItem.itemId + cartItem.cartItemDate
                      // ]
                      true
                    ) {
                      // add if not existed

                      processedCartItemDict[
                        cartItem.itemId + cartItem.cartItemDate
                      ] = true;

                      ///////////////////////////////////////////////////////////////////

                      var cartItemTax = 0;
                      var cartItemSc = 0;
                      // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
                      // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
                      // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

                      var cartItemRatioTax = 0;
                      var cartItemRatioSc = 0;

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemRatioTax = (cartItem.price /
                          todayUserOrders[i].totalPrice) *
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemRatioTax = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemTax = ((todayUserOrders[i].tax || 0) *
                          cartItemRatioTax) /
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemTax = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemRatioSc = (cartItem.price /
                          todayUserOrders[i].totalPrice) *
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemRatioSc = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemSc = ((todayUserOrders[i].sc || 0) *
                          cartItemRatioSc) /
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemSc = 0;
                      }

                      // var cartItemTax =
                      //   ((allOutletsUserOrdersDone[i].tax || 0) *
                      //     cartItemRatioTax) /
                      //   allOutletsUserOrdersDone[i].finalPrice;
                      // var cartItemRatioSc =
                      //   (cartItem.price /
                      //     allOutletsUserOrdersDone[i].totalPrice) *
                      //   allOutletsUserOrdersDone[i].finalPrice;
                      // var cartItemSc =
                      //   ((allOutletsUserOrdersDone[i].sc || 0) *
                      //     cartItemRatioSc) /
                      //   allOutletsUserOrdersDone[i].finalPrice;

                      ///////////////////////////////////////////////////////////////////

                      productSalesDict[cartItem.itemId].totalItems +=
                        cartItem.quantity;
                      productSalesDict[cartItem.itemId].totalSales +=
                        cartItem.price + getOrderDiscountInfo(todayUserOrders[i]);
                      //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
                      // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
                      productSalesDict[cartItem.itemId].totalDiscount += getOrderDiscountInfoInclOrderBased(todayUserOrders[i]);
                      productSalesDict[cartItem.itemId].itemNetSales +=
                        cartItem.price + cartItemTax + cartItemSc;
                      if (
                        todayUserOrders[i].paymentDetails &&
                        todayUserOrders[i].paymentDetails.channel
                      ) {
                        if (
                          OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
                            (paymentMethod) =>
                              paymentMethod.value.channel ===
                              todayUserOrders[i].paymentDetails.channel,
                          )
                        ) {
                          productSalesDict[cartItem.itemId].paymentDetails =
                            'Offline';
                        } else {
                          productSalesDict[cartItem.itemId].paymentDetails =
                            'Online';
                        }
                      }
                      //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
                      //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

                      // need change to sku in future
                      // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
                      // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
                      // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
                      // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

                      filteredOutletItemsTemp.push({
                        ...cartItem,
                        orderCompletedDate:
                          todayUserOrders[i].createdAt,
                        outletId: todayUserOrders[i].outletId,

                        totalItems: cartItem.quantity,
                        totalSales: cartItem.price,
                        paymentDetails: cartItem.paymentDetails,
                        // totalSalesReturn: cartItem.price * cartItem.quantity,
                        itemNetSales: cartItem.price,
                        discount: 0,
                        totalDiscount: 0,
                      });

                      var accumDiscount = todayUserOrders[i].cartItems.reduce((accum, c) => accum + (c.discount ? c.discount : 0), 0);

                      productSalesDict[cartItem.itemId].detailsList.push({
                        // ...todayUserOrders[i],
                        id: todayUserOrders[i].uniqueId,

                        discountPercentage: parseFloat(
                          isFinite(
                            accumDiscount /
                            (todayUserOrders[i].finalPrice +
                              accumDiscount),
                          )
                            ? (accumDiscount /
                              (todayUserOrders[i].finalPrice +
                                accumDiscount)) *
                            100
                            : 0,
                        ),
                        itemPrice: cartItem.price,
                        // itemTax: cartItem.price * currOutlet.taxRate,
                        itemTax: cartItemTax,
                        itemSc: cartItemSc,
                        //salesReturn:
                      });

                      if (selectedFilterProductId === '') {
                        setSelectedFilterProductId(cartItem.itemId);
                      }
                    }
                  }
                  else if ((cartItem.upsellingCampaignId)
                    && productSalesUpsellingDict[cartItem.itemId]) {
                    // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

                    if (
                      // !productSalesDict[cartItem.itemId].detailsList.find(
                      //   (order) =>
                      //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
                      // )
                      !processedCartItemDict[
                      cartItem.itemId + cartItem.cartItemDate
                      ]
                    ) {
                      // add if not existed

                      processedCartItemDict[
                        cartItem.itemId + cartItem.cartItemDate
                      ] = true;

                      ///////////////////////////////////////////////////////////////////

                      var cartItemTax = 0;
                      var cartItemSc = 0;
                      // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
                      // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
                      // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

                      var cartItemRatioTax = 0;
                      var cartItemRatioSc = 0;

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemRatioTax = (cartItem.price /
                          todayUserOrders[i].totalPrice) *
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemRatioTax = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemTax = ((todayUserOrders[i].tax || 0) *
                          cartItemRatioTax) /
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemTax = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemRatioSc = (cartItem.price /
                          todayUserOrders[i].totalPrice) *
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemRatioSc = 0;
                      }

                      if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                        cartItemSc = ((todayUserOrders[i].sc || 0) *
                          cartItemRatioSc) /
                          todayUserOrders[i].finalPrice;
                      }
                      else {
                        cartItemSc = 0;
                      }

                      // var cartItemTax =
                      //   ((allOutletsUserOrdersDone[i].tax || 0) *
                      //     cartItemRatioTax) /
                      //   allOutletsUserOrdersDone[i].finalPrice;
                      // var cartItemRatioSc =
                      //   (cartItem.price /
                      //     allOutletsUserOrdersDone[i].totalPrice) *
                      //   allOutletsUserOrdersDone[i].finalPrice;
                      // var cartItemSc =
                      //   ((allOutletsUserOrdersDone[i].sc || 0) *
                      //     cartItemRatioSc) /
                      //   allOutletsUserOrdersDone[i].finalPrice;

                      ///////////////////////////////////////////////////////////////////

                      productSalesUpsellingDict[cartItem.itemId].totalItems +=
                        cartItem.quantity;
                      productSalesUpsellingDict[cartItem.itemId].totalSales +=
                        cartItem.price + getOrderDiscountInfo(todayUserOrders[i]);
                      //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
                      // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
                      productSalesUpsellingDict[cartItem.itemId].totalDiscount += getOrderDiscountInfoInclOrderBased(todayUserOrders[i]);
                      productSalesUpsellingDict[cartItem.itemId].itemNetSales +=
                        cartItem.price + cartItemTax + cartItemSc;
                      if (
                        todayUserOrders[i].paymentDetails &&
                        todayUserOrders[i].paymentDetails.channel
                      ) {
                        if (
                          OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
                            (paymentMethod) =>
                              paymentMethod.value.channel ===
                              todayUserOrders[i].paymentDetails.channel,
                          )
                        ) {
                          productSalesUpsellingDict[cartItem.itemId].paymentDetails =
                            'Offline';
                        } else {
                          productSalesUpsellingDict[cartItem.itemId].paymentDetails =
                            'Online';
                        }
                      }
                      //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
                      //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

                      // need change to sku in future
                      // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
                      // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
                      // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
                      // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

                      filteredOutletItemsUpsellingTemp.push({
                        ...cartItem,
                        orderCompletedDate:
                          todayUserOrders[i].createdAt,
                        outletId: todayUserOrders[i].outletId,

                        totalItems: cartItem.quantity,
                        totalSales: cartItem.price,
                        paymentDetails: cartItem.paymentDetails,
                        // totalSalesReturn: cartItem.price * cartItem.quantity,
                        itemNetSales: cartItem.price,
                        discount: 0,
                        totalDiscount: 0,
                      });

                      var accumDiscount = todayUserOrders[i].cartItems.reduce((accum, c) => accum + (c.discount ? c.discount : 0), 0);

                      productSalesUpsellingDict[cartItem.itemId].detailsList.push({
                        // ...todayUserOrders[i],
                        id: todayUserOrders[i].uniqueId,

                        discountPercentage: parseFloat(
                          isFinite(
                            accumDiscount /
                            (todayUserOrders[i].finalPrice +
                              accumDiscount),
                          )
                            ? (accumDiscount /
                              (todayUserOrders[i].finalPrice +
                                accumDiscount)) *
                            100
                            : 0,
                        ),
                        itemPrice: cartItem.price,
                        // itemTax: cartItem.price * currOutlet.taxRate,
                        itemTax: cartItemTax,
                        itemSc: cartItemSc,
                        //salesReturn:
                      });

                      if (selectedFilterProductIdUpselling === '') {
                        setSelectedFilterProductIdUpselling(cartItem.itemId);
                      }
                    }
                  }
                }

                if (todayUserOrders[i].cartItemsCancelled) {
                  for (
                    var k = 0;
                    k < todayUserOrders[i].cartItemsCancelled.length;
                    k++
                  ) {
                    const cartItem =
                      todayUserOrders[i].cartItemsCancelled[k];

                    if (productSalesDict[cartItem.itemId]) {
                      productSalesDict[cartItem.itemId].totalSalesReturn +=
                        cartItem.price;

                      // do for cancelled items
                      // filteredOutletItemsTemp.push({
                      //   ...cartItem,
                      //   orderCompletedDate:
                      //     allOutletsUserOrdersDone[i].createdAt,
                      //   outletId: allOutletsUserOrdersDone[i].outletId,

                      //   totalItems: cartItem.quantity,
                      //   totalSales: 0,
                      //   totalSalesReturn: cartItem.price,
                      //   itemNetSales: 0,
                      //   discount: 0,
                      //   totalDiscount: 0,
                      // });
                    }
                  }
                }
              }
            }
          }

          /////////////////////////////////////////////////////////

          const productSalesTemp = Object.entries(productSalesDict).map(
            ([key, value]) => ({ ...value }),
          );
          const productSalesSkuTemp = Object.entries(productSalesSkuDict).map(
            ([key, value]) => ({ ...value }),
          );

          const productSalesUpsellingTemp = Object.entries(productSalesUpsellingDict).map(
            ([key, value]) => ({ ...value }),
          );

          setProductSales(productSalesTemp);
          setProductSalesSku(productSalesSkuTemp);

          setProductSalesUpselling(productSalesUpsellingTemp);

          //setCurrentPage(1);
          setPageCount(Math.ceil(productSalesTemp.length / perPage));

          setFilteredOutletItems(filteredOutletItemsTemp);
          setFilteredOutletItemsUpselling(filteredOutletItemsUpsellingTemp);

          setShowDetails(false);
        }

        setIsLoadingData(false);
      }
    }, 1000);
  }, [
    // allOutletsUserOrdersDone,
    outletItems,
    outletCategoriesDict,
    outletCategories,
    currOutletId,
    allOutlets,
    historyStartDate,
    historyEndDate,
    appliedChartFilterQueries,
    perPage,

    isMounted,

    selectedFilterProductList,
    selectedFilterChannelList,
    filterAppType,

    todayUserOrders,
    // todayUserOrdersRealTime,
    // payoutTransactions,
    // payoutTransactionsExtend,

    ptTimestamp,
    pteTimestamp,
  ]);

  useEffect(() => {
    if (showDetails && selectedItemSummary.detailsList) {
      setProductSalesDetails(selectedItemSummary.detailsList.map(details => {
        const findOrder = todayUserOrders.find(order => order.uniqueId === details.id);

        return {
          ...findOrder,
          ...details,
        };
      }));

      setPageReturn(currentPage);
      // console.log('currentPage value is');
      // console.log(currentPage);
      setCurrentDetailsPage(1);
      setDetailsPageCount(
        Math.ceil(selectedItemSummary.detailsList.length / perPage),
      );
    }
  }, [showDetails, selectedItemSummary, perPage, filterAppType,]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //     tabBarVisible: false,
  // });

  useEffect(() => {
    var title = 'Upselling Sales Report';

    if (
      pageStack.length > 1 &&
      pageStack[pageStack.length - 2] === 'Dashboard'
    ) {
      title = 'Today Upselling Sales Report';

      setSalesBarChartPeriod(CHART_PERIOD.TODAY);
      // setRev_date(moment().startOf('day'));
      // setRev_date1(moment().endOf('day'));
      CommonStore.update(s => {
        s.historyStartDate = moment().startOf('day');
        s.historyEndDate = moment().endOf('day');
      });
    } else {
      title = 'Upselling Sales Report';

      setSalesBarChartPeriod(CHART_PERIOD.THIS_MONTH);
      // setRev_date(moment().subtract(1, 'month').startOf('day'));
      // setRev_date1(moment().endOf('day'));
      CommonStore.update(s => {
        s.historyStartDate = moment().subtract(1, 'month').startOf('day');
        s.historyEndDate = moment().endOf('day');
      });
    }

    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => {
            if (isAlphaUser || true) {
              navigation.navigate('MenuOrderingScreen');

              CommonStore.update((s) => {
                s.currPage = 'MenuOrderingScreen';
                s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
              });
            }
            else {
              navigation.navigate('Table');

              CommonStore.update((s) => {
                s.currPage = 'Table';
                s.currPageStack = [...currPageStack, 'Table'];
              });
            }
            if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
              CommonStore.update((s) => {
                s.expandTab = EXPAND_TAB_TYPE.OPERATION;
              });
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_REPORT_UP_C_LOGO,
              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_LOGO
            })
          }}
          style={styles.headerLeftStyle}>
          <Image
            style={[{
              width: 124,
              height: 26,
            }, switchMerchant ? {
              transform: [
                { scaleX: 0.7 },
                { scaleY: 0.7 }
              ],
            } : {}]}
            resizeMode="contain"
            source={require('../assets/image/logo.png')}
          />
        </TouchableOpacity>
      ),
      headerTitle: () => (
        <View
          style={[
            {
              // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
              // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
              // marginRight: Platform.OS === 'ios' ? "27%" : 0,
              // bottom: switchMerchant ? '2%' : 0,
              ...global.getHeaderTitleStyle(),
            },
            // windowWidth >= 768 && switchMerchant
            //   ? { right: windowWidth * 0.1 }
            //   : {},
            // windowWidth <= 768
            //   ? { right: 20 }
            //   : {},
          ]}>
          <Text
            style={{
              fontSize: switchMerchant ? 20 : 24,
              // lineHeight: 25,
              textAlign: 'left',
              alignItems: 'flex-start',
              justifyContent: 'flex-start',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.whiteColor,
              opacity: 1,
              paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
            }}>
            Upselling Sales Report
          </Text>
        </View>
      ),
      headerRight: () => (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          {outletSelectDropdownView()}
          <View
            style={{
              backgroundColor: 'white',
              width: 0.5,
              height: windowHeight * 0.025,
              opacity: 0.8,
              marginHorizontal: 15,
              bottom: -1,
            }} />
          <TouchableOpacity
            onPress={() => {
              if (global.currUserRole === 'admin') {
                navigation.navigate('Setting');
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_REPORT_UP_C_GEN_SET,
                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_GEN_SET
              })
            }}
            style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text
              style={[{
                fontFamily: 'NunitoSans-SemiBold',
                fontSize: switchMerchant ? 10 : 16,
                color: Colors.secondaryColor,
                marginRight: 15,
              }, switchMerchant ? { width: windowWidth / 8 } : {}]}
              numberOfLines={switchMerchant ? 1 : 1}
            >
              {userName}
            </Text>
            <View
              style={{
                marginRight: 30,
                width: windowHeight * 0.05,
                height: windowHeight * 0.05,
                borderRadius: windowHeight * 0.05 * 0.5,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'white',
              }}>
              <Image
                style={{
                  width: windowHeight * 0.035,
                  height: windowHeight * 0.035,
                  alignSelf: 'center',
                }}
                source={require('../assets/image/profile-pic.jpg')}
              />
            </View>
          </TouchableOpacity>
        </View>
      ),
    });
  }, [pageStack]);

  // componentDidMount = () => {
  //     moment()
  // }

  /* const email = () => {
        var body = {
            email: '<EMAIL>',
            data: list
        }
        ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible1: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    } */

  /* const download = () => {
        var body = {
            data: list
        }
        ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    } */

  // const add = async () => {
  //     if (page + 1 < pageCount) {
  //         await setState({ page: page + 1, currentPage: currentPage + 1 })
  //         // console.log(page)
  //         var e = page
  //         next(e)
  //     }
  // }

  // const next = (e) => {
  //     const offset = e * perPage;
  //     setState({ offset: offset })
  //     loadMoreData()
  // }

  // const less = async () => {
  //     if (page > 0) {
  //         await setState({ page: page - 1, currentPage: currentPage - 1 })
  //         // console.log(page)
  //         var y = page
  //         pre(y)
  //     }
  // }

  // const pre = (y) => {

  //     const offset = y * perPage;
  //     setState({ offset: offset })
  //     loadMoreData()

  // }

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const nextDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage + 1 > detailsPageCount
        ? currentDetailsPage
        : currentDetailsPage + 1,
    );
  };

  const prevDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1,
    );
  };

  const loadMoreData = () => {
    const data = oriList;
    const slice = data.slice(offset, offset + perPage);
    setState({ list: slice, pageCount: Math.ceil(data.length / perPage) });
  };

  // moment = async () => {
  //     const today = new Date();
  //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
  //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
  //     getDetail()
  // }

  const getDetail = () => {
    ApiClient.GET(
      `${API.getSalesBySku + 1}&startDate=${startDate}&endDate=${endDate}`,
    ).then((result) => {
      var data = result;
      var slice = data.slice(offset, offset + perPage);
      setState({
        list: slice,
        oriList: data,
        pageCount: Math.ceil(data.length / perPage),
      });
    });
  };

  const decimal = (value) => {
    return value.toFixed(2);
  };

  const renderSearchItem = ({ item, index }) =>
    (index + 1) % 2 == 0 ? (
      <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.productName}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.category}
          </Text>
          <View style={{ flex: 3 }}>
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {item.productName}
            </Text>
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              -
            </Text>
          </View>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.quantity}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalPrice}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalDiscount}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.discount}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
        </View>
      </View>
    ) : (
      <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.productName}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.category}
          </Text>
          <View style={{ flex: 3 }}>
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {item.productName}
            </Text>
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              -
            </Text>
          </View>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.quantity}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalPrice}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00 ({item.totalDiscount}%)
          </Text>
          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalDiscount}</Text> */}
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.discount}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
        </View>
      </View>
    );

  const onItemSummaryClicked = (item) => {
    // setProductSalesDetails(item.detailsList);
    setSelectedItemSummary(item);
    setShowDetails(true);

    // setCurrentPage(1);
    // setPageCount(Math.ceil(item.detailsList.length / perPage));

    // console.log('item.detailsList');
    // console.log(item.detailsList);
  };

  const renderItem = ({ item, index }) => {
    let tempTotalSales = 0;
    let tempNetSales = 0;
    for (let i = 0; i < item.detailsList.length; i++) {
      // console.log(item.detailsList[i], 'here');
      tempTotalSales += item.detailsList[i].finalPrice;
      tempNetSales +=
        (item.detailsList[i].finalPriceBefore
          ? item.detailsList[i].finalPriceBefore
          : item.detailsList[i].finalPrice) -
        item.detailsList[i].tax -
        (item.detailsList[i].sc ? item.detailsList[i].sc : 0);
    }
    return (
      // (index + 1) % 2 == 0 ?
      // : <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
      //     <View style={{ flexDirection: 'row', }}>
      //         <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.productName}</Text>
      //         <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.category}</Text>
      //         <View style={{ flex: 3 }}>
      //             <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.productName}</Text>
      //             <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>-</Text>
      //         </View>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.quantity}</Text>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalPrice}</Text>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>0.00</Text>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalDiscount}</Text>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.discount}</Text>
      //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.netSale}</Text>
      //     </View>
      // </View>
      (<TouchableOpacity
        onPress={() => {
          onItemSummaryClicked(item);
          setDetailsTitle(item.productName);
          // console.log(item, 'here');
        }}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // width: '100%',
        }}>
        {/* <View style={{ backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.fieldtBgColor, padding: 12 }}> */}
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              width: '5%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((currentPage - 1) * perPage) + index + 1}
          </Text>
          <Text
            style={{
              width: '19%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.productName}
          </Text>
          <Text
            style={{
              width: '11%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.productCategory}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.productSku}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.totalItems}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.totalSales
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{(item.totalSalesReturn).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.totalDiscount
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {(item.totalDiscount != 0
              ? (item.totalDiscount / (item.itemNetSales + item.totalDiscount)) * 100
              : 0
            ).toFixed(2)}
          </Text>
          <Text
            style={{
              width: '11%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.paymentDetails ? item.paymentDetails : 'N/A'}
          </Text>
          {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.discount}</Text> */}
          {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{(item.itemNetSales).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}
          <Text
            style={{
              width: '11%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'right',
              paddingRight: 20,
            }}>
            {item.itemNetSales
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
        </View>
      </TouchableOpacity>)
    );
  };

  const onClickItemDetails = (item) => {
    setExpandDetailsDict({
      ...expandDetailsDict,
      [item.uniqueId]: expandDetailsDict[item.uniqueId] ? false : true,
    });
  };

  const renderItemDetails = ({ item, index }) => {
    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    // for (var i = 0; i < item.cartItems.length; i++) {
    //     const cartItemPriceWIthoutAddOn =
    //         item.cartItems[i].price -
    //         item.cartItems[i].addOns.reduce(
    //             (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //             0,
    //         );

    //     if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
    //         longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
    //     }

    //     for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //         if (
    //             item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
    //         ) {
    //             longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
    //         }
    //     }
    // }

    // if (item.totalPrice.toFixed(0).length > longestStr) {
    //     longestStr = item.totalPrice.toFixed(0).length;
    // }

    // if (item.discount.toFixed(0).length > longestStr) {
    //     longestStr = item.discount.toFixed(0).length;
    // }

    // if (item.tax.toFixed(0).length > longestStr) {
    //     longestStr = item.tax.toFixed(0).length;
    // }

    // if (item.finalPrice.toFixed(0).length > longestStr) {
    //     longestStr = item.finalPrice.toFixed(0).length;
    // }

    // // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    var cartItemPriceWIthoutAddOnSpacingList = [];
    var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //       1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //           item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    var totalPriceSpacing =
      Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    var discountSpacing =
      Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    var finalPriceSpacing =
      Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    ///////////////////////////
    return (
      <View
        // onPress={() => {
        //   onClickItemDetails(item);
        //   // console.log(item);
        // }}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              width: '6%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((currentDetailsPage - 1) * perPage) + index + 1}
          </Text>
          <Text
            style={{
              width: '14%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.orderId ? `#${item.orderId}` : 'N/A'}
          </Text>
          <Text
            style={{
              width: '18%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {moment(item.createdAt).format('DD MMM YY hh:mm A')}
          </Text>
          <Text
            style={{
              width: '10%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat((item.itemPrice) + getOrderDiscountInfo(item))
              //(item.finalPriceBefore ? item.finalPriceBefore : item.finalPrice)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '7%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(getOrderDiscountInfoInclOrderBased(item))
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          {/* <Text
            style={{
              width: '7%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.discountPercentage)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text> */}
          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.discountPercentage).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.itemTax || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.itemSc || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.salesReturn || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text> */}
          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.finalPrice).toFixed(2)}</Text> */}
          <View
            style={{
              width: '12%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingLeft: 10,
              paddingRight: switchMerchant ? '2.05%' : '1.75%',
            }}>
            <Text style={{}} />
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {/* <Text style={{
                            opacity: 0,
                            ...Platform.OS === 'android' && {
                                color: 'transparent',
                            },
                        }}>
                            {'0'.repeat((finalPriceSpacing * 0.6) + (item.finalPrice.toFixed(0).length === 1 ? 1 : 0))}
                        </Text> */}
              {(item.itemPrice + item.itemTax + item.itemSc)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
          {/* <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text> */}
        </View>

        {expandDetailsDict[item.uniqueId] == true ? (
          <View
            style={{
              minheight: windowHeight * 0.35,
              marginTop: 30,
              paddingBottom: 20,
            }}>
            {item.cartItems.map((cartItem, index) => {
              const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

              return (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      alignItems: 'flex-start',
                      flexDirection: 'row',
                      marginBottom: Platform.OS == 'ios' ? 10 : 10,
                      minHeight: 80,
                      //backgroundColor: 'yellow',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',
                        //backgroundColor: 'blue',
                      }}>
                      {index == 0 ? (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            //justifyContent: 'center',
                            alignItems: 'center',
                            //backgroundColor: 'blue',
                          }}>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center',
                              marginTop: 0,
                            }}
                            onPress={() => {
                              var crmUser = null;

                              if (item.crmUserId !== undefined) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (item.crmUserId === crmUsers[i].uniqueId) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (!crmUser) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (item.userId === crmUsers[i].firebaseUid) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (crmUser) {
                                CommonStore.update(
                                  (s) => {
                                    s.selectedCustomerEdit = crmUser;
                                    // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                    s.routeParams = {
                                      pageFrom: 'Reservation',
                                    };
                                  },
                                  () => {
                                    navigation.navigate('NewCustomer');
                                  },
                                );
                              }
                            }}>
                            <Image
                              style={{
                                width: switchMerchant ? 30 : 60,
                                height: switchMerchant ? 30 : 60,
                              }}
                              resizeMode="contain"
                              source={require('../assets/image/default-profile.png')}
                            />

                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    marginTop: 0,
                                    fontSize: 13,
                                    textAlign: 'center',
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      marginTop: 0,
                                      fontSize: 10,
                                      textAlign: 'center',
                                    }
                                    : {},
                                ]}
                                numberOfLines={1}>
                                {item.userName ? item.userName : 'Guest'}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        />
                      )}

                      <View
                        style={{
                          // flex: 0.3,
                          width: '5%',
                          //justifyContent: 'center',
                          alignItems: 'center',
                          //backgroundColor: 'red',
                          //paddingLeft: '1.2%',
                        }}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 13,
                            },
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          {index + 1}.
                        </Text>
                      </View>

                      <View
                        style={{
                          //flex: 0.5,
                          width: '10%',
                          //backgroundColor: 'green',
                          alignItems: 'center',
                        }}>
                        {cartItem.image ? (
                          <AsyncImage
                            source={{ uri: cartItem.image }}
                            // item={cartItem}
                            style={{
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}
                          />
                        ) : (
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}>
                            <Ionicons
                              name="fast-food-outline"
                              size={switchMerchant ? 25 : 35}
                            />
                          </View>
                        )}
                      </View>
                      <View style={{ width: '75%' }}>
                        <View
                          style={{
                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            marginBottom: 10,
                            //backgroundColor: 'blue',
                            width: '100%',
                            flexDirection: 'row',
                          }}>
                          <View style={{ width: '59.7%' }}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 13,
                                },
                                switchMerchant
                                  ? {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                            </Text>
                          </View>

                          <View
                            style={{
                              width: '13%',
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 13,
                                  },
                                  // Platform.OS === 'android'
                                  //   ? {
                                  //       width: '200%',
                                  //     }
                                  //   : {},
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                x{cartItem.quantity}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              width: '18.8%',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : { fontSize: 13 }
                              }>
                              RM
                            </Text>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                                  : {
                                    fontSize: 13,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                              }>
                              {cartItemPriceWIthoutAddOn
                                .toFixed(2)
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                            </Text>
                          </View>
                        </View>

                        {cartItem.remarks && cartItem.remarks.length > 0 ? (
                          <View
                            style={{
                              alignItems: 'center',
                              flexDirection: 'row',
                              marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            }}>
                            <View style={{ justifyContent: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 13,
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                {cartItem.remarks}
                              </Text>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}

                        {cartItem.addOns.map((addOnChoice, i) => {
                          const addOnChoices = addOnChoice.choiceNames.join(", ");
                          return (
                            <View
                              style={{
                                flexDirection: 'row',
                                // marginLeft: -5,
                                width: '100%',
                              }}>
                              <View
                                style={{
                                  width: '59.7%',
                                  flexDirection: 'row',
                                  marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                }}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '25%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '25%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.name}:`}
                                </Text>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '75%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '75%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoices}`}
                                </Text>
                              </View>

                              <View
                                style={[
                                  {
                                    width: '13%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    //backgroundColor: 'blue',
                                  },
                                ]}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '28%',
                                      // right: 38,
                                      //backgroundColor: 'green',
                                      textAlign: 'center',
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '28%',
                                        textAlign: 'center',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.quantities
                                    ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                    : ''
                                    }`}
                                </Text>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '18.8%',
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={[
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        fontSize: 10,
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        fontSize: 13,
                                      },
                                  ]}>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 13,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {(getAddOnChoicePrice(addOnChoice, cartItem))
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', width: '100%' }}>
                    <View style={{ width: '63%' }} />
                    <View style={{ width: 15 }} />
                    {index === item.cartItems.length - 1 ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          //backgroundColor: 'yellow',
                          width: '28.65%',
                        }}>
                        <View
                          style={{
                            justifyContent: 'center',
                            width: '100%',
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Subtotal:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {((item.isRefundOrder && item.finalPrice <= 0)
                                  ? 0
                                  : item.totalPrice +
                                  getOrderDiscountInfo(item)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                          {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                            <View
                              style={{
                                flexDirection: 'row',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                    : {
                                      fontSize: 13,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                }>
                                Delivery Fee:
                              </Text>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '49.2%',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? { fontSize: 10 }
                                      : { fontSize: 13 }
                                  }>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        fontSize: 13,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {item.deliveryFee
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Discount:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                }}>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {' '}
                                {((item.isRefundOrder && item.finalPrice <= 0)
                                  ? 0
                                  :
                                  // item.discount +
                                  getOrderDiscountInfoInclOrderBased(item)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Tax:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                    }
                                    : { fontSize: 13, paddingRight: 20 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {item.tax
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Service Charge:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: switchMerchant ? '49.15%' : '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(item.sc || 0)
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Rounding:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                }}>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(item.finalPrice
                                  ? item.finalPrice - item.finalPriceBefore
                                  : 0
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Total:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                }}>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {item.finalPrice
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>

                  {/* <View style={{alignItems:'flex-end'}}>
                    <View style={{ flexDirection: 'row' }}>
                      <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                    </View>
                  </View> */}
                  {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                  <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                    
                    <View style={{ flex: 1, justifyContent: 'center', }}>
                      <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                    </View>
                    
                  </View>
                  : <></>
                } */}
                </View>
              );
            })}
          </View>
        ) : null}
      </View>
    );
  };

  const downloadCsv = () => {
    //if (productSales && productSales.dataSource && productSales.dataSource.data) {
    //const csvData = convertArrayToCSV(productSales.dataSource.data);
    const csvData = convertArrayToCSV(CsvData);

    const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
      }/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // console.log('PATH', pathToWrite);
    RNFetchBlob.fs
      .writeFile(pathToWrite, csvData, 'utf8')
      .then(() => {
        // console.log(`wrote file ${pathToWrite}`);
        // wrote file /storage/emulated/0/Download/data.csv
        Alert.alert(
          'Success',
          `Send to ${pathToWrite}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((error) => console.error(error));
    //}
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    if (!showDetails) {
      for (var i = 0; i < productSalesUpselling.length; i++) {
        var excelRow = {
          'Product': productSalesUpselling[i].productName,
          'Category': productSalesUpselling[i].productCategory,
          'SKU': productSalesUpselling[i].productSku,
          'Items (Qty)': +parseFloat(productSalesUpselling[i].totalItems),
          'Sales (RM)': +parseFloat(productSalesUpselling[i].totalSales).toFixed(2),
          'Disc (RM)': +parseFloat(productSalesUpselling[i].totalDiscount).toFixed(
            2,
          ),
          'Disc (RM)': +parseFloat(productSalesUpselling[i].totalDiscount != 0
            ? (productSalesUpselling[i].totalDiscount / (productSalesUpselling[i].itemNetSales + productSalesUpselling[i].totalDiscount)) * 100
            : 0
          ).toFixed(2),
          'Payment Type': productSalesUpselling[i].paymentDetails ? productSalesUpselling[i].paymentDetails : 'N/A',
          'Net Sales': +parseFloat(productSalesUpselling[i].itemNetSales).toFixed(
            2,
          ),
        };

        excelData.push(excelRow);
      }

      for (var i = 0; i < productSales.length; i++) {
        var excelRow = {
          'Product': productSales[i].productName,
          'Category': productSales[i].productCategory,
          'SKU': productSales[i].productSku,
          'Items (Qty)': +parseFloat(productSales[i].totalItems),
          'Sales (RM)': +parseFloat(productSales[i].totalSales).toFixed(2),
          'Disc (RM)': +parseFloat(productSales[i].totalDiscount).toFixed(
            2,
          ),
          'Disc (RM)': +parseFloat(productSales[i].totalDiscount != 0
            ? (productSales[i].totalDiscount / (productSales[i].itemNetSales + productSales[i].totalDiscount)) * 100
            : 0
          ).toFixed(2),
          'Payment Type': productSales[i].paymentDetails ? productSales[i].paymentDetails : 'N/A',
          'Net Sales': +parseFloat(productSales[i].itemNetSales).toFixed(
            2,
          ),
        };

        excelData.push(excelRow);
      }
    } else {
      for (var i = 0; i < productSalesDetails.length; i++) {
        const calculatedDiscount = getOrderDiscountInfoInclOrderBased(productSalesDetails[i]);

        var excelRow = {
          //Have to change to product sales details
          // 'Transaction Category': ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType],
          // 'Sales (RM)': parseFloat(transactionTypeSalesDetails[i].finalPrice).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Transaction Date': moment(transactionTypeSalesDetails[i].createdAt).format('DD MMM hh:mma'),
          // 'Total Discount (RM)': parseFloat(transactionTypeSalesDetails[i].discount).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Discount (%)': parseFloat(isFinite(transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount) ? transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount * 100 : 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Tax (RM)': parseFloat(transactionTypeSalesDetails[i].tax).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Tax (RM)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'GP (%)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Net Sales (RM)': parseFloat(transactionTypeSalesDetails[i].totalPrice).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          'Order ID': productSalesDetails[i].orderId
            ? `#${productSalesDetails[i].orderId}`
            : 'N/A',
          'Transaction Date': moment(
            productSalesDetails[i].createdAt,
          ).format('DD MMM hh:mm A'),
          'Sales (RM)': parseFloat((productSalesDetails[i].itemPrice) + getOrderDiscountInfo(productSalesDetails[i]))
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          'Disc (RM)': parseFloat(getOrderDiscountInfoInclOrderBased(productSalesDetails[i]))
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'Discount (%)': parseFloat(
          //   isFinite(
          //     calculatedDiscount /
          //     (productSalesDetails[i].finalPrice + calculatedDiscount),
          //   )
          //     ? (calculatedDiscount /
          //       (productSalesDetails[i].finalPrice + calculatedDiscount)) *
          //     100
          //     : 0,
          // )
          //   .toFixed(2)
          //   .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          'Tax (RM)': parseFloat(productSalesDetails[i].tax)
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          'Service Chage (RM)': parseFloat(productSalesDetails[i].sc || 0)
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          // 'GP (%)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
          'Sales Return (RM)': parseFloat(productSalesDetails[i].salesReturn || 0)
            .toFixed(2),
          'Net Sales (RM)': parseFloat(productSalesDetails[i].itemPrice + productSalesDetails[i].itemTax + productSalesDetails[i].itemSc)
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
        };

        excelData.push(excelRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    if (!showDetails) {
      csvData.push(
        `Product,Category,SKU,Item (Qty),Sales (RM),Disc (RM),Disc (%),Payment Type,Net Sales (RM)`,
      );

      for (var i = 0; i < productSalesUpselling.length; i++) {
        var csvRow = `${productSalesUpselling[i].productName.split(', ').join(' ')},${productSalesUpselling[i].productCategory
          },${productSalesUpselling[i].productSku},${productSalesUpselling[i].totalItems
          },${+parseFloat(productSalesUpselling[i].totalSales).toFixed(2)},${+parseFloat(
            productSalesUpselling[i].totalDiscount,
          ).toFixed(2)},${(productSalesUpselling[i].totalDiscount != 0
            ? (productSalesUpselling[i].totalDiscount / productSalesUpselling[i].totalSales) * 100
            : 0
          ).toFixed(2)},${productSalesUpselling[i].paymentDetails
            ? productSalesUpselling[i].paymentDetails
            : 'N/A'
          },${+parseFloat(productSalesUpselling[i].itemNetSales).toFixed(2)}`;

        csvData.push(csvRow);
      }

      for (var i = 0; i < productSales.length; i++) {
        var csvRow = `${productSales[i].productName.split(', ').join(' ')},${productSales[i].productCategory
          },${productSales[i].productSku},${productSales[i].totalItems
          },${+parseFloat(productSales[i].totalSales).toFixed(2)},${+parseFloat(
            productSales[i].totalDiscount,
          ).toFixed(2)},${(productSales[i].totalDiscount != 0
            ? (productSales[i].totalDiscount / productSales[i].totalSales) * 100
            : 0
          ).toFixed(2)},${productSales[i].paymentDetails
            ? productSales[i].paymentDetails
            : 'N/A'
          },${+parseFloat(productSales[i].itemNetSales).toFixed(2)}`;

        csvData.push(csvRow);
      }
    } else {
      // csvData.push(
      //   `Order ID,Transaction Date,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      // );

      csvData.push(
        `Order ID,Transaction Date,Sales (RM),Disc (RM),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < productSalesDetails.length; i++) {
        // var csvRow = `${productSalesDetails[i].orderId
        //   ? `#${productSalesDetails[i].orderId.split(', ').join(' | ')}`
        //   : 'N/A'
        //   },${moment(productSalesDetails[i].createdAt).format(
        //     'DD MMM YY hh:mm A',
        //   )},${+parseFloat(
        //     productSalesDetails[i].itemPrice +
        //     productSalesDetails[i].itemTax +
        //     productSalesDetails[i].itemSc,
        //   ).toFixed(2)},${+parseFloat(productSalesDetails[i].discount).toFixed(
        //     2,
        //   )},${+parseFloat(productSalesDetails[i].discountPercentage).toFixed(
        //     2,
        //   )},${productSalesDetails[i].tax || 0},${productSalesDetails[i].sc || 0
        //   },${productSalesDetails[i].salesReturn || 0},${productSalesDetails[i].itemPrice
        //   }`;

        var csvRow = `${productSalesDetails[i].orderId
          ? `#${productSalesDetails[i].orderId.split(', ').join(' | ')}`
          : 'N/A'
          },${moment(productSalesDetails[i].createdAt).format(
            'DD MMM YY hh:mm A',
          )},${+parseFloat((productSalesDetails[i].itemPrice) + getOrderDiscountInfo(productSalesDetails[i]))},${+parseFloat(getOrderDiscountInfoInclOrderBased(productSalesDetails[i])).toFixed(
            2,
          )},${+parseFloat(productSalesDetails[i].tax || 0).toFixed(2)},${+parseFloat(productSalesDetails[i].sc || 0).toFixed(2)
          },${+parseFloat(productSalesDetails[i].salesReturn || 0).toFixed(2)},${parseFloat(productSalesDetails[i].itemPrice + productSalesDetails[i].itemTax + productSalesDetails[i].itemSc).toFixed(2)
          }`;

        csvData.push(csvRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return csvData.join('\r\n');
  };

  const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-report-Product-Sales${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Upselling Sales Report',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });

    // XLSX.writeFileAsync(excelFile, excelWorkBook, () => {
    //     Alert.alert(
    //         'Success',
    //         `Send to ${excelFile}`,
    //         [{ text: 'OK', onPress: () => { } }],
    //         { cancelable: false },
    //     );
    // });

    // const csvData = convertArrayToCSV(CsvData);

    // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // // console.log("PATH", excelFile);
    // RNFetchBlob.fs
    //     .writeFile(excelFile, excelWorkBook, 'utf8')
    //     .then(() => {
    //         // console.log(`wrote file ${excelFile}`);
    //         Alert.alert(
    //             'Success',
    //             `Send to ${excelFile}`,
    //             [{ text: 'OK', onPress: () => { } }],
    //             { cancelable: false },
    //         );
    //     })
    //     .catch(error => console.error(error));
  };

  // const emailProductSales = () => {
  //     var body = {
  //         data: CsvData,
  //         //data: convertArrayToCSV(productSales.dataSource.data),
  //         data: convertArrayToCSV(CsvData),
  //         email: exportEmail,
  //     };
  //     //API need to change
  //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
  //         if (result !== null) {
  //             Alert.alert(
  //                 'Success',
  //                 'Email sent to your inbox',
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         }
  //     });

  //     setVisible(false);
  // };

  // Test Email
  const emailProductSales = () => {
    const excelData = convertDataToExcelFormat();

    var body = {
      // data: CsvData,
      //data: convertArrayToCSV(todaySalesChart.dataSource.data),
      data: JSON.stringify(excelData),
      //data: convertDataToExcelFormat(),
      email: exportEmail,
    };

    ApiClient.POST(API.emailDashboard, body, false).then((result) => {
      if (result !== null) {
        Alert.alert(
          'Success',
          'Email has been sent',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    });

    setVisible(false);
  };

  const eventsChart = {
    dataPlotClick: (e, item) => {
      // console.log('test data plot');
    },
  };

  const changeClick = () => {
    if (day == true) {
      setState({ day: false });
    } else setState({ day: true });
  };

  var leftSpacing = '0%';

  if (windowWidth >= 1280) {
    leftSpacing = '10%';
  }

  const leftSpacingScale = {
    marginLeft: leftSpacing,
  };

  const filterPressed = (filterTapped) => {
    if (filterTapped == 1) {
      setExpandSelection(true);
    }
  };

  const groupByPressed = (groupByTapped) => {
    if (groupByTapped == 1) {
      setExpandGroupBy(true);
    }
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 100 });
  };

  return (
    // <View style={styles.container}>
    //     <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={8}
            expandReport
          />
        </View> */}
        <ScrollView horizontal>
          <ModalView
            style={{}}
            visible={exportModalVisibility}
            supportedOrientations={['portrait', 'landscape']}
            transparent
            animationType={'fade'}>
            <View
              style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',
                top:
                  Platform.OS === 'android'
                    ? 0
                    : keyboardHeight > 0
                      ? -keyboardHeight * 0.45
                      : 0,
              }}>
              <View
                style={{
                  height: windowWidth * 0.3,
                  width: windowWidth * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: windowWidth * 0.03,
                  alignItems: 'center',
                  justifyContent: 'center',

                  ...getTransformForModalInsideNavigation(),
                }}>
                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    position: 'absolute',
                    right: windowWidth * 0.02,
                    top: windowWidth * 0.02,

                    elevation: 1000,
                    zIndex: 1000,
                  }}
                  onPress={() => {
                    setExportModalVisibility(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View
                  style={{
                    alignItems: 'center',
                    top: '20%',
                    position: 'absolute',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 16 : 24,
                    }}>
                    Download Report
                  </Text>
                </View>
                <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 20,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    Email Address:
                  </Text>
                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: switchMerchant ? 240 : 370,
                      height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      padding: 5,
                      marginVertical: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    autoCapitalize="none"
                    placeholderStyle={{ padding: 5 }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    placeholder="Enter your email"
                    onChangeText={(text) => {
                      setExportEmail(text);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_REPORT_UP_DL_TB_EMAIL_ADDRESS,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_DL_TB_EMAIL_ADDRESS
                      })
                    }}
                    value={exportEmail}
                  />
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 20,
                      fontFamily: 'NunitoSans-Bold',
                      marginTop: 15,
                    }}>
                    Send As:
                  </Text>

                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'row',
                      marginTop: 10,
                    }}>
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: switchMerchant ? 100 : 100,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 15,
                      }}
                      onPress={() => {
                        if (exportEmail.length > 0) {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });

                          setIsExcel(true);

                          const excelData = convertDataToExcelFormat();

                          generateEmailReport(
                            EMAIL_REPORT_TYPE.EXCEL,
                            excelData,
                            'KooDoo Upselling Sales Report',
                            'KooDoo Upselling Sales Report.xlsx',
                            `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                            exportEmail,
                            'KooDoo Upselling Sales Report',
                            'KooDoo Upselling Sales Report',
                            () => {
                              CommonStore.update((s) => {
                                s.isLoading = false;
                              });

                              setIsExcel(false);

                              Alert.alert(
                                'Success',
                                'Report will be sent to the email address shortly',
                              );

                              setExportModalVisibility(false);
                            },
                          );
                        } else {
                          Alert.alert('Info', 'Invalid email address');
                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_DL_RP_C_REP_EXCEL,
                            eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_DL_RP_C_REP_EXCEL
                          })
                        }
                      }}>
                      {isLoading && isExcel ? (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          EXCEL
                        </Text>
                      )}
                    </TouchableOpacity>

                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: switchMerchant ? 100 : 100,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      onPress={() => {
                        if (exportEmail.length > 0) {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });

                          setIsCsv(true);

                          //const csvData = convertArrayToCSV(productSales);
                          const csvData = convertDataToCSVFormat();

                          generateEmailReport(
                            EMAIL_REPORT_TYPE.CSV,
                            csvData,
                            'KooDoo Upselling Sales Report',
                            'KooDoo Upselling Sales Report.csv',
                            `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                            exportEmail,
                            'KooDoo Upselling Sales Report',
                            'KooDoo Upselling Sales Report',
                            () => {
                              CommonStore.update((s) => {
                                s.isLoading = false;
                              });

                              setIsCsv(false);

                              Alert.alert(
                                'Success',
                                'Report will be sent to the email address shortly',
                              );

                              setExportModalVisibility(false);
                            },
                          );
                        } else {
                          Alert.alert('Info', 'Invalid email address');
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_DL_RP_C_REP_CSV,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_DL_RP_C_REP_CSV
                        })
                      }}>
                      {isLoading && isCsv ? (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          CSV
                        </Text>
                      )}
                    </TouchableOpacity>

                    {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
                  </View>
                </View>
              </View>
            </View>
          </ModalView>

          <DateTimePickerModal
            isVisible={showDateTimePicker}
            mode={'date'}
            onConfirm={(text) => {
              // setRev_date(moment(text).startOf('day'));
              CommonStore.update(s => {
                s.historyStartDate = moment(text).startOf('day');
              });
              setShowDateTimePicker(false);
            }}
            onCancel={() => {
              setShowDateTimePicker(false);
            }}
            maximumDate={moment(historyEndDate).toDate()}
            date={moment(historyStartDate).toDate()}
          />

          <DateTimePickerModal
            isVisible={showDateTimePicker1}
            mode={'date'}
            onConfirm={(text) => {
              // setRev_date1(moment(text).endOf('day'));
              CommonStore.update(s => {
                s.historyEndDate = moment(text).endOf('day');
              });
              setShowDateTimePicker1(false);
            }}
            onCancel={() => {
              setShowDateTimePicker1(false);
            }}
            minimumDate={moment(historyStartDate).toDate()}
            date={moment(historyEndDate).toDate()}
          />

          <DateTimePickerModal
            isVisible={showDateTimePickerFilter}
            mode={'date'}
            onConfirm={(text) => {
              setSelectedChartFilterQueries([
                {
                  ...selectedChartFilterQueries[0],
                  fieldDataValue: text,
                },
              ]);

              setShowDateTimePickerFilter(false);
            }}
            onCancel={() => {
              setShowDateTimePickerFilter(false);
            }}
          />

          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{ flex: 1 }}
            visible={visible}
            transparent
            animationType="slide">
            <KeyboardAvoidingView
              behavior="padding"
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: windowHeight,
              }}>
              <View style={styles.confirmBox}>
                <Text
                  style={{
                    fontSize: 24,
                    justifyContent: 'center',
                    alignSelf: 'center',
                    marginTop: 40,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  Enter your email
                </Text>
                <View
                  style={{
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                    marginTop: 20,
                    flexDirection: 'row',
                    width: '80%',
                  }}>
                  <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                    <Text
                      style={{ color: Colors.descriptionColor, fontSize: 20 }}>
                      email:
                    </Text>
                  </View>
                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={[styles.textInput8, { paddingLeft: 5 }]}
                    placeholder="Enter your email"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    // style={{
                    //     // paddingLeft: 1,
                    // }}
                    //defaultValue={extentionCharges}
                    onChangeText={(text) => {
                      // setState({ exportEmail: text });
                      setExportEmail(text);
                    }}
                    value={exportEmail}
                  />
                </View>
                <Text
                  style={{
                    fontSize: 20,
                    fontFamily: 'NunitoSans-Bold',
                    marginTop: 25,
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                  }}>
                  Share As:
                </Text>

                {/* Share file using email */}
                <View
                  style={{
                    justifyContent: 'space-between',
                    alignSelf: 'center',
                    marginTop: 10,
                    flexDirection: 'row',
                    width: '80%',
                  }}>
                  <TouchableOpacity
                    style={[
                      styles.modalSaveButton1,
                      {
                        zIndex: -1,
                      },
                    ]}
                    onPress={() => { }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}>
                      Excel
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.modalSaveButton1,
                      {
                        zIndex: -1,
                      },
                    ]}
                    onPress={() => { }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}>
                      CSV
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.modalSaveButton1,
                      {
                        zIndex: -1,
                      },
                    ]}
                    onPress={() => { }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}>
                      PDF
                    </Text>
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    alignSelf: 'center',
                    marginTop: 20,
                    justifyContent: 'center',
                    alignItems: 'center',
                    // width: 260,
                    width: windowWidth * 0.2,
                    height: 60,
                    alignContent: 'center',
                    flexDirection: 'row',
                    marginTop: 40,
                  }}>
                  <TouchableOpacity
                    onPress={emailProductSales}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '100%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: 60,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={{
                        fontSize: 22,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      Email
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      setVisible(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '100%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: 60,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={{
                        fontSize: 22,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </KeyboardAvoidingView>
          </ModalView>

          <View
            style={[
              styles.content,
              {
                flexShrink: 1,
                flexGrow: 1,
              },
            ]}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={{ flex: 1 }}
              contentContainerStyle={{
                paddingBottom: '1%',
              }}>
              <View style={{ zindex: 2 }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    alignSelf: 'center',
                    //backgroundColor: '#ffffff',
                    justifyContent: 'space-between',
                    //padding: 18,
                    marginTop: 5,
                    width: windowWidth * 0.87,
                    paddingLeft: 1,
                    zindex: 2,
                  }}>
                  <View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Sales By {name}
                    </Text>

                    {/* <View
                      style={{
                        justifyContent: 'center',
                      }}>
                      <View
                        style={{ alignItems: 'center', flexDirection: 'row' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 20 : 26,
                          }}>
                          {!showDetails
                            ? productSales.length
                            : productSalesDetails.length}{' '}
                          Products
                        </Text>
                      </View>
                    </View> */}
                  </View>
                  <View
                    style={{
                      flexDirection: 'row', zindex: 2,
                    }}>
                    <View
                      style={[
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          marginRight: 10,
                          zIndex: 5,
                        },
                      ]}>
                      <DropDownPicker
                        containerStyle={{ marginLeft: 10, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        items={outletDropdownList}
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                        // placeholder="Choose Outlet"
                        placeholder={`${currOutlet && currOutlet.name ? currOutlet.name : 'Choose Outlet'}`}
                        onChangeItem={(item) => {
                          // setSelectedOutletList(items.map(item => item.value))
                          if (item !== undefined) {
                            CommonStore.update((s) => {
                              s.reportOutletIdList = item
                            })
                          }
                        }}
                        defaultValue={selectedOutletList}
                        dropDownMaxHeight={100}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 90 : 90,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                        multiple
                        multipleText="%d outlet(s) selected"
                      />
                    </View>
                    {/* <View
                      style={[
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          marginRight: 10,
                          zIndex: 5,
                        },
                      ]}>
                      <DropDownPicker
                        containerStyle={{ marginLeft: 10, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        items={[
                          // { label: 'All', value: 'ALL' },
                          { label: 'Merchant App Order', value: APP_TYPE.MERCHANT },
                          { label: 'Waiter App Order', value: APP_TYPE.WAITER },
                          { label: 'User App Order', value: APP_TYPE.USER },
                          { label: 'QR Order', value: APP_TYPE.WEB_ORDER },
                        ]}
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                        placeholder={'Filter App Type'}
                        onChangeItem={(item) => {
                          setFilterAppType(item);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_DD_APP_ORDER,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_DD_APP_ORDER
                          })

                        }}
                        defaultValue={filterAppType}
                        dropDownMaxHeight={100}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 90 : 90,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                        multiple
                        multipleText="%d App Type(s)"
                      />
                    </View> */}
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 140,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 10,
                      }}
                      onPress={() => {
                        setExportModalVisibility(true);

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_C_DOWNLOAD,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_DOWNLOAD
                        })
                      }}>
                      <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Icon
                          name="download"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          DOWNLOAD
                        </Text>
                      </View>
                    </TouchableOpacity>

                    {/* <View
                                    style={[{
                                        // flex: 1,
                                        // alignContent: 'flex-end',
                                        // marginBottom: 10,
                                        // flexDirection: 'row',
                                        // marginRight: '-40%',
                                        // marginLeft: 310,
                                        // backgroundColor: 'red',
                                        // alignItems: 'flex-end',
                                        // right: '-50%',
                                        // width: '23%',
                                        height: 40,
                                        //marginTop: 10,

                                    }, !isTablet() ? {
                                        marginLeft: 0,
                                    } : {}]}> */}

                    <View
                      style={{
                        width: switchMerchant ? 200 : 250,
                        height: switchMerchant ? 35 : 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        //marginRight: windowWidth * Styles.sideBarWidth,

                        //position: 'absolute',
                        //right: '17%',

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      }}>
                      <Icon
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{ marginLeft: 15 }}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: switchMerchant ? 180 : 250,
                          fontSize: switchMerchant ? 10 : 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        }}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        onChangeText={(text) => {
                          setSearch(text);
                          // setList1(false);
                          // setSearchList(true);
                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_TB_SEARCH,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_TB_SEARCH
                          })
                        }}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        value={search}
                      />
                    </View>

                    {/* </View> */}
                  </View>
                </View>
                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text> */}
                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 5,
                  marginHorizontal: 1,
                  zIndex: -1,
                  justifyContent: 'space-between',
                }}>
                <View style={{ flexDirection: 'row' }}>
                  {/* <TouchableOpacity
                                style={[leftSpacingScale, {
                                    marginRight: 10,
                                    paddingHorizontal: 15,
                                    backgroundColor: Colors.whiteColor,
                                    height: 40,
                                    // width: 200, 
                                    alignItems: 'center',
                                    borderRadius: 7,
                                    flexDirection: 'row',
                                    justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,

                                    opacity: !showDetails ? 0 : 100,
                                }]}
                                onPress={() => {
                                    setShowDetails(false);

                                    setCurrentPage(1);
                                    setPageCount(Math.ceil(productSales.length / perPage));
                                }}
                                disabled={!showDetails}
                            >
                                <AntDesign name="arrowleft" size={18} color={Colors.primaryColor} style={{
                                    top: 1,
                                    marginRight: -5,
                                }} />
                                <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Back to Summary</Text>
                            </TouchableOpacity> */}

                  {/* <View style={{ flexDirection: 'column', zIndex: 1 }}> */}
                  {/* <TouchableOpacity style={{
                                        justifyContent: 'center',
                                        width: 100,
                                    }}
                                        onPress={() => {
                                            filterPressed(1); setFilterTapped(1); setExpandSelection(!expandSelection);;
                                        }}>
                                        <View style={{
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 0,
                                            borderColor: '#4E9F7D',
                                            borderRadius: 8,
                                            alignItems: 'center',
                                            backgroundColor: '#FFFFFF',

                                            position: 'relative',

                                            paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40,
                                            width: 100,
                                            alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center',
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                        }}>
                                            <Feather name='filter' size={18} color='#27353C' />
                                            <Text style={{
                                                color: '#27353C',
                                                fontSize: 14,
                                                fontFamily: 'Nunitosans-Regular',
                                                marginLeft: 7
                                            }}>
                                                Filter
                                            </Text>
                                        </View>
                                    </TouchableOpacity> */}
                  {/* </View> */}

                  {/* <View style={{
                                    width: 250,
                                    height: 40,
                                    marginLeft: '35%',
                                    backgroundColor: 'white',
                                    borderRadius: 10,
                                    // marginLeft: '53%',
                                    flexDirection: 'row',
                                    alignContent: 'center',
                                    alignItems: 'center',

                                    //marginRight: windowWidth * Styles.sideBarWidth,

                                    position: 'absolute',
                                    //right: '17%',

                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                }}>
                                    <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{
                                            width: 250,
                                            fontSize: 15,
                                            fontFamily: 'NunitoSans-Regular',
                                        }}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setSearch(text);
                                            // setList1(false);
                                            // setSearchList(true);
                                        }}
                                        value={search}
                                    />
                                </View> */}
                </View>

                {/* <FlatList
                data={outletItems.filter(item => {
                  if (search !== '') {
                    return item.name.toLowerCase().includes(search.toLowerCase());
                  }
                  else {
                    return true;
                  }
                })}
                extraData={outletItems.filter(item => {
                  if (search !== '') {
                    return item.name.toLowerCase().includes(search.toLowerCase());
                  }
                  else {
                    return true;
                  }
                })}
                renderItem={renderSearchItem}
                keyExtractor={(item, index) => String(index)}
              /> */}
                <View style={{ flexDirection: 'row', zIndex: -1 }}>
                  {/* <TouchableOpacity style={{
                                justifyContent: 'center',
                                width: 100,
                                marginHorizontal: 20,
                                marginLeft: 0,
                            }}
                                onPress={() => {
                                    groupByPressed(1); setGroupByTapped(1); setExpandGroupBy(!expandGroupBy);
                                }}>
                                <View style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 0,
                                    borderColor: '#4E9F7D',
                                    borderRadius: 8,
                                    alignItems: 'center',
                                    backgroundColor: '#FFFFFF',
                                    position: 'relative',
                                    paddingHorizontal: 15, 
                                    backgroundColor: Colors.whiteColor, height: 40,
                                    width: 120,
                                    alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center',
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}>
                                    <Feather name='filter' size={18} color={Colors.primaryColor} />
                                    <Text style={{
                                        color: '#27353C',
                                        fontFamily: 'Nunitosans-Bold',
                                        fontSize: 13,
                                        marginLeft: Platform.OS === 'ios' ? 7 : 7
                                    }}>
                                        Group By
                                    </Text>
                                </View>
                            </TouchableOpacity> */}

                  {/* <TouchableOpacity style={{
                                justifyContent: 'center',
                                width: 100,
                                marginHorizontal: 10,
                            }}
                                onPress={() => {
                                    filterPressed(1); setFilterTapped(1); setExpandSelection(!expandSelection);
                                }}>
                                <View style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 0,
                                    borderColor: '#4E9F7D',
                                    borderRadius: 8,
                                    alignItems: 'center',
                                    backgroundColor: '#FFFFFF',

                                    position: 'relative',

                                    paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40,
                                    width: 100,
                                    alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center',
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}>
                                    <Feather name='filter' size={18} color={Colors.primaryColor} />
                                    <Text style={{
                                        color: '#27353C',
                                        fontFamily: 'Nunitosans-Bold',
                                        fontSize: 13,
                                        marginLeft: 7
                                    }}>
                                        Filter
                                    </Text>
                                </View>
                            </TouchableOpacity> */}

                  {/* <View style={[{
                                marginRight: 10,
                                marginLeft: 10,
                                // paddingLeft: 15,
                                width: 230, flexDirection: 'row',
                                alignItems: 'center', borderRadius: 10, height: 40, justifyContent: 'center',
                                backgroundColor: Colors.whiteColor,
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                            }]}
                            >
                                <View style={{ alignSelf: "center" }} onPress={() => { setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                    <EvilIcons name="calendar" size={25} color={Colors.primaryColor} />
                                </View>
                                <TouchableOpacity onPress={() => {
                                    setShowDateTimePicker(true);
                                    setShowDateTimePicker1(false);
                                }} style={{
                                    marginHorizontal: 4,
                                }}>
                                    <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, }}>{moment(rev_date).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>

                                <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', }}>-</Text>

                                <TouchableOpacity onPress={() => {
                                    setShowDateTimePicker(false);
                                    setShowDateTimePicker1(true);
                                }} style={{
                                    marginHorizontal: 4,
                                }}>
                                    <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, }}>{moment(rev_date1).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity>

                            </View> */}

                  {/* <TouchableOpacity
                                style={{
                                    paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}
                                onPress={() => {
                                    // setState({
                                    //     visible: true
                                    // })
                                    setVisible(true);
                                }}
                            >
                                <Upload width={15} height={15} />
                                <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, marginLeft: 12 }}>Email</Text>
                            </TouchableOpacity> */}

                  {/* {
                                ((currPageStack.length > 1 && currPageStack[currPageStack.length - 2] === 'Dashboard') || currPageStack[currPageStack.length - 1] === 'Dashboard')
                                    ?
                                    <TouchableOpacity
                                        style={{
                                            marginLeft: 10,
                                            paddingHorizontal: 10, backgroundColor: Colors.whiteColor, height: 40, width: 140, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                        }}
                                        onPress={() => {
                                            navigation.navigate('Dashboard');
                                        }}
                                    >
                                        <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, }}>Dashboard</Text>
                                    </TouchableOpacity>
                                    :
                                    <></>
                            } */}
                </View>

                {/* <View style={{ flex: 4 }}>
                            <TouchableOpacity>
                                <View style={{ width: '92%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{ width: '82%' }}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setState({
                                                search: text.trim(),
                                                list1: false,
                                                searchList: true,
                                            });
                                        }}
                                        value={search}
                                    //onSubmitEditing={searchBarItem()}
                                    />
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={{ flex: 6, flexDirection: 'row', justifyContent: 'flex-end' }}>
                            <View style={{ width: '40%' }}>
                                <TouchableOpacity style={{ width: '100%' }} onPress={() => { setState({ day: !day }) }}>
                                    <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <EvilIcons name='calendar' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                        <View style={{ justifyContent: 'center', flex: 2 }}>
                                            <Text style={{ color: Colors.descriptionColor, marginLeft: '1%', fontSize: 12 }}>{moment(startDate).format('DD MMM YYYY')} - {moment(endDate).format('DD MMM YYYY')} </Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                                <DateTimePickerModal
                                    isVisible={showDateTimePicker}
                                    mode={pickerMode}
                                    onConfirm={(text) => {
                                        if (pick == 1) {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ startDate: year + "-" + month + "-" + date })
                                        } else {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ endDate: year + "-" + month + "-" + date })
                                        }

                                        setState({ showDateTimePicker: false })
                                    }}
                                    onCancel={() => {
                                        setState({ showDateTimePicker: false })
                                    }}
                                />
                                {day ?
                                    <View style={{ position: 'absolute', width: "100%", backgroundColor: Colors.whiteColor, marginTop: '20%', zIndex: 6000 }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { moment() }}>
                                            <Text style={{ color: Colors.whiteColor }}>Today</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(1, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Yesterday</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(7, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 7 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(30, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 30 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).startOf("month")).format('YYYY-MM-DD'), endDate: moment(moment(new Date()).endOf("month")).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>This month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(moment(new Date()).startOf("month")).subtract(1, 'month')).format('YYYY-MM-DD'), endDate: moment(moment(moment(new Date()).endOf("month")).subtract(1, 'month')).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                            <Text style={{ color: "#828282" }}>Custom range</Text>
                                        </TouchableOpacity>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ flex: 1, marginLeft: 25 }}>
                                                <Text style={{ color: "#828282" }}>From</Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ color: "#828282" }}>To</Text>
                                            </View>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(startDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(endDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }) }}>
                                                <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }), getDetail() }}>
                                                <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ height: 20 }}>
                                        </View>
                                    </View>
                                    : null}
                            </View>
                            <TouchableOpacity style={{ width: '20%', marginLeft: '4%' }} onPress={() => { setState({ visible: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='download' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '5%', fontSize: 15 }}>Download</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            <View style={{ width: '4%' }}></View>
                            <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible1: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='upload' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%', flex: 1 }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 15 }}>Email</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </View> */}
              </View>

              <View style={{ zIndex: -1 }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.87,
                    minHeight: windowHeight * 0.01,
                    marginTop: 10,
                    marginHorizontal: 30,
                    marginBottom: 15,
                    alignSelf: 'center',
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}>
                  {/* <View style={{ width: '100%', flexDirection: 'row', marginVertical: 10, marginBottom: 20, alignItems: 'center' }}>
                            <View style={{ marginHorizontal: 5 }}>
                                <GCalendar width={22} height={22} />
                            </View>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.primaryColor, fontSize: 18, marginHorizontal: 10, alignSelf: 'flex-start' }}>Overall Sales</Text>
                        </View> */}
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      marginVertical: 20,
                      marginBottom: 0,
                      paddingRight: 20,
                      alignItems: 'center',
                      justifyContent: 'space-between',

                      zIndex: 3,
                    }}>
                    {/* <Text style={{
                                fontFamily: 'NunitoSans-Bold',
                                color: Colors.primaryColor, fontSize: 18, margin: 10, alignSelf: 'flex-start',
                                // backgroundColor: 'red',
                                //width: windowWidth * 0.3,
                            }}>Y-Axis</Text> */}
                    <View style={{ flexDirection: 'row', width: '20%', zindex: 3 }}>
                      <DropDownPicker
                        containerStyle={{ marginLeft: 20, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          zindex: 4,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        items={
                          CHART_Y_AXIS_DROPDOWN_LIST[
                          CHART_TYPE.REPORT_PRODUCT_SALES
                          ]
                        }
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 4 }}
                        placeholder={'Select'}
                        onChangeItem={(item) => {
                          setSelectedChartDropdownValue(item.value);
                        }}
                        defaultValue={selectedChartDropdownValue}
                        dropDownMaxHeight={150}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 100 : 120,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 4,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                      // arrowColor={'#BDBDBD'}
                      // arrowSize={23}
                      // arrowStyle={{
                      //     fontWeight: 'bold',
                      //     bottom: 1,
                      // }}
                      // style={{ width: Platform.OS === 'ios' ? 250 : 250, height: 45, marginLeft: 10 }}
                      // // items={[
                      // //     { label: 'Daily', value: 1 },
                      // //     { label: 'Weekly', value: 2 },
                      // //     { label: 'Monthly', value: 3 },
                      // //     //{ label: 'Hour of day', value: 4 },
                      // //     //{ label: 'Day of week', value: 5 }
                      // // ]}
                      // items={CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES]}
                      // // placeholder={'Total Sales (RM)'}
                      // // placeholderStyle={{ color: 'black' }}
                      // dropDownStyle={{
                      //     width: 250,
                      //     marginLeft: 10,
                      // }}
                      // itemStyle={{
                      //     justifyContent: 'flex-start',
                      // }}
                      // globalTextStyle={{
                      //     fontFamily: 'NunitoSans-SemiBold',
                      //     fontSize: 14,
                      //     color: Colors.fontDark,
                      //     marginLeft: 5,
                      // }}
                      // onChangeItem={item => {
                      //     // setState({ sort: selectedSort }),
                      //     //sortingFunc(selectedSort);
                      //     setSelectedChartDropdownValue(item.value);
                      // }}
                      // defaultValue={selectedChartDropdownValue}
                      />

                      <View
                        style={{
                          flexDirection: 'row',
                          marginLeft: switchMerchant ? 10 : 20,
                          //...(pageStack.length > 1 && pageStack[pageStack.length - 2] === 'Dashboard') && {
                          //    display: 'none',
                          // },
                        }}>
                        <View
                          style={{
                            paddingHorizontal: 15,
                            flexDirection: 'row',
                            alignItems: 'center',
                            borderRadius: 10,
                            paddingVertical: 10,
                            justifyContent: 'center',
                            backgroundColor: Colors.whiteColor,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: 1,
                          }}>
                          <View
                            style={{ alignSelf: 'center', marginRight: 5 }}
                            onPress={() => {
                              setState({
                                pickerMode: 'date',
                                showDateTimePicker: true,
                              });
                            }}>
                            <GCalendar
                              width={switchMerchant ? 15 : 20}
                              height={switchMerchant ? 15 : 20}
                            />
                          </View>

                          <TouchableOpacity
                            onPress={() => {
                              setShowDateTimePicker(true);
                              setShowDateTimePicker1(false);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_UP_C_CAL_START,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CAL_START
                              })
                            }}
                            style={{
                              marginHorizontal: 4,
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                                  : { fontFamily: 'NunitoSans-Regular' }
                              }>
                              {moment(historyStartDate).format('DD MMM yyyy')}
                            </Text>
                          </TouchableOpacity>

                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            -
                          </Text>

                          <TouchableOpacity
                            onPress={() => {
                              setShowDateTimePicker(false);
                              setShowDateTimePicker1(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_UP_C_CAL_END,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CAL_END
                              })
                            }}
                            style={{
                              marginHorizontal: 4,
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                                  : { fontFamily: 'NunitoSans-Regular' }
                              }>
                              {moment(historyEndDate).format('DD MMM yyyy')}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        zIndex: -1,
                        marginLeft: Platform.OS === 'ios' ? '5%' : 0,
                        //...(pageStack.length > 1 && pageStack[pageStack.length - 2] === 'Dashboard') && {
                        //   display: 'none',
                        //},
                      }}>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.TODAY
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.TODAY
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.TODAY);
                          // setRev_date(moment(Date.now()).startOf('day'));
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now()).startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_TODAY,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_TODAY
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.TODAY
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.TODAY
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          TODAY
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.THIS_WEEK
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.THIS_WEEK
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.THIS_WEEK);
                          // setRev_date(
                          //   moment(Date.now()).subtract(7, 'days').startOf('day'),
                          // );
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now()).subtract(7, 'days').startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_TW,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_TW
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_WEEK
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_WEEK
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          1W
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.THIS_MONTH
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.THIS_MONTH
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.THIS_MONTH);
                          // setRev_date(
                          //   moment(Date.now())
                          //     .subtract(31, 'days')
                          //     .startOf('day'),
                          // );
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now())
                              .subtract(31, 'days')
                              .startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_MONTH,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_MONTH
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_MONTH
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_MONTH
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          1M
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.THREE_MONTHS
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.THREE_MONTHS
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.THREE_MONTHS);
                          // setRev_date(
                          //   moment(Date.now())
                          //     .subtract(3, 'months')
                          //     .startOf('day'),
                          // );
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now())
                              .subtract(3, 'months')
                              .startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_3_MONTHS,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_3_MONTHS
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod ==
                                    CHART_PERIOD.THREE_MONTHS
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod ==
                                    CHART_PERIOD.THREE_MONTHS
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          3M
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.SIX_MONTHS
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.SIX_MONTHS
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.SIX_MONTHS);
                          // setRev_date(
                          //   moment(Date.now())
                          //     .subtract(6, 'month')
                          //     .startOf('day'),
                          // );
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now())
                              .subtract(6, 'month')
                              .startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_6_MONTHS,
                            eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_6_MONTHS
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.SIX_MONTHS
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.SIX_MONTHS
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          6M
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.THIS_YEAR
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.THIS_YEAR
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.THIS_YEAR);
                          // setRev_date(moment().startOf('year').startOf('day'));
                          // setRev_date1(moment().endOf('year').endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment().startOf('year').startOf('day');
                            s.historyEndDate = moment().endOf('year').endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_1_YEAR,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_1_YEAR
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_YEAR
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.THIS_YEAR
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          1Y
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 10,
                          borderBottomWidth:
                            salesBarChartPeriod == CHART_PERIOD.YTD
                              ? 2
                              : StyleSheet.hairlineWidth,
                          borderBottomColor:
                            salesBarChartPeriod == CHART_PERIOD.YTD
                              ? Colors.primaryColor
                              : Colors.lightPrimary,
                        }}
                        onPress={() => {
                          setSalesBarChartPeriod(CHART_PERIOD.YTD);
                          // setRev_date(
                          //   moment(Date.now())
                          //     .subtract(12, 'months')
                          //     .startOf('day'),
                          // );
                          // setRev_date1(moment(Date.now()).endOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(Date.now())
                              .subtract(12, 'months')
                              .startOf('day');
                            s.historyEndDate = moment(Date.now()).endOf('day');
                          });

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_UP_C_CHART_YESTERDAY,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_CHART_YESTERDAY
                          })
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.YTD
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                                fontSize: 10,
                              }
                              : {
                                fontFamily: 'NunitoSans-Bold',
                                color:
                                  salesBarChartPeriod == CHART_PERIOD.YTD
                                    ? Colors.blackColor
                                    : Colors.descriptionColor,
                              }
                          }>
                          YTD
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                  {/* new feature product filter dropdown */}

                  <View>
                    {isChartLoading && (
                      <View style={{
                        flex: 1,
                        backgroundColor: 'rgba(255,255,255,0.8)', 
                        position: 'absolute', 
                        width: '100%', 
                        height: '100%',
                        justifyContent: 'center', 
                        alignItems: 'center',
                        zIndex: 999,
                        pointerEvents: 'auto'
                      }}>
                        <ActivityIndicator size="large" color={Colors.primaryColor} />
                      </View>
                    )}

                    <View style={{
                      width: '100%',
                      flexDirection: 'row',
                      marginVertical: 20,
                      marginBottom: 0,
                      paddingRight: 20,
                      alignItems: 'center',
                      zindex: 2
                    }}>
                      {/* product */}
                      {
                        (outletItemsDropdownList.length > 0 &&
                          selectedFilterProductId !== '' &&
                          outletItemsDropdownList.find(option => option.value === selectedFilterProductId)
                        )
                          ?
                          <DropDownPicker
                            containerStyle={{ marginLeft: 20, height: 40, zindex: 2 }}
                            arrowColor={'black'}
                            arrowSize={switchMerchant ? 10 : 20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            style={{
                              width: switchMerchant ? 140 : 300,
                              paddingVertical: 0,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              zindex: 2,
                            }}
                            placeholderStyle={{ color: Colors.fieldtTxtColor }}
                            defaultValue={selectedFilterProductId}
                            items={outletItemsDropdownList}
                            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                            placeholder={'Product'}
                            onChangeItem={(item) => {
                              // setSelectedChartDropdownValue(item.value);
                              // setSelectedFilterProductList(item)
                              setSelectedFilterProductId(item.value);
                            }}
                            // defaultValue={selectedChartDropdownValue}
                            dropDownMaxHeight={150}
                            dropDownStyle={{
                              width: switchMerchant ? 140 : 300,
                              height: switchMerchant ? 100 : 120,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              borderWidth: 1,
                              textAlign: 'left',
                              zIndex: 2,
                            }}
                            globalTextStyle={{
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: switchMerchant ? 10 : 14,
                              color: Colors.fontDark,
                              marginLeft: 5,
                            }}
                            // multiple={true}
                            // multipleText="%d product(s)"
                            searchable
                          />
                          :
                          <></>
                      }

                      {
                        (outletItemsUpsellingDropdownList.length > 0 &&
                          selectedFilterProductIdUpselling !== '' &&
                          outletItemsUpsellingDropdownList.find(option => option.value === selectedFilterProductIdUpselling)
                        )
                          ?
                          <DropDownPicker
                            containerStyle={{ marginLeft: 20, height: 40, zindex: 2 }}
                            arrowColor={'black'}
                            arrowSize={switchMerchant ? 10 : 20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            style={{
                              width: switchMerchant ? 140 : 300,
                              paddingVertical: 0,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              zindex: 2,
                            }}
                            placeholderStyle={{ color: Colors.fieldtTxtColor }}
                            defaultValue={selectedFilterProductIdUpselling}
                            items={outletItemsUpsellingDropdownList}
                            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                            placeholder={'Upsell Product'}
                            onChangeItem={(item) => {
                              // setSelectedChartDropdownValue(item.value);
                              // setSelectedFilterProductList(item)
                              setSelectedFilterProductIdUpselling(item.value);
                            }}
                            // defaultValue={selectedChartDropdownValue}
                            dropDownMaxHeight={150}
                            dropDownStyle={{
                              width: switchMerchant ? 140 : 300,
                              height: switchMerchant ? 100 : 120,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              borderWidth: 1,
                              textAlign: 'left',
                              zIndex: 2,
                            }}
                            globalTextStyle={{
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: switchMerchant ? 10 : 14,
                              color: Colors.fontDark,
                              marginLeft: 5,
                            }}
                            // multiple={true}
                            // multipleText="%d product(s)"
                            searchable
                          />
                          :
                          <></>
                      }

                      {/* channel */}
                      <DropDownPicker
                        containerStyle={{ marginLeft: 20, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.gold,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        // items={[
                        //   { label: 'Dine In', value: 'DINEIN' },
                        //   { label: 'Delivery', value: 'DELIVERY' },
                        //   { label: 'Pick Up', value: 'Takeaway' },
                        // ]}
                        items={ORDER_TYPE_DROP_DOWN_LIST}
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                        placeholder={'Filter Channel'}
                        onChangeItem={(items) => {
                          // setSelectedChartDropdownValue(item.value);
                          setSelectedFilterChannelList(items);
                        }}
                        defaultValue={selectedFilterChannelList}
                        dropDownMaxHeight={150}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 100 : 120,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                        searchable
                        multiple
                        multipleText="%d channel(s)"
                      />
                    </View>

                    <View
                      style={{
                        alignSelf: 'flex-start',
                        marginLeft: '3%',
                        marginBottom: '1%',
                      }}>
                      {/* <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.descriptionColor, fontSize: 16 }}>
                                  RM
                              </Text> */}
                      <Text />
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        zIndex: -1,
                      }}>
                      <View
                        style={{
                          // backgroundColor: 'red',
                          zIndex: -1,
                          paddingLeft: 5,
                        }}>
                        <FusionCharts
                          type={CHART_DATA[CHART_TYPE.REPORT_UPSELL_SALES].type}
                          width={windowWidth * (0.83 - Styles.sideBarWidth)}
                          height={
                            switchMerchant ? windowHeight * 1.1 : windowHeight * 0.6
                          }
                          dataFormat={productSalesChart.dataFormat}
                          dataSource={productSalesChart.dataSource}
                          libraryPath={FS_LIBRARY_PATH}
                          events={eventsChart}
                        />
                      </View>

                      {/* <View
                        style={{
                          width: windowWidth * (0.15 - Styles.sideBarWidth),
                          marginTop: switchMerchant
                            ? windowHeight * 0.75
                            : Platform.OS === 'ios'
                              ? windowHeight * 0.4
                              : windowHeight * 0.45,
                          // height: 45,
                        }}>
                        <DropDownPicker
                          arrowColor={'black'}
                          arrowSize={switchMerchant ? 10 : 20}
                          arrowStyle={{ fontWeight: 'bold' }}
                          style={{
                            width:
                              Platform.OS === 'ios'
                                ? windowWidth * (0.17 - Styles.sideBarWidth)
                                : windowWidth * (0.16 - Styles.sideBarWidth),
                            marginLeft: Platform.OS === 'ios' ? 0 : 10,
                            borderRadius: 10,
                            backgroundColor: Colors.fieldtBgColor,
                          }}
                          placeholderStyle={{ color: Colors.fieldtTxtColor }}
                          items={
                            CHART_X_AXIS_DROPDOWN_LIST[
                            CHART_TYPE.REPORT_PRODUCT_SALES
                            ]
                          }
                          itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                          placeholder={'Select'}
                          onChangeItem={(item) => {
                            setSelectedChartDropdownValueX(item.value);
                          }}
                          defaultValue={selectedChartDropdownValueX}
                          dropDownMaxHeight={150}
                          dropDownStyle={{
                            width:
                              Platform.OS === 'ios'
                                ? windowWidth * (0.17 - Styles.sideBarWidth)
                                : windowWidth * (0.16 - Styles.sideBarWidth),
                            marginLeft: Platform.OS === 'ios' ? 0 : 10,
                            backgroundColor: Colors.fieldtBgColor,
                            borderRadius: 10,
                            borderWidth: 1,
                          }}
                          globalTextStyle={{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: Colors.fontDark,
                            marginLeft: 5,
                          }}
                        />
                      </View> */}
                    </View>
                  </View>
                </View>
              </View>

              {showDetails ? (
                <TouchableOpacity
                  style={{
                    marginLeft: switchMerchant
                      ? 0
                      : windowWidth <= 1823 && windowWidth >= 1820
                        ? 24
                        : 10,
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    width: switchMerchant ? 90 : 120,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    opacity: !showDetails ? 0 : 100,
                  }}
                  onPress={() => {
                    setShowDetails(false);
                    setCurrentPage(pageReturn);
                    // console.log('Returning to page');
                    // console.log(pageReturn);
                    setPageCount(Math.ceil((productSalesUpselling.length + productSales.length) / perPage));
                    setCurrReportSummarySort('');
                    setCurrReportDetailsSort('');
                  }}
                  disabled={!showDetails}>
                  <AntDesign
                    name="arrowleft"
                    size={switchMerchant ? 10 : 20}
                    color={Colors.whiteColor}
                    style={
                      {
                        // top: -1,
                        //marginRight: -5,
                      }
                    }
                  />
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                      marginBottom: Platform.OS === 'ios' ? 0 : 2,
                    }}>
                    Summary
                  </Text>
                </TouchableOpacity>
              ) : (
                <></>
              )}

              <View style={{ width: '100%', marginTop: 20 }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.87,
                    height:
                      Platform.OS == 'android'
                        ? windowHeight * 0.6
                        : windowHeight * 0.66,
                    //marginTop: 5,
                    marginHorizontal: 30,
                    marginBottom: 10,
                    alignSelf: 'center',
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}>
                  {/* Left Button */}
                  {/* <View style={{ flexDirection: "row"}}>
                                    <View style={{ flexDirection: 'row', zIndex: 1 }}>
                                        <TouchableOpacity
                                                    style={ {
                                                        marginRight: 10,
                                                        paddingHorizontal: 15,
                                                        backgroundColor: Colors.whiteColor,
                                                        height: 40,
                                                        width: 200, 
                                                        alignItems: 'center',
                                                        borderRadius: 7,
                                                        flexDirection: 'row',
                                                        justifyContent: 'center', shadowColor: '#000',
                                                        shadowOffset: {
                                                            width: 0,
                                                            height: 2,
                                                        },
                                                        shadowOpacity: 0.22,
                                                        shadowRadius: 3.22,
                                                        elevation: 1,

                                                        opacity: !showDetails ? 0 : 100,
                                                    }}
                                                    onPress={() => {
                                                        setShowDetails(false);

                                                        setCurrentPage(1);
                                                        setPageCount(Math.ceil(productSales.length / perPage));
                                                    }}
                                                    disabled={!showDetails}
                                                >
                                                    <AntDesign name="arrowleft" size={18} color={Colors.primaryColor} style={{
                                                        top: 1,
                                                        marginRight: -5,
                                                    }} />
                                                    <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Back to Summary</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View> */}

                  {/* Right Button */}
                  {showDetails ? (
                    <View style={{ flexDirection: 'row' }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          //justifyContent: 'flex-end',
                          // marginBottom: 5,
                          // marginTop: 5,
                          width: '100%',
                          width: windowWidth * 0.875,
                          alignSelf: 'flex-end',
                        }}>
                        <Text
                          style={{
                            marginLeft: 20,
                            marginTop: 10,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 14 : 22,
                            alignSelf: 'center',
                          }}>
                          {detailsTitle}
                        </Text>
                      </View>
                      {/* <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>{!showDetails ? productName : productName } </Text> */}
                    </View>
                  ) : null}
                  {/* <View style={{ height: '88%', position: 'absolute', justifyContent: 'space-between', zIndex: 10, marginVertical: 0, marginTop: 80, alignSelf: 'center' }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToTop();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '8%', zIndex: 10 }}>
                                    <AntDesign name={'upcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToBottom();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '42%', zIndex: 10 }}>
                                    <AntDesign name={'downcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                </TouchableOpacity>
                            </View> */}

                  {/* /////////////////////////////////////// */}

                  {/* 2022-02-14 - Hide flatlist first */}

                  {!showDetails ? (
                    <View style={{ marginTop: 10, flexDirection: 'row' }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '5%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <View style={{ flexDirection: 'row' }}>
                          <View style={{ flexDirection: 'column' }}>
                            <Text
                              numberOfLines={2}
                              style={{
                                fontSize: switchMerchant ? 10 : 13,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {'No.\n'}
                            </Text>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 8 : 10,
                                color: Colors.descriptionColor,
                              }} />
                          </View>
                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color="transparent" />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color="transparent" />
                            <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }} />
                          </View>

                          {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                        </View>
                        {/* <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View> */}
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '19%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_PROD,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_PROD
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Product\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '11%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_CAT,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_CAT
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Category\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_SKU,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_SKU
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'SKU\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_ITEMS,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_ITEMS
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nItems'}</Text> */}
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Items\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                Qty
                              </Text>
                              {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales'}</Text> */}
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Sales\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View
                              style={{
                                marginLeft: '3%',
                                justifyContent: 'space-between',
                              }}>
                              <View>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                              </View>
                              {/* <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }}></Text> */}
                              <Tooltip
                                isVisible={saleTip}
                                content={
                                  <Text>
                                    Product Amount - Discount + Tax + Service
                                    Charge
                                  </Text>
                                }
                                placement="top"
                                onClose={() => setSaleTip(false)}>
                                <TouchableOpacity
                                  onPress={() => {

                                    setSaleTip(true);


                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_ITEMS_C_ICON_QM,
                                      eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_ITEMS_C_ICON_QM
                                    })


                                  }}
                                  style={styles.touchable}>
                                  <Feather
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 15}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      {/* <View style={{ flexDirection: 'row', flex: 1.6, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales Return'}</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View>
                                    </View> */}
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '8%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_DISC_RM,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_DISC_RM
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nDiscount'}</Text> */}
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Disc\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '8%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_DISC_PERC,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_DISC_PERC
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Disc\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                %
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '11%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_PAY_TYPE,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_PAY_TYPE
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'Payment\nType'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View
                              style={{
                                marginLeft: '3%',
                                left: Platform.OS === 'ios' ? 0 : '-10%',
                              }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      {/* <View style={{ flexDirection: 'row', flex: 1.6, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>

                                        <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View>
                                    </View> */}
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '11%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.NET_SALES_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.NET_SALES_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES,
                              eventNameAnalytics: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Net Sales\n'}
                              </Text>
                              <View style={{ flexDirection: 'row' }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.primaryColor,
                                  }}>
                                  {' '}
                                  *incl tax
                                </Text>
                              </View>
                            </View>
                            <View
                              style={{
                                marginLeft: '3%',
                                justifyContent: 'space-between',
                              }}>
                              <View>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.NET_SALES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                              </View>
                              {/* <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }}></Text> */}
                              <Tooltip
                                isVisible={netSaleTip}
                                content={
                                  <Text>
                                    Total Sales - Tax - Service Charge
                                  </Text>
                                }
                                placement="top"
                                onClose={() => setNetSaleTip(false)}>
                                <TouchableOpacity
                                  onPress={() => {
                                    setNetSaleTip(true);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_NS_C_ICON_QM,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_NS_C_ICON_QM
                                    })

                                  }}
                                  style={styles.touchable}>
                                  <Feather
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 15}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <View style={{ marginTop: 10, flexDirection: 'row' }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '6%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <View style={{ flexDirection: 'row' }}>
                          <View style={{ flexDirection: 'column' }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'No.\n'}
                              </Text>
                              {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                            </View>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 8 : 10,
                                color: Colors.descriptionColor,
                              }} />
                          </View>
                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color="transparent" />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color="transparent" />
                            <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }} />
                          </View>
                        </View>
                        {/* <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View> */}
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '14%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_O_ID,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_O_ID
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Order ID\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '18%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC,
                              );
                            }

                            logEventAnalytics({

                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_TD,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_TD
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Transaction Date\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '10%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_S,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_S
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Sales\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View
                              style={{
                                marginLeft: '3%',
                                justifyContent: 'space-between',
                              }}>
                              <View>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                              </View>
                              {/* <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }}></Text> */}
                              <Tooltip
                                isVisible={saleTip}
                                content={
                                  <Text>
                                    Product Amount - Discount + Tax + Service
                                    Charge
                                  </Text>
                                }
                                placement="top"
                                onClose={() => setSaleTip(false)}>
                                <TouchableOpacity
                                  onPress={() => {

                                    setSaleTip(true);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_S_C_I_QM,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_S_C_I_QM
                                    })

                                  }}
                                  style={styles.touchable}>
                                  <Feather
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 15}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '7%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_DISC,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_DISC
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>{'Total\nDiscount'}</Text> */}
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Disc\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      {/* <View
                        style={{
                          flexDirection: 'row',
                          width: '7%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                              );
                            }
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Disc\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                %
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}
                      {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View>
                                    </View> */}

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '8%',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_T,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_T
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Tax\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          borderRightWidth: 1,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SC,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SC
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Service\nCharge'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>

                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          // borderRightWidth: 1,
                          // borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SR,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SR
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Sales\nReturn'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportDetailsSort ===
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                } />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View>
                                    </View> */}
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '14%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                          borderLeftWidth: 1,
                          borderLeftColor: 'lightgrey',
                          left: 3,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportDetailsSort ===
                              REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                            ) {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC,
                              );
                            } else {
                              setCurrReportDetailsSort(
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_NS,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_NS
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Net Sales\n'}
                              </Text>
                              <View style={{ flexDirection: 'row' }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.primaryColor,
                                  }}>
                                  {' '}
                                  *incl tax
                                </Text>
                              </View>
                            </View>
                            <View
                              style={{
                                marginLeft: '3%',
                                justifyContent: 'space-between',
                              }}>
                              <View>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                              </View>
                              {/* <Text
                              style={{
                                fontSize: 10,
                                color: Colors.descriptionColor,
                              }}></Text> */}
                              <Tooltip
                                isVisible={netSaleTip}
                                content={
                                  <Text>
                                    Total Sales - Tax - Service Charge
                                  </Text>
                                }
                                placement="top"
                                onClose={() => setNetSaleTip(false)}>
                                <TouchableOpacity
                                  onPress={() => {

                                    setNetSaleTip(true);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_NS_C_I_QM,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_TABLE_IND_SUM_NS_C_I_QM
                                    })

                                  }}
                                  style={styles.touchable}>
                                  <Feather
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 15}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}

                {isDataLoading && (
                  <View style={{
                    flex: 1,
                    backgroundColor: 'rgba(255,255,255,0.8)', 
                    position: 'absolute', 
                    width: '100%', 
                    height: '100%',
                    justifyContent: 'center', 
                    alignItems: 'center',
                    zIndex: 999,
                    pointerEvents: 'auto'
                  }}>
                    <ActivityIndicator size="large" color={Colors.primaryColor} />
                  </View>
                )}

                  {!showDetails ? (
                    <>
                      {(productSalesUpselling.concat(productSales)).length > 0 ? (
                        <FlatList
                          showsVerticalScrollIndicator={false}
                          ref={flatListRef}
                          data={sortReportDataList(
                            (productSalesUpselling.concat(productSales)).filter((item) => {
                              if (search !== '') {
                                if (
                                  item.productName
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.productCategory
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.productSku
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.totalItems
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.totalSales
                                    .toFixed(2)
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.totalDiscount
                                    .toFixed(2)
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else {
                                  return false;
                                }
                              } else {
                                return true;
                              }
                            }),
                            currReportSummarySort,
                          ).slice(
                            (currentPage - 1) * perPage,
                            currentPage * perPage,
                          )}
                          // extraData={productSales}
                          renderItem={renderItem}
                          keyExtractor={(item, index) => String(index)}
                          style={{ marginTop: 10 }}
                          initialNumToRender={8}
                        />
                      ) : (
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '71%',
                          }}>
                          <Text style={{ color: Colors.descriptionColor }}>
                            - No Data Available -
                          </Text>
                        </View>
                      )}
                    </>
                  ) : (
                    <>
                      {productSalesDetails.length > 0 ? (
                        <FlatList
                          showsVerticalScrollIndicator={false}
                          ref={flatListRef}
                          data={sortReportDataList(
                            productSalesDetails.filter((item) => {
                              if (search !== '') {
                                if (
                                  item.orderId
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.totalPrice
                                    .toFixed(2)
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  getOrderDiscountInfoInclOrderBased(item)
                                    .toFixed(2)
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.tax
                                    .toFixed(2)
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else if (
                                  item.createdAt
                                    .toString()
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                                ) {
                                  return true;
                                } else {
                                  return false;
                                }
                              } else {
                                return true;
                              }
                            }),
                            currReportDetailsSort,
                          ).slice(
                            (currentDetailsPage - 1) * perPage,
                            currentDetailsPage * perPage,
                          )}
                          // extraData={transactionTypeSales}
                          renderItem={renderItemDetails}
                          keyExtractor={(item, index) => String(index)}
                          style={{ marginTop: 10 }}
                        />
                      ) : (
                        <View style={{ height: windowHeight * 0.4 }}>
                          <View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '100%',
                            }}>
                            <Text style={{ color: Colors.descriptionColor }}>
                              - No Data Available -
                            </Text>
                          </View>
                        </View>
                      )}
                    </>
                  )}
                </View>

                {!showDetails ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      marginTop: 10,
                      width: windowWidth * 0.87,
                      alignItems: 'center',
                      alignSelf: 'center',
                      justifyContent: 'flex-end',
                      top:
                        Platform.OS == 'ios'
                          ? pushPagingToTop && keyboardHeight > 0
                            ? -keyboardHeight * 0.9
                            : 0
                          : 0,
                      // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                      // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                      // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                      // borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                      paddingHorizontal:
                        pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                      // shadowOffset: {
                      //     width: 0,
                      //     height: 1,
                      // },
                      // shadowOpacity: 0.22,
                      // shadowRadius: 3.22,
                      // elevation: 1,
                      paddingBottom: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: '1%',
                      }}>
                      Items Showed
                    </Text>
                    <View
                      style={{
                        width: Platform.OS === 'ios' ? 65 : '13%', //65,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                        //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                        // paddingTop: '-60%',
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        marginRight: '1%',
                      }}>
                      <RNPickerSelect
                        placeholder={{}}
                        useNativeAndroidPickerStyle={false}
                        style={{
                          inputIOS: {
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                          },
                          inputAndroid: {
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            justifyContent: 'center',
                            textAlign: 'center',
                            height: 40,
                            color: 'black',
                          },
                          inputAndroidContainer: { width: '100%' },
                          //backgroundColor: 'red',
                          height: 35,

                          chevronContainer: {
                            display: 'none',
                          },
                          chevronDown: {
                            display: 'none',
                          },
                          chevronUp: {
                            display: 'none',
                          },
                        }}
                        items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                          label: 'All',
                          value: !showDetails
                            ? (productSalesUpselling.length + productSales.length)
                            : productSalesDetails.length,
                        })}
                        value={perPage}
                        onValueChange={(value) => {
                          setPerPage(value);
                        }}
                      />
                    </View>

                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: '1%',
                      }}>
                      Page
                    </Text>
                    <View
                      style={{
                        width: switchMerchant ? 65 : 70,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: 22,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        backgroundColor: Colors.highlightColor
                      }}>
                      {console.log('currentPage')}
                      {console.log(currentPage)}

                      <TextInput
                        onChangeText={(text) => {
                          var currentPageTemp =
                            text.length > 0 ? parseInt(text) : 1;

                          setCurrentPage(
                            currentPageTemp > pageCount
                              ? pageCount
                              : currentPageTemp < 1
                                ? 1
                                : currentPageTemp,
                          );

                        }}
                        placeholder={currentPage.toString()}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          color: 'black',
                          // fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          marginTop: Platform.OS === 'ios' ? 0 : -15,
                          marginBottom: Platform.OS === 'ios' ? 0 : -15,
                          textAlign: 'center',
                          width: '100%',
                        }}
                        value={currentPage.toString()}
                        defaultValue={currentPage.toString()}
                        keyboardType={'numeric'}
                        onFocus={() => {
                          setPushPagingToTop(true);
                        }}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: '1%',
                        marginRight: '1%',
                      }}>
                      of {pageCount}
                    </Text>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        prevPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_C_PREV_PAGE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_PREV_PAGE
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-left"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        nextPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_C_NEXT_PAGE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_C_NEXT_PAGE
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View
                    style={{
                      flexDirection: 'row',
                      marginTop: 10,
                      width: windowWidth * 0.87,
                      alignItems: 'center',
                      alignSelf: 'center',
                      justifyContent: 'flex-end',
                      top:
                        Platform.OS == 'ios'
                          ? pushPagingToTop && keyboardHeight > 0
                            ? -keyboardHeight * 0.9
                            : 0
                          : 0,
                      // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                      // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                      // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                      // borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                      paddingHorizontal:
                        pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                      // shadowOffset: {
                      //     width: 0,
                      //     height: 1,
                      // },
                      // shadowOpacity: 0.22,
                      // shadowRadius: 3.22,
                      // elevation: 1,
                      paddingBottom: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: '1%',
                      }}>
                      Items Showed
                    </Text>
                    <View
                      style={{
                        width: Platform.OS === 'ios' ? 65 : '13%', //65,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                        //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                        // paddingTop: '-60%',
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        marginRight: '1%',
                      }}>
                      <RNPickerSelect
                        placeholder={{}}
                        useNativeAndroidPickerStyle={false}
                        style={{
                          inputIOS: {
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                          },
                          inputAndroid: {
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            justifyContent: 'center',
                            textAlign: 'center',
                            height: 40,
                            color: 'black',
                          },
                          inputAndroidContainer: { width: '100%' },
                          // backgroundColor: 'red',
                          height: 35,

                          chevronContainer: {
                            display: 'none',
                          },
                          chevronDown: {
                            display: 'none',
                          },
                          chevronUp: {
                            display: 'none',
                          },
                        }}
                        items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                          label: 'All',
                          value: !showDetails
                            ? productSales.length
                            : productSalesDetails.length,
                        })}
                        value={perPage}
                        onValueChange={(value) => {
                          setPerPage(value);
                        }}
                      />
                    </View>

                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: '1%',
                      }}>
                      Page
                    </Text>
                    <View
                      style={{
                        width: switchMerchant ? 65 : 70,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: 22,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      }}>
                      {console.log('currentDetailsPage')}
                      {console.log(currentDetailsPage)}

                      <TextInput
                        onChangeText={(text) => {
                          var currentPageTemp =
                            text.length > 0 ? parseInt(text) : 1;

                          setCurrentDetailsPage(
                            currentPageTemp > detailsPageCount
                              ? detailsPageCount
                              : currentPageTemp < 1
                                ? 1
                                : currentPageTemp,
                          );
                        }}
                        placeholder={currentDetailsPage.toString()}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          color: 'black',
                          // fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          marginTop: Platform.OS === 'ios' ? 0 : -15,
                          marginBottom: Platform.OS === 'ios' ? 0 : -15,
                          textAlign: 'center',
                          width: '100%',
                        }}
                        value={currentDetailsPage.toString()}
                        defaultValue={currentDetailsPage.toString()}
                        keyboardType={'numeric'}
                        onFocus={() => {
                          setPushPagingToTop(true);
                        }}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: '1%',
                        marginRight: '1%',
                      }}>
                      of {detailsPageCount}
                    </Text>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        prevDetailsPage();
                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_SUM_IND_C_PREV_PAGE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_IND_C_PREV_PAGE
                        })

                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-left"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        nextDetailsPage();
                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_UP_SUM_IND_C_NEXT_PAGE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UP_SUM_IND_C_NEXT_PAGE
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                  </View>
                )}

                {/* /////////////////////////////////////// */}

                {/* </View> */}
              </View>
            </ScrollView>
            {expandGroupBy ? (
              <View style={styles.ManageFilterBox1}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    margin: 10,
                  }}>
                  <Text
                    style={{
                      fontSize: 13,
                      color: '#27353C',
                      fontFamily: 'Nunitosans-Bold',
                    }}>
                    Group By
                  </Text>
                </View>
                <View
                  style={{
                    borderWidth: 0.5,
                    borderColor: '#D2D2D2',
                    width: '100%',
                    marginTop: 5,
                    marginBottom: 5,
                  }} />
                <View
                  style={{
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    alignItems: 'center',
                    margin: 10,
                    height: 45,
                    elevation: 1,
                    zIndex: 1,
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      Colors: '#27353C',
                      fontFamily: 'Nunitosans-bold',
                    }}>
                    Select Group:{' '}
                  </Text>
                  <DropDownPicker
                    arrowColor={'#BDBDBD'}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    style={{
                      width: 300,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    //items={CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES]}
                    dropDownStyle={{
                      width: 300,
                    }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    globalTextStyle={{
                      fontFamily: 'NunitoSans-SemiBold',
                      color: Colors.fontDark,
                      marginLeft: 5,
                    }}
                    items={[
                      { label: 'Product Name', value: 1 },
                      { label: 'Category', value: 2 },
                      { label: 'Tag', value: 3 },
                      { label: 'Sku', value: 4 },
                    ]}
                    placeholder={'select a group'}
                    //onChangeItem={item => {
                    //    setSelectedChartFilterQueries([
                    //        {
                    //            ...selectedChartFilterQueries[0],
                    //            fieldNameKey: item.value,
                    //            fieldNameType: item.fieldType,
                    //        },
                    //    ]);
                    //}}
                    //defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                    listMode={'SCROLLVIEW'}
                    scrollViewProps={{
                      nestedScrollEnabled: true,
                    }}
                  />
                </View>
                <View
                  style={{
                    borderWidth: 0.5,
                    borderColor: '#D2D2D2',
                    width: '100%',
                    marginTop: 5,
                    marginBottom: 5,
                  }} />
                <View
                  style={{
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    alignItems: 'center',
                    margin: 10,
                    height: 45,
                    elevation: 1,
                    zIndex: -1,
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      Colors: '#27353C',
                      fontFamily: 'Nunitosans-bold',
                    }}>
                    Select SubItems:{' '}
                  </Text>
                  <DropDownPicker
                    arrowColor={'#BDBDBD'}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    style={{
                      width: 300,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    //items={CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES]}
                    dropDownStyle={{
                      width: 300,
                    }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    globalTextStyle={{
                      fontFamily: 'NunitoSans-SemiBold',
                      color: Colors.fontDark,
                      marginLeft: 5,
                    }}
                    items={[
                      { label: 'Product Name', value: 1 },
                      { label: 'Category', value: 2 },
                      { label: 'Tag', value: 3 },
                      { label: 'Sku', value: 4 },
                    ]}
                    placeholder={'select a group'}
                    //onChangeItem={item => {
                    //    setSelectedChartFilterQueries([
                    //        {
                    //            ...selectedChartFilterQueries[0],
                    //            fieldNameKey: item.value,
                    //            fieldNameType: item.fieldType,
                    //        },
                    //    ]);
                    //}}
                    //defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                    listMode={'SCROLLVIEW'}
                    scrollViewProps={{
                      nestedScrollEnabled: true,
                    }}
                  />
                </View>
              </View>
            ) : null}

            {expandSelection ? (
              <View style={styles.ManageFilterBox}>
                {/* <ScrollViewGH showsVerticalScrollIndicator={false}> */}
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    margin: 10,
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      Colors: '#27353C',
                      fontFamily: 'Nunitosans-bold',
                    }}>
                    Manage Filter
                  </Text>
                </View>
                <View
                  style={{
                    borderWidth: 0.5,
                    borderColor: '#D2D2D2',
                    width: '100%',
                    marginTop: 5,
                    marginBottom: 5,
                  }} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    margin: 10,
                    height: 35,
                    elevation: 1,
                    zIndex: 1,
                  }}>
                  <DropDownPicker
                    arrowColor={'#BDBDBD'}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    style={{ width: 250, height: 35 }}
                    // items={[
                    //     { label: 'Daily', value: 1 },
                    //     { label: 'Weekly', value: 2 },
                    //     { label: 'Monthly', value: 3 },
                    //     //{ label: 'Hour of day', value: 4 },
                    //     //{ label: 'Day of week', value: 5 }
                    // ]}
                    items={
                      CHART_FIELD_NAME_DROPDOWN_LIST[
                      CHART_TYPE.REPORT_PRODUCT_SALES
                      ]
                    }
                    // placeholder={'Field name'}
                    // placeholderStyle={{ color: 'black' }}
                    dropDownStyle={{
                      width: 250,
                    }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    globalTextStyle={{
                      fontFamily: 'NunitoSans-SemiBold',
                      // fontSize: 12,
                      color: Colors.fontDark,
                      marginLeft: 5,
                    }}
                    onChangeItem={(item) => {
                      // setState({ sort: selectedSort }),
                      //sortingFunc(selectedSort);

                      // console.log('test queries');
                      // console.log([
                      //   {
                      //     ...selectedChartFilterQueries[0],
                      //     fieldNameKey: item.value,
                      //     fieldNameType: item.fieldType,
                      //     fieldDataValue: null,
                      //     fieldSpecial: item.special ? item.special : null,
                      //   },
                      // ]);

                      setSelectedChartFilterQueries([
                        {
                          ...selectedChartFilterQueries[0],
                          fieldNameKey: item.value,
                          fieldNameType: item.fieldType,
                          fieldDataValue: null,
                          fieldSpecial: item.special ? item.special : null,
                        },
                      ]);
                    }}
                    defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                    listMode={'SCROLLVIEW'}
                    scrollViewProps={{
                      nestedScrollEnabled: true,
                    }}
                  />

                  <DropDownPicker
                    arrowColor={'#BDBDBD'}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    style={{ marginLeft: '5%', width: 150 }}
                    // items={[
                    //     { label: 'Daily', value: 1 },
                    //     { label: 'Weekly', value: 2 },
                    //     { label: 'Monthly', value: 3 },
                    //     //{ label: 'Hour of day', value: 4 },
                    //     //{ label: 'Day of week', value: 5 }
                    // ]}
                    items={
                      CHART_FIELD_COMPARE_DROPDOWN_LIST[
                      CHART_TYPE.REPORT_PRODUCT_SALES
                      ]
                    }
                    // placeholder={'Field name'}
                    // placeholderStyle={{ color: 'black' }}
                    dropDownStyle={{
                      width: 150,
                      marginLeft: '5%',
                    }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    globalTextStyle={{
                      fontFamily: 'NunitoSans-SemiBold',
                      // fontSize: 12,
                      color: Colors.fontDark,
                      marginLeft: 5,
                    }}
                    onChangeItem={(item) => {
                      // setState({ sort: selectedSort }),
                      //sortingFunc(selectedSort);
                      setSelectedChartFilterQueries([
                        {
                          ...selectedChartFilterQueries[0],
                          fieldCompare: item.value,
                          fieldDataValue: null,
                        },
                      ]);
                    }}
                    defaultValue={selectedChartFilterQueries[0].fieldCompare}
                    listMode={'SCROLLVIEW'}
                    scrollViewProps={{
                      nestedScrollEnabled: true,
                    }}
                  />
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    margin: 10,
                    marginVertical: 5,
                    // height: 35,
                  }}>
                  <TextInput
                    {...(selectedChartFilterQueries[0].fieldNameType ===
                      CHART_FIELD_TYPE.DATETIME && {
                      onPressIn: () => {
                        setShowDateTimePickerFilter(true);
                      },
                    })}
                    editable={
                      selectedChartFilterQueries[0].fieldNameType ===
                        CHART_FIELD_TYPE.DATETIME
                        ? false
                        : true
                    }
                    style={{
                      width: 410,
                      borderWidth: 1,
                      borderColor: '#D2D2D2',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'black',
                      fontFamily: 'NunitoSans-Regular',
                      borderRadius: 8,
                      paddingVertical: 3,
                      paddingLeft: 15,
                      height: 35,
                    }}
                    placeholder="Field Value"
                    placeholderStyle={{
                      color: 'black',
                      justifyContent: 'center',
                      alignItems: 'center',
                      fontFamily: 'NunitoSans-Regular',
                    }}
                    defaultValue={
                      selectedChartFilterQueries[0].fieldNameType ===
                        CHART_FIELD_TYPE.DATETIME
                        ? moment(
                          selectedChartFilterQueries[0].fieldDataValue,
                        ).format('DD/MM/YYYY')
                        : selectedChartFilterQueries[0].fieldDataValue
                          ? selectedChartFilterQueries[0].fieldDataValue
                          : ''
                    }
                    onChangeText={(text) => {
                      setSelectedChartFilterQueries([
                        {
                          ...selectedChartFilterQueries[0],
                          fieldDataValue: text,
                        },
                      ]);
                    }}
                    keyboardType={
                      selectedChartFilterQueries[0].fieldNameType ===
                        CHART_FIELD_TYPE.STRING
                        ? 'default'
                        : 'numeric'
                    }
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  />
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    margin: 10,
                    height: 40,
                    zIndex: -1,
                  }} />
                <View
                  style={{
                    borderWidth: 0.5,
                    borderColor: '#D2D2D2',
                    width: '100%',
                    marginTop: 5,
                    marginBottom: 5,
                  }} />
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    margin: 10,
                  }}>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      width: '30%',
                    }}
                    onPress={() => {
                      setExpandSelection(false);

                      setAppliedChartFilterQueries([]);
                    }}>
                    <View
                      style={{
                        justifyContent: 'center',
                        width: '100%',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: '#BDBDBD',
                        borderRadius: 5,
                        height: 35,
                        alignItems: 'center',
                        backgroundColor: '#FFFFFF',
                        height: windowHeight * 0.05,
                      }}>
                      <Text
                        style={{
                          color: '#BDBDBD',
                          fontSize: 15,
                          fontFamily: 'Nunitosans-bold',
                          marginLeft: 7,
                        }}>
                        CANCEL
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      width: '30%',
                      marginLeft: '3%',
                    }}
                    onPress={() => {
                      setExpandSelection(false);

                      setAppliedChartFilterQueries(selectedChartFilterQueries);
                    }}>
                    <View
                      style={{
                        justifyContent: 'center',
                        width: '100%',
                        flexDirection: 'row',
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        height: 35,
                        alignItems: 'center',
                        height: windowHeight * 0.05,
                      }}>
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 15,
                          fontFamily: 'Nunitosans-bold',
                          marginLeft: 7,
                        }}>
                        APPLY
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                {/* </ScrollViewGH> */}
              </View>
            ) : null}

            {/* <ModalView style={{ flex: 1 }} visible={visible} transparent={true} animationType="slide">
                    <View
                        style={{
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: windowHeight,
                        }}>
                        <View style={styles.confirmBox}>
                            <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>

                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                                <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { download() }}>
                                    <Text style={{ color: Colors.primaryColor, fontSize: 24, fontFamily: 'NunitoSans-SemiBold', textAlign: 'center' }}>Download</Text>
                                </TouchableOpacity>
                                <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { setState({ visible: !visible }); }}>
                                    <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontFamily: 'NunitoSans-SemiBold', textAlign: 'center' }}>Cancel</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </ModalView> */}
            {/* <ModalView style={{ flex: 1 }} visible={visible1} transparent={true} animationType="slide">
                    <View
                        style={{
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: windowHeight,
                        }}>
                        <View style={styles.confirmBox}>
                            <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>

                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                                <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { email() }}>
                                    <Text style={{ color: Colors.primaryColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Email</Text>
                                </TouchableOpacity>
                                <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { setState({ visible1: !visible1 }); }}>
                                    <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Cancel</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </ModalView> */}
          </View>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  confirmBox: {
    // width: '30%',
    // height: '30%',
    // borderRadius: 30,
    // backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.4,
    height: Dimensions.get('window').height * 0.4,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  modalSaveButton1: {
    width: Dimensions.get('window').width * 0.1,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  ManageFilterBox: {
    //width: windowWidth * 0.33,
    height: Dimensions.get('window').height * 0.25,
    width:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.42
        : Dimensions.get('window').width * 0.33,
    position: 'absolute',
    marginTop: '18.5%',
    marginLeft: '2%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 10,
    zIndex: 1000,
    borderRadius: 10,
    borderTopLeftRadius: 0,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  ManageFilterBox1: {
    //width: windowWidth * 0.33,
    //height: windowHeight * 0.25,
    width:
      Platform.OS === 'ios'
        ? Dimensions.get('window').width * 0.42
        : Dimensions.get('window').width * 0.33,
    position: 'absolute',
    marginTop: '18.5%',
    marginLeft: '2%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 10,
    zIndex: 1000,
    borderRadius: 10,
    borderTopLeftRadius: 0,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ReportSalesUpselling;
