import React from 'react';
import 'react-native';

import { FlatList } from "react-native-gesture-handler";

const VirtualizedView = props => {
    return (
        <FlatList
            data={[]}
            ListEmptyComponent={null}
            keyExtractor={() => { }}
            renderItem={null}
            ListHeaderComponent={() => (
                <React.Fragment>{props.children}</React.Fragment>
            )}
            contentContainerStyle={props.contentContainerStyle}
        />
    );
};

export default VirtualizedView;