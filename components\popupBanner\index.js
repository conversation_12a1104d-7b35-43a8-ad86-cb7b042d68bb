import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Colors from '../../constant/Colors';

const PopupBanner = ({ popupMsg }) => {
  const hasContent = popupMsg && (
    (typeof popupMsg === 'string' && popupMsg.trim().length > 0) ||
    (typeof popupMsg === 'object' && popupMsg !== null && (
      (popupMsg.content && popupMsg.content.trim().length > 0) ||
      (popupMsg.data && popupMsg.data.trim().length > 0) ||
      (popupMsg.message && popupMsg.message.trim().length > 0) ||
      (popupMsg.text && popupMsg.text.trim().length > 0)
    ))
  );

  if (!hasContent) {
    return null;
  }

  const { width: windowWidth, height: windowHeight } = Dimensions.get('window');

  const bannerHeight = windowHeight * 0.1;
  const bannerWidth = windowWidth * 0.5;

  let messageContent = '';
  if (typeof popupMsg === 'string') {
    messageContent = popupMsg;
  } else if (typeof popupMsg === 'object' && popupMsg !== null) {
    messageContent = popupMsg.content || popupMsg.data || popupMsg.message || popupMsg.text || '';
  }

  return (
    <View style={[styles.bannerContainer, {
      width: bannerWidth,
      height: bannerHeight,
    }]}>
      <View style={styles.bannerContent}>
        <Text style={styles.bannerText} numberOfLines={0}>
          {messageContent}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bannerContainer: {
    position: 'absolute',
    top: 0,
    left: '25%',
    backgroundColor: Colors.primaryColor || '#4E9F7D',
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 9999,
  },
  bannerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  bannerText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 22,
    fontFamily: 'NunitoSans-SemiBold',
  },
});

export default PopupBanner;
