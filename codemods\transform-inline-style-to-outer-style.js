const { get } = require("lodash");

module.exports = function (file, api) {
  const j = api.jscodeshift;
  const root = j(file.source);

  // Helper function to generate a unique keystring for the style
  const generateKey = (() => {
    let counter = 1;
    return () => `style${counter++}`;
  })();

  // A dictionary to store unique styles and avoid duplicates
  const styleDict = {};

  // Function to convert inline styles to standalone style object properties
  const convertInlineStyles = (styleObj) => {
    if (!styleObj || styleObj.type !== 'ObjectExpression') return null;

    const styleProperties = styleObj.properties;
    const styleKey = generateKey();
    const styleValue = {};

    // Check if the style contains conditional checking
    let hasConditionalCheck = false;
    styleProperties.forEach((prop) => {
      if (!prop || !prop.value) return; // Ensure the property and its value exist
      if (
        prop.value.type === "ConditionalExpression" ||
        prop.value.type === "LogicalExpression" ||
        (prop.value.type === "MemberExpression" && get(prop.value, "object.name") !== "Platform")
      ) {
        hasConditionalCheck = true;
      }
    });

    if (hasConditionalCheck) {
      return null; // Leave the style as-is if there's non-Platform conditional logic
    }

    // Convert inline styles to standalone properties
    styleProperties.forEach((prop) => {
      if (!prop || !prop.key || !prop.value) return; // Ensure prop, key, and value exist
      const key = prop.key.name || prop.key.value;
      const value = j(prop.value).toSource();
      styleValue[key] = value;
    });

    // If this exact style already exists in the dictionary, reuse it
    for (let existingKey in styleDict) {
      if (JSON.stringify(styleDict[existingKey]) === JSON.stringify(styleValue)) {
        return existingKey; // Return the existing key if styles match
      }
    }

    // Otherwise, add the new style to the dictionary and return the new key
    styleDict[styleKey] = styleValue;
    return styleKey;
  };

  // Find all JSX elements with style attributes and modify them
  root
    .find(j.JSXAttribute, { name: { name: "style" } })
    .filter((path) => path.value.value && path.value.value.expression && path.value.value.expression.type === "ObjectExpression")
    .forEach((path) => {
      const inlineStyle = path.value.value.expression;
      const styleKey = convertInlineStyles(inlineStyle);

      if (styleKey) {
        // Replace the inline style with the new style object key reference
        path.get('value').replace(j.jsxExpressionContainer(j.identifier(styleKey)));
      }
    });

  // Add the new styles object to the top of the file
  if (Object.keys(styleDict).length > 0) {
    const stylesDeclaration = j.variableDeclaration('const', [
      j.variableDeclarator(
        j.identifier('styles'),
        j.objectExpression(
          Object.keys(styleDict).map((key) =>
            j.property(
              'init',
              j.identifier(key),
              j.objectExpression(
                Object.entries(styleDict[key]).map(([k, v]) =>
                  j.property(
                    'init',
                    j.identifier(k),
                    j.literal(eval(v)) // Evaluate string value back to its original form
                  )
                )
              )
            )
          )
        )
      )
    ]);

    root.find(j.Program).get('body', 0).insertBefore(stylesDeclaration);
  }

  return root.toSource();
};
