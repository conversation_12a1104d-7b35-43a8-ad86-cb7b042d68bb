import { Text } from "react-native-fast-text";
import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
    createRef,
    useCallback,
} from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    Dimensions,
    TouchableOpacity,
    Switch,
    Modal as ModalComponent,
    KeyboardAvoidingView,
    Platform,
    useWindowDimensions,
    InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { NativeViewGestureHandler, TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import {
    getAddOnChoicePrice,
    getAddOnChoiceQuantity,
    getCartItemPriceWithoutAddOn,
    getOrderDiscountInfo,
    getOrderDiscountInfoInclOrderBased,
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import {
    ORDER_TYPE,
    PAYMENT_CHANNEL_NAME_PARSED,
    DINE_IN_SORT_FIELD_TYPE,
    DINE_IN_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_COMPARE_OPERATOR,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE_SHORT,
    KD_PRINT_VARIATION,
    KD_ITEM_STATUS,
    PRIVILEGES_NAME,
    ROLE_TYPE,
} from '../constant/common';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { FlashList } from "@shopify/flash-list";
import { openCashDrawer, printDocket, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder } from '../util/printer';
// import { storageMMKV } from '../util/storageMMKV';
import { TableStore } from '../store/tableStore';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const OdOrderDetails = React.memo((props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [sort, setSort] = useState(null);
    const [sort1, setSort1] = useState(null);
    const [outletId, setOutletId] = useState(User.getOutletId());
    const [table, setTable] = useState([]);
    const [prepareTime, setPrepareTime] = useState([]);
    const [order, setOrder] = useState([]);
    const [expandOrder, setExpandOrder] = useState(false);
    const [filter, setFilter] = useState(null);
    const [lastSort, setLastSort] = useState(null);
    const [lastFilter, setLastFilter] = useState(null);
    const [visible, setVisible] = useState(false);
    const [visible1, setVisible1] = useState(false);
    const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [modalCancelVisibility, setModalCancelVisibility] = useState(false);
    const [currToCancelOrder, setCurrToCancelOrder] = useState({});
    const [currOrderIndex, setCurrOrderIndex] = useState(0);

    const [controller, setController] = useState({});
    const [controller1, setController1] = useState({});
    const [refArray, setRefArray] = useState([]);

    const [refreshRate, setRefreshRate] = useState(new Date());

    const [expandViewDict, setExpandViewDict] = useState({});

    const [dineInOrders, setDineInOrders] = useState([]);

    ///////////Sorting////////
    const [sortOrderID, setSortOrderID] = useState();
    const [sortDateTime, setSortDateTime] = useState();
    const [sortCustomerName, setSortCustomerName] = useState();
    const [sortWaitingTime, setSortWaitingTime] = useState();
    const [sortAuthorization, setSortAuthorization] = useState();
    // const [sortPaymentMethod, setSortPaymentMethod] = useState();
    const [sortTotalPrice, setSortTotalPrice] = useState();
    /////////////////////////////

    const [search, setSearch] = useState('');

    const userOrders = OutletStore.useState((s) => s.userOrders);
    const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);

    const userName = UserStore.useState((s) => s.name);
    const firebaseUid = UserStore.useState((s) => s.firebaseUid);

    const merchantName = MerchantStore.useState((s) => s.name);
    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
    const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const crmUsers = OutletStore.useState((s) => s.crmUsers);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const privileges_state = UserStore.useState((s) => s.privileges);

    const [privileges, setPrivileges] = useState([]);
    const role = UserStore.useState((s) => s.role);
    const pinNo = UserStore.useState(s => s.pinNo);

    const selectedOrder = CommonStore.useState((s) => s.selectedOrder);

    useEffect(() => {
        const useEffectCallback = async () => {
            // admin full access

            // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
            // const enteredPinNo = storageMMKV.getString('enteredPinNo');
            const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

            if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
                setPrivileges([
                    "EMPLOYEES",
                    "OPERATION",
                    "PRODUCT",
                    "INVENTORY",
                    "INVENTORY_COMPOSITE",
                    "DOCKET",
                    "VOUCHER",
                    "PROMOTION",
                    "CRM",
                    "LOYALTY",
                    "TRANSACTIONS",
                    "REPORT",
                    "RESERVATIONS",

                    // for action
                    'REFUND_ORDER',

                    'SETTINGS',

                    'QUEUE',

                    'OPEN_CASH_DRAWER',

                    'KDS',

                    'UPSELLING',

                    // for Kitchen

                    'REJECT_ITEM',
                    'CANCEL_ORDER',
                    //'REFUND_tORDER',

                    'MANUAL_DISCOUNT',
                ]);
            } else {
                setPrivileges(privileges_state || []);
            }
        };

        useEffectCallback();
    }, [role, privileges_state, pinNo]);

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            setTargetOutletDropdownList(
                allOutlets.map((outlet) => ({
                    label: outlet.name,
                    value: outlet.uniqueId,
                })),
            );

            if (allOutlets.length > 0) {
                setSelectedTargetOutletId(currOutletId);
            }
        });
    }, [allOutlets, currOutletId]);

    useEffect(() => {
        if (isMounted) {
            InteractionManager.runAfterInteractions(() => {
                setDineInOrders(
                    userOrders.filter((order) => order.orderType === ORDER_TYPE.DINEIN &&
                        (
                            order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                            ||
                            order.orderStatus === USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
                            ||
                            order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING
                            ||
                            order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                            ||
                            order.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED
                        )
                    ),
                );

                // // console.log('dineInOrders');
                // // console.log(userOrders.filter(order => order.orderType === ORDER_TYPE.DINEIN));
            });
        }
    }, [userOrders, isMounted]);

    useEffect(() => {
        setRefArray((ref) =>
            Array(dineInOrders.length)
                .fill()
                .map((_, i) => ref[i] || createRef()),
        );
    }, [dineInOrders.length]);

    // useEffect(() => {
    //   setTimeout(() => {
    //     setRefreshRate(new Date());

    //     checkOvertimeOrders();
    //   }, 30000);

    //   checkOvertimeOrders();
    // }, [refreshRate]);

    const [tempStatus, setTempStatus] = useState('')

    useEffect(() => {
        if (selectedOrder.orderStatus == 'PICKUP' && selectedOrder.orderTypeSub == 'OTHER_DELIVERY') {
            if (selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) {
                setTempStatus('CONFIRMED')
            }
        }
        else if (selectedOrder.orderStatus == 'PICKUP') {
            if (selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) {
                setTempStatus('CONFIRMED')
            }
        }
        else if (selectedOrder.orderStatus == 'DINEIN') {
            setTempStatus('CONFIRMED')
        }
        else {
            setTempStatus('CONFIRMED')
        }

    }, [selectedOrder.orderStatus, tempStatus]);

    useEffect(() => {
        setRefArray((ref) =>
            Array(dineInOrders.length)
                .fill()
                .map((_, i) => ref[i] || createRef()),
        );
    }, [dineInOrders.length]);
    tempStatus
    const sortDineIn = (dataList, dineInSortFieldType) => {
        var dataListTemp = [...dataList];
        // console.log('dineindataList');
        // console.log(dataList);

        // console.log('dineInSortFieldType');
        // console.log(dineInSortFieldType);

        const dineInSortFieldTypeValue =
            DINE_IN_SORT_FIELD_TYPE_VALUE[dineInSortFieldType];

        const dineInSortFieldTypeCompare =
            REPORT_SORT_FIELD_TYPE_COMPARE[dineInSortFieldType];

        //TABLE_CODE
        // if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_ASC) {
        //     dataListTemp.sort1((a, b) =>
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //         .localeCompare(
        //          b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //     );
        //   } else {
        //     dataListTemp.sort1((a, b) =>
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //         .localeCompare(
        //         b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
        //     );
        //   }
        // }
        // else if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_DESC) {
        //     dataListTemp.sort1((a, b) =>
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //         .localeCompare(
        //          a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //     );
        //   } else {
        //     dataListTemp.sort1((a, b) =>
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //         .localeCompare(
        //         a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
        //     );
        //   }
        // }

        //ORDER_ID
        // if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_ID_ASC) {
        //     dataListTemp.sort1((a, b) =>
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //         -
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //     );
        //   } else {
        //     dataListTemp.sort1((a, b) =>
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //         -
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
        //     );
        //   }
        // }
        // else if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_ID_DESC) {
        //     dataListTemp.sort1((a, b) =>
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //         -
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
        //     );
        //   } else {
        //     dataListTemp.sort1((a, b) =>
        //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
        //         -
        //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
        //     );
        //   }
        // }

        //ORDER_DATE
        if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_ASC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : null) -
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : null),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : null) -
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : null),
                );
            }
        } else if (
            dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
        ) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_DESC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : null) -
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : null),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : null) -
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : null),
                );
            }
        }

        //WAITER_NAME
        if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_ASC) {
                dataListTemp.sort1((a, b) =>
                    (a[dineInSortFieldTypeValue]
                        ? a[dineInSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '',
                    ),
                );
            } else {
                dataListTemp.sort1((a, b) =>
                    (a[dineInSortFieldTypeValue]
                        ? a[dineInSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '',
                    ),
                );
            }
        } else if (
            dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
        ) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_DESC) {
                dataListTemp.sort1((a, b) =>
                    (b[dineInSortFieldTypeValue]
                        ? b[dineInSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '',
                    ),
                );
            } else {
                dataListTemp.sort1((a, b) =>
                    (b[dineInSortFieldTypeValue]
                        ? b[dineInSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '',
                    ),
                );
            }
        }

        //WAITING_TIME
        if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : '') -
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : ''),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : '') -
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : ''),
                );
            }
        } else if (
            dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
        ) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_DESC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : '') -
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : ''),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (moment(b[dineInSortFieldTypeValue]).valueOf()
                            ? moment(b[dineInSortFieldTypeValue]).valueOf()
                            : '') -
                        (moment(a[dineInSortFieldTypeValue]).valueOf()
                            ? moment(a[dineInSortFieldTypeValue]).valueOf()
                            : ''),
                );
            }
        }

        //PAYMENT_DETAILS

        //FINAL_PRICE
        if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_ASC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '') -
                        (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '') -
                        (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
                );
            }
        } else if (
            dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
        ) {
            if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_DESC) {
                dataListTemp.sort1(
                    (a, b) =>
                        (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '') -
                        (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
                );
            } else {
                dataListTemp.sort1(
                    (a, b) =>
                        (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '') -
                        (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
                );
            }
        }

        return dataListTemp;
    };

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false,
    // });

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //   ? { right: windowWidth * 0.05 }
                    //   : {},
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Order Dashboard
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    // componentDidMount = () => {
    //   setInterval(() => {
    //     getOrderList();
    //   }, 5000);
    //   getOrderList();

    // }

    const checkOvertimeOrders = async () => {
        for (var i = 0; i < dineInOrders.length; i++) {
            const waitingTime =
                // (moment().valueOf() - dineInOrders[i].estimatedPreparedDate) /
                (moment().valueOf() - dineInOrders[i].createdAt) /
                (1000 * 60);

            if (waitingTime >= 300) {
                await cancelOrder(dineInOrders[i], false);
            }
        }
    };

    const getOrderList = () => {
        ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(
            (result) => {
                var dineInList = [];
                for (const order of result) {
                    if (order.customTable != 'TAKE AWAY') {
                        dineInList.push(order);
                    }
                }

                if (unfilteredOrder && unfilteredOrder.length > 0) {
                    var diff = false;

                    if (dineInList.length !== unfilteredOrder.length) {
                        diff = true;
                    } else {
                        for (var i = 0; i < dineInList.length; i++) {
                            if (dineInList[i].id !== unfilteredOrder[i].id) {
                                diff = true;
                                break;
                            }
                        }
                    }

                    diff &&
                        setState({
                            order: [...dineInList],
                            unfilteredOrder: [...dineInList],
                        });
                } else {
                    setState({ order: [...dineInList], unfilteredOrder: [...dineInList] });
                }
            },
        );
    };

    const sortingOrders = (param) => {
        if (param.value == 1) {
            //orderid
            setDineInOrders(
                dineInOrders
                    .slice(0)
                    .sort((a, b) => b.orderId.localeCompare(a.orderId)),
            );
        }
        if (param.value == 2) {
            //date time
            setDineInOrders(
                dineInOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate),
            );
        }
        if (param.value == 3) {
            //Name
            setDineInOrders(
                dineInOrders
                    .slice(0)
                    .sort((a, b) => b.userName.localeCompare(a.userName)),
            );
        }
        if (param.value == 4) {
            //Waiting Time
            setDineInOrders(
                dineInOrders
                    .slice(0)
                    // .sort(
                    //   (a, b) =>
                    //     moment().valueOf() -
                    //     b.estimatedPreparedDate -
                    //     (moment().valueOf() - a.estimatedPreparedDate),
                    // ),
                    .sort(
                        (a, b) =>
                            moment().valueOf() -
                            b.createdAt -
                            (moment().valueOf() - a.createdAt),
                    ),
            );
        }
        if (param.value == 5) {
            //Payment Method
            setDineInOrders(
                dineInOrders
                    .slice(0)
                    .sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)),
            );
        }
        if (param.value == 6) {
            //Total
            setDineInOrders(
                dineInOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice),
            );
        }
    };

    const filterOrders = (param) => {
        if (param.value == 0) {
            // All orders
            setDineInOrders(
                userOrders.filter((order) => order.orderType === ORDER_TYPE.DINEIN),
            );
        }

        if (param.value == 1) {
            //Awaiting Authorizaion
            setDineInOrders(
                userOrders.filter(
                    (order) =>
                        order.orderType === ORDER_TYPE.DINEIN &&
                        order.paymentDetails === null,
                ),
            );
        }

        if (param.value == 2) {
            //Paid
            setDineInOrders(
                userOrders.filter(
                    (order) =>
                        order.orderType === ORDER_TYPE.DINEIN &&
                        order.paymentDetails !== null,
                ),
            );
        }
    };

    const expandOrderFunc = (param) => {
        // if (expandOrder == false) {
        //   // return setState({ expandOrder: true }), param.expandOrder = true;
        //   // setExpandOrder(true);
        //   setExpandViewDict({
        //     ...expandViewDict,
        //     [param.uniqueId]: true,
        //   });
        // } else {
        //   // return setState({ expandOrder: false }), param.expandOrder = false;
        //   // setExpandOrder(false);
        //   setExpandViewDict({
        //     ...expandViewDict,
        //     [param.uniqueId]: undefined,
        //   });
        // }

        if (!expandViewDict[param.uniqueId]) {
            // return setState({ expandOrder: true }), param.expandOrder = true;
            // setExpandOrder(true);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: true,
            });
        } else {
            // return setState({ expandOrder: false }), param.expandOrder = false;
            // setExpandOrder(false);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: false,
            });
        }
    };

    const prioritizeOrder = (param) => {
        var body = {
            orderId: param,
        };

        // Alert.alert(
        //   'Success',
        //   'The order had been prioritized',
        //   [{ text: 'OK', onPress: () => { } }],
        //   { cancelable: false },
        // );
        // setVisible(false);

        // ApiClient.POST(API.prioritizeOrder, body, false)
        APILocal.prioritizeOrder({ body })
            .then((result) => {
                if (result !== null) {
                    Alert.alert(
                        'Success',
                        'Order has been prioritized',
                        [{ text: 'OK', onPress: () => { } }],
                        { cancelable: false },
                    );
                    // setState({ visible: false, visible1: false });
                    setVisible(false);

                    for (const ref of refArray) {
                        if (refArray.indexOf(ref) === currOrderIndex && ref && ref.current) {
                            ref.current.close();
                        }
                    }
                } else {
                    Alert.alert(
                        'Failed',
                        'Your request has failed',
                        [{ text: 'OK', onPress: () => { } }],
                        { cancelable: false },
                    );
                    // setState({ visible: false, visible1: false });
                    setVisible(false);
                }
            });
    };

    const cancelOrder = async (param, showAlert = true) => {
        var body = {
            orderId: param.uniqueId,
            tableId: param.tableId,

            orderIdHuman: `#${(param.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + param.orderId}`,
        };

        // ApiClient.POST(API.cancelUserOrderByMerchant, body, false)
        APILocal.cancelUserOrderByMerchant({ body, uid: firebaseUid })
            .then(
                (result) => {
                    if (result && result.status === 'success') {
                        if (showAlert) {
                            Alert.alert(
                                'Success',
                                'Order has been cancelled',
                                [{ text: 'OK', onPress: () => { } }],
                                { cancelable: false },
                            );
                            setModalCancelVisibility(false);

                            for (const ref of refArray) {
                                if (
                                    refArray.indexOf(ref) === currOrderIndex &&
                                    ref &&
                                    ref.current
                                ) {
                                    ref.current.close();
                                }
                            }
                        }
                    } else if (showAlert) {
                        Alert.alert(
                            'Failed',
                            'Your request has failed',
                            [{ text: 'OK', onPress: () => { } }],
                            { cancelable: false },
                        );
                        // setState({ visible: false });
                        // setVisible(false);
                        setModalCancelVisibility(false);
                    }
                },
            );
    };

    const renderModal = (item) => {
        return (
            <View>
                {item && item.uniqueId && (
                    <>
                        <ModalView
                            supportedOrientations={['landscape', 'portrait']}
                            style={{ flex: 1 }}
                            visible={visible}
                            transparent
                            animationType="slide">
                            <View
                                style={{
                                    backgroundColor: 'rgba(0,0,0,0.5)',
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    minHeight: windowHeight,
                                }}>
                                <View
                                    style={[
                                        styles.confirmBox,
                                        switchMerchant
                                            ? {
                                                height: windowHeight * 0.25,
                                                width: windowWidth * 0.3,
                                            }
                                            : {},
                                    ]}>
                                    <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                                        <Text
                                            style={[
                                                {
                                                    textAlign: 'center',
                                                    fontWeight: '700',
                                                    fontSize: 24,
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 16,
                                                    }
                                                    : {},
                                            ]}>
                                            Prioritize Order
                                        </Text>
                                    </View>
                                    <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                                        <Text
                                            style={[
                                                {
                                                    textAlign: 'center',
                                                    color: Colors.descriptionColor,
                                                    fontSize: 18,
                                                    width: '80%',
                                                    alignSelf: 'center',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {/* Priotize Takeaway for {item.user == null ? "" : item.user.name}, Order#{item.customTable == "TAKE AWAY" ? "T" : ""}{item.id} */}
                                            {/* Priotize Order for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                                            Order #{item.orderId}
                                        </Text>
                                    </View>
                                    <View style={{ height: windowHeight * 0.033 }} />
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignSelf: 'center',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            width: '50%',
                                            alignContent: 'center',
                                            zIndex: 6000,
                                        }} />
                                    <View
                                        style={[
                                            {
                                                alignSelf: 'center',
                                                marginTop: 20,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                width: switchMerchant ? '71.5%' : 250,
                                                height: switchMerchant ? 35 : 40,
                                                alignContent: 'center',
                                                flexDirection: 'row',
                                                marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 32 : 40,
                                            },
                                            switchMerchant
                                                ? {
                                                    marginTop: '13%',
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    alignItems: 'flex-end',
                                                }
                                                : {},
                                        ]}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                prioritizeOrder(item.uniqueId);
                                            }}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '70%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignContent: 'center',
                                                height: switchMerchant ? 35 : 80,
                                                borderBottomLeftRadius: 10,
                                                borderRightWidth: StyleSheet.hairlineWidth,
                                                borderTopWidth: StyleSheet.hairlineWidth,
                                            }}>
                                            <Text
                                                style={[
                                                    { fontSize: 22, color: Colors.primaryColor },
                                                    switchMerchant
                                                        ? {
                                                            fontSize: 10,
                                                            // borderWidth: 1,
                                                            width: '100%',
                                                            textAlign: 'center',
                                                        }
                                                        : {},
                                                ]}>
                                                Confirm
                                            </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            onPress={() => {
                                                // setState({ visible: false });
                                                setVisible(false);
                                            }}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '70%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignContent: 'center',
                                                height: switchMerchant ? 35 : 80,
                                                borderBottomRightRadius: 10,
                                                borderTopWidth: StyleSheet.hairlineWidth,
                                            }}>
                                            <Text
                                                style={[
                                                    { fontSize: 22, color: Colors.descriptionColor },
                                                    switchMerchant
                                                        ? {
                                                            fontSize: 10,
                                                            // borderWidth: 1,
                                                            width: '100%',
                                                            textAlign: 'center',
                                                        }
                                                        : {},
                                                ]}>
                                                Cancel
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </ModalView>
                    </>
                )}
            </View>
        );
    };

    const renderModalCancel = (item) => {
        return (
            <View>
                {item && item.uniqueId && (
                    <ModalView
                        supportedOrientations={['landscape', 'portrait']}
                        style={{ flex: 1 }}
                        visible={modalCancelVisibility}
                        transparent
                        animationType="slide">
                        <View
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: windowHeight,
                            }}>
                            <View
                                style={[
                                    styles.confirmBox,
                                    switchMerchant
                                        ? {
                                            height: windowHeight * 0.25,
                                            width: windowWidth * 0.3,
                                        }
                                        : {},
                                ]}>
                                <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                                    <Text
                                        style={[
                                            {
                                                textAlign: 'center',
                                                fontWeight: '700',
                                                fontSize: 24,
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 16,
                                                }
                                                : {},
                                        ]}>
                                        Cancel Order
                                    </Text>
                                </View>
                                <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                                    <Text
                                        style={[
                                            {
                                                textAlign: 'center',
                                                color: Colors.descriptionColor,
                                                fontSize: 18,
                                                width: '80%',
                                                alignSelf: 'center',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        {/* Cancel Order for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                                        Order #{item.orderId}
                                    </Text>
                                </View>
                                <View style={{ height: windowHeight * 0.057 }} />
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignSelf: 'center',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: '50%',
                                        alignContent: 'center',
                                        zIndex: 6000,
                                    }} />
                                <View
                                    style={[
                                        {
                                            alignSelf: 'center',
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            width: switchMerchant ? '71.5%' : 250,
                                            height: switchMerchant ? 35 : 40,
                                            alignContent: 'center',
                                            flexDirection: 'row',
                                            marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 12 : 20,
                                        },
                                        switchMerchant
                                            ? {
                                                marginTop: '13%',
                                                position: 'absolute',
                                                bottom: 0,
                                                alignItems: 'flex-end',
                                            }
                                            : {},
                                    ]}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            cancelOrder(item);
                                        }}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: '70%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            alignContent: 'center',
                                            height: switchMerchant ? 35 : 80,
                                            borderBottomLeftRadius: 10,
                                            borderRightWidth: StyleSheet.hairlineWidth,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}>
                                        <Text
                                            style={[
                                                { fontSize: 22, color: '#d90000' },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                        width: '100%',
                                                        textAlign: 'center',
                                                    }
                                                    : {},
                                            ]}>
                                            Confirm
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => {
                                            // setState({ visible: false });
                                            // setVisible(false);
                                            setModalCancelVisibility(false);
                                        }}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: '70%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            alignContent: 'center',
                                            height: switchMerchant ? 35 : 80,
                                            borderBottomRightRadius: 10,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}>
                                        <Text
                                            style={[
                                                { fontSize: 22, color: Colors.descriptionColor },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                        width: '100%',
                                                        textAlign: 'center',
                                                    }
                                                    : {},
                                            ]}>
                                            Cancel
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ModalView>
                )}
            </View>
        );
    };

    const userOrdersTableDict = OutletStore.useState(
        (s) => s.userOrdersTableDict,
    );

    const [timePassed, setTimePassed] = useState('')

    useEffect(() => {
        var diffMinute = moment().diff(selectedOrder.createdAt, 'minute');
        var diffHour = moment().diff(selectedOrder.createdAt, 'hour');
        var diffDay = moment().diff(selectedOrder.createdAt, 'day')
        let diffTime;

        if (diffMinute < 60) {
            diffTime = diffMinute > 1 ? `${moment().diff(selectedOrder.createdAt, 'minute')} minutes ago` : `${moment().diff(selectedOrder.createdAt, 'minute')} minute ago`
        }
        else if (diffHour < 24 && diffMinute > 60) {
            diffTime = diffHour > 1 ? `${moment().diff(selectedOrder.createdAt, 'hour')} hours ago` : `${moment().diff(selectedOrder.createdAt, 'hour')} hour ago`
        }
        else if (diffHour > 24 && diffMinute > 60) {
            diffTime = diffDay > 1 ? `${moment().diff(selectedOrder.createdAt, 'day')} days ago` : `${moment().diff(selectedOrder.createdAt, 'day')} day ago`
        }
        setTimePassed(diffTime);
    })

    return (
        // <View style={styles.container}>
        //   {renderModal(currToPrioritizeOrder)}

        //   <View style={styles.sidebar}>
        (<UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                ]}>
                {selectedOrder && Object.keys(selectedOrder).length !== 0 ?
                    <ScrollView>
                        {renderModal(currToPrioritizeOrder)}
                        {renderModalCancel(currToCancelOrder)}
                        <View style={{ flex: 1, paddingHorizontal: 30, paddingVertical: 10, }}>
                            <View
                                style={[
                                    {
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        padding: 2,
                                        justifyContent: 'space-between',
                                        // borderWidth: 1
                                    },
                                    switchMerchant
                                        ? {
                                            marginBottom: windowHeight * 0.08,
                                            marginTop: '-3%',
                                        }
                                        : {},
                                ]}>
                                <View style={{
                                    flexDirection: 'row',
                                }}>
                                    <Text
                                        style={[
                                            {
                                                color: Colors.fontDark,
                                                fontSize: 26,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    // fontSize: 15,
                                                    //folow dashboard
                                                    fontSize: windowWidth / 35,
                                                    // borderWidth: 1,
                                                    // top: windowHeight * -0.08,
                                                }
                                                : {},
                                        ]}>
                                        #{selectedOrder.orderId}
                                    </Text>
                                    <Text
                                        style={[
                                            {
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-SemiBold',
                                                color: Colors.descriptionColor,

                                                marginLeft: 20,
                                                top: 11,
                                            },
                                            switchMerchant
                                                ? {
                                                    // fontSize: 15,
                                                    //folow dashboard
                                                    fontSize: windowWidth / 35,
                                                    // borderWidth: 1,
                                                    // top: windowHeight * -0.08,
                                                }
                                                : {},
                                        ]}>
                                        {
                                            selectedOrder.orderType == 'DINEIN' ? 'Dine In'
                                                : selectedOrder.orderType == 'PICKUP' && selectedOrder.orderTypeSub == 'OTHER_DELIVERY' ? 'Other Delivery'
                                                    : selectedOrder.orderType == 'PICKUP' ? 'Takeaway'
                                                        : 'Pre-Order'
                                        }
                                    </Text>
                                </View>

                                <View style={{ flexDirection: 'row' }}>
                                    <View style={{}}>
                                        <View
                                            style={[
                                                {
                                                    //width: 50,
                                                    width: 60,
                                                    height: 65,
                                                    backgroundColor: Colors.highlightColor,
                                                    borderRadius: 5,
                                                    //flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    alignContent: 'center',
                                                    alignItems: 'center',

                                                    // shadowColor: '#000',
                                                    // shadowOffset: {
                                                    //     width: 0,
                                                    //     height: 2,
                                                    // },
                                                    // shadowOpacity: 0.22,
                                                    // shadowRadius: 3.22,
                                                    // elevation: 3,
                                                    borderWidth: 1,
                                                    borderColor: Colors.highlightColor,
                                                    paddingHorizontal: 10,
                                                },
                                                switchMerchant
                                                    ? {
                                                        height: 35,
                                                        width: 200,
                                                        // top: windowHeight * -0.075,
                                                    }
                                                    : {},
                                            ]}>
                                            {selectedOrder.orderType !== 'DINEIN' ?
                                                <Image
                                                    style={[
                                                        {
                                                            width: 30,
                                                            height: 30,
                                                            //marginLeft: 0,
                                                            //left: 30,
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                width: windowWidth * 0.03,
                                                                height:
                                                                    windowHeight * 0.05,
                                                                top: windowHeight * 0.009,
                                                            }
                                                            : {},
                                                    ]}
                                                    resizeMode="contain"
                                                    source={require('../assets/image/TakeawayBlack.png')}
                                                />
                                                :
                                                <Text
                                                    style={[
                                                        {
                                                            color: Colors.fontDark,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: 16,
                                                            textAlign: 'center',
                                                            alignItems: 'center',
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}
                                                    numberOfLines={2}
                                                >
                                                    {selectedOrder.tableCode ? selectedOrder.tableCode.slice(0, 6) : ''}
                                                </Text>
                                            }
                                        </View>
                                    </View>
                                </View>
                            </View>

                            <View
                                style={[
                                    { marginTop: 30, marginBottom: 10, zIndex: -1 },
                                    switchMerchant
                                        ? {
                                            marginTop: windowHeight * -0.05,
                                            marginBottom: windowHeight * 0.15,
                                        }
                                        : {},
                                ]}>
                                <View
                                    style={{
                                        width: '100%',
                                        flexDirection: 'row',
                                        alignItems: 'flex-start',
                                        //paddingBottom: 5,
                                    }}>
                                    <View
                                        style={{
                                            marginRight: 10,
                                            width: '20%',
                                            alignItems: 'center',
                                            borderWidth: 1,
                                            borderColor: tempStatus == 'PENDING' ? Colors.secondaryColor : Colors.primaryColor,
                                            backgroundColor: tempStatus == 'PENDING' ? Colors.secondaryColor : Colors.primaryColor,
                                            borderRadius: 5,
                                            paddingHorizontal: 5,

                                        }}>
                                        <Text
                                            style={[
                                                { color: 'white', fontFamily: 'NunitoSans-Bold', bottom: 1, },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {tempStatus}
                                        </Text>
                                    </View>
                                    {(moment().diff(selectedOrder.createdAt, 'minute') < 60) ?
                                        <View
                                            style={{
                                                marginRight: 10,
                                                width: '20%',
                                                alignItems: 'center',
                                                borderWidth: 1,
                                                borderColor: Colors.tabCyan,
                                                borderRadius: 5,
                                                paddingHorizontal: 5,
                                                backgroundColor: Colors.tabCyan,
                                            }}>
                                            <Text
                                                style={[
                                                    { color: Colors.whiteColor, fontFamily: 'NunitoSans-Bold', bottom: 1, },
                                                    switchMerchant
                                                        ? {
                                                            fontSize: 10,
                                                        }
                                                        : {},
                                                ]}>
                                                JUST PLACED
                                            </Text>
                                        </View>
                                        :
                                        <></>
                                    }
                                    <Text
                                        style={[
                                            {
                                                color: Colors.fontDark,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                width: '40.5%'
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        {timePassed}
                                    </Text>

                                    <View style={{
                                        marginLeft:
                                            (moment().diff(selectedOrder.createdAt, 'minute') < 60) ? (selectedOrder.paymentDetails && selectedOrder.paymentDetails.channel || selectedOrder.completedDate !== null) ? 15
                                                : 15
                                                : (selectedOrder.paymentDetails && selectedOrder.paymentDetails.channel || selectedOrder.completedDate !== null) ? 130
                                                    : 130,
                                        alignItems: 'flex-end'
                                    }}>
                                        <View style={{ flexDirection: 'row', }}>
                                            <MaterialCommunityIcons
                                                name="cash"
                                                size={30}
                                                style={{
                                                    color: selectedOrder.paymentDetails && selectedOrder.paymentDetails.channel
                                                        || selectedOrder.completedDate !== null
                                                        ? Colors.primaryColor
                                                        : Colors.tabRed,
                                                    // alignItems: 'center',
                                                    // justifyContent: 'center'
                                                    bottom: 4
                                                }}
                                            />

                                            <Text style={[{
                                                color: Colors.fontDark,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                justifyContent: 'center',
                                                textAlign: 'right',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},]}>
                                                {selectedOrder.paymentDetails && selectedOrder.paymentDetails.channel || selectedOrder.completedDate !== null ? 'Paid' : 'Unpaid'}
                                            </Text>
                                        </View>
                                    </View>
                                </View>

                                {/* {expandViewDict[item.uniqueId] == true ? ( */}
                                <View
                                    style={{
                                        minheight: windowHeight * 0.35,
                                        //marginTop: 0,
                                        paddingBottom: 20,
                                    }}>
                                    <View style={{
                                        justifyContent: 'center',
                                        height: 40,
                                        backgroundColor: Colors.highlightColor,
                                        width: '120%',
                                        paddingLeft: 20,

                                        marginBottom: 5,
                                        marginLeft: -30,
                                    }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            marginLeft: 10,
                                        }}>
                                            Order Details
                                        </Text>
                                    </View>
                                    {/*//////////// Items ////////////*/}
                                    {selectedOrder.cartItems.map((cartItem, index) => {
                                        const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

                                        return <>
                                            <View
                                                style={{
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}>

                                                <View
                                                    style={{
                                                        width: '100%',
                                                        alignItems: 'flex-start',
                                                        flexDirection: 'row',
                                                        marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                                        minHeight: 80,
                                                        //backgroundColor: 'yellow',
                                                        marginTop: 5
                                                    }}>
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            width: '100%',
                                                            //backgroundColor: 'blue',
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',

                                                                marginBottom: 20,
                                                                marginRight: 25,
                                                            }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 16,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {index + 1}.
                                                            </Text>
                                                        </View>

                                                        <View
                                                            style={{
                                                                //width: '10%',
                                                                //backgroundColor: 'green',
                                                                alignItems: 'center',
                                                            }}>
                                                            {cartItem.image ? (
                                                                <AsyncImage
                                                                    source={{ uri: cartItem.image }}
                                                                    // item={cartItem}
                                                                    style={{
                                                                        width: switchMerchant ? 30 : 60,
                                                                        height: switchMerchant ? 30 : 60,
                                                                        borderWidth: 1,
                                                                        borderColor: '#E5E5E5',
                                                                        borderRadius: 5,
                                                                    }}
                                                                />
                                                            ) : (
                                                                <View
                                                                    style={{
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        width: switchMerchant ? 30 : 60,
                                                                        height: switchMerchant ? 30 : 60,
                                                                        borderWidth: 1,
                                                                        borderColor: '#E5E5E5',
                                                                        borderRadius: 5,
                                                                    }}>
                                                                    <Ionicons
                                                                        name="fast-food-outline"
                                                                        size={switchMerchant ? 15 : 35}
                                                                    />
                                                                </View>
                                                            )}
                                                        </View>
                                                        <View style={{ width: '75%' }}>
                                                            <View
                                                                style={{
                                                                    marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                                    marginBottom: 10,
                                                                    //backgroundColor: 'blue',
                                                                    width: '100%',
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <View style={{ width: '68%' }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 16,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                                                    </Text>
                                                                </View>

                                                                <View
                                                                    style={{
                                                                        width: '12%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            alignItems: 'center',
                                                                            //backgroundColor: 'yellow',
                                                                        }}>
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                    fontSize: 16,
                                                                                },
                                                                                // Platform.OS === 'android'
                                                                                //   ? {
                                                                                //       width: '200%',
                                                                                //     }
                                                                                //   : {},
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            x{cartItem.quantity}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '13.2%' : '21%',
                                                                        // borderWidth: 1
                                                                        marginLeft: 10,
                                                                        marginTop: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    // width: '20%'
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    //paddingRight: 25,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                }
                                                                        }>
                                                                        {cartItemPriceWIthoutAddOn
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            {cartItem.remarks &&
                                                                cartItem.remarks.length > 0 ? (
                                                                <View
                                                                    style={{
                                                                        alignItems: 'center',
                                                                        flexDirection: 'row',
                                                                        marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                                    }}>
                                                                    <View style={{ justifyContent: 'center' }}>
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                                    fontSize: 16,
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {cartItem.remarks}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            ) : (
                                                                <></>
                                                            )}

                                                            {cartItem.addOns.map((addOnChoice, i) => {
                                                                return (
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            // marginLeft: -5,
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={{
                                                                                width: '68.2%',
                                                                                flexDirection: 'row',
                                                                                marginLeft:
                                                                                    Platform.OS == 'ios' ? 14 : 14,
                                                                                // borderWidth: 1
                                                                            }}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontFamily: 'NunitoSans-Bold',
                                                                                        fontSize: 16,
                                                                                        color: Colors.descriptionColor,
                                                                                        width: '30%',
                                                                                        // marginLeft: 5,
                                                                                    },
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                        }
                                                                                        : {},
                                                                                ]}>
                                                                                {`${addOnChoice.name}:`}
                                                                            </Text>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontFamily: 'NunitoSans-Bold',
                                                                                        fontSize: 16,
                                                                                        color: Colors.descriptionColor,
                                                                                        width: '70%',
                                                                                        // borderWidth: 1
                                                                                        // marginLeft: 5,
                                                                                    },
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                        }
                                                                                        : {},
                                                                                ]}>
                                                                                {`${addOnChoice.choiceNames[0]}`}
                                                                            </Text>
                                                                        </View>

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    width: '12%',
                                                                                    flexDirection: 'row',
                                                                                    justifyContent: 'center',
                                                                                    //backgroundColor: 'blue',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        // borderWidth: 1
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontFamily: 'NunitoSans-Bold',
                                                                                        fontSize: 16,
                                                                                        color: Colors.descriptionColor,
                                                                                        //width: '28%',
                                                                                        // right: 38,
                                                                                        //backgroundColor: 'green',
                                                                                        textAlign: 'center',
                                                                                    },
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                            // borderWidth: 1,
                                                                                            // paddingLeft: '7%',
                                                                                            textAlign: 'center',
                                                                                        }
                                                                                        : {},
                                                                                    !switchMerchant &&
                                                                                        Platform.OS === 'android'
                                                                                        ? {}
                                                                                        : {},
                                                                                ]}>
                                                                                {`${addOnChoice.quantities
                                                                                    ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                                                                    : ''
                                                                                    }`}
                                                                            </Text>
                                                                        </View>

                                                                        <View
                                                                            style={{
                                                                                flexDirection: 'row',
                                                                                justifyContent: 'space-between',
                                                                                width: switchMerchant
                                                                                    ? '13.2%'
                                                                                    : '21%',
                                                                                alignItems: 'center',
                                                                                marginLeft: 10,
                                                                                // borderWidth: 1
                                                                            }}>
                                                                            <Text
                                                                                style={[
                                                                                    switchMerchant
                                                                                        ? { fontSize: 10 }
                                                                                        : {
                                                                                            color: Colors.descriptionColor,
                                                                                            fontSize: 16,
                                                                                            fontFamily: 'NunitoSans-Regular',
                                                                                        },
                                                                                ]}>
                                                                                RM
                                                                            </Text>
                                                                            <Text
                                                                                style={
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                        }
                                                                                        : {
                                                                                            color: Colors.descriptionColor,
                                                                                            //paddingRight: 25,
                                                                                            fontSize: 16,
                                                                                            fontFamily: 'NunitoSans-Regular',
                                                                                        }
                                                                                }>
                                                                                {(getAddOnChoicePrice(addOnChoice, cartItem))
                                                                                    .toFixed(2)
                                                                                    .replace(
                                                                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                        '$1,',
                                                                                    )}
                                                                            </Text>
                                                                        </View>
                                                                    </View>
                                                                );
                                                            })}
                                                        </View>
                                                    </View>

                                                    <View
                                                        style={[
                                                            {
                                                                width: '14%',
                                                                //height: '30%',
                                                                //justifyContent: 'space-between',
                                                                flexDirection: 'column',
                                                                //backgroundColor: 'green',
                                                            },
                                                            Platform.OS === 'android' ? {} : {},
                                                            switchMerchant
                                                                ? {
                                                                    fontSize: 8,
                                                                }
                                                                : {},
                                                        ]}>
                                                        <View style={{ marginTop: 3 }}>
                                                            {cartItem.addOns.map((addOnChoice, i) => {
                                                                return (
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            //width: '138.5%',
                                                                            //height: 22.5,
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    width: '100%',
                                                                                    flexDirection: 'row',
                                                                                    //backgroundColor: 'blue',
                                                                                    alignItems: 'center',
                                                                                    justifyContent: 'space-between',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                        // borderWidth: 1,
                                                                                        paddingLeft:
                                                                                            windowWidth *
                                                                                            0.017,
                                                                                    }
                                                                                    : {},
                                                                            ]} />
                                                                    </View>
                                                                );
                                                            })}
                                                        </View>
                                                    </View>
                                                </View>

                                            </View>
                                        </>;
                                    })}
                                    {/* ///////////////////////////// */}

                                    <View style={{
                                        justifyContent: 'center',
                                        height: 40,
                                        backgroundColor: Colors.highlightColor,
                                        width: '120%',
                                        paddingLeft: 20,

                                        marginBottom: 5,
                                        marginLeft: -30,
                                    }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            marginLeft: 10,
                                        }}>
                                            Payment Details
                                        </Text>
                                    </View>

                                    {selectedOrder.cartItems.map((cartItem, index) => {
                                        const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

                                        return <>
                                            <View style={{ width: '100%' }}>
                                                {index === selectedOrder.cartItems.length - 1 ? (
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            //backgroundColor: 'yellow',
                                                            //width: '28%',
                                                        }}>
                                                        <View
                                                            style={{
                                                                justifyContent: 'center',
                                                                width: '100%',
                                                            }}>
                                                            <View
                                                                style={[
                                                                    {
                                                                        flexDirection: 'row',
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            // width: '50%'
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Subtotal:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? { fontSize: 10 }
                                                                                : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '55%',
                                                                                    textAlign: 'right',
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {((selectedOrder.isRefundOrder && selectedOrder.finalPrice <= 0) ? 0 : (
                                                                            selectedOrder.totalPrice +
                                                                            getOrderDiscountInfo(selectedOrder)
                                                                        ))
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '50%',
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    width: '83.5%',
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                }
                                                                        }>
                                                                        Delivery Fee:
                                                                    </Text>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            width: switchMerchant ? '35.4%' : '21.5%',
                                                                            // borderWidth: 1
                                                                        }}>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? { fontSize: 10 }
                                                                                    : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                            }>
                                                                            RM
                                                                        </Text>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                        width: '55%',
                                                                                        textAlign: 'right',
                                                                                    }
                                                                                    : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                            }>
                                                                            {selectedOrder.deliveryFee
                                                                                .toFixed(2)
                                                                                .replace(
                                                                                    /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                    '$1,',
                                                                                )}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            ) : (
                                                                <></>
                                                            )}

                                                            {selectedOrder.orderType === ORDER_TYPE.DELIVERY ? (
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '50%',
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    width: '83.5%',
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                }
                                                                        }>
                                                                        Delivery Fee:
                                                                    </Text>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            width: '21.5%',
                                                                            marginHorizontal: 1,
                                                                        }}>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? { fontSize: 10 }
                                                                                    : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                            }>
                                                                            RM
                                                                        </Text>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                            }>
                                                                            {selectedOrder.deliveryFee
                                                                                .toFixed(2)
                                                                                .replace(
                                                                                    /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                    '$1,',
                                                                                )}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            ) : (
                                                                <></>
                                                            )}
                                                            {
                                                                (currOutlet.pickupPackagingFee && selectedOrder.orderType === ORDER_TYPE.PICKUP)
                                                                    ?
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                        }}>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                        width: '50%',
                                                                                    }
                                                                                    : {
                                                                                        fontSize: 16,
                                                                                        width: '83.5%',
                                                                                        fontFamily: 'NunitoSans-Bold',
                                                                                    }
                                                                            }>
                                                                            {/* Takeaway  */}
                                                                            {`Packaging Fee`}
                                                                        </Text>
                                                                        <View
                                                                            style={{
                                                                                flexDirection: 'row',
                                                                                justifyContent: 'space-between',
                                                                                width: '21.5%',
                                                                                marginHorizontal: 1,
                                                                            }}>
                                                                            <Text
                                                                                style={
                                                                                    switchMerchant
                                                                                        ? { fontSize: 10 }
                                                                                        : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                                }>
                                                                                RM
                                                                            </Text>
                                                                            <Text
                                                                                style={
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                        }
                                                                                        : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                                }>
                                                                                {selectedOrder.pickupPackagingFee
                                                                                    .toFixed(2)
                                                                                    .replace(
                                                                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                        '$1,',
                                                                                    )}
                                                                            </Text>
                                                                        </View>
                                                                    </View>
                                                                    :
                                                                    <></>
                                                            }

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Discount:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Regular',
                                                                            },
                                                                            switchMerchant ? {} : {},
                                                                        ]}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {' '}
                                                                        {((selectedOrder.isRefundOrder && selectedOrder.finalPrice <= 0) ? 0 : (
                                                                            // selectedOrder.discount +
                                                                            (getOrderDiscountInfoInclOrderBased(selectedOrder))
                                                                        ))
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Tax:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    // width: '30%'
                                                                                }
                                                                                : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '55%',
                                                                                    textAlign: 'right',
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {selectedOrder.tax
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Service Charge:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    // width: '30%'
                                                                                }
                                                                                : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '55%',
                                                                                    textAlign: 'right',
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {(selectedOrder.sc || 0)
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Rounding:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    // width: '30%'
                                                                                }
                                                                                : { fontSize: 16, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '55%',
                                                                                    textAlign: 'right',
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {(selectedOrder.finalPrice ? (selectedOrder.finalPrice - selectedOrder.finalPriceBefore) : 0)
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '50%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '83.5%',
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                            }
                                                                    }>
                                                                    Total:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '35.4%' : '21.5%',
                                                                        // borderWidth: 1
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Regular',
                                                                            },
                                                                            switchMerchant ? {} : {},
                                                                        ]}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : { fontSize: 16, paddingRight: 25, fontFamily: 'NunitoSans-Regular', }
                                                                        }>
                                                                        {selectedOrder.finalPrice
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <></>
                                                )}
                                            </View>
                                            {selectedOrder.orderType !== 'DINEIN' ?
                                                <View style={{ width: '100%', alignItems: 'flex-start', marginLeft: -35 }}>
                                                    {index === selectedOrder.cartItems.length - 1 ? (
                                                        <View
                                                            style={{
                                                                justifyContent: 'flex-start',
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                                height: 100,
                                                                marginHorizontal: 10,
                                                                marginTop: 20,
                                                            }}>

                                                            {(selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                                                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                                                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                                                                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) ? (
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        // setState({
                                                                        //   currToPrioritizeOrder: item,
                                                                        //   visible: true,
                                                                        // });
                                                                        setCurrOrderIndex(index);
                                                                        setCurrToPrioritizeOrder(selectedOrder);
                                                                        setVisible(true);
                                                                    }}
                                                                    style={[
                                                                        {
                                                                            height: '100%',
                                                                            justifyContent: 'center',
                                                                            alignItems: 'center',
                                                                            backgroundColor: Colors.primaryColor,
                                                                            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                            width: 75,
                                                                            marginLeft: 25,
                                                                            borderRadius: 10,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                width: windowWidth * 0.08,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {switchMerchant ? (
                                                                        <MaterialCommunityIcons
                                                                            name="message-alert-outline"
                                                                            size={10}
                                                                            color={Colors.whiteColor}
                                                                            style={{ marginTop: 10 }}
                                                                        />
                                                                    ) : (
                                                                        <MaterialCommunityIcons
                                                                            name="message-alert-outline"
                                                                            size={40}
                                                                            color={Colors.whiteColor}
                                                                            style={{ marginTop: 10 }}
                                                                        />
                                                                    )}
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                color: Colors.whiteColor,
                                                                                fontSize: 12,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                                textAlign: 'center',
                                                                                width: '80%',
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {`Prioritize\nOrder`}
                                                                    </Text>
                                                                </TouchableOpacity>
                                                            ) : null}

                                                            {(
                                                                ((selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                                                    selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                                                    selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED || selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) && (selectedOrder.orderType === ORDER_TYPE.PICKUP))
                                                                // &&
                                                                // item.paymentDetails === null
                                                            )
                                                                ? (
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            // setState({
                                                                            //   currToPrioritizeOrder: item,
                                                                            //   visible: true,
                                                                            // });

                                                                            // setCurrOrderIndex(index);
                                                                            // setCurrToPrioritizeOrder(item);
                                                                            // setVisible(true);

                                                                            CommonStore.update(s => {
                                                                                s.isCheckingOutTakeaway = true;

                                                                                s.checkingOutTakeawayOrder = selectedOrder;

                                                                                s.timestamp = Date.now();

                                                                                s.checkingOutTakeawayTimestamp = Date.now();
                                                                            }, () => {
                                                                                navigation.navigate('Table');
                                                                            });
                                                                        }}
                                                                        style={[
                                                                            {
                                                                                height: '100%',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                backgroundColor: Colors.tabCyan,
                                                                                underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                width: 75,
                                                                                marginLeft: 25,
                                                                                borderRadius: 10,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: windowWidth * 0.08,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {switchMerchant ? (
                                                                            <MaterialIcons
                                                                                name="payment"
                                                                                size={10}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        ) : (
                                                                            <MaterialIcons
                                                                                name="payment"
                                                                                size={40}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        )}
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    color: Colors.whiteColor,
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    textAlign: 'center',
                                                                                    width: '80%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`Checkout\nOrder`}
                                                                        </Text>
                                                                    </TouchableOpacity>
                                                                ) : <></>
                                                            }

                                                            {
                                                                (currOutlet && currOutlet.privileges &&
                                                                    currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                                                    && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER) ?
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            // setState({
                                                                            //   currToPrioritizeOrder: item,
                                                                            //   visible: true,
                                                                            // });
                                                                            if (selectedOrder.paymentDetails && (
                                                                                selectedOrder.paymentDetails.txn_ID !== undefined ||
                                                                                selectedOrder.paymentDetails.txnId !== undefined
                                                                            )) {
                                                                                Alert.alert('Info', 'Online paid order is unable to cancel.');
                                                                            }
                                                                            else {
                                                                                setCurrOrderIndex(index);
                                                                                setCurrToCancelOrder(selectedOrder);
                                                                                setModalCancelVisibility(true);
                                                                            }
                                                                        }}
                                                                        style={[
                                                                            {
                                                                                height: '100%',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                // backgroundColor: Colors.primaryColor,
                                                                                backgroundColor: '#d90000',
                                                                                underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                width: 75,
                                                                                marginLeft: 25,
                                                                                borderRadius: 10,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: windowWidth * 0.08,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {switchMerchant ? (
                                                                            <MaterialCommunityIcons
                                                                                name="close"
                                                                                size={10}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        ) : (
                                                                            <MaterialCommunityIcons
                                                                                name="close"
                                                                                size={40}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        )}
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    color: Colors.whiteColor,
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    textAlign: 'center',
                                                                                    width: '80%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`Cancel\nOrder`}
                                                                        </Text>
                                                                    </TouchableOpacity>
                                                                    : <></>
                                                            }

                                                            {/* 2023-01-30 - For reprint kd */}

                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                                                    var printTimes = 1;

                                                                    if (global.outletCategoriesDict) {
                                                                        if (selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                            for (var i = 0; i < selectedOrder.cartItems.length; i++) {
                                                                                if (global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId] &&
                                                                                    global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum) {
                                                                                    printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                                }
                                                                            }
                                                                        }

                                                                        if (selectedOrder.cartItemsCancelled && selectedOrder.cartItemsCancelled.length > 0) {
                                                                            for (var i = 0; i < selectedOrder.cartItemsCancelled.length; i++) {
                                                                                if (global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId] &&
                                                                                    global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId].printKDNum) {
                                                                                    printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    for (var i = 0; i < printTimes; i++) {
                                                                        await printUserOrder(
                                                                            {
                                                                                orderData: selectedOrder,
                                                                            },
                                                                            false,
                                                                            [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                            false,
                                                                            false,
                                                                            false,
                                                                            { isInternetReachable: true, isConnected: true },
                                                                            false,
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        printKDSummaryCategoryWrapper(
                                                                            {
                                                                                orderData: selectedOrder,
                                                                            },
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        if (selectedOrder && selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                            for (let bdIndex = 0; bdIndex < selectedOrder.cartItems.length; bdIndex++) {
                                                                                if (!selectedOrder.cartItems[bdIndex].isDocket) {
                                                                                    await printDocketForKD(
                                                                                        {
                                                                                            userOrder: selectedOrder,
                                                                                            cartItem: selectedOrder.cartItems[bdIndex],
                                                                                        },
                                                                                        // true,
                                                                                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                                        [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                                        // deliveredUser,
                                                                                    );
                                                                                }
                                                                            }

                                                                            for (let index = 0; index < selectedOrder.cartItems.length; index++) {
                                                                                if (selectedOrder.cartItems[index].isDocket) {
                                                                                    await printDocket(
                                                                                        {
                                                                                            userOrder: selectedOrder,
                                                                                            cartItem: selectedOrder.cartItems[index],
                                                                                        },
                                                                                        // true,
                                                                                        [PRINTER_USAGE_TYPE.RECEIPT],
                                                                                        [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                                        // deliveredUser,
                                                                                    );
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    // await printUserOrder(
                                                                    //   {
                                                                    //     orderData: item,
                                                                    //   },
                                                                    //   false,
                                                                    //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //   false,
                                                                    //   false,
                                                                    //   false,
                                                                    //   { isInternetReachable: true, isConnected: true },
                                                                    //   false,
                                                                    //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                    // );

                                                                    // printKDSummaryCategoryWrapper(
                                                                    //   {
                                                                    //     orderData: item,
                                                                    //   },
                                                                    //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                    // );

                                                                    // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                                                    //   await printDocketForKD(
                                                                    //     {
                                                                    //       userOrder: item,
                                                                    //       cartItem: item.cartItems[bdIndex],
                                                                    //     },
                                                                    //     // true,
                                                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //   );
                                                                    // }

                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    // disconnectPrinter(printer); // no need anymore

                                                                    // await printUserOrder(
                                                                    //   {
                                                                    //     orderId: item.uniqueId,
                                                                    //     receiptNote: currOutlet.receiptNote || '',
                                                                    //   },
                                                                    //   false,
                                                                    //   [PRINTER_USAGE_TYPE.RECEIPT],
                                                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //   false,
                                                                    //   false,
                                                                    //   false,
                                                                    //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                                                    //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    // );

                                                                    // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                                                    //   await printUserOrder(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //     false,
                                                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //     false,
                                                                    //     false,
                                                                    //     false,
                                                                    //     { isInternetReachable: true, isConnected: true },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                                                    //   printKDSummaryCategoryWrapper(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                                                    //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                                                    //     await printDocketForKD(
                                                                    //       {
                                                                    //         userOrder: item,
                                                                    //         cartItem: item.cartItems[bdIndex],
                                                                    //       },
                                                                    //       // true,
                                                                    //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //     );
                                                                    //   }
                                                                    // }
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                ) : (
                                                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Reprint\nKD`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            {/* 2023-05-08 - For reprint os */}

                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    Alert.alert('Info', 'Order summary has been added to print queue');

                                                                    var printTimes = 1;

                                                                    // if (global.outletCategoriesDict) {
                                                                    //   if (item.cartItems && item.cartItems.length > 0) {
                                                                    //     for (var i = 0; i < item.cartItems.length; i++) {
                                                                    //       if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                                                    //         global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                                    //       }
                                                                    //     }
                                                                    //   }

                                                                    //   if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                                                    //     for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                                                    //       if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                                                    //         global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                                    //       }
                                                                    //     }
                                                                    //   }
                                                                    // }

                                                                    for (var i = 0; i < printTimes; i++) {
                                                                        await printUserOrder(
                                                                            {
                                                                                orderData: item,
                                                                            },
                                                                            false,
                                                                            [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                                                            false,
                                                                            false,
                                                                            false,
                                                                            { isInternetReachable: true, isConnected: true },
                                                                            true,
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        // printKDSummaryCategoryWrapper(
                                                                        //   {
                                                                        //     orderData: item,
                                                                        //   },
                                                                        //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        // );

                                                                        // if (item && item.cartItems && item.cartItems.length > 0) {
                                                                        //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                                                        //     if (!item.cartItems[bdIndex].isDocket) {
                                                                        //       await printDocketForKD(
                                                                        //         {
                                                                        //           userOrder: item,
                                                                        //           cartItem: item.cartItems[bdIndex],
                                                                        //         },
                                                                        //         // true,
                                                                        //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                        //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                        //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                        //         // deliveredUser,
                                                                        //       );
                                                                        //     }
                                                                        //   }

                                                                        //   for (let index = 0; index < item.cartItems.length; index++) {
                                                                        //     if (item.cartItems[index].isDocket) {
                                                                        //       await printDocket(
                                                                        //         {
                                                                        //           userOrder: item,
                                                                        //           cartItem: item.cartItems[index],
                                                                        //         },
                                                                        //         // true,
                                                                        //         [PRINTER_USAGE_TYPE.RECEIPT],
                                                                        //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                        //         // deliveredUser,
                                                                        //       );
                                                                        //     }
                                                                        //   }
                                                                        // }
                                                                    }
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                ) : (
                                                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Reprint\nOS`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            {selectedOrder.orderType === ORDER_TYPE.DELIVERY &&
                                                                selectedOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ? (
                                                                <TouchableOpacity
                                                                    onPress={() => {
                                                                        setCurrToManageOrder(selectedOrder);
                                                                        setDeliveryFeeNew(selectedOrder.deliveryFee);
                                                                        setDeliveryQuotation({
                                                                            totalFee: selectedOrder.deliveryFee,
                                                                        });

                                                                        setManageSenderModal(true);
                                                                    }}
                                                                    style={[
                                                                        {
                                                                            height: '100%',
                                                                            justifyContent: 'center',
                                                                            alignItems: 'center',
                                                                            backgroundColor: Colors.tabGold,
                                                                            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                            width: 75,
                                                                            marginLeft: 25,
                                                                            borderRadius: 10,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                width: windowWidth * 0.08,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {switchMerchant ? (
                                                                        <MaterialIcons
                                                                            name="delivery-dining"
                                                                            size={10}
                                                                            color={Colors.whiteColor}
                                                                            style={{ marginTop: 10 }}
                                                                        />
                                                                    ) : (
                                                                        <MaterialIcons
                                                                            name="delivery-dining"
                                                                            size={40}
                                                                            color={Colors.whiteColor}
                                                                            style={{ marginTop: 10 }}
                                                                        />
                                                                    )}
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                color: Colors.whiteColor,
                                                                                fontSize: 12,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                                textAlign: 'center',
                                                                                width: '80%',
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        Change Sender
                                                                    </Text>
                                                                </TouchableOpacity>
                                                            ) : null}

                                                            {selectedOrder.orderStatus !== USER_ORDER_STATUS.ORDER_AUTHORIZED &&
                                                                selectedOrder.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARING &&
                                                                selectedOrder.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
                                                                selectedOrder.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED
                                                                ?
                                                                (
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            //   currToPrioritizeOrder: item,
                                                                            //   visible: true,
                                                                            // });
                                                                            setCurrOrderIndex(index);
                                                                            setCurrToAuthorizeOrder(selectedOrder);
                                                                            setModalAuthorizeVisibility(true);
                                                                        }}
                                                                        style={[
                                                                            {
                                                                                height: '100%',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                backgroundColor: '#8fbc8f',
                                                                                //backgroundColor: Colors.tabCyan,
                                                                                underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                width: 75,
                                                                                marginLeft: 25,
                                                                                borderRadius: 10,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: windowWidth * 0.08,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {switchMerchant ? (
                                                                            <Feather
                                                                                name="check"
                                                                                size={10}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        ) : (
                                                                            <Feather
                                                                                name="check"
                                                                                size={40}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        )}

                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    color: Colors.whiteColor,
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    textAlign: 'center',
                                                                                    width: '80%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            Authorize Order
                                                                        </Text>
                                                                    </TouchableOpacity>
                                                                ) : null}
                                                        </View>
                                                    ) : (<></>)}
                                                </View>
                                                :
                                                <View style={{ width: '100%', alignItems: 'flex-start', marginLeft: -35 }}>
                                                    {index === selectedOrder.cartItems.length - 1 ? (
                                                        <View
                                                            style={{
                                                                justifyContent: 'flex-start',
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                                height: 100,
                                                                marginHorizontal: 10,
                                                                marginTop: 20,
                                                            }}>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    // setState({
                                                                    //   currToPrioritizeOrder: item,
                                                                    //   visible: true,
                                                                    // });
                                                                    setCurrOrderIndex(index);
                                                                    setCurrToPrioritizeOrder(item);
                                                                    setVisible(true);
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.primaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <MaterialCommunityIcons
                                                                        name="message-alert-outline"
                                                                        size={10}
                                                                        color={Colors.whiteColor}
                                                                        style={{ marginTop: 10 }}
                                                                    />
                                                                ) : (
                                                                    <MaterialCommunityIcons
                                                                        name="message-alert-outline"
                                                                        size={40}
                                                                        color={Colors.whiteColor}
                                                                        style={{ marginTop: 10 }}
                                                                    />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Prioritize\nOrder`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            {/* {selectedOrder.orderType === ORDER_TYPE.DINEIN ?
                                                                (
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            requestAnimationFrame(() => {
                                                                                CommonStore.update((s) => {
                                                                                    s.selectedOutletTable = selectedOrder;

                                                                                    // Herks - 2022-06-08

                                                                                    s.isCheckingOutTakeaway = false;
                                                                                    s.checkingOutTakeawayOrder = {};
                                                                                });

                                                                                //////////////////////////////////

                                                                                // need set the user list dropdown also

                                                                                var selectedOrderToPayUserListTemp = [];
                                                                                var selectedOrderToPayUserIdDictTemp = {};

                                                                                if (userOrdersTableDict[selectedOrder.uniqueId]) {
                                                                                    for (
                                                                                        var i = 0;
                                                                                        i < userOrdersTableDict[selectedOrder.uniqueId].length;
                                                                                        i++
                                                                                    ) {
                                                                                        const userOrder = userOrdersTableDict[selectedOrder.uniqueId][i];

                                                                                        selectedOrderToPayUserIdDictTemp[userOrder.userId] = {
                                                                                            // label: userOrder.userName ? userOrder.userName : 'Anonymous',
                                                                                            // value: userOrder.userId,
                                                                                            userId: userOrder.userId,
                                                                                            userName: userOrder.userName ? userOrder.userName : 'Guest',
                                                                                            userPhone: userOrder.userPhone ? userOrder.userPhone : '',
                                                                                            userAddress: userOrder.userAddress
                                                                                                ? userOrder.userAddress
                                                                                                : 'N/A',
                                                                                            orderId: userOrder.orderId ? userOrder.orderId : 'N/A',
                                                                                        };

                                                                                        // if (availableUserIdDict[userOrder.userId] === undefined) {
                                                                                        //   // means havent insert to user list
                                                                                        // }
                                                                                    }
                                                                                }

                                                                                selectedOrderToPayUserListTemp = Object.entries(
                                                                                    selectedOrderToPayUserIdDictTemp,
                                                                                ).map(([key, obj]) => {
                                                                                    return {
                                                                                        label: `${obj.userName} #${obj.orderId}`,
                                                                                        value: obj.userId,

                                                                                        userId: obj.userId,
                                                                                        email: obj.email,
                                                                                    };
                                                                                });

                                                                                //////////////////////////////////////////////////////////////

                                                                                // read from the table user id list

                                                                                if (selectedOrder && selectedOrder.payUserIdList && item.payUserIdList.length > 0) {
                                                                                    for (var i = 0; i < selectedOrder.payUserIdList.length; i++) {
                                                                                        var payUserId = selectedOrder.payUserIdList[i];

                                                                                        if (!selectedOrderToPayUserIdDictTemp[payUserId]) {
                                                                                            var crmUser = crmUsers.find(
                                                                                                (user) =>
                                                                                                    user.userId === payUserId ||
                                                                                                    user.email === payUserId,
                                                                                            );

                                                                                            if (crmUser) {
                                                                                                selectedOrderToPayUserIdDictTemp[payUserId] = {
                                                                                                    userId: crmUser.userId || crmUser.email,
                                                                                                    userName: crmUser.name ? crmUser.name : 'Guest',
                                                                                                    userPhone: crmUser.number ? crmUser.number : '',
                                                                                                    userAddress: 'N/A',
                                                                                                    orderId: 'N/A',

                                                                                                    userEmail: crmUser.email ? crmUser.email : '',
                                                                                                };

                                                                                                selectedOrderToPayUserListTemp.push({
                                                                                                    label: crmUser.name,
                                                                                                    value: crmUser.userId || crmUser.email,

                                                                                                    userId: crmUser.userId,
                                                                                                    email: crmUser.email,
                                                                                                });

                                                                                                // setSelectedVoucherUserIdList([
                                                                                                //   {
                                                                                                //     uniqueId: crmUser.uniqueId,
                                                                                                //     value: crmUser.userId || crmUser.email,
                                                                                                //   },
                                                                                                //   ...selectedVoucherUserIdList,
                                                                                                // ]);
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }

                                                                                //////////////////////////////////////////////////////////////

                                                                                // setSelectedOrderToPayUserList(selectedOrderToPayUserListTemp);
                                                                                // setSelectedOrderToPayUserIdDict(selectedOrderToPayUserIdDictTemp);
                                                                                // // setSelectedOrderToPayUserId(selectedOrderToPayUserListTemp[0].value);

                                                                                TableStore.update(s => {
                                                                                    s.selectedOrderToPayUserList = selectedOrderToPayUserListTemp;
                                                                                    s.selectedOrderToPayUserIdDict = selectedOrderToPayUserIdDictTemp;
                                                                                    s.viewTableOrderModal = true;
                                                                                });

                                                                                // CommonStore.update((s) => {
                                                                                //     s.selectedOrderToPayUserId =
                                                                                //         selectedOrderToPayUserListTemp[0].value;
                                                                                // });

                                                                                navigation.navigate('Table');

                                                                                //////////////////////////////////

                                                                                // setViewTableOrderModal(true);       
                                                                            });
                                                                        }}
                                                                        style={[
                                                                            {
                                                                                height: '100%',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                backgroundColor: Colors.tabCyan,
                                                                                underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                width: 75,
                                                                                marginLeft: 25,
                                                                                borderRadius: 10,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: windowWidth * 0.08,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {switchMerchant ? (
                                                                            <MaterialIcons
                                                                                name="payment"
                                                                                size={10}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        ) : (
                                                                            <MaterialIcons
                                                                                name="payment"
                                                                                size={40}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        )}
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    color: Colors.whiteColor,
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    textAlign: 'center',
                                                                                    width: '80%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`Checkout\nOrder`}
                                                                        </Text>
                                                                    </TouchableOpacity>
                                                                ) : <></>} */}
                                                            {
                                                                (currOutlet && currOutlet.privileges &&
                                                                    currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                                                    && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER) ?
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            // setState({
                                                                            //   currToPrioritizeOrder: item,
                                                                            //   visible: true,
                                                                            // });

                                                                            // setCurrOrderIndex(index);
                                                                            // setCurrToCancelOrder(item);
                                                                            // setModalCancelVisibility(true);

                                                                            if (selectedOrder.paymentDetails && (
                                                                                selectedOrder.paymentDetails.txn_ID !== undefined ||
                                                                                selectedOrder.paymentDetails.txnId !== undefined
                                                                            )) {
                                                                                Alert.alert('Info', 'Online paid order is unable to cancel.');
                                                                            }
                                                                            else {
                                                                                setCurrOrderIndex(index);
                                                                                setCurrToCancelOrder(item);
                                                                                setModalCancelVisibility(true);
                                                                            }
                                                                        }}
                                                                        style={[
                                                                            {
                                                                                height: '100%',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                // backgroundColor: Colors.primaryColor,
                                                                                backgroundColor: '#d90000',
                                                                                underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                width: 75,
                                                                                marginLeft: 25,
                                                                                borderRadius: 10,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    width: windowWidth * 0.08,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {switchMerchant ? (
                                                                            <MaterialCommunityIcons
                                                                                name="close"
                                                                                size={10}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        ) : (
                                                                            <MaterialCommunityIcons
                                                                                name="close"
                                                                                size={40}
                                                                                color={Colors.whiteColor}
                                                                                style={{ marginTop: 10 }}
                                                                            />
                                                                        )}

                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    color: Colors.whiteColor,
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-Regular',
                                                                                    textAlign: 'center',
                                                                                    width: windowWidth <= 1133 ? '85%' : '80%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`Cancel\nOrder`}
                                                                        </Text>
                                                                    </TouchableOpacity>
                                                                    : <></>
                                                            }

                                                            {/* 2023-01-30 - For reprint kd */}

                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                                                    var printTimes = 1;

                                                                    if (global.outletCategoriesDict) {
                                                                        if (selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                            for (var i = 0; i < selectedOrder.cartItems.length; i++) {
                                                                                if (global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId] &&
                                                                                    global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum) {
                                                                                    printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                                }
                                                                            }
                                                                        }

                                                                        if (selectedOrder.cartItemsCancelled && selectedOrder.cartItemsCancelled.length > 0) {
                                                                            for (var i = 0; i < selectedOrder.cartItemsCancelled.length; i++) {
                                                                                if (global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId] &&
                                                                                    global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId].printKDNum) {
                                                                                    printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    for (var i = 0; i < printTimes; i++) {
                                                                        await printUserOrder(
                                                                            {
                                                                                orderData: item,
                                                                            },
                                                                            false,
                                                                            [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                            false,
                                                                            false,
                                                                            false,
                                                                            { isInternetReachable: true, isConnected: true },
                                                                            false,
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        printKDSummaryCategoryWrapper(
                                                                            {
                                                                                orderData: item,
                                                                            },
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        if (item && selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                            for (let bdIndex = 0; bdIndex < selectedOrder.cartItems.length; bdIndex++) {
                                                                                if (!selectedOrder.cartItems[bdIndex].isDocket) {
                                                                                    await printDocketForKD(
                                                                                        {
                                                                                            userOrder: item,
                                                                                            cartItem: selectedOrder.cartItems[bdIndex],
                                                                                        },
                                                                                        // true,
                                                                                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                                        [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                                        // deliveredUser,
                                                                                    );
                                                                                }
                                                                            }

                                                                            for (let index = 0; index < selectedOrder.cartItems.length; index++) {
                                                                                if (selectedOrder.cartItems[index].isDocket) {
                                                                                    await printDocket(
                                                                                        {
                                                                                            userOrder: item,
                                                                                            cartItem: selectedOrder.cartItems[index],
                                                                                        },
                                                                                        // true,
                                                                                        [PRINTER_USAGE_TYPE.RECEIPT],
                                                                                        [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                                        // deliveredUser,
                                                                                    );
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    // for (let bdIndex = 0; bdIndex < selectedOrder.cartItems.length; bdIndex++) {
                                                                    //   await printDocketForKD(
                                                                    //     {
                                                                    //       userOrder: item,
                                                                    //       cartItem: selectedOrder.cartItems[bdIndex],
                                                                    //     },
                                                                    //     // true,
                                                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //   );
                                                                    // }

                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    // disconnectPrinter(printer); // no need anymore

                                                                    // await printUserOrder(
                                                                    //   {
                                                                    //     orderId: selectedOrder.uniqueId,
                                                                    //     receiptNote: currOutlet.receiptNote || '',
                                                                    //   },
                                                                    //   false,
                                                                    //   [PRINTER_USAGE_TYPE.RECEIPT],
                                                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //   false,
                                                                    //   false,
                                                                    //   false,
                                                                    //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                                                    //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    // );

                                                                    // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                                                    //   await printUserOrder(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //     false,
                                                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //     false,
                                                                    //     false,
                                                                    //     false,
                                                                    //     { isInternetReachable: true, isConnected: true },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                                                    //   printKDSummaryCategoryWrapper(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                                                    //   for (let bdIndex = 0; bdIndex < selectedOrder.cartItems.length; bdIndex++) {
                                                                    //     await printDocketForKD(
                                                                    //       {
                                                                    //         userOrder: item,
                                                                    //         cartItem: selectedOrder.cartItems[bdIndex],
                                                                    //       },
                                                                    //       // true,
                                                                    //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //     );
                                                                    //   }
                                                                    // }
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                ) : (
                                                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Reprint\nKD`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            {/* 2023-05-08 - For reprint os */}

                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    ///////////////////////////////////////////////////////////////////////////

                                                                    Alert.alert('Info', 'Order summary has been added to print queue');

                                                                    var printTimes = 1;

                                                                    // if (global.outletCategoriesDict) {
                                                                    //   if (selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                    //     for (var i = 0; i < selectedOrder.cartItems.length; i++) {
                                                                    //       if (global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId] &&
                                                                    //         global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum) {
                                                                    //         printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                    //       }
                                                                    //     }
                                                                    //   }

                                                                    //   if (selectedOrder.cartItemsCancelled && selectedOrder.cartItemsCancelled.length > 0) {
                                                                    //     for (var i = 0; i < selectedOrder.cartItemsCancelled.length; i++) {
                                                                    //       if (global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId] &&
                                                                    //         global.outletCategoriesDict[selectedOrder.cartItemsCancelled[i].categoryId].printKDNum) {
                                                                    //         printTimes = global.outletCategoriesDict[selectedOrder.cartItems[i].categoryId].printKDNum;
                                                                    //       }
                                                                    //     }
                                                                    //   }
                                                                    // }

                                                                    for (var i = 0; i < printTimes; i++) {
                                                                        await printUserOrder(
                                                                            {
                                                                                orderData: item,
                                                                            },
                                                                            false,
                                                                            [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                                                            false,
                                                                            false,
                                                                            false,
                                                                            { isInternetReachable: true, isConnected: true },
                                                                            true,
                                                                            [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        );

                                                                        // printKDSummaryCategoryWrapper(
                                                                        //   {
                                                                        //     orderData: item,
                                                                        //   },
                                                                        //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                                                        // );

                                                                        // if (item && selectedOrder.cartItems && selectedOrder.cartItems.length > 0) {
                                                                        //   for (let bdIndex = 0; bdIndex < selectedOrder.cartItems.length; bdIndex++) {
                                                                        //     if (!selectedOrder.cartItems[bdIndex].isDocket) {
                                                                        //       await printDocketForKD(
                                                                        //         {
                                                                        //           userOrder: item,
                                                                        //           cartItem: selectedOrder.cartItems[bdIndex],
                                                                        //         },
                                                                        //         // true,
                                                                        //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                        //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                        //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                        //         // deliveredUser,
                                                                        //       );
                                                                        //     }
                                                                        //   }

                                                                        //   for (let index = 0; index < selectedOrder.cartItems.length; index++) {
                                                                        //     if (selectedOrder.cartItems[index].isDocket) {
                                                                        //       await printDocket(
                                                                        //         {
                                                                        //           userOrder: item,
                                                                        //           cartItem: selectedOrder.cartItems[index],
                                                                        //         },
                                                                        //         // true,
                                                                        //         [PRINTER_USAGE_TYPE.RECEIPT],
                                                                        //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                                        //         // deliveredUser,
                                                                        //       );
                                                                        //     }
                                                                        //   }
                                                                        // }
                                                                    }
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                ) : (
                                                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Reprint\nOS`}
                                                                </Text>
                                                            </TouchableOpacity>
                                                        </View>
                                                    ) : (
                                                        <></>
                                                    )}
                                                </View>
                                            }
                                        </>;
                                    })}
                                </View>
                                {/* ) : null} */}
                            </View>
                        </View>
                    </ScrollView>
                    :
                    <View
                        style={{
                            // backgroundColor: 'red',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',

                            height: '100%',
                        }}>
                        <View
                            style={{
                                marginBottom: Platform.OS === 'ios' ? '15%' : '10%',
                            }}>
                            <Text style={{
                                fontFamily: 'NunitoSans-SemiBold',
                                fontSize: switchMerchant ? 10 : 16,
                            }}>
                                Please select an order
                            </Text>
                        </View>
                    </View>
                }
            </View>
        </UserIdleWrapper>)
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.whiteColor,
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        backgroundColor: Colors.whiteColor,
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#c4c4c4',
        borderRadius: 20,
        paddingVertical: 20,
        paddingHorizontal: 20,
        marginRight: 10,
        marginBottom: 10,
        width: (Dimensions.get('window').width - 150) / 2,
    },
    tablebox: {
        backgroundColor: Colors.whiteColor,
        shadowColor: '#c4c4c4',
        shadowOffset: {
            width: 8,
            height: 8,
        },
        shadowOpacity: 0.55,
        shadowRadius: 10.32,
        width: 100,
        height: 100,
        marginRight: 25,
        borderRadius: 10,
        marginBottom: 30,
        marginTop: 10,
        marginHorizontal: 20,
    },

    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    confirmBox: {
        width: 350,
        height: 237,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default OdOrderDetails;
