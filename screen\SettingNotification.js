import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Switch,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import {
  isTablet, parseImagePickerResponse
} from '../util/common';
import { MERCHANT_VOUCHER_CODE_FORMAT, MERCHANT_VOUCHER_TYPE, SEGMENT_TYPE, EXPAND_TAB_TYPE, } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import AsyncImage from '../components/asyncImage';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';



/////////////////////////////////////////////////////////////////////////////////////

const SettingNotification = props => {
  const {
    navigation,
  } = props;

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const [viewNotification, setViewNotification] = useState(true);
  const [addNotification, setAddNotification] = useState(false);
  const [uploadImage, setUploadImage] = useState(false);
  //Show flat list
  const [notificationList, setNotificationList] = useState(true);
  //Notification Items
  //  const [notificationItems, setNotificationItems] = useState([
  //    {
  //      notificationId: '',
  //      targetCustomer: '',
  //      subject: '',
  //      message: '',
  //    }
  //  ])

  const [expandSelection, setExpandSelection] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [notificationSubject, setNotificationSubject] = useState(''); //Subject
  const [notificationMessage, setNotificationMessage] = useState(''); //Message
  const [targetCustomer, settargetCustomer] = useState(''); //Target Customer
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [rev_date, setRev_date] = useState(moment().startOf('day')); //show date
  const [rev_time, setRev_time] = useState(moment().startOf('day')); //show time

  const [search, setSearch] = useState('')

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {

  }, []);

  const setState = () => { };

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });


  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Notification
        </Text>
      </View>
    ),
    headerRight: () => (
      <TouchableOpacity onPress={() => {
        if (global.currUserRole === 'admin') {
          navigation.navigate('Setting');
        }
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          // backgroundColor: 'red',
        }}>
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.whiteColor,
            }}>
            {merchantName}
          </Text>

          <View style={{
            backgroundColor: 'white',
            width: 0.5,
            height: Dimensions.get('screen').height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />

          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}>
            {userName}
          </Text>

          <View style={{
            marginRight: 30,
            width: Dimensions.get('screen').height * 0.05,
            height: Dimensions.get('screen').height * 0.05,
            borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
          }}>
            <Image style={{
              width: Dimensions.get('screen').height * 0.035,
              height: Dimensions.get('screen').height * 0.035,
              alignSelf: 'center',
            }} source={require('../assets/image/profile-pic.jpg')} />
          </View>
        </View>

      </TouchableOpacity>
    ),
  });


  ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
  ////Function Start Here

  const threeDotButtonPressed = () => {
    setExpandSelection(true)
  }

  const editTapped = () => {
    return (
      Alert.alert(
        "Edit",
        "Do you want to edit this Notification?",
        [
          {
            text: 'YES',
            onPress: () => {

            }
          },
          {
            text: 'NO',
            onPress: () => {

            }
          }
        ]
      )
    );
  }

  const deleteTapped = () => {
    return (
      Alert.alert(
        "Alert",
        "Do you want to remove this Notification?",
        [
          {
            text: 'Yes',
            onPress: () => {

            }
          },
          {
            text: 'No',
            onPress: () => { }
          }
        ]
      )
    );
  }

  const publishNotification = async () => {
    if (!notificationSubject && !notificationMessage && !image && !imageType) {
      //whenever you call state the variable has to be already declared above
      //if not show alert
      Alert.alert(
        'Error',
        'Please fill in the information',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    }
    else {
      var body = {
        subject: notificationSubject,
        message: notificationSubject,
        targetCustomer,
        //Date:  , 
        //TIme  ,
        url: image,
      }
    }
  }

  const handleUploadPhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));
        setIsImageChanged(true);
      }
    });
  }

  const renderItem = ({ item, index }) => (
    <View style={{
      backgroundColor: '#FFFFFF',
      paddingVertical: 10,
      paddingHorizontal: 3,
      paddingLeft: 1,
      borderColor: '#BDBDBD',
      borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      height: 100,
    }}>

      <View style={{ flexDirection: 'row', marginTop: 5, marginBottom: 5, alignItems: 'center', flex: 1 }}>

        <View style={{ flex: 1.2 }}>
          <Image
            //source={{ uri: logo }}
            style={{
              alignSelf: 'center',
              width: 95,
              height: 70,
              backgroundColor: Colors.secondaryColor,
              borderRadius: 5,
            }} />
        </View>

        <View style={{ flex: 2 }}>
          <Text style={{ fontSize: 13, fontWeight: '500', textAlign: 'left', color: Colors.primaryColor, textDecorationLine: 'underline' }}>Black Friday</Text>
        </View>

        <View style={{ flexDirection: 'column', flex: 2 }}>
          <Text style={{ fontSize: 13, fontWeight: '500', textAlign: 'left' }}>May 26-30, 2020</Text>
          <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'left', color: Colors.fieldtTxtColor }}>11.00AM - 10.00PM</Text>
        </View>


        <View style={{ flex: 2 }}>
          <Text style={{ fontSize: 13, fontWeight: '500', textAlign: 'left' }}>Everyone</Text>
        </View>

        <View style={{ flex: 0.5, flexDirection: 'row' }}>
          <TouchableOpacity style={{}}
            onPress={() => { threeDotButtonPressed(true); setExpandSelection(!expandSelection); }}>
            <Entypo name='dots-three-vertical' size={25} color={Colors.tabGrey} />
          </TouchableOpacity>
          {expandSelection ?
            <View style={{
              width: 110, height: 75, marginLeft: -130, marginBottom: -50, zIndex: 1000, flexDirection: 'column', backgroundColor: '#FFFFFF', borderRadius: 7, shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>

              <TouchableOpacity style={{ flexDirection: 'row', height: '49%', justifyContent: 'center', alignItems: 'center' }}
                onPress={() => { editTapped() }}>
                <View style={{ width: '30%', paddingLeft: 12 }}>
                  {/* <MaterialIcons name='edit' size={17} color='darkgreen' /> */}
                  <FontAwesome5 name='edit' size={17} color={Colors.primaryColor} />
                </View>
                <View style={{ width: '70%' }}>
                  <Text style={{ marginLeft: 5 }}>
                    Edit
                  </Text>
                </View>
              </TouchableOpacity>

              <View style={{ borderWidth: 1, borderColor: Colors.fieldtBgColor }} />

              <TouchableOpacity style={{ flexDirection: 'row', height: '49%', justifyContent: 'center', alignItems: 'center' }}
                onPress={() => { deleteTapped() }}>
                <View style={{ width: '30%', paddingLeft: 12 }}>
                  <MaterialCommunityIcons name='delete-sweep' size={17} color='grey' />
                </View>
                <View style={{ width: '70%' }}>
                  <Text style={{ marginLeft: 5 }}>
                    Delete
                  </Text>
                </View>
              </TouchableOpacity>

            </View>
            : null}

        </View>
      </View>
    </View>
  )

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
  //Render Start Here
  return (
    <View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {
    }]}>
      {/* <View style={[styles.sidebar, !isTablet() ? {
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={10} expandSettings />
      </View> */}

      {viewNotification ? (
        <View style={styles.list}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: 20,
            marginTop: 5,
            //backgroundColor: 'red',
            height: '10%',
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            paddingHorizontal: 10,
          }}>
            <View style={{ width: '55%', justifyContent: 'center' }}>
              <Text style={{ fontFamily: 'Nunitosans-bold', fontSize: 18, }}>
                Summary
              </Text>
            </View>
            <TouchableOpacity style={{ height: 35, borderRadius: 7, backgroundColor: Colors.primaryColor, justifyContent: 'center' }}
              onPress={() => {
                setViewNotification(false)
                setAddNotification(true)
              }}
            >
              <View style={{ flexDirection: 'row', paddingHorizontal: 10, alignContent: 'center', alignItems: 'center' }}>
                <View style={{ width: 20, height: 20, justifyContent: 'center', backgroundColor: Colors.whiteColor, borderRadius: 100 }}>
                  <Icon name='add' size={19} style={{ color: Colors.primaryColor, alignSelf: 'center', paddingLeft: 1.5 }} />
                </View>
                <Text style={{ marginLeft: 10, fontFamily: 'Nunitosans-Bold', color: Colors.whiteColor, fontSize: 16 }}>
                  Add Notification
                </Text>
              </View>
            </TouchableOpacity>

          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginHorizontal: 40, }}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'center',
              width: '25%',
              height: 35,
              alignItems: 'center',
              borderWidth: 1,
              borderRadius: 5,
              borderColor: '#E5E5E5',
              alignSelf: 'center'
            }}>
              <View style={{ flex: 3 }}>
                <TextInput placeholder='Search'
                  style={{ marginLeft: 10 }}
                  onChangeText={(text) => {
                    setSearch(text);
                  }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  value={search}
                />
              </View>
              <View style={{ flex: 1, height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.primaryColor, borderRadius: 5 }}>
                <Icon name='search' size={21} color={Colors.whiteColor} />
              </View>
            </View>
          </View>


          {/* ~~~~~~~~~~~~~~~~~List View Start Here~~~~~~~~~~~~~~~~~~~~~ */}
          <View style={{ width: '100%', marginTop: 15 }}>
            <View style={{ backgroundColor: Colors.whiteColor, padding: 12, paddingTop: 0, height: '82%' }}>
              <View style={{ flexDirection: 'row' }}>

                <View style={{ flexDirection: 'row', flex: 1.2, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={{ fontSize: 14, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }} />
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={{ fontSize: 14, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>Title</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={{ fontSize: 14, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>Schedule on:</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 2, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={{ fontSize: 14, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>Target Customer(s)</Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', flex: 0.5, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'flex-start' }}>
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={{ fontSize: 14, color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }} />
                  </View>
                </View>

              </View>
              {notificationList ? (

                <FlatList
                  data={renderItem}
                  //   Search Function
                  //   data={transactionTypeSalesDetails.filter(item => {
                  //     if (search !== '') {
                  //         return item.orderType.toLowerCase().includes(search.toLowerCase());
                  //     }
                  //     else {
                  //         return true;
                  //     }
                  // }).slice((currentPage - 1) * perPage, currentPage * perPage)}
                  renderItem={renderItem}
                  keyExtractor={(item, index) => String(index)}
                  style={{ marginTop: 10 }}
                />

              ) : null}
            </View>
          </View>

        </View>
      ) : null}

      {/* Add Notification View */}
      {addNotification ? (
        <ScrollView style={{
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
        }}
        >
          <View style={styles.list}>

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              margin: 20,
              marginTop: 5,
              //backgroundColor: 'red',
              height: '10%',
              borderBottomWidth: StyleSheet.hairlineWidth,
              borderBottomColor: '#c4c4c4',
              paddingHorizontal: 10,
            }}>
              <View style={{ width: '55%', justifyContent: 'center' }}>
                <Text style={{ fontFamily: 'Nunitosans-bold', fontSize: 18, }}>
                  Notification Announcement
                </Text>
              </View>
              <TouchableOpacity style={{ height: 35, borderRadius: 7, backgroundColor: Colors.primaryColor, justifyContent: 'center' }}
                onPress={() => { publishNotification() }}
              >
                <View style={{ flexDirection: 'row', paddingHorizontal: 10, alignContent: 'center', alignItems: 'center' }}>
                  <Text style={{ fontFamily: 'Nunitosans-Bold', color: Colors.whiteColor, fontSize: 16 }}>
                    Publish
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            <View style={{ justifyContent: 'flex-start', marginLeft: 30 }}>
              <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center' }}
                onPress={() => { setViewNotification(true); setAddNotification(false) }}>
                <Ionicons name='chevron-back' size={23} color={Colors.primaryColor} />
                <Text style={{ color: Colors.primaryColor, fontSize: 15, fontWeight: '500' }}>
                  Back
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ alignItems: 'center', marginTop: 15 }}>
              <View style={{
                borderWidth: 1,
                borderRadius: 5,
                borderColor: '#E5E5E5',
                width: '60%',
                padding: 15,
              }}
              >

                <View style={{ flexDirection: 'column', margin: 10 }}>
                  <Text style={{ fontWeight: '500', marginBottom: 5 }}>
                    Schedule on:
                  </Text>
                  <View style={{ flexDirection: 'row' }}>
                    {/* Show Date  */}
                    <DateTimePickerModal
                      isVisible={showDatePicker}
                      mode={'date'}
                      onConfirm={(text) => {
                        setRev_date(moment(text));
                        setShowDatePicker(false);
                      }}
                      onCancel={() => {
                        setShowDatePicker(false);
                      }}
                    />
                    {/* Show Time */}
                    <DateTimePickerModal
                      isVisible={showTimePicker}
                      mode={'time'}
                      onConfirm={(text) => {
                        setRev_time(moment(text));
                        setShowTimePicker(false);
                      }}
                      onCancel={() => {
                        setShowTimePicker(false);
                      }}
                    />

                    <TouchableOpacity onPress={() => { setShowDatePicker(true); }}
                      style={{ flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderRadius: 5, borderColor: '#E5E5E5', height: 40, width: 170, justifyContent: 'space-between', paddingHorizontal: 10 }}>
                      <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(rev_date).format("DD MMM yyyy")}</Text>
                      <AntDesign name='calendar' size={20} />
                    </TouchableOpacity>

                    <TouchableOpacity onPress={() => { setShowTimePicker(true); }}
                      style={{ flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderRadius: 5, borderColor: '#E5E5E5', height: 40, width: 170, justifyContent: 'space-between', paddingHorizontal: 10, marginHorizontal: 10 }}>
                      <Text style={{ fontFamily: "NunitoSans-Regular" }} >{moment(rev_time).format("hh:MM a")}</Text>
                      <AntDesign name='clockcircleo' size={20} />
                    </TouchableOpacity>

                  </View>
                </View>

                <View style={{ flexDirection: 'column', margin: 10 }}>
                  <Text style={{ fontWeight: '500', marginBottom: 5 }}>
                    Target Customer(s)
                  </Text>
                  <DropDownPicker
                    containerStyle={{ height: 40 }}
                    style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 130, paddingVertical: 0, margin: 0, marginLeft: 0 }}
                    dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 130, paddingVertical: 0 }}
                    arrowSize={20}
                    arrowColor={'black'}
                    arrowStyle={fontWeight = 'bold'}
                    placeholder='Select One'
                    placeholderStyle={{ marginLeft: 0 }}
                    items={[{ label: 'Member', value: 1 }, { label: 'VIP Only', value: 2 }, { label: 'Everyone', value: 3 }]}
                    itemStyle={{ justifyContent: 'flex-start' }}
                    onChangeItem={(item) => {
                      setTargetCustomer(item)
                      // setSupplierItems(supplierItems.map((supplierItem, i) => (i === index ? {
                      //   ...supplierItem,
                      //   unit: item,
                      //   isChanged: true,
                      // } : supplierItem)))
                    }}
                    value={targetCustomer}
                    defaultValue={targetCustomer}
                  />
                </View>

                <View style={{ flexDirection: 'column', margin: 10, zIndex: -1 }}>
                  <Text style={{ fontWeight: '500', }}>
                    Subject:
                  </Text>
                  <TextInput style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: '90%', height: 40, marginTop: 5, paddingLeft: 5 }}
                    placeholder='Subject'
                    value={notificationSubject}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  />
                </View>

                <View style={{ flexDirection: 'column', margin: 10, zIndex: -1 }}>
                  <Text style={{ fontWeight: '500' }}>
                    Message
                  </Text>
                  <TextInput style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: '90%', height: 100, marginTop: 5, textAlignVertical: 'top', paddingLeft: 5 }}
                    placeholder='message...'
                    multiline
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    value={notificationMessage}
                  />
                </View>

                <View style={{ flexDirection: 'column', margin: 10 }}>
                  <Text style={{ fontWeight: '500', marginBottom: 5 }}>
                    Media:
                  </Text>
                  <TouchableOpacity style={{ borderRadius: 10, borderWidth: 1, borderColor: Colors.primaryColor, height: 35, width: 80, marginBottom: 10, alignItems: 'center', justifyContent: 'center', flexDirection: 'row' }}
                    onPress={() => { handleUploadPhoto(), setUploadImage(true) }}
                  >
                    <MaterialCommunityIcons name='upload' color={Colors.primaryColor} size={19} />
                    <Text style={{ color: Colors.primaryColor, fontFamily: 'Nunitosans-Bold', marginLeft: 2 }}>
                      image
                    </Text>
                  </TouchableOpacity>
                  {uploadImage ? (
                    <View style={{ backgroundColor: Colors.secondaryColor, width: 142.5, height: 105, borderRadius: 5 }}>
                      <AsyncImage
                        source={{ uri: image }}
                        style={{ width: 142.5, height: 105 }}
                        hideLoading />

                    </View>
                  ) : null}
                </View>

              </View>
            </View>


          </View>
        </ScrollView>
      ) : null}


    </View>
  )

};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('screen').width * 0.85,
    height: Dimensions.get('screen').height,
    marginTop: 40,
    marginBottom: 10,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default SettingNotification;
