import { Text } from "react-native-fast-text";
import React, { useState, useEffect, createRef } from 'react';
import { StyleSheet, Image, View, Alert, Dimensions, TouchableOpacity } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Styles from '../constant/Styles';
import moment from 'moment';
import {
  isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { ORDER_TYPE, USER_ORDER_STATUS, LALAMOVE_STATUS_PARSED, COURIER_INFO_DICT, COURIER_CODE, COURIER_DROPDOWN_LIST, MRSPEEDY_STATUS_PARSED, EXPAND_TAB_TYPE, } from '../constant/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common'

const GlobalCreditScreen = props => {
  const {
    navigation,
  } = props;

  const [outletId, setOutletId] = useState(User.getOutletId());
  const [table, setTable] = useState([]);
  const [prepareTime, setPrepareTime] = useState([]);
  const [order, setOrder] = useState([]);
  const [expandOrder, setExpandOrder] = useState(false);
  const [sort, setSort] = useState(null);
  const [filter, setFilter] = useState(null);
  const [lastSort, setLastSort] = useState(null);
  const [lastFilter, setLastFilter] = useState(null);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [visible, setVisible] = useState(false);
  const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});

  const [modalCancelVisibility, setModalCancelVisibility] = useState(false);
  const [currToCancelOrder, setCurrToCancelOrder] = useState({});

  const [modalAuthorizeVisibility, setModalAuthorizeVisibility] = useState(false);
  const [currToAuthorizeOrder, setCurrToAuthorizeOrder] = useState({});

  ///////////Sorting////////
  const [sortOrderID, setSortOrderID] = useState();
  const [sortDateTime, setSortDateTime] = useState();
  const [sortCustomerName, setSortCustomerName] = useState();
  const [sortWaitingTime, setSortWaitingTime] = useState();
  const [sortAuthorization, setSortAuthorization] = useState();
  const [sortSender, setSortSender] = useState();
  const [sortPaymentMethod, setSortPaymentMethod] = useState();
  const [sortDeliveryFee, setSortDeliveryFee] = useState();

  //////////////////////Manage Sender//////////////////////

  const [manageSenderModal, setManageSenderModal] = useState(false);
  const [currToManageOrder, setCurrToManageOrder] = useState({});

  const [selectedSender, setSelectedSender] = useState(COURIER_DROPDOWN_LIST[0].value);

  const [deliveryFeeNew, setDeliveryFeeNew] = useState(0);

  const [deliveryQuotation, setDeliveryQuotation] = useState({
    totalFee: 0,
  });

  ////////////////////////////////////////////////////////
  const [refArray, setRefArray] = useState([]);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const [expandViewDict, setExpandViewDict] = useState({});

  const [takeAwayOrders, setTakeAwayOrders] = useState([]);

  const [search, setSearch] = useState('');
  const [topUpAmount, setTopUpAmount] = useState('');

  const userOrders = OutletStore.useState(s => s.userOrders);

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);
  const merchantId = UserStore.useState(s => s.merchantId);
  const isLoading = CommonStore.useState(s => s.isLoading);

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState(s => s.currOutletId);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const allOutlets = MerchantStore.useState(s => s.allOutlets);

  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////
  useEffect(() => {
    if (sortOrderID) { ////////Sort Order ID
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId)))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.orderId.localeCompare(b.orderId)))
    }
  }, [sortOrderID]);

  useEffect(() => {
    if (sortDateTime) { ////////Sort Date/Time Order
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.orderDate - b.orderDate))
    }
  }, [sortDateTime])

  useEffect(() => {
    // console.log('hiii')
    if (sortCustomerName) { ////////Sort Customer Name
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.userName.localeCompare(a.userName)))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.userName.localeCompare(b.userName)))
    }
  }, [sortCustomerName])

  useEffect(() => {
    if (sortWaitingTime) { ///////// Sort Waiting Time
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => (moment().valueOf() - b.estimatedPreparedDate) - (moment().valueOf() - a.estimatedPreparedDate)))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => (moment().valueOf() - a.estimatedPreparedDate) - (moment().valueOf() - b.estimatedPreparedDate)))
    }
  }, [sortWaitingTime])

  useEffect(() => {
    if (sortAuthorization) { ////////Sort Authorization / Status of the Order
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.orderStatus.localeCompare(b.orderStatus)))
    }
  }, [sortAuthorization])

  useEffect(() => {
    if (sortSender) { ///////// Sort Sender/ Courier
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.courierCode.localeCompare(a.courierCode)))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.courierCode.localeCompare(b.courierCode)))
    }
  }, [sortSender])

  useEffect(() => {
    if (sortPaymentMethod) { ///////// Sort Payment Gateway/ Method
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => (b.paymentDetails ? b.paymentDetails.channel : 'N/A').localeCompare((a.paymentDetails ? a.paymentDetails.channel : 'N/A'))))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => (a.paymentDetails ? a.paymentDetails.channel : 'N/A').localeCompare(b.paymentDetails ? b.paymentDetails.channel : 'N/A')))
    }
  }, [sortPaymentMethod])

  useEffect(() => {
    if (sortDeliveryFee) { ////////// Sort Total Price of the Order
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.deliveryFee - a.deliveryFee))
    }
    else {
      setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => a.deliveryFee - b.deliveryFee))
    }
  }, [sortDeliveryFee])


  useEffect(() => {
    setTakeAwayOrders(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN));

    // console.log('takeAwayOrders');
    // console.log(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN));
  }, [userOrders]);

  useEffect(() => {
    setRefArray(ref => (
      Array(takeAwayOrders.length).fill().map((_, i) => ref[i] || createRef())
    ));
  }, [takeAwayOrders.length]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     setRefreshRate(new Date());

  //     checkOvertimeOrders();
  //   }, 30000);

  //   checkOvertimeOrders();
  // }, [refreshRate]);

  useEffect(() => {
    if (manageSenderModal) {
      if (currToManageOrder.crUserAddress && currOutlet) {
        // valid order to proceed

        if (selectedSender === COURIER_CODE.LALAMOVE) {
          var body = {
            outletLat: currToManageOrder.crOutletLat,
            outletLng: currToManageOrder.crOutletLng,
            outletAddress: currToManageOrder.crOutletAddress,

            outletPhone: currToManageOrder.crOutletPhone,
            outletName: currOutlet.name,

            userLat: currToManageOrder.crUserLat,
            userLng: currToManageOrder.crUserLng,
            userAddress: currToManageOrder.crUserAddress,

            userName: currToManageOrder.crUserName,
            userPhone: currToManageOrder.crUserPhone,
            userRemarks: currToManageOrder.crUserRemarks,

            // scheduleAt: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
          };

          // console.log('quotation body');
          // console.log(body);

          ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
            // console.log("lalamove quotation result");
            // console.log(result);

            if (result === undefined) {
              // means lalamove can't deliver to this address

              Alert.alert(
                'Info',
                'Sorry, we are unable to deliver to this address \nPlease try another one',
              );
            }
            else if (result && result.totalFee) {
              // { totalFee: "0.00", totalFeeCurrency: "MYR" }

              setDeliveryQuotation({
                totalFee: parseFloat(result.totalFee),
                totalFeeCurrency: result.totalFeeCurrency,
                courierCode: COURIER_CODE.LALAMOVE,
              });
            }
          });
        }
        else if (selectedSender === COURIER_CODE.MRSPEEDY) {
          var body = {
            outletLat: currToManageOrder.crOutletLat,
            outletLng: currToManageOrder.crOutletLng,
            outletAddress: currToManageOrder.crOutletAddress,

            outletPhone: currToManageOrder.crOutletPhone,
            outletName: currOutlet.name,

            userLat: currToManageOrder.crUserLat,
            userLng: currToManageOrder.crUserLng,
            userAddress: currToManageOrder.crUserAddress,

            userName: currToManageOrder.crUserName,
            userPhone: currToManageOrder.crUserPhone,
            userRemarks: currToManageOrder.crUserRemarks,

            totalWeightKg: currToManageOrder.totalWeightKg,
            // outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
            // outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
            // userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
            // userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
          };

          // console.log('quotation body');
          // console.log(body);

          ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
            // console.log("mr speedy quotation result");
            // console.log(result);

            if (!result || !result.is_successful) {
              // means lalamove can't deliver to this address

              Alert.alert(
                'Info',
                'Sorry, we are unable to deliver to this address \nPlease try another one',
              );
            }
            else if (result.is_successful && result.order && result.order.payment_amount) {
              // { totalFee: "0.00", totalFeeCurrency: "MYR" }

              setDeliveryQuotation({
                totalFee: parseFloat(result.order.payment_amount),
                totalFeeCurrency: 'MYR',
                courierCode: COURIER_CODE.MRSPEEDY,
              });
            }
          });
        }
      }
      else {
        // do nothing for now
      }
    }
  }, [manageSenderModal, currToManageOrder, selectedSender, currOutlet]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Global Credit
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   getTakeAwayList();
  //   setInterval(() => {
  //     getTakeAwayList();
  //   }, 5000);
  //   //getTakeAwayList();

  // }

  const checkOvertimeOrders = async () => {
    for (var i = 0; i < takeAwayOrders.length; i++) {
      const waitingTime = ((moment().valueOf() - takeAwayOrders[i].estimatedPreparedDate) / (1000 * 60));

      // if (waitingTime >= 300) {
      //   await cancelOrder(takeAwayOrders[i], false);
      // }
    }
  };

  //   const prioritizeOrder = (param) => {
  //     var body = {
  //       orderId: param
  //     };

  //     // Alert.alert(
  //     //   'Success',
  //     //   'The order had been prioritized',
  //     //   [{ text: 'OK', onPress: () => { } }],
  //     //   { cancelable: false },
  //     // );
  //     // // setState({ visible: false });
  //     // setVisible(false);

  //     ApiClient.POST(API.prioritizeOrder, body, false).then((result) => {
  //       if (result !== null) {
  //         Alert.alert(
  //           'Success',
  //           'The takeaway had been prioritized',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //         // setState({ visible: false });
  //         setVisible(false);
  //       } else {
  //         Alert.alert(
  //           "Failed",
  //           'The request is failed',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //         // setState({ visible: false });
  //         setVisible(false);
  //       }
  //     });
  //   }

  //   const cancelOrder = async (param, showAlert = true) => {
  //     var body = {
  //       orderId: param.uniqueId,
  //       tableId: param.tableId,
  //     };
  //     ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then((result) => {
  //       if (result && result.status === 'success') {
  //         if (showAlert) {
  //           Alert.alert(
  //             'Success',
  //             'The takeaway had been cancelled',
  //             [{ text: 'OK', onPress: () => { } }],
  //             { cancelable: false },
  //           );
  //           // setState({ visible: false });
  //           // setVisible(false);
  //           setModalCancelVisibility(false);
  //         }
  //       } else {
  //         if (showAlert) {
  //           Alert.alert(
  //             "Failed",
  //             result.message || 'The request is failed',
  //             [{ text: 'OK', onPress: () => { } }],
  //             { cancelable: false },
  //           );
  //           // setState({ visible: false });
  //           // setVisible(false);
  //           setModalCancelVisibility(false);
  //         }
  //       }
  //     });
  //   }

  //   const authorizeOrder = async (param, showAlert = true) => {
  //     var body = {
  //       orderId: param.uniqueId,
  //       // tableId: param.tableId,
  //     };
  //     ApiClient.POST(API.authorizeUserOrderByMerchant, body, false).then((result) => {
  //       if (result && result.status === 'success') {
  //         if (showAlert) {
  //           Alert.alert(
  //             'Success',
  //             'The takeaway had been authorized.',
  //             [{ text: 'OK', onPress: () => { } }],
  //             { cancelable: false },
  //           );
  //           // setState({ visible: false });
  //           // setVisible(false);
  //           setModalAuthorizeVisibility(false);
  //         }
  //       } else {
  //         if (showAlert) {
  //           Alert.alert(
  //             "Failed",
  //             'The request is failed',
  //             [{ text: 'OK', onPress: () => { } }],
  //             { cancelable: false },
  //           );
  //           // setState({ visible: false });
  //           // setVisible(false);
  //           setModalAuthorizeVisibility(false);
  //         }
  //       }
  //     });
  //   }

  //   const getTakeAwayList = () => {
  //     ApiClient.GET(API.getCurrentTakeAwayOrder + User.getOutletId()).then(result => {
  //       var takeAwayList = result;

  //       if (unfilteredOrder && unfilteredOrder.length > 0) {
  //         var diff = false;

  //         if (takeAwayList.length !== unfilteredOrder.length) {
  //           diff = true;
  //         }
  //         else {
  //           for (var i = 0; i < takeAwayList.length; i++) {
  //             if (takeAwayList[i].id !== unfilteredOrder[i].id) {
  //               diff = true;
  //               break;
  //             }
  //           }
  //         }

  //         diff && setState({ order: [...takeAwayList], unfilteredOrder: [...takeAwayList] });
  //       }
  //       else {
  //         setState({ order: [...takeAwayList], unfilteredOrder: [...takeAwayList] });
  //       }

  //       // setState({ order: result, unfilteredOrder: result })
  //     })
  //   }


  //   const filterOrders = (param) => {
  //     if (param.value == 0) { // All orders
  //       setTakeAwayOrders(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN));
  //     }

  //     if (param.value == 1) { //Awaiting Authorizaion
  //       setTakeAwayOrders(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN && order.paymentDetails === null));
  //     }

  //     if (param.value == 2) { //Paid
  //       setTakeAwayOrders(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN && order.paymentDetails !== null));
  //     }
  //   }

  //   const expandOrderFunc = (param) => {
  //     if (expandOrder == false) {
  //       // return setState({ expandOrder: true }), param.expandOrder = true;
  //       setExpandOrder(true);
  //       setExpandViewDict({
  //         ...expandViewDict,
  //         [param.uniqueId]: true,
  //       });
  //       expandViewDict;
  //     } else {
  //       // return setState({ expandOrder: false }), param.expandOrder = false;
  //       setExpandOrder(false);
  //       setExpandViewDict({
  //         ...expandViewDict,
  //         [param.uniqueId]: undefined,
  //       });
  //     }
  //   }

  //   const bearCost = () => {
  //     CommonStore.update(s => {
  //       s.isLoading = true;
  //     });

  //     var body = {
  //       orderId: currToManageOrder.uniqueId,
  //       merchantId: merchantId,

  //       deliveryFeeDiff: Math.max(deliveryQuotation.totalFee - currToManageOrder.deliveryFee, 0),

  //       deliveryFee: deliveryQuotation.totalFee,
  //       courierCode: deliveryQuotation.courierCode,
  //       deliveryCurrency: deliveryQuotation.totalFeeCurrency,
  //     };

  //     ApiClient.POST(API.updateUserOrderCourier, body, {
  //       timeout: 10000,
  //     }).then(async (result) => {
  //       // console.log("updateUserOrderCourier");
  //       // console.log(result)

  //       if (result) {
  //         if (result.status === 'success') {
  //           Alert.alert(
  //             "Success",
  //             "Sender for this order changed successfully.",
  //             [
  //               {
  //                 text: "OK",
  //                 onPress: () => {
  //                 },
  //               },
  //             ],
  //             { cancelable: false }
  //           );
  //         }
  //         else {
  //           Alert.alert(
  //             "Error",
  //             result.message,
  //             [
  //               {
  //                 text: "OK",
  //                 onPress: () => {
  //                 },
  //               },
  //             ],
  //             { cancelable: false }
  //           );
  //         }
  //       }
  //       else {
  //         Alert.alert(
  //           "Error",
  //           "Failed to change the sender for this order.",
  //           [
  //             {
  //               text: "OK",
  //               onPress: () => {
  //               },
  //             },
  //           ],
  //           { cancelable: false }
  //         );
  //       }

  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     });
  //   }

  //   const notifyUser = () => {
  //     CommonStore.update(s => {
  //       s.isLoading = true;
  //     });

  //     var body = {
  //       orderId: currToManageOrder.uniqueId,
  //     };

  //     ApiClient.POST(API.notifyUserOrderCourierAction, body, false).then((result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'The user of this order had been notified.',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //       } else {
  //         Alert.alert(
  //           "Failed",
  //           result.message || 'The request is failed',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //       }

  //       setManageSenderModal(false);

  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     });
  //   }

  //   const cancelAndRefund = () => {
  //     CommonStore.update(s => {
  //       s.isLoading = true;
  //     });

  //     var body = {
  //       orderId: currToManageOrder.uniqueId,
  //       tableId: currToManageOrder.tableId,
  //     };

  //     ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then((result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'The delivery order had been cancelled, and refund process will take several working days to completed.',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //       } else {
  //         Alert.alert(
  //           "Failed",
  //           result.message || 'The request is failed',
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //       }

  //       setManageSenderModal(false);

  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     });
  //   }

  //   const rightAction = (item) => {
  //     return (
  //       <View style={{
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //         flexDirection: 'row',
  //         //width: '32%',
  //       }}>
  //         <TouchableOpacity
  //           onPress={() => {
  //             // setState({
  //             //   currToPrioritizeOrder: item,
  //             //   visible: true,
  //             // });
  //             setCurrToPrioritizeOrder(item);
  //             setVisible(true);
  //           }}
  //           style={{
  //             height: '100%',
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //             alignContent: 'center',
  //             alignSelf: "center",
  //             backgroundColor: Colors.primaryColor,
  //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //             paddingBottom: 6,
  //             width: 75,
  //           }}>
  //           <MaterialCommunityIcons
  //             name="message-alert-outline"
  //             size={40}
  //             color={Colors.whiteColor}
  //             style={{ marginTop: 10 }}
  //           />
  //           <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>Prioritize Order</Text>

  //         </TouchableOpacity>

  //         <TouchableOpacity
  //           onPress={() => {
  //             // setState({
  //             //   currToPrioritizeOrder: item,
  //             //   visible: true,
  //             // });
  //             setCurrToCancelOrder(item);
  //             setModalCancelVisibility(true);
  //           }}
  //           style={{
  //             height: '100%',
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //             alignContent: 'center',
  //             alignSelf: "center",
  //             // backgroundColor: Colors.primaryColor,
  //             backgroundColor: '#d90000',
  //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //             paddingBottom: 6,
  //             width: 75,
  //           }}>
  //           <MaterialCommunityIcons
  //             name="close"
  //             size={40}
  //             color={Colors.whiteColor}
  //             style={{ marginTop: 10 }}
  //           />
  //           <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>Cancel Order</Text>

  //         </TouchableOpacity>

  //         {item.orderType === ORDER_TYPE.DELIVERY
  //           ?
  //           <TouchableOpacity
  //             onPress={() => {
  //               setCurrToManageOrder(item);
  //               setDeliveryFeeNew(item.deliveryFee);
  //               setDeliveryQuotation({
  //                 totalFee: item.deliveryFee,
  //               });

  //               setManageSenderModal(true);
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: "center",
  //               //backgroundColor: '#8fbc8f',
  //               backgroundColor: Colors.tabGold,
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             <MaterialIcons
  //               name="delivery-dining"
  //               size={40}
  //               color={Colors.whiteColor}
  //               style={{ marginTop: 10 }}
  //             />
  //             <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>
  //               Manage Sender
  //             </Text>
  //           </TouchableOpacity>

  //           : null}

  //         <TouchableOpacity
  //           onPress={() => {
  //             // setState({
  //             //   currToPrioritizeOrder: item,
  //             //   visible: true,
  //             // });
  //             setCurrToAuthorizeOrder(item);
  //             setModalAuthorizeVisibility(true);
  //           }}
  //           style={{
  //             height: '100%',
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //             alignContent: 'center',
  //             alignSelf: "center",
  //             backgroundColor: '#8fbc8f',
  //             //backgroundColor: Colors.tabCyan,
  //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //             paddingBottom: 6,
  //             width: 75,
  //           }}>
  //           <Feather
  //             name="check"
  //             size={40}
  //             color={Colors.whiteColor}
  //             style={{ marginTop: 10 }}
  //           />
  //           <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>Authorize Order</Text>

  //         </TouchableOpacity>

  //       </View>
  //     )

  //   };

  const topUpWallet = () => {

  }

  const renderOrder = ({ item, index }) => {
    const waitingTime = ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60));

    var courierStatusParsed = '';

    if (item.courierCode === COURIER_CODE.LALAMOVE) {
      courierStatusParsed = LALAMOVE_STATUS_PARSED[item.courierStatus];
    }
    else if (item.courierCode === COURIER_CODE.MRSPEEDY) {
      courierStatusParsed = MRSPEEDY_STATUS_PARSED[item.courierStatus];
    }


    return (
      <View style={{
        paddingVertical: 5,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
      }}>
        <View style={{ elevation: 2, borderRadius: 7, backgroundColor: 'white' }}>

          <View style={{ width: "100%", flexDirection: 'row', height: Dimensions.get('screen').height * 0.1, alignItems: 'center', borderBottomColor: Colors.fieldtT, borderBottomWidth: expandViewDict[item.uniqueId] == true ? StyleSheet.hairlineWidth : null }}>

            <View style={{ width: '12%', marginRight: 3, alignItems: 'center' }}>
              <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId}</Text>
            </View>
            <View style={{ width: '15%', marginHorizontal: 4, alignItems: 'center', justifyContent: 'center' }}>
              <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{moment(item.orderDate).format('DD MMM YYYY')}</Text>
              <Text style={{ color: Colors.fontDark, fontSize: 13, fontFamily: 'NunitoSans-Bold', marginTop: 2 }}>{moment(item.orderDate).format('LT')}</Text>
            </View>
            <View style={{ width: '18%', marginHorizontal: 4, alignItems: 'center' }}>
              <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }} numberOfLines={1}>{item.userName ? item.userName : ''}</Text>
            </View>


            <View style={{ width: '18%', marginHorizontal: 4, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <View style={{
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                {
                  item.courierId
                    ?
                    <>
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <Image style={{ width: 25, height: 25, borderRadius: 5 }}
                          source={COURIER_INFO_DICT[item.courierCode].img}
                        />
                        <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>{COURIER_INFO_DICT[item.courierCode].name}</Text>
                      </View>

                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <Text style={{
                          fontSize: 14, fontFamily: 'NunitoSans-SemiBold',
                          textAlign: 'center', color: Colors.descriptionColor,
                          marginTop: 8,
                          marginLeft: 8,
                        }}>{courierStatusParsed ? courierStatusParsed : 'N/A'}</Text>
                      </View>
                    </>
                    :
                    <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>N/A</Text>
                }
              </View>
            </View>
            <View style={{ width: '15%', marginHorizontal: 4, alignItems: 'center' }}>
              <Text style={{ color: Colors.blackColor, fontFamily: 'NunitoSans-Bold', }}>{item.paymentDetails.channel}</Text>
            </View>
            <View style={{ width: '16%', marginHorizontal: 4, alignItems: 'center' }}>
              <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>RM {(item.deliveryFee).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
            </View>

          </View>


        </View>
      </View>
    );
  }



  return (
    // <View style={styles.container}>
    //   {renderModal(currToPrioritizeOrder)}

    //   <View style={styles.sidebar}>
    (<View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {}]}>
      {/* {renderModal(currToPrioritizeOrder)}
      {renderModalCancel(currToCancelOrder)}
      {renderModalAuthorize(currToAuthorizeOrder)} */}
      {/* <View style={[styles.sidebar, !isTablet() ? {
        width: Dimensions.get('screen').width * 0.08,
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={1} expandOperation={true} />
      </View> */}
      <View style={{ flex: 1, paddingHorizontal: 25, paddingVertical: 30 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', padding: 2, width: '100%', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
            <Text style={{ fontSize: 28, marginRight: 1, fontFamily: 'NunitoSans-Bold' }}>Balance: </Text>
            <Text style={{ fontSize: 28, fontFamily: 'NunitoSans-Bold', }}>RM{' '}100.00</Text>
            {/* <Text style={{ fontSize: 28, fontFamily: 'NunitoSans-Bold', }}>RM{' '}{topUpAmount}</Text> */}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ fontWeight: '600', fontSize: 17, marginRight: 5 }}>
              RM
            </Text>
            <TextInput
              style={{
                width: 110,
                height: 40,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                paddingLeft: 5,
                backgroundColor: Colors.whiteColor,

              }}
              placeholder={'Enter Amount'}
              onChangeText={(text) => {
                setTopUpAmount(parseValidPriceText(text))
              }}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              value={topUpAmount}
            />
            <TouchableOpacity
              style={{
                width: 110,
                height: 40,
                borderWidth: 1,
                borderColor: Colors.primaryColor,
                borderRadius: 5,
                marginLeft: 20,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: Colors.primaryColor
              }}
              onPress={() => { topUpWallet() }}
            >
              <Text style={{ fontSize: 17, fontWeight: '600', color: Colors.whiteColor }}>
                Top-Up
              </Text>
            </TouchableOpacity>
          </View>

          {/* <View style={{
              width: 200,
              height: 40,
              backgroundColor: 'white',
              borderRadius: 10,
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>
              <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
              <TextInput
                // editable={!loading}
                underlineColorAndroid={Colors.whiteColor}
                style={{
                  width: 160,
                  fontSize: 15,
                  fontFamily: 'NunitoSans-Regular',
                }}
                clearButtonMode="while-editing"
                placeholder=" Search"
                onChangeText={(text) => {
                  setSearch(text.trim());
                }}
                value={search}
              />
            </View> */}

        </View>
        <View style={{ marginTop: 30, marginBottom: 100, zIndex: -1, }}>
          <View style={{ width: "100%", flexDirection: 'row', alignItems: 'center', paddingBottom: 10 }}>

            <View style={{ alignItems: 'center', width: '12%', marginRight: 3 }}>
              {/* <TouchableOpacity onPress={()=>{setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId)))}}> */}
              <TouchableOpacity onPress={() => { setSortOrderID(!sortOrderID) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Order ID</Text>
              </TouchableOpacity>
            </View>
            <View style={{ alignItems: 'center', width: '15%', marginHorizontal: 4 }}>
              <TouchableOpacity onPress={() => { setSortDateTime(!sortDateTime) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Date/Time</Text>
              </TouchableOpacity>
            </View>
            <View style={{ alignItems: 'center', width: '18%', marginHorizontal: 4 }}>
              <TouchableOpacity onPress={() => { setSortCustomerName(!sortCustomerName) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Customer</Text>
              </TouchableOpacity>
            </View>
            <View style={{ alignItems: 'center', width: '18%', marginHorizontal: 4 }}>
              <TouchableOpacity onPress={() => { setSortSender(!sortSender) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Sender</Text>
              </TouchableOpacity>
            </View>
            <View style={{ alignItems: 'center', width: '15%', marginHorizontal: 4 }}>
              <TouchableOpacity onPress={() => { setSortPaymentMethod(!sortPaymentMethod) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Payment Method</Text>
              </TouchableOpacity>
            </View>
            <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center', width: '16%', marginHorizontal: 4 }}>
              <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center', }}
                onPress={() => { setSortDeliveryFee(!sortDeliveryFee) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Delivery Fee</Text>
              </TouchableOpacity>
            </View>
          </View>

          <FlatList
            data={takeAwayOrders.slice(0).sort((a, b) => {

            }).filter(takeAwayOrders => {
              if (takeAwayOrders.courierCode === COURIER_CODE.MRSPEEDY) {
                return takeAwayOrders
              }
              if (takeAwayOrders.courierCode === COURIER_CODE.LALAMOVE) {
                return takeAwayOrders
              }
              else {

              }
            })}
            renderItem={renderOrder}
            keyExtractor={(item, index) => String(index)}
          />

        </View>

      </View>
    </View>)
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
  },
  titles: {
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center'
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2
  },
  ManageFilterBox: {
    //width: Dimensions.get('screen').width * 0.4,
    //height: Dimensions.get('screen').height * 0.7,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20
  },

  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  confirmBox: {
    width: 350,
    height: 280,
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center'
  },
});
export default GlobalCreditScreen;
