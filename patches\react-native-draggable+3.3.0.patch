diff --git a/node_modules/react-native-draggable/Draggable.js b/node_modules/react-native-draggable/Draggable.js
index d1c8650..d2073b8 100644
--- a/node_modules/react-native-draggable/Draggable.js
+++ b/node_modules/react-native-draggable/Draggable.js
@@ -53,9 +53,9 @@ export default function Draggable(props) {
   // The Animated object housing our xy value so that we can spring back
   const pan = React.useRef(new Animated.ValueXY());
   // Always set to xy value of pan, would like to remove
-  const offsetFromStart = React.useRef({x: 0, y: 0});
+  const offsetFromStart = React.useRef({ x: 0, y: 0 });
   // Width/Height of Draggable (renderSize is arbitrary if children are passed in)
-  const childSize = React.useRef({x: renderSize, y: renderSize});
+  const childSize = React.useRef({ x: renderSize, y: renderSize });
   // Top/Left/Right/Bottom location on screen from start of most recent touch
   const startBounds = React.useRef();
   // Whether we're currently dragging or not
@@ -80,7 +80,7 @@ export default function Draggable(props) {
   );
 
   const reversePosition = React.useCallback(() => {
-    const originalOffset = {x: 0, y: 0};
+    const originalOffset = { x: 0, y: 0 };
     const newOffset = onReverse ? onReverse() : originalOffset;
     Animated.spring(pan.current, {
       toValue: newOffset || originalOffset,
@@ -111,7 +111,7 @@ export default function Draggable(props) {
       isDragging.current = true;
       if (!shouldReverse) {
         pan.current.setOffset(offsetFromStart.current);
-        pan.current.setValue({x: 0, y: 0});
+        pan.current.setValue({ x: 0, y: 0 });
       }
     },
     [getBounds, shouldReverse],
@@ -119,8 +119,8 @@ export default function Draggable(props) {
 
   const handleOnDrag = React.useCallback(
     (e, gestureState) => {
-      const {dx, dy} = gestureState;
-      const {top, right, left, bottom} = startBounds.current;
+      const { dx, dy } = gestureState;
+      const { top, right, left, bottom } = startBounds.current;
       const far = 999999999;
       const changeX = clamp(
         dx,
@@ -132,7 +132,7 @@ export default function Draggable(props) {
         Number.isFinite(minY) ? minY - top : -far,
         Number.isFinite(maxY) ? maxY - bottom : far,
       );
-      pan.current.setValue({x: changeX, y: changeY});
+      pan.current.setValue({ x: changeX, y: changeY });
       onDrag(e, gestureState);
     },
     [maxX, maxY, minX, minY, onDrag],
@@ -164,7 +164,7 @@ export default function Draggable(props) {
     if (!shouldReverse) {
       curPan.addListener((c) => (offsetFromStart.current = c));
     } else {
-        reversePosition();
+      reversePosition();
     }
     return () => {
       curPan.removeAllListeners();
@@ -179,8 +179,10 @@ export default function Draggable(props) {
       left: 0,
       width: Window.width,
       height: Window.height,
+      elevation: z,
+      zIndex: z,
     };
-  }, []);
+  }, [z]);
 
   const dragItemCss = React.useMemo(() => {
     const style = {
@@ -216,7 +218,7 @@ export default function Draggable(props) {
     } else if (imageSource) {
       return (
         <Image
-          style={{width: renderSize, height: renderSize}}
+          style={{ width: renderSize, height: renderSize }}
           source={imageSource}
         />
       );
@@ -226,8 +228,8 @@ export default function Draggable(props) {
   }, [children, imageSource, renderSize, renderText]);
 
   const handleOnLayout = React.useCallback((event) => {
-    const {height, width} = event.nativeEvent.layout;
-    childSize.current = {x: width, y: height};
+    const { height, width } = event.nativeEvent.layout;
+    childSize.current = { x: width, y: height };
   }, []);
 
   const handlePressOut = React.useCallback(
@@ -241,7 +243,7 @@ export default function Draggable(props) {
   );
 
   const getDebugView = React.useCallback(() => {
-    const {width, height} = Dimensions.get('window');
+    const { width, height } = Dimensions.get('window');
     const far = 9999;
     const constrained = minX || maxX || minY || maxY;
     if (!constrained) {
@@ -254,7 +256,7 @@ export default function Draggable(props) {
     return (
       <View
         pointerEvents="box-none"
-        style={{left, right, top, bottom, ...styles.debugView}}
+        style={{ left, right, top, bottom, ...styles.debugView }}
       />
     );
   }, [maxX, maxY, minX, minY]);
@@ -271,7 +273,7 @@ export default function Draggable(props) {
           {...touchableOpacityProps}
           onLayout={handleOnLayout}
           style={dragItemCss}
-          disabled={disabled}
+          // disabled={disabled}
           onPress={onShortPressRelease}
           onLongPress={onLongPress}
           onPressIn={onPressIn}
@@ -291,13 +293,13 @@ Draggable.defaultProps = {
   shouldReverse: false,
   disabled: false,
   debug: false,
-  onDrag: () => {},
-  onShortPressRelease: () => {},
-  onDragRelease: () => {},
-  onLongPress: () => {},
-  onPressIn: () => {},
-  onPressOut: () => {},
-  onRelease: () => {},
+  onDrag: () => { },
+  onShortPressRelease: () => { },
+  onDragRelease: () => { },
+  onLongPress: () => { },
+  onPressIn: () => { },
+  onPressOut: () => { },
+  onRelease: () => { },
   x: 0,
   y: 0,
   z: 1,
@@ -336,8 +338,8 @@ Draggable.propTypes = {
 };
 
 const styles = StyleSheet.create({
-  text: {color: '#fff', textAlign: 'center'},
-  test: {backgroundColor: 'red'},
+  text: { color: '#fff', textAlign: 'center' },
+  test: { backgroundColor: 'red' },
   debugView: {
     backgroundColor: '#ff000044',
     position: 'absolute',
diff --git a/node_modules/react-native-draggable/Draggable.tsx b/node_modules/react-native-draggable/Draggable.tsx
index 363ab4a..53e0ba8 100644
--- a/node_modules/react-native-draggable/Draggable.tsx
+++ b/node_modules/react-native-draggable/Draggable.tsx
@@ -25,36 +25,36 @@ function clamp(number: number, min: number, max: number) {
 }
 
 interface IProps {
-    /**** props that should probably be removed in favor of "children" */
-    renderText?: string;
-    isCircle?: boolean;
-    renderSize?: number;
-    imageSource?: number;
-    renderColor?: string;
-    /**** */
-    children?: React.ReactNode;
-    shouldReverse?: boolean;
-    disabled?: boolean;
-    debug?: boolean;
-    animatedViewProps?: object;
-    touchableOpacityProps?: object;
-    onDrag?: (e: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
-    onShortPressRelease?: (event: GestureResponderEvent) => void;
-    onDragRelease?: (e: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
-    onLongPress?: (event: GestureResponderEvent) => void;
-    onPressIn?: (event: GestureResponderEvent) => void;
-    onPressOut?: (event: GestureResponderEvent) => void;
-    onRelease?: (event: GestureResponderEvent, wasDragging: boolean) => void;
-    onReverse?: () => {x: number, y: number},
-    x?: number;
-    y?: number;
-    // z/elevation should be removed because it doesn't sync up visually and haptically
-    z?: number;
-    minX?: number;
-    minY?: number;
-    maxX?: number;
-    maxY?: number;
-  };
+  /**** props that should probably be removed in favor of "children" */
+  renderText?: string;
+  isCircle?: boolean;
+  renderSize?: number;
+  imageSource?: number;
+  renderColor?: string;
+  /**** */
+  children?: React.ReactNode;
+  shouldReverse?: boolean;
+  disabled?: boolean;
+  debug?: boolean;
+  animatedViewProps?: object;
+  touchableOpacityProps?: object;
+  onDrag?: (e: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
+  onShortPressRelease?: (event: GestureResponderEvent) => void;
+  onDragRelease?: (e: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
+  onLongPress?: (event: GestureResponderEvent) => void;
+  onPressIn?: (event: GestureResponderEvent) => void;
+  onPressOut?: (event: GestureResponderEvent) => void;
+  onRelease?: (event: GestureResponderEvent, wasDragging: boolean) => void;
+  onReverse?: () => { x: number, y: number },
+  x?: number;
+  y?: number;
+  // z/elevation should be removed because it doesn't sync up visually and haptically
+  z?: number;
+  minX?: number;
+  minY?: number;
+  maxX?: number;
+  maxY?: number;
+};
 
 export default function Draggable(props: IProps) {
   const {
@@ -88,11 +88,11 @@ export default function Draggable(props: IProps) {
   // The Animated object housing our xy value so that we can spring back
   const pan = React.useRef(new Animated.ValueXY());
   // Always set to xy value of pan, would like to remove
-  const offsetFromStart = React.useRef({x: 0, y: 0});
+  const offsetFromStart = React.useRef({ x: 0, y: 0 });
   // Width/Height of Draggable (renderSize is arbitrary if children are passed in)
-  const childSize = React.useRef({x: renderSize, y: renderSize});
+  const childSize = React.useRef({ x: renderSize, y: renderSize });
   // Top/Left/Right/Bottom location on screen from start of most recent touch
-  const startBounds = React.useRef({top: 0, bottom: 0, left: 0, right: 0});
+  const startBounds = React.useRef({ top: 0, bottom: 0, left: 0, right: 0 });
   // Whether we're currently dragging or not
   const isDragging = React.useRef(false);
 
@@ -116,7 +116,7 @@ export default function Draggable(props: IProps) {
 
   const reversePosition = React.useCallback(() => {
     Animated.spring(pan.current, {
-      toValue: {x: 0, y: 0},
+      toValue: { x: 0, y: 0 },
       useNativeDriver: false,
     }).start();
   }, [pan]);
@@ -143,7 +143,7 @@ export default function Draggable(props: IProps) {
       isDragging.current = true;
       if (!shouldReverse) {
         pan.current.setOffset(offsetFromStart.current);
-        pan.current.setValue({x: 0, y: 0});
+        pan.current.setValue({ x: 0, y: 0 });
       }
     },
     [getBounds, shouldReverse],
@@ -151,8 +151,8 @@ export default function Draggable(props: IProps) {
 
   const handleOnDrag = React.useCallback(
     (e: GestureResponderEvent, gestureState: PanResponderGestureState) => {
-      const {dx, dy} = gestureState;
-      const {top, right, left, bottom} = startBounds.current;
+      const { dx, dy } = gestureState;
+      const { top, right, left, bottom } = startBounds.current;
       const far = 999999999;
       const changeX = clamp(
         dx,
@@ -164,7 +164,7 @@ export default function Draggable(props: IProps) {
         Number.isFinite(minY) ? minY - top : -far,
         Number.isFinite(maxY) ? maxY - bottom : far,
       );
-      pan.current.setValue({x: changeX, y: changeY});
+      pan.current.setValue({ x: changeX, y: changeY });
       onDrag(e, gestureState);
     },
     [maxX, maxY, minX, minY, onDrag],
@@ -198,7 +198,7 @@ export default function Draggable(props: IProps) {
       curPan.addListener(c => (offsetFromStart.current = c));
     }
     return () => {
-        // Typed incorrectly
+      // Typed incorrectly
       curPan.removeAllListeners();
     };
   }, [shouldReverse]);
@@ -211,8 +211,10 @@ export default function Draggable(props: IProps) {
       left: 0,
       width: Window.width,
       height: Window.height,
+      elevation: z,
+      zIndex: z,
     };
-  }, []);
+  }, [z]);
 
   const dragItemCss = React.useMemo(() => {
     const style: StyleProp<ViewStyle> = {
@@ -248,7 +250,7 @@ export default function Draggable(props: IProps) {
     } else if (imageSource) {
       return (
         <Image
-          style={{width: renderSize, height: renderSize}}
+          style={{ width: renderSize, height: renderSize }}
           source={imageSource}
         />
       );
@@ -258,8 +260,8 @@ export default function Draggable(props: IProps) {
   }, [children, imageSource, renderSize, renderText]);
 
   const handleOnLayout = React.useCallback(event => {
-    const {height, width} = event.nativeEvent.layout;
-    childSize.current = {x: width, y: height};
+    const { height, width } = event.nativeEvent.layout;
+    childSize.current = { x: width, y: height };
   }, []);
 
   const handlePressOut = React.useCallback(
@@ -273,7 +275,7 @@ export default function Draggable(props: IProps) {
   );
 
   const getDebugView = React.useCallback(() => {
-    const {width, height} = Dimensions.get('window');
+    const { width, height } = Dimensions.get('window');
     const far = 9999;
     const constrained = minX || maxX || minY || maxY;
     if (!constrained) {
@@ -286,7 +288,7 @@ export default function Draggable(props: IProps) {
     return (
       <View
         pointerEvents="box-none"
-        style={{left, right, top, bottom, ...styles.debugView}}
+        style={{ left, right, top, bottom, ...styles.debugView }}
       />
     );
   }, [maxX, maxY, minX, minY]);
@@ -323,20 +325,20 @@ Draggable.defaultProps = {
   shouldReverse: false,
   disabled: false,
   debug: false,
-  onDrag: () => {},
-  onShortPressRelease: () => {},
-  onDragRelease: () => {},
-  onLongPress: () => {},
-  onPressIn: () => {},
-  onPressOut: () => {},
-  onRelease: () => {},
+  onDrag: () => { },
+  onShortPressRelease: () => { },
+  onDragRelease: () => { },
+  onLongPress: () => { },
+  onPressIn: () => { },
+  onPressOut: () => { },
+  onRelease: () => { },
   x: 0,
   y: 0,
   z: 1,
 };
 
 const styles = StyleSheet.create({
-  text: {color: '#fff', textAlign: 'center'},
+  text: { color: '#fff', textAlign: 'center' },
   debugView: {
     backgroundColor: '#ff000044',
     position: 'absolute',
