buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 26 // 23 to 26 first, for RMS ToP SDK
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"

        //////////////////////////////////////////////////////////////////
    
        // firebaseIidVersion = "19.0.1" // default: "19.0.1"
        supportLibVersion = '1.0.2' // Use '28.0.0' or don't specify for old libraries, '1.0.2' or similar for AndroidX
        mediaCompatVersion = '1.0.1' // Do not specify if using old libraries, specify '1.0.1' or similar for androidx.media:media dependency
        supportV4Version = '1.0.0' // Do not specify if using old libraries, specify '1.0.0' or similar for androidx.legacy:legacy-support-v4 dependency
    
        //////////////////////////////////////////////////////////////////
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")

        // NOTE: if you are on react-native 0.71 or below, you must not update
        //       the google-services plugin past version 4.3.15 as it requires gradle >= 7.3.0
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:perf-plugin:1.4.2'

        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'

        // implementation(project(':react-native-tcp-socket')) {
        //     exclude group: 'org.bouncycastle'
        // }
    }
}

subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 34                
            }
        }
    }
}
subprojects {
    // project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

// plugins {
//     // Make sure that you have the AGP plugin 8.1+ dependency
//     // id("com.android.application") version "8.1.4" apply false
//     // ...

//     // Make sure that you have the Google services Gradle plugin 4.4.1+ dependency
//     // id("com.google.gms.google-services") version "4.4.2" apply false

//     // Add the dependency for the Crashlytics Gradle plugin
//     id("com.google.firebase.crashlytics") version "3.0.2" apply false
// }


apply plugin: "com.facebook.react.rootproject"
