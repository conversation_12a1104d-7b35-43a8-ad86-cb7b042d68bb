Fatal Exception: RCTFatalException: Unhandled JS Exception: TypeError: undefined is not a function This error is located at: in Unknown in StaticContainer in EnsureSingleNavigator in SceneView in RCTView in Unknown in DebugContainer in MaybeNestedStack in RCTView in Unknown in RNSScreen in Unknown in Suspender in Suspense in Freeze in DelayedFreeze in InnerScreen in Screen in SceneView in Suspender in Suspense in Freeze in DelayedFreeze in RNSScreenStack in ScreenStack in NativeStackViewInner in RCTView in Unknown in SafeAreaProviderCompat in NativeStackView in PreventRemoveProvider in NavigationContent in Unknown in NativeStackNavigator in DashboardScreenStack in StaticContainer in EnsureSingleNavigator in SceneView in RCTView in Unknown in RCTView in Unknown in Background in Screen in RNSScreen in Unknown in Suspender in Suspense in Freeze in DelayedFreeze in InnerScreen in Screen in MaybeScreen in RNSScreenNavigationContainer in ScreenContainer in MaybeScreenContainer in RNCSafeAreaProvider in SafeAreaProvider in SafeAreaProviderCompat in BottomTabView in PreventRemoveProvider in NavigationContent in Unknown in BottomTabNavigator in EnsureSingleNavigator in BaseNavigationContainer in ThemeProvider in NavigationContainerInner in App in MainScreen in RCTView in Unknown in GestureHandlerRootView in App in RCTView in Unknown in AppContainer, js engine: hermes
Unhandled JS Exception: TypeError: undefined is not a function This error is located at: in Unknown in StaticContainer in EnsureSingleNavigator in SceneView ..., stack: anonymous@1239163:42 anonymous@1239162:55 commitHookEffectListMount@33936:31 commitHookPassiveMountEffects@34514:31 recursivelyTraverseReconnectPassiveEffects@34556:39 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34562:345 recursivelyTraverseReconnectPassiveEffects@34568:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 recursivelyTraverseReconnectPassiveEffects@34555:52 commitPassiveMountOnFiber@3