export default{column2d:"charts",column3d:"charts",bar2d:"charts",bar3d:"charts",pie2d:"charts",pie3d:"charts",line:"charts",area2d:"charts",doughnut2d:"charts",doughnut3d:"charts",pareto2d:"charts",pareto3d:"charts",mscolumn2d:"charts",mscolumn3d:"charts",msline:"charts",msarea:"charts",msbar2d:"charts",msbar3d:"charts",stackedcolumn2d:"charts",marimekko:"charts",stackedcolumn3d:"charts",stackedarea2d:"charts",stackedcolumn2dline:"charts",stackedcolumn3dline:"charts",stackedbar2d:"charts",stackedbar3d:"charts",msstackedcolumn2d:"charts",mscombi2d:"charts",mscombi3d:"charts",mscolumnline3d:"charts",mscombidy2d:"charts",mscombidy3d:"charts",mscolumn3dlinedy:"charts",stackedcolumn2dlinedy:"charts",stackedarea2dlinedy:"charts",stackedcolumn3dlinedy:"charts",msstackedcolumn2dlinedy:"charts",scatter:"charts",bubble:"charts",ssgrid:"charts",scrollbar2d:"charts",scrollcolumn2d:"charts",scrollcolumn3d:"charts",scrollline2d:"charts",scrollarea2d:"charts",scrollstackedcolumn2d:"charts",scrollstackedbar2d:"charts",scrollcombi2d:"charts",scrollmsstackedcolumn2d:"charts",scrollmsstackedcolumn2dlinedy:"charts",scrollcombidy2d:"charts",spline:"charts",splinearea:"charts",msspline:"charts",mssplinearea:"charts",mssplinedy:"charts",multiaxisline:"powercharts",multilevelpie:"powercharts",sunburst:"powercharts",waterfall2d:"powercharts",msstepline:"powercharts",inversemsline:"powercharts",inversemscolumn2d:"powercharts",inversemsarea:"powercharts",errorbar2d:"powercharts",errorscatter:"powercharts",errorline:"powercharts",logmsline:"powercharts",logmscolumn2d:"powercharts",logstackedcolumn2d:"powercharts",radar:"powercharts",chord:"powercharts",dragnode:"powercharts",candlestick:"powercharts",selectscatter:"powercharts",dragcolumn2d:"powercharts",dragline:"powercharts",dragarea:"powercharts",boxandwhisker2d:"powercharts",kagi:"powercharts",heatmap:"powercharts",sankey:"powercharts",angulargauge:"widgets",bulb:"widgets",cylinder:"widgets",drawingpad:"widgets",funnel:"widgets",hbullet:"widgets",hled:"widgets",hlineargauge:"widgets",vlineargauge:"widgets",pyramid:"widgets",realtimearea:"widgets",realtimecolumn:"widgets",realtimeline:"widgets",realtimelinedy:"widgets",realtimestackedarea:"widgets",realtimestackedcolumn:"widgets",sparkcolumn:"widgets",sparkline:"widgets",sparkwinloss:"widgets",thermometer:"widgets",vbullet:"widgets",vled:"widgets",zoomline:"zoomline",zoomlinedy:"zoomline",gantt:"gantt",treemap:"treemap",zoomscatter:"zoomscatter",overlappedbar2d:"overlappedbar2d",overlappedcolumn2d:"overlappedcolumn2d",msstackedcolumn2dsplinedy:"msstackedcolumn2dsplinedy",timeseries:"timeseries"};