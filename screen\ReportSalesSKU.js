import { Text } from "react-native-fast-text";
import React, { Component, useReducer } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    Switch,
    Modal as ModalComponent,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import moment from 'moment'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import Styles from '../constant/Styles';
import {
  isTablet
} from '../util/common';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

class ReportSalesSKU extends Component {
    constructor({ navigation, props }) {
        super(props);
        // navigation.dangerouslyGetParent().setOptions({
        //     tabBarVisible: false,
        // });
        navigation.setOptions({
            headerLeft: () => (
                <View style={{
                    width: Dimensions.get('screen').width * 0.17,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Image
                        style={{
                            width: 124,
                            height: 26,
                        }}
                        resizeMode="contain"
                        source={require('../assets/image/logo.png')}
                    />
                </View>
            ),
        });
        this.state = {
            list: [],
            page: 0,
            name: 'SKU',
            visible: false,
            visible1: false,
            isChecked: false,
            isChecked1: false,
            endDate: new Date(),
            startDate: '',
            oriList: [],
            offset: 0,
            perPage: 10,
            pageCount: 0,
            currentPage: 1,
            day: false,
            pick: null,
            pick1: null,
            search: '',
            lists: [],
            list1: true,
            searchList: false,
        }
    }
    componentDidMount() {
        this.moment()
    }

    email() {
        var body = {
            email: '<EMAIL>',
            data: this.state.list
        }
        ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { this.setState({ visible1: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    }

    download() {
        var body = {
            data: this.state.list
        }
        ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { this.setState({ visible: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    }

    searchBarItem() {
        ApiClient.GET(
            API.salesBySkuSearchBar +
            1 +
            '&queryName=' +
            this.state.search,
        ).then((result) => {

            this.setState({ lists: result });
        });
    }

    add = async () => {
        if (this.state.page + 1 < this.state.pageCount) {
            await this.setState({ page: this.state.page + 1, currentPage: this.state.currentPage + 1 })
            // console.log(this.state.page)
            var e = this.state.page
            this.next(e)
        }
    }

    next(e) {
        const offset = e * this.state.perPage;
        this.setState({ offset: offset })
        this.loadMoreData()
    }

    less = async () => {
        if (this.state.page > 0) {
            await this.setState({ page: this.state.page - 1, currentPage: this.state.currentPage - 1 })
            // console.log(this.state.page)
            var y = this.state.page
            this.pre(y)
        }
    }

    pre(y) {

        const offset = y * this.state.perPage;
        this.setState({ offset: offset })
        this.loadMoreData()

    }

    loadMoreData() {
        const data = this.state.oriList;
        const slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
        this.setState({ list: slice, pageCount: Math.ceil(data.length / this.state.perPage) })
    }

    moment = async () => {
        const today = new Date();
        const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
        // console.log('day', day)
        await this.setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
        this.getDetail()
    }

    getDetail() {
        ApiClient.GET(API.getSalesBySku + 1 + '&startDate=' + this.state.startDate + '&endDate=' + this.state.endDate).then((result) => {
            var data = result
            var slice = data.slice(this.state.offset, this.state.offset + this.state.perPage)
            this.setState({ list: slice, oriList: data, pageCount: Math.ceil(data.length / this.state.perPage) })
        });
    }

    decimal(value) {
        return value.toFixed(2);
    }

    renderSearchItem = ({ item, index }) => (
        (index + 1) % 2 == 0 ? <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
            <View style={{ flexDirection: 'row', }}>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.category}</Text>
                <View style={{ flex: 3 }}>
                    <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                    <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>-</Text>
                </View>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.quantity}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalPrice}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>0.00</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalDiscount}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.discount}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.netSale}</Text>
            </View>
        </View>
            : <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
                <View style={{ flexDirection: 'row', }}>
                    <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                    <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.category}</Text>
                    <View style={{ flex: 3 }}>
                        <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                        <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>-</Text>
                    </View>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.quantity}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalPrice}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>0.00</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalDiscount}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.discount}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.netSale}</Text>
                </View>
            </View>
    )

    renderItem = ({ item, index }) => (
        (index + 1) % 2 == 0 ? <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
            <View style={{ flexDirection: 'row', }}>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.category}</Text>
                <View style={{ flex: 3 }}>
                    <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                    <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>-</Text>
                </View>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.quantity}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalPrice}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>0.00</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalDiscount}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.discount}</Text>
                <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.netSale}</Text>
            </View>
        </View>
            : <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
                <View style={{ flexDirection: 'row', }}>
                    <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                    <Text style={{ flex: 3, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.category}</Text>
                    <View style={{ flex: 3 }}>
                        <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.productName}</Text>
                        <Text style={{ fontSize: 12, fontWeight: '500', textAlign: 'center' }}>-</Text>
                    </View>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.quantity}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalPrice}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>0.00</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.totalDiscount}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.discount}</Text>
                    <Text style={{ flex: 2, fontSize: 12, fontWeight: '500', textAlign: 'center' }}>{item.netSale}</Text>
                </View>
            </View>
    )

    render() {
        return (
            // <View style={styles.container}>
            //     <View style={styles.sidebar}>
            (<View style={[styles.container, !isTablet() ? {
                transform: [
                    { scaleX: 1 },
                    { scaleY: 1 },
                ],
            } : {}]}>
                {/* <View style={[styles.sidebar, !isTablet() ? {
                    width: Dimensions.get('screen').width * 0.4,
                } : {}]}>
                    
                    <SideBar navigation={this.props.navigation} selectedTab={8} expandReport={true} expandSales={true} />
                </View> */}
                <View style={styles.content}>
                    <View style={{ flex: 1 }}>
                        <Text style={{ fontWeight: 'bold', fontSize: 32, marginTop: 10 }}>Sales by: {this.state.name}</Text>
                        {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text> */}
                        <View style={{ flexDirection: 'row', marginTop: 10 }}>
                            <View style={{ flex: 4 }}>
                                <TouchableOpacity>
                                    <View style={{ width: '92%', height: 30, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                        <TextInput
                                            editable={!this.state.loading}
                                            underlineColorAndroid={Colors.whiteColor}
                                            style={{ width: '82%' }}
                                            clearButtonMode="while-editing"
                                            placeholder=" Search"
                                            onChangeText={(text) => {
                                                this.setState({
                                                    search: text.trim(),
                                                    list1: false,
                                                    searchList: true,
                                                });
                                            }}
                                            value={this.state.search}
                                            placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                                        //onSubmitEditing={this.searchBarItem()}
                                        />
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ flex: 6, flexDirection: 'row', justifyContent: 'flex-end' }}>
                                <View style={{ width: '40%' }}>
                                    <TouchableOpacity style={{ width: '100%' }} onPress={() => { this.setState({ day: !this.state.day }) }}>
                                        <View style={{ width: '100%', height: 30, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                            <EvilIcons name='calendar' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                            <View style={{ justifyContent: 'center', flex: 2 }}>
                                                <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 12 }}>{moment(this.state.startDate).format('DD MMM YYYY')} - {moment(this.state.endDate).format('DD MMM YYYY')} </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                    <DateTimePickerModal
                                        isVisible={this.state.showDateTimePicker}
                                        mode={this.state.pickerMode}
                                        onConfirm={(text) => {
                                            if (this.state.pick == 1) {
                                                var date_ob = new Date(text);
                                                let date = ("0" + date_ob.getDate()).slice(-2);
                                                let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                                let year = date_ob.getFullYear();
                                                this.setState({ startDate: year + "-" + month + "-" + date })
                                            } else {
                                                var date_ob = new Date(text);
                                                let date = ("0" + date_ob.getDate()).slice(-2);
                                                let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                                let year = date_ob.getFullYear();
                                                this.setState({ endDate: year + "-" + month + "-" + date })
                                            }

                                            this.setState({ showDateTimePicker: false })
                                        }}
                                        onCancel={() => {
                                            this.setState({ showDateTimePicker: false })
                                        }}
                                    />
                                    {this.state.day ?
                                        <View style={{ position: 'absolute', width: "100%", backgroundColor: Colors.whiteColor, marginTop: '20%', zIndex: 6000 }}>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { this.moment() }}>
                                                <Text style={{ color: Colors.whiteColor }}>Today</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ startDate: moment(moment(new Date()).subtract(1, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                                <Text style={{ color: "#828282" }}>Yesterday</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ startDate: moment(moment(new Date()).subtract(7, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                                <Text style={{ color: "#828282" }}>Last 7 days</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ startDate: moment(moment(new Date()).subtract(30, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                                <Text style={{ color: "#828282" }}>Last 30 days</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ startDate: moment(moment(new Date()).startOf("month")).format('YYYY-MM-DD'), endDate: moment(moment(new Date()).endOf("month")).format('YYYY-MM-DD') }) }}>
                                                <Text style={{ color: "#828282" }}>This month</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { this.setState({ startDate: moment(moment(moment(new Date()).startOf("month")).subtract(1, 'month')).format('YYYY-MM-DD'), endDate: moment(moment(moment(new Date()).endOf("month")).subtract(1, 'month')).format('YYYY-MM-DD') }) }}>
                                                <Text style={{ color: "#828282" }}>Last month</Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                                <Text style={{ color: "#828282" }}>Custom range</Text>
                                            </TouchableOpacity>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flex: 1, marginLeft: 25 }}>
                                                    <Text style={{ color: "#828282" }}>From</Text>
                                                </View>
                                                <View style={{ flex: 1 }}>
                                                    <Text style={{ color: "#828282" }}>To</Text>
                                                </View>
                                            </View>
                                            <View style={{ flexDirection: 'row' }}>
                                                <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                    onPress={() => { this.setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                    <Text style={{ fontSize: 12 }}>{moment(this.state.startDate).format("DD MMM yyyy")}</Text>
                                                </TouchableOpacity>
                                                <View style={{ width: "8%" }}>
                                                </View>
                                                <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                    onPress={() => { this.setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                    <Text style={{ fontSize: 12 }}>{moment(this.state.endDate).format("DD MMM yyyy")}</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                                <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                    onPress={() => { this.setState({ day: false }) }}>
                                                    <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                                </TouchableOpacity>
                                                <View style={{ width: "8%" }}>
                                                </View>
                                                <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                    onPress={() => { this.setState({ day: false }), this.getDetail() }}>
                                                    <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ height: 20 }}>
                                            </View>
                                        </View>
                                        : null}
                                </View>
                                <View style={{ width: '4%' }}></View>
                                <TouchableOpacity style={{ width: '20%' }} onPress={() => { this.setState({ visible: true }); }}>
                                    <View style={{ width: '100%', height: 30, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <AntDesign name='download' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                        <View style={{ justifyContent: 'center', flex: 2 }}>
                                            <Text style={{ color: Colors.descriptionColor, marginLeft: '5%', fontSize: 15 }}>Download</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                                <View style={{ width: '4%' }}></View>
                                <TouchableOpacity style={{ width: '20%' }} onPress={() => { this.setState({ visible1: true }); }}>
                                    <View style={{ width: '100%', height: 30, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <AntDesign name='upload' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%', flex: 1 }} />
                                        <View style={{ justifyContent: 'center', flex: 2 }}>
                                            <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 15 }}>Email</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={{ width: '100%', marginTop: 10 }}>
                            <View style={{ backgroundColor: Colors.whiteColor, padding: 12, height: '78%' }}>
                                <View style={{ marginTop: 10, flexDirection: 'row', }}>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontSize: 12, fontWeight: '500' }}>Product Name</Text>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontSize: 12, fontWeight: '500' }}>Product Category</Text>
                                        <View style={{ marginLeft: '3%' }}>
                                            <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                                            <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontSize: 12, fontWeight: '500' }}>Product SKU</Text>
                                        <Entypo name='triangle-up' size={12} style={{ marginLeft: '3%' }}></Entypo>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontSize: 12, fontWeight: '500' }}>Total Items</Text>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Total Sales</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Total Sale Return</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Total Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Discount</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                        <View>
                                            <Text style={{ fontSize: 12, fontWeight: '500' }}>Item Net Sales</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                        </View>
                                    </View>
                                </View>
                                {this.state.list1 ? (

                                    <FlatList
                                        data={this.state.list}
                                        extraData={this.state.list}
                                        renderItem={this.renderItem}
                                        keyExtractor={(item, index) => String(index)}
                                        style={{ marginTop: 10 }}
                                    />

                                ) : null}
                                {this.state.searchList ? (

                                    <FlatList
                                        data={this.state.lists}
                                        extraData={this.state.lists}
                                        renderItem={this.renderSearchItem}
                                        keyExtractor={(item, index) => String(index)}
                                    />

                                ) : null}
                            </View>
                            <View style={{ marginLeft: '77%', flexDirection: 'row', marginTop: 10, width: '100%' }}>
                                <Text style={{ marginRight: '1%' }}>Page</Text>
                                <View style={{ borderWidth: 1, borderColor: Colors.blackColor, width: '6%', height: 20, alignItems: 'center', backgroundColor: Colors.whiteColor }}>
                                    <Text>{this.state.currentPage}</Text>
                                </View>
                                <Text style={{ marginLeft: '1%', marginRight: '1%' }}>of {this.state.pageCount}</Text>
                                <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { this.less() }}>
                                    <MaterialIcons name='keyboard-arrow-left' size={25} style={{ color: Colors.whiteColor }} />
                                </TouchableOpacity>
                                <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, borderRadius: 2, justifyContent: 'center', alignItems: 'center' }} onPress={() => { this.add() }}>
                                    <MaterialIcons name='keyboard-arrow-right' size={25} style={{ color: Colors.whiteColor }} />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                    <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={this.state.visible} transparent={true} animationType="slide">
                        <View
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: Dimensions.get('window').height,
                            }}>
                            <View style={styles.confirmBox}>
                                <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>

                                </View>
                                <View style={{ flex: 1, flexDirection: 'row' }}>
                                    <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { this.download() }}>
                                        <Text style={{ color: Colors.primaryColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Download</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { this.setState({ visible: !this.state.visible }); }}>
                                        <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Cancel</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ModalView>
                    <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={this.state.visible1} transparent={true} animationType="slide">
                        <View
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: Dimensions.get('window').height,
                            }}>
                            <View style={styles.confirmBox}>
                                <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>

                                </View>
                                <View style={{ flex: 1, flexDirection: 'row' }}>
                                    <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { this.email() }}>
                                        <Text style={{ color: Colors.primaryColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Email</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { this.setState({ visible1: !this.state.visible1 }); }}>
                                        <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Cancel</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ModalView>
                </View>
            </View>)
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 12,
        width: Dimensions.get('screen').width * 0.8,
        backgroundColor: Colors.fieldtBgColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
    },
});
export default ReportSalesSKU;
