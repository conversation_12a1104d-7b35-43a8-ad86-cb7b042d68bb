diff --git a/node_modules/react-native-modal-datetime-picker/src/Modal.js b/node_modules/react-native-modal-datetime-picker/src/Modal.js
index ab94984..1f11b8a 100644
--- a/node_modules/react-native-modal-datetime-picker/src/Modal.js
+++ b/node_modules/react-native-modal-datetime-picker/src/Modal.js
@@ -75,27 +75,30 @@ export class Modal extends Component {
 
   show = () => {
     this.setState({ isVisible: true });
-    Animated.timing(this.animVal, {
-      easing: Easing.inOut(Easing.quad),
-      // Using native driver in the modal makes the content flash
-      useNativeDriver: false,
-      duration: MODAL_ANIM_DURATION,
-      toValue: 1,
-    }).start();
+    // Animated.timing(this.animVal, {
+    //   easing: Easing.inOut(Easing.quad),
+    //   // Using native driver in the modal makes the content flash
+    //   useNativeDriver: false,
+    //   duration: MODAL_ANIM_DURATION,
+    //   toValue: 1,
+    // }).start();
   };
 
   hide = () => {
-    Animated.timing(this.animVal, {
-      easing: Easing.inOut(Easing.quad),
-      // Using native driver in the modal makes the content flash
-      useNativeDriver: false,
-      duration: MODAL_ANIM_DURATION,
-      toValue: 0,
-    }).start(() => {
-      if (this._isMounted) {
-        this.setState({ isVisible: false }, this.props.onHide);
-      }
-    });
+    // Animated.timing(this.animVal, {
+    //   easing: Easing.inOut(Easing.quad),
+    //   // Using native driver in the modal makes the content flash
+    //   useNativeDriver: false,
+    //   duration: MODAL_ANIM_DURATION,
+    //   toValue: 0,
+    // }).start(() => {
+    //   if (this._isMounted) {
+    //     this.setState({ isVisible: false }, this.props.onHide);
+    //   }
+    // });
+    if (this._isMounted) {
+      this.setState({ isVisible: false }, this.props.onHide);
+    }
   };
 
   render() {
@@ -107,20 +110,30 @@ export class Modal extends Component {
       ...otherProps
     } = this.props;
     const { deviceHeight, deviceWidth, isVisible } = this.state;
+    // const backdropAnimatedStyle = {
+    //   opacity: this.animVal.interpolate({
+    //     inputRange: [0, 1],
+    //     outputRange: [0, MODAL_BACKDROP_OPACITY],
+    //   }),
+    // };
     const backdropAnimatedStyle = {
-      opacity: this.animVal.interpolate({
-        inputRange: [0, 1],
-        outputRange: [0, MODAL_BACKDROP_OPACITY],
-      }),
+      opacity: MODAL_BACKDROP_OPACITY,
     };
+    // const contentAnimatedStyle = {
+    //   transform: [
+    //     {
+    //       translateY: this.animVal.interpolate({
+    //         inputRange: [0, 1],
+    //         outputRange: [deviceHeight, 0],
+    //         extrapolate: "clamp",
+    //       }),
+    //     },
+    //   ],
+    // };
     const contentAnimatedStyle = {
       transform: [
         {
-          translateY: this.animVal.interpolate({
-            inputRange: [0, 1],
-            outputRange: [deviceHeight, 0],
-            extrapolate: "clamp",
-          }),
+          translateY: isVisible ? 0 : deviceHeight,
         },
       ],
     };
