diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
new file mode 100644
index 0000000..a29509a
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
@@ -0,0 +1,119 @@
+
+/*
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by GenerateRCTThirdPartyFabricComponentsProviderH
+ */
+
+#pragma GCC diagnostic push
+#pragma GCC diagnostic ignored "-Wreturn-type-c-linkage"
+
+#import <React/RCTComponentViewProtocol.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+Class<RCTComponentViewProtocol> RCTThirdPartyFabricComponentsProvider(const char *name);
+#if RCT_NEW_ARCH_ENABLED
+#ifndef RCT_DYNAMIC_FRAMEWORKS
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV
+Class<RCTComponentViewProtocol> RNDateTimePickerCls(void) __attribute__((used)); // 3
+#endif
+
+#if !TARGET_OS_VISION
+Class<RCTComponentViewProtocol> RNCPickerCls(void) __attribute__((used)); // 4
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_VISION
+Class<RCTComponentViewProtocol> AutoLayoutViewCls(void) __attribute__((used)); // 5
+Class<RCTComponentViewProtocol> CellContainerCls(void) __attribute__((used)); // 5
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_VISION
+Class<RCTComponentViewProtocol> RNExternalDisplayCls(void) __attribute__((used)); // 7
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+Class<RCTComponentViewProtocol> RNGestureHandlerButtonCls(void) __attribute__((used)); // 9
+Class<RCTComponentViewProtocol> RNGestureHandlerRootViewCls(void) __attribute__((used)); // 9
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+
+#if !TARGET_OS_OSX
+Class<RCTComponentViewProtocol> RNCSafeAreaProviderCls(void) __attribute__((used)); // 12
+Class<RCTComponentViewProtocol> RNCSafeAreaViewCls(void) __attribute__((used)); // 12
+#endif
+
+#if !TARGET_OS_OSX
+Class<RCTComponentViewProtocol> RNSFullWindowOverlayCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSModalScreenCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenContainerCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenNavigationContainerCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenStackHeaderConfigCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenStackHeaderSubviewCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSScreenStackCls(void) __attribute__((used)); // 13
+Class<RCTComponentViewProtocol> RNSSearchBarCls(void) __attribute__((used)); // 13
+#endif
+
+Class<RCTComponentViewProtocol> RNSVGCircleCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGClipPathCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGDefsCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGEllipseCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGFeColorMatrixCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGFilterCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGForeignObjectCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGGroupCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGImageCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGSvgViewCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGLinearGradientCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGLineCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGMarkerCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGMaskCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGPathCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGPatternCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGRadialGradientCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGRectCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGSymbolCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGTextCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGTextPathCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGTSpanCls(void) __attribute__((used)); // 14
+Class<RCTComponentViewProtocol> RNSVGUseCls(void) __attribute__((used)); // 14
+#if !TARGET_OS_OSX
+
+#endif
+
+#if !TARGET_OS_TV
+Class<RCTComponentViewProtocol> RNCWebViewCls(void) __attribute__((used)); // 16
+#endif
+
+
+#endif
+#endif
+
+#ifdef __cplusplus
+}
+#endif
+
+#pragma GCC diagnostic pop
+
diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
new file mode 100644
index 0000000..a27b067
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
@@ -0,0 +1,160 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by GenerateRCTThirdPartyFabricComponentsProviderCpp
+ */
+
+// OSS-compatibility layer
+
+#import "RCTThirdPartyFabricComponentsProvider.h"
+
+#import <string>
+#import <unordered_map>
+
+Class<RCTComponentViewProtocol> RCTThirdPartyFabricComponentsProvider(const char *name) {
+  static std::unordered_map<std::string, Class (*)(void)> sFabricComponentsClassMap = {
+    #if RCT_NEW_ARCH_ENABLED
+    #ifndef RCT_DYNAMIC_FRAMEWORKS
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV
+
+    {"RNDateTimePicker", RNDateTimePickerCls}, // 3
+#endif
+
+#if !TARGET_OS_VISION
+
+    {"RNCPicker", RNCPickerCls}, // 4
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_VISION
+
+    {"AutoLayoutView", AutoLayoutViewCls}, // 5
+
+    {"CellContainer", CellContainerCls}, // 5
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_VISION
+
+    {"RNExternalDisplay", RNExternalDisplayCls}, // 7
+#endif
+
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+
+    {"RNGestureHandlerButton", RNGestureHandlerButtonCls}, // 9
+
+    {"RNGestureHandlerRootView", RNGestureHandlerRootViewCls}, // 9
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+#endif
+
+
+#if !TARGET_OS_OSX
+
+    {"RNCSafeAreaProvider", RNCSafeAreaProviderCls}, // 12
+
+    {"RNCSafeAreaView", RNCSafeAreaViewCls}, // 12
+#endif
+
+#if !TARGET_OS_OSX
+
+    {"RNSFullWindowOverlay", RNSFullWindowOverlayCls}, // 13
+
+    {"RNSModalScreen", RNSModalScreenCls}, // 13
+
+    {"RNSScreenContainer", RNSScreenContainerCls}, // 13
+
+    {"RNSScreen", RNSScreenCls}, // 13
+
+    {"RNSScreenNavigationContainer", RNSScreenNavigationContainerCls}, // 13
+
+    {"RNSScreenStackHeaderConfig", RNSScreenStackHeaderConfigCls}, // 13
+
+    {"RNSScreenStackHeaderSubview", RNSScreenStackHeaderSubviewCls}, // 13
+
+    {"RNSScreenStack", RNSScreenStackCls}, // 13
+
+    {"RNSSearchBar", RNSSearchBarCls}, // 13
+#endif
+
+
+    {"RNSVGCircle", RNSVGCircleCls}, // 14
+
+    {"RNSVGClipPath", RNSVGClipPathCls}, // 14
+
+    {"RNSVGDefs", RNSVGDefsCls}, // 14
+
+    {"RNSVGEllipse", RNSVGEllipseCls}, // 14
+
+    {"RNSVGFeColorMatrix", RNSVGFeColorMatrixCls}, // 14
+
+    {"RNSVGFilter", RNSVGFilterCls}, // 14
+
+    {"RNSVGForeignObject", RNSVGForeignObjectCls}, // 14
+
+    {"RNSVGGroup", RNSVGGroupCls}, // 14
+
+    {"RNSVGImage", RNSVGImageCls}, // 14
+
+    {"RNSVGSvgView", RNSVGSvgViewCls}, // 14
+
+    {"RNSVGLinearGradient", RNSVGLinearGradientCls}, // 14
+
+    {"RNSVGLine", RNSVGLineCls}, // 14
+
+    {"RNSVGMarker", RNSVGMarkerCls}, // 14
+
+    {"RNSVGMask", RNSVGMaskCls}, // 14
+
+    {"RNSVGPath", RNSVGPathCls}, // 14
+
+    {"RNSVGPattern", RNSVGPatternCls}, // 14
+
+    {"RNSVGRadialGradient", RNSVGRadialGradientCls}, // 14
+
+    {"RNSVGRect", RNSVGRectCls}, // 14
+
+    {"RNSVGSymbol", RNSVGSymbolCls}, // 14
+
+    {"RNSVGText", RNSVGTextCls}, // 14
+
+    {"RNSVGTextPath", RNSVGTextPathCls}, // 14
+
+    {"RNSVGTSpan", RNSVGTSpanCls}, // 14
+
+    {"RNSVGUse", RNSVGUseCls}, // 14
+#if !TARGET_OS_OSX
+
+#endif
+
+#if !TARGET_OS_TV
+
+    {"RNCWebView", RNCWebViewCls}, // 16
+#endif
+
+    #endif
+    #endif
+  };
+
+  auto p = sFabricComponentsClassMap.find(name);
+  if (p != sFabricComponentsClassMap.end()) {
+    auto classFunc = p->second;
+    return classFunc();
+  }
+  return nil;
+}
diff --git a/node_modules/react-native/React/Views/RCTModalHostView.h b/node_modules/react-native/React/Views/RCTModalHostView.h
index 2fcdcae..9719ddf 100644
--- a/node_modules/react-native/React/Views/RCTModalHostView.h
+++ b/node_modules/react-native/React/Views/RCTModalHostView.h
@@ -36,11 +36,13 @@
 
 @property (nonatomic, copy) NSArray<NSString *> *supportedOrientations;
 @property (nonatomic, copy) RCTDirectEventBlock onOrientationChange;
+@property (nonatomic, strong) UIWindow *modalWindow;
 
 // Fabric only
 @property (nonatomic, copy) RCTDirectEventBlock onDismiss;
 
 - (instancetype)initWithBridge:(RCTBridge *)bridge NS_DESIGNATED_INITIALIZER;
+- (void)dismissModalViewControllerWithCompletion:(void (^)(void))completion;
 
 @end
 
@@ -52,5 +54,9 @@
 - (void)dismissModalHostView:(RCTModalHostView *)modalHostView
           withViewController:(RCTModalHostViewController *)viewController
                     animated:(BOOL)animated;
+- (void)dismissModalHostViewWithCompletion:(RCTModalHostView *)modalHostView
+          withViewController:(RCTModalHostViewController *)viewController
+                    animated:(BOOL)animated completion: (void (^)(void))completion;
+
 
 @end
diff --git a/node_modules/react-native/React/Views/RCTModalHostView.m b/node_modules/react-native/React/Views/RCTModalHostView.m
index dfde4ae..2ca8dc0 100644
--- a/node_modules/react-native/React/Views/RCTModalHostView.m
+++ b/node_modules/react-native/React/Views/RCTModalHostView.m
@@ -40,6 +40,7 @@ - (instancetype)initWithBridge:(RCTBridge *)bridge
     _modalViewController.view = containerView;
     _touchHandler = [[RCTTouchHandler alloc] initWithBridge:bridge];
     _isPresented = NO;
+    _modalViewController.modalHostView = self;
 
     __weak typeof(self) weakSelf = self;
     _modalViewController.boundsDidChangeBlock = ^(CGRect newBounds) {
@@ -115,9 +116,14 @@ - (void)didUpdateReactSubviews
 }
 
 - (void)dismissModalViewController
+{
+  [self dismissModalViewControllerWithCompletion: nil];
+}
+
+- (void)dismissModalViewControllerWithCompletion:(void (^)(void))completion
 {
   if (_isPresented) {
-    [_delegate dismissModalHostView:self withViewController:_modalViewController animated:[self hasAnimationType]];
+    [_delegate dismissModalHostViewWithCompletion:self withViewController:_modalViewController animated:[self hasAnimationType] completion: completion];
     _isPresented = NO;
   }
 }
diff --git a/node_modules/react-native/React/Views/RCTModalHostViewController.h b/node_modules/react-native/React/Views/RCTModalHostViewController.h
index b12b0f7..ec340e8 100644
--- a/node_modules/react-native/React/Views/RCTModalHostViewController.h
+++ b/node_modules/react-native/React/Views/RCTModalHostViewController.h
@@ -6,11 +6,14 @@
  */
 
 #import <UIKit/UIKit.h>
+#import "RCTModalHostView.h"
 
 @interface RCTModalHostViewController : UIViewController
 
 @property (nonatomic, copy) void (^boundsDidChangeBlock)(CGRect newBounds);
 
+@property RCTModalHostView* modalHostView;
+
 @property (nonatomic, assign) UIInterfaceOrientationMask supportedInterfaceOrientations;
 
 @end
diff --git a/node_modules/react-native/React/Views/RCTModalHostViewManager.m b/node_modules/react-native/React/Views/RCTModalHostViewManager.m
index b0295e0..90f54a9 100644
--- a/node_modules/react-native/React/Views/RCTModalHostViewManager.m
+++ b/node_modules/react-native/React/Views/RCTModalHostViewManager.m
@@ -78,37 +78,85 @@ - (void)presentModalHostView:(RCTModalHostView *)modalHostView
   dispatch_async(dispatch_get_main_queue(), ^{
     if (self->_presentationBlock) {
       self->_presentationBlock([modalHostView reactViewController], viewController, animated, completionBlock);
-    } else {
-      [[modalHostView reactViewController] presentViewController:viewController
-                                                        animated:animated
-                                                      completion:completionBlock];
+    } 
+    // else {
+    //   [[modalHostView reactViewController] presentViewController:viewController
+    //                                                     animated:animated
+    //                                                   completion:completionBlock];
+    // }
+    else {
+      UIViewController* presentingViewController;
+      // pageSheet and formSheet presentation style animate the presented view so we need to use the last presented view controller
+      // For other presentation styles we use the new window
+      if (modalHostView.presentationStyle == UIModalPresentationPageSheet || modalHostView.presentationStyle == UIModalPresentationFormSheet) {
+        UIViewController *lastPresentedViewController = RCTKeyWindow().rootViewController;
+        UIViewController *presentedViewController = nil;
+        while (lastPresentedViewController != nil) {
+          presentedViewController = lastPresentedViewController;
+          lastPresentedViewController = lastPresentedViewController.presentedViewController;
+        }
+        presentingViewController = presentedViewController;
+      } else {
+        modalHostView.modalWindow = [[UIWindow alloc] initWithFrame:UIScreen.mainScreen.bounds];
+        modalHostView.modalWindow.windowLevel = UIWindowLevelAlert;
+        UIViewController *newViewController = [[UIViewController alloc] init];
+        modalHostView.modalWindow.rootViewController = newViewController;
+        [modalHostView.modalWindow makeKeyAndVisible];
+        presentingViewController = newViewController;
+      }
+      [presentingViewController presentViewController:viewController animated:animated completion:completionBlock];
     }
   });
 }
 
-- (void)dismissModalHostView:(RCTModalHostView *)modalHostView
+- (void)dismissModalHostViewWithCompletion:(RCTModalHostView *)modalHostView
           withViewController:(RCTModalHostViewController *)viewController
-                    animated:(BOOL)animated
+                    animated:(BOOL)animated completion:(void (^)(void))completion
 {
   dispatch_block_t completionBlock = ^{
     if (modalHostView.identifier) {
       [[self.bridge moduleForClass:[RCTModalManager class]] modalDismissed:modalHostView.identifier];
     }
+    if (completion) {
+      completion();
+    }
+    modalHostView.modalWindow = nil;
   };
   dispatch_async(dispatch_get_main_queue(), ^{
     if (self->_dismissalBlock) {
       self->_dismissalBlock([modalHostView reactViewController], viewController, animated, completionBlock);
-    } else if (viewController.presentingViewController) {
-      [viewController.presentingViewController dismissViewControllerAnimated:animated completion:completionBlock];
+    } 
+    // else if (viewController.presentingViewController) {
+    //   [viewController.presentingViewController dismissViewControllerAnimated:animated completion:completionBlock];
+    // } else {
+    //   // Make sure to call the completion block in case the presenting view controller is nil
+    //   // In an internal app we have a use case where a modal presents another view without bein dismissed
+    //   // This, somehow, invalidate the presenting view controller and the modal remains always visible.
+    //   completionBlock();
+    // }
+
+    // Will be true for pageSheet and formSheet presentation styles
+    // We dismiss the nested modal and then dismiss the current modal
+    if (viewController.presentedViewController != nil && [viewController.presentedViewController isKindOfClass:[RCTModalHostViewController class]]) {
+      RCTModalHostViewController* presentedModalViewController = (RCTModalHostViewController *)viewController.presentedViewController;
+      dispatch_block_t childModalCompletionBlock = ^{
+        [viewController.presentingViewController dismissViewControllerAnimated:animated completion:completionBlock];
+      };
+
+      [presentedModalViewController.modalHostView dismissModalViewControllerWithCompletion: childModalCompletionBlock];
     } else {
-      // Make sure to call the completion block in case the presenting view controller is nil
-      // In an internal app we have a use case where a modal presents another view without bein dismissed
-      // This, somehow, invalidate the presenting view controller and the modal remains always visible.
-      completionBlock();
+      [viewController.presentingViewController dismissViewControllerAnimated:animated completion:completionBlock];
     }
   });
 }
 
+- (void)dismissModalHostView:(RCTModalHostView *)modalHostView
+          withViewController:(RCTModalHostViewController *)viewController
+                    animated:(BOOL)animated
+{
+  [self dismissModalHostViewWithCompletion:modalHostView withViewController:viewController animated:animated completion:nil];
+}
+
 - (RCTShadowView *)shadowView
 {
   return [RCTModalHostShadowView new];
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.cpp
new file mode 100644
index 0000000..2fdd870
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.cpp
@@ -0,0 +1,29 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorCpp.js
+ */
+
+#include <react/renderer/components/rncore/ComponentDescriptors.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>
+
+namespace facebook::react {
+
+void rncore_registerComponentDescriptorsFromCodegen(
+  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry) {
+registry->add(concreteComponentDescriptorProvider<ActivityIndicatorViewComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<AndroidDrawerLayoutComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<AndroidHorizontalScrollContentViewComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<AndroidSwipeRefreshLayoutComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<DebuggingOverlayComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<PullToRefreshViewComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<SwitchComponentDescriptor>());
+registry->add(concreteComponentDescriptorProvider<UnimplementedNativeViewComponentDescriptor>());
+}
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h
new file mode 100644
index 0000000..d20605d
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h
@@ -0,0 +1,31 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/rncore/ShadowNodes.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>
+
+namespace facebook::react {
+
+using ActivityIndicatorViewComponentDescriptor = ConcreteComponentDescriptor<ActivityIndicatorViewShadowNode>;
+using AndroidDrawerLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidDrawerLayoutShadowNode>;
+using AndroidHorizontalScrollContentViewComponentDescriptor = ConcreteComponentDescriptor<AndroidHorizontalScrollContentViewShadowNode>;
+using AndroidSwipeRefreshLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidSwipeRefreshLayoutShadowNode>;
+using DebuggingOverlayComponentDescriptor = ConcreteComponentDescriptor<DebuggingOverlayShadowNode>;
+using PullToRefreshViewComponentDescriptor = ConcreteComponentDescriptor<PullToRefreshViewShadowNode>;
+using SwitchComponentDescriptor = ConcreteComponentDescriptor<SwitchShadowNode>;
+using UnimplementedNativeViewComponentDescriptor = ConcreteComponentDescriptor<UnimplementedNativeViewShadowNode>;
+
+void rncore_registerComponentDescriptorsFromCodegen(
+  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry);
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp
new file mode 100644
index 0000000..c983c53
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp
@@ -0,0 +1,132 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateEventEmitterCpp.js
+ */
+
+#include <react/renderer/components/rncore/EventEmitters.h>
+
+
+namespace facebook::react {
+
+
+void AndroidDrawerLayoutEventEmitter::onDrawerSlide(OnDrawerSlide $event) const {
+  dispatchEvent("drawerSlide", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "offset", $event.offset);
+    return $payload;
+  });
+}
+
+
+void AndroidDrawerLayoutEventEmitter::onDrawerStateChanged(OnDrawerStateChanged $event) const {
+  dispatchEvent("drawerStateChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "drawerState", $event.drawerState);
+    return $payload;
+  });
+}
+
+
+void AndroidDrawerLayoutEventEmitter::onDrawerOpen(OnDrawerOpen $event) const {
+  dispatchEvent("drawerOpen", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+void AndroidDrawerLayoutEventEmitter::onDrawerClose(OnDrawerClose $event) const {
+  dispatchEvent("drawerClose", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+
+void AndroidSwipeRefreshLayoutEventEmitter::onRefresh(OnRefresh $event) const {
+  dispatchEvent("refresh", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+void AndroidSwitchEventEmitter::onChange(OnChange $event) const {
+  dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "value", $event.value);
+$payload.setProperty(runtime, "target", $event.target);
+    return $payload;
+  });
+}
+
+
+
+
+void PullToRefreshViewEventEmitter::onRefresh(OnRefresh $event) const {
+  dispatchEvent("refresh", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+
+void ModalHostViewEventEmitter::onRequestClose(OnRequestClose $event) const {
+  dispatchEvent("requestClose", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+void ModalHostViewEventEmitter::onShow(OnShow $event) const {
+  dispatchEvent("show", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+void ModalHostViewEventEmitter::onDismiss(OnDismiss $event) const {
+  dispatchEvent("dismiss", [](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    
+    return $payload;
+  });
+}
+
+
+void ModalHostViewEventEmitter::onOrientationChange(OnOrientationChange $event) const {
+  dispatchEvent("orientationChange", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "orientation", toString($event.orientation));
+    return $payload;
+  });
+}
+
+
+
+void SwitchEventEmitter::onChange(OnChange $event) const {
+  dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "value", $event.value);
+$payload.setProperty(runtime, "target", $event.target);
+    return $payload;
+  });
+}
+
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h
new file mode 100644
index 0000000..0c3bce4
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h
@@ -0,0 +1,169 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateEventEmitterH.js
+ */
+#pragma once
+
+#include <react/renderer/components/view/ViewEventEmitter.h>
+
+
+namespace facebook::react {
+class ActivityIndicatorViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class AndroidDrawerLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnDrawerSlide {
+      Float offset;
+    };
+
+  struct OnDrawerStateChanged {
+      int drawerState;
+    };
+
+  struct OnDrawerOpen {
+      
+    };
+
+  struct OnDrawerClose {
+      
+    };
+  void onDrawerSlide(OnDrawerSlide value) const;
+
+  void onDrawerStateChanged(OnDrawerStateChanged value) const;
+
+  void onDrawerOpen(OnDrawerOpen value) const;
+
+  void onDrawerClose(OnDrawerClose value) const;
+};
+class AndroidHorizontalScrollContentViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class AndroidSwipeRefreshLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+  void onRefresh(OnRefresh value) const;
+};
+class AndroidSwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    int target;
+    };
+  void onChange(OnChange value) const;
+};
+class DebuggingOverlayEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class AndroidProgressBarEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class PullToRefreshViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+  void onRefresh(OnRefresh value) const;
+};
+class InputAccessoryEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class ModalHostViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRequestClose {
+      
+    };
+
+  struct OnShow {
+      
+    };
+
+  struct OnDismiss {
+      
+    };
+
+  enum class OnOrientationChangeOrientation {
+    Portrait,
+    Landscape
+  };
+
+  static char const *toString(const OnOrientationChangeOrientation value) {
+    switch (value) {
+      case OnOrientationChangeOrientation::Portrait: return "portrait";
+      case OnOrientationChangeOrientation::Landscape: return "landscape";
+    }
+  }
+
+  struct OnOrientationChange {
+      OnOrientationChangeOrientation orientation;
+    };
+  void onRequestClose(OnRequestClose value) const;
+
+  void onShow(OnShow value) const;
+
+  void onDismiss(OnDismiss value) const;
+
+  void onOrientationChange(OnOrientationChange value) const;
+};
+class SafeAreaViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+class SwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    int target;
+    };
+  void onChange(OnChange value) const;
+};
+class UnimplementedNativeViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+  
+};
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp
new file mode 100644
index 0000000..c8a0efd
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp
@@ -0,0 +1,155 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GeneratePropsCpp.js
+ */
+
+#include <react/renderer/components/rncore/Props.h>
+#include <react/renderer/core/PropsParserContext.h>
+#include <react/renderer/core/propsConversions.h>
+
+namespace facebook::react {
+
+ActivityIndicatorViewProps::ActivityIndicatorViewProps(
+    const PropsParserContext &context,
+    const ActivityIndicatorViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    hidesWhenStopped(convertRawProp(context, rawProps, "hidesWhenStopped", sourceProps.hidesWhenStopped, {true})),
+    animating(convertRawProp(context, rawProps, "animating", sourceProps.animating, {true})),
+    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
+    size(convertRawProp(context, rawProps, "size", sourceProps.size, {ActivityIndicatorViewSize::Small}))
+      {}
+AndroidDrawerLayoutProps::AndroidDrawerLayoutProps(
+    const PropsParserContext &context,
+    const AndroidDrawerLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    keyboardDismissMode(convertRawProp(context, rawProps, "keyboardDismissMode", sourceProps.keyboardDismissMode, {AndroidDrawerLayoutKeyboardDismissMode::None})),
+    drawerBackgroundColor(convertRawProp(context, rawProps, "drawerBackgroundColor", sourceProps.drawerBackgroundColor, {})),
+    drawerPosition(convertRawProp(context, rawProps, "drawerPosition", sourceProps.drawerPosition, {AndroidDrawerLayoutDrawerPosition::Left})),
+    drawerWidth(convertRawProp(context, rawProps, "drawerWidth", sourceProps.drawerWidth, {})),
+    drawerLockMode(convertRawProp(context, rawProps, "drawerLockMode", sourceProps.drawerLockMode, {AndroidDrawerLayoutDrawerLockMode::Unlocked})),
+    statusBarBackgroundColor(convertRawProp(context, rawProps, "statusBarBackgroundColor", sourceProps.statusBarBackgroundColor, {}))
+      {}
+AndroidHorizontalScrollContentViewProps::AndroidHorizontalScrollContentViewProps(
+    const PropsParserContext &context,
+    const AndroidHorizontalScrollContentViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    removeClippedSubviews(convertRawProp(context, rawProps, "removeClippedSubviews", sourceProps.removeClippedSubviews, {false}))
+      {}
+AndroidSwipeRefreshLayoutProps::AndroidSwipeRefreshLayoutProps(
+    const PropsParserContext &context,
+    const AndroidSwipeRefreshLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    enabled(convertRawProp(context, rawProps, "enabled", sourceProps.enabled, {true})),
+    colors(convertRawProp(context, rawProps, "colors", sourceProps.colors, {})),
+    progressBackgroundColor(convertRawProp(context, rawProps, "progressBackgroundColor", sourceProps.progressBackgroundColor, {})),
+    size(convertRawProp(context, rawProps, "size", sourceProps.size, {AndroidSwipeRefreshLayoutSize::Default})),
+    progressViewOffset(convertRawProp(context, rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(context, rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+AndroidSwitchProps::AndroidSwitchProps(
+    const PropsParserContext &context,
+    const AndroidSwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    disabled(convertRawProp(context, rawProps, "disabled", sourceProps.disabled, {false})),
+    enabled(convertRawProp(context, rawProps, "enabled", sourceProps.enabled, {true})),
+    thumbColor(convertRawProp(context, rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(context, rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(context, rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {})),
+    value(convertRawProp(context, rawProps, "value", sourceProps.value, {false})),
+    on(convertRawProp(context, rawProps, "on", sourceProps.on, {false})),
+    thumbTintColor(convertRawProp(context, rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    trackTintColor(convertRawProp(context, rawProps, "trackTintColor", sourceProps.trackTintColor, {}))
+      {}
+DebuggingOverlayProps::DebuggingOverlayProps(
+    const PropsParserContext &context,
+    const DebuggingOverlayProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps)
+
+    
+      {}
+AndroidProgressBarProps::AndroidProgressBarProps(
+    const PropsParserContext &context,
+    const AndroidProgressBarProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    styleAttr(convertRawProp(context, rawProps, "styleAttr", sourceProps.styleAttr, {})),
+    typeAttr(convertRawProp(context, rawProps, "typeAttr", sourceProps.typeAttr, {})),
+    indeterminate(convertRawProp(context, rawProps, "indeterminate", sourceProps.indeterminate, {false})),
+    progress(convertRawProp(context, rawProps, "progress", sourceProps.progress, {0.0})),
+    animating(convertRawProp(context, rawProps, "animating", sourceProps.animating, {true})),
+    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
+    testID(convertRawProp(context, rawProps, "testID", sourceProps.testID, {""}))
+      {}
+PullToRefreshViewProps::PullToRefreshViewProps(
+    const PropsParserContext &context,
+    const PullToRefreshViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    tintColor(convertRawProp(context, rawProps, "tintColor", sourceProps.tintColor, {})),
+    titleColor(convertRawProp(context, rawProps, "titleColor", sourceProps.titleColor, {})),
+    title(convertRawProp(context, rawProps, "title", sourceProps.title, {})),
+    progressViewOffset(convertRawProp(context, rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(context, rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+InputAccessoryProps::InputAccessoryProps(
+    const PropsParserContext &context,
+    const InputAccessoryProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    backgroundColor(convertRawProp(context, rawProps, "backgroundColor", sourceProps.backgroundColor, {}))
+      {}
+ModalHostViewProps::ModalHostViewProps(
+    const PropsParserContext &context,
+    const ModalHostViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    animationType(convertRawProp(context, rawProps, "animationType", sourceProps.animationType, {ModalHostViewAnimationType::None})),
+    presentationStyle(convertRawProp(context, rawProps, "presentationStyle", sourceProps.presentationStyle, {ModalHostViewPresentationStyle::FullScreen})),
+    transparent(convertRawProp(context, rawProps, "transparent", sourceProps.transparent, {false})),
+    statusBarTranslucent(convertRawProp(context, rawProps, "statusBarTranslucent", sourceProps.statusBarTranslucent, {false})),
+    hardwareAccelerated(convertRawProp(context, rawProps, "hardwareAccelerated", sourceProps.hardwareAccelerated, {false})),
+    visible(convertRawProp(context, rawProps, "visible", sourceProps.visible, {false})),
+    animated(convertRawProp(context, rawProps, "animated", sourceProps.animated, {false})),
+    supportedOrientations(convertRawProp(context, rawProps, "supportedOrientations", sourceProps.supportedOrientations, {static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)})),
+    identifier(convertRawProp(context, rawProps, "identifier", sourceProps.identifier, {0}))
+      {}
+SafeAreaViewProps::SafeAreaViewProps(
+    const PropsParserContext &context,
+    const SafeAreaViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps)
+
+    
+      {}
+SwitchProps::SwitchProps(
+    const PropsParserContext &context,
+    const SwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    disabled(convertRawProp(context, rawProps, "disabled", sourceProps.disabled, {false})),
+    value(convertRawProp(context, rawProps, "value", sourceProps.value, {false})),
+    tintColor(convertRawProp(context, rawProps, "tintColor", sourceProps.tintColor, {})),
+    onTintColor(convertRawProp(context, rawProps, "onTintColor", sourceProps.onTintColor, {})),
+    thumbTintColor(convertRawProp(context, rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    thumbColor(convertRawProp(context, rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(context, rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(context, rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {}))
+      {}
+UnimplementedNativeViewProps::UnimplementedNativeViewProps(
+    const PropsParserContext &context,
+    const UnimplementedNativeViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    name(convertRawProp(context, rawProps, "name", sourceProps.name, {""}))
+      {}
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h
new file mode 100644
index 0000000..044c1e9
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h
@@ -0,0 +1,392 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GeneratePropsH.js
+ */
+#pragma once
+
+#include <cinttypes>
+#include <react/renderer/components/view/ViewProps.h>
+#include <react/renderer/core/PropsParserContext.h>
+#include <react/renderer/graphics/Color.h>
+#include <vector>
+
+namespace facebook::react {
+
+enum class ActivityIndicatorViewSize { Small, Large };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, ActivityIndicatorViewSize &result) {
+  auto string = (std::string)value;
+  if (string == "small") { result = ActivityIndicatorViewSize::Small; return; }
+  if (string == "large") { result = ActivityIndicatorViewSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const ActivityIndicatorViewSize &value) {
+  switch (value) {
+    case ActivityIndicatorViewSize::Small: return "small";
+    case ActivityIndicatorViewSize::Large: return "large";
+  }
+}
+
+class ActivityIndicatorViewProps final : public ViewProps {
+ public:
+  ActivityIndicatorViewProps() = default;
+  ActivityIndicatorViewProps(const PropsParserContext& context, const ActivityIndicatorViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool hidesWhenStopped{true};
+  bool animating{true};
+  SharedColor color{};
+  ActivityIndicatorViewSize size{ActivityIndicatorViewSize::Small};
+};
+
+enum class AndroidDrawerLayoutKeyboardDismissMode { None, OnDrag };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AndroidDrawerLayoutKeyboardDismissMode &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = AndroidDrawerLayoutKeyboardDismissMode::None; return; }
+  if (string == "on-drag") { result = AndroidDrawerLayoutKeyboardDismissMode::OnDrag; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutKeyboardDismissMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutKeyboardDismissMode::None: return "none";
+    case AndroidDrawerLayoutKeyboardDismissMode::OnDrag: return "on-drag";
+  }
+}
+enum class AndroidDrawerLayoutDrawerPosition { Left, Right };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AndroidDrawerLayoutDrawerPosition &result) {
+  auto string = (std::string)value;
+  if (string == "left") { result = AndroidDrawerLayoutDrawerPosition::Left; return; }
+  if (string == "right") { result = AndroidDrawerLayoutDrawerPosition::Right; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerPosition &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerPosition::Left: return "left";
+    case AndroidDrawerLayoutDrawerPosition::Right: return "right";
+  }
+}
+enum class AndroidDrawerLayoutDrawerLockMode { Unlocked, LockedClosed, LockedOpen };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AndroidDrawerLayoutDrawerLockMode &result) {
+  auto string = (std::string)value;
+  if (string == "unlocked") { result = AndroidDrawerLayoutDrawerLockMode::Unlocked; return; }
+  if (string == "locked-closed") { result = AndroidDrawerLayoutDrawerLockMode::LockedClosed; return; }
+  if (string == "locked-open") { result = AndroidDrawerLayoutDrawerLockMode::LockedOpen; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerLockMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerLockMode::Unlocked: return "unlocked";
+    case AndroidDrawerLayoutDrawerLockMode::LockedClosed: return "locked-closed";
+    case AndroidDrawerLayoutDrawerLockMode::LockedOpen: return "locked-open";
+  }
+}
+
+class AndroidDrawerLayoutProps final : public ViewProps {
+ public:
+  AndroidDrawerLayoutProps() = default;
+  AndroidDrawerLayoutProps(const PropsParserContext& context, const AndroidDrawerLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  AndroidDrawerLayoutKeyboardDismissMode keyboardDismissMode{AndroidDrawerLayoutKeyboardDismissMode::None};
+  SharedColor drawerBackgroundColor{};
+  AndroidDrawerLayoutDrawerPosition drawerPosition{AndroidDrawerLayoutDrawerPosition::Left};
+  Float drawerWidth{};
+  AndroidDrawerLayoutDrawerLockMode drawerLockMode{AndroidDrawerLayoutDrawerLockMode::Unlocked};
+  SharedColor statusBarBackgroundColor{};
+};
+
+class AndroidHorizontalScrollContentViewProps final : public ViewProps {
+ public:
+  AndroidHorizontalScrollContentViewProps() = default;
+  AndroidHorizontalScrollContentViewProps(const PropsParserContext& context, const AndroidHorizontalScrollContentViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool removeClippedSubviews{false};
+};
+
+enum class AndroidSwipeRefreshLayoutSize { Default, Large };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, AndroidSwipeRefreshLayoutSize &result) {
+  auto string = (std::string)value;
+  if (string == "default") { result = AndroidSwipeRefreshLayoutSize::Default; return; }
+  if (string == "large") { result = AndroidSwipeRefreshLayoutSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidSwipeRefreshLayoutSize &value) {
+  switch (value) {
+    case AndroidSwipeRefreshLayoutSize::Default: return "default";
+    case AndroidSwipeRefreshLayoutSize::Large: return "large";
+  }
+}
+
+class AndroidSwipeRefreshLayoutProps final : public ViewProps {
+ public:
+  AndroidSwipeRefreshLayoutProps() = default;
+  AndroidSwipeRefreshLayoutProps(const PropsParserContext& context, const AndroidSwipeRefreshLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool enabled{true};
+  std::vector<SharedColor> colors{};
+  SharedColor progressBackgroundColor{};
+  AndroidSwipeRefreshLayoutSize size{AndroidSwipeRefreshLayoutSize::Default};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class AndroidSwitchProps final : public ViewProps {
+ public:
+  AndroidSwitchProps() = default;
+  AndroidSwitchProps(const PropsParserContext& context, const AndroidSwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool enabled{true};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+  bool value{false};
+  bool on{false};
+  SharedColor thumbTintColor{};
+  SharedColor trackTintColor{};
+};
+
+class DebuggingOverlayProps final : public ViewProps {
+ public:
+  DebuggingOverlayProps() = default;
+  DebuggingOverlayProps(const PropsParserContext& context, const DebuggingOverlayProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class AndroidProgressBarProps final : public ViewProps {
+ public:
+  AndroidProgressBarProps() = default;
+  AndroidProgressBarProps(const PropsParserContext& context, const AndroidProgressBarProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string styleAttr{};
+  std::string typeAttr{};
+  bool indeterminate{false};
+  double progress{0.0};
+  bool animating{true};
+  SharedColor color{};
+  std::string testID{""};
+};
+
+class PullToRefreshViewProps final : public ViewProps {
+ public:
+  PullToRefreshViewProps() = default;
+  PullToRefreshViewProps(const PropsParserContext& context, const PullToRefreshViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor tintColor{};
+  SharedColor titleColor{};
+  std::string title{};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class InputAccessoryProps final : public ViewProps {
+ public:
+  InputAccessoryProps() = default;
+  InputAccessoryProps(const PropsParserContext& context, const InputAccessoryProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor backgroundColor{};
+};
+
+enum class ModalHostViewAnimationType { None, Slide, Fade };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, ModalHostViewAnimationType &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = ModalHostViewAnimationType::None; return; }
+  if (string == "slide") { result = ModalHostViewAnimationType::Slide; return; }
+  if (string == "fade") { result = ModalHostViewAnimationType::Fade; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewAnimationType &value) {
+  switch (value) {
+    case ModalHostViewAnimationType::None: return "none";
+    case ModalHostViewAnimationType::Slide: return "slide";
+    case ModalHostViewAnimationType::Fade: return "fade";
+  }
+}
+enum class ModalHostViewPresentationStyle { FullScreen, PageSheet, FormSheet, OverFullScreen };
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, ModalHostViewPresentationStyle &result) {
+  auto string = (std::string)value;
+  if (string == "fullScreen") { result = ModalHostViewPresentationStyle::FullScreen; return; }
+  if (string == "pageSheet") { result = ModalHostViewPresentationStyle::PageSheet; return; }
+  if (string == "formSheet") { result = ModalHostViewPresentationStyle::FormSheet; return; }
+  if (string == "overFullScreen") { result = ModalHostViewPresentationStyle::OverFullScreen; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewPresentationStyle &value) {
+  switch (value) {
+    case ModalHostViewPresentationStyle::FullScreen: return "fullScreen";
+    case ModalHostViewPresentationStyle::PageSheet: return "pageSheet";
+    case ModalHostViewPresentationStyle::FormSheet: return "formSheet";
+    case ModalHostViewPresentationStyle::OverFullScreen: return "overFullScreen";
+  }
+}
+using ModalHostViewSupportedOrientationsMask = uint32_t;
+
+enum class ModalHostViewSupportedOrientations: ModalHostViewSupportedOrientationsMask {
+  Portrait = 1 << 0,
+  PortraitUpsideDown = 1 << 1,
+  Landscape = 1 << 2,
+  LandscapeLeft = 1 << 3,
+  LandscapeRight = 1 << 4
+};
+
+constexpr bool operator&(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs & static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr ModalHostViewSupportedOrientationsMask operator|(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr void operator|=(
+  ModalHostViewSupportedOrientationsMask &lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  lhs = lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, ModalHostViewSupportedOrientationsMask &result) {
+  auto items = std::vector<std::string>{value};
+  for (const auto &item : items) {
+    if (item == "portrait") {
+      result |= ModalHostViewSupportedOrientations::Portrait;
+      continue;
+    }
+    if (item == "portrait-upside-down") {
+      result |= ModalHostViewSupportedOrientations::PortraitUpsideDown;
+      continue;
+    }
+    if (item == "landscape") {
+      result |= ModalHostViewSupportedOrientations::Landscape;
+      continue;
+    }
+    if (item == "landscape-left") {
+      result |= ModalHostViewSupportedOrientations::LandscapeLeft;
+      continue;
+    }
+    if (item == "landscape-right") {
+      result |= ModalHostViewSupportedOrientations::LandscapeRight;
+      continue;
+    }
+    abort();
+  }
+}
+
+static inline std::string toString(const ModalHostViewSupportedOrientationsMask &value) {
+    auto result = std::string{};
+    auto separator = std::string{", "};
+
+    if (value & ModalHostViewSupportedOrientations::Portrait) {
+      result += "portrait" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::PortraitUpsideDown) {
+      result += "portrait-upside-down" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::Landscape) {
+      result += "landscape" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeLeft) {
+      result += "landscape-left" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeRight) {
+      result += "landscape-right" + separator;
+    }
+    if (!result.empty()) {
+      result.erase(result.length() - separator.length());
+    }
+    return result;
+}
+
+class ModalHostViewProps final : public ViewProps {
+ public:
+  ModalHostViewProps() = default;
+  ModalHostViewProps(const PropsParserContext& context, const ModalHostViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  ModalHostViewAnimationType animationType{ModalHostViewAnimationType::None};
+  ModalHostViewPresentationStyle presentationStyle{ModalHostViewPresentationStyle::FullScreen};
+  bool transparent{false};
+  bool statusBarTranslucent{false};
+  bool hardwareAccelerated{false};
+  bool visible{false};
+  bool animated{false};
+  ModalHostViewSupportedOrientationsMask supportedOrientations{static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)};
+  int identifier{0};
+};
+
+class SafeAreaViewProps final : public ViewProps {
+ public:
+  SafeAreaViewProps() = default;
+  SafeAreaViewProps(const PropsParserContext& context, const SafeAreaViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class SwitchProps final : public ViewProps {
+ public:
+  SwitchProps() = default;
+  SwitchProps(const PropsParserContext& context, const SwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool value{false};
+  SharedColor tintColor{};
+  SharedColor onTintColor{};
+  SharedColor thumbTintColor{};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+};
+
+class UnimplementedNativeViewProps final : public ViewProps {
+ public:
+  UnimplementedNativeViewProps() = default;
+  UnimplementedNativeViewProps(const PropsParserContext& context, const UnimplementedNativeViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string name{""};
+};
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h
new file mode 100644
index 0000000..8139124
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h
@@ -0,0 +1,293 @@
+/**
+* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+*
+* Do not edit this file as changes may cause incorrect behavior and will be lost
+* once the code is regenerated.
+*
+* @generated by codegen project: GenerateComponentHObjCpp.js
+*/
+
+#import <Foundation/Foundation.h>
+#import <React/RCTDefines.h>
+#import <React/RCTLog.h>
+
+NS_ASSUME_NONNULL_BEGIN
+
+@protocol RCTActivityIndicatorViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidDrawerLayoutViewProtocol <NSObject>
+- (void)openDrawer;
+- (void)closeDrawer;
+@end
+
+RCT_EXTERN inline void RCTAndroidDrawerLayoutHandleCommand(
+  id<RCTAndroidDrawerLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"openDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView openDrawer];
+  return;
+}
+
+if ([commandName isEqualToString:@"closeDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView closeDrawer];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidDrawerLayout", commandName);
+#endif
+}
+
+@protocol RCTAndroidHorizontalScrollContentViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidSwipeRefreshLayoutViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwipeRefreshLayoutHandleCommand(
+  id<RCTAndroidSwipeRefreshLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwipeRefreshLayout", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwipeRefreshLayout", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwipeRefreshLayout", commandName);
+#endif
+}
+
+@protocol RCTAndroidSwitchViewProtocol <NSObject>
+- (void)setNativeValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwitchHandleCommand(
+  id<RCTAndroidSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwitch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwitch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwitch", commandName);
+#endif
+}
+
+@protocol RCTDebuggingOverlayViewProtocol <NSObject>
+- (void)highlightTraceUpdates:(const NSArray *)updates;
+- (void)highlightElements:(const NSArray *)elements;
+- (void)clearElementsHighlights;
+@end
+
+RCT_EXTERN inline void RCTDebuggingOverlayHandleCommand(
+  id<RCTDebuggingOverlayViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"highlightTraceUpdates"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSArray class], @"array", @"DebuggingOverlay", commandName, @"1st")) {
+    return;
+  }
+#endif
+  const NSArray * updates = (NSArray *)arg0;
+
+  [componentView highlightTraceUpdates:updates];
+  return;
+}
+
+if ([commandName isEqualToString:@"highlightElements"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSArray class], @"array", @"DebuggingOverlay", commandName, @"1st")) {
+    return;
+  }
+#endif
+  const NSArray * elements = (NSArray *)arg0;
+
+  [componentView highlightElements:elements];
+  return;
+}
+
+if ([commandName isEqualToString:@"clearElementsHighlights"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView clearElementsHighlights];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"DebuggingOverlay", commandName);
+#endif
+}
+
+@protocol RCTAndroidProgressBarViewProtocol <NSObject>
+
+@end
+
+@protocol RCTPullToRefreshViewViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)refreshing;
+@end
+
+RCT_EXTERN inline void RCTPullToRefreshViewHandleCommand(
+  id<RCTPullToRefreshViewViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"PullToRefreshView", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"PullToRefreshView", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL refreshing = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:refreshing];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"PullToRefreshView", commandName);
+#endif
+}
+
+@protocol RCTInputAccessoryViewProtocol <NSObject>
+
+@end
+
+@protocol RCTModalHostViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTSafeAreaViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTSwitchViewProtocol <NSObject>
+- (void)setValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTSwitchHandleCommand(
+  id<RCTSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"Switch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"Switch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"Switch", commandName);
+#endif
+}
+
+@protocol RCTUnimplementedNativeViewViewProtocol <NSObject>
+
+@end
+
+NS_ASSUME_NONNULL_END
\ No newline at end of file
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp
new file mode 100644
index 0000000..429785b
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp
@@ -0,0 +1,24 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateShadowNodeCpp.js
+ */
+
+#include <react/renderer/components/rncore/ShadowNodes.h>
+
+namespace facebook::react {
+
+extern const char ActivityIndicatorViewComponentName[] = "ActivityIndicatorView";
+extern const char AndroidDrawerLayoutComponentName[] = "AndroidDrawerLayout";
+extern const char AndroidHorizontalScrollContentViewComponentName[] = "AndroidHorizontalScrollContentView";
+extern const char AndroidSwipeRefreshLayoutComponentName[] = "AndroidSwipeRefreshLayout";
+extern const char DebuggingOverlayComponentName[] = "DebuggingOverlay";
+extern const char PullToRefreshViewComponentName[] = "PullToRefreshView";
+extern const char SwitchComponentName[] = "Switch";
+extern const char UnimplementedNativeViewComponentName[] = "UnimplementedNativeView";
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h
new file mode 100644
index 0000000..3427808
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h
@@ -0,0 +1,109 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateShadowNodeH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/rncore/EventEmitters.h>
+#include <react/renderer/components/rncore/Props.h>
+#include <react/renderer/components/rncore/States.h>
+#include <react/renderer/components/view/ConcreteViewShadowNode.h>
+#include <jsi/jsi.h>
+
+namespace facebook::react {
+
+JSI_EXPORT extern const char ActivityIndicatorViewComponentName[];
+
+/*
+ * `ShadowNode` for <ActivityIndicatorView> component.
+ */
+using ActivityIndicatorViewShadowNode = ConcreteViewShadowNode<
+    ActivityIndicatorViewComponentName,
+    ActivityIndicatorViewProps,
+    ActivityIndicatorViewEventEmitter,
+    ActivityIndicatorViewState>;
+
+JSI_EXPORT extern const char AndroidDrawerLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidDrawerLayout> component.
+ */
+using AndroidDrawerLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidDrawerLayoutComponentName,
+    AndroidDrawerLayoutProps,
+    AndroidDrawerLayoutEventEmitter,
+    AndroidDrawerLayoutState>;
+
+JSI_EXPORT extern const char AndroidHorizontalScrollContentViewComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidHorizontalScrollContentView> component.
+ */
+using AndroidHorizontalScrollContentViewShadowNode = ConcreteViewShadowNode<
+    AndroidHorizontalScrollContentViewComponentName,
+    AndroidHorizontalScrollContentViewProps,
+    AndroidHorizontalScrollContentViewEventEmitter,
+    AndroidHorizontalScrollContentViewState>;
+
+JSI_EXPORT extern const char AndroidSwipeRefreshLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidSwipeRefreshLayout> component.
+ */
+using AndroidSwipeRefreshLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidSwipeRefreshLayoutComponentName,
+    AndroidSwipeRefreshLayoutProps,
+    AndroidSwipeRefreshLayoutEventEmitter,
+    AndroidSwipeRefreshLayoutState>;
+
+JSI_EXPORT extern const char DebuggingOverlayComponentName[];
+
+/*
+ * `ShadowNode` for <DebuggingOverlay> component.
+ */
+using DebuggingOverlayShadowNode = ConcreteViewShadowNode<
+    DebuggingOverlayComponentName,
+    DebuggingOverlayProps,
+    DebuggingOverlayEventEmitter,
+    DebuggingOverlayState>;
+
+JSI_EXPORT extern const char PullToRefreshViewComponentName[];
+
+/*
+ * `ShadowNode` for <PullToRefreshView> component.
+ */
+using PullToRefreshViewShadowNode = ConcreteViewShadowNode<
+    PullToRefreshViewComponentName,
+    PullToRefreshViewProps,
+    PullToRefreshViewEventEmitter,
+    PullToRefreshViewState>;
+
+JSI_EXPORT extern const char SwitchComponentName[];
+
+/*
+ * `ShadowNode` for <Switch> component.
+ */
+using SwitchShadowNode = ConcreteViewShadowNode<
+    SwitchComponentName,
+    SwitchProps,
+    SwitchEventEmitter,
+    SwitchState>;
+
+JSI_EXPORT extern const char UnimplementedNativeViewComponentName[];
+
+/*
+ * `ShadowNode` for <UnimplementedNativeView> component.
+ */
+using UnimplementedNativeViewShadowNode = ConcreteViewShadowNode<
+    UnimplementedNativeViewComponentName,
+    UnimplementedNativeViewProps,
+    UnimplementedNativeViewEventEmitter,
+    UnimplementedNativeViewState>;
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.cpp
new file mode 100644
index 0000000..9762c3c
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.cpp
@@ -0,0 +1,16 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateStateCpp.js
+ */
+#include <react/renderer/components/rncore/States.h>
+
+namespace facebook::react {
+
+
+
+} // namespace facebook::react
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.h
new file mode 100644
index 0000000..55114f1
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/States.h
@@ -0,0 +1,139 @@
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateStateH.js
+ */
+#pragma once
+
+#ifdef ANDROID
+#include <folly/dynamic.h>
+#include <react/renderer/mapbuffer/MapBuffer.h>
+#include <react/renderer/mapbuffer/MapBufferBuilder.h>
+#endif
+
+namespace facebook::react {
+
+class ActivityIndicatorViewState {
+public:
+  ActivityIndicatorViewState() = default;
+
+#ifdef ANDROID
+  ActivityIndicatorViewState(ActivityIndicatorViewState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class AndroidDrawerLayoutState {
+public:
+  AndroidDrawerLayoutState() = default;
+
+#ifdef ANDROID
+  AndroidDrawerLayoutState(AndroidDrawerLayoutState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class AndroidHorizontalScrollContentViewState {
+public:
+  AndroidHorizontalScrollContentViewState() = default;
+
+#ifdef ANDROID
+  AndroidHorizontalScrollContentViewState(AndroidHorizontalScrollContentViewState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class AndroidSwipeRefreshLayoutState {
+public:
+  AndroidSwipeRefreshLayoutState() = default;
+
+#ifdef ANDROID
+  AndroidSwipeRefreshLayoutState(AndroidSwipeRefreshLayoutState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class DebuggingOverlayState {
+public:
+  DebuggingOverlayState() = default;
+
+#ifdef ANDROID
+  DebuggingOverlayState(DebuggingOverlayState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class PullToRefreshViewState {
+public:
+  PullToRefreshViewState() = default;
+
+#ifdef ANDROID
+  PullToRefreshViewState(PullToRefreshViewState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class SwitchState {
+public:
+  SwitchState() = default;
+
+#ifdef ANDROID
+  SwitchState(SwitchState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+class UnimplementedNativeViewState {
+public:
+  UnimplementedNativeViewState() = default;
+
+#ifdef ANDROID
+  UnimplementedNativeViewState(UnimplementedNativeViewState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+  MapBuffer getMapBuffer() const {
+    return MapBufferBuilder::EMPTY();
+  };
+#endif
+};
+
+} // namespace facebook::react
\ No newline at end of file
diff --git a/node_modules/react-native/index.js b/node_modules/react-native/index.js
index f087b70..34c67d2 100644
--- a/node_modules/react-native/index.js
+++ b/node_modules/react-native/index.js
@@ -746,4 +746,32 @@ if (__DEV__) {
       );
     },
   });
+
+  Object.defineProperty(module.exports, 'ColorPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').ColorPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'EdgeInsetsPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').EdgeInsetsPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'PointPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').PointPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'ViewPropTypes', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').ViewPropTypes;
+    },
+  });
 }
