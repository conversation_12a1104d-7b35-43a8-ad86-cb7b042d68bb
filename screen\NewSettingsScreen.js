import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useCallback,
  useEffect,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Footer from './footer';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
import GCalendar from '../assets/svg/GCalendar';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import { ScrollView as ScrollViewGH, ScrollView, FlatList } from 'react-native-gesture-handler';
import DraggableFlatList from 'react-native-draggable-flatlist';
import VenueSettingScreen from './components/VenueSettingsScreen';
import MainLoyaltySettingsScreen from './components/LoyaltySettingsScreen';
import ReservationSettingScreen from './components/ReservationSettingScreen';

const NewSettingsScreen = (props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // show use state
  const [showLoyaltyMain, setShowLoyaltyMain] = useState(false);
  const [showVenueMain, setShowVenueMain] = useState(true);
  const [showReservationMain, setShowReservationMain] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // modal use state

  // switch use state

  // dropdownpicker use state

  // time use state

  // other use state

  // textinput use state

  //useEffect start
  useEffect(() => {
    setShowVenueMain(true);
    setShowReservationMain(false);
    setShowLoyaltyMain(false);
  }, []);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);


  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Reservation Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <View
      style={[
        styles.container,
        !isTablet() ? { transform: [{ scaleX: 1 }, { scaleY: 1 }] } : {},
      ]}>
      {/* Sidebar */}
      {/* <View
        style={[
          styles.sidebar,
          !isTablet() ? { width: windowWidth * 0.08 } : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}
      <View
        style={{
          flex: 1,
          position: 'absolute',
          bottom: Platform.OS == 'ios' ? windowHeight * 0.075 : windowHeight * 0.1,
          zIndex: 20,
        }}>
        <Footer />
      </View>

      {/* New settings screen page content */}
      {/* Left container */}
      <View
        style={{
          marginLeft: Platform.OS === 'ios' ? 2 : -2,
          width: windowWidth * 0.2,
          height: Platform.OS == 'ios' ? windowHeight * 0.83 : windowHeight * 0.754,
          borderLeftWidth: Platform.OS === 'ios' ? 0.1 : 0.001,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 5,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,

          elevation: 3,
        }}>
        {/* Header */}
        {/* <View
          style={{
            height: windowHeight * 0.05,
            backgroundColor: '#E5E7E9',
            width: windowWidth * 0.1955,
            justifyContent: 'center',
            marginBottom: windowHeight * 0.03,
          }}>
          <Text
            style={{
              textAlign: 'center',
            }}>
            Settings
          </Text>
        </View> */}
        {/* Body */}
        <View
          style={{
            justifyContent: 'space-between',
            // borderWidth: 1,
            height: '100%',
            width: windowWidth * 0.1955,
          }}>
          <View
            style={{
              // height: windowHeight * 0.05,
              backgroundColor: '#FFFFFF',
              width: windowWidth * 0.1955,
              marginBottom: windowHeight * 0.03,
            }}>
            <TouchableOpacity
              onPress={() => {
                setShowVenueMain(true);
                setShowLoyaltyMain(false);
                setShowReservationMain(false);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                borderBottomWidth: 1,
                borderBottomColor: '#EBEDEF',
                width: '100%',
                paddingVertical: windowHeight * 0.02,
                backgroundColor: showVenueMain ? Colors.highlightColor : Colors.whiteColor,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Venue Settings
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setShowVenueMain(false);
                setShowLoyaltyMain(false);
                setShowReservationMain(true);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                borderBottomWidth: 1,
                borderBottomColor: '#EBEDEF',
                width: '100%',
                paddingVertical: windowHeight * 0.02,
                backgroundColor: showReservationMain ? Colors.highlightColor : Colors.whiteColor,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Reservations
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity>
            {/* <TouchableOpacity
              onPress={() => {
                setShowVenueMain(false);
                setShowLoyaltyMain(false);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                borderBottomWidth: 1,
                borderBottomColor: '#EBEDEF',
                width: '100%',
                paddingVertical: windowHeight * 0.02,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Reservations
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity> */}
            {/* <TouchableOpacity
              onPress={() => {
                setShowVenueMain(false);
                setShowLoyaltyMain(false);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                borderBottomWidth: 1,
                  borderBottomColor: '#EBEDEF',
                  width: '100%',
                paddingVertical: windowHeight * 0.02,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Payment Settings
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity> */}
            {/* <TouchableOpacity
              onPress={() => {
                setShowVenueMain(false);
                setShowLoyaltyMain(false);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                borderBottomWidth: 1,
                  borderBottomColor: '#EBEDEF',
                  width: '100%',
                paddingVertical: windowHeight * 0.02,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Tag Management
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity> */}
            {/* <TouchableOpacity
              onPress={() => {
                setShowVenueMain(false);
                setShowLoyaltyMain(true);
              }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                // borderBottomWidth: 1,
                width: '100%',
                paddingVertical: windowHeight * 0.02,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Loyalty
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity> */}
          </View>
          <View>
            {/* <TouchableOpacity
              onPress={() => { }}
              style={{
                paddingHorizontal: windowWidth * 0.01,
                // borderBottomWidth: 1,
                width: '100%',
                backgroundColor: '#ffffff',
                paddingVertical: windowHeight * 0.02,
              }}>
              <View
                style={{
                  // borderWidth: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Account
                </Text>
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              </View>
            </TouchableOpacity> */}
          </View>
        </View>
      </View>
      {/* Content bar */}
      <View
        style={{
          flex: 1,
          // borderWidth:1,
          height: Platform.OS == 'ios' ? '89.9%' : '93.7%',
        }}>
        {showLoyaltyMain ? (
          <MainLoyaltySettingsScreen changeLayout={showLoyaltyMain} navigation={navigation} />
        ) : null}
        {showVenueMain ? (
          <VenueSettingScreen navigation={props.navigation} />
        ) : null}
        {showReservationMain ? (
          <ReservationSettingScreen navigation={props.navigation} />
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#EBEDEF',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.5,
    height: Dimensions.get('window').height * 0.2,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default NewSettingsScreen;
