
import { FlatList } from 'react-native-gesture-handler';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import React, { Component, useState, useEffect, useRef, createRef } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, Button, Dimensions, TextInput, TouchableOpacity, Alert, Modal, Linking, Platform, ActivityIndicator } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'

import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { max } from 'moment';
import Feather from 'react-native-vector-icons/Feather';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import TableBar from './components/TableBar';
import Switch from 'react-native-switch-pro';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { CommonStore } from '../store/commonStore';
//import ToggleSwitch from 'toggle-switch-react-native'; 

const QueueScreen = props => {
    const {
        navigation,
    } = props;

    const [orderId, setOrderId] = useState('');
    const [param, setParam] = useState(1);
    const [showItem, setShowItem] = useState(false);
    const [switchState, setSwitchState] = useState('');
    const [count, setCount] = useState('');

    const [refArray, setRefArray] = useState([]);

    const setState = () => { };

    const currOutlet = MerchantStore.useState(s => s.currOutlet);

    const userQueues = OutletStore.useState(s => s.userQueues);
    const userQueuesDict = OutletStore.useState(s => s.userQueuesDict);

    const outletTablesDict = OutletStore.useState(s => s.outletTablesDict);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    useEffect(() => {
        setRefArray(ref => (
            Array(userQueues.length).fill().map((_, i) => ref[i] || createRef())
        ));
    }, [userQueues.length]);

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity style={{
            }} onPress={() => { props.navigation.goBack(); }}>
                <View
                    style={{
                        marginLeft: 10,
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        marginTop: Platform.OS == 'android' ? 9 : 10,
                        opacity: 0.8,
                    }}>
                    <Icon
                        name="chevron-back"
                        size={26}
                        color={Colors.whiteColor}
                        style={{}}
                    />

                    <Text
                        style={{
                            color: Colors.whiteColor,
                            fontSize: 20,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-SemiBold',
                            // lineHeight: 22,
                            //marginTop: -3,
                            marginBottom: Platform.OS == 'android' ? 2 : 0,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -2,
            }}>
                <Text
                    style={{
                        fontSize: 25,
                        // lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 0.8,
                    }}>
                    Queue
                </Text>
            </View>
        ),
        headerRight: () => (
            <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
            }}>
                <TouchableOpacity
                    onPress={() => { props.navigation.navigate('Profile') }}>
                    <Image style={{
                        width: 32,
                        height: 32,
                        marginTop: 8,
                        marginRight: 25,
                    }} source={require('../assets/image/drawer.png')} />
                </TouchableOpacity>
            </View>
        ),
    });

    // componentDidMount = () => {
    //     getOrderHistory();

    //     getOutletQueue();
    //     setInterval(() => {
    //         getOutletQueue();
    //     }, 5000);

    //     getOutlet();
    // }
    // closeSwipeable(item) {
    //     swipeableRef.close(item);
    // }

    const closeAllSwipeable = () => {
        for (const ref of refArray) {
            // ref.close()
            if (ref && ref.current) {
                ref.current.close();
            }
        }
    }

    const getOrderHistory = () => {
        ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(result => {
            setState({ orderId: result, count: result[0].orderItems.length })
        }).catch(err => { console.log(err) })
    }

    const showOrderItem = param => {
        if (showItem == false) {
            return setState({ showItem: true }), param.expand = true
        } else {
            return setState({ showItem: false }), param.expand = false
        }

    }

    const getOutlet = () => {
        ApiClient.GET(API.outlet + User.getOutletId()).then(result => {
            setState({ switchState: result[0].queueStatus })
        }).catch(err => { console.log(err) })

    }

    const switchQueueStatus = (value) => {
        // self.onButtonClickHandler();
        var body = {
            // outletId: User.getOutletId()
            outletId: currOutlet.uniqueId,
            queueStatus: value,
        };

        CommonStore.update((s) => {
            s.isLoading = true;
        });

        ApiClient.POST(API.switchQueueStatus, body).then(result => {
            // if (result.queueStatus === true) {
            //     // Alert.alert("Queue is closed now")
            //     return self.setState({ switchState: false })

            // } else if (result.queueStatus === false) {
            //     //   Alert.alert("Queue is open now")
            //     return self.setState({ switchState: true })
            // }
            if (result.status) {
                setTimeout(() => {
                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                }, 1000);
            }
        }).catch(err => { console.log(err) })
    }

    const getOutletQueue = () => {
        ApiClient.GET(API.getAllOutletQueue + User.getOutletId()).then(result => {
            setState({ outletQueue: result })
        }).catch(err => { console.log(err) })
    }

    const seatedForQueue = (param) => {
        var body = {
            queueId: param,
            seated: 1,
        }

        ApiClient.POST(API.seatedQueue, body, false).then(result => {
            if (result.success) {
                getOutletQueue()
                Alert.alert("Successfully Seated")
                closeAllSwipeable()
            }
        }).catch(err => { console.log(err) })
    }

    const notifyForQueue = (param) => {
        var body = {
            queueId: param,
            seated: 1,
        }

        ApiClient.POST(API.notifyQueueMember, body, false).then(result => {
            if ({ outletQueue: result }) {
                getOutletQueue()
                Alert.alert("Successfully Notified")
            }
        }).catch(err => { console.log(err) })
    }


    // onToggle = () => {	
    //             setState(prevState => {
    //                 return { 	
    //                     isOn: !prevState.isOn,	
    //                 };   
    //             });   	
    //         };

    const renderOrderTop = ({ item }) => (

        <View style={styles.topFlat}>
            <Text style={{ fontSize: 20, fontFamily: 'NunitoSans-SemiBold' }}>
                {!item.table ? null : item.table.code}
            </Text>
            <Icon name={'chevron-down'} style={{ color: Colors.primaryColor, position: 'absolute', bottom: -3, right: 0 }} size={25} />
            <View style={[styles.smallCircle, { top: -10, right: -10 }]}>
                <Text style={styles.smallCircleFont}>{count}</Text>
            </View>
        </View>

    )



    const renderOrder = ({ item, index }) => (
        <View style={[styles.insideFlat]}>
            {/* <Swipeable
                ref={(swipe) => item = swipe}
                renderRightActions={() => rightAction(item)}
                onSwipeableOpen={close}
                </Swipeable> */}

            <Swipeable
                //ref={swipeableRef}
                //onSwipeableOpen={() => closeSwipeable(item)}
                renderRightActions={() => rightAction(item)}
                // ref={ref => {
                //     if (ref && !refArray.includes(ref)) {
                //         refArray[index] = ref;
                //     }
                // }}
                ref={refArray[index]}
                autoClose="true"
            >
                <View style={{ flexDirection: 'row', padding: 10, alignSelf: 'center', borderBottomWidth: 1, borderBottomColor: '#E5E4E2', backgroundColor: 'white' }}>
                    <View style={styles.topPart}>
                        <Text style={{ fontSize: 18, fontFamily: 'NunitoSans-Bold', alignSelf: 'center', textAlign: 'center' }}>{item.userName == null ? null : item.userName}</Text>
                        <Text style={{ fontSize: 12, fontFamily: 'NunitoSans-Regular', alignSelf: 'center', marginTop: 3 }}>CUSTOMER</Text>
                    </View>
                    <View style={[styles.topPart, { flex: 2, alignItems: 'center' }]}>
                        <View style={{ width: '100%' }}>
                            <Text style={{ fontSize: 25, fontFamily: 'NunitoSans-Bold', alignSelf: 'center' }}>{item.number == null ? null : item.number}</Text>
                            <Text style={{ fontFamily: 'NunitoSans-Regular', alignSelf: 'center', fontSize: 13, marginBottom: 5 }}>NUMBER</Text>
                        </View>
                    </View>
                    <View style={[styles.topPart, { flex: 2, alignItems: 'center' }]}>
                        <Text style={{ fontSize: 22, fontFamily: 'NunitoSans-Bold', alignSelf: 'center' }}>{item.pax == null ? null : item.pax}</Text>
                        <Text style={{ fontFamily: 'NunitoSans-Regular', alignSelf: 'center', fontSize: 13 }}>PAX</Text>
                    </View>
                </View>
            </Swipeable>
        </View>
    )

    const rightAction = (item) => {
        return (
            <View style={styles.insideSwipe}>
                <TouchableOpacity
                    style={{ width: '33.36%' }}
                    onPress={() => { notifyForQueue(item.id) }}>
                    <View style={[styles.swipeButton, { backgroundColor: Colors.primaryColor }]}>
                        <MIcon name={'comment-alert-outline'} size={25} color={Colors.whiteColor} />
                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: 'white' }}>Notify</Text>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity
                    style={{ width: '33.35%' }}
                    onPress={() => { Linking.openURL(`tel:${item.userPhone}`) }}>
                    <View style={[styles.swipeButton, { backgroundColor: '#8fbc8f' }]}>
                        {/* <Image style={{ width: 40, height: 50 }} source={require('../assets/image/call.png')} /> */}
                        <Feather name={'phone-call'} size={25} color={Colors.whiteColor} />
                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: 'white' }}>Call</Text>
                    </View>
                </TouchableOpacity>

                <TouchableOpacity
                    style={{ width: '33.34%' }}
                    onPress={() => { seatedForQueue(item.uniqueId) }}>
                    <View style={[styles.swipeButton, { backgroundColor: '#8F8F8F' }]}>
                        {/* <Image style={{ width: 45, height: 48, marginTop: 7 }} source={require('../assets/image/seated.png')} /> */}
                        <MIcon name={'checkbox-marked-circle-outline'} size={25} color={'white'} />
                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: 'white' }}>Seated</Text>
                    </View>
                </TouchableOpacity>
            </View>
        )

    };

    return (
        <View style={styles.container}>


            {/* <View style={{ height: '22%', backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 20, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}> */}
            <View style={{ flex: 1.8, backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 15, paddingVertical: 15, paddingBottom: 5, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}>
                <TableBar orderTables={orderId} />
            </View>
            {/*  <View>
          <FlatList style={{ paddingTop:'10%' }} horizontal={true} showsHorizontalScrollIndicator={false}
                        data={orderId}
                        renderItem={renderOrderTop}
                        keyExtractor={(item, index) => String(index)} />
                
    </View> */}
            <View style={{ height: '10%', alignItems: 'center', justifyContent: 'center', flexDirection: 'row' }}>
                <View style={{ marginRight: 10 }}>
                    <Text style={{ fontSize: 'NunitoSans-Bold', fontSize: 20, }}>Queue</Text>
                </View>

                {isLoading ?
                    <ActivityIndicator size={'small'} color={Colors.primaryColor} />
                    :
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{ transform: [{ scaleX: .9 }, { scaleY: .9 }] }}>
                            <Switch
                                onSyncPress={(value) =>
                                    // setState({ switchState: value })
                                    switchQueueStatus(value)
                                }
                                // onChange={() => switchQueueStatus(this)}
                                value={currOutlet.queueStatus}
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive='#dddddd'
                            />
                        </View>

                        <Text
                            style={{ fontSize: 20, marginLeft: 10, color: currOutlet.queueStatus ? Colors.primaryColor : Colors.fieldtTxtColor }}>
                            {currOutlet.queueStatus ? 'ON' : 'OFF'}
                        </Text>
                    </View>
                }

            </View>

            <View style={{ height: '68%' }}>

                <View>
                    <FlatList style={{ paddingVerticle: 2 }}
                        // data={outletQueue}
                        data={userQueues}
                        renderItem={renderOrder}
                        keyExtractor={(item, index) => 'order' + index}
                        // keyExtractor={(item, index) => String(index)}
                        contentContainerStyle={{
                            // paddingBottom: Dimensions.get('screen').height * 0.12,
                        }}
                    />
                </View>

                {/* }  */}

            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff'
    },
    topFlat: {
        backgroundColor: 'white',
        shadowColor: 'grey',
        shadowRadius: 3,
        shadowOpacity: 20,
        shadowOffset: { width: 1, height: 1 },
        margin: 10,
        borderRadius: 8,
        alignItems: 'center',
        paddingTop: 15,
        height: 60,
        width: 60,
    },
    section: {
        height: '20%',
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        alignItems: 'center',
        paddingHorizontal: 10
    },
    circle: {
        height: 65,
        width: 65,
        borderRadius: (65) / 2,
        borderWidth: 1,
        borderColor: Colors.secondaryColor,
        justifyContent: 'center',
        alignItems: 'center'
    },
    smallCircle: {
        width: 25,
        height: 25,
        backgroundColor: Colors.primaryColor,
        borderRadius: 12.5,
        position: 'absolute',
        top: -5,
        right: -5,
        alignItems: 'center',
        justifyContent: 'center'

    },
    smallCircleFont: {
        color: 'white',
        fontFamily: 'NunitoSans-Bold',
        fontSize: 15
    },
    circleIcon: {
        width: '50%',
        height: "50%",
        resizeMode: 'contain'
    },
    insideFlat: {
        margin: 10,
        flex: 1,
        borderRadius: 10,
        overflow: 'hidden',
        elevation: 10,
        backgroundColor: Colors.fieldtBgColor,
        borderColor: 'grey',
        borderWidth: 1
    },
    topFlat: {
        backgroundColor: 'white',
        shadowColor: 'grey',
        shadowRadius: 3,
        shadowOpacity: 20,
        shadowOffset: { width: 1, height: 1 },
        margin: 10,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        height: 60,
        width: 60,

    },
    topPart: {
        paddingHorizontal: 10,
        justifyContent: 'center',
        flex: 3,
        alignSelf: 'center',
    },
    bottomPart: {
        flexDirection: 'row',
        padding: 10,
        paddingHorizontal: 10
    },
    primaryFont: {
        fontFamily: 'NunitoSans-Regular'
    },
    insideSwipe: {
        justifyContent: 'center',
        alignItems: 'center',
        //backgroundColor: 'orange',
        flexDirection: 'row',
        width: '60%',

    },
    swipeButton: {
        fontFamily: 'NunitoSans-Regular',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        //width: '33%',
    },
    swipeFont: {
        fontSize: 14,
        fontFamily: 'NunitoSans-Bold'
    },

})
export default QueueScreen







