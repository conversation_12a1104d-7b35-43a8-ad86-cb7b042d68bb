import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  StyleSheet,
  TouchableOpacity,
  Image,
  View,
  Text,
  TextInput,
  Platform,
  Dimensions,
  Alert,
  Modal as ModalComponent,
  ActivityIndicator,
  KeyboardAwareScrollView
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import {
  EXPAND_TAB_TYPE,
} from '../constant/common';
import Styles from '../constant/Styles';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import {
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import UserIdleWrapper from '../components/userIdleWrapper';
import Feather from 'react-native-vector-icons/Feather';
import DraggableFlatList, {
  ShadowDecorator
} from 'react-native-draggable-flatlist';
import APILocal from '../util/apiLocalReplacers';
import Switch from 'react-native-switch-pro';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const QueueConfigScreen = ({ navigation }) => {

  const [switchMerchant, setSwitchMerchant] = useState(!isTablet());
  const { width: windowWidth, height: windowHeight } = Dimensions.get('window');

  ////////////////////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => { setIsMounted(false) };
    }, [])
  );

  ////////////////////////////////////////////////////////////////////////

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const userName = UserStore.useState((s) => s.name);

  const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  ////////////////////////////////////////////////////////////////////////
  // State
  const [loading, setLoading] = useState(false);
  const queueConfig = OutletStore.useState((s) => s.queueConfig);
  const [queueSizeOptions, setQueueSizeOptions] = useState(queueConfig && queueConfig.queueSizeOptions ? queueConfig.queueSizeOptions : []);

  // Utility Method - For Object Compare Only
  const sortQueueOptions = (options) => {
    return options.map(option => ({
      minPax: option.minPax,
      maxPax: option.maxPax,
      name: option.name,
      isEnabled: option.isEnabled,
    }));
  };

  const isSaved = useMemo(() => {
    if (!queueConfig) return false;

    return JSON.stringify(sortQueueOptions(queueConfig.queueSizeOptions)) === JSON.stringify(sortQueueOptions(queueSizeOptions));
  }, [queueConfig, queueSizeOptions]);

  //////////////////////////////////////
  const [noConfigModalVisible, setNoConfigModalVisible] = useState(queueConfig === null || !queueConfig.queueSizeOptions || queueConfig.queueSizeOptions.length === 0);
  const [newCategoryModalVisible, setNewCategoryModalVisible] = useState(false);
  const [editingQueueOptionIndex, setEditingQueueOptionIndex] = useState(null);

  const [categoryName, setCategoryName] = useState('');
  const [categoryMinPax, setCategoryMinPax] = useState(1);
  const [categoryMaxPax, setCategoryMaxPax] = useState(1);
  const [isMinPaxNeeded, setIsMinPaxNeeded] = useState(true);
  const [isMaxPaxNeeded, setIsMaxPaxNeeded] = useState(true);

  // Reset State
  useEffect(() => {
    // Field set to text, will auto convert when save
    if (editingQueueOptionIndex === null || queueSizeOptions.length === 0) {
      setCategoryName('');
      setCategoryMinPax('');
      setCategoryMaxPax('');
      setIsMinPaxNeeded(true);
      setIsMaxPaxNeeded(true);
    }
    else {
      setCategoryName(queueSizeOptions[editingQueueOptionIndex].name);
      setCategoryMinPax(queueSizeOptions[editingQueueOptionIndex].minPax !== null ? queueSizeOptions[editingQueueOptionIndex].minPax.toString() : '');
      setCategoryMaxPax(queueSizeOptions[editingQueueOptionIndex].maxPax !== null ? queueSizeOptions[editingQueueOptionIndex].maxPax.toString() : '');
      setIsMinPaxNeeded(queueSizeOptions[editingQueueOptionIndex].minPax !== null);
      setIsMaxPaxNeeded(queueSizeOptions[editingQueueOptionIndex].maxPax !== null);
    }
  }, [newCategoryModalVisible, editingQueueOptionIndex, queueSizeOptions]);
  ////////////////////////////////////////////////////////////////////////

  // Handlers

  const handleBackButtonOnPressed = async () => {
    if (isSaved) {
      navigation.goBack();
      return;
    }

    Alert.alert(
      'Unsaved Changes',
      'You have pending changes that will be lost. Do you want to save them before leaving?',
      [
        {
          text: 'Discard',
          style: 'destructive',
          onPress: () => navigation.goBack()
        },
        {
          text: 'Save',
          onPress: async () => {
            try {
              await handleSaveQueueConfig();
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to save changes. Please try again.');
            }
          }
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ],
      { cancelable: true }
    );
  };

  // NoConfig Action - Use Template
  const handleNoConfigUseTemplate = () => {
    setQueueOptionTemplate();
    setNoConfigModalVisible(false);
  };

  // NoConfig Action - Use Custom
  const handleNoConfigUseCustom = () => {
    setNewCategoryModalVisible(true);
    setNoConfigModalVisible(false);
  };

  const handleCloseNewCategoryModal = () => {
    setNewCategoryModalVisible(false);
    setEditingQueueOptionIndex(null);
  };

  const handleAddUpdateQueueOption = () => {
    // If Category Name is empty
    if (!categoryName || categoryName.trim() === '') {
      Alert.alert('Error', 'Category name is required');
      return;
    }

    if (isMinPaxNeeded) {
      // Invalid Type
      if (Number.isNaN(parseInt(categoryMinPax, 10))) {
        Alert.alert('Error', 'Invalid minimum pax');
        return;
      }

      // If MinPax < 0
      if (parseInt(categoryMinPax, 10) < 0) {
        Alert.alert('Error', 'Minimum pax cannot be less than 0');
        return;
      }
    }

    if (isMaxPaxNeeded) {
      // Invalid Type
      if (Number.isNaN(parseInt(categoryMaxPax, 10))) {
        Alert.alert('Error', 'Invalid maximum pax');
        return;
      }

      // If MaxPax < 0
      if (parseInt(categoryMaxPax, 10) < 0) {
        Alert.alert('Error', 'Maximum pax cannot be less than 0');
        return;
      }
    }

    if (isMinPaxNeeded && isMaxPaxNeeded) {
      // If MinPax > Max Pax
      if (parseInt(categoryMinPax, 10) > parseInt(categoryMaxPax, 10)) {
        Alert.alert('Error', 'Minimum pax cannot be greater than maximum pax');
        return;
      }
    }

    if (editingQueueOptionIndex === null) {
      addNewQueueOption();
    }
    else {
      updateQueueOption(editingQueueOptionIndex);
      setEditingQueueOptionIndex(null);
    }
    setNewCategoryModalVisible(false);
  };

  const handleEditQueueOption = (index) => {
    setEditingQueueOptionIndex(index);
    setNewCategoryModalVisible(true);
  };

  const handleUpdateQueueOptionAvailability = async (index, isEnabled) => {
    setLoading(true);

    if (!isSaved) {
      setLoading(false);
      Alert.alert('Error', 'Please save your changes first');
      return;
    }

    try {
      // Update Local State
      const updatedQueueSizeOptions = [...queueSizeOptions];
      updatedQueueSizeOptions[index] = {
        ...updatedQueueSizeOptions[index],
        isEnabled
      };
      setQueueSizeOptions(updatedQueueSizeOptions);

      // Update Firebase
      await updateQueueOptionAvailability(index, isEnabled);
    }
    catch (error) {
      console.error('Error updating queue option availability:', error);
      Alert.alert('Error', 'Failed to update queue option availability');
    }
    finally {
      setLoading(false);
    }
  };

  const handleSaveQueueConfig = async () => {
    setLoading(true);

    if (isSaved) {
      setLoading(false);
      Alert.alert('Success', 'Queue configuration saved successfully');
      return;
    }

    try {
      if (queueConfig === null) {
        let body = {
          queueSizeOptions,
          outletId: currOutlet.uniqueId,
          merchantId: currOutlet.merchantId,
        }

        await APILocal.createQueueSettings({ body });
      }
      else {
        let body = {
          queueSizeOptions,
          queueConfigId: queueConfig.uniqueId,
        }

        await APILocal.updateQueueSettings({ body });
      }

      Alert.alert('Success', 'Queue configuration saved successfully');
    }
    catch (error) {
      console.error('Error saving queue config:', error);
      Alert.alert('Error', 'Failed to save queue configuration');
    }
    finally {
      setLoading(false);
    }
  };

  ////////////////////////////////////////////////////////////////////////

  const setQueueOptionTemplate = () => {
    const queueSizeOptionsTemplate = [
      {
        name: '1 to 2 pax',
        minPax: 1,
        maxPax: 2,
        isEnabled: true
      },
      {
        name: '3 to 4 pax',
        minPax: 3,
        maxPax: 4,
        isEnabled: true
      },
      {
        name: '5 to 6 pax',
        minPax: 5,
        maxPax: 6,
        isEnabled: true
      },
      {
        name: '7 pax or above',
        minPax: 7,
        maxPax: null,
        isEnabled: true
      }
    ];

    setQueueSizeOptions(queueSizeOptionsTemplate);
  };

  const addNewQueueOption = () => {
    const newQueueSizeOption = {
      name: categoryName,
      minPax: isMinPaxNeeded ? parseInt(categoryMinPax, 10) : null,
      maxPax: isMaxPaxNeeded ? parseInt(categoryMaxPax, 10) : null,
      isEnabled: true,
    };

    setQueueSizeOptions([...queueSizeOptions, newQueueSizeOption]);
  };

  const updateQueueOption = (index) => {
    setQueueSizeOptions(prevQueueSizeOptions =>
      prevQueueSizeOptions.map((option, i) =>
        i === index ? {
          ...option,
          name: categoryName,
          minPax: isMinPaxNeeded ? parseInt(categoryMinPax, 10) : null,
          maxPax: isMaxPaxNeeded ? parseInt(categoryMaxPax, 10) : null
        } : option
      )
    );
  };

  const updateQueueOptionAvailability = async (index, isEnabled) => {
    try {
      let body = {
        queueConfigId: queueConfig.uniqueId,
        index,
        isEnabled
      }

      const response = await APILocal.updateQueueOptionAvailability({ body });

      if (response.status === 'success') {
        Alert.alert('Success', 'Queue option availability updated successfully');
      } else {
        Alert.alert('Error', 'Failed to update queue option availability');
      }
    } catch (error) {
      console.error('Error updating queue option availability:', error);
      Alert.alert('Error', 'Failed to update queue option availability, please save your changes first');
    }
  };

  const removeQueueOption = (index) => {
    setQueueSizeOptions(prevQueueSizeOptions =>
      prevQueueSizeOptions.filter((_, i) => i !== index)
    );
  };
  ////////////////////////////////////////////////////////////////////////
  // Renderer

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? "27%" : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Queue Configuration
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const styles = StyleSheet.create({
    headerLeftStyle: {
      width: windowWidth * 0.17,
      justifyContent: 'center',
      alignItems: 'center',
    },
    container: {
      flex: 1,
      flexDirection: 'row',
      backgroundColor: Colors.highlightColor,
    },
    transformView: {
      transform: [{ scaleX: 1 }, { scaleY: 1 }]
    },
    flexRowContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: Colors.modalBgColor,
      alignItems: 'center',
      justifyContent: 'center',
    },

    // NoQueueConfig Modal Styles
    noQueueConfigModalContainer: {
      width: windowWidth * 0.4,
      height: windowHeight * 0.4,
      backgroundColor: Colors.whiteColor,
      borderRadius: 15,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    noQueueConfigModalTitle: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 24,
      color: Colors.primaryColor,
    },
    noQueueConfigModalDescription: {
      maxWidth: '80%',
      fontFamily: 'NunitoSans-Regular',
      fontSize: 16,
      color: Colors.fontDark,
      textAlign: 'center',
      marginTop: 20,
    },
    noQueueConfigModalTitleContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noQueueConfigModalButtonContainer: {
      height: '30%',
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: Colors.lightGrey,
      borderBottomLeftRadius: 15,
      borderBottomRightRadius: 15,
    },
    noQueueConfigModalButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noQueueConfigModalButtonText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.primaryColor,
    },
    noQueueConfigModalButtonDescription: {
      maxWidth: '70%',
      fontFamily: 'NunitoSans-Regular',
      fontSize: 12,
      color: Colors.fontDark,
      textAlign: 'center',
    },

    // New Category Modal Styles
    newCategoryModalContainer: {
      width: windowWidth * 0.45,
      height: windowHeight * 0.7,
      backgroundColor: Colors.whiteColor,
      borderRadius: 15,
      paddingVertical: 40,
      paddingHorizontal: 20,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    newCategoryModalCloseButtonContainer: {
      height: '10%',
      width: '90%',
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    newCategoryModalButtonContainer: {
      height: '15%',
      width: '70%',
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    newCategoryModalDiscardButtonText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.descriptionColor,
    },
    newCategoryModalAddButtonContainer: {
      paddingHorizontal: 20,
      paddingVertical: 10,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: Colors.primaryColor,
      borderRadius: 10,
    },
    newCategoryModalAddButtonText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.whiteColor,
    },
    newCategoryModalInputFormContainer: {
      flex: 1,
      width: '80%',
    },
    newCategoryModalInputFormItemContainer: {
      height: '30%',
      flexDirection: 'column',
    },
    newCategoryModalInputFormItemTitleContainer: {
      height: '35%',
      flexDirection: 'row',
      alignItems: 'center',
    },
    newCategoryModalInputFormItemText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 18,
      color: Colors.fontDark,
      marginRight: 10,
    },
    newCategoryModalInputFormItemInputContainer: {
      height: '65%',
      flexDirection: 'row',
      alignItems: 'center',
    },
    newCategoryModalInputFormItemInput: {
      height: '80%',
      width: '80%',
      paddingVertical: 5,
      paddingHorizontal: 10,
      backgroundColor: Colors.fieldtBgColor,
      borderRadius: 5,
      fontFamily: 'NunitoSans-Regular',
      fontSize: 16,
    },
    newCategoryModalInputFormItemInputDisabled: {
      backgroundColor: Colors.fieldtBgColor2,
    },

    // Saving Modal Styles
    savingModalContainer: {
      width: windowWidth * 0.3,
      height: windowHeight * 0.4,
      backgroundColor: Colors.whiteColor,
      borderRadius: 15,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    savingModalText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 18,
      color: Colors.primaryColor,
      marginBottom: 20,
    },

    // SideBar Styles
    sidebar: {
      width: windowWidth * Styles.sideBarWidth,
    },

    // Main Content Styles
    content: {
      width: windowWidth * (1 - Styles.sideBarWidth),
      marginVertical: windowHeight * 0.02,
      paddingHorizontal: windowWidth * 0.02,
    },

    // Header
    headerContainer: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    backButtonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    backButtonText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 18,
      color: Colors.primaryColor,
    },
    headerButtonContainer: {
      backgroundColor: Colors.primaryColor,
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 5,
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerButtonText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.whiteColor,
      marginLeft: 5,
    },
    unsavedChangesText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.tabRed,
      opacity: 0.7,
      marginRight: 10,
    },

    // Contents
    mainContentContainer: {
      flex: 1,
      flexDirection: 'column',
      alignItems: 'center',
      marginVertical: windowHeight * 0.01,
      backgroundColor: Colors.whiteColor,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.22,
      shadowRadius: 3.22,
      elevation: 3,
    },

    // QueueSizeOptions
    queueSizeOptionContainer: {
      height: windowHeight * 0.05,
      minWidth: '100%',
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    queueOptionsHeaderContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: Colors.primaryColor,
    },
    queueOptionsHeaderItemContainer: {
      height: windowHeight * 0.08,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    queueOptionHeaderItemText: {
      fontFamily: 'NunitoSans-Bold',
      fontSize: 16,
      color: Colors.mainTxtColor,
    },
    queueOptionItemText: {
      fontFamily: 'NunitoSans-SemiBold',
      fontSize: 15,
      color: Colors.mainTxtColor,
    },
    queueOptionUpdatedItemText: {
      marginLeft: 2,
      fontFamily: 'NunitoSans-Bold',
      fontSize: 15,
      color: Colors.tabRed,
      opacity: 0.5,
    },
    noQueueSizeOptionsText: {
      marginTop: windowHeight * 0.15,
      fontFamily: 'NunitoSans-Bold',
      fontSize: 18,
      color: Colors.descriptionColor,
    },
  });

  const renderNoQueueConfigModal = () => {
    return (
      <View style={styles.modalContainer}>
        <View style={styles.noQueueConfigModalContainer}>
          <View style={styles.noQueueConfigModalTitleContainer}>
            <Text style={styles.noQueueConfigModalTitle}>No Queue Configuration Found</Text>
            <Text style={styles.noQueueConfigModalDescription}>
              You haven&apos;t set up any queue configurations yet. Queue configurations help you manage different group sizes and categories for your waiting list.
            </Text>
          </View>

          <View style={styles.noQueueConfigModalButtonContainer}>
            <TouchableOpacity
              style={[styles.noQueueConfigModalButton]}
              onPress={() => { handleNoConfigUseTemplate() }}
            >
              <Text style={styles.noQueueConfigModalButtonText}>Use Default Template</Text>
              <Text style={styles.noQueueConfigModalButtonDescription}>Quick setup with common configurations</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.noQueueConfigModalButton]}
              onPress={() => { handleNoConfigUseCustom() }}
            >
              <Text style={styles.noQueueConfigModalButtonText}>Create Custom Configuration</Text>
              <Text style={styles.noQueueConfigModalButtonDescription}>Set up your own categories and group sizes</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const renderNewCategoryModal = () => {
    return (
      <View style={styles.modalContainer}>
        <View style={styles.newCategoryModalContainer}>
          <View style={styles.newCategoryModalCloseButtonContainer}>
            <TouchableOpacity onPress={() => { handleCloseNewCategoryModal() }}>
              <Feather name='x' size={35} color={Colors.blackColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.newCategoryModalInputFormContainer}>
            {/* Category Name */}
            <View style={styles.newCategoryModalInputFormItemContainer}>
              <View style={styles.newCategoryModalInputFormItemTitleContainer}>
                <Text style={styles.newCategoryModalInputFormItemText}>Category Name</Text>
              </View>
              <View style={styles.newCategoryModalInputFormItemInputContainer}>
                <TextInput
                  style={styles.newCategoryModalInputFormItemInput}
                  placeholder="Category Name"
                  value={categoryName}
                  onChangeText={(text) => { setCategoryName(text) }}
                />
              </View>
            </View>

            {/* Min Pax */}
            <View style={styles.newCategoryModalInputFormItemContainer}>
              <View style={styles.newCategoryModalInputFormItemTitleContainer}>
                <Text style={styles.newCategoryModalInputFormItemText}>Min Pax</Text>
                <Switch
                  value={isMinPaxNeeded}
                  onSyncPress={(value) => { setIsMinPaxNeeded(value) }}
                  circleColorActive={Colors.whiteColor}
                  circleColorInactive={Colors.fieldtTxtColor}
                  backgroundActive={Colors.primaryColor}
                />
              </View>
              <View style={styles.newCategoryModalInputFormItemInputContainer}>
                <TextInput
                  style={[styles.newCategoryModalInputFormItemInput, !isMinPaxNeeded && styles.newCategoryModalInputFormItemInputDisabled]}
                  placeholder="Min Pax"
                  keyboardType="numeric"
                  value={categoryMinPax}
                  editable={isMinPaxNeeded}
                  onChangeText={(text) => { setCategoryMinPax(text) }}
                />
              </View>
            </View>

            {/* Max Pax */}
            <View style={styles.newCategoryModalInputFormItemContainer}>
              <View style={styles.newCategoryModalInputFormItemTitleContainer}>
                <Text style={styles.newCategoryModalInputFormItemText}>Max Pax</Text>

                <Switch
                  value={isMaxPaxNeeded}
                  onSyncPress={(value) => { setIsMaxPaxNeeded(value) }}
                  circleColorActive={Colors.whiteColor}
                  circleColorInactive={Colors.fieldtTxtColor}
                  backgroundActive={Colors.primaryColor}
                />
              </View>
              <View style={styles.newCategoryModalInputFormItemInputContainer}>
                <TextInput
                  style={[styles.newCategoryModalInputFormItemInput, !isMaxPaxNeeded && styles.newCategoryModalInputFormItemInputDisabled]}
                  placeholder="Max Pax"
                  keyboardType="numeric"
                  value={categoryMaxPax}
                  editable={isMaxPaxNeeded}
                  onChangeText={(text) => { setCategoryMaxPax(text) }}
                />
              </View>
            </View>
          </View>

          <View style={styles.newCategoryModalButtonContainer}>
            <TouchableOpacity styles={{ width: '50%' }} onPress={() => { setNewCategoryModalVisible(false) }}>
              <Text style={styles.newCategoryModalDiscardButtonText}>Discard</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.newCategoryModalAddButtonContainer, { width: '50%' }]} onPress={() => { handleAddUpdateQueueOption() }}>
              <Text style={styles.newCategoryModalAddButtonText}>{editingQueueOptionIndex !== null ? 'Update Category' : 'Add Category'}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const renderLoadingModal = () => {
    return (
      <View style={styles.modalContainer}>
        <View style={styles.savingModalContainer}>
          <Text style={styles.savingModalText}>Saving Queue Configurations...</Text>
          <ActivityIndicator size="large" color={Colors.primaryColor} />
        </View>
      </View>
    );
  };

  const renderQueueOptionsHeader = () => {
    return (
      <>
        {/* Placeholder for DragnDrop */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '5%' }]} />

        {/* Index */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '10%' }]}>
          <Text style={styles.queueOptionHeaderItemText}>Index</Text>
        </View>

        {/* Option Name */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '30%' }]}>
          <Text style={styles.queueOptionHeaderItemText}>Option Name</Text>
        </View>

        {/* Enabled? */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '10%' }]}>
          <Text style={styles.queueOptionHeaderItemText}>Enabled?</Text>
        </View>

        {/* Min Pax */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '15%' }]}>
          <Text style={styles.queueOptionHeaderItemText}>Min Pax</Text>
        </View>

        {/* Max Pax */}
        <View style={[styles.queueOptionsHeaderItemContainer, { width: '15%' }]}>
          <Text style={styles.queueOptionHeaderItemText}>Max Pax</Text>
        </View>
      </>
    );
  };

  const renderQueueSizeOption = (params) => {
    const { item, drag, isActive } = params

    const index = params.getIndex()

    return (
      <ShadowDecorator>
        <View style={styles.queueSizeOptionContainer}>
          {/* Placeholder for DragnDrop */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '5%' }]}>
            <TouchableOpacity onLongPress={drag} delayLongPress={1000}>
              <Feather name="menu" size={25} color={Colors.primaryColor} />
            </TouchableOpacity>
          </View>

          {/* Index */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '10%' }]}>
            <Text style={styles.queueOptionItemText}>{index + 1}</Text>
          </View>

          {/* Option Name */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '30%' }]}>
            <Text style={styles.queueOptionItemText}>{item.name}</Text>
          </View>

          {/* Enabled? */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '10%' }]}>
            <Switch
              value={item.isEnabled}
              onSyncPress={(value) => { handleUpdateQueueOptionAvailability(index, value) }}
              circleColorActive={Colors.whiteColor}
              circleColorInactive={Colors.fieldtTxtColor}
              backgroundActive={Colors.primaryColor}
            />
          </View>

          {/* Min Pax */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '15%' }]}>
            <Text style={styles.queueOptionItemText}>{item.minPax ? item.minPax : 'Not Set'}</Text>
          </View>

          {/* Max Pax */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '15%' }]}>
            <Text style={styles.queueOptionItemText}>{item.maxPax ? item.maxPax : 'Not Set'}</Text>
          </View>

          {/* Operations */}
          <View style={[styles.queueOptionsHeaderItemContainer, { width: '15%' }]}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', width: '50%' }}>
              <TouchableOpacity onPress={() => { handleEditQueueOption(index) }}>
                <Feather name="edit" size={25} color={Colors.primaryColor} />
              </TouchableOpacity>

              <TouchableOpacity onPress={() => { removeQueueOption(index) }}>
                <Feather name="trash-2" size={25} color={Colors.tabRed} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ShadowDecorator>
    );
  };

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View style={[
        styles.container,
        !isTablet() && styles.transformView,
        getTransformForScreenInsideNavigation()
      ]}>

        {/* No Queue Config Modal */}
        {
          noConfigModalVisible && (
            <ModalView visible={noConfigModalVisible} animationType="slide" transparent>
              {renderNoQueueConfigModal()}
            </ModalView>
          )
        }

        {/* New Category Modal */}
        {
          newCategoryModalVisible && (
            <ModalView visible={newCategoryModalVisible} animationType="slide" transparent>
              {renderNewCategoryModal()}
            </ModalView>
          )
        }

        {/* Loading Modal */}
        {
          loading && (
            <ModalView visible={loading} animationType="slide" transparent>
              {renderLoadingModal()}
            </ModalView>
          )
        }

        {/* SideBar */}
        {/* <View style={styles.sidebar}>
          <SideBar navigation={navigation} selectedTab={1} expandOperation />
        </View> */}

        {/* Main Content */}
        <View style={styles.content}>
          {/* Back Button, Save Button */}
          <View style={styles.headerContainer}>
            {/* Back Button */}
            <TouchableOpacity style={styles.backButtonContainer} onPress={() => { handleBackButtonOnPressed() }}>
              <Feather name="chevron-left" size={30} color={Colors.primaryColor} />
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>

            {/* Save Button */}
            <View style={[styles.flexRowContainer, { alignItems: 'flex-end' }]}>
              {(!isSaved) && (
                <Text style={styles.unsavedChangesText}>*Unsaved Changes</Text>
              )}

              <TouchableOpacity style={[styles.headerButtonContainer, { marginRight: 10 }]} onPress={() => { setNewCategoryModalVisible(true) }}>
                <Feather name="plus-square" size={25} color={Colors.whiteColor} />
                <Text style={styles.headerButtonText}>NEW CATEGORY</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.headerButtonContainer} disabled={loading} onPress={() => { handleSaveQueueConfig() }}>
                <Feather name="save" size={25} color={Colors.whiteColor} />
                <Text style={styles.headerButtonText}>SAVE CONFIGS</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Queue Options Flatlist Container */}
          <View style={styles.mainContentContainer}>

            <View style={styles.queueOptionsHeaderContainer}>
              {renderQueueOptionsHeader()}
            </View>

            {queueSizeOptions.length === 0 ? (
              <Text style={styles.noQueueSizeOptionsText}>
                No Queue Size Options
              </Text>
            ) : (
              <DraggableFlatList
                data={queueSizeOptions}
                renderItem={renderQueueSizeOption}
                onDragEnd={({ data }) => { setQueueSizeOptions(data) }}
                keyExtractor={(item, index) => index.toString()}
              />
            )}
          </View>
        </View>
      </View>
    </UserIdleWrapper>
  );
};

export default QueueConfigScreen;
