import React, { Component } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import PhoneNavigator from '../navigation/PhoneNavigator';
import LoginScreen from './LoginScreen';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Token from '../util/Token';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import NetInfo from "@react-native-community/netinfo";

var hide = true;
class MainScreen extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {
            showApp: false,
            isLoggedIn: null,
        }
    }

    componentDidMount() {
        this.checkLogin();
        this.checkPinLogin();
        User.setRefreshMainScreen(this.checkPinLogin.bind(this));
        User.setRefreshMainScreen(this.checkLogin.bind(this));

    }

    checkPinLogin(param) {
        this.setState({ isLoggedIn: param })
    }
    // function here
    checkLogin() {
        AsyncStorage.multiGet(['accessToken', 'userData', 'refreshToken']).then(async (items, err) => {
            const switchMerchantRaw = await AsyncStorage.getItem('switchMerchant');

            if (switchMerchantRaw === '1') {
            }
            else {
                if (items) {
                    if (items[0][1]) {
                        NetInfo.fetch().then(async state => {
                            console.log("Connection type", state.type);
                            console.log("Is connected?", state.isInternetReachable);
                            console.log(state);

                            if (state.isInternetReachable) {
                                Token.setToken(items[0][1]);
                                Token.setRefreshToken(items[2][1]);
                                User.initFromJSON(JSON.parse(items[1][1]));
                                this.setState({ showApp: true })

                                const firebaseUid = await AsyncStorage.getItem('firebaseUid');
                                const merchantId = await AsyncStorage.getItem('merchantId');

                                const role = await AsyncStorage.getItem('role');

                                const isAlphaUser = await AsyncStorage.getItem('isAlphaUser');

                                UserStore.update(s => {
                                    s.firebaseUid = firebaseUid;
                                    s.merchantId = merchantId;

                                    s.role = role;

                                    s.isAlphaUser = isAlphaUser === '1' ? true : false;
                                });

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });
                            }
                            else {
                                Alert.alert('Error', 'Unable to connect to the Internet');

                                this.setState({ showApp: false });

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });
                            }
                        });
                    } else {
                        this.setState({ showApp: false });

                        CommonStore.update(s => {
                            s.isAuthenticating = false;
                        });
                    }
                }
                else {
                    this.setState({ showApp: false });

                    CommonStore.update(s => {
                        s.isAuthenticating = false;
                    });
                }
            }
        }).catch(err => {
            console.log(err);

            this.setState({ showApp: false });

            CommonStore.update(s => {
                s.isAuthenticating = false;
            });
        });
    }
    // function end


    render() {
        console.log('showApp');
        console.log(this.state.showApp);

        const showAppTest = this.state.showApp;

        return (
            !this.state.showApp ? <LoginScreen checkLogin={this.checkLogin.bind(this)} /> :
                // : !this.state.isLoggedIn? <PinLogin checkPinLogin={this.checkPinLogin.bind(this)} /> :
                <PhoneNavigator />
        )
    }
}

const styles = StyleSheet.create({});
export default MainScreen;
