import { Text } from "react-native-fast-text";
import React, { Component, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal as ModalComponent,
  Platform,
  useWindowDimensions,
  InteractionManager,
  TextInput,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import Back from 'react-native-vector-icons/EvilIcons';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import Close from 'react-native-vector-icons/AntDesign';
import Draggable from 'react-native-draggable';
import {
  compareTwoValues,
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import { CHARGES_TYPE, ORDER_TYPE, EXPAND_TAB_TYPE, PRODUCT_PRICE_TYPE, UNIT_TYPE_SHORT, ORDER_TYPE_SUB } from '../constant/common';
import { naturalCompare } from '../util/common';
import moment from 'moment';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { FlashList } from '@shopify/flash-list';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const OutletMenuScreen = React.memo((props) => {
  const { navigation, route } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  // const outletData = route.params.outletData;
  const navFromParam = route.params.params.navFrom;

  // const [outletData, setOutletData] = useState(outletData);
  const [outletMenu, setOutletMenu] = useState([]);
  const [category, setCategory] = useState('');
  const [menu, setMenu] = useState([]);
  const [reverse, setReverse] = useState(false);
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [test, setTest] = useState(0);
  const [currentMenu, setCurrentMenu] = useState([]);
  const [productList2, setProductList2] = useState([]);
  const [productList, setProductList] = useState([]);
  const [choice, setChoice] = useState(null);
  const [categoryIndex, setCategoryIndex] = useState(0);
  const [navFrom, setNavFrom] = useState(navFromParam);
  const [isInfoTabHitTop, setIsInfoTabHitTop] = useState(false);
  const [onStartVisible, setOnStartVisible] = useState(false);
  const [cartIcon, setCartIcon] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [cartWarning, setCartWarning] = useState(false);
  const [cartProceed, setCartProceed] = useState([]);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const [outletItemsDisplay, setOutletItemsDisplay] = useState([]);

  const [effectiveDays, setEffectiveDays] = useState(moment().day());

  //////////////////////////////////////////////

  // 2022-06-22 Sortable outlet categories

  const [filteredOutletCategories, setFilteredOutletCategories] = useState([]);

  //////////////////////////////////////////////

  const outletData = MerchantStore.useState((s) => s.currOutlet);

  // const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);
  // const selectedOutletItemCategories = CommonStore.useState(s => s.selectedOutletItemCategories);
  const selectedOutletItemCategory = CommonStore.useState(
    (s) => s.selectedOutletItemCategory,
  );

  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const orderType = CommonStore.useState((s) => s.orderType);
  const orderTypeSub = CommonStore.useState((s) => s.orderTypeSub);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict,
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict,
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict,
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict,
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict,
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict,
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict,
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict,
  );

  const selectedOutletItemCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // 11/10/2022 For Available On (Time) - Greg
  const timeCheckItem = CommonStore.useState(s => s.timeCheckItem);

  // 5/1/2023 For Search Bar - Greg
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [isOnSearch, setIsOnSearch] = useState(false);
  const [openSearchBar, setOpenSearchBar] = useState(false);

  // 2024-04-01 - commented, can be triggered by dashboard
  // useEffect(() => {
  //   setInterval(() => {
  //     CommonStore.update(s => {
  //       s.timeCheckItem = Date.now();
  //     });
  //   }, 60000);
  // }, []);

  useEffect(() => {
    console.log('useEffect - OutletMenu - 1');

    InteractionManager.runAfterInteractions(() => {
      var filteredOutletCategoriesTemp = outletCategories
        .filter((category) => {
          // return category.hideInOrderTypes &&
          //   category.hideInOrderTypes.length > 0
          //   ? category.hideInOrderTypes.includes(orderType)
          //     ? false
          //     : true
          //   : true;

          if ((category.isActive || category.isActive === undefined) &&
            (category.isAvailableDayActive
              ? (category.effectiveTypeOptions.includes(effectiveDays) &&
                category.effectiveStartTime && category.effectiveEndTime &&
                moment().isSameOrAfter(
                  moment(category.effectiveStartTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                )
                &&
                moment().isBefore
                  (moment(category.effectiveEndTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                  )
              )
              : true)) {
            return true;
          }
        });

      filteredOutletCategoriesTemp = filteredOutletCategoriesTemp.sort((a, b) => {
        return (
          (a.orderIndex
            ? a.orderIndex
            : filteredOutletCategoriesTemp.length) -
          (b.orderIndex
            ? b.orderIndex
            : filteredOutletCategoriesTemp.length)
        );
      });

      const isEqual = compareTwoValues(filteredOutletCategories, filteredOutletCategoriesTemp);
      if (!isEqual) {
        setFilteredOutletCategories(filteredOutletCategoriesTemp);
      }
    });
  }, [outletCategories, timeCheckItem]);

  const timestampOutletCategory = CommonStore.useState(s => s.timestampOutletCategory);

  useEffect(() => {
    console.log('useEffect - OutletMenu - 2');

    if (filteredOutletCategories.length > 0 && selectedOutletItemCategory &&
      !filteredOutletCategories.find(category => category.uniqueId === selectedOutletItemCategory.uniqueId)) {
      InteractionManager.runAfterInteractions(() => {
        const isEqual = compareTwoValues(selectedOutletItemCategory, filteredOutletCategories[0]);
        if (!isEqual) {
          CommonStore.update(s => {
            s.selectedOutletItemCategory = filteredOutletCategories[0];
          });
        }
      });
    }
  }, [filteredOutletCategories]);

  useEffect(() => {
    console.log('useEffect - OutletMenu - 3');

    if (filteredOutletCategories.length > 0) {
      InteractionManager.runAfterInteractions(() => {
        CommonStore.update(s => {
          s.selectedOutletItemCategory = filteredOutletCategories[0];
        });
      });
    }
  }, [timestampOutletCategory]);

  useEffect(() => {
    console.log('useEffect - OutletMenu - 4');

    if (cartItems.length > 0) {
      setCartIcon(true);
    }
  }, [cartItems.length]);

  useEffect(() => {
    console.log('useEffect - OutletMenu - 5');

    // InteractionManager.runAfterInteractions(() => {
    if (!openSearchBar) {
      let outletItemsDisplayTemp = outletItems.filter((item) => {
        var result = selectedOutletItemCategory &&
          item.categoryId === selectedOutletItemCategory.uniqueId;

        var resultSearch = false;
        if (search !== '') {
          const searchLowerCase = search.toString().toLowerCase();
          if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
            resultSearch = true;
          } else {
            resultSearch = false;
          }
        } else {
          resultSearch = true;
        }

        return (
          result && resultSearch
        );
      });

      setOutletItemsDisplay(
        outletItemsDisplayTemp.sort((a, b) => {
          return (a.orderIndex ? a.orderIndex : outletItemsDisplayTemp.length) -
            (b.orderIndex ? b.orderIndex : outletItemsDisplayTemp.length)
        })
      );
    }
    else {
      // setOutletItemsDisplay(outletItems);

      let outletItemsDisplayTemp = outletItems.filter((item) => {
        var resultSearch = false;
        if (search !== '') {
          const searchLowerCase = search.toString().toLowerCase();
          if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
            resultSearch = true;
          } else {
            resultSearch = false;
          }
        } else {
          resultSearch = true;
        }

        return (
          resultSearch
        );
      })

      setOutletItemsDisplay(
        outletItemsDisplayTemp.sort((a, b) => {
          return (a.orderIndex ? a.orderIndex : outletItemsDisplayTemp.length) -
            (b.orderIndex ? b.orderIndex : outletItemsDisplayTemp.length)
        })
      );
    }

    // });
  }, [
    outletItems, selectedOutletItemCategory, orderType,
    // timeCheckItem,
    search, openSearchBar,
  ]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          } else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? '27%' : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? '1%' : 0,
          }}>
          Outlet Menu
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount() {
  //   this.category();
  //   this.refresh();
  //   this.refreshMenu()
  //   ApiClient.GET(API.outlet2 + this.state.outletData.id).then((result) => {
  //     // // console.log(result)
  //     this.setState({ outletData: result })
  //   });
  //   // menu item
  //   // ApiClient.GET(API.merchantMenu + this.state.outletData.id).then((result) => {
  //   //   // // console.log(result)
  //   //   if (result.length > 0) {
  //   //     this.setState({ category: result[0].category, menu: result[0].items })
  //   //   }
  //   //   this.setState({ outletMenu: result })
  //   // });
  //   // this.cartCount();
  //   // this.getCartItem();
  //   setInterval(() => {
  //     this.cartCount();
  //     this.getCartItem();
  //   }, 7000);

  // }

  // function here
  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() });
    // // console.log(cartItem)
  };

  const cartCount = () => {
    if (Cart.getCartItem() !== null) {
      if (Cart.getCartItem().length > 0) {
        setState({ cartIcon: true });
      } else {
        setState({ cartIcon: false });
      }
    } else {
      setState({ cartIcon: false });
    }
  };

  const goToCart = () => {
    if (Cart.getCartItem().length > 0) {
      if (navFrom == 'TAKEAWAY') {
        // 2024-07-02 - to initialize cache data
        CommonStore.update(s => {
          s.cartOutletItemsDict = {};
          s.cartOutletItemAddOnDict = {};
          s.cartOutletItemAddOnChoiceDict = {};
        });

        props.navigation.navigate({
          name: 'Cart',
          params: {
            test, outletData, navFrom
          },
          merge: true,
        });
      } else {
        props.navigation.navigate({
          name: 'Cart',
          params: {
            test, outletData
          },
          merge: true,
        });
      }
    } else {
      Alert.alert(
        'Info',
        'No items in your cart at the moment',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
    }
  };

  const onCartClicked = () => {
    if (cartItems.length > 0) {
      // if (navFrom == ORDER_TYPE.TAKEAWAY) {
      if (orderType !== ORDER_TYPE.DINEIN) {
        // 2024-07-02 - to initialize cache data
        CommonStore.update(s => {
          s.cartOutletItemsDict = {};
          s.cartOutletItemAddOnDict = {};
          s.cartOutletItemAddOnChoiceDict = {};
        });

        props.navigation.navigate({
          name: 'Cart',
          params: {
            test, outletData, navFrom
          },
          merge: true,
        });
      } else {
        props.navigation.navigate({
          name: 'Cart',
          params: {
            test, outletData,
          },
          merge: true,
        });
      }
    } else {
      Alert.alert(
        'Info',
        'No items in your cart at the moment',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
    }
  };

  const categoryFunc = () => {
    ApiClient.GET(API.activeMenu + outletData.id)
      .then((result) => {
        const tmpCategories = {};
        for (const category of result) {
          const categoryName = category.name;
          const categoryId = category.id;
          if (!tmpCategories[categoryName]) {
            tmpCategories[categoryName] = {
              label: categoryName,
              value: categoryId,
            };
          }
        }
        const categories = Object.values(tmpCategories);
        setState({ categoryOutlet: categories, category: categories[0].label });
      })
      .catch((err) => {
        // console.log('Error');
        // console.log(err);
      });
  };

  const refresh = () => {
    ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
      if (result != undefined && result.length > 0) {
        var productListRaw = [];

        result.forEach((element) => {
          // console.log(element.items);
          productListRaw = productListRaw.concat(element.category);
          const activeItem = productListRaw.filter((item) => item.active == 1);
          setState(
            { productList: productListRaw, productList2: activeItem },
            () => { },
          );
        });
      }
    });
  };

  const refreshMenu = () => {
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      const category = result.filter((i) => i.name == category);
      category.map((i) => {
        // const newList = []
        // for (const item of i.items){
        //   if(item.name !== ""){
        //     newList.push(item)
        //   }
        // }
        // setState({ currentMenu: newList })
        setState({ currentMenu: i.items });
      });
      // }

      // }else{
      //   setState({ currentMenu: result });
      // }
    });
  };

  // const refresh = () => {
  //   setState({ refresh: true });
  // }

  const renderMenu = ({ item }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    // const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
    // if (itemsInCart) {
    //   for (const obj of itemsInCart) {
    //     quantity += parseInt(obj.quantity);
    //   }
    // }

    quantity = cartItems.reduce((accum, cartItem) => {
      accum + (
        cartItem.itemId === item.uniqueId
          ?
          parseInt(cartItem.quantity)
          :
          0
      )
    }, 0);

    var overrideCategoryPrice = undefined;

    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    var amountOffCategory = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      amountOffCategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      amountOffCategory =
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    var percentageOffCategory = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      percentageOffCategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      percentageOffCategory =
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    // var pointsRedeemCategory = undefined;
    // if (selectedOutletItemCategoriesDict[item.categoryId] && pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name] !== undefined) {
    //   pointsRedeemCategory = pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name];
    // }

    var buy1Free1Category = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      buy1Free1CategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      buy1Free1Category =
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    var extraPrice = 0;
    if (
      orderType === ORDER_TYPE.DELIVERY &&
      outletData &&
      outletData.deliveryPrice
    ) {
      extraPrice = outletData.deliveryPrice;
    } else if (
      orderType === ORDER_TYPE.PICKUP &&
      orderTypeSub === ORDER_TYPE_SUB.NORMAL &&
      outletData &&
      outletData.pickUpPrice
    ) {
      extraPrice = outletData.pickUpPrice;
    }

    if (orderType === ORDER_TYPE.DELIVERY) {
      extraPrice = item.deliveryCharges || 0;

      if (
        extraPrice &&
        item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }

      if (!item.deliveryChargesActive) {
        extraPrice = 0;
      }
    }
    // else {
    //   extraPrice = 0;
    // }

    if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
      extraPrice = item.pickUpCharges || 0;

      if (
        extraPrice &&
        item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }

      if (!item.pickUpChargesActive) {
        extraPrice = 0;
      }
    }

    if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
      extraPrice = item.otherDCharges || 0;

      if (
        extraPrice &&
        item.otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }

      if (!item.otherDChargesActive) {
        extraPrice = 0;
      }
    }

    // else {
    //   extraPrice = 0;
    // }

    return (
      <TouchableOpacity
        onPress={() => {
          if (
            item.isActive &&
            (item.isAvailableDayActive
              ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                item.effectiveStartTime && item.effectiveEndTime &&
                moment().isSameOrAfter(
                  moment(item.effectiveStartTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                )
                &&
                moment().isBefore
                  (moment(item.effectiveEndTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                  )
              )
              : true)
          ) {
            CommonStore.update((s) => {
              s.selectedOutletItem = item;

              s.selectedOutletItemAddOn = {};
              s.selectedOutletItemAddOnChoice = {};
            });

            props.navigation.navigate({
              name: 'MenuItemDetails',
              params: {
                refresh: refresh.bind(this),
                menuItem: item,
                outletData,
                orderType,
                orderTypeSub,
              },
              merge: true,
            });
          } else {
            Alert.alert(
              'Info',
              'Sorry, this product is currently not available',
            );
          }

          // if (checkCartOutlet()) {
          //   setState({ cartWarning: true });
          // } else {
          //   if (
          //     item.isActive &&
          //     (item.isAvailableDayActive
          //       ? (item.effectiveTypeOptions.includes(effectiveDays) &&
          //         item.effectiveStartTime && item.effectiveEndTime &&
          //         moment().isSameOrAfter(
          //           moment(item.effectiveStartTime)
          //             .year(moment().year())
          //             .month(moment().month())
          //             .date(moment().date())
          //         )
          //         &&
          //         moment().isBefore
          //           (moment(item.effectiveEndTime)
          //             .year(moment().year())
          //             .month(moment().month())
          //             .date(moment().date())
          //           )
          //       )
          //       : true)
          //   ) {
          //     CommonStore.update((s) => {
          //       s.selectedOutletItem = item;
          //     });

          //     props.navigation.navigate('MenuItemDetails', {
          //       refresh: refresh.bind(this),
          //       menuItem: item,
          //       outletData: outletData,
          //       orderType: orderType,
          //     });
          //   } else {
          //     Alert.alert(
          //       'Info',
          //       'Sorry, this product is currently not available',
          //     );
          //   }
          // }
        }}>
        <View
          style={{
            flexDirection: 'row',
            paddingHorizontal: 20,
            paddingBottom: 15,
            paddingTop: 10,
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: 'row',
            // borderWidth: 1
            paddingRight: windowWidth * 0.08,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              width: '75%',
              display: 'flex',
              justifyContent: 'flex-start',
              // backgroundColor: 'blue',
              // ...(buy1Free1ItemSkuDict[item.sku] !== undefined ||
              //   buy1Free1Category !== undefined) && {
              //     marginTop: 5,
              //     marginBottom: 20,
              // },
            }}>
            <View
              style={[
                {
                  backgroundColor: Colors.secondaryColor,
                  // width: 60,
                  // height: 60,
                  width: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  height: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  borderRadius: 10,
                },
                item.image
                  ? {}
                  : {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
              ]}>
              {item.image ? (
                <>
                  {switchMerchant ? (
                    <AsyncImage
                      source={{ uri: item.image }}
                      item={item}
                      style={{
                        // width: 60,
                        // height: 60,
                        width: windowWidth * 0.08,
                        height: windowWidth * 0.08,
                        borderRadius: 10,
                      }}
                    />
                  ) : (
                    <AsyncImage
                      source={{ uri: item.image }}
                      item={item}
                      style={{
                        // width: 60,
                        // height: 60,
                        width: windowWidth * 0.11,
                        height: windowWidth * 0.11,
                        borderRadius: 10,
                      }}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Ionicons name="fast-food-outline" size={20} />
                  ) : (
                    <Ionicons name="fast-food-outline" size={50} />
                  )}
                </>
              )}

              {!item.isActive ||
                !(item.isAvailableDayActive
                  ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                    item.effectiveStartTime && item.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(item.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(item.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      )
                  )
                  : true) ? (
                <View
                  style={{
                    position: 'absolute',
                    zIndex: 3,
                  }}>
                  <View
                    style={[{
                      // width: 120,
                      width: windowWidth * 0.125,
                      left: -windowWidth * 0.015,
                      padding: 0,
                      paddingLeft: windowWidth * 0.01,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: Colors.tabRed,
                      height: 25,
                      borderTopRightRadius: 10,
                      borderBottomRightRadius: 3,

                      ...(!item.image && {
                        left: -windowWidth * 0.007,
                        bottom: '112%',
                      }),
                    }, switchMerchant ? {
                      width: windowWidth * 0.095,
                      top: windowWidth * -0.02,
                      height: 20,
                    } : {}]}>
                    <Text
                      style={{
                        color: '#FFF',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 8 : 12,
                        bottom: 1,
                      }}>
                      Not available
                    </Text>
                  </View>
                  <View
                    style={[{
                      left: -windowWidth * 0.015,
                      bottom: '1%',
                      width: 0,
                      height: 0,
                      backgroundColor: 'transparent',
                      borderStyle: 'solid',
                      borderRightWidth: windowWidth * 0.015,
                      borderTopWidth: windowWidth * 0.015,
                      borderRightColor: 'transparent',
                      borderTopColor: 'red',
                      transform: [{ rotate: '90deg' }],

                      ...(!item.image && {
                        left: -windowWidth * 0.007,
                        bottom: '113%',
                      }),
                    }, switchMerchant ? {
                      // width: windowWidth * 0.095,
                      top: windowWidth * -0.02,
                    } : {}]}
                  />
                </View>
              ) : (
                <></>
              )}

              {
                (buy1Free1ItemSkuDict[item.sku] !== undefined ||
                  buy1Free1Category !== undefined)
                  ?
                  <View style={{
                    //marginTop: 2,
                    // paddingBottom: 150,
                    // height: 30,
                  }}>
                    <Text style={{
                      color: Colors.descriptionColor,
                      fontFamily: "NunitoSans-SemiBold",
                      fontSize: switchMerchant ? 10 : 16,
                      textAlign: 'center',

                      top: item.image ? 0 : (switchMerchant ? ((windowWidth * 0.08) * 0.4) : ((windowWidth * 0.11) * 0.4)),
                    }}>
                      {`Bundle Deal`}
                      {/* {
                          buy1Free1ItemSkuDict[item.sku] !== undefined
                            ?
                            `Buy ${buy1Free1ItemSkuDict[item.sku].buyAmount} Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                            :
                            `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`
                        } */}
                    </Text>
                  </View>
                  :
                  <></>
              }
            </View>
            <View
              style={{
                marginLeft: 15,
                // flexDirection: 'row',
                // flexShrink: 1,
                width: '55%',
                // backgroundColor: 'red',
              }}>
              <Text
                // numberOfLines={1}
                style={{
                  fontSize: switchMerchant ? 10 : 16,
                  textTransform: 'uppercase',
                  fontFamily: 'NunitoSans-Bold',
                  // flexWrap: 'wrap',
                  // flex: 1,
                  // flexShrink: 1,
                  // width: '100%',
                }}>
                {item.name}
              </Text>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  // marginBottom: 10 ,
                }}>
                <Text
                  style={{
                    color:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? Colors.secondaryColor
                        : Colors.primaryColor,

                    fontFamily: 'NunitoSans-Bold',
                    paddingTop: 5,
                    // fontSize: 14,
                    fontSize: switchMerchant ? 10 : 16,
                    textDecorationLine:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? 'line-through'
                        : 'none',
                  }}>
                  RM{' '}
                  {parseFloat(extraPrice + item.price)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                </Text>

                {overrideItemPriceSkuDict[item.sku] !== undefined ||
                  overrideCategoryPrice !== undefined ? (
                  overrideItemPriceSkuDict[item.sku] !== undefined ? (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}
                      {parseFloat(
                        overrideItemPriceSkuDict[item.sku].overridePrice,
                      ).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}{parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  )
                ) : (
                  <></>
                )}
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  //backgroundColor: 'blue',
                }}>
                {amountOffItemSkuDict[item.sku] !== undefined ||
                  amountOffCategory !== undefined ? (
                  <Text
                    style={{
                      color: Colors.descriptionColor,
                      fontFamily: 'NunitoSans-SemiBold',
                      paddingTop: windowWidth * 0.01,
                      fontSize: switchMerchant ? 10 : 16,
                      // marginLeft: 5,
                      width: '130%',
                    }}>
                    {amountOffItemSkuDict[item.sku] !== undefined
                      ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin} ~ ${amountOffItemSkuDict[item.sku].quantityMax
                      } pcs to enjoy RM${amountOffItemSkuDict[
                        item.sku
                      ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                      })`
                      : `Buy ${amountOffCategory.quantityMin} ~ ${amountOffCategory.quantityMax
                      } pcs to enjoy RM${amountOffCategory.amountOff.toFixed(
                        0,
                      )} off\n(Min purchases: RM${amountOffCategory.priceMin
                      })`}
                    {/* `Buy $11 ~ $11 pcs to enjoy $12% off\n(Min ~ max purchases: RM$11~$11)` */}
                  </Text>
                ) : (
                  <></>
                )}
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  //backgroundColor: 'blue',
                }}>
                {percentageOffItemSkuDict[item.sku] !== undefined ||
                  percentageOffCategory !== undefined ? (
                  <Text
                    style={{
                      color: Colors.descriptionColor,
                      fontFamily: 'NunitoSans-SemiBold',
                      paddingTop: windowWidth * 0.01,
                      fontSize: switchMerchant ? 10 : 16,
                      // marginLeft: 5,
                      width: '130%',
                    }}>
                    {percentageOffItemSkuDict[item.sku] !== undefined
                      ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                      } ~ ${percentageOffItemSkuDict[item.sku].quantityMax
                      } pcs to enjoy ${percentageOffItemSkuDict[
                        item.sku
                      ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                      })`
                      : `Buy ${percentageOffCategory.quantityMin} ~ ${percentageOffCategory.quantityMax
                      } pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                        0,
                      )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                      })`}
                    {/* `Buy $11 ~ $11 pcs to enjoy $12% off\n(Min ~ max purchases: RM$11~$11)` */}
                  </Text>
                ) : (
                  <></>
                )}
              </View>

              {/* <Text
                  style={{
                    color: Colors.primaryColor,
                    fontFamily: 'NunitoSans-Bold',
                    paddingTop: 5,
                    fontSize: switchMerchant ? 10 : 16,
                  }}>
                  RM
                  {parseFloat(item.price)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text> */}
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              // width: "20%",
              // marginLeft: 60
            }}>
            <View
              style={{
                backgroundColor: '#e3e1e1',
                // width: 67,
                // height: 24,

                width: 68,
                height: 26,

                // paddingVertical: 4,
                // paddingHorizontal: 20,

                borderRadius: 10,
                justifyContent: 'center',
                alignSelf: 'center',
              }}>
              <TouchableOpacity
                onPress={() => {
                  if (checkCartOutlet()) {
                    setState({ cartWarning: true, cartProceed: item });
                  } else if (item.isActive) {
                    CommonStore.update((s) => {
                      s.selectedOutletItem = item;

                      s.selectedOutletItemAddOn = {};
                      s.selectedOutletItemAddOnChoice = {};
                    });

                    props.navigation.navigate({
                      name: 'MenuItemDetails',
                      params: {
                        refresh: refresh.bind(this),
                        menuItem: item,
                        outletData,
                        orderType,
                        orderTypeSub,
                      },
                      merge: true,
                    });
                  } else {
                    Alert.alert(
                      'Info',
                      'Sorry, this product is currently not available',
                    );
                  }
                }}>
                <Text
                  style={{
                    alignSelf: 'center',
                    color: '#8f8f8f',
                    fontSize: switchMerchant ? 10 : 13,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  {quantity > 0 ? quantity : 'Add'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };



  // onContainerScroll = e => {
  //   // console.log(windowWidth);
  //   // console.log(e.nativeEvent.contentOffset.y);
  //   // console.log('---------------------------')

  //   if (e.nativeEvent.contentOffset.y * 2 >= windowWidth) {
  //     // console.log('hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: true,
  //     // });
  //   }
  //   else {
  //     // console.log('not hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: false,
  //     // });
  //   }
  // }

  const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const nextCategory = () => {
    const catLength = categoryOutlet.length;
    if (categoryIndex == catLength - 1) {
      setState({
        category: categoryOutlet[0],
        categoryIndex: 0,
        // menu: choice[index].category,
      });
    } else {
      setState({
        category: categoryOutlet[categoryIndex + 1],
        categoryIndex: categoryIndex + 1,
        // menu: choice[index].category,
      });
    }
    refreshMenu();
  };

  const checkCartOutlet = () => {
    const outletId = outletData.uniqueId;
    // console.log(Cart.getOutletId() != null);
    if (outletId != Cart.getOutletId() && Cart.getOutletId() != null) {
      return true;
    }
    return false;
  };
  // function end

  return (
    (<UserIdleWrapper disabled={!isMounted} screenName={'OutletMenuScreen'}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={cartWarning}
          transparent
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: windowHeight,
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  setState({ cartWarning: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-start',
                    padding: 14,
                  }}>
                  {/* <Close name="close" size={25} color={'#b0b0b0'} /> */}
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </View>
              </TouchableOpacity>
              <View style={{ marginBottom: 10 }}>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: '700',
                    fontSize: switchMerchant ? 10 : 18,
                  }}>
                  You are entering a different outlet
                </Text>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: '700',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Your existing cart items will be cleared if you proceed. Are you
                  sure?
                </Text>
              </View>
              <View
                style={{
                  alignSelf: 'center',
                  marginTop: 30,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 250,
                  height: 40,
                  alignContent: 'center',
                  marginTop: 40,
                }}>
                <TouchableOpacity
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    borderRadius: 10,
                    height: 60,
                    marginTop: 30,
                    alignSelf: 'center',
                  }}
                  onPress={() => {
                    setState({ cartWarning: false });
                    Cart.clearCart();

                    // CommonStore.update(s => {
                    //   s.selectedOutletItem = item;
                    // });

                    props.navigation.navigate({
                      name: 'MenuItemDetails',
                      params: {
                        refresh: refresh.bind(this),
                        menuItem: cartProceed,
                        outletData,
                        orderType,
                        orderTypeSub,
                      },
                      merge: true,
                    });
                  }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 28,
                      color: Colors.whiteColor,
                    }}>
                    Proceed
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  // onPress={() => {
                  //   setState({ visible: false });
                  // }}
                  style={{
                    backgroundColor: Colors.secondaryColor,
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    borderRadius: 10,
                    height: 60,
                    marginTop: 20,
                  }}
                  onPress={() => {
                    setState({ cartWarning: false });
                  }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 28,
                      color: Colors.whiteColor,
                    }}>
                    Take me back
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ModalView>
        {/* <ScrollView showsHorizontalScrollIndicator={false}
         //onScroll={onContainerScroll}
        onScroll={({ nativeEvent }) => {
          if (isCloseToBottom(nativeEvent)) {
            //// console.log("HIT BOTTOM")
            //nextCategory()
          }
        }}
        stickyHeaderIndices={[1]}
      > */}

        <View
          style={[
            switchMerchant
              ? {
                height: windowHeight * 0.8,
                // borderWidth: 1,
                width: windowWidth * 1.04,
                // padding: 0
                // marginLeft: '-10%'
              }
              : {
                width: windowWidth * 0.96,
              },
          ]}>
          {/* <Image
          source={{ uri: outletData.cover }}
          style={styles.outletCover}
          resizeMode={'cover'}
        /> */}

          <View style={{ flexDirection: 'row', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <TouchableOpacity
              style={{ marginVertical: 8 }}
              onPress={() => {
                requestAnimationFrame(() => {
                  props.navigation.goBack();
                });
              }}>
              <View
                style={{
                  marginLeft: 10,
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  marginTop: 5,
                  marginBottom: 5,
                  opacity: 0.8,
                }}>
                {switchMerchant ? (
                  <Feather
                    name="chevron-left"
                    size={20}
                    color={Colors.primaryColor}
                    style={{}}
                  />
                ) : (
                  <Feather
                    name="chevron-left"
                    size={30}
                    color={Colors.primaryColor}
                    style={{}}
                  />
                )}

                <Text
                  style={{
                    color: Colors.primaryColor,
                    fontSize: switchMerchant ? 14 : 17,
                    textAlign: 'center',
                    fontFamily: 'NunitoSans-Bold',
                    marginBottom: Platform.OS === 'ios' ? 0 : 2,
                    // lineHeight: 22,
                    //marginTop: -3,
                  }}>
                  Back
                </Text>
              </View>
            </TouchableOpacity>

            <View
              style={{
                width: switchMerchant ? 35 : 40,
                height: switchMerchant ? 35 : 40,
                backgroundColor: 'white',
                borderRadius: 25,
                //flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                justifyContent: 'center',

                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 4,

                borderWidth: 1,
                borderColor: '#E5E5E5',

                marginRight: '5%',

                // marginLeft: Platform.OS == 'ios' ? '82%' : '87%',
                // marginTop: 10,
                //marginBottom: 5,
                //marginRight: switchMerchant ? 0 : 0,
              }}>
              <TouchableOpacity
                style={{ alignSelf: "center" }}
                onPress={() => {
                  if (openSearchBar) {
                    setOpenSearchBar(false)
                  }
                  else {
                    setOpenSearchBar(true);
                  }
                }}
              >
                <Feather
                  name="search"
                  size={switchMerchant ? 13 : 18}
                  color={Colors.primaryColor}
                  style={{}}
                />
              </TouchableOpacity>
            </View>
          </View>

          {openSearchBar ? (
            <View style={{ width: '96%' }}>
              {/* <View
                style={[
                  {
                    position: 'absolute',
                    alignItems: 'center',
                    paddingVertical: 12,
                    width: 30,
                    height: switchMerchant ? windowHeight * 0.115 : 49,
                    backgroundColor: Colors.highlightColor,
                    opacity: 0.85,
                  },
                  windowWidth === 1280 && windowHeight === 800
                    ? {
                      height: 50,
                    }
                    : {},
                ]}>
                {switchMerchant ? (
                  <Entypo
                    name="chevron-thin-left"
                    size={16}
                    color={Colors.primaryColor}
                    style={{ marginLeft: -1, height: '120%' }}
                  />
                ) : (
                  <Entypo
                    name="chevron-thin-left"
                    size={20}
                    color={Colors.primaryColor}
                    style={{ marginLeft: -1, marginTop: '2%' }}
                  />
                )}
              </View> */}

              <View>
                <View
                  style={[styles.infoTab,
                  {
                    paddingHorizontal: 25,
                    justifyContent: "center",
                    alignItems: "center",
                    paddingVertical: 4.5,
                  }
                  ]}
                >
                  <View
                    style={{
                      padding: 8,
                      backgroundColor: "#ffffff",
                      borderRadius: 25,
                      alignItems: "center",
                      justifyContent: "flex-start",
                      width: windowWidth * 0.8,
                      height: 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 4,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    }}>
                    <Feather
                      name={"search"}
                      color={Colors.primaryColor}
                      size={24}
                      style={{
                        // top: 1,
                      }}
                    />
                    <TextInput
                      editable={!loading}
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: windowWidth * 0.75,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                        height: 40,
                        marginLeft: 10
                      }}
                      clearButtonMode="while-editing"
                      placeholder="Search"
                      placeholderTextColor={'#a9a9a9'}
                      onChangeText={(text) => {
                        if (text) {
                          setSearch(text);
                          CommonStore.update((s) => {
                            s.isOnCategory = false;
                          });
                        } else {
                          CommonStore.update((s) => {
                            s.isOnCategory = true;
                          });
                          setSearch('');
                          setOpenSearchBar(false);
                        }
                        // setIsOnSearch(true)
                      }}
                      value={search}
                    />
                  </View>
                </View>
              </View>
            </View>
          ) : (
            <View style={{ width: '96%' }}>
              <View
                style={[
                  {
                    position: 'absolute',
                    alignItems: 'center',
                    paddingVertical: 12,
                    width: 30,
                    height: switchMerchant ? windowHeight * 0.115 : 49,
                    backgroundColor: Colors.highlightColor,
                    opacity: 0.85,
                  },
                  windowWidth === 1280 && windowHeight === 800
                    ? {
                      height: 50,
                    }
                    : {},
                ]}>
                {switchMerchant ? (
                  <Entypo
                    name="chevron-thin-left"
                    size={16}
                    color={Colors.primaryColor}
                    style={{ marginLeft: -1, height: '120%' }}
                  />
                ) : (
                  <Entypo
                    name="chevron-thin-left"
                    size={20}
                    color={Colors.primaryColor}
                    style={{ marginLeft: -1, marginTop: '2%' }}
                  />
                )}
              </View>

              <View
                style={[
                  {
                    position: 'absolute',
                    alignItems: 'center',
                    alignSelf: 'flex-end',
                    paddingVertical: 12,
                    width: 30,
                    height: switchMerchant ? windowHeight * 0.115 : 49,
                    backgroundColor: Colors.highlightColor,
                    opacity: 0.85,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 1,
                      height: 0.5,
                    },
                    shadowOpacity: 0.12,
                    shadowRadius: 3.22,
                  },
                  switchMerchant
                    ? {
                      // borderWidth: 1,
                      right: windowWidth * 0.08,
                      // opacity: 0,
                    }
                    : {},
                  windowWidth === 1280 && windowHeight === 800
                    ? {
                      height: 50,
                    }
                    : {},
                ]}>

                {switchMerchant ? (
                  <Entypo
                    name="chevron-thin-right"
                    size={16}
                    color={Colors.primaryColor}
                    style={{ height: '120%' }}
                  />
                ) : (
                  <Entypo
                    name="chevron-thin-right"
                    size={20}
                    color={Colors.primaryColor}
                    style={{ marginLeft: -2, marginTop: '2%' }}
                  />
                )}
              </View>

              <ScrollView
                showsHorizontalScrollIndicator={false}
                alwaysBounceHorizontal
                horizontal
                contentContainerStyle={[
                  {
                    // paddingLeft: 20,
                  },
                  switchMerchant
                    ? {
                      // width: windowWidth * 1.2,
                      // backgroundColor: 'red',
                      paddingLeft: windowWidth * 0.05,
                    }
                    : {
                      paddingLeft: 20,
                    },
                ]}
                style={[
                  styles.infoTab,
                  {
                    zIndex: -1,
                    // ...!isInfoTabHitTop && { position: 'absolute' },
                    // ...!isInfoTabHitTop && { top: 120 },
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.88,
                      height: windowHeight * 0.115,
                      // backgroundColor: 'red',
                    }
                    : {},
                ]}
              // stickyHeaderIndices={[0]}
              >
                {/* {categoryOutlet.map((item, index) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setState({
                      category: item.label,
                      categoryIndex: index,
                      // menu: choice[index].category,

                    })
                    refreshMenu();
                  }}>
                  <View
                    style={[
                      styles.category,
                      {
                        borderBottomColor:
                          category == item.label
                            ? Colors.primaryColor
                            : null,
                        borderBottomWidth:
                          category == item.label ? 3 : 0,
                      },
                    ]}>
                    <Text
                      style={{
                        textTransform: 'capitalize',
                        paddingVertical: 12,
                        fontFamily: category == item.label
                          ? "NunitoSans-Bold"
                          : "NunitoSans-Regular",
                        color: category == item.label
                          ? Colors.primaryColor
                          : Colors.blackColor,
                        fontSize: 15,
                      }}>
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })} */}

                {filteredOutletCategories
                  // .slice().sort((a, b) => {
                  //   return (a.orderIndex ? a.orderIndex : filteredOutletCategories.length) -
                  //     (b.orderIndex ? b.orderIndex : filteredOutletCategories.length)
                  // })
                  .map((item, index) => {
                    return (
                      <TouchableOpacity
                        onPress={() => {
                          // setState({
                          //   category: item.label,
                          //   categoryIndex: index,
                          //   // menu: choice[index].category,

                          // })
                          // refreshMenu();

                          CommonStore.update((s) => {
                            s.selectedOutletItemCategory = item;
                          });
                        }}>
                        <View
                          style={[
                            styles.category,
                            {
                              // borderBottomColor:
                              //   selectedOutletItemCategory.name == item.name
                              //     ? Colors.primaryColor
                              //     : null,
                              // borderBottomWidth:
                              //   selectedOutletItemCategory.name == item.name ? 3 : 0,
                            },
                            switchMerchant
                              ? {
                                width: windowWidth * 0.14,
                                height: windowHeight * 0.115,
                              }
                              : {},
                          ]}>
                          <View
                            style={[
                              {
                                borderBottomColor:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? Colors.primaryColor
                                    : null,
                                borderBottomWidth:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? 3
                                    : 0,
                              },
                              switchMerchant ? {} : {},
                            ]}>
                            <Text
                              style={{
                                textTransform: 'capitalize',
                                paddingVertical: 12,
                                fontFamily:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? 'NunitoSans-Bold'
                                    : 'NunitoSans-Regular',
                                color:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? Colors.primaryColor
                                    : Colors.mainTxtColor,
                                fontSize: switchMerchant ? 10 : 16,
                              }}>
                              {item.name}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
              </ScrollView>
            </View>
          )}

          {switchMerchant ? (
            //switchMercant by category
            (<FlashList
              // data={outletItemsDisplay.filter((item) => {
              //   if (search !== '') {
              //     const searchLowerCase = search.toString().toLowerCase();
              //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
              //       return true;
              //     } else {
              //       return false;
              //     }
              //   } else {
              //     return true;
              //   }
              // })}
              // data={outletItemsDisplay.slice().sort((a, b) => {
              //   return (a.orderIndex ? a.orderIndex : outletItemsDisplay.length) -
              //     (b.orderIndex ? b.orderIndex : outletItemsDisplay.length)
              // })}
              data={outletItemsDisplay}
              // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
              // extraData={outletItems}
              renderItem={renderMenu}
              keyExtractor={(item, index) => index}
              contentContainerStyle={{
                // paddingLeft: 10,
                paddingTop: 20,
                width: windowWidth * 0.88,
                // backgroundColor: 'red'
              }}
            />)
          ) : switchMerchant && openSearchBar ? (
            <FlashList
              // data={outletItemsDisplay.filter((item) => {
              //   if (search !== '') {
              //     const searchLowerCase = search.toString().toLowerCase();
              //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
              //       return true;
              //     } else {
              //       return false;
              //     }
              //   } else {
              //     return true;
              //   }
              // })}
              // data={outletItemsDisplay.slice().sort((a, b) => {
              //   return (a.orderIndex ? a.orderIndex : outletItemsDisplay.length) -
              //     (b.orderIndex ? b.orderIndex : outletItemsDisplay.length)
              // })}
              data={outletItemsDisplay}
              // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
              // extraData={outletItems}
              renderItem={renderMenu}
              keyExtractor={(item, index) => index}
              contentContainerStyle={{
                // paddingLeft: 10,
                paddingTop: 20,
                width: windowWidth * 0.88,
                // backgroundColor: 'red'
              }}
            />
          ) : openSearchBar ? (
            <FlashList
              // data={outletItemsDisplay.filter((item) => {
              //   if (search !== '') {
              //     const searchLowerCase = search.toString().toLowerCase();
              //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
              //       return true;
              //     } else {
              //       return false;
              //     }
              //   } else {
              //     return true;
              //   }
              // })}
              // data={outletItemsDisplay.slice().sort((a, b) => {
              //   return (a.orderIndex ? a.orderIndex : outletItemsDisplay.length) -
              //     (b.orderIndex ? b.orderIndex : outletItemsDisplay.length)
              // })}
              data={outletItemsDisplay}
              // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
              // extraData={outletItems}
              renderItem={renderMenu}
              keyExtractor={(item, index) => index}
              // style={{
              //   width: windowWidth * 0.9,
              // }}
              contentContainerStyle={{
                paddingLeft: 10,
                paddingRight: 10,
                paddingTop: 20,
                // width: windowWidth * 0.9,

                width: windowWidth * 0.9,

                // paddingRight: windowWidth * 0.1,
              }}
            />
          ) : (
            //By category
            (<FlashList
              // data={outletItemsDisplay.filter((item) => {
              //   if (search !== '') {
              //     const searchLowerCase = search.toString().toLowerCase();
              //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
              //       return true;
              //     } else {
              //       return false;
              //     }
              //   } else {
              //     return true;
              //   }
              // })}
              // data={outletItemsDisplay.slice().sort((a, b) => {
              //   return (a.orderIndex ? a.orderIndex : outletItemsDisplay.length) -
              //     (b.orderIndex ? b.orderIndex : outletItemsDisplay.length)
              // })}
              data={outletItemsDisplay}
              // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
              // extraData={outletItems}
              renderItem={renderMenu}
              keyExtractor={(item, index) => index}
              contentContainerStyle={{
                paddingLeft: 10,
                paddingRight: 10,
                paddingTop: 20,
                width: windowWidth * 0.9,
              }}
            />)
          )}
          <View style={{ height: 30 }} />
        </View>
        {/* <View style={{ minHeight: 100 }} /> */}
        {/* </ScrollView>  */}

        {cartIcon ? (
          <Draggable
            shouldReverse={reverse}
            renderSize={120}
            renderColor={Colors.secondaryColor}
            isCircle
            x={switchMerchant ? windowWidth * 0.7 : windowWidth * 0.8}
            y={switchMerchant ? windowHeight * 0.65 : windowHeight * 0.6}
            // onShortPressRelease={() => { goToCart(), cartCount() }}
            onShortPressRelease={() => {
              requestAnimationFrame(() => {
                onCartClicked();
              });
            }}
            // onPressOut={onCartClicked}
            onRelease={() => {
              requestAnimationFrame(() => {
                onCartClicked();
              });
            }}>
            <View
              style={{
                width: switchMerchant ? 45 : 90,
                height: switchMerchant ? 45 : 90,
                justifyContent: 'center',
              }}>
              <View style={{ alignSelf: 'center' }}>
                {switchMerchant ? (
                  <Ionicons
                    name="cart-outline"
                    size={25}
                    color={Colors.mainTxtColor}
                  />
                ) : (
                  <Ionicons
                    name="cart-outline"
                    size={60}
                    color={Colors.mainTxtColor}
                  />
                )}
              </View>
              <View
                style={[
                  styles.cartCount,
                  switchMerchant ? {
                    height: 30,
                    width: 30,
                    top: -4,
                    right: -8,
                  } : {},
                ]}>
                {/* <Text style={{ color: Colors.whiteColor, fontSize: 10, fontFamily: "NunitoSans-Regular" }}>{Cart.getCartItem().length}</Text> */}
                <Text
                  style={[{
                    color: Colors.whiteColor,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    bottom: Platform.OS === 'android' ? 2 : 0,
                  }, switchMerchant ? {
                    bottom: Platform.OS === 'android' ? 1 : 0,
                  } : {}]}>
                  {cartItems.length}
                </Text>
              </View>
            </View>
          </Draggable>
        ) : null}
      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  confirmBox: {
    width: 350,
    height: 350,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
    flexDirection: 'row',
  },
  outletCover: {
    // width: '50%',
    // alignSelf: 'center',
    // height: undefined,
    // aspectRatio: 2,
    // borderRadius: 5,
    width: '100%',
    alignSelf: 'center',
    height: '30%',
    // aspectRatio: 2,
    borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
  workingHourTab: {
    padding: 16,
    flexDirection: 'row',
  },
  outletAddress: {
    textAlign: 'center',
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: 'row',
    marginTop: 20,
  },
  actionView: {
    width: Dimensions.get('window').width / 4,
    height: Dimensions.get('window').width / 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
  category: {
    width: 190,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatCartBtn: {
    zIndex: 2,
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: 'absolute',
    top: -1,
    right: -2,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default OutletMenuScreen;
