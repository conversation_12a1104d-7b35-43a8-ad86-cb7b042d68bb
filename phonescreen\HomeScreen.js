import React, { Component, useState, useEffect } from 'react'
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, Dimensions, Button, Platform } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'
import { FlatList } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import TableBar from './components/TableBar';
import { listenToCurrOutletIdChangesWaiter, listenToFirestoreChanges, listenToUserChangesWaiter, listenToSelectedOutletItemChanges, listenToSelectedOutletTableIdChanges, requestNotificationsPermission, subText, removeOrdinalFromDate, getOrdinalFromDate } from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { NOTIFICATIONS_CHANNEL, NOTIFICATIONS_ID, ORDER_TYPE, ROLE_TYPE, USER_ORDER_STATUS, RING_TOP_BAR_SORT, RING_TOP_BAR, RING_TOP_BAR_PARSED } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import messaging from '@react-native-firebase/messaging';
import { parseMessages } from '../util/notifications';
import PushNotification from 'react-native-push-notification';
import { NotificationStore } from '../store/notificationStore';
import { useScrollToTop } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import moment from 'moment';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';

const currDate = moment();

const HomeScreen = props => {
    const {
        navigation,
    } = props;

    const [tableCount, setTableCount] = useState('');
    const [orderCount, setOrderCount] = useState('');
    const [takeawayCount, setTakeawayCount] = useState('');
    const [reservationCount, setReservationCount] = useState('');
    const [queueCount, setQueueCount] = useState('');
    const [param, setParam] = useState(1);
    const [showItem, setShowItem] = useState(false);
    const [takeAway, setTakeAway] = useState('');
    const [ringCount, setRingCount] = useState('');
    const [ringOrder, setRingOrder] = useState({});
    const [ringTable, setRingTable] = useState({});
    const [ringOrderItems, setRingOrderItems] = useState([]);
    const [ringOrderItemPrice, setRingOrderItemPrice] = useState('');
    const [ringOrderItemQuantity, setRingOrderItemQuantity] = useState('');
    const [ringOrderId, setRingOrderId] = useState('');
    const [ring, setRing] = useState('');
    const [ringType, setRingType] = useState('');
    const [visible, setVisible] = useState(false);
    const [orderItem, setOrderItem] = useState([]);
    const [isCheck, setIsCheck] = useState(false);
    const [visible2, setVisible2] = useState(false);
    const [isCheck2, setIsCheck2] = useState(false);
    const [tick3, setTick3] = useState('');
    const [ringScreen, setRingScreen] = useState('');

    navigation.setOptions({
        headerLeft: () => <View></View>,
        headerTitle: () => (
            <View style={{
                alignSelf: 'center',
                marginTop: '2%',
            }}>
                <Image
                    style={styles.headerLogo}
                    resizeMode="contain"
                    source={require('../assets/image/logo_2.png')}
                />
            </View>
        ),
        headerRight: () => (
            <View style={styles.button}>
                <TouchableOpacity
                    onPress={() => { props.navigation.navigate('Profile') }}>
                    <Image style={styles.drawerIcon} source={require('../assets/image/drawer.png')} />
                </TouchableOpacity>
            </View>

        ),
    });

    // navigation.setOptions({
    //     headerRight: () => (
    //         <View style={styles.button}>
    //             <TouchableOpacity
    //                 onPress={() => { props.navigation.navigate('Profile') }}>
    //                 <Image style={styles.drawerIcon} source={require('../assets/image/drawer.png')} />
    //             </TouchableOpacity>

    //         </View>

    //     ),
    // });

    const merchantId = UserStore.useState(s => s.merchantId);

    const role = UserStore.useState(s => s.role);
    const firebaseUid = UserStore.useState(s => s.firebaseUid);

    const currOutletId = MerchantStore.useState(s => s.currOutletId);

    const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
    const selectedOutletTable = CommonStore.useState(s => s.selectedOutletTable);

    const outletTables = OutletStore.useState(s => s.outletTables);
    const userOrders = OutletStore.useState(s => s.userOrders);
    const userReservations = OutletStore.useState(s => s.userReservations);
    const userQueues = OutletStore.useState(s => s.userQueues);
    const userRings = OutletStore.useState(s => s.userRings);
    const userOrdersAllStatus = OutletStore.useState(s => s.userOrdersAllStatus);

    const nUserOrder = NotificationStore.useState(s => s.nUserOrder);
    const nUserRing = NotificationStore.useState(s => s.nUserRing);
    const nWaiterKitchenReadyOrder = NotificationStore.useState(s => s.nWaiterKitchenReadyOrder);

    useEffect(() => {
        if (firebaseUid !== '') {
            // if (role === ROLE_TYPE.FRONTLINER) {
            //     listenToUserChangesWaiter(firebaseUid);
            // }
            listenToUserChangesWaiter(firebaseUid);
        }
    }, [firebaseUid, role]);

    useEffect(() => {
        if (currOutletId !== '' && role !== '') {
            // if (role === ROLE_TYPE.FRONTLINER) {
            //     listenToCurrOutletIdChangesWaiter(role, currOutletId);
            // }
            listenToCurrOutletIdChangesWaiter(role, currOutletId, true);
        }
    }, [currOutletId, role]);

    useEffect(() => {
        if (selectedOutletItem !== null &&
            selectedOutletItem !== undefined &&
            selectedOutletItem.uniqueId) {
            listenToSelectedOutletItemChanges(selectedOutletItem);
        }
    }, [selectedOutletItem]);

    useEffect(() => {
        if (firebaseUid.length > 0 && selectedOutletTable && selectedOutletTable.uniqueId && currOutletId) {
            listenToSelectedOutletTableIdChanges(firebaseUid, selectedOutletTable.uniqueId, currOutletId);
        }
    }, [firebaseUid, selectedOutletTable, currOutletId]);

    ////////////////////////////////////////////////////////

    // from notifications

    useEffect(() => {
        if (nUserOrder && nUserOrder.type) {
            if (nUserOrder.orderType === ORDER_TYPE.DINEIN) {
                navigation.navigate('Order');
            }
            else {
                navigation.navigate('Takeaway');
            }
        }

        if (nUserRing && nUserRing.type) {
            navigation.navigate('Ring');
        }

        if (nWaiterKitchenReadyOrder && nWaiterKitchenReadyOrder.type) {
            if (nWaiterKitchenReadyOrder.orderType === ORDER_TYPE.DINEIN) {
                navigation.navigate('Order');
            }
            else {
                navigation.navigate('Takeaway');
            }
        }
    }, [nUserOrder,
        userOrders,

        nUserRing,
        nWaiterKitchenReadyOrder]);

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (firebaseUid.length > 0 && currOutletId.length > 0 && merchantId.length > 0 && role.length > 0) {
            messaging().getToken()
                .then(async tokenFcm => {
                    await AsyncStorage.setItem('tokenFcm', tokenFcm);

                    await updateTokenFcm();
                });

            messaging().onTokenRefresh(async tokenFcm => {
                await AsyncStorage.setItem('tokenFcm', tokenFcm);

                await updateTokenFcm();
            });
        }
    }, [firebaseUid, currOutletId, merchantId, role]);

    const updateTokenFcm = async () => {
        const tokenFcm = await AsyncStorage.getItem('tokenFcm');

        if (tokenFcm) {
            const body = {
                tokenFcm: tokenFcm,
                userId: firebaseUid,
                outletId: currOutletId,
                merchantId: merchantId,
                role: role,
            };

            ApiClient.POST(API.updateTokenFcm, body).then((result) => {
                console.log('updated token fcm');
            });
        }
    };

    useEffect(() => {
        requestNotificationsPermission();

        messaging().onMessage(async msg => {
            console.log('message from foreground!');
            console.log(msg);

            parseMessages(msg);
        });
    }, []);

    ////////////////////////////////////////////////////////

    const setState = () => { };

    // getTableCount();
    // getOrderCount();
    // getCurrentTakeaway();
    // getRingList();
    // getReservationList();
    // getOutletQueue();
    // getRingOrder();

    // setInterval(() => {
    //     getRingList();
    //     getOutletQueue();
    //     getTableCount();
    //     getOrderCount();
    // }, 10000);

    const waiterCheckOrder = () => {
        var body = {
            orderItem: orderItem
        }

        ApiClient.POST(API.waiterCheckOrder, body, false).then(result => {

            //getRingOrder();
            if (result.success == true) {
                Alert.alert('Order has been sent to the kitchen')
                setState({ orderItem: [] })
            }
            if (orderItem[0] == 0) {
                setState({ visible2: true })
            }
        }).catch(err => { console.log(err) })

    }

    const pushArray = param => {
        const push = orderItem
        push.push(
            {
                id: param
            }
        )
    }

    const removePush = param => {
        const items = orderItem
        const filterArray = items.filter(item => item.id !== param)
        setState({ orderItem: filterArray })
    }

    const checkItem = param => {
        if (isCheck == false) {
            setState({ isCheck: true }), param.tick = true
            removePush(param.id)

        } else {
            setState({ isCheck: false }), param.tick = false
            pushArray(param.id)
        }
    }

    const checkItem2 = () => {
        if (tick2 == false) {
            setState({ tick2: true })

        } else {
            setState({ tick2: false })
        }
    }

    const checkItem3 = () => {
        if (tick3 == false) {
            setState({ tick3: true })
        } else {
            setState({ tick3: false })
        }
    }

    const renderOrderItems2 = ({ item }) => (
        <View style={styles.bottomPart}>
            <TouchableOpacity onPress={() => { checkItem(item) }}><View style={[styles.topPart, { flex: 0.5 },]}><Icon name={'checkbox'} size={20} color={!item.tick == true ? Colors.tabGrey : Colors.primaryColor} /></View></TouchableOpacity>
            <View style={[styles.topPart, { flex: 1 }]}><Text>{item.name}</Text><Text style={{ fontSize: 15, color: Colors.tabGrey }}>{item.remarks}</Text></View>
            <View style={styles.topPart}><Text>x{item.quantity}</Text></View>
            <View style={styles.topPart}><Text>RM{item.price}</Text></View>

        </View>
    )

    const showOrderItem = param => {

        if (showItem == false) {
            return setState({ showItem: true }), param.expand = true
        } else {
            return setState({ showItem: false }), param.expand = false
        }

    }

    const getRingOrder = () => {

        ApiClient.GET(API.getOutletRing + User.getOutletId()).then(result => {
            if (result) {
                //console.log('ringOrderItemPrice');
                //console.log(ringOrderItemPrice);
                setState({
                    ring: result,
                    ringOrder: result.order,
                    ringTable: result.order.table,
                    ringOrderItems: result.order.orderItems,
                    //ringOrderItemPrice: result.order.orderItems[0].price,
                    //ringOrderItemQuantity: result.order.orderItems[0].quantity,
                    ringType: result.type,
                    visible: true
                })
            }

            if (!result) {
                return false;
            }

        }).catch(err => { console.log(err) })

    }

    const acceptRingOrder = param => {
        var body = {
            ringId: param,
        }

        ApiClient.POST(API.notifiedOutletRing, body, false).then(result => {
            if (result.success) {
                setState({ visible: false })
                getRingList()
                Alert.alert("Order Accepted")
            }
            if (orderItem[0] !== null) {
                setState({ visible2: true })
            }
        }).catch(err => { console.log(err) })
    }

    const getReservationList = () => {
        ApiClient.GET(API.getOutletReservation + User.getOutletId()).then(result => {
            setState({ reservationCount: result.length })
        }).catch(err => { console.log(err) })
    }

    const getRingList = () => {
        ApiClient.GET(API.getOutletRingList + User.getOutletId()).then(result => {
            setState({ ringCount: result.length, })
        }).catch(err => { console.log(err) })
    }

    const getOrderCount = () => {
        ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(result => {
            var count = 0
            for (const order of result) {
                if (order.type == "Dine In") {
                    count = count + 1;
                    //console.log('Count' + count);
                }
                setState({ orderCount: count });
            };
        }).catch(err => { console.log(err) })
    }

    const getTableCount = () => {
        ApiClient.GET(API.getTable + User.getOutletId()).then(result => {

            var count = 0
            for (const table of result) {
                if (table.section.length > 0 && table.capacity > 0) {
                    count = count + 1
                }
            }
            setState({ tableCount: count })
        }).catch(err => { console.log(err) })
        // .catch(err => { setState({ tableCount: 13 }) })
    }

    const getCurrentTakeaway = () => {
        ApiClient.GET(API.getCurrentTakeAwayOrder + User.getOutletId()).then(result2 => {
            setState({ takeaway: result2, takeawayCount: result2.length })
        }).catch(err => { console.log(err) })
    }

    const getOutletQueue = () => {
        ApiClient.GET(API.getAllOutletQueue + User.getOutletId()).then(result => {
            setState({ queueCount: result.length })
        }).catch(err => { console.log() })
    }

    return (
        <View style={styles.container}>
            <View style={{ flex: 1.8, backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 15, paddingVertical: 15, paddingBottom: 5, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}>
                {/* <TableBar
                // orderTables={orderId}
                /> */}
                <TableBar />
            </View>

            <View style={{
                flex: 7.8,
                padding: 5
            }}>
                <View style={styles.section}>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Table') }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            <MaterialCommunityIcons name='table-chair' size={32} style={{ opacity: 0.9 }} color={Colors.tabGrey} />
                            {!outletTables.filter(e => e.seated > 0).length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{outletTables.filter(e => e.seated > 0).length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Table</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Order') }} style={{ width: '34%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            <Image style={styles.circleIcon} source={require('../assets/image/ClickButton2.png')} />
                            {!userOrders.filter(order => (order.orderType === ORDER_TYPE.DINEIN) && (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED || order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING)).length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{userOrders.filter(order => (order.orderType === ORDER_TYPE.DINEIN) && (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED || order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING)).length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Order</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Takeaway') }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            {/* <Image style={styles.circleIcon} source={require('../assets/image/TakeawayBlack.png')} /> */}
                            <SimpleLineIcons name='bag' size={33} color={Colors.tabGrey} />
                            {!userOrders.filter(order => (order.orderType !== ORDER_TYPE.DINEIN) && (order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED || order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING)).length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{userOrders.filter(order => (order.orderType !== ORDER_TYPE.DINEIN) && (order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED || order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING)).length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Takeaway</Text>
                    </TouchableOpacity>

                </View>
                <View style={styles.section}>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Ring'); }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            {/* <EvilIcons name={'bell'} size={47} color={Colors.tabGrey} /> */}
                            <SimpleLineIcons name={'bell'} size={33} color={Colors.tabGrey} />
                            {!userRings.length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{userRings.length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Ring</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Reservation') }} style={{ width: '34%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            <Image style={styles.circleIcon} source={require('../assets/image/Seach.png')} />
                            {!userReservations.length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{userReservations.length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Reservations</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Queue') }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            <Image style={styles.circleIcon} source={require('../assets/image/QueueBlack.png')} />
                            {!userQueues.length == 0 ? <View style={styles.smallCircle}>
                                <Text style={styles.smallCircleFont}>{userQueues.length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>Queue</Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.section}>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('OrderHistory') }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>
                        <View style={styles.circle}>
                            <MaterialCommunityIcons name={'history'} size={42} color={Colors.tabGrey} style={{
                                opacity: 0.85,
                                top: 1,
                                right: 1,
                            }} />
                            {!userOrdersAllStatus.length == 0 ? <View style={styles.smallCircle}>
                                <Text style={{
                                    color: 'white',
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 15,
                                    marginTop: Platform.OS == 'android' ? 1 : 0,
                                }}>{userOrdersAllStatus.length}</Text>
                            </View> : null}
                        </View>
                        <Text style={{ alignSelf: "center", color: "#7B8E92", fontSize: 16, fontFamily: 'NunitoSans-SemiBold', marginTop: 5, }}>History</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { }} style={{ width: '34%', justifyContent: 'center', alignItems: 'center' }}>
                        {/* <View style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                        }}>
                            {subText(
                                removeOrdinalFromDate(
                                    currDate // date variable
                                ),
                                getOrdinalFromDate(
                                    currDate // date variable
                                )
                            )}
                            <Text>
                                {moment(currDate).format(' MMMM')}
                            </Text>
                        </View> */}
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => { }} style={{ width: '30%', justifyContent: 'center', alignItems: 'center' }}>

                    </TouchableOpacity>
                </View>
                <View style={{ height: '1%' }}>
                </View>
                <View style={{
                    height: '30%',
                    // backgroundColor: Colors.primaryColor,
                    borderRadius: 30,
                    margin: 20,
                    marginBottom: 0,
                }}>

                    {/* <View style={{
                        justifyContent: 'center',
                        flex: 1, alignItems: 'center',
                        fontFamily: 'NunitoSans-Bold',

                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                    }}>
                        <Image style={{ width: '80%', height: '100%', resizeMode: 'contain' }} source={require('../assets/image/logo.png')} />
                    </View> */}

                    <Modal
                        supportedOrientations={['landscape', 'portrait']}
                        animationType="fade"
                        transparent={true}
                        visible={visible}
                        style={{ flex: 1, backgroundColor: 'red' }}
                    >
                        {ringType == 2 ? (
                            <View style={{ flex: 1, minHeight: Dimensions.get('window').height, width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.modalBgColor }}>
                                <View style={{ backgroundColor: 'white', width: '85%', height: '60%', alignItems: 'center', justifyContent: 'center', borderRadius: 20 }}>
                                    <View style={{ alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}><Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 25, marginTop: 40 }}>New Order!</Text></View>
                                    <View style={{ height: '20%', width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>

                                        <Text style={[styles.modalTop, { fontSize: 15, fontFamily: 'NunitoSans-Light' }]}>Table <Text style={{ fontSize: 15 }}>{ringOrder == null ? null : ringTable == null ? null : ringTable.code == null ? null : ringTable.code}</Text></Text>
                                        <Text style={[styles.modalTop, { fontSize: 15, fontFamily: 'NunitoSans-Bold' }]}>Order <Text style={{ fontSize: 15 }}>#{ringOrder.id}</Text></Text>
                                        <Text style={[styles.modalTop, { fontSize: 15, color: Colors.primaryColor, fontFamily: 'NunitoSans-Bold' }]}>RM{ringOrder.finalPrice}</Text>

                                    </View>

                                    <View style={{ height: '70%', width: '100%', }}>
                                        <ScrollView>
                                            {ringOrderItems !== null ?
                                                <FlatList

                                                    data={ringOrderItems}
                                                    renderItem={renderOrderItems2}
                                                    keyExtractor={(item, index) => String(index)} /> : null}
                                        </ScrollView>

                                        <View style={{ height: '50%', width: '90%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <View style={{ paddingVertical: 0, marginBottom: 5 }}><Text style={{ color: 'grey' }}>Do you approve this order for the kitchen ?</Text></View>
                                            <TouchableOpacity onPress={() => { acceptRingOrder(ring.id), waiterCheckOrder(), setState({ visible: false }) }}>
                                                <View style={{ height: 40, width: 120, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, marginBottom: 5, alignItems: 'center', justifyContent: 'center' }}><Text style={{ color: Colors.primaryColor }}>Accept</Text></View>
                                            </TouchableOpacity>
                                        </View>

                                    </View>

                                </View>
                            </View>) :
                            ringType == 1 ? (
                                <View style={{ flex: 1, minHeight: Dimensions.get('window').height, width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.modalBgColor }}>
                                    <View style={{ backgroundColor: 'white', width: '85%', height: '30%', alignItems: 'center', justifyContent: 'center', borderRadius: 20 }}>
                                        <View style={{ alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}><Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 25 }}>Customer Ring !</Text></View>
                                        <View style={{ height: '20%', width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={[styles.modalTop, { fontSize: 15, fontFamily: 'NunitoSans-Light', flex: 0.7 }]}><Text style={{ fontSize: 15 }}>Custormer at Table {ringTable == null ? null : ringTable.code} is calling for a waiter</Text></Text>
                                        </View>
                                        <View style={{ height: '30%', width: '80%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <TouchableOpacity onPress={() => { acceptRingOrder(ring.id), setState({ visible: false }) }}>
                                                <View style={{ height: 40, width: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, alignItems: 'center', justifyContent: 'center' }}><Text style={{ color: Colors.primaryColor }}>Accept</Text></View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>) : null}
                        {visible2 == true ? <View style={{ flex: 1, minHeight: Dimensions.get('window').height, width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.modalBgColor }}>
                            <View style={{ backgroundColor: 'white', width: '85%', height: '50%', alignItems: 'center', justifyContent: 'center', borderRadius: 20 }}>
                                <View style={{ alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                                    <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 25 }}>Order not checked !</Text>
                                </View>
                                <View style={{ height: '10%', width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                    <Text style={[styles.modalTop, { fontSize: 15, fontFamily: 'NunitoSans-Light' }]}>You didn't check {orderItem} x{ringOrderItemQuantity} Order #{ringOrderId} for the kitchen
                                    </Text>
                                </View>
                                <View style={{ width: '100%', padding: 15 }}>
                                    <Text style={[styles.insideCheck, { color: Colors.tabGrey, fontFamily: 'NunitoSans-Light' }]}>Please state the reason</Text>
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <TouchableOpacity onPress={() => checkItem2}><Icon name={'checkbox'} size={20} color={!tick2 == true ? Colors.tabGrey : Colors.primaryColor} /></TouchableOpacity>
                                        <Text style={[styles.insideCheck]}>Cancelled Order</Text>
                                    </View>
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <TouchableOpacity onPress={() => checkItem3}><Icon name={'checkbox'} size={20} color={!tick3 == true ? Colors.tabGrey : Colors.primaryColor} /></TouchableOpacity>
                                        <Text style={[styles.insideCheck]}>Out of Stock</Text>
                                    </View>
                                </View>
                                <View style={{ width: '100%', height: '30%', padding: 10 }}>
                                    <Text style={[styles.insideCheck, { color: Colors.tabGrey }]}>Others:</Text>
                                    <TextInput style={{ backgroundColor: Colors.fieldtBgColor, width: '100%', height: '70%', borderRadius: 15, padding: 10 }} multiline={true}>
                                    </TextInput>
                                </View>

                                <View style={{ width: '40%', height: '10%', padding: 10, backgroundColor: Colors.fieldtBgColor, borderRadius: 15, justifyContent: 'center', alignItems: 'center' }}>
                                    <TouchableOpacity onPress={() => { setState({ visible2: false }) }}>
                                        <Text style={{ color: Colors.primaryColor, fontSize: 15, fontFamily: 'NunitoSans-SemiBold' }}>Submit</Text>
                                    </TouchableOpacity>
                                </View>


                            </View>

                        </View>
                            : null}

                    </Modal>

                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff'
    },
    topFlat: {
        backgroundColor: '#ffffff',
        margin: 10,
        borderRadius: 8,
        alignItems: 'center',
        paddingTop: 15,
        height: 60,
        width: 60,
        elevation: 5,
    },
    section: {
        height: '20%',
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        alignItems: 'center',
        paddingHorizontal: 10,
        marginBottom: 30,
        paddingTop: 50,
    },
    circle: {
        height: 65,
        width: 65,
        borderRadius: 100,
        borderWidth: 1,
        borderColor: Colors.secondaryColor,
        justifyContent: 'center',
        alignItems: 'center',

        backgroundColor: 'white',

        // shadowOpacity: 0,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 2,
        // },
        // shadowOpacity: 0.22,
        // shadowRadius: 3.22,
        // elevation: 3,
    },
    smallCircle: {
        width: 25,
        height: 25,
        backgroundColor: Colors.primaryColor,
        borderRadius: 12.5,
        position: 'absolute',
        top: -5,
        right: -5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    smallCircleFont: {
        color: 'white',
        fontFamily: 'NunitoSans-Bold',
        fontSize: 15,
        marginBottom: Platform.OS == 'android' ? 1 : 0,
    },
    circleIcon: {
        width: '50%',
        height: "50%",
        resizeMode: 'contain',
    },
    modalTop: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        fontFamily: 'NunitoSans-Regular',
        fontSize: 15,

    },
    bottomPart: {
        flexDirection: 'row',
        padding: 10,
        paddingHorizontal: 10,
    },
    topPart: {
        paddingHorizontal: 5,
        justifyContent: 'center',
        flex: 1,
        alignItems: 'center',
    },
    insideCheck: {
        fontSize: 15,
        padding: 2,
        fontFamily: 'NunitoSans-Regular',
    },
    button: {
        flexDirection: 'row',
        // justifyContent: 'flex-end',
        // flex: 1
        justifyContent: 'center',
        alignItems: 'center',
    },
    drawerIcon: {
        width: 32,
        height: 32,
        // marginTop: 23,
        // marginRight: 8
        marginTop: 8,
        marginRight: 25,
    },

    headerLogo: {
        // width: 140,
        // height: 100,
        width: 160,
        height: 50,
    },
})
export default HomeScreen



