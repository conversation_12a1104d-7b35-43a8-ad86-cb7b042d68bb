diff --git a/node_modules/react-native-switch-pro/lib/index.js b/node_modules/react-native-switch-pro/lib/index.js
index 7715b57..bd0be46 100644
--- a/node_modules/react-native-switch-pro/lib/index.js
+++ b/node_modules/react-native-switch-pro/lib/index.js
@@ -1,13 +1,17 @@
 import React, { Component } from 'react'
 import PropTypes from "prop-types"
 import {
-  ViewPropTypes,
-  ColorPropType,
+  // ViewPropTypes,
+  // ColorPropType,
   StyleSheet,
   Animated,
   Easing,
   PanResponder,
 } from 'react-native'
+import {
+  ColorPropType,
+  ViewPropTypes,
+} from 'deprecated-react-native-prop-types';
 
 const SCALE = 6 / 5
 
@@ -50,8 +54,10 @@ export default class extends Component {
       value,
       toggleable: true,
       alignItems: value ? 'flex-end' : 'flex-start',
-      handlerAnimation: new Animated.Value(this.handlerSize),
-      switchAnimation: new Animated.Value(value ? -1 : 1)
+      // handlerAnimation: new Animated.Value(this.handlerSize),
+      // switchAnimation: new Animated.Value(value ? -1 : 1)
+      handlerAnimation: this.handlerSize,
+      switchAnimation: value ? -1 : 1,
     }
   }
 
@@ -143,42 +149,56 @@ export default class extends Component {
 
     this.animateHandler(this.handlerSize)
     if (result) {
-      this.animateSwitch(toValue, () => {
-        this.setState({
-          value: toValue,
-          alignItems: toValue ? 'flex-end' : 'flex-start'
-        }, () => {
-          callback(toValue)
-        })
-        switchAnimation.setValue(toValue ? -1 : 1)
+      // this.animateSwitch(toValue, () => {
+      //   this.setState({
+      //     value: toValue,
+      //     alignItems: toValue ? 'flex-end' : 'flex-start'
+      //   }, () => {
+      //     callback(toValue)
+      //   })
+      //   switchAnimation.setValue(toValue ? -1 : 1)
+      // })
+
+      this.setState({
+        value: toValue,
+        alignItems: toValue ? 'flex-end' : 'flex-start'
+      }, () => {
+        callback(toValue)
       })
+      this.setState({
+        switchAnimation: toValue ? -1 : 1,
+      });
     }
   }
 
   animateSwitch = (value, callback = () => null) => {
     const { switchAnimation } = this.state
 
-    Animated.timing(switchAnimation,
-      {
-        toValue: value ? this.offset : -this.offset,
-        duration: 200,
-        easing: Easing.linear,
-        useNativeDriver: false
-      }
-    ).start(callback)
+    // Animated.timing(switchAnimation,
+    //   {
+    //     toValue: value ? this.offset : -this.offset,
+    //     duration: 200,
+    //     easing: Easing.linear,
+    //     useNativeDriver: false
+    //   }
+    // ).start(callback)
   }
 
   animateHandler = (value, callback = () => null) => {
     const { handlerAnimation } = this.state
 
-    Animated.timing(handlerAnimation,
-      {
-        toValue: value,
-        duration: 200,
-        easing: Easing.linear,
-        useNativeDriver: false
-      }
-    ).start(callback)
+    // Animated.timing(handlerAnimation,
+    //   {
+    //     toValue: value,
+    //     duration: 200,
+    //     easing: Easing.linear,
+    //     useNativeDriver: false
+    //   }
+    // ).start(callback)
+
+    this.setState({
+      handlerAnimation: value,
+    });
   }
 
   render() {
@@ -190,17 +210,21 @@ export default class extends Component {
       ...rest
     } = this.props
 
-    const interpolatedBackgroundColor = switchAnimation.interpolate({
-      inputRange: value ? [-this.offset, -1]: [1, this.offset],
-      outputRange: [backgroundInactive, backgroundActive],
-      extrapolate: 'clamp'
-    })
+    // const interpolatedBackgroundColor = switchAnimation.interpolate({
+    //   inputRange: value ? [-this.offset, -1]: [1, this.offset],
+    //   outputRange: [backgroundInactive, backgroundActive],
+    //   extrapolate: 'clamp'
+    // })
 
-    const interpolatedCircleColor = switchAnimation.interpolate({
-      inputRange: value ? [-this.offset, -1]: [1, this.offset],
-      outputRange: [circleColorInactive, circleColorActive],
-      extrapolate: 'clamp'
-    })
+    const interpolatedBackgroundColor = value ? backgroundActive : backgroundInactive;
+
+    // const interpolatedCircleColor = switchAnimation.interpolate({
+    //   inputRange: value ? [-this.offset, -1]: [1, this.offset],
+    //   outputRange: [circleColorInactive, circleColorActive],
+    //   extrapolate: 'clamp'
+    // })
+
+    const interpolatedCircleColor = value ? circleColorActive : circleColorInactive;
 
     const circlePosition = (value) => {
       const modifier = value ? 1 : -1
@@ -217,11 +241,13 @@ export default class extends Component {
       return position
     }
 
-    const interpolatedTranslateX = switchAnimation.interpolate({
-      inputRange: value ? [-this.offset, -1]: [1, this.offset],
-      outputRange: value ? [-this.offset, circlePosition(value)]: [circlePosition(value), this.offset],
-      extrapolate: 'clamp'
-    })
+    // const interpolatedTranslateX = switchAnimation.interpolate({
+    //   inputRange: value ? [-this.offset, -1]: [1, this.offset],
+    //   outputRange: value ? [-this.offset, circlePosition(value)]: [circlePosition(value), this.offset],
+    //   extrapolate: 'clamp'
+    // })
+
+    const interpolatedTranslateX = value ? circlePosition(value) : circlePosition(value);
 
     return (
       <Animated.View
