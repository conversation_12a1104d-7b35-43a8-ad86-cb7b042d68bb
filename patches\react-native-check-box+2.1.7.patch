diff --git a/node_modules/react-native-check-box/index.js b/node_modules/react-native-check-box/index.js
index 7fb0df3..df5e5ed 100644
--- a/node_modules/react-native-check-box/index.js
+++ b/node_modules/react-native-check-box/index.js
@@ -14,11 +14,12 @@ import {
     Image,
     Text,
     TouchableHighlight,
-    ViewPropTypes as RNViewPropTypes,
+    // ViewPropTypes as RNViewPropTypes,
 } from 'react-native';
 import PropTypes from 'prop-types';
+import { ViewPropTypes as RNViewPropTypes } from 'deprecated-react-native-prop-types';
 
-const ViewPropTypes = RNViewPropTypes || View.propTypes;
+const ViewPropTypes = RNViewPropTypes || RNViewPropTypes;
 
 export default class CheckBox extends Component {
     constructor(props) {
