import { Store } from 'pullstate';
import moment from 'moment';
import { OFFLINE_BILL_TYPE, OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST, RESERVATION_PRIORITY } from '../constant/common';

export const TableStore = new Store({
    showDiscountModal: false,
    selectedDiscountItem: {},
    discountModalAmount: 0,
    discountModalQuantity: 0,
    isDiscountPercentage: false,
    isItemDiscount: false,

    discountCartItemDict: {},
    discountOrder: {
        value: '0',
        text: '0',
    },

    discountCartItemTableIdDict: {},
    discountOrderTableIdDict: {},

    discountAccum: 0,
    discountAccumOrder: 0,

    discountText: '',

    showAddCustomer: false,

    isCustomer: false,
    showCustomerList: false,

    searchCustomer: '',

    customerAvatar: '',
    customerName: '',
    customerPhone: '+60',
    customerUsername: '',
    customerUniqueId: '',

    customerNextVisitDate: '',
    customerNextVisitTime: '',
    customerNextVisitDateIsChanged: false,
    customerNextVisitTimeIsChanged: false,

    customerIsActiveMember: true,
    customerTags: [],
    customerGender: 'Others',
    customerDob: Date.now(),
    customerEmail: '',
    customerRace: '',

    customerAddress: '',
    customerAddressLat: '',
    customerAddressLng: '',

    customerState: '',
    customerPostcode: '',
    customerFirstVisitDate: '',
    customerLastVisitDate: '',

    customerPhoneSecond: '',
    customerEmailSecond: '',
    customerAddressSecond: '',
    customerAddressLatSecond: '',
    customerAddressLngSecond: '',

    image: '',
    imageType: '',
    isImageChanged: false,

    selectedVoucherUserIdList: [],

    selectedVoucherUserId: [],
    selectedVoucherUserList: [],
    selectedVoucherUserItem: '',

    floorSection: [],
    refresh: false,
    tableSection: [],
    modal: false,
    outletId: '',
    visible: false,
    selectedTableId: false,
    customerAdd: false,
    orderDetail: false,
    selectedTableCode: null,
    orderCode: [],
    displayQr: false,
    qrData: [],
    qrTableId: null,
    tableSum: null,
    paymentOrder: null,
    total: null,
    paymentMethods: OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    isCancelled: false,
    isDelivered: false,
    promoCode: null,
    points: null,
    selectedPay: 'Credit Card',

    selectedPaymentMethodName: '',
    selectedPaymentMethodType: '',
    selectedPaymentMethodRemarks: '',
    selectedPaymentMethodBankCode: 'MBBEMYKL',
    selectedPaymentMethodLast4CardDigit: '',

    cancelUser: [],
    deliveredUser: [],
    cancelledUser: [],
    summaryTotal: 0,
    orderId: null,
    receiptInfo: null,
    selectedImage: null,
    capturedReceipt: null,
    search: '',
    cashInsert: false,
    cashAmount: '0',
    checkOutTime: null,
    tableCheckoutState: 0,
    displayQrModal: false,
    displayQModal: false,
    tableList: '',
    totalTables: 0,
    changeTable: false,
    changeTableId: '',
    joinTableId: '',
    onRemove: '',
    modalOrder: false,
    deleteTableModal: false,
    renderPaymentSummary: false,
    renderReceipt: false,
    loading: false,
    section: '',
    tableCode: '',
    capacity: 0,
    seat: 0,
    isCheckout: false,
    isPay: false,

    isAddSection: false,

    inputSectionName: '',

    inputTableCode: '',
    inputTablePax: '0',
    inputTablePaxMin: '0',
    inputTableTurnTime: 60,
    inputTableBookingPriority: RESERVATION_PRIORITY.MEDIUM,
    inputTableIsOnline: true,
    inputTableZIndex: '0',

    turnTimeOption: [
        { label: '0h 15m', value: 15 },
        { label: '0h 30m', value: 30 },
        { label: '0h 45m', value: 45 },
        { label: '1h 00m', value: 60 },
        { label: '1h 15m', value: 75 },
        { label: '1h 30m', value: 90 },
        { label: '1h 45m', value: 105 },
        { label: '2h 00m', value: 120 },
        { label: '2h 15m', value: 135 },
        { label: '2h 30m', value: 150 },
        { label: '2h 45m', value: 165 },
        { label: '3h 00m', value: 180 },
        { label: '3h 15m', value: 195 },
        { label: '3h 30m', value: 210 },
        { label: '3h 45m', value: 225 },
        { label: '4h 00m', value: 240 },
        { label: '4h 15m', value: 255 },
        { label: '4h 30m', value: 270 },
        { label: '4h 45m', value: 285 },
        { label: '5h 00m', value: 300 },
    ],
    priorityOption: [
        { label: 'Low', value: 'LOW' },
        { label: 'Medium', value: 'MEDIUM' },
        { label: 'High', value: 'HIGH' },
    ],

    moveOrderTableId: '',

    seatingPax: 0,
    viewTableOrderModal: false,
    orderDisplaySummary: true,
    orderDisplayIndividual: false,
    orderDisplayProduct: false,
    updateTableModal: false,
    joinTableModal: false,
    moveOrderModal: false,
    sectionArea: [],
    currentSectionArea: null,
    addSectionAreaModel: false,
    addTableModal: false,
    preventDeleteTableModal: false,
    seatingModal: false,
    numPad: false,
    amount: '0.00',
    diffAmount: 0,
    paidAmount: 0,

    qrDateTimeEncrypted: '',

    joinTableItems: [],

    filteredOutletTables: [],
    filteredOutletTablesForRendered: [],

    showGenericQRCode: false,

    splitAmount: 0,

    selectedVoucher: {},

    selectedOrderToPayUserList: [],
    selectedOrderToPayUserIdDict: [],
    selectedOrderToPayList: [],
    selectedOrdersCartItems: [],
    currPendingOrder: {},
    currPendingOrderTotalPrice: 0,
    currPendingOrderTotalPriceWithoutManualDisc: 0,
    currPendingOrderDiscount: 0,
    currPendingOrderTax: 0,
    currPendingOrderSc: 0,
    currPendingOrderFinalPrice: 0,
    currBillType: OFFLINE_BILL_TYPE.SUMMARY,

    //////////////////////////////////////////////////////////////////

    // 2023-05-23 - for backup purpose (to restore promotion-influenced order(s) back to original order(s))

    // currPendingOrderDiscountBak: 0,
    currPendingOrderBak: {},
    selectedOrderToPayListBak: [],
    selectedOrdersCartItemsBak: [],

    //////////////////////////////////////////////////////////////////

    selectedIndividualOrdersDict: {},
    selectedProductOrdersDict: {},
    selectedSummaryOrdersDict: {},
    selectedCancelledOrdersDict: {},
    selectedDeliveredOrdersDict: {},
    count: 0,
    deliveryCount: 0,
    cancelledCount: 0,
    userOrdersActive: [],

    voucherList: [],
    selectedVoucherId: '',

    phoneNumberChecking: false,
    currCRMUser: null,

    phoneNumber: '+60',
    loyaltyName: '',

    showLoyaltyModal: false,
    showAddLoyaltyModal: false,
    selectedRegisteredLoyalty: '',
    searchLoyalty: '',
    loyaltyAmount: '0.00',

    discountAmount: {},

    isRedeem: true,

    registeredCRMUsersDropdownList: [],
    registeredCRMUsersNoLimit: [],
    selectedRegisteredCRMUserId: '',

    registeredCRMUsersDropdownListHistory: [],

    cashbackAmount: '0.00',

    showVoucher: false,
    useCashback: false,
    useLoyaltyVoucher: false,

    useLoyaltyVoucherTableIdDict: {},

    cashbackModal: false,

    discountPromotionsTotalLCC: 0,
    qrTakeaway: false,

    promoCodePromotionDropdownList: [],
    selectedPromoCodePromotionId: '',
    selectedPromoCodePromotionIdTableIdDict: '',

    outletPaymentMethodsDropdownList: [],

    promotionIdAppliedList: [],

    discAam: 0,
    discAamList: [],
    discAamDt: Date.now(),

    selectedOrderItemList: [],
    selectedOrderItemCancelledList: [],
    selectedOrderItemDeliveredList: [],

    selectedOrderItemCheckDict: {},
    selectedOrderItemCancelledCheckDict: {},
    selectedOrderItemDeliveredCheckDict: {},

    taggableVoucherDropdownList: [],
    selectedTaggableVoucherId: '',
    selectedTaggableVoucherIdTableIdDict: {},

    appliedDiscountTotal: 0,

    discountPromotionsTotal: 0,

    qrCodeTableRef: null,
    qrCodeTakeawayRef: null,

    isSavingQRCode: false,

    splitQuantity: 1,

    //switch table layout
    switchTableLayout: false,

    forceCloseShiftBeforeSignOut: false,

    receiptEmailToSent: '',

    isLeaveTablePaymentSummary: false,

    currToPaidUser: null,

    razerPaymentMethods: [],

    isHideReject: false,

    /////////////////////////

    // 2025-03-18 - additions

    // isMounted: true,
    // customerIDNum: '',
    // customerTIN: '',
    // customerIdType: '',
    // customerEINStreet: '',
    // customerEINCity: '',
    // customerEINState: '',
    // customerEINPostcode: '',
    // isEdit: '',
    // eInvoiceInfo: '',

    /////////////////////////

    toast: '',

    tablePaymentSummaryVoucherToggleLoading: false,
});