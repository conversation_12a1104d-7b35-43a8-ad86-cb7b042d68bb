import React, { Component } from 'react'
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'

class ResetPasswordScreen extends Component {
    // constructor({ navigation, props }) {
    constructor(props) {
        console.log('reset props');
        console.log(props);

        super(props)
        this.state = {
            email: "",
            success: false,
        }
    }

    componentDidMount() {
    }

    sendEmail() {
        var body = {
            email: this.state.email
        }
        ApiClient.POST(API.userResetPassword, body).then(result => {
            // if (result == true) {
            //     this.setState({ success: false });

            //     Alert.alert('Success', 'Password reset email had sent to your inbox.');
            // }

            if (result == true) {
                Alert.alert(
                    'Success',
                    'Temporary password reset link has been sent to your email',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                this.setState({ success: false });

                                this.props.goToLoginState();
                            },
                        },
                    ],
                    { cancelable: false },
                );
            }
            else {
                Alert.alert(
                    'Error',
                    'Unable to send the reset password email',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                this.setState({ success: false });
                            },
                        },
                    ],
                    { cancelable: false },
                );
            }
        }).catch(err => { console.log(err) })
    }

    render() {
        return (
            <View style={styles.container}>
                <View style={{ paddingHorizontal: 30, flex: 1, justifyContent: "center" }}>
                    <View style={{ alignSelf: 'center', width: '100%' }}>
                        <Image style={styles.logo} resizeMode="contain" source={require('../assets/image/logo_2.png')} />
                        <Text style={styles.description}>Please provide your email, we will send a reset link to your email.</Text>
                        {this.state.success == false ? <TextInput
                            editable={!this.state.loading}
                            underlineColorAndroid={Colors.fieldtBgColor}
                            clearButtonMode='while-editing'
                            style={styles.textInput}
                            placeholder="Email"
                            keyboardType='email-address'
                            autoCapitalize="none"
                            autoCompleteType="off"
                            onChangeText={(text) => { this.setState({ email: text.trim() }) }}
                            value={this.state.email}
                        /> : <Text style={styles.description}>A reset link has been sent to your email.</Text>}

                        {this.state.success == false ? <TouchableOpacity disabled={this.state.loading} onPress={() => { this.sendEmail() }}>
                            <View style={[Styles.button, { marginTop: 50 }]}>
                                <Text style={{ color: '#ffffff', fontSize: 18 }}>{this.state.loading ? "LOADING..." : "SEND"}</Text>
                            </View>
                        </TouchableOpacity> : <Text> </Text>}
                        <TouchableOpacity disabled={this.state.loading} onPress={() => { this.props.goToLoginState() }}>
                            <View style={styles.resetContainer}>
                                <Text style={{ color: Colors.primaryColor }}>Back To Login Screen</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row'
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logo: {
        width: 300,
        height: 80,
        alignSelf: 'center',
        marginTop: 10,
        paddingBottom: 10,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
    },
    loginTxt: {
        color: Colors.mainTxtColor,
        fontWeight: "600",
        fontSize: 30
    },
    description: {
        color: Colors.descriptionColor,
        paddingVertical: 20,
        justifyContent: 'center',
        alignSelf: 'center'
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 20,
    },
    checkBox: {
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: Colors.descriptionColor,
        width: 30,
        height: 30,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    floatbtn: {
        zIndex: 1,
        position: 'absolute',
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3
    },
    loginImg: {
        width: undefined,
        height: '100%',
        resizeMode: 'cover'
    },
    resetContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 25,
        alignSelf: 'center'
    }
})
export default ResetPasswordScreen