diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/.DS_Store b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/.DS_Store
new file mode 100644
index 0000000..3ee370c
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/.DS_Store differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/.DS_Store b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/.DS_Store
new file mode 100644
index 0000000..00abcfc
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/.DS_Store differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/.DS_Store b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/.DS_Store
new file mode 100644
index 0000000..9d40f5c
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/.DS_Store differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/.DS_Store b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/.DS_Store
new file mode 100644
index 0000000..f2a28bb
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/.DS_Store differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/GSDK.framework/.DS_Store b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/GSDK.framework/.DS_Store
new file mode 100644
index 0000000..a7f0adc
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/GPrinterLegacy/GSDK/GSDK.framework/.DS_Store differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/react-native-thermal-receipt-printer-image-qr.podspec b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/react-native-thermal-receipt-printer-image-qr.podspec
index 4108e86..0ce1b5f 100644
--- a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/react-native-thermal-receipt-printer-image-qr.podspec
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/react-native-thermal-receipt-printer-image-qr.podspec
@@ -47,8 +47,10 @@ Pod::Spec.new do |s|
   # ]
 
   # Header mappings
-  s.header_mappings_dir = "ios/GPrinterLegacy/GSDK/GSDK.framework/Headers"
-  s.public_header_files = "ios/GPrinterLegacy/GSDK/GSDK.framework/Headers/*.h"
+  # s.header_mappings_dir = "ios/GPrinterLegacy/GSDK/GSDK.framework/Headers"
+  # s.requires_arc = true
+  s.header_mappings_dir = "ios"
+  s.public_header_files = ["ios/GPrinterLegacy/GSDK/GSDK.framework/Headers/*.h", "ios/**/*.h"]
 
   s.static_framework = true
   
