!function(e){"object"==typeof module&&"undefined"!=typeof module.exports?module.exports=e:e()}((function(){(window.webpackJsonpFusionCharts=window.webpackJsonpFusionCharts||[]).push([[7],{1171:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=void 0;var o=i(a(1172));t.Gantt=o["default"];var n={name:"gantt",type:"package",requiresFusionCharts:!0,extension:function(e){return e.addDep(o["default"])}};t["default"]=n},1172:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=void 0;var o=i(a(1173))["default"];t["default"]=o},1173:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=void 0;var o,n=i(a(229)),r=i(a(547)),s=i(a(1174)),l=i(a(1176)),c=i(a(1182)),h=i(a(1183)),d=i(a(1186)),p=i(a(1188)),g=a(331),u=a(414),m=a(215),f=Math.max,v=Math.min,b=(o=[],{get:function(){return o},clear:function(){o.length=0},add:function(e,t){o.push({conf:e,handler:t})}});m.setAttribDefs&&(0,m.setAttribDefs)({showpercentlabel:{type:m.attrTypeBool,pAttr:"showpercentlabel"},fontsize:{type:m.attrTypeNum},alpha:{type:m.attrTypeNum},showborder:{type:m.attrTypeBool},borderthickness:{type:m.attrTypeNum},borderalpha:{type:m.attrTypeNum},showHoverEffect:{type:m.attrTypeNum},hoverFillAlpha:{type:m.attrTypeNum},slackHoverFillColor:{type:m.attrTypeNum},slackHoverFillAlpha:{type:m.attrTypeBool},showlabels:{type:m.attrTypeBool,pAttr:"showtasknames"},slackfillcolor:{pAttr:"slackfillcolor"},showtasklabels:{type:m.attrTypeBool,pAttr:"showtasknames"},showtasknames:{type:m.attrTypeBool,pAttr:"showlabels"},showconnectorhovereffect:{type:m.attrTypeNum,pAttr:"showhovereffect"},connectorextension:{type:m.attrTypeNum},tasklabelspadding:{type:m.attrTypeNum},taskdatepadding:{type:m.attrTypeNum},showstartdate:{type:m.attrTypeNum,pAttr:"showtaskstartdate"},showenddate:{type:m.attrTypeNum,pAttr:"showtaskenddate"},showtaskhovereffect:{type:m.attrTypeNum,pAttr:"showhovereffect"},useverticalscrolling:{type:m.attrTypeNum},taskbarroundradius:{type:m.attrTypeNum},showshadow:{type:m.attrTypeNum},showslackasfill:{type:m.attrTypeNum}});var x=function(e){function t(){var t;return(t=e.call(this)||this)._scrollBar=b,t.components={},t.fireGroupEvent=!0,t.hasInteractiveLegend=!1,t.defaultPaletteOptions=(0,m.extend2)((0,m.extend2)({},m.defaultGaugePaletteOptions),{paletteColors:[["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"],["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"],["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"],["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"],["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"]],bgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],bgAngle:[270,270,270,270,270],bgRatio:["100","100","100","100","100"],bgAlpha:["100","100","100","100","100"],canvasBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],canvasBgAngle:[0,0,0,0,0],canvasBgAlpha:["100","100","100","100","100"],canvasBgRatio:["","","","",""],canvasBorderColor:["545454","545454","415D6F","845001","68001B"],canvasBorderAlpha:[100,100,100,90,100],gridColor:["DDDDDD","D8DCC5","99C4CD","DEC49C","FEC1D0"],gridResizeBarColor:["999999","545454","415D6F","845001","D55979"],categoryBgColor:["F1F1F1","EEF0E6","F2F8F9","F7F0E6","FFF4F8"],dataTableBgColor:["F1F1F1","EEF0E6","F2F8F9","F7F0E6","FFF4F8"],toolTipBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],toolTipBorderColor:["545454","545454","415D6F","845001","68001B"],baseFontColor:["555555","60634E","025B6A","A15E01","68001B"],borderColor:["767575","545454","415D6F","845001","68001B"],borderAlpha:[50,50,50,50,50],legendBgColor:["ffffff","ffffff","ffffff","ffffff","ffffff"],legendBorderColor:["666666","545454","415D6F","845001","D55979"],plotBorderColor:["999999","8A8A8A","6BA9B6","C1934D","FC819F"],plotFillColor:["EEEEEE","D8DCC5","BCD8DE","E9D8BE","FEDAE3"],scrollBarColor:["EEEEEE","D8DCC5","99C4CD","DEC49C","FEC1D0"]}),t.registerFactory("canvas",s["default"]),t.registerFactory("axis",l["default"],["canvas"]),t.registerFactory("dataset",p["default"],["canvas"]),t}(0,n["default"])(t,e),t.getName=function(){return"Gantt"};var a=t.prototype;return a.draw=function(){e.prototype.draw.call(this),this.createScrollbarContainer()},a.getName=function(){return"Gantt"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.alignCaptionWithCanvas=0,t.defaultDatasetType="task",t.taskbarroundradius=0,t.taskbarfillmix=" { light-10 }, { dark-20 }, { light-50 }, { light-85 }",t.taskbarfillratio="0,8,84,8",t.showslackasfill=1,t.dateintooltip=1,t.tasklabelsalign=m.POSITION_CENTER,t.datepadding=3,t.showtasknames=0,t.showpercentlabel=!1,t.showhovereffect=1,t.slackfillcolor="FF5E5E",t.connectorextension=10,t.tasklabelspadding=2,t.taskdatepadding=3,t.showlabels=void 0,t.showtooltip=1,t.showtaskhovereffect=void 0,t.useverticalscrolling=1,t.ganttpanedurationunit=void 0,t.ganttpaneduration=void 0,t.showtaskstartdate=void 0,t.showtaskenddate=void 0,t.ganttwidthpercent=void 0,t.showshadow=1,t.taskhoverfillalpha=m.HUNDREDSTRING,t.enablemousetracking=!1},a._checkInvalidSpecificData=function(){var e=this.getFromEnv("dataSource"),t=e.processes,a=t&&t.process,i=a&&a.length,o=e.tasks,n=o&&o.task||o,r=n&&n.length,s=e.categories,l=s&&s[0]&&s[0].category,c=l&&l.length;return!i||!r||!c},a.parseChartAttr=function(t){var a=this.config,i=t&&t.chart||this.getFromEnv("chart-attrib");e.prototype.parseChartAttr.call(this,t),a.showBorder=(0,m.pluckNumber)(i.showborder,0),a.alignLegendWithCanvas=0,a.taskhoverfillalpha=t.chart.taskhoverfillalpha},a._feedAxesRawData=function(){var e=this.getFromEnv("color-manager"),t=this.getFromEnv("dataSource"),a=t.chart,i=this.is3d,o=i?m.chartPaletteStr.chart3D:m.chartPaletteStr.chart2D;return{xAxisConf:[{isVertical:!1,isReverse:!1,isOpposit:!0,outCanfontFamily:(0,m.pluck)(a.outcnvbasefont,a.basefont,"Verdana,sans"),outCanfontSize:(0,m.pluckFontSize)(a.outcnvbasefontsize,a.basefontsize,10),outCancolor:(0,m.pluck)(a.outcnvbasefontcolor,a.basefontcolor,e.getColor(o.baseFontColor)).replace(/^#? ([a-f0-9]+)/gi,"#$1"),axisBreaks:a.xaxisbreaks,axisNamePadding:a.xaxisnamepadding,axisValuePadding:a.labelpadding,axisNameFont:a.xaxisnamefont,axisNameFontSize:a.xaxisnamefontsize,axisNameFontColor:a.xaxisnamefontcolor,axisNameFontBold:a.xaxisnamefontbold,axisNameFontItalic:a.xaxisnamefontitalic,axisNameBgColor:a.xaxisnamebgcolor,axisNameBorderColor:a.xaxisnamebordercolor,axisNameAlpha:a.xaxisnamealpha,axisNameFontAlpha:a.xaxisnamefontalpha,axisNameBgAlpha:a.xaxisnamebgalpha,axisNameBorderAlpha:a.xaxisnameborderalpha,axisNameBorderPadding:a.xaxisnameborderpadding,axisNameBorderRadius:a.xaxisnameborderradius,axisNameBorderThickness:a.xaxisnameborderthickness,axisNameBorderDashed:a.xaxisnameborderdashed,axisNameBorderDashLen:a.xaxisnameborderdashlen,axisNameBorderDashGap:a.xaxisnameborderdashgap,useEllipsesWhenOverflow:a.useellipseswhenoverflow,divLineColor:(0,m.pluck)(a.vdivlinecolor,e.getColor(o.divLineColor)),divLineAlpha:(0,m.pluck)(a.vdivlinealpha,i?e.getColor("divLineAlpha3D"):e.getColor("divLineAlpha")),divLineThickness:(0,m.pluckNumber)(a.vdivlinethickness,1),divLineIsDashed:Boolean((0,m.pluckNumber)(a.vdivlinedashed,a.vdivlineisdashed,0)),divLineDashLen:(0,m.pluckNumber)(a.vdivlinedashlen,4),divLineDashGap:(0,m.pluckNumber)(a.vdivlinedashgap,2),showAlternateGridColor:(0,m.pluckNumber)(a.showalternatevgridcolor,0),alternateGridColor:(0,m.pluck)(a.alternatevgridcolor,e.getColor("altVGridColor")),alternateGridAlpha:(0,m.pluck)(a.alternatevgridalpha,e.getColor("altVGridAlpha")),numDivLines:(0,m.pluckNumber)(a.numvdivlines,this.config.numVDivLines),labelFont:a.labelfont,labelFontSize:a.labelfontsize,labelFontColor:a.labelfontcolor,labelFontAlpha:a.labelalpha,labelFontBold:a.labelfontbold,labelFontItalic:a.labelfontitalic,maxLabelHeight:a.maxlabelheight,axisName:a.xaxisname,axisMinValue:a.xaxisminvalue,axisMaxValue:a.xaxismaxvalue,setAdaptiveMin:a.setadaptivexmin,adjustDiv:a.adjustvdiv,labelDisplay:a.labeldisplay,showLabels:a.showlabels,rotateLabels:a.rotatelabels,slantLabel:(0,m.pluckNumber)(a.slantlabels,a.slantlabel),labelStep:(0,m.pluckNumber)(a.labelstep,a.xaxisvaluesstep),showAxisValues:(0,m.pluckNumber)(a.showxaxisvalues,a.showxaxisvalue),showLimits:(0,m.pluckNumber)(a.showvlimits,this.config.showvlimits),showDivLineValues:(0,m.pluckNumber)(a.showvdivlinevalues,a.showvdivlinevalues),zeroPlaneColor:a.vzeroplanecolor,zeroPlaneThickness:a.vzeroplanethickness||2,zeroPlaneAlpha:a.vzeroplanealpha,showZeroPlaneValue:a.showvzeroplanevalue,vTrendLines:t.trendlines,trendlineColor:a.trendlinecolor,trendlineToolText:a.trendlinetooltext,trendlineThickness:a.trendlinethickness,trendlineAlpha:a.trendlinealpha,showTrendlinesOnTop:a.showtrendlinesontop,showAxisLine:(0,m.pluckNumber)(a.showxaxisline,a.showaxislines,a.drawAxisLines,0),axisLineThickness:(0,m.pluckNumber)(a.xaxislinethickness,a.axislinethickness,1),axisLineAlpha:(0,m.pluckNumber)(a.xaxislinealpha,a.axislinealpha,100),axisLineColor:(0,m.pluck)(a.xaxislinecolor,a.axislinecolor,"#000000")}],yAxisConf:[{isVertical:!0,isReverse:!0,isOpposit:!1,outCanfontFamily:(0,m.pluck)(a.outcnvbasefont,a.basefont,"Verdana,sans"),outCanfontSize:(0,m.pluckFontSize)(a.outcnvbasefontsize,a.basefontsize,10),outCancolor:(0,m.pluck)(a.outcnvbasefontcolor,a.basefontcolor,e.getColor(o.baseFontColor)).replace(/^#? ([a-f0-9]+)/gi,"#$1"),axisBreaks:a.yaxisbreaks,axisNamePadding:a.yaxisnamepadding,axisValuePadding:a.yaxisvaluespadding,axisNameFont:a.yaxisnamefont,axisNameFontSize:a.yaxisnamefontsize,axisNameFontColor:a.yaxisnamefontcolor,axisNameFontBold:a.yaxisnamefontbold,axisNameFontItalic:a.yaxisnamefontitalic,axisNameBgColor:a.yaxisnamebgcolor,axisNameBorderColor:a.yaxisnamebordercolor,axisNameAlpha:a.yaxisnamealpha,axisNameFontAlpha:a.yaxisnamefontalpha,axisNameBgAlpha:a.yaxisnamebgalpha,axisNameBorderAlpha:a.yaxisnameborderalpha,axisNameBorderPadding:a.yaxisnameborderpadding,axisNameBorderRadius:a.yaxisnameborderradius,axisNameBorderThickness:a.yaxisnameborderthickness,axisNameBorderDashed:a.yaxisnameborderdashed,axisNameBorderDashLen:a.yaxisnameborderdashlen,axisNameBorderDashGap:a.yaxisnameborderdashgap,axisNameWidth:a.yaxisnamewidth,useEllipsesWhenOverflow:a.useellipseswhenoverflow,rotateAxisName:(0,m.pluckNumber)(a.rotateyaxisname,1),axisName:a.yaxisname,divLineColor:(0,m.pluck)(a.divlinecolor,e.getColor(o.divLineColor)),divLineAlpha:(0,m.pluck)(a.divlinealpha,i?e.getColor("divLineAlpha3D"):e.getColor("divLineAlpha")),divLineThickness:(0,m.pluckNumber)(a.divlinethickness,1),divLineIsDashed:Boolean((0,m.pluckNumber)(a.divlinedashed,a.divlineisdashed,0)),divLineDashLen:(0,m.pluckNumber)(a.divlinedashlen,4),divLineDashGap:(0,m.pluckNumber)(a.divlinedashgap,2),showAlternateGridColor:(0,m.pluckNumber)(a.showalternatehgridcolor,1),alternateGridColor:(0,m.pluck)(a.alternatehgridcolor,e.getColor("altHGridColor")),alternateGridAlpha:(0,m.pluck)(a.alternatehgridalpha,e.getColor("altHGridAlpha")),numDivLines:(0,m.pluckNumber)(a.numdivlines,this.numDivLines),axisMinValue:a.yaxisminvalue,axisMaxValue:a.yaxismaxvalue,setAdaptiveMin:a.setadaptiveymin,adjustDiv:a.adjustdiv,labelStep:a.yaxisvaluesstep,showAxisValues:(0,m.pluckNumber)(a.showyaxisvalues,a.showyaxisvalue),showLimits:(0,m.pluckNumber)(a.showlimits,this.showLimits),showDivLineValues:(0,m.pluckNumber)(a.showdivlinevalues,a.showdivlinevalue),zeroPlaneColor:a.zeroplanecolor,zeroPlaneThickness:a.zeroplanethickness||2,zeroPlaneAlpha:a.zeroplanealpha,showZeroPlaneValue:a.showzeroplanevalue,trendlineColor:a.trendlinecolor,trendlineToolText:a.trendlinetooltext,trendlineThickness:a.trendlinethickness,trendlineAlpha:a.trendlinealpha,showTrendlinesOnTop:a.showtrendlinesontop,showAxisLine:(0,m.pluckNumber)(a.showyaxisline,a.showaxislines,a.drawAxisLines,0),axisLineThickness:(0,m.pluckNumber)(a.yaxislinethickness,a.axislinethickness,1),axisLineAlpha:(0,m.pluckNumber)(a.yaxislinealpha,a.axislinealpha,100),axisLineColor:(0,m.pluck)(a.yaxislinecolor,a.axislinecolor,"#000000")}]}},a._resetViewPortConfig=function(){this.config.viewPortConfig={scaleX:1,scaleY:1,x:0,y:0}},a._setCategories=function(){var e=this.getFromEnv("dataSource"),t=this.getChildren("xAxis")[0],a=this.getChildren("yAxis")[0],i=e.categories,o=e.datatable,n=e.processes;a.setAxisPadding(.5,.5),a.setProcess(n),a.setDataTable(o),t.setCategory(i)},a.getDSdef=function(e){switch(e){case"milestone":return c["default"];case"connector":return d["default"];case"task":default:return h["default"]}},a._createLegendItems=function(){var e,t,a,i,o,n,r,s,l,c,h=this.getFromEnv("dataSource"),d=this.getFromEnv("legend"),p=d.getChildren("legendItem"),g=h.legend&&h.legend.item||[];if(!this.config.legendItemIds&&(this.config.legendItemIds=[]),(s=p&&p.length||0)>(l=g.length)){for(t=l;t<s;t++)d.disposeItem(p[t].getId());p&&p.splice(l,s-l)}for(t=0,e=g.length;t<e;t++)n=g[t],d.getItem(this.config.legendItemIds[t])?c=this.config.legendItemIds[t]:(c=d.createItem(),this.config.legendItemIds.push(c)),i=n.color,a=(0,m.getLightColor)(i,60).replace(m.dropHash,"#"),o={FCcolor:{color:i+","+i+","+(0,m.getLightColor)(i,40)+","+i+","+i,ratio:"0,70,30",angle:270,alpha:"100,100,100,100,100"}},r={label:n.label,interactiveLegend:!1},d.getItem(c).configure(r),d.getItem(c).setStateCosmetics("default",{symbol:{fill:(0,m.toRaphaelColor)(o),stroke:(0,m.toRaphaelColor)(a)}})},a._spaceManager=function(){var e,t,a,i,o,n,r=this.config,s=this.getChildren("xAxis")[0],l=this.getChildren("yAxis")[0],c=this.getChildren("legend")[0],h=100-(r.ganttwidthpercent||67),d=r.borderWidth,p=0,g=0;this._resetViewPortConfig(),this._allocateSpace({top:d,bottom:d,left:d,right:d}),this._allocateSpace(this._manageActionBarSpace&&this._manageActionBarSpace(.225*r.availableHeight)||{}),c&&(a=c.config.legendPos?c.config.legendPos.split("-"):[]),e=a[0]===m.POSITION_BOTTOM||a[0]===m.POSITION_TOP?.6*r.canvasHeight:.6*r.canvasWidth,this._manageChartMenuBar(e),i="right"===a[0]||"left"===a[0]?.3*r.canvasWidth:.3*r.canvasHeight,r.showLegend&&this._manageLegendSpace(i),r.actualCanvasTop=r.canvasTop,r.actualCanvasLeft=r.canvasLeft,t=r.canvasWidth*(h/100),p+=((o=l.placeAxis(t)).left||0)+(o.right||0),l&&this._allocateSpace(o),e=a[0]===m.POSITION_BOTTOM||a[0]===m.POSITION_TOP?.6*r.canvasHeight:.6*r.canvasWidth,e=.6*r.canvasHeight,g+=(o=s.placeAxis(e)).top||0,r.totalWidth=p,r.totalHeight=g,s&&this._allocateSpace(o),n=l&&l.setProcessHeight(),l.setAxisConfig({processTotalHeight:n})},a._postSpaceManagement=function(){var e,t,a,i,o,n=this.config,r=this.getChildren("xAxis")[0],s=this.getChildren("yAxis")[0],l=r.getLimit(),c=s.getAxisConfig("processTotalHeight"),h=n.canvasHeight,d=this.getChildren("legend")[0],p=n.xDepth,g=this.getChildren("canvas")[0].config,m=g.canvasBorderWidth,f=g.canvasPadding,v=this.getChildren("vScrollBar")&&this.getChildren("vScrollBar")[0],b=v&&v.config,x=g.canvasPaddingLeft,C=g.canvasPaddingRight,k=Number(n.ganttpaneduration),w=n.ganttpanedurationunit,L=this.getChildren("hProcessScrollBar")&&this.getChildren("hProcessScrollBar")[0],y=this.getChildren("hScrollBar")&&this.getChildren("hScrollBar")[0],A=y&&y.config.height||0,F=L&&L.config.height||0;n.hScrollEnabled=a=!isNaN(k)&&void 0!==w&&function(e,t,a,i){if(e>0)switch(t){case"y":return+u.timeYear.offset(a,e)<i;case"m":return+u.timeMonth.offset(a,e)<i;case"d":return+u.timeDay.offset(a,e)<i;case"h":return+u.timeHour.offset(a,e)<i;case"mn":return+u.timeMinute.offset(a,e)<i;case"s":return+u.timeSecond.offset(a,e)<i}}(k,w,l.min,l.max),i=s.getAxisConfig("totalWidth")>s.getAxisConfig("totalVisiblelWidth"),c>h-Math.max(i?F:0,a?A:0)&&(t=!0),e=t&&b.width||0,o=n.canvasWidth-(p||0)-Math.max(x,f)-Math.max(C,f)-e,r&&r.setAxisDimention({x:g.canvasLeft+(p||0)+Math.max(x,f),y:g.canvasTop-(n.shift||0),opposite:g.canvasTop+g.canvasHeight+m,axisLength:o}),n.currentCanvasWidth=o,s&&s.setAxisDimention({x:g.canvasLeft-(n.shift||0),y:g.canvasTop,opposite:g.canvasRight+m,axisLength:n.canvasHeight}),this._manageScrollbarPosition(),n.showLegend&&d.postSpaceManager(),this.setScrollDimensions(),this.allocateDimensionOfChartMenuBar()},a.createScrollbarContainer=function(){var e=this.getChildContainer(),t=this.getContainer(),a=t.scrollBarParentGroup,i=this.getFromEnv("animationManager");a||(a=t.scrollBarParentGroup=i.setAnimation({el:"group",attr:{name:"scrollBarParentGroup"},container:t.parentgroup,component:this}),this.config.scrollbarContainer=a,a.insertBefore(e.datalabelsGroup))},a.setScrollDimensions=function(){var e,t,a,i,o,n,r,s,l,c,h,d,p,g,u,m,f=this.config,v=this.getChildren(),b=v.xAxis[0],x=b.config.axisRange,C=f.viewPortConfig,k=f.scrollOptions||(f.scrollOptions={}),w=x.max,L=x.min,y=k.horizontalVxLength,A=this.getChildren("hScrollBar")[0],F=A.getChildren("scrollAnchor")[0].config,T=this.getChildren("vScrollBar")[0],P=this.getChildren("hProcessScrollBar")[0],N=f.useverticalscrolling,E=w-L,S=f.canvasRight,D=C.scaleX,B=C.scaleY,V=f.vScrollEnabled,_=v.yAxis[0],H=V?T.config.conf.width:0,I=this.getContainer();i=f.canvasLeft,o=f.canvasTop,n=f.canvasHeight,r=f.canvasWidth,s=v.canvas[0].config,k.viewPortMin=L,k.viewPortMax=w,k.scrollRatio=y/E,d=k.windowedCanvasWidth=b.getPixel(y),p=k.fullCanvasWidth=b.getPixel(w-L)-d,h=k.fullCanvasHeight=_.getAxisConfig("processTotalHeight"),c=k.windowedCanvasHeight=n,g=_.getAxisConfig("totalWidth"),u=_.getAxisConfig("totalVisiblelWidth"),l=1/B,m=I.scrollBarParentGroup,!1!==f.hScrollEnabled&&(e=A.config,A.setDimension({x:i,y:o+n,width:r-H}),e.scrollRatio=1/D,e.scrollPosition=F.scrollPosition=C.x*D/(r*(D-1)),e.roundEdges=s.isRoundEdges,e.fullCanvasWidth=p,e.windowedCanvasWidth=d,e.parentLayer=m),!1!==V&&N&&(t=T.config,T.setDimension({x:S-H,y:o,height:n}),t.scrollRatio=l,t.roundEdges=s.isRoundEdges,t.fullCanvasWidth=h,t.windowedCanvasWidth=c,t.parentLayer=m),u<g&&u>0&&(a=P.config,P.setDimension({x:i-u,y:o+n,width:u}),a.scrollRatio=u/g,a.roundEdges=s.isRoundEdges,a.fullCanvasWidth=p,a.windowedCanvasWidth=d,a.parentLayer=m)},a._setAxisScale=function(){var e,t,a,i,o,n=this.getChildren(),r=this.config,s=n.xAxis[0],l=s.getLimit(),c=l.max,h=l.min,d=new Date(h),p=n.yAxis[0],g=this.getFromEnv("number-formatter"),u=Number(r.ganttpaneduration),m=r.scrollOptions||(r.scrollOptions={}),b=r.ganttpanedurationunit,x=c-h,C=r.canvasHeight,k=r.scrolltodate,w=r.canvasWidth,L=r.canvasLeft,y=this.getChildren("hProcessScrollBar")[0],A=this.getChildren("hScrollBar")[0],F=this.getChildren("vScrollBar")[0],T=A&&A.config&&A.config.height||0,P=y&&y.config&&y.config.height||0,N=s.getPixel(c)-L,E=p.getAxisConfig("processTotalHeight");if(r.hScrollEnabled){switch(b){case"y":d.setFullYear(d.getFullYear()+u);break;case"m":d.setMonth(d.getMonth()+u);break;case"d":d.setDate(d.getDate()+u);break;case"h":d.setHours(d.getHours()+u);break;case"mn":d.setMinutes(d.getMinutes()+u);break;default:d.setSeconds(d.getSeconds()+u)}d=d.getTime(),o=s.getPixel(d)-L,r.hScrollEnabled=!0,r.viewPortConfig.scaleX=e=N/o,m.horizontalVxLength=x/void 0*u,k&&(t=g.getDateValue(k).ms,a=s.getPixel(t),r.viewPortConfig.x=v(a-L,w*(e-1))/e),r.scrollPos>=0&&(r.xOffset=r.currentCanvasWidth*(e-1)*r.scrollPos,r.viewPortConfig.x=r.xOffset/e)}else A.remove();p.getAxisConfig("totalWidth")>p.getAxisConfig("totalVisiblelWidth")?r.hProcessScrollEnabled=!0:(r.hProcessScrollEnabled=!1,y.remove()),i=C-f(r.hProcessScrollEnabled?P:0,r.hScrollEnabled?T:0),Math.floor(E)>i&&r.useverticalscrolling?(r.viewPortConfig.scaleY=E/i,r.vScrollEnabled=!0):(r.vScrollEnabled=!1,F.remove())},a._createToolBoxGantt=function(){var e,t,a,i,o,n=this,r=n.getChildren("hScrollBar")&&n.getChildren("hScrollBar")[0],s=n.getChildren("vScrollBar")&&n.getChildren("vScrollBar")[0],l=n.getChildren("hProcessScrollBar")&&n.getChildren("hProcessScrollBar")[0],c=n.getChildren(),h=c.yAxis[0],d=n._scrollBar,p=c.chartMenuBar||{},u=c.actionBar,m=d.get,f=d.add;p.drawn||u&&u.drawn||(d.clear(),f({isHorizontal:!0,scale:1,scrollPosition:0},{scroll:(o=n,function(){o.updateManagerH(arguments[0],!0)}),scrollStart:function(t){e=t,n.fireChartInstanceEvent("scrollstart",{scrollPosition:t})},scrollEnd:function(t){n.fireChartInstanceEvent("scrollend",{scrollPosition:t,prevScrollPosition:e})}}),f({isHorizontal:!1,scale:1,scrollPosition:0},{scroll:function(e,t){return function(){e.updateManagerV(arguments[0],!1)}}(n),scrollStart:function(t){e=t,n.fireChartInstanceEvent("scrollstart",{scrollPosition:t})},scrollEnd:function(t){n.fireChartInstanceEvent("scrollend",{scrollPosition:t,prevScrollPosition:e})}}),f({isHorizontal:!0,scale:1,scrollPosition:0},{scroll:function(){h.manageProcessScroll(arguments[0])},scrollStart:function(t){e=t,n.fireChartInstanceEvent("scrollstart",{scrollPosition:t})},scrollEnd:function(t){n.fireChartInstanceEvent("scrollend",{scrollPosition:t,prevScrollPosition:e})}}),t=m()[0],i=m()[1],a=m()[2],r||(r=n.attachChild(new g.ScrollBar,"hScrollBar")),r.configure(t.conf),r.attachEventHandlers(t.handler),s||(s=n.attachChild(new g.ScrollBar,"vScrollBar")),s.configure(i.conf),s.attachEventHandlers(i.handler),l||(l=n.attachChild(new g.ScrollBar,"hProcessScrollBar")),l.configure(a.conf),l.attachEventHandlers(a.handler))},a._manageScrollbarPosition=function(){var e,t,a,i=this.config,o=this.getChildren("hScrollBar")[0],n=this.getChildren("vScrollBar")[0],r=this.getChildren("hProcessScrollBar")[0],s=i.totalWidth||0,l=i.totalHeight||0;this._setAxisScale&&this._setAxisScale(),o=this.getChildren("hScrollBar")[0],n=this.getChildren("vScrollBar")[0],r=this.getChildren("hProcessScrollBar")[0],t=i.vScrollEnabled,e=i.hScrollEnabled,a=o.getLogicalSpace(),i.hScrollHeight=!1===e?0:a.height+n.config.padding,a=n.getLogicalSpace(),i.vScrollWidth=!1!==t?a.width+n.config.conf.padding:0,a=r.getLogicalSpace(),i.hProcessScrollHeight=i.hProcessScrollEnabled?a.height+r.config.padding:0,l+=f(i.hProcessScrollHeight,i.hScrollHeight),this._allocateSpace({bottom:f(i.hProcessScrollHeight,i.hScrollHeight)}),i.totalWidth=s,i.totalHeight=l},a.updateManagerH=function(e){var t,a=this.config,i=a.lastXpos||(a.lastXpos={x:0,y:0}),o=this.config.viewPortConfig,n=this.getFromEnv("animationManager"),r=o.scaleX,s=this.getChildren().xAxis[0],l=this.getChildContainer(),c=l.plotGroup,h=this.getChildContainer("datalabelsGroup"),d=l.datalabelsGroup,p=l.trackerGroup,g=a.xOffset,u=a.yOffset||0,m=a.currentCanvasWidth,f=s.getContainer("ganttPlotLineContainer");a.scrollPos=e,g=a.xOffset=m*(r-1)*e,o.x=g/r,s.translateAxis(-g,void 0),i.x=-g,t="t"+-g+", "+-u,n.setAnimation({el:c,attr:{transform:t},component:this}),n.setAnimation({el:d,attr:{transform:t},component:this}),n.setAnimation({el:p,attr:{transform:t},component:this}),n.setAnimation({el:h,attr:{transform:t},component:this}),n.setAnimation({el:f,attr:{transform:"t"+-g+", 0"},component:this})},a.updateManagerV=function(e){var t,a=this.config,i=this.getFromEnv("animationManager"),o=a.xOffset,n=this.config.viewPortConfig,r=this.getChildren().yAxis[0],s=a.yOffset,l=a.canvasHeight,c=n.scaleY,h=this.getChildContainer("datalabelsGroup"),d=r.getContainer("labelContainer"),p=this.getChildContainer("plotGroup"),g=r.getContainer("ganttPlotHoverBandContainer"),u=r.getContainer("ganttPlotLineContainer");s=a.yOffset=l*(c-1)*e,n.y=s/c,t=r.config.lastTranslate||(r.config.lastTranslate={x:0,y:0}),i.setAnimation({el:d,attr:{transform:"t"+t.x+", "+-s},component:this}),i.setAnimation({el:g,attr:{transform:"t0, "+-s},component:this}),i.setAnimation({el:p,attr:{transform:"t"+-o+", "+-s},component:this}),i.setAnimation({el:h,attr:{transform:"t"+-o+", "+-s},component:this}),i.setAnimation({el:u,attr:{transform:"t0, "+-s},component:this})},t}(r["default"]);t["default"]=x},1174:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=function(e){var t;(0,n.componentFactory)(e,o["default"],"canvas",1),t=e.getChildren("canvas");for(var a=0,i=t.length;a<i;a++)t[a].configure()};var o=i(a(1175)),n=a(215)},1175:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=void 0;var o=i(a(229)),n=i(a(449)),r=a(215),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.createGroup=function(){e.prototype.createGroup.call(this);var t,a=this.getFromEnv("chart"),i=a.config,o=i.style.inCanvasStyle,n=this.getFromEnv("animationManager"),s=a.getChildContainer("plotGroup");!this.getChildContainer("taskGroup")&&this.addChildContainer("taskGroup",n.setAnimation({el:"group",attr:{name:"task"},container:s,component:this})),!this.getChildContainer("connectorGroup")&&this.addChildContainer("connectorGroup",n.setAnimation({el:"group",attr:{name:"connector"},container:s,component:this})),!this.getChildContainer("milestoneGroup")&&this.addChildContainer("milestoneGroup",n.setAnimation({el:"group",attr:{name:"milestone"},container:s,component:this})),t=i.milestoneLabelStyle={fontSize:(0,r.pluckNumber)(i.milestonefontsize,o.fontSize)+"px",fontFamily:(0,r.pluck)(i.milestonefont,o.fontFamily),fontWeight:(0,r.pluckNumber)(i.milestonefontbold,0)?"bold":"normal",fontStyle:(0,r.pluckNumber)(i.milestonefontitalic,0)?"italic":"normal"},(0,r.setLineHeight)(t),this.getChildContainer("milestoneGroup").css(t)},a.draw=function(){e.prototype.draw.call(this),this.setClipping()},a.setClipping=function(){var e,t=this.getFromEnv("chart"),a=t.config,i=this.getChildContainer("taskGroup"),o=this.getChildContainer("connectorGroup"),n=t.getChildContainer(),r=n.plotGroup,s=n.datalabelsGroup,l=n.trackerGroup,c=a.viewPortConfig,h=c.scaleX,d=c.x,p=this.getFromEnv("animationManager"),g=t.getChildren("canvas")[0].config.clip["clip-canvas"].slice(0);p.setAnimation({el:r,attr:{"clip-rect":g},state:t.config.clipSet?"updating":"appearing",component:this}),p.setAnimation({el:s,attr:{"clip-rect":g},state:t.config.clipSet?"updating":"appearing",component:this}),p.setAnimation({el:l,attr:{"clip-rect":g},state:t.config.clipSet?"updating":"appearing",component:this}),a.xOffset=e=d*h,p.setAnimation({el:r,attr:{transform:"T"+-e+",0"},component:this}),p.setAnimation({el:i,attr:{transform:"T"+-e+",0"},component:this}),p.setAnimation({el:o,attr:{transform:"T"+-e+",0"},component:this}),p.setAnimation({el:s,attr:{transform:"T"+-e+",0"},component:this}),p.setAnimation({el:l,attr:{transform:"T"+-e+",0"},component:this}),t.config.clipSet=!0},a.drawCanvas=function(){var e,t,a,i,o=this.getFromEnv("chart"),n=o.getFromEnv("dataSource").chart,s=o.config,l=this.getFromEnv("animationManager"),c=this.config,h=c.clip={},d=this.getGraphicalElement("canvasBorderElement"),p=this.getGraphicalElement("canvasElement"),g=s.actualCanvasLeft,u=s.actualCanvasTop,m=s.canvasWidth,f=s.canvasHeight,v=s.canvasLeft,b=s.canvasTop,x=s.canvasWidth+s.totalWidth,C=s.canvasHeight+s.totalHeight,k=this.getContainer("canvasGroup"),w=c.canvasBorderRadius,L=c.canvasBorderWidth,y=.5*L,A=c.canvasBorderColor,F=c.canBGColor,T=c.shadow,P=c.showCanvasBG=Boolean((0,r.pluckNumber)(n.showcanvasbg,1)),N=c.shadowOnCanvasFill;a=F,i={x:g-y,y:u-y,width:x+L,height:C+L,r:w,"stroke-width":L,stroke:A,"stroke-linejoin":L>2?"round":"miter"},c.showCanvasBorder?(e=l.setAnimation({el:d||"rect",attr:i,container:k,component:this}),d?e.show():e=this.addGraphicalElement("canvasBorderElement",e),e.shadow(T)):d&&d.hide(),h["clip-canvas"]=[Math.max(0,v),Math.max(0,b),Math.max(1,m),Math.max(1,f)],h["clip-canvas-init"]=[Math.max(0,v),Math.max(0,b),1,Math.max(1,f)],P?(i={x:g,y:u,width:x,height:C,r:w,"stroke-width":0,stroke:"none",fill:(0,r.toRaphaelColor)(a)},t=l.setAnimation({el:p||"rect",attr:i,component:this,container:k}),p?t.show():t=this.addGraphicalElement("canvasElement",t),t.shadow(N)):p&&p.hide()},t}(n["default"]);t["default"]=s},1176:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=function(e){var t,a,i=e.getChildren("canvas")[0],s={zoomable:!0,pannable:!0},l=e._feedAxesRawData();(0,o.componentFactory)(e,r["default"],"xAxis",1,l.xAxisConf),(0,o.componentFactory)(e,n["default"],"yAxis",1,l.yAxisConf),t=e.getChildren("yAxis")[0],a=e.getChildren("xAxis")[0],t.setLinkedItem("canvas",i),a.setLinkedItem("canvas",i),i.attachAxis(a,!1,e.zoomX?s:{}),i.attachAxis(t,!0,e.zoomY?s:{}),e._setCategories()};var o=a(215),n=i(a(1177)),r=i(a(1181))},1177:function(e,t,a){"use strict";var i=a(212),o=a(208);t.__esModule=!0,t["default"]=void 0;var n=o(a(229)),r=i(a(1178)),s=a(215),l=function(e){function t(){return e.apply(this,arguments)||this}(0,n["default"])(t,e);var a=t.prototype;return a.getName=function(){return"GanttProcess"},a.configure=function(t){var a=this.config,i=this.getFromEnv("color-manager"),o=this.getFromEnv("dataSource").chart;e.prototype.configure.call(this,t),a.lineColor=(0,s.convertColor)((0,s.pluck)(o.gridbordercolor,i.getColor("gridColor")),(0,s.pluckNumber)(o.gridborderalpha,100)),a.lineThickness=(0,s.pluckNumber)(o.gridborderthickness,1),a.lineDashStyle=(0,s.pluckNumber)(o.gridborderdashed,0)?(0,s.getDashStyle)((0,s.pluckNumber)(o.gridborderdashlen,1),o.gridborderdashgap,a.lineThickness):"none",a.plotLineColor=(0,s.convertColor)((0,s.pluck)(o.ganttlinecolor,i.getColor("gridColor")),(0,s.pluckNumber)(o.ganttlinealpha,100)),a.plotLineThickness=(0,s.pluckNumber)(o.ganttlinethickness,1),a.plotLineDashStyle=(0,s.pluckNumber)(o.ganttlinedashed,0)?(0,s.getDashStyle)((0,s.pluckNumber)(o.ganttlinedashlen,1),o.ganttlinedashgap,a.lineThickness):"none",a.gridResizeBarColor=(0,s.convertColor)((0,s.pluck)(o.gridresizebarcolor,i.getColor("gridResizeBarColor")),(0,s.pluckNumber)(o.gridresizebaralpha,100)),a.gridResizeBarThickness=(0,s.pluckNumber)(o.gridresizebarthickness,1),a.forceRowHeight=(0,s.pluckNumber)(o.forcerowheight,0),a.rowHeight=(0,s.pluckNumber)(o.rowheight,0),a.hoverColor=(0,s.pluck)(o.processhoverbandcolor,o.hoverbandcolor,i.getColor("gridColor")),a.hoverAlpha=(0,s.pluckNumber)(o.processhoverbandalpha,o.hoverbandalpha,30),a.useHover=(0,s.pluckNumber)(o.showprocesshoverband,o.showhoverband,o.showhovereffect,1),a.usePlotHover=(0,s.pluckNumber)(o.showganttpanehorizontalhoverband),a.showFullDataTable=(0,s.pluckNumber)(o.showfulldatatable,1),a.forceGanttWidthPercent=(0,s.pluckNumber)(o.forceganttwidthpercent,0),a.useVerticalScrolling=(0,s.pluckNumber)(o.useverticalscrolling,1),a.gridLineHeaderPath="",a.gridLinePath=""},a.setProcess=function(e){var t,a,i,o,n,l=this.config,c=l.startPad||0,h=l.endPad||0;if(l.processes={},e){for(l.hasProcess=1,a=l.processes.process=(0,s.extend2)({},e),(0,r.extractAttribToEnd)(a,{}),t=a.process.length,n=l.processes.processMap={},l.processes.processHeightMap={},i=0;i<t;i+=1)(o=a.process[i]).id&&(n[o.id.toLowerCase()]={catObj:o,index:i});this.setAxisRange({min:Number((0,s.toPrecision)(-c,10)),max:Number((0,s.toPrecision)(t-1+h,10)),tickInterval:Number((0,s.toPrecision)(1,10))})}else l.hasProcess=0},a.getProcessPositionByIndex=function(e){var t=this.config.processes.processHeightMap;return!!t[e]&&t[e]},a.getProcessPositionById=function(e){var t=this.config,a=t.processes&&t.processes.processMap[e],i=t.processes.processHeightMap;return!!a&&i[a.index]},a.setDataTable=function(e){var t=e,a=this.config;a.dataTables={},a.dataTables.dataTable={},t?(a.hasDataTables=1,(0,s.extend2)(a.dataTables.dataTable,t),t=a.dataTables.dataTable,(0,r.extractAttribToEnd)(t,{})):a.hasDataTables=0},a.setProcessHeight=function(){var e,t,a,i=this.config,o=this.getFromEnv("chart").config.canvasHeight,n=i.processes.process.process,r=i.processes.processHeightMap,l=i.processMaxHeight,c=0,h=i.forceRowHeight,d=i.rowHeight;for((l*n.length<o||0===i.useVerticalScrolling)&&(l=o/n.length),0===h?d&&d>l&&(l=d):l=d||l,a=0,t=n.length;a<t;a++)e=(0,s.pluckNumber)(n[a].height,l),r[a]={top:c,bottom:c+e,height:e},c+=e;return c},a.adjustWidth=function(){var e,t,a,i,o,n,r,l,c=this.config,h=c.totalWidth,d=h,p=0,g=!1;if(e=c.processVlineArr=[],d-=20*(c.hasDataTables&&c.dataTables&&c.dataTables.dataTable&&c.dataTables.dataTable.datacolumn?c.dataTables.dataTable.datacolumn.length+1:1),a=function(e){var t;return d+=20,t=e.match(/%/g)?(0,s.pluckNumber)(h*Number(e.replace(/%/g,"")/100),0):(0,s.pluckNumber)(e,0),d<20?t=20:t>d&&(t=d),d-=t,t},c.hasProcess&&(n=c.processes.process.process,"right"===c.processes.process.positioningrid&&(g=!0),i=p,p+=a((t=n._attrib).width||""+(t.rightPos-t.leftPos)),t.leftPos=i,t.rightPos=p,g?p=0:e.push({type:"process",ind:0,xPos:t.rightPos,left:t,leftLimit:t.leftPos+20})),c.hasDataTables)for(r in o=c.dataTables.dataTable.datacolumn)o.hasOwnProperty(r)&&"_attrib"!==r&&(i=p,p+=a((t=o[r]._attrib).width||""+(t.rightPos-t.leftPos)),t.leftPos=i,t.rightPos=p,(l=e[e.length-1])&&(l.right=t,l.rightLimit=t.rightPos-20),e.push({type:"dataTable",ind:r,xPos:t.rightPos,left:t,leftLimit:t.leftPos+20}));c.hasProcess&&(g?((t=n._attrib).rightPos=p+(t.rightPos-t.leftPos),t.leftPos=p,p+=t.rightPos-t.leftPos,(l=e[e.length-1])&&(l.right=t,l.rightLimit=t.rightPos-20)):e.pop()),c.totalWidth=p},a.placeAxis=function(e){var t,a,i,o,n,r,l,c,h,d,p,g,u,m,f,v,b=this.config,x=this.getFromEnv("chart"),C=x.getFromEnv("smartLabel"),k=b.labels.style,w=0,L=0,y={left:0,right:0},A=0,F=!1,T=0,P=0,N=0;if(C.useEllipsesOnOverflow(x.config.useEllipsesWhenOverflow),C.setStyle({fontSize:k.fontSize,fontFamily:k.fontFamily,lineHeight:k.lineHeight,fontWeight:k.fontWeight}),(b.forceGanttWidthPercent||0===b.showFullDataTable)&&(N=e/((b.hasDataTables&&b.dataTables&&b.dataTables.dataTable&&b.dataTables.dataTable.datacolumn?b.dataTables.dataTable.datacolumn.length:0)+1)),b.hasProcess){for(g=b.processes.process.process,"right"===(l=b.processes.process).positioningrid&&(F=!0),l.headertext&&(l.drawLabel=(0,s.parseUnsafeString)(l.headertext),r=l._attrib,(c={fontFamily:(0,s.pluck)(r.headerfontfamily,k.fontFamily),fontSize:(0,s.pluck)(r.headerfontsize,k.fontSize).replace(/px/i,"")+"px",fontWeight:(0,s.pluck)(1===Number(r.headerisbold)||"undefined"==typeof r.headerisbold?"bold":void 0,k.fontWeight),fontStyle:(0,s.pluck)(r.headerisitalic?"italic":void 0,k.fontStyle)}).lineHeight=(0,s.setLineHeight)(c),C.setStyle(c),(i=C.getOriSize(l.drawLabel)).width>L&&(w=i,L=i.width)),t=0,o=g.length;t<o;t++)r=(n=g[t])._attrib,n.drawLabel=(0,s.parseUnsafeString)(n.label||n.name),(c={fontFamily:(0,s.pluck)(r.fontfamily,k.fontFamily),fontSize:(0,s.pluck)(r.fontsize,k.fontSize).replace(/px/i,"")+"px",fontWeight:(0,s.pluck)(r.isbold?"bold":void 0,k.fontWeight),fontStyle:(0,s.pluck)(r.isitalic?"italic":void 0,k.fontStyle)}).lineHeight=(0,s.setLineHeight)(c),C.setStyle(c),(i=C.getOriSize(n.drawLabel)).width>L&&(w=i,L=i.width),i.height>P&&(P=i.height);b.processMaxHeight=P+8,g._attrib.leftPos=A,F?T=N||w.width+4:A+=N||w.width+4,g._attrib.rightPos=A}if(b.hasDataTables)for(t in h=b.dataTables.dataTable.datacolumn)if(h.hasOwnProperty(t)&&"_attrib"!==t){for(u in L=0,(d=h[t]).headertext&&(r=d._attrib,d.drawLabel=(0,s.parseUnsafeString)(d.headertext),(f={fontFamily:(0,s.pluck)(r.headerfontfamily,k.fontFamily),fontSize:(0,s.pluck)(r.headerfontsize,k.fontSize).replace(/px/i,"")+"px",fontWeight:(0,s.pluck)(1===Number(r.headerisbold)||"undefined"==typeof r.headerisbold?"bold":void 0,k.fontWeight),fontStyle:(0,s.pluck)(r.headerisitalic?"italic":void 0,k.fontStyle)}).lineHeight=(0,s.setLineHeight)(f),C.setStyle(f),(i=C.getOriSize(d.drawLabel)).width>L&&(v=i,L=i.width)),p=d.text)p.hasOwnProperty(u)&&"_attrib"!==u&&((a=p[u]).drawLabel=(0,s.parseUnsafeString)(a.label||a.name),m=a._attrib,(f={fontFamily:(0,s.pluck)(m.fontfamily,k.fontFamily),fontSize:(0,s.pluck)(m.fontsize,k.fontSize).replace(/px/i,"")+"px",fontWeight:(0,s.pluck)(m.isbold?"bold":void 0,k.fontWeight),fontStyle:(0,s.pluck)(m.isitalic?"italic":void 0,k.fontStyle)}).lineHeight=(0,s.setLineHeight)(f),C.setStyle(f),(i=C.getOriSize(a.drawLabel)).width>L&&(v=i,L=i.width));h[t]._attrib.leftPos=A,A+=N||v.width+4,h[t]._attrib.rightPos=A}return b.hasProcess&&F&&(g._attrib.leftPos+=A,g._attrib.rightPos+=A+T,A+=T),b.totalWidth=A,this.adjustWidth(),A=b.totalWidth>e?e:b.totalWidth,b.totalVisiblelWidth=A,y.left+=A,y},a.getProcessLen=function(){return this.config.processes.process.process.length},a._drawProcessAndDataTable=function(){var e,t,a,i,o,n,r,s,l,c,h,d,p=this.config,g=this.getFromEnv("chart"),u=(p.axisDimention||{}).x,m=p.totalWidth||0,f=p.gridArr||(p.gridArr=[]),v=g.getChildren("canvas")[0],b=g.config,x=g.getFromEnv("animationManager"),C=v.canvasTop||b.canvasTop,k=v.canvasLeft||b.canvasLeft,w=v.canvasHeight||b.canvasHeight,L=v.canvasWidth||b.canvasWidth,y=g.getChildContainer("axisBottomGroup"),A=p.totalVisiblelWidth,F=0,T=0,P=this.getContainer("ganttPlotHoverBandContainer"),N=this.getContainer("ganttPlotLineContainer"),E=this.getContainer("headerContainer"),S=this.getContainer("headerBackContainer"),D=this.getContainer("headerLineContainer"),B=this.getContainer("headerTextContainer"),V=this.getContainer("labelContainer"),_=this.getContainer("labelBackContainer"),H=this.getContainer("labelLineContainer"),I=this.getContainer("labelTextContainer"),R=this.getContainer("hotContainer");if(d=this.getContainer("ganttPlotHoverBandContainerParent")||this.addContainer("ganttPlotHoverBandContainerParent",x.setAnimation({el:"group",attr:{name:"gantt-plot-band-container-parent"},container:y,component:this})),this.addContainer("ganttPlotHoverBandContainer",x.setAnimation({el:P||"group",attr:{name:"gantt-plot-band-container","clip-rect":k+","+C+","+L+","+w},container:d,component:this})),this.addContainer("ganttPlotLineContainer",x.setAnimation({el:N||"group",attr:{name:"gantt-plot-line-container","clip-rect":k+","+C+","+L+","+w},container:y,component:this})),h={name:"gantt-header-container","clip-rect":k-p.totalVisiblelWidth+","+(C-b.categorySpaceUsed)+","+p.totalVisiblelWidth+","+b.categorySpaceUsed},p.isDraged?delete h.transform:h.transform="t0,0",E=this.addContainer("headerContainer",x.setAnimation({el:E||"group",attr:h,container:y,component:this})),S||(S=this.addContainer("headerBackContainer",x.setAnimation({el:"group",attr:{name:"gantt-header-back-container"},container:E,component:this}))),D||(D=this.addContainer("headerLineContainer",x.setAnimation({el:"group",attr:{name:"gantt-header-line-container"},container:E,component:this}))),B||(B=this.addContainer("headerTextContainer",x.setAnimation({el:"group",attr:{name:"gantt-header-text-container"},container:E,component:this}))),c={name:"gantt-label-container","clip-rect":k-p.totalVisiblelWidth+","+C+","+p.totalVisiblelWidth+","+w},p.isDraged?delete c.transform:c.transform="t0,0",V=this.addContainer("labelContainer",x.setAnimation({el:V||"group",attr:c,component:this,container:y})),_||(_=this.addContainer("labelBackContainer",x.setAnimation({el:"group",attr:{name:"gantt-label-back-container"},container:V,component:this}))),H||(H=this.addContainer("labelLineContainer",x.setAnimation({el:"group",attr:{name:"gantt-label-line-container"},container:V,component:this}))),I||(I=this.addContainer("labelTextContainer",x.setAnimation({el:"group",attr:{name:"gantt-label-text-container"},container:V,component:this}))),this.addContainer("hotContainer",x.setAnimation({el:R||"group",attr:{name:"gantt-hot-container","clip-rect":k-p.totalVisiblelWidth+","+(C-b.categorySpaceUsed)+","+p.totalVisiblelWidth+","+(w+b.categorySpaceUsed)},component:this,container:g.getContainer("parentgroup")})),p.gridLinePath="",p.gridLineHeaderPath="",p.hoverElemsArr=[],p.labelHoverEventName={click:"ProcessClick",rollOver:"ProcessRollOver",rollOut:"ProcessRollOut"},p.hasProcess)for(t=p.processes.process.process,r={elem:p.processes.process,elemIndex:F,dimension:{left:u-m+t._attrib.leftPos,right:u-m+t._attrib.rightPos,top:C-b.categorySpaceUsed,bottom:C},type:"header"},this._drawProcessAndDataTableElement(r),F+=1,f=p.gridArr=[],e=0,a=t.length;e<a;e++)s=this.getProcessPositionByIndex(e),r={elem:t[e],elemIndex:F,pos:e,dimension:{left:u-m+t._attrib.leftPos,right:u-m+t._attrib.rightPos,top:C+s.top,bottom:C+s.bottom},type:"process"},this._drawProcessAndDataTableElement(r),F+=1,f.push({y:r.dimension.bottom});if(p.hasDataTables){for(e in i=p.dataTables.dataTable.datacolumn)if(i.hasOwnProperty(e)&&"_attrib"!==e)for(n in T=0,r={elem:i[e],elemIndex:F,pos:e,dimension:{left:u-m+i[e]._attrib.leftPos,right:u-m+i[e]._attrib.rightPos,top:C-b.categorySpaceUsed,bottom:C},type:"header"},this._drawProcessAndDataTableElement(r),F+=1,o=i[e].text){if(T>=a)break;o[n]._attrib&&t[n]&&t[n]._attrib&&(o[n]._attrib.hoverbandcolor=t[n]._attrib.hoverbandcolor,o[n]._attrib.hoverbandalpha=t[n]._attrib.hoverbandalpha,o[n]._attrib.showhoverband=t[n]._attrib.showhoverband),o.hasOwnProperty(n)&&"_attrib"!==n&&(s=this.getProcessPositionByIndex(n),r={elem:o[n],elemIndex:F,pos:n,dimension:{left:u-m+i[e]._attrib.leftPos,right:u-m+i[e]._attrib.rightPos,top:C+s.top,bottom:C+s.bottom},type:"datatable"},T++,this._drawProcessAndDataTableElement(r),F+=1)}p.drawFromProcessVlineDrag?p.drawFromProcessVlineDrag=!1:m>A?(l=m-A,this.resetTransletAxis(),this.translateAxis(l,void 0)):this.resetTransletAxis()}this._drawGridLine(),this._disposeExtraProcessAndDataTableElement(F)},a._drawVerticalLineAndTracker=function(){var e,t,a,i,o,n,r,l,c,h=this,d=h.config,p=h.getFromEnv("chart"),g=d.canvas,u=p.config,m=(d.axisDimention||{}).x,f=d.totalWidth||0,v=g.canvasTop||u.canvasTop,b=h.components.processVline||(h.components.processVline=[]),x=d.processVlineArr,C=h.getContainer("hotContainer"),k=h.getFromEnv("animationManager"),w=0,L=function(){var e=this.data("drag-options");e.origX=e.lastX||(e.lastX=0),e.vHoverLine.show(),p.trackerClicked=!0,e.draged=!1},y=function(e){var t,a=this.data("drag-options"),i=a.vLineSetting,o="string"==typeof e.data?+e.data.substr(0,e.data.indexOf(",")):e.data[0]||0,n=i.xPos+o,r=i.leftLimit,s=i.rightLimit;n<r&&(o=r-i.xPos),n>s&&(o=s-i.xPos),t={transform:"t"+(a.origX+o)+",0"},this.attr(t),a.vHoverLine.attr(t),a.draged=!0,a.lastX=o},A=function(){var e,t=this.data("drag-options"),a=t.vLineSetting,i=t.vLineIndex;p.trackerClicked=!1,t.vHoverLine.hide(),t.draged&&(d.isDraged=!0,a.left.rightPos+=t.lastX||0,a.right.leftPos+=t.lastX||0,a.xPos+=t.lastX||0,x[i-1]&&(x[i-1].rightLimit+=t.lastX||0),x[i+1]&&(x[i+1].leftLimit+=t.lastX||0),d.drawFromProcessVlineDrag=!0,h._drawProcessAndDataTable(),h._drawVerticalLineAndTracker(),e={transform:"t0,0"},this.attr(e),t.vHoverLine.attr(e))};for(n={stroke:d.gridResizeBarColor,"stroke-width":d.gridResizeBarThickness},r={stroke:s.TRACKER_FILL,"stroke-width":30},o=v-u.categorySpaceUsed,e=0,t=x.length;e<t;e+=1)a=["M",l=m-f+("process"===x[e].type?d.processes.process.process:d.dataTables.dataTable.datacolumn[x[e].ind])._attrib.rightPos,o,"L",l,v+d.processTotalHeight],b[w]?((c=b[w].graphics.vHoverLine).attr({path:a}).attr(n),(i=b[w].graphics.hotElement).attr({path:a}).attr(r)):(n.path=a,c=k.setAnimation({el:"path",container:C,component:h,attr:n}),r.path=a,i=k.setAnimation({el:"path",container:C,component:h,attr:r}),b[w]={},b[w].graphics={},b[w].config={},b[w].graphics.vHoverLine=c,b[w].graphics.hotElement=i),i.show(),c.hide(),i.css("cursor",s.hasSVG?"ew-resize":"e-resize").drag(y,L,A).data("drag-options",{vHoverLine:b[w].graphics.vHoverLine,vLineSetting:x[e],vLineIndex:e}),w+=1;for(e=w,t=b.length;e<t;e+=1)b[e].graphics.vHoverLine.attr({path:["M",0,0]}),b[e].graphics.hotElement.attr({path:["M",0,0]})},a._drawComponents=function(){var e=this.config;e.isDraged=!1,this._drawProcessAndDataTable(),e.drawPlotlines&&this._drawPlotLine(),this._drawVerticalLineAndTracker(),this._drawGridLine()},t}(r["default"]);t["default"]=l},1178:function(e,t,a){"use strict";var i=a(212),o=a(208);t.__esModule=!0,t.extractAttribToEnd=function m(e,t){var a;for(a in e._attrib=(0,h.extend2)({},t),e._attrib=(0,h.extend2)(e._attrib,function(e){var t,a={};for(t in e)e.hasOwnProperty(t)&&"string"==typeof e[t]&&e[t]&&(a[t]=e[t]);return a}(e)),e)e.hasOwnProperty(a)&&e[a]&&"object"==typeof e[a]&&"_attrib"!==a&&m(e[a],e._attrib)},t["default"]=void 0;var n,r,s,l=o(a(229)),c=i(a(1179)),h=a(215),d=a(223),p=o(a(1180)),g=function(e,t){var a;for(a=0;a<e.length;a++)if(e[a].id===t)return e[a];return!1};(0,d.addDep)({name:"ganttCommonAnimation",type:"animationRule",extension:p["default"]});var u=function(e){function t(){var t;return(t=e.call(this)||this).components={},t}(0,l["default"])(t,e);var a=t.prototype;return a.getName=function(){return"GanttCommon"},a._drawPlotLine=function(){var e,t,a,i,o=this.config,r=this.getFromEnv("chart"),s=r.getChildren("canvas")[0],l=r.config,c=this.getContainer("ganttPlotLineContainer"),h=s.canvasBottom||l.canvasBottom,d=s.canvasLeft||l.canvasLeft,p=s.canvasRight||l.canvasRight,g=s.canvasTop||l.canvasTop,u=this.getFromEnv("animationManager"),m=o.gridArr,f=this.getGraphicalElement("plotLine"),v=[];for(a=0,i=m.length;a<i;a+=1)m[a].x!==n?v.push("M",m[a].x,g,"L",m[a].x,h):v.push("M",d,m[a].y,"L",p,m[a].y);e={"stroke-dasharray":o.plotLineDashStyle,"stroke-width":o.plotLineThickness,stroke:o.plotLineColor,path:v},t=u.setAnimation({el:f&&f[0]||"path",attr:e,container:c,label:"path",component:this}),f&&f[0]||this.addGraphicalElement("plotLine",t,!0)},a._drawTrendLine=function(){var e,t,a,i,o,r,s,l,d,p,u,m,f,v,b,x,C,k,w,L,y=this.getComponentInfo("trend"),A=this.getFromEnv("toolTipController"),F=this.config,T=F.isOpposit,P=F.labelPadding,N=this.getFromEnv("chart"),E=N.getFromEnv("animationManager"),S=N.config,D=N.getChildren("canvas")[0],B=D.config.canvasTop+D.config.canvasHeight,V=D.config.canvasLeft||S.canvasLeft,_=D.config.canvasRight||S.canvasRight,H=D.config.canvasTop||S.canvasTop,I=F.drawTrendLabels,R=this.getGraphicalElement("trendlabels")||[],M=F.axisTrendContainerTop,O=F.axisTrendContainerBottom,G=F.axisDimention||{},W=this._isZoomed(),z=this.getVisibleConfig(),j=-1*S.xOffset,X=this.getGraphicalElement("trendElems"),K=this.getGraphicalElement("trendlabels"),U=F.trendLines,Y=F.vTrendLines;for(!X&&(X=[]),!K&&(K=[]),e=0;e<X.length;e++)(w=g(X,e))&&w.remove();if(Y?u=T?(G.opposite||B)-(F.trendBottomPadding||0):(G.y||B)+(F.trendBottomPadding||0):(u=T?(G.opposite||V)+(P||0):(G.x||V)-(P||0),m=T?(G.x||_)+(P||0):(G.opposite||_)+(P||0)),W&&(b=Math.max(this.getPixel(z.minValue,{wrtVisible:!0}),this.getPixel(z.maxValue,{wrtVisible:!0})),x=Math.min(this.getPixel(z.minValue,{wrtVisible:!0}),this.getPixel(z.maxValue,{wrtVisible:!0}))),U||Y)for(e=0;e<y.length;e++)t=y[e].marker,a=y[e].label,i={fill:t.fill||"",stroke:t.stroke||"","stroke-width":t.strokeWidth,"stroke-dasharray":t.strokeDashArray,"shape-rendering":t.shapeRendering},o={fill:a.fill||"",text:a.text,"text-anchor":a.textAnchor,"text-bound":a.textBound},s=t.isZone,d=t.startValue,p=t.endValue,Y?(r=this.getPixel(F.hasBreakPoints?this._getRelativeBreakValue(d):d,{wrtVisible:!0}),l=p?this.getPixel(F.hasBreakPoints?this._getRelativeBreakValue(p):p,{wrtVisible:!0}):0,r+=j,l+=j,o.y=u,o["vertical-align"]="top",p!==n&&""!==p&&p!==d&&s?(i.path=["M",r,H,"L",r,B,"L",l,B,"L",l,H,"Z"],o.x=r+(l-r)/2):(f=p?(0,c.getCrispPath)(["M",r,H,"L",l,B],t.strokeWidth):(0,c.getCrispPath)(["M",r,H,"L",r,B],t.strokeWidth),i.path=f.path,o.x=p?l:r),W&&(!I||o.x>b||o.x<x)?(o.text=h.BLANKSTRING,o.visibility="hidden"):o.visibility="visible"):U&&(r=this.getPixel(d,{wrtVisible:!0}),l=p?this.getPixel(p,{wrtVisible:!0}):0,k=a.valueOnRight,p!==n&&""!==p&&p!==d&&s?(i.path=["M",V,r,"L",_,r,"L",_,l,"L",V,l,"Z"],o.x=k?m:u,o.y=r+(l-r)/2):(f=p?(0,c.getCrispPath)(["M",V,r,"L",_,l,"Z"],t.strokeWidth):(0,c.getCrispPath)(["M",V,r,"L",_,r,"Z"],t.strokeWidth),i.path=f.path,o.x=k?m:u,o.y=p&&k?l:r),W&&(!I||o.y>b||o.y<x)?(o.text=h.BLANKSTRING,o.visibility="hidden"):o.visibility="visible"),C=y[e].showOnTop,v=N.config.is3D||1!==C&&1!==F.showTrendlinesOnTop?O:M,w=g(X,e),w=E.setAnimation({el:w||"path",attr:i,container:v,label:"path",component:this}).show(),(L=K[e])&&E.setAnimation({el:L,attr:o,label:"text",component:this}),!g(X,e)&&this.addGraphicalElement("trendElems",w,!0),w.id=e,""!==a.toolText?A.enableToolTip(w,a.toolText):A.disableToolTip(w);else for(e=0;e<X.length;e++)(w=g(X,e))&&(w.remove(),R[e]&&R[e].remove())},a._drawProcessAndDataTableStyleParser=function(e){var t,a,i,o,r,s,l,c,d,p,g,u,m,f,v,b,x,C,k,w,L,y=this.config,A=this.getFromEnv("chart"),F=this.getFromEnv("smartLabel"),T=this.getFromEnv("color-manager"),P=y.labels.style,N=e.elem||{},E=e.dimension,S=N._attrib||{},D=E.left,B=E.right,V=E.top,_=E.bottom;switch(e.type){case"category":case"datatable":case"process":t=(0,h.pluck)(S.font,P.fontFamily),a=(0,h.pluck)(S.fontsize,P.fontSize).replace(/px/i,"")+"px",o=(0,h.pluck)(Number(S.isitalic)?"italic":n,P.fontStyle),r=(0,h.convertColor)((0,h.pluck)(S.bgcolor?(0,h.getFirstColor)(S.bgcolor):n,T.getColor("categoryBgColor")),(0,h.pluckNumber)(S.bgalpha,100)),m=(0,h.pluck)(S.fontcolor?(0,h.getFirstColor)(S.fontcolor):n,P.color),f=(0,h.pluckNumber)(S.isunderline,0)?"underline":"none",g=(0,h.pluck)(S.valign,"center").toLowerCase(),u=(0,h.pluck)(S.align,"middle").toLowerCase(),c=N.drawLabel||"",i=(0,h.pluck)(Number(S.isbold)?"bold":n,P.fontWeight),w=N.link;break;case"header":t=(0,h.pluck)(S.headerfont,P.fontFamily),a=(0,h.pluck)(S.headerfontsize,P.fontSize).replace(/px/i,"")+"px",i=(0,h.pluck)(1===Number(S.headerisbold)||S.headerisbold===n?"bold":n,P.fontWeight),m=(0,h.pluck)(S.headerfontcolor?(0,h.getFirstColor)(S.headerfontcolor):n,P.color),f=(0,h.pluckNumber)(S.headerisunderline,0)?"underline":"none",o=(0,h.pluck)(S.headerisitalic?"italic":n,P.fontStyle),r=(0,h.convertColor)((0,h.pluck)(S.headerbgcolor?(0,h.getFirstColor)(S.headerbgcolor):n,T.getColor("categoryBgColor")),(0,h.pluckNumber)(S.headerbgalpha,100)),g=(0,h.pluck)(S.headervalign,"center").toLowerCase(),u=(0,h.pluck)(S.headeralign,"middle").toLowerCase(),c=N.drawLabel||"",w=N.headerlink}switch(e.type){case"category":y.gridLinePath+="M"+D+","+V+"L"+D+","+_+"L"+B+","+_,i=(0,h.pluck)(1===Number(S.isbold)||S.isbold===n?"bold":n,P.fontWeight);break;case"datatable":case"process":y.gridLinePath+="M"+D+","+_+"L"+B+","+_+"L"+B+","+V;break;case"header":y.gridLineHeaderPath+="M"+D+","+_+"L"+B+","+_+"L"+B+","+V}return v=(0,h.pluck)(N._attrib.hoverbandcolor,y.hoverColor),b=(0,h.pluckNumber)(N._attrib.hoverbandalpha,y.hoverAlpha),"left"===u?(s=D+2,u="start"):"right"===u?(s=D+(B-D)-2,u="end"):(u="middle",s=D+(B-D)/2),"top"===g?l=V-2:"bottom"===g?l=V+(_-V)-2:(g="middle",l=V+(_-V)/2),P={fontFamily:t,fontSize:a,fontWeight:i,fontStyle:o,textDecoration:f},L=(0,h.setLineHeight)(P),L=_-V>(L=Number(L.replace(/px/i,"")))?_-V:L,F.useEllipsesOnOverflow(A.config.useEllipsesWhenOverflow),F.setStyle(P),d={textAttr:{x:s,y:l,text:(p=F.getSmartText(c,B-D,L)).text,fill:m,"text-anchor":u,"vertical-align":g,cursor:w?"pointer":A.getFromEnv("paper").canvas.style.cursor},css:P,rectAttr:{x:D,y:V,width:D<B?B-D:0,height:V<_?_-V:0,fill:r,"stroke-width":0,cursor:w?"pointer":A.getFromEnv("paper").canvas.style.cursor},eventArgs:{isHeader:"header"===e.type,label:c,vAlign:g,align:u,link:w,id:N.id},tooltext:p.oriText},"datatable"===e.type||"process"===e.type||"category"===e.type?(k=(0,h.convertColor)(v,b),x=(0,h.pluckNumber)(N._attrib.showhoverband,y.useHover),C=(0,h.pluckNumber)(N._attrib.showganttpanehoverband,y.usePlotHover,x),d.dataArgs={rollOverColor:k,useHover:x,usePlotHover:C,dimension:E,hoverEle:N,type:e.type,pos:e.pos,axis:this,groupId:e.elemIndex}):d.dataArgs={rollOverColor:n,useHover:0,usePlotHover:0,dimension:E,hoverEle:N,type:e.type,pos:e.pos,axis:this,groupId:e.elemIndex},d},a._drawProcessAndDataTableElement=function(e){var t,a,i,o,n,l=this,c=l.config,h=l.getFromEnv("chart"),d=l.components.categoryElement||[],p=c.hoverElemsArr||(c.hoverElemsArr=[]),g=l.getFromEnv("animationManager"),u=e.elemIndex,m=c.labelHoverEventName,f=l.getFromEnv("toolTipController"),v=h.config.showtooltip,b=function(e){h.plotEventHandler(this,e,m.click)},x=function(e){s=clearTimeout(s),r&&!r.removed||(r=null),r&&l._gridOutHandler.call(r),l._gridHoverHandler.call(this),h.plotEventHandler(this,e,m.rollOver)},C=function(e){r=this,s=clearTimeout(s),s=setTimeout((function(){return l._gridOutHandler.call(r)}),500),h.plotEventHandler(r,e,m.rollOut)};"header"===e.type?(a=l.getContainer("headerBackContainer"),i=l.getContainer("headerTextContainer")):(a=l.getContainer("labelBackContainer"),i=l.getContainer("labelTextContainer")),t=l._drawProcessAndDataTableStyleParser(e),o=g.setAnimation({el:d[u]&&d[u].graphics.rect||"rect",attr:t.rectAttr,container:a,label:"rect",component:l}),n=g.setAnimation({el:d[u]&&d[u].graphics.label||"text",attr:t.textAttr,container:i,label:"text",component:l}),d[u]?(n.removeCSS(),a.appendChild(o),i.appendChild(n)):(l.addGraphicalElement("rectElement",o,!0),l.addGraphicalElement("textElement",n,!0),d[u]={},d[u].graphics={},d[u].config={},d[u].graphics.label=n,d[u].graphics.rect=o,o.hover(x,C).on("fc-click",b),n.hover(x,C).on("fc-click",b)),n.css(t.css),"header"!==e.type&&(p[e.pos]||(p[e.pos]=[]),p[e.pos].push({bgElem:o,bgColor:t.rectAttr.fill})),o.data("dataObj",e.elem).data("eventArgs",t.eventArgs).data("data",t.dataArgs),n.data("dataObj",e.elem).data("eventArgs",t.eventArgs).data("data",t.dataArgs),v?f.enableToolTip(n,t.tooltext):f.disableToolTip(n),l.components.categoryElement=d},a._drawGridLine=function(){var e,t,a,i,o=this.config,n=this.getFromEnv("animationManager"),r=this.getGraphicalElement("gridLine")||[],s=0,l=0,c=2;for(e={"stroke-dasharray":o.lineDashStyle,"stroke-width":o.lineThickness,stroke:o.lineColor};l<c;l+=1){if(0===l)a=o.gridLinePath,i=this.getContainer("labelLineContainer");else if(a=o.gridLineHeaderPath,i=this.getContainer("headerLineContainer"),!a)continue;e.path=a,t=n.setAnimation({el:r&&r[s]||"path",attr:e,container:i,label:"path",component:this}),(!r||!r[s])&&this.addGraphicalElement("gridLine",t,!0),s+=1}for(l=s,c=this.getGraphicalElement("gridLine").length;l<c;l+=1)this.getGraphicalElement("gridLine")[l].attr({path:"M0,0"})},a._gridHoverHandler=function(){var e,t,a,i=this.data("data"),o=i.type,n=i.dimension,r=i.axis,s=r.getFromEnv("chart").config,l=r.getFromEnv("animationManager"),c=r.config.hoverElemsArr||[],h=r.getContainer("ganttPlotHoverBandContainer"),d=r.getGraphicalElement("plotHoverElement"),p=d?1:0;if(e="category"===o?{x:n.left,y:s.canvasTop,width:n.left<n.right?n.right-n.left:0,height:s.height,fill:i.rollOverColor,"stroke-width":0}:{y:n.top,x:s.canvasLeft,height:n.top<n.bottom?n.bottom-n.top:0,width:s.width,fill:i.rollOverColor,"stroke-width":0},i.usePlotHover&&(d=l.setAnimation({el:d||"rect",attr:e,component:r,label:"rect",container:h}),p?d.show():r.addGraphicalElement("plotHoverElement",d)),i.useHover&&c[i.pos])for(t=0,a=c[i.pos].length;t<a;t+=1)l.setAnimation({el:c[i.pos][t].bgElem||"rect",attr:{fill:i.rollOverColor},label:"rect"})},a._gridOutHandler=function(){var e,t,a,i=this.data("data"),o=i.axis,n=o.config.hoverElemsArr||[],r=o.getFromEnv("animationManager"),s=o.getGraphicalElement("plotHoverElement");if(i.usePlotHover&&s&&s.hide(),i.useHover&&n[i.pos])for(e=0,t=n[i.pos].length;e<t;e+=1)a=n[i.pos][e],r.setAnimation({el:a.bgElem||"rect",attr:{fill:a.bgColor},label:"rect"})},a._disposeExtraProcessAndDataTableElement=function(e){var t,a,i=this.components.categoryElement||[];for(t=e,a=i.length;t<a;t+=1)i[t].graphics.label.attr({text:""}),i[t].graphics.rect.attr({x:0,y:0,width:0,heigth:0})},t}(c["default"]);t["default"]=u},1179:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t._drawScrollBar=v,t.getCrispPath=t["default"]=void 0;var o,n=i(a(229)),r=i(a(465)),s=i(a(466)),l=a(230),c=a(215),h=(0,a(223).getDep)("redraphael","plugin"),d=function(e,t){return parseInt(e,t||10)},p=function(e,t,a){return a.getFromEnv("animationManager").setAnimation({el:"group",attr:{name:e},container:t,state:"appearing",component:a,label:"group"})},g=function(e,t){var a;return void 0===t&&(t=0),0===t||0===(a=Math.abs(e)%t)?e:e<0?-(Math.abs(e)-a):e+t-a},u=function(e,t){return e<t?t:e},m=function(e){e.draw()};function f(e){var t=c.BLANKSTRING;return"trend"!==e&&"trends"!==e||(t="trend"),"catVLine"!==e&&"catVLines"!==e||(t="catVLine"),"label"!==e&&"labels"!==e||(t="labels"),"line"!==e&&"lines"!==e||(t="lines"),"band"!==e&&"bands"!==e||(t="band"),"catBand"!==e&&"catBands"!==e||(t="catBand"),t}function v(){var e,t,a,i,o,n,r,s,l,d,g,u,m,f,v,b,x=this,C=x.getFromEnv("chart"),k=C.config,w=C.graphics,L=x.config,y=L.axisRange,A=k.scrollOptions||(k.scrollOptions={}),F=y.max,T=y.min,P=x.getLinkedItem("scrollBar"),N=P&&P.config.node,E=C.getChildren("canvas")[0].config;P&&(e=E.canvasLeft,t=E.canvasTop,a=E.canvasHeight,i=E.canvasBorderWidth,o=L.showAxisLine&&L.axisLineThickness||0,n=(0,c.pluckNumber)(i,L.lineStartExtension),r=(0,c.pluckNumber)(i,L.lineEndExtension),A.viewPortMin=T,A.viewPortMax=F,s=(m=(u=x.getVisibleConfig()).maxValue-u.minValue)/(f=y.max-y.min),v=(u.minValue-y.min)/(f-m),l=A.windowedCanvasWidth=x.getPixel(A.vxLength),d=A.fullCanvasWidth=x.getPixel(F-T)-l,(g=w.scrollBarParentGroup)||(g=w.scrollBarParentGroup=p("scrollBarParentGroup",w.parentGroup).insertBefore(C.getChildContainer().datalabelsGroup)),!1!==L.scrollEnabled?(L.isVertical?P.draw(e,t,{height:a,scrollRatio:s,roundEdges:E.isRoundEdges,fullCanvasWidth:d,windowedCanvasWidth:l,scrollPosition:v,parentLayer:g}):P.draw(e-n,t+a+i+o-2,{width:E.canvasWidth+n+r,scrollRatio:s,roundEdges:E.isRoundEdges,fullCanvasWidth:d,windowedCanvasWidth:l,scrollPosition:v,parentLayer:g}),!N&&(h.eve.on("raphael.scroll.start."+P.config.node.id,(function(e){x.setState("scrolling",!0),b=e,C.fireChartInstanceEvent("scrollstart",{scrollPosition:e})})),h.eve.on("raphael.scroll.end."+P.config.node.id,(function(e){x.setState("scrolling",!1),C.fireChartInstanceEvent("scrollend",{prevScrollPosition:b,scrollPosition:e})})))):P&&P.node&&P.node.hide(),L.scrollBarDrawn=!0)}t.getCrispPath=function(e,t){var a,i,o=!1,n=t%2;return e[1]===e[4]&&(a=e[1],i=Math.round(a),e[1]=e[4]=n?i>a?i-.5:i+.5:i,o=!0),e[2]===e[5]&&(a=e[2],i=Math.round(a),e[2]=e[5]=n?i>a?i-.5:i+.5:i,o=!0),{path:e,isCrisped:o}};var b=function(e){function t(){var t;return(t=e.call(this)||this)._drawScrollBar=v,t}(0,n["default"])(t,e);var a=t.prototype;return a.getType=function(){return"axis"},a.getName=function(){return"cartesian"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.setAdaptiveMin=0,t.adjustDiv=1,t.axisNameWidth=o,t.rotateAxisName=0,t.useEllipsesWhenOverflow=1,t.divLineColor=o,t.divLineAlpha=o,t.divLineThickness=o,t.divLineIsDashed=o,t.divLineDashLen=o,t.divLineDashGap=o,t.showAlternateGridColor=o,t.alternateGridColor=o,t.alternateGridAlpha=o,t.showZeroPlane=1,t.zeroPlaneAlpha=80,t.showZeroPlaneValue=1,t.showZeroPlaneOnTop=1,t.showAxisLine=o,t.axisLineThickness=o,t.axisLineAlpha=o,t.tickLength=o,t.trendlineToolText=o,t.trendlineColor="333333",t.trendlineThickness=1,t.trendlineAlpha=o,t.showTrendlinesOnTop=0,t.trendlinesAreDashed=0,t.trendlinesDashLen=5,t.trendlinesDashGap=2,t.isTrendZone=o,t.showTrendlines=1,t.showTrendlineLabels=1,t.showLabels=1,t.maxLabelHeight=o,t.rotateLabels=o,t.slantLabel=0,t.showAxisValues=1,t.showTooltip=1,t.isActive=!0,t.drawLabels=!0,t.drawOnlyCategoryLine=!1,t.drawLabelsOpposit=!1,t.drawPlotlines=!0,t.drawAxisLine=!0,t.drawPlotBands=!0,t.drawAxisName=!0,t.drawAxisNameOpposit=!1,t.axisNameAlignCanvas=!1,t.drawAxisNameFromBottom=!1,t.drawTrendLines=!0,t.drawTrendLabels=!0,t.drawTick=!0,t.drawTickMinor=!0,t.animateAxis=!0,t.drawAxisLineWRTCanvas=!0,t.isRelativeAxisInverse=!1,t.axisIndex=0,t.uniqueClassName=0,t.viewPortRatio={},t.canvas={},t.axisRange={},t.isConfigured=!0,t.axisDimention={},t.extremeLabels={firstLabel:{},lastLabel:{}},t._setRangeAgain=!1,t._defaultForceDecimal=o,t._defaultDecimalPrecision=o,t.rangeChanged=!1,t.dimensionChanged=!1,t.apparentScrollPos=0,this.addToEnv("componentInfo",{catVLine:[],trend:[],labels:[],lines:[],bands:[],catBand:[]})},a.configure=function(e){var t,a,i,n,r,s,l,h,p=this.config,g=this.getFromEnv("chart"),m=this.getFromEnv("chart").config.is3D,f=g.getFromEnv("dataSource").chart,v=this.getFromEnv("number-formatter"),b=this.getFromEnv("tempAxis");if(t=p.rawAttr=e,e.vtrendlines)for(r=0;r<e.vtrendlines.length;++r)for(s=0;s<e.vtrendlines[r].line.length;++s)l=t.vtrendlines[r].line[s],h=e.vtrendlines[r].line[s],l.startvalue=(0,c.pluckNumber)(h.startvalue,o),l.endvalue=(0,c.pluckNumber)(h.endvalue,h.startvalue),l.color=(0,c.pluck)(h.color,"FFFFFF"),l.istrendzone=(0,c.pluckNumber)(h.istrendzone,1),l.thickness=(0,c.pluckNumber)(h.thickness,1),l.trendTextAlpha=(0,c.pluckNumber)(h.alpha,p.trendlineAlpha,99),l.alpha=(0,c.pluckNumber)(h.alpha,40),l.tooltext=(0,c.pluck)(h.tooltext,"");if(e.trendlines)for(r=0;r<e.trendlines.length;++r)for(s=0;s<e.trendlines[r].line.length;++s)l=t.trendlines[r].line[s],h=e.trendlines[r].line[s],l.startvalue=(0,c.pluckNumber)(h.startvalue,o),l.endvalue=(0,c.pluckNumber)(h.endvalue,h.startvalue),l.color=(0,c.pluck)(h.color,"FFFFFF"),l.istrendzone=(0,c.pluckNumber)(h.istrendzone,1),l.thickness=(0,c.pluckNumber)(h.thickness,1),l.trendTextAlpha=(0,c.pluckNumber)(h.alpha,p.trendlineAlpha,99),l.alpha=(0,c.pluckNumber)(h.alpha,40),l.showOnTop=(0,c.pluckNumber)(h.showOnTop,1),l.valueOnRight=(0,c.pluckNumber)(h.valueOnRight,0);p.trendLines=t.trendlines,p.vTrendLines=t.vtrendlines,(0,c.parseConfiguration)(t,p),p.axisName=(0,c.parseUnsafeString)(t.axisName),p.axisValuePadding=p.axisNamePadding||(0,c.pluckNumber)(t.axisValuePadding,4),p.axisNamePadding=p.axisNamePadding||(0,c.pluckNumber)(t.axisNamePadding,5),p.maxLabelWidthPercent=(0,c.pluckNumber)(t.maxLabelWidthPercent),p.maxLabelWidthPercent=Math.abs(p.maxLabelWidthPercent),p.minLabelWidthPercent=Math.abs((0,c.pluckNumber)(t.minLabelWidthPercent)),p.numDivLines=(0,c.pluckNumber)(t.numDivLines,4),p.numDivLines=u(p.numDivLines,0),p.categoryNumDivLines=(0,c.pluckNumber)(t.numDivLines,0),p.axisValuePadding=u(p.axisValuePadding,0),p.isReverse=Number(t.isReverse,0),p.isOpposit=Number(t.isOpposit,0),p.isVertical=Number(t.isVertical,0),p.categoryDivLinesFromZero=1,p.axisMinValue=v.getCleanValue(t.axisMinValue),p.axisMaxValue=v.getCleanValue(t.axisMaxValue),p.zeroPlaneColor=(0,c.pluck)(t.zeroPlaneColor,t.divLineColor),p.zeroPlaneThickness=(0,c.pluck)(t.zeroPlaneThickness,t.divLineThickness),p.axisLineColor=(0,c.convertColor)(t.axisLineColor,t.axisLineAlpha),p.tickAlpha=(0,c.pluckNumber)(t.tickAlpha,p.axisLineAlpha),p.tickColor=(0,c.convertColor)((0,c.pluck)(t.tickColor,t.axisLineColor),p.tickAlpha),p.tickWidth=(0,c.pluckNumber)(t.tickWidth,p.axisLineThickness),p.maxZoomLimit=(0,c.pluckNumber)(f.maxzoomlimit,g.maxzoomlimit,1e3),p.showVLines=(0,c.pluckNumber)(f.showvlines,1),p.showVLinesOnTop=(0,c.pluckNumber)(f.showvlinesontop,0),p.showVLineLabels=(0,c.pluckNumber)(f.showvlinelabels,this.showVLineLabels,1),p.showVLineLabelBorder=(0,c.pluckNumber)(f.showvlinelabelborder,1),p.rotateVLineLabels=(0,c.pluckNumber)(f.rotatevlinelabels,0),p.vLineColor=(0,c.pluck)(f.vlinecolor,"333333"),p.vLineLabelColor=(0,c.pluck)(f.vlinelabelcolor),p.vLineThickness=(0,c.pluck)(f.vlinethickness,1),p.vLineAlpha=(0,c.pluckNumber)(f.vlinealpha,80),p.vLineLabelBgColor=(0,c.pluck)(f.vlinelabelbgcolor,"ffffff"),p.vLineLabelBgAlpha=(0,c.pluckNumber)(f.vlinelabelbgalpha,m?50:100),p.staggerLines=Math.max((0,c.pluckNumber)(f.staggerlines,2),2),p.staggerLines=u(p.staggerLines,1),p.trendlineValuesOnOpp=(0,c.pluck)(t.trendlineValuesOnOpp,t.trendlineValuesOnOpp,0),p.labelDisplay=(0,c.pluck)(t.labelDisplay,"auto").toLowerCase(),p.labelStep=(0,c.pluckNumber)(t.labelStep,0),p.labelStep=Math.round(p.labelStep),p.labelStep=u(p.labelStep,0),p.startPad=0,p.endPad=0,p._oriLabelStep=p.labelStep,p.showLimits=(0,c.pluckNumber)(t.showLimits,p.showAxisValues),p.showUpperLimit=t.showLimits,p.showDivLineValues=(0,c.pluckNumber)(t.showDivLineValues,p.showAxisValues),p.showCanvasBorder=g.getChildren("canvas")[0].config.showCanvasBorder?1:0,p.axisBreak=t.axisBreaks,p.isBreak=!!p.axisBreak,p.isBreak&&this._processAxisBreak(),a=(a=(0,c.getFirstValue)(t.axisNameBorderColor,c.BLANKSTRING))?(0,c.convertColor)(a,(0,c.pluckNumber)(t.axisNameBorderAlpha,t.axisNameAlpha,100)):c.BLANKSTRING,p.name=p.name||{},p.name.style={fontFamily:(0,c.pluck)(t.axisNameFont,t.outCanfontFamily),fontSize:(0,c.pluck)(t.axisNameFontSize,d(t.outCanfontSize))+"px",color:(0,c.convertColor)((0,c.pluck)(t.axisNameFontColor,t.outCancolor),(0,c.pluckNumber)(t.axisNameFontAlpha,t.axisNameAlpha,100)),fontWeight:(0,c.pluckNumber)(t.axisNameFontBold,1)?"bold":"normal",fontStyle:(0,c.pluckNumber)(t.axisNameFontItalic)?"italic":"normal",border:a||t.axisNameBgColor?(0,c.pluckNumber)(t.axisNameBorderThickness,1)+"px solid":o,borderColor:a,borderThickness:(0,c.pluckNumber)(t.axisNameBorderThickness,1),borderPadding:(0,c.pluckNumber)(t.axisNameBorderPadding,2),borderRadius:(0,c.pluckNumber)(t.axisNameBorderRadius,0),backgroundColor:t.axisNameBgColor?(0,c.convertColor)(t.axisNameBgColor,(0,c.pluckNumber)(t.axisNameBgAlpha,t.axisNameAlpha,100)):c.BLANKSTRING,borderDash:(0,c.pluckNumber)(t.axisNameBorderDashed,0)?(0,c.getDashStyle)((0,c.pluckNumber)(t.axisNameBorderDashLen,4),(0,c.pluckNumber)(t.axisNameBorderDashGap,2)):"none"},p.name.style.lineHeight=(0,c.setLineHeight)(p.name.style),i=(i=(0,c.getFirstValue)(f.trendvaluebordercolor,c.BLANKSTRING))?(0,c.convertColor)(i,(0,c.pluckNumber)(f.trendvalueborderalpha,f.trendvaluealpha,100)):c.BLANKSTRING,p.trend=p.trend||{},p.trend.trendStyle={fontFamily:(0,c.pluck)(f.trendvaluefont,t.outCanfontFamily),color:(0,c.pluck)(f.trendvaluefontcolor,t.trendlineColor,t.outCancolor,"333333"),fontSize:(0,c.pluckFontSize)(f.trendvaluefontsize,d(t.outCanfontSize))+"px",fontWeight:(0,c.pluckNumber)(f.trendvaluefontbold)?"bold":"normal",fontStyle:(0,c.pluckNumber)(f.trendvaluefontitalic)?"italic":"normal",border:i||f.trendvaluebgcolor?(0,c.pluckNumber)(f.trendvalueborderthickness,1)+"px solid":"",borderColor:i,borderThickness:(0,c.pluckNumber)(f.trendvalueborderthickness,1),borderPadding:(0,c.pluckNumber)(f.trendvalueborderpadding,2),borderRadius:(0,c.pluckNumber)(f.trendvalueborderradius,0),backgroundColor:f.trendvaluebgcolor?(0,c.convertColor)(f.trendvaluebgcolor,(0,c.pluckNumber)(f.trendvaluebgalpha,f.trendvaluealpha,100)):c.BLANKSTRING,borderDash:(0,c.pluckNumber)(f.trendvalueborderdashed,0)?(0,c.getDashStyle)((0,c.pluckNumber)(f.trendvalueborderdashlen,4),(0,c.pluckNumber)(f.trendvalueborderdashgap,2)):"none"},p.trend.trendStyle.lineHeight=(0,c.setLineHeight)(p.trend.trendStyle),p.labels=p.labels||{},p.lines=p.lines||{},p.band=p.band||{},n=(n=(0,c.getFirstValue)(f.labelbordercolor,c.BLANKSTRING))?(0,c.convertColor)(n,(0,c.pluckNumber)(f.labelborderalpha,f.labelalpha,100)):c.BLANKSTRING,p.labels.style={fontFamily:(0,c.pluck)(t.labelFont,t.outCanfontFamily),fontSize:(0,c.pluckNumber)(t.labelFontSize,d(t.outCanfontSize))+"px",fontWeight:(0,c.pluckNumber)(t.labelFontBold)?"bold":"normal",fontStyle:(0,c.pluckNumber)(t.labelFontItalic)?"italic":"normal",color:(0,c.convertColor)((0,c.pluck)(t.labelFontColor,t.outCancolor),(0,c.pluckNumber)(t.labelFontAlpha,100)),labelLink:f.labellink,border:n||f.labelbgcolor?(0,c.pluckNumber)(f.labelborderthickness,1)+"px solid":"",borderColor:n,borderThickness:(0,c.pluckNumber)(f.labelborderthickness,1),borderPadding:(0,c.pluckNumber)(f.labelborderpadding,2),borderRadius:(0,c.pluckNumber)(f.labelborderradius,0),backgroundColor:f.labelbgcolor?(0,c.convertColor)(f.labelbgcolor,(0,c.pluckNumber)(f.labelbgalpha,f.labelalpha,100)):c.BLANKSTRING,borderDash:(0,c.pluckNumber)(f.labelborderdashed,0)?(0,c.getDashStyle)((0,c.pluckNumber)(f.labelborderdashlen,4),(0,c.pluckNumber)(f.labelborderdashgap,2)):"none"},p.labels.style.lineHeight=(0,c.setLineHeight)(p.labels.style),p.numberFormatterFn=(0,c.pluck)(t.numberFormatterFn),p.apparentScrollPos=t.apparentScrollPos||p.apparentScrollPos,p.axisEndLabelDisplaySpace={left:0,right:0,top:0,bottom:0},p.isConfigured=!0,p._defaultForceDecimal=o,p._defaultDecimalPrecision=o,this.setScrollType("smart"),this.addToEnv("savedAxis",b&&(0,c.extend2)({},b))},a.setScrollType=function(e){var t=this.getVisibleConfig();"none"!==e&&"smart"!==e&&"always"!==e||(this.config.scrollType=e),this.setVisibleConfig(t.minValue,t.maxValue)},a.getScrollType=function(){return this.config.scrollType},a._processAxisBreak=function(){var e,t,a,i,o=this.config;for(o.breakPoints=[],a=0,i=(e=o.axisBreak.split("|")).length,t=0;a<i;a+=1)e[a]=e[a].split(","),isNaN(e[a][0])||isNaN(e[a][1])||(o.breakPoints[t]={start:(0,c.pluckNumber)(e[a][0]),end:(0,c.pluckNumber)(e[a][1]),length:(0,c.pluckNumber)(e[a][2],0)},t+=1);o.breakPoints.sort((function(e,t){return e.start-t.start})),o.hasBreakPoints=!0,this._validateBreakPoints()},a._validateBreakPoints=function(){var e,t=this.config,a=t.breakPoints,i=0,o=a.length;for(e=0;e<o;e+=1)i+=a[e].end-a[e].start;t.totalBreakAmount=i},a._getRelativeBreakValue=function(e){var t,a=this.config.breakPoints,i=a.length,o=0;for(t=0;t<i;t+=1){if(e>=a[t].start&&e<=a[t].end)return a[t].start-o;if(e<a[t].start)break;o+=a[t].end-a[t].start}return e-o},a._getRealBreakValue=function(e){var t,a=e,i=this.config.breakPoints,o=i.length;for(t=0;t<o;t+=1)if(a>=i[t].start)a+=i[t].end-i[t].start;else if(a<i[t].start)return a;return a},a._adjustNumberFormatter=function(e){var t,a,i=this.config,n=this.getFromEnv("chart").getFromEnv("number-formatter"),r=i._defaultDecimalPrecision,s=i._defaultForceDecimal,l=0;a=i.isVertical||"yAxis"===i.numberFormatterFn?(n.Y[i.axisIndex]||n.Y[0]).yAxisLabelConf:n.paramX,r!==o?a.decimalprecision=r:i._defaultDecimalPrecision=a.decimalprecision,s!==o?a.forcedecimals=s:i._defaultForceDecimal=a.forcedecimals,parseInt(e,10)>0||(t=e.toString().split(".")[1])&&((l=t.match(/^[0]*/)[0].length)+1>a.decimalprecision&&(a.forcedecimals=1),a.decimalprecision=Math.max(l+1,a.decimalprecision))},a._isZoomed=function(){var e=this.getFromEnv("chart").config.viewPortConfig;return this.config.isVertical?1!==e.scaleY:1!==e.scaleX},a._getIntervalArr=function(e){var t,a,i,o,n,r,s,l=this.config,h=l.labels,d=l.axisRange.tickInterval*(e&&e.step||1),p=[];if(t=this.getVisibleConfig(),"polar"===this.getFromEnv("chart").config.axisType?(a=t.minValue,i=t.maxValue):(a=t.minValue-(e&&e.minPad||0),i=t.maxValue+(e&&e.maxPad||0)),(o=g(a,d))===(n=g(i-i%d,d)))return[o];if(h.drawNormalVal)for(r=(0,c.toPrecision)(o+d,10);r<n;r=(0,c.toPrecision)(r+d,10))s=l.hasBreakPoints?(0,c.toPrecision)(this._getRealBreakValue(r),10):(0,c.toPrecision)(r,10),p.push(s);return h.drawLimitVal&&p.push(n,o),-1!==p.indexOf(0)&&p.splice(p.indexOf(0),1),l.showZeroPlane&&o<=0&&n>=0&&p.push(0),p.sort((function(e,t){return e-t}))},a._disposeScrollBar=function(){var e=this.config;e.scrollBarDrawn&&(this.getLinkedItem("scrollBar").hide(),e.scrollBarDrawn=!1)},a.addComponentInfo=function(e,t){this.getFromEnv("componentInfo")[f(e)].push(t)},a.getComponentInfo=function(e){return this.getFromEnv("componentInfo")[f(e)]},a.clearComponentInfo=function(){var e,t=this.getFromEnv("componentInfo");for(e in t)t.hasOwnProperty(e)&&(t[e]=[])},a.draw=function(){var e,t,a,i,o,n,r,s,l,c,h,d,g,u,f=this.config,v=f.canvas,b=this.getFromEnv("chart"),x=b.config,C=f.isVertical,k=x.viewPortConfig,w=this.getChildren().limitUpdater,L=f.viewPortRatio||{},y=v.canvasLeft||x.canvasLeft,A=v.canvasTop||x.canvasTop,F=v.canvasWidth||x.canvasWidth,T=v.canvasHeight||x.canvasHeight,P=f.axisContainer,N=f.axisLabelContainerTop,E=f.axisPlotLineContainer,S=f.axisPlotLineContainerTop,D=f.vlineLabelContainer,B=f.axisBandContainer,V=f.axisNameContainer,_=f.axisTrendContainerTop,H=f.axisTrendContainerBottom,I=f.axisTrendLabelContainer,R=f.axisAxisLineContainer,M=f.axisAxisLineContainerBottom,O=f.divLineThickness||0,G=b.getChildContainer(),W=G.axisBottomGroup,z=G.axisTopGroup,j=[];this.clearComponentInfo(),d=k.y*k.scaleY,g=k.x*k.scaleX,u=C?y+","+(A-O)+","+F+","+(T+2*O):y-O+","+A+","+(F+2*O)+","+T,e=this.getContainer("axisBandGroup")||this.addContainer("axisBandGroup",p("dataset-Band-group",W,this)),t=this.getContainer("axisPlotLineGroup")||this.addContainer("axisPlotLineGroup",p("dataset-Line-group",W,this)),a=this.getContainer("axisPlotLineGroupTop")||this.addContainer("axisPlotLineGroupTop",p("dataset-Line-group-top",z,this)),o=this.getContainer("axisNameGroup")||this.addContainer("axisNameGroup",p("dataset-Name-group",W,this)),i=this.getContainer("axisLineGroup")||this.addContainer("axisLineGroup",p("axis-Line-group",z,this)),s=this.getContainer("axisTrendGroupTop")||this.addContainer("axisTrendGroupTop",p("dataset-Trend-group-top",z,this)),n=this.getContainer("axisLabelGroup")||this.addContainer("axisLabelGroup",p("dataset-Label-group",W,this)),r=this.getContainer("axisLabelGroupTop")||this.addContainer("axisLabelGroupTop",p("dataset-Label-group",z,this)),l=this.getContainer("axisTrendGroupBottom")||this.addContainer("axisTrendGroupBottom",p("dataset-Trend-group-bottom",W,this)),H||(f.axisTrendContainerBottom=H=p("dataset-axis-trend-bottom",l,this)),H.attr({"clip-rect":y+","+A+","+F+","+T}),P||(f.axisContainer=P=p("dataset-axis",n,this)),N||(f.axisLabelContainerTop=N=p("dataset-top-label",r,this)),R||(f.axisAxisLineContainer=R=p("axis-line-tick",i,this)),D||(f.vlineLabelContainer=D=p("axis-vline-label",i,this)),B?j.push({el:B,attrs:{"clip-rect":y+","+A+","+F+","+T},animType:"linear",animConfig:[{syncWith:"initial",start:0,initial:1}]}):(f.axisBandContainer=B=p("dataset-axis-bands",e,this),B.attr({"clip-rect":y+","+A+","+F+","+T})),E?j.push({el:E,attrs:{"clip-rect":u},animType:"linear",animConfig:[{syncWith:"initial",start:0,initial:1}]}):(f.axisPlotLineContainer=E=p("dataset-axis-lines",t,this),E.attr({"clip-rect":u})),S?j.push({el:S,attrs:{"clip-rect":u},animType:"linear",animConfig:[{syncWith:"initial",start:0,initial:1}]}):(f.axisPlotLineContainerTop=S=p("dataset-axis-lines-top",a,this),S.attr({"clip-rect":u})),_?j.push({el:_,attrs:{"clip-rect":y+","+A+","+F+","+T},animType:"linear",animConfig:[{syncWith:"initial",start:0,initial:1}]}):(f.axisTrendContainerTop=_=p("dataset-axis-trend-top",s,this),_.attr({"clip-rect":y+","+A+","+F+","+T})),I||(f.axisTrendLabelContainer=I=p("dataset-axis-trend-label",s,this)),V||(f.axisNameContainer=V=p("dataset-axis-name",o,this)),M||(f.axisAxisLineContainerBottom=M=p("axis-line-tick-bottom",W,this)),L.scaleX&&L.scaleY&&(L.scaleX!==k.scaleX||L.scaleY!==k.scaleY)?(L.scaleX=k.scaleX,L.scaleY=k.scaleY,this._drawComponents()):(C?(h=d-k.y*k.scaleY,D.transform("t0,"+h),P.transform("t0,"+h),N.transform("t0,"+h),E.transform("t0,"+h),S.transform("t0,"+h),B.transform("t0,"+h),_.transform("t0,"+h),H.transform("t0,"+h)):(c=g-k.x*k.scaleX,D.transform("t"+c+",0"),P.transform("t"+c+",0"),N.transform("t"+c+",0"),E.transform("t"+c+",0"),S.transform("t"+c+",0"),B.transform("t"+c+",0"),_.transform("t"+c+",0"),H.transform("t"+c+",0")),this._drawComponents()),this.addExtEventListener("animationcomplete",(function(){w&&w.forEach(m)}),this.getFromEnv("animationmanager")),this.addToEnv("tempAxis",{canvasHeight:this.config.canvas.canvasHeight,canvasWidth:this.config.canvas.canvasWidth,canvasLeft:this.config.canvas.canvasLeft,canvasRight:this.config.canvas.canvasRight,canvasBottom:this.config.canvas.canvasBottom,canvasTop:this.config.canvas.canvasTop,visibleRange:this.getVisibleConfig(),visibleLength:this.getVisibleLength(),axisLength:this.config.axisDimention.axisLength,axisOpposite:this.config.axisDimention.opposite,axisY:this.config.axisDimention.y,axisX:this.config.axisDimention.x,axisRangeMin:this.config.axisRange.min,axisRangeMax:this.config.axisRange.max,axisTickInterval:this.config.axisRange.tickInterval,axisIsReverse:this.config.isReverse,axisIsVertical:this.config.isVertical,axisHasBreakPoints:this.config.hasBreakPoints,viewPortScaleY:this.getFromEnv("chart").config.viewPortConfig.scaleY,viewPortY:this.getFromEnv("chart").config.viewPortConfig.y,viewPortScaleX:this.getFromEnv("chart").config.viewPortConfig.scaleX,viewPortX:this.getFromEnv("chart").config.viewPortConfig.x,chartHeight:this.getFromEnv("chart").config.canvasHeight,chartWidth:this.getFromEnv("chart").config.canvasWidth,chartLeft:this.getFromEnv("chart").config.canvasLeft,chartRight:this.getFromEnv("chart").config.canvasRight,chartBottom:this.getFromEnv("chart").config.canvasBottom,chartTop:this.getFromEnv("chart").config.canvasTop,pvr:this.config.pvr,refVal:this.config.refVal,refVisibleVal:this.config.refVisibleVal})},a.getBreakPoints=function(){var e=this.config;return!!e.hasBreakPoints&&e.breakPoints},a.getValue=function(e,t){var a=this.config;return(t&&t.wrtVisible?a.refVisibleVal:a.refVal)+(e-a.refPx)/this.config.pvr},a.getVisibleLength=function(){var e=this.getVisibleConfig();return Math.abs(e.maxValue-e.minValue)},a.setAxisPadding=function(e,t){void 0===e&&(e=0),void 0===t&&(t=0);var a=this.config,i=e,o=t;i=a.startPad=Math.max(a.startPad,i),o=a.endPad=Math.max(a.endPad,o),0===a.oriCatLen&&(0===i&&(i=.5),0===o&&(o=.5)),a.hasCategory?a.oriCatLen>=0&&this.setAxisRange({max:a.oriCatLen+o,min:-i}):a.originalMax&&a.originalMin&&this.setDataLimit(a.originalMax,a.originalMin)},a.setAxisConfig=function(e){var t,a=this.config;for(t in e)e.hasOwnProperty(t)&&(a[t]=e[t])},a.getAxisConfig=function(e){var t=this.config;return e?t[e]:t},a.setAxisRange=function(e){var t,a,i,o=this.config,n=o.axisRange;for(i in e)e.hasOwnProperty(i)&&(n[i]=e[i]);o.isReverse?o.refVal=n.max:o.refVal=n.min,this.setVisibleConfig(n.min,n.max),this.getFromEnv("tempAxis")&&((a={max:(t=this.getFromEnv("tempAxis")).axisRangeMax,min:t.axisRangeMin,tickInterval:t.axisTickInterval}).max!==n.max||a.min!==n.min||a.tickInterval!==n.tickInterval?o.rangeChanged=!0:o.rangeChanged=!1)},a.setAxisDimention=function(e){var t,a=this.config,i=this.getFromEnv("chart").config,o=a.axisDimention||(a.axisDimention={});o.opposite=(0,c.pluckNumber)(e.opposite,o.opposite),o.x=(0,c.pluckNumber)(e.x,i.canvasLeft,o.x),o.y=(0,c.pluckNumber)(e.y,i.canvasTop,o.y),o.axisLength=(0,c.pluckNumber)(e.axisLength,o.axisLength),a.isVertical?a.refPx=o.y:a.refPx=o.x,this.getPVR(),this.getFromEnv("tempAxis")&&((t=this.getFromEnv("tempAxis")).axisLength!==o.axisLength||t.axisOpposite!==o.opposite||t.axisY!==o.y||t.axisX!==o.x?a.dimensionChanged=!0:a.dimensionChanged=!1)},a.setDataLimit=function(e,t){var a,i,o,n,l,h=this.config,d=e,p=t,g=h.axisRange,u=h.isPercent?100:h.axisMaxValue,m=h.isPercent?0:h.axisMinValue,f=h.numDivLines,v=h.setAdaptiveMin,b=h.adjustDiv,x=h.startPad||0,C=h.endPad||0,k=h.trendLines||h.vTrendLines,w=k&&k[0]&&k[0].line;n=(0,r["default"])(w,(function(e){return e.startvalue})),l=(0,s["default"])(w,(function(e){return e.endvalue})),p=(0,r["default"])([n,p]),d=(0,s["default"])([l,d]),h.originalMax=d,h.originalMin=p,d=h.isPercent?100:d+C,p=h.isPercent?0:p-x,a=i=!v,h.hasBreakPoints&&(d-=h.totalBreakAmount),o=(0,c.getAxisLimits)((0,c.pluckNumber)(d,u),(0,c.pluckNumber)(p,m),u,m,i,a,f,b),this.setAxisRange({max:Number((0,c.toPrecision)(o.Max,10)),min:Number((0,c.toPrecision)(o.Min,10)),tickInterval:Number((0,c.toPrecision)(o.divGap,10))}),this._adjustNumberFormatter(g.tickInterval),0===g.tickInterval&&(g.tickInterval=1)},a.setVisibleConfig=function(e,t){var a,i,o,n=this.config,r=n.axisRange,s=this.getScrollType(),l=n.maxZoomLimit;return!(e>t)&&(o=(r.max-r.min)/(t-e),!(l&&o>l)&&(n.minVisibleValue=e,n.maxVisibleValue=t,n.isReverse?n.refVisibleVal=t:n.refVisibleVal=e,"always"===s?this.setScrollEnabled(!0):"smart"===s?this._isZoomed()?this.setScrollEnabled(!0):this.setScrollEnabled(!1):"none"===s&&this.isScrollEnabled()&&this.setScrollEnabled(!1),this.getPVR(),this.fireEvent("visiblerangeset",{minValue:n.minVisibleValue,maxValue:n.maxVisibleValue}),this.getFromEnv("tempAxis")&&(this.asyncDraw(),a=-this.getTranslation(),i=n.isVertical?["T",0,",",a].join(""):["T",a,",",0].join(""),n.axisContainer.attr({transform:i}),n.axisBandContainer.attr({transform:i}),n.axisPlotLineContainer.attr({transform:i}),n.axisTrendContainerTop.attr({transform:i})),!0))},a.getVisibleConfig=function(){var e=this.config;return{minValue:e.minVisibleValue,maxValue:e.maxVisibleValue}},a.getPVR=function(){var e=this.config,t=this.getVisibleConfig(),a=t.maxValue-t.minValue,i=(e.axisDimention||{}).axisLength/a;return i&&(e.isReverse?e.pvr=-i:e.pvr=i),e.pvr},a.getPixel=function(e,t){var a=this.config,i=e,o=t&&t.wrtVisible,n=o?a.refVisibleVal:a.refVal,r=this.getFromEnv("tempAxis"),s=a.pvr*this.getFromEnv("chartConfig").viewPortConfig.scaleX;return t&&t.preValue&&r&&(n=o?r.refVisibleVal:r.refVal,s=this.getOldPVR()),s?(a.hasBreakPoints&&(i=this._getRelativeBreakValue(i)),(0,c.toPrecision)(a.refPx+(i-n)*s,2)):0},a.getLimit=function(){var e=this.config.axisRange;return{min:e.min,max:e.max,tickInterval:e.tickInterval}},a.getOldPVR=function(){var e=this.getFromEnv("tempAxis");return e?e.pvr:this.config.pvr},a.hide=function(){var e=this.config;e.axisContainer&&(e.axisLabelContainerTop.hide(),e.axisContainer.hide(),e.axisPlotLineContainer.hide(),e.axisPlotLineContainerTop.hide(),e.axisBandContainer.hide(),e.axisNameContainer.hide(),e.axisTrendContainerTop.hide(),e.axisTrendContainerBottom.hide(),e.axisTrendLabelContainer.hide(),e.axisAxisLineContainer.hide(),e.axisAxisLineContainerBottom.hide())},a.show=function(){var e=this.config;e.axisContainer&&(e.axisLabelContainerTop.show(),e.axisContainer.show(),e.axisPlotLineContainer.show(),e.axisPlotLineContainerTop.show(),e.axisBandContainer.show(),e.axisNameContainer.show(),e.axisTrendContainerTop.show(),e.axisTrendContainerBottom.show(),e.axisTrendLabelContainer.show(),e.axisAxisLineContainer.show(),e.axisAxisLineContainerBottom.show())},a.getTranslation=function(){var e=this.config;return this.getPixel(e.refVisibleVal)-e.refPx},a.setScrollEnabled=function(e){this.config.scrollEnabled=e,e?(this.getFromEnv("chart")._createToolBoxGantt(),this.getFromEnv("tempAxis")&&this.asyncDraw()):this._disposeScrollBar()},a.isScrollEnabled=function(){return this.config.scrollEnabled},a.manageProcessScroll=function(e){var t,a=this.config,i=a.totalWidth||0,n=a.totalVisiblelWidth;i>n&&(t=(i-n)*(1-e),this.translateAxis(t,o))},a.translateAxis=function(e,t){var a,i,n=this.config,r=this.getContainer("ganttPlotLineContainer"),s=this.getContainer("ganttPlotHoverBandContainer"),l=n.lastTranslate||(n.lastTranslate={x:0,y:0}),c=this.getContainer("labelContainer"),h=this.getContainer("headerContainer"),d=this.getContainer("hotContainer");a=e!==o?e-l.x:0,i=t!==o?t-l.y:0,l.x=e!==o?e:l.x,l.y=t!==o?t:l.y,c&&c.translate(a,i),h&&h.translate(a,i),n.labelContainer&&n.labelContainer.translate(a,i),d&&d.translate(a,i),n.headerContainer&&n.headerContainer.translate(a,0),n.isVertical?(r&&r.translate(0,i),s&&s.translate(0,i)):(r&&r.translate(a,0),s&&s.translate(a,0),this.setAxisConfig({animateAxis:!1}),n.drawTrendLines&&this._drawTrendLine(),this.setAxisConfig({animateAxis:!0}))},a.resetTransletAxis=function(){var e,t=this.config,a=this.getContainer("labelContainer"),i=this.getContainer("headerContainer"),o=this.getContainer("hotContainer");e={transform:"t0,0"},t.lastTranslate={x:0,y:0},a&&a.attr(e),i&&i.attr(e),t.labelContainer&&t.labelContainer.attr(e),t.headerContainer&&t.headerContainer.attr(e),t.ganttPlotLineContainer&&t.ganttPlotLineContainer.attr(e),t.ganttPlotHoverBandContainer&&t.ganttPlotHoverBandContainer.attr(e),o&&o.attr(e)},t}(l.ComponentInterface);t["default"]=b},1180:function(e,t,a){"use strict";t.__esModule=!0,t["default"]=void 0;var i=function(){return[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"axis",startEnd:{start:0,end:.3}}]},o={"initial.axis.GanttProcess":function(){return{"path.appearing":i,"rect.appearing":i,"text.appearing":i}},"initial.axis.GanttTime":function(){return{"path.appearing":i,"rect.appearing":i,"text.appearing":i}}};t["default"]=o},1181:function(e,t,a){"use strict";var i=a(212),o=a(208);t.__esModule=!0,t["default"]=void 0;var n=o(a(229)),r=i(a(1178)),s=a(215),l=s.preDefStr.POSITION_START,c=s.preDefStr.POSITION_TOP,h=s.preDefStr.POSITION_END;var d=function(e){function t(){return e.apply(this,arguments)||this}(0,n["default"])(t,e);var a=t.prototype;return a.getName=function(){return"GanttTimeCategory"},a.configure=function(t){var a,i=this.config,o=this.getFromEnv("chart"),n=this.getFromEnv("dataSource"),r=o.getFromEnv("color-manager"),l=n.chart;e.prototype.configure.call(this,t),a=i.rawAttr,i.plotLineColor=i.lineColor=(0,s.convertColor)((0,s.pluck)(l.ganttlinecolor,r.getColor("gridColor")),(0,s.pluckNumber)(l.ganttlinealpha,100)),i.plotLineThickness=i.lineThickness=(0,s.pluckNumber)(l.ganttlinethickness,1),i.plotLineDashStyle=i.lineDashStyle=(0,s.pluckNumber)(l.ganttlinedashed,0)?(0,s.getDashStyle)((0,s.pluckNumber)(l.ganttlinedashlen,1),l.ganttlinedashgap,i.lineThickness):"none",i.hoverColor=(0,s.pluck)(l.categoryhoverbandcolor,l.hoverbandcolor,r.getColor("gridColor")),i.hoverAlpha=(0,s.pluckNumber)(l.categoryhoverbandalpha,l.hoverbandalpha,30),i.useHover=(0,s.pluckNumber)(l.showcategoryhoverband,l.showhoverband,l.showhovereffect,1),i.usePlotHover=(0,s.pluckNumber)(l.showganttpaneverticalhoverband),i.trendlinesDashLen=(0,s.pluckNumber)(a.trendlinesDashLen,3),i.trendlinesDashGap=(0,s.pluckNumber)(a.trendlinesDashGap,3),i.gridLineHeaderPath="",i.gridLinePath=""},a.setCategory=function(e){var t,a,i,o,n,l,c,h=this.getFromEnv("number-formatter"),d=this.config,p=d.startPad||0,g=d.endPad||0,u=Infinity,m=-Infinity;if(d.categories={},e){for(l in d.hasCategory=1,a=d.categories.category=(0,s.extend2)({},e),(0,r.extractAttribToEnd)(a,{}),a)if(a.hasOwnProperty(l)&&"_attrib"!==l)for(c=0,t=a[l].category.length;c<t;c+=1)i=a[l].category[c],o=h.getDateValue(i.start).ms,n=h.getDateValue(i.end).ms,isNaN(o)&&(o=void 0),o>m&&(m=o),o<=u&&(u=o),isNaN(n)&&(n=void 0),n>m&&(m=n),n<=u&&(u=n);this.setAxisRange({min:Number((0,s.toPrecision)(u-p,10)),max:Number((0,s.toPrecision)(m+g,10)),tickInterval:Number((0,s.toPrecision)(1,10))})}else d.hasCategory=0},a.placeAxis=function(e){var t,a,i,o,n,r,l,c,h,d,p,g,u,m,f=this.config,v=this.getFromEnv("chart"),b=v.config,x=this.getFromEnv("number-formatter"),C=this.getFromEnv("smartLabel"),k=f.labels.style,w=0,L={top:0,bottom:0},y=0,A=f.trend.trendStyle,F=f.vTrendLines,T=f.useEllipsesWhenOverflow,P=0,N=0;if(C.useEllipsesOnOverflow(b.useEllipsesWhenOverflow),C.setStyle({fontSize:k.fontSize,fontFamily:k.fontFamily,lineHeight:k.lineHeight,fontWeight:k.fontWeight}),f.maxTopSpaceAvailable=b.canvasTop,f.hasCategory)for(i in t=f.categories.category)if(t.hasOwnProperty(i)&&"_attrib"!==i){for(r in w=0,a=t[i].category)a.hasOwnProperty(r)&&"_attrib"!==r&&((o=a[r]).drawLabel=(0,s.parseUnsafeString)(o.label||o.name),l=o._attrib,c={fontFamily:(0,s.pluck)(l.fontfamily,k.fontFamily).replace(/px/i,"")+"px",fontSize:(0,s.pluck)(l.fontsize,k.fontSize),fontWeight:(0,s.pluck)(1===Number(l.isbold)||void 0===l.isbold?"bold":void 0,k.fontWeight),fontStyle:(0,s.pluck)(l.isitalic?"italic":void 0,k.fontStyle)},(0,s.setLineHeight)(c),C.setStyle(c),(n=C.getOriSize(o.drawLabel)).height>w&&(p=n,w=n.height));t[i]._attrib.topPos=y,y+=p.height+8,t[i]._attrib.bottomPos=y}if(m=e-y,f.drawTrendLines&&f.drawTrendLabels&&F&&f.isActive)for(C.setStyle({fontSize:A.fontSize,fontFamily:A.fontFamily,lineHeight:A.lineHeight,fontWeight:A.fontWeight}),f.trendBottomPadding=-1,r=0,h=F.length;r<h;r+=1)for(i=0,d=F[r].line.length;i<d;i+=1)o=(g=F[r].line[i]).origText||g.displayvalue||g.endvalue||g.startvalue||"",o=(0,s.parseUnsafeString)(o),g.startvalue=g.start&&x.getDateValue(g.start).ms,g.endvalue=g.end&&x.getDateValue(g.end).ms,g.origText=o,m-((u=C.getSmartText(o,v.canvasWidth,A.lineHeight,T)).height+2)<0?g.displayvalue="":(g.displayvalue=u.text,P=P<u.height?u.height:P),u.tooltext?g.valueToolText=u.tooltext:delete g.valueToolText;return f.totalHeight=y,P>0&&(N+=P+Math.abs(f.trendBottomPadding||0)),y=y>e?e:y,L.top+=y,L.bottom+=N,b.categorySpaceUsed=y,L},a._drawCategories=function(){var e,t,a,i,o,n,r,s,l,c=this.config,h=(c.axisDimention||{}).y,d=c.totalHeight||0,p=this.getFromEnv("chart"),g=p.config,u=p.getFromEnv("animationManager"),m=p.getFromEnv("number-formatter"),f=c.canvas,v=c.gridArr||(c.gridArr=[]),b=f.canvasLeft||g.canvasLeft,x=f.canvasTop||g.canvasTop,C=f.canvasHeight||g.canvasHeight,k=f.canvasWidth||g.canvasWidth,w=p.getChildContainer("axisBottomGroup"),L=0,y=this.getContainer("ganttPlotHoverBandContainerParent"),A=this.getContainer("ganttPlotHoverBandContainer"),F=this.getContainer("ganttPlotLineContainer"),T=this.getContainer("labelContainer"),P=this.getContainer("labelBackContainer"),N=this.getContainer("labelLineContainer"),E=this.getContainer("labelTextContainer");if(r=(r=Math.min(d,x-(c.maxTopSpaceAvailable||0)))>0?r:0,y||(y=this.addContainer("ganttPlotHoverBandContainerParent",u.setAnimation({el:"group",attr:{name:"gantt-plot-band-container-parent"},container:w,component:this}))),this.addContainer("ganttPlotHoverBandContainer",u.setAnimation({el:A||"group",attr:{name:"gantt-plot-band-container","clip-rect":b+","+x+","+k+","+C},container:y,component:this})),this.addContainer("ganttPlotLineContainer",u.setAnimation({el:F||"group",attr:{name:"gantt-plot-line-container","clip-rect":b+","+x+","+k+","+C,transform:"t0,0"},container:w,component:this})),T=this.addContainer("labelContainer",u.setAnimation({el:T||"group",attr:{name:"gantt-label-container","clip-rect":b+","+(x-r)+","+k+","+r,transform:"t0,0"},container:w,component:this})),P||(P=this.addContainer("labelBackContainer",u.setAnimation({el:"group",attr:{name:"gantt-label-back-container"},container:T,component:this}))),N||(N=this.addContainer("labelLineContainer",u.setAnimation({el:"group",attr:{name:"gantt-label-line-container"},component:this,container:T}))),E||(E=this.addContainer("labelTextContainer",u.setAnimation({el:"group",attr:{name:"gantt-label-text-container"},container:T,component:this}))),c.gridLinePath="",c.gridLineHeaderPath="",c.hoverElemsArr=[],c.labelHoverEventName={click:"CategoryClick",rollOver:"CategoryRollOver",rollOut:"CategoryRollOut"},c.hasCategory)for(e in t=c.categories.category)if(t.hasOwnProperty(e)&&"_attrib"!==e)for(i in a=t[e].category,n=void 0,v=c.gridArr=[],a)s=m.getDateValue(a[i].start).ms,l=m.getDateValue(a[i].end).ms,!a.hasOwnProperty(i)||"_attrib"===i||isNaN(s)||isNaN(l)||(n=(o={elem:a[i],elemIndex:L,pos:L,dimension:{left:n||this.getPixel(s),right:this.getPixel(l),top:h-d+t[e]._attrib.topPos,bottom:h-d+t[e]._attrib.bottomPos},type:"category",isHeader:!1}).dimension.right,this._drawProcessAndDataTableElement(o),L+=1,v.push({x:o.dimension.left}));this._drawGridLine(),this._disposeExtraProcessAndDataTableElement(L)},a._drawComponents=function(){var e=this.config,t=this.getFromEnv("chartConfig");this._drawCategories(),e.lastTranslate={x:0,y:0},this.translateAxis(-t.viewPortConfig.x*t.viewPortConfig.scaleX,0),e.drawPlotlines&&this._drawPlotLine(),function(e){var t,a,i,o,n,r,d,p,g,u,m,f,v,b,x,C,k,w,L,y,A,F,T,P,N=e.config,E=N.isVertical,S=N.isOpposit,D=e.getFromEnv("animationManager"),B=N.axisIndex,V=e.getFromEnv("number-formatter"),_=N.axisRange,H=_.max,I=_.min,R=N.trend.trendStyle,M=N.labelPadding,O=N.axisTrendLabelContainer,G={fontFamily:R.fontFamily,fontSize:R.fontSize,lineHeight:R.lineHeight,fontWeight:R.fontWeight,fontStyle:R.fontStyle},W=N.vTrendLines,z=N.trendLines,j=N.drawTrendLabels,X=N.axisDimention||{},K=e.getFromEnv("chartConfig"),U=e.getFromEnv("chart").getChildren("canvas")[0],Y=U.config.canvasBottom||K.canvasBottom,Z=U.config.canvasLeft||K.canvasLeft,J=U.config.canvasRight||K.canvasRight,q=[],$=e.getVisibleConfig(),Q=e.getGraphicalElement("trendlabels")||[];for(o=0;o<Q.length;o++)Q[o].remove();if(N.hasBreakPoints&&($.minValue=e._getRealBreakValue($.minValue),$.maxValue=e._getRealBreakValue($.maxValue)),(i=e._isZoomed())?(t=Math.max(e.getPixel($.minValue,{wrtVisible:!0}),e.getPixel($.maxValue,{wrtVisible:!0})),a=Math.min(e.getPixel($.minValue,{wrtVisible:!0}),e.getPixel($.maxValue,{wrtVisible:!0}))):($.minValue=I,$.maxValue=H),W?F=S?(X.opposite||Y)-(N.trendBottomPadding||0):(X.y||Y)+(N.trendBottomPadding||0):(F=S?(X.opposite||Z)+(M||0):(X.x||Z)-(M||0),T=S?(X.x||J)+(M||0):(X.opposite||J)+(M||0)),u=z||W)for(r=0,d=u.length;r<d;r+=1)for(o=0,n=u[r].line&&u[r].line.length;o<n;o+=1)P=E?"yAxis":"xAxis",(b=u[r].line[o]).startvalue||b.value||0,k=V.getCleanValue((0,s.pluck)(b.startvalue,b.value,0)),w=Number(b.endvalue)||void 0,p=(0,s.getValidValue)((0,s.parseUnsafeString)((0,s.pluck)(u[r].line[o].tooltext,u[0].tooltext,N.trendlineToolText),!1)),p=(0,s.parseTooltext)(p,[7,15,16,17,18,19],{startValue:k,startDataValue:V[P](k,B),endValue:w||k,endDataValue:V[P](w||k,B),axisName:N.axisName},b),k>H||k<I||w>H||w<I||(W?(x=(0,s.pluck)((0,s.parseUnsafeString)(b.displayvalue),b.start,""),L=e.getPixel(N.hasBreakPoints?e._getRelativeBreakValue(k):k,{wrtVisible:!0}),m=(0,s.pluckNumber)(b.istrendzone,N.isTrendZone,1),y=w?e.getPixel(N.hasBreakPoints?e._getRelativeBreakValue(w):w,{wrtVisible:!0}):0,void 0!==w&&""!==w&&w!==k&&m?(f={fill:(0,s.convertColor)((0,s.pluck)(b.color,N.trendlineColor),(0,s.pluck)(b.alpha,N.trendlineAlpha,40)),"stroke-width":0},v={fill:(0,s.convertColor)((0,s.pluck)(b.color,R.color),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),"vertical-align":c,text:x,x:L+(y-L)/2,y:F}):(A=(0,s.pluckNumber)(b.thickness,N.trendlineThickness,1),f={stroke:(0,s.convertColor)((0,s.pluck)(b.color,N.trendlineColor),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),"stroke-width":A,"stroke-dasharray":(0,s.pluck)(b.dashed,N.trendlinesAreDashed)===s.ONESTRING?(0,s.getDashStyle)((0,s.pluckNumber)(b.dashlen,N.trendlinesDashLen),(0,s.pluckNumber)(b.dashgap,N.trendlinesDashGap)):"none"},v={fill:(0,s.convertColor)((0,s.pluck)(b.color,R.color),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),"vertical-align":c,text:x,x:w?y:L,y:F})):z&&(x=(0,s.pluck)((0,s.parseUnsafeString)(b.displayvalue),b.start,""),C=(0,s.pluckNumber)(b.valueonright,0),m=(0,s.pluckNumber)(b.istrendzone,N.isTrendZone,0),L=e.getPixel(k,{wrtVisible:!0}),y=w?e.getPixel(w,{wrtVisible:!0}):0,void 0!==w&&""!==w&&w!==k&&m?(f={fill:(0,s.convertColor)((0,s.pluck)(b.color,N.trendlineColor),(0,s.pluck)(b.alpha,N.trendlineAlpha,40)),"stroke-width":0},v={"text-anchor":C?l:h,fill:(0,s.convertColor)((0,s.pluck)(b.color,R.color),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),text:x,x:C?T:F,y:L+(y-L)/2}):(A=(0,s.pluckNumber)(b.thickness,N.trendlineThickness,1),f={stroke:(0,s.convertColor)((0,s.pluck)(b.color,N.trendlineColor),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),"stroke-width":A,"stroke-dasharray":(0,s.pluck)(b.dashed,N.trendlinesAreDashed)===s.ONESTRING?(0,s.getDashStyle)((0,s.pluckNumber)(b.dashlen,N.trendlinesDashLen),(0,s.pluckNumber)(b.dashgap,N.trendlinesDashGap)):"none"},v={"text-anchor":C?l:h,fill:(0,s.convertColor)((0,s.pluck)(b.color,R.color),(0,s.pluck)(b.alpha,N.trendlineAlpha,99)),text:x,x:C?T:F,y:w&&C?y:L}),i&&(!j||v.y>t||v.y<a)&&(v.text="")),v["text-bound"]=v.text?[R.backgroundColor,R.borderColor,R.borderThickness,R.borderPadding,R.borderRadius,R.borderDash]:[],g=D.setAnimation({el:"text",attr:v,css:G,container:O,component:e}).show(),N.showTooltip&&p||(p=""),e.addComponentInfo("trend",{marker:{isZone:m,startValue:k,endValue:w,fill:f.fill,stroke:f.stroke,strokeWidth:f["stroke-width"],strokeDashArray:f["stroke-dasharray"],shapeRendering:f["shape-rendering"]},label:{fill:v.fill,text:x,textAnchor:v["text-anchor"],textBound:v["text-bound"],valueOnRight:(0,s.pluckNumber)(b.valueonright,0),toolText:p},showOnTop:(0,s.pluckNumber)(b.showontop)}),q.push(g));q.length&&e.addGraphicalElement("trendlabels",q)}(this),e.drawTrendLines&&this._drawTrendLine()},t}(r["default"]);t["default"]=d},1182:function(e,t,a){"use strict";var i=a(212),o=a(208);t.__esModule=!0,t["default"]=void 0;var n=o(a(229)),r=i(a(1183)),s=a(215),l=o(a(1185)),c=a(223),h=a(227),d=function(e){return function(t){e.plotEventHandler(this,t,"MilestoneClick")}},p=function(e){return function(t){var a=this.data("dataObj"),i=a.config;e.plotEventHandler(this,t,"MilestoneRollOver"),i.showHoverEffect&&a.graphics.element.attr({fill:i.hoverFillColor,stroke:i.hoverBorderColor,"fill-opacity":i.hoverFillAlpha,"stroke-opacity":i.hoverBorderAlpha})}},g=function(e){return function(t){var a=this.data("dataObj"),i=a.config;e.plotEventHandler(this,t,"MilestoneRollOut"),i.showHoverEffect&&a.graphics.element.attr({fill:i.fillColor,stroke:i.borderColor,"fill-opacity":i.fillAlpha,"stroke-opacity":i.borderAlpha})}};(0,c.addDep)({name:"milestoneAnimation",type:"animationRule",extension:l["default"]});var u=function(e){function t(){return e.apply(this,arguments)||this}(0,n["default"])(t,e);var a=t.prototype;return a.getName=function(){return"milestone"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.showpercentlabel=0,t.showstartdate=0,t.showenddate=0,t.showlabels=void 0,t.showborder=1,t.borderthickness=1,t.showHoverEffect=1,t.slackFillColor="FF5E5E",t.font=s.BLANKSTRING,t.fontcolor=s.BLANKSTRING,t.fontsize=s.BLANKSTRING,t.color=s.BLANKSTRING,t.alpha=s.HUNDREDSTRING,t.bordercolor=s.BLANKSTRING,t.borderalpha=s.HUNDREDSTRING,t.hoverFillColor=s.BLANKSTRING,t.hoverFillAlpha=s.HUNDREDSTRING,t.slackHoverFillColor=10,t.slackHoverFillAlpha=s.HUNDREDSTRING},a.configureAttributes=function(){var e=this.config,t=this.getFromEnv("dataSource"),a=(0,s.extend2)({},t.milestones&&t.milestones.length?t.milestones[0]:t.milestones||{});(0,s.parseConfiguration)(a,e,{milestones:!0}),this.components||(this.components={}),this._setConfigure(),this.setState("dirty",!0)},a._setConfigure=function(e){var t,a,i,o,n,l,c,h,d,p,g,u,m,f,v=this.getFromEnv("chart"),b=v.getChildren("yAxis")[0].config,x=this.getFromEnv("dataSource"),C=x.milestones&&x.milestones.length?x.milestones[0]:x.milestones||{},k=e||C.length?C:C.milestone,w=k&&k.length,L=this.getFromEnv("color-manager"),y=v.config,A=this.getFromEnv("number-formatter"),F=this.components.data,T=v.config.style,P=T.inCanvasStyle,N=v.components.tasksMap,E=b.processes.processMap,S=b.processes.process.process,D=v.config.milestoneLabelStyle;for(F||(F=this.components.data=[]),o=0;o<w;o+=1)l=k[o],(t=F[o])||(t=F[o]={config:{}}),i=t.config,u=(0,s.getFirstValue)(l.taskid,s.BLANKSTRING).toLowerCase(),d=(0,s.pluck)(l.shape,"polygon").toLowerCase(),c=(0,s.pluckNumber)(l.numsides,5),p=0,"star"===d?p=.4:(d=(0,s.mapSymbolName)(c),d=(0,s.mapSymbolName)(c).split("-")[0]),h=(0,s.pluck)(l.color,L.getColor("legendBorderColor")),g=(0,s.getValidValue)((0,s.parseUnsafeString)((0,s.pluck)(l.tooltext,l.hovertext,y.milestonetooltext),!1)),m=A.getDateValue(l.date).ms,f=A.getFormattedDate(m),void 0!==g&&N[u]?(a=N[u].config,n=E[u]?E[u].catObj.label||E[u].catObj.name:S[o]&&(S[o].label||S[o].name),g=(0,s.parseTooltext)(g,[28,32,33,34,35,36],{date:f,taskStartDate:a._startDate,taskEndDate:a._endDate,taskLabel:a.label,taskPercentComplete:-1!==a.percentComplete?A.percentValue(a.percentComplete):s.BLANKSTRING,processName:n},l)):g=f,T=i.style=(0,r.extractLabelStyle)({fontSize:l.fontsize,fontFamily:l.font,fontWeight:l.fontbold,fontStyle:l.fontitalic}),i.textColor=(0,s.getFirstColor)((0,s.pluck)(l.fontcolor,y.milestonefontcolor,P.color)),(0,s.setLineHeight)(T),i.lineHeight=(0,s.pluck)(T&&T.lineHeight,D&&D.lineHeight),i.numSides=c,i.startAngle=(0,s.pluckNumber)(l.startangle,90),i.radius=l.radius,i.origDate=l.date,i.date=A.getDateValue(l.date),i.fillColor=(0,s.getFirstColor)(h),i.fillAlpha=.01*(0,s.pluckNumber)(l.fillalpha,l.alpha,100),i.borderColor=(0,s.getFirstColor)((0,s.pluck)(l.bordercolor,h)),i.borderAlpha=.01*(0,s.pluckNumber)(l.borderalpha,l.alpha,100),i.displayValue=(0,s.parseUnsafeString)(l.label),i.style=T,i.hoverFillColor=(0,s.getFirstColor)((0,s.pluck)(l.hoverfillcolor,y.milestonehoverfillcolor,(0,s.getDarkColor)(h,80))),i.hoverFillAlpha=.01*(0,s.pluckNumber)(l.hoverfillalpha,y.milestonehoverfillalpha,l.fillalpha,l.alpha,100),i.hoverBorderColor=(0,s.getFirstColor)((0,s.pluck)(l.hoverbordercolor,y.milestonehoverbordercolor,(0,s.getDarkColor)((0,s.pluck)(l.bordercolor,h),80))),i.hoverBorderAlpha=.01*(0,s.pluckNumber)(l.hoverborderalpha,y.milestonehoverborderalpha,l.borderalpha,l.alpha,100),i.showHoverEffect=(0,s.pluckNumber)(l.showhovereffect,y.showmilestonehovereffect,y.showhovereffect,1),i.depth=p,i.taskId=u,i.borderThickness=(0,s.pluckNumber)(l.borderthickness,1),i.link=l.link,i.toolText=g;(0,s.pluckNumber)(C.visible,1)?this.setState("visible",!0):this.setState("visible",!1)},a.drawLabel=function(){var e,t,a,i,o,n,l,c,h,u,m,f=this.getFromEnv("chart"),v=this.getFromEnv("animationManager"),b=f.components.tasksMap,x=this.getFromEnv("toolTipController"),C=this.getContainer("milestoneLabelContainer"),k=this.components.data;for(i=0,l=k.length;i<l;i++)u=b[(e=(a=k[i]).config).taskId],o=(c=a.graphics).labelElement,t=e.eventArgs,h=e._labelAttrs,m=e.style,e.displayValue!==s.BLANKSTRING&&void 0!==e.displayValue&&u?(n=c.labelElement=v.setAnimation({el:o||"text",attr:h,container:C,component:this}),o?(n.removeCSS(),n.show()):n.on("fc-click",d(f)).hover(p(f),g(f)),n.css(m),x.enableToolTip(n,e.toolText),n.data("eventArgs",t).data("dataObj",a)):o&&v.setAnimation({el:o,component:this,callback:r.hideFn,doNotRemove:!0})},a.draw=function(){var e,t,a,i,o,n,l,c,u,m,f,v,b,x,C,k=this.getFromEnv("chart"),w=k.components,L=this.components,y=this.getFromEnv("toolTipController"),A=k.getChildren("xAxis")[0],F=L.data,T=w.tasksMap,P=k.config,N=k.getChildren("canvas")[0],E=this.getContainer("milestoneContainer"),S=N.getChildContainer("milestoneGroup"),D=this.getContainer("milestoneLabelContainer"),B=this.getState("visible"),V=this.getFromEnv("animationManager"),_=this.components.removeDataArr||[],H=_.length,I=P.showtooltip;for(E||(E=this.addContainer("milestoneContainer",V.setAnimation({el:"group",attr:{name:"milestone"},container:S,component:this}))),B?E.show():E.hide(),D||(D=this.addContainer("milestoneLabelContainer",V.setAnimation({el:"group",attr:{name:"labels"},container:S,component:this}))),B?D.show():D.hide(),x=F&&F.length,b=0;b<x;b+=1)if(e=F[b])if(i=e.config,a=e.graphics,t=T[i.taskId],!e.graphics&&(e.graphics={}),l=a.element,t){if(n=t.config,o=i.eventArgs={sides:i.sides,date:i.origDate,radius:i.radius,taskId:i.taskId,toolText:i.toolText,link:i.link,numSides:i.numSides},u=A.getPixel(i.date.ms),m=n.yPos+.5*n.height,v=(0,s.pluckNumber)(i.radius,.6*n.height),!1===(0,r.checkInvalidValue)(u,m,v))continue;f=[i.numSides,u,m,v,i.startAngle,i.depth],(c=a.element=V.setAnimation({el:l||"polypath",label:"polypath",attr:{polypath:f,fill:i.fillColor,"fill-opacity":i.fillAlpha,stroke:i.borderColor,"stroke-opacity":i.borderAlpha,groupId:"gId"+b,cursor:i.link?"pointer":s.BLANKSTRING,"stroke-width":i.borderThickness},component:this,container:E})).on("fc-click",d(k)).hover(p(k),g(k)),c.show().data("eventArgs",o).data("dataObj",e),I?y.enableToolTip(c,i.toolText):y.disableToolTip(c),(C=i._labelAttrs||(i._labelAttrs={})).x=u,C.y=m,C.text=i.displayValue,C.groupId="gId"+b,C.cursor=i.link?"pointer":s.BLANKSTRING,C.direction=P.textDirection,C["text-anchor"]="middle",C.fill=i.textColor}else l&&V.setAnimation({el:l,component:this,callback:r.hideFn,doNotRemove:!0});for(this.drawn?this.drawLabel():this.addJob("drawMilestoneLabels",this.drawLabel.bind(this),h.priorityList.label),this.drawn=!0,b=0;b<H;b++)this._removeDataVisuals(_.shift())},t}(r["default"]);t["default"]=u},1183:function(e,t,a){"use strict";var i=a(208);t.__esModule=!0,t["default"]=t.hideFn=t.extractLabelStyle=t.checkInvalidValue=void 0;var o=i(a(229)),n=i(a(520)),r=a(215),s=a(223),l=i(a(1184)),c=a(227),h={left:"start",right:"end",center:"middle"},d={left:0,right:1,center:.5,undefined:.5},p={left:5,right:-5,center:0,undefined:0},g={fontWeight:["normal","bold"],fontStyle:["normal","italic"]},u=function(){this.hide()},m=function(e){return null!=e},f=function(e){var t,a;for(a in e)if(void 0!==e[a])switch(t=t||{},a){case"fontWeight":case"fontStyle":t[a]=g[a][e[a]];break;default:t[a]=e[a]}return t},v=function(e){return/%/g.test(e)},b=function(){var e,t=0,a=arguments.length,i=!1;for(t=0;t<a;t++){if(e=arguments[t],isNaN(e))return!1;i=!0}return i};t.checkInvalidValue=b,t.extractLabelStyle=f,t.hideFn=u,(0,s.addDep)({name:"taskAnimation",type:"animationRule",extension:l["default"]});var x=function(e){function t(){var t;return(t=e.call(this)||this).components={},t}(0,o["default"])(t,e);var a=t.prototype;return a.getName=function(){return"task"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.showpercentlabel=void 0,t.showlabels=void 0,t.showborder=1,t.borderthickness=1,t.font=r.BLANKSTRING,t.fontcolor=r.BLANKSTRING,t.fontsize=r.BLANKSTRING,t.color=r.BLANKSTRING,t.alpha=r.HUNDREDSTRING,t.angle=270,t.slackfillcolor=void 0,t.borderalpha=r.HUNDREDSTRING,t.hoverfillcolor=r.BLANKSTRING,t.slackhoverfillalpha=r.HUNDREDSTRING,t.showstartdate=void 0,t.showenddate=void 0},a.configureAttributes=function(){var e=this.getFromEnv("dataSource"),t=(0,r.extend2)({},e.tasks&&e.tasks.length?e.tasks[0]:e.tasks||{});(0,r.parseConfiguration)(t,this.config,this.getFromEnv("chart").config,{task:!0}),this.components||(this.components={}),this.config.hoverfillalpha=(0,r.pluckNumber)(t.hoverfillalpha,this.getFromEnv("chart").config.taskhoverfillalpha,r.HUNDREDSTRING),this._setConfigure(),this.setState("dirty",!0)},a._setConfigure=function(e){var t,a,i,o,n,s,l,c,h,d,p,g,u,m,v,b,x,C,k,w,L,y,A,F,T,P,N,E,S=this.config,D=this.getFromEnv("chart"),B=D.getChildren("yAxis")[0].config,V=this.getFromEnv("dataSource"),_=V.chart,H=V.tasks&&V.tasks.length?V.tasks[0]:V.tasks||{},I=e||H.length?H:H.task,R=I&&I.length,M=this.getFromEnv("color-manager"),O=D.config,G=this.getFromEnv("number-formatter"),W=O.taskbarfillmix,z=O.taskbarfillratio,j=O.showslackasfill,X=this.components.data,K=B.processes.processMap,U=B.processes.process.process,Y=O.dateintooltip,Z={right:"right",left:"left",undefined:"center",center:"center"},J=D.components.tasksMap={},q=O.style.inCanvasStyle,$=(0,r.pluckNumber)(_.showtooltip,1),Q=O.dataLabelStyle;if(X||(X=this.components.data=[]),S.showlabels=(0,r.pluck)(H.showlabels,H.showlabels,H.showname,_.showtasklabels,_.showtasknames,0),S.showTextOutline=(0,r.pluckNumber)(_.textoutline,0),N=S.labelStyle=f({fontSize:S.fontSize,fontFamily:S.font}),(0,r.setLineHeight)(S.labelStyle),R)for(o=0;o<R;o+=1)d=I[o],(p=(0,r.pluck)(d.processid))&&"string"==typeof p&&(p=p.toLowerCase()),T=d.id,d.percentcomplete,g=(0,r.pluckNumber)(d.alpha,S.alpha),b=(0,r.pluck)(d.color,S.color,M.getColor("plotFillColor")),u=(0,r.pluckNumber)(d.borderalpha,S.borderalpha,"100"),m=(0,r.pluck)(d.bordercolor,S.bordercolor,M.getColor("plotBorderColor")),v=(0,r.getFirstValue)((0,r.pluck)(d.label,d.name),""),s=M.parseColorMix(b,W),l=M.parseAlphaList(g.toString(),s.length),c=M.parseRatioList(z,s.length),x=(0,r.pluckNumber)(d.angle,S.angle),(P=(X[o]||(X[o]={config:{}})).config).index=o,P.link=d.link,P.processId=(0,r.pluck)(p,"PROCESS_"+o),P.textColor=(0,r.getFirstColor)((0,r.pluck)(d.fontcolor,S.fontcolor,q.color)),P.textFont=(0,r.pluck)(d.font,S.font,q.fontFamily),P.textFontSize=(0,r.pluckNumber)(d.fontsize,S.fontsizes,q.fontSize),!d.fontsize&&O.dataLabelStyle&&(d.fontsize=O.dataLabelStyle.fontSize),P.style=f({fontSize:d.fontsize,fontFamily:d.font}),(0,r.setLineHeight)(P.style),E=P.style,P.lineHeight=(0,r.pluck)(E&&E.lineHeight,N&&N.lineHeight,Q&&Q.lineHeight),P.startMs=G.getDateValue(d.start).ms,P.endMs=G.getDateValue(d.end).ms,i=G.getFormattedDate(P.startMs),a=G.getFormattedDate(P.endMs),P.tAlpha=g,P.tBorderColor=m,P.tBorderAlpha=u,h="",P.percentComplete=t=Math.min((0,r.pluckNumber)(d.percentcomplete,-1),100),P.labelAlign=Z[[(0,r.pluck)(d.labelalign,O.tasklabelsalign).toLowerCase()]],P.showAsGroup=(0,r.pluckNumber)(d.showasgroup,0),C=P.showPercentLabel=(0,r.pluckNumber)(d.showpercentlabel,S.showpercentlabel),(0,r.pluckNumber)(d.showlabel,d.showname,S.showlabels)&&(h=v),C&&-1!==t&&(h+=" "+t+"%"),P.percentComplete=t,y={FCcolor:{color:s.join(),alpha:l,ratio:c,angle:x}},L=M.parseColorMix((0,r.pluck)(d.slackfillcolor,S.slackfillcolor,O.slackfillcolor),W),L=j?{FCcolor:{color:L.join(),alpha:l,ratio:c,angle:x}}:r.TRACKER_FILL,A={FCcolor:{color:M.parseColorMix((0,r.pluck)(d.hoverfillcolor,S.hoverfillcolor,O.taskhoverfillcolor,(0,r.getDarkColor)(b,80)),W).join(),alpha:M.parseAlphaList((0,r.pluck)(d.hoverfillalpha,S.hoverfillalpha).toString(),s.length),ratio:c,angle:x}},F=(0,r.convertColor)((0,r.pluck)(d.hoverbordercolor,S.hoverbordercolor,(0,r.getDarkColor)(m,80)),(0,r.pluck)(d.hoverborderalpha,S.hoverborderalpha,u)),w=j?{FCcolor:{color:M.parseColorMix((0,r.getDarkColor)((0,r.pluck)(d.slackhoverfillcolor,S.slackhoverfillcolor,O.slackfillcolor),80),W).join(),alpha:M.parseAlphaList((0,r.pluck)(d.slackhoverfillalpha,S.slackhoverfillalpha,"100").toString(),s.length),ratio:c,angle:x}}:r.TRACKER_FILL,P.color=(0,r.toRaphaelColor)(y),P.rawTaskColor=b,P.rawTaskAlpha=g,P.slackColor=(0,r.toRaphaelColor)(L),P.hoverFillColor=(0,r.toRaphaelColor)(A),P.hoverBorderColor=F,P.slackHoverColor=(0,r.toRaphaelColor)(w),P.showHoverEffect=(0,r.pluckNumber)(d.showhovereffect,S.showhovereffect,_.showhovereffect,1),P.taskHeight=(0,r.pluck)(d.height,"35%"),P.topPadding=(0,r.pluck)(d.toppadding,"35%"),P.showPercentLabel=C,P.endDate=(0,r.pluckNumber)(d.showenddate,S.showenddate)?a:void 0,P._endDate=a,P.startDate=(0,r.pluckNumber)(d.showstartdate,S.showstartdate)?i:void 0,P._startDate=i,P.shadow={opacity:Math.max(g,u)/100,inverted:!0},P.id=p,P.taskId=T,P.borderColor=(0,r.convertColor)(m,u),P.borderThickness=(0,r.pluckNumber)(d.showborder,S.showborder)?(0,r.pluckNumber)(d.borderthickness,S.borderthickness):0,k=(0,r.getValidValue)((0,r.parseUnsafeString)((0,r.pluck)(d.tooltext,S.hovertext,S.plottooltext,_.plottooltext),!1)),$&&(void 0!==k?(n=K[p]?K[p].catObj.label||K[p].catObj.name:U[o]&&(U[o].label||U[o].name),k=(0,r.parseTooltext)(k,[3,28,29,30,31],{end:a,start:i,label:v,percentComplete:-1!==t?G.percentValue(t):"",processName:n},S)):k=(""!==v?v+(Y?", ":""):"")+(Y?i+" - "+a:"")),P.label=h,P.toolText=k,"string"==typeof T&&(T=T.toLowerCase()),void 0!==T&&(J[T]=X[o]);(0,r.pluckNumber)(H.visible,1)?this.setState("visible",!0):this.setState("visible",!1)},a.slackElemHandlers=function(e,t){var a=this;e&&e.on("fc-click",(function(e){t.plotEventHandler(this,e)})).hover((function(e){var i=this.data("dataObj");t.plotEventHandler(this,e,"DataPlotRollOver"),i.config.showHoverEffect&&a.taskHoverHandler.call(this,t)}),(function(e){var i=this.data("dataObj");t.plotEventHandler(this,e,"DataPlotRollOut"),i.config.showHoverEffect&&a.taskHoverOutHandler.call(this,t)}))},a.trimData=function(e){if(this.config.JSONData){var t,a,i=this.config,o=i&&i.context,n=o&&o.prevCatlen,r=this.getFromEnv("xAxis").getProcessLen(),s=n-r,l=i.JSONData,c=l.task&&l.task.length,h=e.task&&e.task.length||0,d=c-h;s>d?(t=s,a=r):(t=d,a=h),t>0&&this.removeData(a,t,!1)}},a.getAxisValuePadding=function(){return this.config.defaultPadding},a.drawLabel=function(){var e,t,a,i,o,n,r,s,l,c,h,d,p,g,f=this.config,v=this.getFromEnv("chart"),b=this.getFromEnv("animationManager"),x=this.components.data,C=this.getContainer("dataLabelContainer"),k=x.length;for(g=0;g<k;g++)r=(l=x[g]).config,s=l.graphics,c=r.eventArgs,e=s.valElem,h=r._labelTextAttr,m(r.label)&&""!==r.label&&h?((i=s.valElem=b.setAnimation({el:e||"text",attr:h,container:C,component:this})).outlineText(f.showTextOutline,h.fill),e?(i.removeCSS(),i.show()):(i.data("dataset",this),this.slackElemHandlers(i,v)),i.css(r.style),i.data("dataObj",l).data("dataObj",l).data("eventArgs",c)):e&&b.setAnimation({el:e,component:this,callback:u,doNotRemove:!0}),t=s.startValElem,d=r._startLabelTextAttr,m(r.startDate)&&""!==r.startDate&&d?((o=s.startValElem=b.setAnimation({el:t||"text",attr:d,container:C,component:this})).outlineText(f.showTextOutline,d.fill),t?(o.removeCSS(),o.show()):(o.data("dataset",this),this.slackElemHandlers(o,v)),o.css(r.style),o.data("dataObj",l).data("chart",v).data("eventArgs",c)):t&&b.setAnimation({el:t,component:this,callback:u,doNotRemove:!0}),a=s.endValElem,p=r._endLabelTextAttr,m(r.endDate)&&""!==r.endDate&&p?((n=s.endValElem=b.setAnimation({el:a||"text",attr:p,container:C,component:this})).outlineText(f.showTextOutline,p.fill),a?(n.removeCSS(),n.show().css(r.style)):(n.data("dataset",this),this.slackElemHandlers(n,v)),n.data("dataObj",l).data("chart",v).data("eventArgs",c)):a&&b.setAnimation({el:a,component:this,callback:u,doNotRemove:!0})},a.taskHoverHandler=function(){var e=this.data("dataObj")||{},t=this.data("dataset").components.data,a=e.config||{},i=a.index,o=t[i]&&t[i].graphics,n={fill:a.hoverFillColor,stroke:a.hoverBorderColor};-1===a.percentComplete||a.showAsGroup||(o.slackElem.attr({fill:a.slackHoverColor}),o.taskFill.attr({fill:a.hoverFillColor}),delete n.fill),o.element.attr(n)},a.taskHoverOutHandler=function(){var e=this.data("dataObj")||{},t=(this.data("dataset")||{}).components.data,a=e.config||{},i=a.index,o=t[i]&&t[i].graphics,n={fill:a.color,stroke:a.borderColor,"stroke-width":a.borderThickness,"stroke-dasharray":a.dashedStyle};-1===a.percentComplete||a.showAsGroup||(o.slackElem.attr({fill:a.slackColor}),o.taskFill.attr({fill:a.color}),delete n.fill),o.element.attr(n)},a.parsePlotAttributes=function(e,t){var a,i,o,n,s,l,c,g,u,m,f,x,C,k,w,L,y,A,F,T,P,N,E,S,D,B=this.getFromEnv("chart"),V=B.getFromEnv("dataSource"),_=B.config,H=_.canvasTop,I=B.getChildren("xAxis")[0],R=B.getChildren("yAxis")[0],M=t,O=_.datepadding,G=_.viewPortConfig,W=G.x,z=V.processes.process&&V.processes.process.length,j=G.scaleX,X=_.taskbarroundradius;a=e&&e.config,!e.graphics&&(e.graphics={}),o=a&&a.startMs,n=a&&a.endMs,void 0!==e&&void 0!==o&&null!==n&&(T=a.taskHeight,s=a.link,C=a.borderThickness,c=a.id,A=a.color,D=a.lineHeight,M>z-1&&(M=0),S="undefined"!=typeof a.id?R.getProcessPositionById(c):R.getProcessPositionByIndex(M),M++,F=(g=S.height)*(v(a.topPadding)&&.01*parseFloat(a.topPadding,10))||(0,r.pluckNumber)(a.topPadding,g),l=a.height=g*(v(T)&&.01*parseFloat(T,10))||(0,r.pluckNumber)(T,g),f=a.xPos=I.getPixel(a.startMs)+W*j,i=I.getPixel(a.endMs)+W*j,u=a.width=Math.abs(m=i-f),x=S.bottom+H-g,x=a.yPos=x+Math.min(F,g-l),L=.5*l,f=(w=(0,r.crispBound)(f,x,u,l,C)).x,x=w.y,u=w.width,l=w.height,!1!==b(f,x,u,l)?(a.props={element:{},perComElem:{},slackElem:{}},k=a.showAsGroup?{path:["M",f,x,"V",x+l,"L",f+L,x+L,"H",f+u-L,"L",f+u,x+l,"V",x,"H",f]}:{x:w.x,y:w.y,width:w.width||1,height:l},Object.assign(k,{fill:A,stroke:a.borderColor,cursor:s?"pointer":"",r:X,"stroke-width":C,width:w.width||1}),a.eventArgs={processId:a.processId,taskId:a.taskId,start:a._startDate,end:a._endDate,showAsGroup:a.showAsGroup,link:a.link,sourceType:"task",percentComplete:-1!==a.percentComplete,bgColor:a.rawTaskColor,bgAlpha:a.rawTaskAlpha,font:a.textFont,fontSize:a.textFontSize,fontColor:a.textColor},-1===a.percentComplete||a.showAsGroup||(m=u*a.percentComplete*.01,A=r.TRACKER_FILL,a.props.perComElem={attr:{x:f,y:x,height:l,width:m,fill:a.color,cursor:s?"pointer":"","stroke-width":0}},a.props.slackElem={attr:{x:f+m||1,y:x,width:u-m,height:l,fill:a.slackColor,cursor:s?"pointer":"","stroke-width":0}},k.fill=A),y=a.labelAlign,P=a._labelTextAttr||(a._labelTextAttr={}),a.props.element.attr=k,P.x=f+u*d[y]+p[y],P.y=x-.5*parseInt(D,10)-_.tasklabelspadding,P.text=a.label,P.direction=_.textDirection,P["text-anchor"]=h[y],P.cursor=void 0,P.fill=(0,r.convertColor)(a.textColor),P["line-height"]=D,(E=a._startLabelTextAttr||(a._startLabelTextAttr={})).x=f-2-O,E.y=x+.5*l,E.text=a.startDate,E["text-anchor"]=r.POSITION_END,E.cursor=void 0,E.direction=_.textDirection,E.fill=(0,r.convertColor)(a.textColor),E["line-height"]=D,(N=a._endLabelTextAttr||(a._endLabelTextAttr={})).x=f+u+2+O,N.y=x+.5*l,N.text=a.endDate,N.cursor=void 0,N.direction=_.textDirection,N["text-anchor"]=r.POSITION_START,N.fill=(0,r.convertColor)(a.textColor),N["line-height"]=D,a.cursor=void 0):a.inValidValue=!0)},a.allocatePosition=function(){var e,t,a=this.components.data||[],i=a.length;for(e=0;e<i;e++)t=a[e],this.parsePlotAttributes(t,e)},a._removeDataVisuals=function(e){var t,a,i,o=this.getFromEnv("animationManager");if(e)for(t in a=e.graphics)(i=a[t])&&o.setAnimation({el:i,component:this})},a.draw=function(){var e,t,a,i,o,n,r,s,l,h,d,p,g,m,f,v,b=this.getState("visible"),x=this.getFromEnv("chart"),C=this.getFromEnv("toolTipController"),k=x.config,w=x.getChildren("canvas")[0],L=this.getFromEnv("animationManager"),y=w.getChildContainer("taskGroup"),A=this.components,F=A.data,T=A.removeDataArr,P=T&&T.length,N=x.getChildContainer("datalabelsGroup"),E=w.getContainer("labelsGroup"),S=this.getContainer("dataLabelContainer"),D=this._contextChanged(),B=this.getContainer("taskColumnContainer"),V=this.getContainer("taskColumnShadowContainer"),_=k.showshadow,H=this.config;if(this.getState("removed")||this.getState("dirty")||D&&b){for(B||(B=this.addContainer("taskColumnContainer",L.setAnimation({el:"group",attr:{name:"columns"},container:y,component:this}))),b?B.show():B.hide(),E||(E=w.addContainer("labelsGroup",L.setAnimation({el:"group",attr:{name:"task-plot-labels"},container:N,component:w}))),S?S.removeCSS():S=this.addContainer("dataLabelContainer",L.setAnimation({el:"group",attr:{name:"labels"},container:y,component:this})),b?S.show():S.hide(),E=L.setAnimation({el:E,attr:{transform:"T"+-x.config.xOffset+",0"},component:w}),S.css(H.labelStyle),V||(V=this.addContainer("taskColumnShadowContainer",L.setAnimation({el:"group",attr:{name:"shadow"},container:y,component:this}).toBack()),b||V.hide()),t=F.length,e=0;e<t;e++)i=(a=F[e])&&a.config,!a.graphics&&(a.graphics={}),o=a.graphics,n=i&&i.startMs,r=i&&i.endMs,void 0!==a&&void 0!==n&&null!==r&&(s=i.toolText,i.inValidValue||(l=o.element,m=i.showAsGroup?"path":"rect",l&&l.type!==m&&(l.remove(),l=o.element=null),f=i.props.element.attr,o.element=L.setAnimation({el:l||m,label:m,attr:f,container:B,component:this}),l||((l=o.element).data("dataset",this),this.slackElemHandlers(l,x)),v=i.eventArgs,-1===i.percentComplete||i.showAsGroup?(o.taskFill&&L.setAnimation({el:o.taskFill,component:this,callback:u,doNotRemove:!0}),o.slackElem&&L.setAnimation({el:o.slackElem,component:this,callback:u,doNotRemove:!0})):(d=o.taskFill,g=o.slackElem,h=o.taskFill=L.setAnimation({el:d||"rect",attr:i.props.perComElem.attr,container:B,component:this}),d||this.slackElemHandlers(h,x),p=o.slackElem=L.setAnimation({el:g||"rect",attr:i.props.slackElem.attr,container:B,component:this}),g||this.slackElemHandlers(p,x),h.show(),h.data("chart",x).data("dataObj",a).data("dataset",this),C.enableToolTip(h,s),p.show(),h&&h.data("eventArgs",v),p&&p.data("eventArgs",v).data("dataObj",a).data("dataset",this).data("chart",x)),l.show().shadow({opacity:_},V).data("dataObj",a).data("chart",x).data("dataset",this).data("eventArgs",v),C.enableToolTip(l,s)));for(this.drawn?this.drawLabel():this.addJob("drawLabel",this.drawLabel.bind(this),c.priorityList.label),this.drawn=!0,e=0;e<P;e++)this._removeDataVisuals(T.shift())}},t}(n["default"]);t["default"]=x},1184:function(e,t,a){"use strict";t.__esModule=!0,t["default"]=void 0;t["default"]={"initial.dataset.task":function(){return{"rect.appearing":function(e){return[{initialAttr:{width:0},finalAttr:{width:e.attr.width},slot:"middle"}]},"path.appearing":function(e){var t=e.attr.path;return[{initialAttr:{path:t.slice(0,3)},finalAttr:{path:t},slot:"middle"}]}}}}},1185:function(e,t,a){"use strict";t.__esModule=!0,t["default"]=void 0;t["default"]={"initial.dataset.milestone":function(){return{"polypath.appearing":function(){return[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"middle",startEnd:{start:.9,end:1}}]}}}}},1186:function(e,t,a){"use strict";var i=a(212),o=a(208);t.__esModule=!0,t["default"]=void 0;var n=o(a(229)),r=i(a(1183)),s=a(215),l=o(a(1187)),c=a(223),h=function(e){return function(t){e.plotEventHandler(this,t,"ConnectorClick")}},d=function(e){return function(t){var a=this.data("dataObj"),i=a.config,o=e.components.tasksMap,n=o[i.fromTaskId],r=o[i.toTaskId],s={stroke:i.hoverColor,"stroke-dasharray":i.dashedStyle,"stroke-width":i.hoverThickness},l=a.graphics.connector;e.plotEventHandler(this,t,"ConnectorRollOver"),i.showHoverEffect&&([n,r].forEach((function(e){var t={fill:e.config.hoverFillColor,stroke:e.config.hoverBorderColor},a=e.config.percentComplete,i=e.graphics.slackElem,o=e.graphics.element,n=e.graphics.taskFill;a&&!e.config.showAsGroup&&(i&&i.attr({fill:e.config.slackHoverColor}),n&&n.attr({fill:e.config.hoverFillColor,stroke:e.config.hoverBorderColor}),delete t.fill),o&&o.attr(t)})),l&&l.attr(s))}},p=function(e){return function(t){var a=this.data("dataObj"),i=a.config,o=e.components.tasksMap,n=o[i.fromTaskId],r=o[i.toTaskId],s={stroke:i.color,"stroke-width":i.thickness,"stroke-dasharray":i.dashedStyle},l=a.graphics.connector;e.plotEventHandler(this,t,"ConnectorRollOut"),i.showHoverEffect&&([n,r].forEach((function(e){var t={fill:e.config.color,stroke:e.config.borderColor,"stroke-width":e.config.borderThickness,"stroke-dasharray":e.config.dashedStyle},a=e.config.percentComplete,i=e.graphics.slackElem,o=e.graphics.element,n=e.graphics.taskFill;a&&!e.config.showAsGroup&&(i&&i.attr({fill:e.config.slackColor}),n&&n.attr({fill:e.config.color}),delete t.fill),o&&o.attr(t)})),l&&l.attr(s))}};(0,c.addDep)({name:"connectorAnimation",type:"animationRule",extension:l["default"]});var g=function(e){function t(){return e.apply(this,arguments)||this}(0,n["default"])(t,e);var a=t.prototype;return a.getName=function(){return"connector"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.isdashed=1,t.thickness=1},a.configureAttributes=function(){var e=this.getFromEnv("dataSource"),t=(0,s.extend2)({},e.connectors&&e.connectors.length?e.connectors[0]:e.connectors||{});(0,s.parseConfiguration)(t,this.config,this.getFromEnv("chart").config,{connector:!0}),this.components||(this.components={}),this._setConfigure(),this.setState("dirty",!0)},a._setConfigure=function(e){var t,a,i,o,n,r,l,c,h=this.config,d=this.getFromEnv("chart"),p=this.getFromEnv("dataSource"),g=p.connectors&&p.connectors.length?p.connectors[0]:p.connectors||{},u=e||g.length?g:g.connector,m=u&&u.length||0,f=d.getFromEnv("color-manager"),v=d.config,b=this.components.data;for(b||(b=this.components.data=[]),t=0;t<m;t+=1)i=u[t],(a=b[t])||(a=b[t]={config:{}}),!a.config&&(a.config={}),o=a.config,n=(0,s.pluck)(i.color,h.color,f.getColor("plotBorderColor")),r=(0,s.pluckNumber)(i.alpha,h.alpha,100),l=(0,s.pluckNumber)(i.thickness,h.thickness,1),c=(0,s.pluckNumber)(i.isdashed,h.isdashed,1),o.fromTaskId=(0,s.getFirstValue)(i.fromtaskid,"").toLowerCase(),o.toTaskId=(0,s.getFirstValue)(i.totaskid,"").toLowerCase(),o.fromTaskConnectStart=(0,s.pluckNumber)(i.fromtaskconnectstart,0),o.toTaskConnectStart=(0,s.pluckNumber)(i.totaskconnectstart,1),o.color=(0,s.convertColor)(n),o.alpha=.01*r,o.link=i.link,o.showHoverEffect=(0,s.pluckNumber)(i.showhovereffect,h.showhovereffect,v.showconnectorhovereffect,1),o.hoverColor=(0,s.convertColor)((0,s.pluck)(i.hovercolor,h.hovercolor,v.connectorhovercolor,(0,s.getDarkColor)(n,80)),(0,s.pluckNumber)(i.hoveralpha,h.hoveralpha,v.connectorhoveralpha,r)),o.hoverThickness=(0,s.pluckNumber)(i.hoverthickness,h.hoverthickness,v.connectorhoverthickness,l),o.thickness=l,o.dashedStyle=c?(0,s.getDashStyle)((0,s.pluckNumber)(i.dashlen,h.dashlen,5),(0,s.pluckNumber)(i.dashgap,h.dashgap,l),l):"none";(0,s.pluckNumber)(g.visible,1)?this.setState("visible",!0):this.setState("visible",!1)},a.draw=function(){var e,t,a,i,o,n,l,c,g,u,m,f,v,b,x,C,k,w,L,y,A,F,T,P,N,E=this.getFromEnv("chart"),S=E.components,D=this.components.data,B=this.getFromEnv("animationManager"),V=E.config,_=D.length,H=S.tasksMap,I=V.connectorextension,R=E.getChildren("canvas")[0].getChildContainer("connectorGroup"),M=this.getContainer("connectorContainer"),O=this.getState("visible"),G=this._contextChanged(),W=this.components.removeDataArr||[],z=W.length;if(this.getState("removed")||this.getState("dirty")||G&&O){for(M||(M=this.addContainer("connectorContainer",B.setAnimation({el:"group",attr:{name:"connectors"},container:R,component:this})),O?M.show():M.hide()),N=0;N<=_;N+=1)if(o=D[N])if(x=o.config,!o.graphics&&(o.graphics={}),y=o.graphics,e=x.fromTaskId&&x.fromTaskId.toLowerCase(),t=x.toTaskId&&x.toTaskId.toLowerCase(),a=H[e],i=H[t],A=y.connector,a&&i){if(C=a.config,k=i.config,n=(l=C.yPos+.5*C.height)===(c=k.yPos+.5*k.height),g=C.xPos,u=C.xPos+C.width,m=k.xPos,f=k.xPos+k.width,!1===(0,r.checkInvalidValue)(g,u,m,f))continue;if(v=0,b=0,0===x.fromTaskConnectStart&&1===x.toTaskConnectStart&&(b=1),0===x.fromTaskConnectStart&&0===x.toTaskConnectStart&&(b=2),1===x.fromTaskConnectStart&&1===x.toTaskConnectStart&&(b=3),1===x.fromTaskConnectStart&&0===x.toTaskConnectStart&&(b=4),n)switch(L=C.height,b){case 1:w=["M",u,l,u+(v=(m-u)/10),l,"L",u+v,l,u+v,l-L,"L",u+v,l-L,m-v,l-L,"L",m-v,l-L,m-v,l,"L",m-v,l,m,c];break;case 2:w=["M",u,l,u+I,l,"L",u+I,l,u+I,l-L,"L",u+I,l-L,f+I,l-L,"L",f+I,c-L,f+I,c,f,c];break;case 3:w=["M",g,l,g-I,l,"L",g-I,l,g-I,l-L,"L",g-I,l-L,m-I,l-L,"L",m-I,l-L,m-I,l,"L",m-I,l,m,l];break;case 4:w=["M",g,l,g-I,l,"L",g-I,l,g-I,l-L,"L",g-I,l-L,f+I,l-L,"L",f+I,l-L,f+I,l,"L",f+I,l,f,l]}else switch(b){case 1:w=["M",u,l,u+(m-u)/2,l,"L",u+(m-u)/2,l,u+(m-u)/2,c,"L",u+(m-u)/2,c,m,c],w=u<=m?["M",u,l,u+(m-u)/2,l,"L",u+(m-u)/2,l,u+(m-u)/2,c,"L",u+(m-u)/2,c,m,c]:["M",u,l,u+I,l,"L",u+I,l,u+I,l+(c-l)/2,"L",u+I,l+(c-l)/2,m-I,l+(c-l)/2,"L",m-I,l+(c-l)/2,m-I,c,"L",m-I,c,m,c];break;case 2:w=["M",u,l,u+I+(v=f-u<0?0:f-u),l,"L",u+I+v,l,u+I+v,c,"L",u+I+v,c,f,c];break;case 3:w=["M",g,l,g-I-(v=g-m<0?0:g-m),l,"L",g-I-v,l,g-I-v,c,"L",g-I-v,c,m,c];break;case 4:w=g>f?["M",g,l,g-(g-f)/2,l,"L",g-(g-f)/2,l,g-(g-f)/2,c,"L",g-(g-f)/2,c,f,c]:["M",g,l,g-I,l,"L",g-I,l,g-I,l+(c-l)/2,"L",g-I,l+(c-l)/2,f+I,l+(c-l)/2,"L",f+I,l+(c-l)/2,f+I,c,"L",f+I,c,f,c]}(y.connector=B.setAnimation({el:A||"path",label:"path",attr:{path:w,stroke:x.color,"stroke-opacity":x.alpha,"stroke-width":x.thickness,"stroke-dasharray":x.dashedStyle},container:M,component:this})).show(),F={fromTaskId:x.fromTaskId,toTaskId:x.toTaskId,fromTaskConnectStart:x.fromTaskConnectStart,toTaskConnectStart:x.toTaskConnectStart,link:x.link,sourceType:"connector"},T=y.trackerElement,P=y.trackerElement=B.setAnimation({el:T||"path",attr:{path:w,stroke:s.TRACKER_FILL,"stroke-width":Math.max(x.thickness,10),cursor:x.link?"pointer":""},container:M,component:this}),T||P.on("fc-click",h(E)).hover(d(E),p(E)),P.data("dataObj",o).data("eventArgs",F)}else A&&B.setAnimation({el:A,component:this,callback:r.hideFn,doNotRemove:!0}),y.trackerElement&&B.setAnimation({el:y.trackerElement,component:this,callback:r.hideFn,doNotRemove:!0});for(N=0;N<z;N++)this._removeDataVisuals(W.shift())}},t}(r["default"]);t["default"]=g},1187:function(e,t,a){"use strict";t.__esModule=!0,t["default"]=void 0;t["default"]={"initial.dataset.connector":function(){return{"path.appearing":function(){return[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"final"}]}}}}},1188:function(e,t,a){"use strict";t.__esModule=!0,t["default"]=function(e){var t,a,o,n=e.getFromEnv("dataSource");if(function(e){return function(e){var t=e.tasks,a=!0;void 0!==t&&(a=t.length?!(t.length>0):t.task&&!(t.task.length>0));return a}(e)&&function(e){var t=e.connectors,a=!0;void 0!==t&&(a=t.length?!(t.length>0):t.task&&!(t.task.length>0));return a}(e)&&function(e){var t=e.milestones,a=!0;void 0!==t&&(a=t.length?!(t.length>0):t.milestone&&!(t.milestone.length>0));return a}(e)}(n))return void e.setChartMessage();t=n.tasks,a=n.connectors,o=n.milestones,t&&(0,i.datasetFactory)(e,e.getDSdef("task"),"task",1,t.length?t:[t]),a&&(0,i.datasetFactory)(e,e.getDSdef("connector"),"connector",1,a.length?a:[a]),o&&(0,i.datasetFactory)(e,e.getDSdef("milestone"),"milestone",1,o.length?o:[o]),e.getDatasets().forEach((function(t){t.addToEnv("yAxis",e.getChildren("xAxis")[0]),t.addToEnv("xAxis",e.getChildren("yAxis")[0])})),e.config.showLegend&&e._createLegendItems()};var i=a(215)}}])}));
//# sourceMappingURL=http://localhost:3052/3.15.3/map/eval/fusioncharts.gantt.js.map