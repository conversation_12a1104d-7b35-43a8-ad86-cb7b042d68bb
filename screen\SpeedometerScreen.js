import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  Animated,
  KeyboardAvoidingView,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import AIcon from 'react-native-vector-icons/AntDesign';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import {
  isTablet
} from '../util/common';
import { MERCHANT_VOUCHER_CODE_FORMAT, MERCHANT_VOUCHER_TYPE, SEGMENT_TYPE } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncImage from '../components/asyncImage';
import Switch from 'react-native-switch-pro';
import { NavigationContext } from '@react-navigation/native';
// import Pie from 'react-native-pie';


const Speedometer = props => {
  const {
    navigation,
  } = props;

  const [gaugeValue, setGaugeValue] = useState(0);
  const GaugeAnim = useRef(new Animated.Value(0)).current;

  const fadeIn = () => {
    Animated.timing(GaugeAnim, {
      toValue: gaugeValue,
      duration: 1000
    }).start();
  };

  const spin = () => {
    return (
      GaugeAnim.interpolate({
        inputRange: [0, 180],
        outputRange: ['0deg', '180deg'],
      }))
  }


  return (
    <View style={{ paddingVertical: 10 }}>
      <View>
        <TextInput
          placeholder={'Insert Number 0-180'}
          onChangeText={(text) => {
            //fadeIn(text)
            setGaugeValue(text)
          }}
          placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
          value={gaugeValue}
        />
      </View>
      <View style={{ width: 180, height: 180, backgroundColor: 'white', marginLeft: 15, transform: [{ rotate: '-90deg' }] }}>
        {/* <Pie
          radius={90}
          innerRadius={75}
          sections={[
            {
              percentage: 10,
              color: Colors.primaryColor,
            },
            {
              percentage: 20,
              color: Colors.secondaryColor,
              //color: Colors.tabYellow,
            },
            {
              percentage: 20,
              color: Colors.tabRed,
            },
            {
              percentage: 50,
              color: 'white',
            },
          ]}
          //dividerSize={0}
          strokeCap={'butt'}
        /> */}
      </View>
      {/* <Animated.View style={{ marginLeft: 15, width: 180, transform: [{ rotate: spin() }], top: -90, }}>
      <View style={{ width: 90, borderWidth: 2, borderBottomStartRadius: 5, borderTopStartRadius: 5, borderColor: '#1d3960', elevation: 4 }}/>
    </Animated.View> */}
      <Animated.View style={{
        marginLeft: 15, width: 180, transform: [{
          rotate: `${gaugeValue}deg`,
        }], top: -90,
      }}>
        <View style={{ width: 90, borderWidth: 2, borderBottomStartRadius: 5, borderTopStartRadius: 5, borderColor: '#1d3960', elevation: 4 }} />
      </Animated.View>
      <View style={{ flexDirection: 'row', width: 210, justifyContent: 'space-between', top: -90 }}>
        <Text style={{ textAlign: 'center', fontWeight: '500', color: Colors.fieldtTxtColor }}>
          Specific
        </Text>
        {/* <TouchableOpacity onPress={fadeIn}>
          <Text>
            Press Me
          </Text>
        </TouchableOpacity> */}
        <Text style={{ textAlign: 'center', fontWeight: '500', color: Colors.fieldtTxtColor }}>
          Board
        </Text>
      </View>
    </View>

  )


}

export default Speedometer;