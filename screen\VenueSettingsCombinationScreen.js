import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal as ModalComponent,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  TouchableWithoutFeedback,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
import GCalendar from '../assets/svg/GCalendar';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  RESERVATION_PRIORITY,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import { ScrollView as ScrollViewGH, ScrollView, FlatList } from 'react-native-gesture-handler';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { Calendar } from 'react-native-big-calendar';
// import ColorPanel from 'react-native-color-panel';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

import {
  CHART_DATA,
  CHART_TYPE,
  FS_LIBRARY_PATH,
  CHART_Y_AXIS_DROPDOWN_LIST,
  CHART_FIELD_COMPARE_DROPDOWN_LIST,
  CHART_FIELD_NAME_DROPDOWN_LIST,
  CHART_FIELD_TYPE,
  CHART_FIELD_COMPARE_DICT,
  CHART_PERIOD,
} from '../constant/chart';
import {
  filterChartItems,
  getDataForChartDashboardTodaySales,
  getDataForSalesLineChart,
} from '../util/chart';
import APILocal from '../util/apiLocalReplacers';


const VenueSettingsCombinationScreen = (props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // Dummy calendar data
  const dummycalendarData = [
    { date: moment('12/20/2021'), open: false },
    { date: moment('12/21/2021'), open: false },
    { date: moment('12/22/2021'), open: true },
    { date: moment('12/23/2021'), open: true },
    { date: moment('12/24/2021'), open: true },
    { date: moment('12/25/2021'), open: true },
    { date: moment('12/26/2021'), open: false },
  ];
  // calendar useState
  const [calendarData, setCalendarData] = useState(dummycalendarData);

  // initial room data
  const initialRoomData = [...Array(3)].map((d, index) => {
    return {
      key: `room-${index}`,
      roomName: String(index) + '',
      online: true,
      tables: 10,
      capacity: 46,
      combinations: 2,
      isSelect: false,
      tableData: {
        key: index,
        fromTable: 'Indoor',
        tableName: 'A',
        online: true,
        min: 5,
        max: 6,
        turnTime: '1h 45m',
        priority: 'Medium',
        isTick: false,
        expand: true,
      },
    };
  });

  // initial two table data
  const initialAllTableListData = [];

  // initial table combination data
  const initialAllTableCombinationListData = [
    {
      tableName: 'Indoor',
      expand: true,
      tableData: [...Array(10)].map((d, index) => {
        return {
          key: index,
          fromTable: 'Indoor',
          tableName: String(index) + 'A',
          online: true,
          min: 5,
          max: 6,
          turnTime: '1h 45m',
          priority: 'Medium',
          isTick: false,
        };
      }),
    },
  ];

  const events = [
    {
      title: 'Spa + Massage',
      start: moment('2021-12-23' + ' ' + '00:30'),
      end: moment('2021-12-23' + ' ' + '05:30'),
    },
    {
      title: 'Sleeping Mask',
      start: moment('2021-12-24' + ' ' + '22:00'),
      end: moment('2021-12-25' + ' ' + '02:00'),
    },
    {
      title: 'Rob Bank',
      start: moment('2021-12-25' + ' ' + '01:30'),
      end: moment('2021-12-25' + ' ' + '03:30'),
    },
  ];

  // get the page from asyncStorage
  const openPage = CommonStore.useState((s) => s.venueSettingPage);

  // room page useState
  const [roomData, setRoomData] = useState(initialRoomData);
  const [showAddArea, setShowAddArea] = useState(false);
  const [addModalRoomNameTxt, setAddModalRoomNameTxt] = useState('');
  const [roomNameFlatListTxt, setRoomNameFlatListTxt] = useState('');

  // table page useState
  const [allTableListData, setAllTableListData] = useState(
    initialAllTableListData,
  );

  // table combination page useState
  const [allTableCombinationListData, setAllTableCombinationListData] =
    useState(initialAllTableCombinationListData);
  const [showEditTableModal, setShowEditTableModal] = useState(false);
  const [showAddTableCombinationModal, setShowAddTableCombinationModal] =
    useState(false);
  const [switchOnline, setSwitchOnline] = useState(true);
  const [comNameModal, setComNameModal] = useState('');
  const [minModal, setMinModal] = useState('');
  const [maxModal, setMaxModal] = useState('');
  const [turnTimeModal, setTurnTimeModal] = useState('');
  const [shortTimeModal, setShortTimeModal] = useState('');
  const [bookingModal, setBookingModal] = useState('');
  const [isNew, setIsNew] = useState(false);

  //reservation page useState
  const [showAddAvModal, setShowAddAvModal] = useState(false);
  const [isBasic, setIsBasic] = useState(false);
  const [isTimeDays, setIsTimeDays] = useState(false);
  const [isPacing, setIsPacing] = useState(false);
  const [isRoom, setIsRoom] = useState(false);
  const [isColor, setIsColor] = useState(false);
  const [showPacingModal, setShowPacingModal] = useState(false);

  //modal basic tab
  const [basicReserveName, setBasicReserveName] = useState('');
  const [switchActive, setSwitchActive] = useState(true);
  const [basicColor, setBasicColor] = useState('');

  //modal time & days tab
  const [timeDaysDateTimePicker, setTimeDaysDateTimePicker] = useState(false);
  const [timeDaysDateTimePicker2, setTimeDaysDateTimePicker2] = useState(false);
  const [rev_date, setRev_date] = useState(moment().endOf(Date.now()).toDate());
  const [rev_date2, setRev_date2] = useState(
    moment().endOf(Date.now()).toDate(),
  );
  const [repeatShift, setRepeatShift] = useState('');
  const [resHours1, setResHours1] = useState('');
  const [resHours2, setResHours2] = useState('');
  const [switchDaily, setSwitchDaily] = useState(true);
  const [dailyHours1, setDailyHours1] = useState('');
  const [dailyHours2, setDailyHours2] = useState('');
  const [dailyHours3, setDailyHours3] = useState('');
  const [dailyHours4, setDailyHours4] = useState('');
  const [dailyHours5, setDailyHours5] = useState('');
  const [dailyHours6, setDailyHours6] = useState('');
  const [dailyHours7, setDailyHours7] = useState('');
  const [dailyHours8, setDailyHours8] = useState('');
  const [dailyHours9, setDailyHours9] = useState('');
  const [dailyHours10, setDailyHours10] = useState('');
  const [dailyHours11, setDailyHours11] = useState('');
  const [dailyHours12, setDailyHours12] = useState('');
  const [dailyHours13, setDailyHours13] = useState('');
  const [dailyHours14, setDailyHours14] = useState('');
  const [dailyHours15, setDailyHours15] = useState('');
  const [dailyHours16, setDailyHours16] = useState('');
  const [isMon, setIsMon] = useState(true);
  const [isTue, setIsTue] = useState(true);
  const [isWed, setIsWed] = useState(true);
  const [isThur, setIsThur] = useState(true);
  const [isFri, setIsFri] = useState(true);
  const [isSat, setIsSat] = useState(true);
  const [isSun, setIsSun] = useState(true);
  const [weeklyRepeat, setWeeklyRepeat] = useState('');
  const [monthlyRepeat, setMonthlyRepeat] = useState('');
  const [yearlyRepeat, setYearlyRepeat] = useState('');
  const [pacingRes, setPacingRes] = useState('');
  const [dummyAuto, setDummyAuto] = useState(true);
  const [dummyCapped, setDummyCapped] = useState(false);
  const [isAllRoom, setIsAllRoom] = useState(true);

  const [roomAreaOption, setRoomAreaOption] = useState([
    // {label: 'area1', value: 'AREA1'},
    // {label: '2', value: '2'},
  ]);

  const [turnTimeOption, setTurnTimeOption] = useState([
    // {label: '0h 15m', value: '15M'},
    // {label: '0h 30m', value: '30M'},
    // {label: '0h 45m', value: '40M'},
    // {label: '1h 00m', value: '1H'},
    // {label: '1h 15m', value: '1H_15M'},
    // {label: '1h 30m', value: '1H_30M'},
    // {label: '1h 45m', value: '1H_45M'},
    // {label: '2h 00m', value: '2H'},
    // {label: '2h 15m', value: '2H15M'},
    // {label: '2h 30m', value: '2H30M'},
    // {label: '2h 45m', value: '2H45M'},
    // {label: '3h 00m', value: '3H'},
    // {label: '3h 15m', value: '3H15M'},
    // {label: '3h 30m', value: '3H30M'},
    // {label: '3h 45m', value: '3H45M'},
    // {label: '4h 00m', value: '4H'},
    // {label: '4h 15m', value: '4H15M'},
    // {label: '4h 30m', value: '4H30M'},
    // {label: '4h 45m', value: '4H45M'},
    // {label: '5h 00m', value: '5H'},
    { label: '0h 15m', value: 15 },
    { label: '0h 30m', value: 30 },
    { label: '0h 45m', value: 45 },
    { label: '1h 00m', value: 60 },
    { label: '1h 15m', value: 75 },
    { label: '1h 30m', value: 90 },
    { label: '1h 45m', value: 105 },
    { label: '2h 00m', value: 120 },
    { label: '2h 15m', value: 135 },
    { label: '2h 30m', value: 150 },
    { label: '2h 45m', value: 165 },
    { label: '3h 00m', value: 180 },
    { label: '3h 15m', value: 195 },
    { label: '3h 30m', value: 210 },
    { label: '3h 45m', value: 225 },
    { label: '4h 00m', value: 240 },
    { label: '4h 15m', value: 255 },
    { label: '4h 30m', value: 270 },
    { label: '4h 45m', value: 285 },
    { label: '5h 00m', value: 300 },
  ]);

  const [priorityOption, setPriorityOption] = useState([
    { label: 'Low', value: RESERVATION_PRIORITY.LOW },
    { label: 'Medium', value: RESERVATION_PRIORITY.MEDIUM },
    { label: 'High', value: RESERVATION_PRIORITY.HIGH },
  ]);

  const minMaxOptions = [];
  const [oneMonth, setOneMonth] = useState([]);
  for (let i = 1; i <= 8; i++) {
    minMaxOptions.push({
      label: `${i}`,
      value: i,
    });
  }

  const [selectedSectionId, setSelectedSectionId] = useState('');

  const [resultedCombination, setResultedCombination] = useState({});

  const [selectedCombination, setSelectedCombination] = useState(null);

  /////////////////////////////////////////////////////////////////////////

  // store states

  const outletSections = OutletStore.useState((s) => s.outletSections);
  const outletTables = OutletStore.useState((s) => s.outletTables);
  const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);
  const outletTableCombinations = OutletStore.useState((s) => s.outletTableCombinations);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  /////////////////////////////////////////////////////////////////////////

  //use effect start

  useEffect(() => {
    var allTableCombinationListDataTemp = [];
    var allTableListDataTemp = [];
    var roomAreaOptionTemp = [];

    for (var i = 0; i < outletSections.length; i++) {
      var outletSection = outletSections[i];

      roomAreaOptionTemp.push({
        label: outletSection.sectionName,
        value: outletSection.uniqueId,
      });

      var rowData = {
        roomName: outletSection.sectionName,
        expand: true,
        tableData: [],

        outletSectionId: outletSection.uniqueId,
      };

      rowData.tableData = outletTableCombinations.filter(combination => combination.outletSectionId === outletSection.uniqueId).map((combination, index) => {
        return {
          key: index,
          fromTable: rowData.roomName,
          tableName: String(index) + combination.code,
          isOnline: combination.isOnline || true,
          paxMin: combination.paxMin || 1,
          paxMax: combination.paxMax || 6,
          capacity: combination.capacity || 6,
          outletSectionId: combination.outletSectionId,
          turnTime: combination.turnTime || 60,
          priority: combination.priority || RESERVATION_PRIORITY.MEDIUM,
          isTick: false,

          toJoinedTableIdList: combination.toJoinedTableIdList ? combination.toJoinedTableIdList.filter(tableId => outletTablesDict[tableId]) : [],

          tableCode: combination.code || '',

          // tableCapacity: table.capacity,
          // tableCode: table.code,
          // outletTableId: table.uniqueId,
          outletTableCombinationId: combination.uniqueId,
        };
      });

      allTableCombinationListDataTemp.push(rowData);

      ////////////////////////////////////////////////////////

      var rowDataTable = {
        roomName: outletSection.sectionName,
        expand: true,
        tableData: [],

        outletSectionId: outletSection.uniqueId,
      };

      rowDataTable.tableData = outletTables.filter(table => table.outletSectionId === outletSection.uniqueId && (!table.joinedTables || (table.joinedTables.length <= 0))).map((table, index) => ({
        key: index,
        fromTable: rowDataTable.roomName,
        tableName: String(index) + table.code,
        isOnline: table.isOnline || true,
        paxMin: table.paxMin || 1,
        paxMax: table.paxMax || 6,
        capacity: table.capacity || 8,
        outletSectionId: table.outletSectionId,
        turnTime: table.turnTime || 60,
        priority: table.priority || RESERVATION_PRIORITY.MEDIUM,
        isTick: false,

        capacity: table.capacity || 8,

        tableCapacity: table.capacity,
        tableCode: table.code,
        outletTableId: table.uniqueId,
      }));

      allTableListDataTemp.push(rowDataTable);
    }

    setAllTableCombinationListData(allTableCombinationListDataTemp);
    setAllTableListData(allTableListDataTemp);
    setRoomAreaOption(roomAreaOptionTemp);
  }, [
    outletSections,
    outletTables,
    outletTablesDict,
    outletTableCombinations,
  ]);

  useEffect(() => {
    // create a possible/preview combination based on ticked tables

    var currTableList = (allTableListData.find(rowData => rowData.outletSectionId === selectedSectionId)
      ?
      (allTableListData.find(rowData => rowData.outletSectionId === selectedSectionId).tableData || [])
      :
      []);

    var tickedTableList = currTableList.filter(table => table.isTick);

    var resultedCombinationTemp = {};

    var paxMinAccum = 0;
    var paxMaxAccum = 0;
    var capacityAccum = 0;
    var turnTimeLargest = 0;
    var toJoinedTableIdList = [];

    if (tickedTableList.length >= 2) {
      var pendingCodes = [];

      for (var i = 0; i < tickedTableList.length; i++) {
        var table = tickedTableList[i];

        pendingCodes.push(table.tableCode);

        if (table.turnTime > turnTimeLargest) {
          turnTimeLargest = table.turnTime;
        }

        if (table.paxMin > paxMinAccum) {
          paxMinAccum = table.paxMin;
        }

        if (table.paxMax > paxMaxAccum) {
          paxMaxAccum = table.paxMax;
        }

        if (table.capacity > capacityAccum) {
          capacityAccum = table.capacity;
        }

        // paxMinAccum += table.paxMin || 0;
        // paxMaxAccum += table.paxMax || 0;

        toJoinedTableIdList.push(table.outletTableId);
      }

      pendingCodes.sort((a, b) => {
        return naturalCompare(a, b);
      });

      resultedCombinationTemp = {
        key: uuidv4(), // just for unique purpose
        fromTable: tickedTableList[0].fromTable,
        tableName: pendingCodes.join('') || '',
        isOnline: true,
        paxMin: paxMinAccum || 1,
        paxMax: paxMaxAccum || 6,
        capacity: capacityAccum || 6,
        outletSectionId: tickedTableList[0].outletSectionId,
        turnTime: turnTimeLargest || 60,
        priority: RESERVATION_PRIORITY.MEDIUM,
        isTick: false,

        capacity: capacityAccum || 6,

        toJoinedTableIdList: toJoinedTableIdList || [],

        tableCode: pendingCodes.join('') || '',

        // tableCapacity: table.capacity,
        // tableCode: table.code,
        // outletTableId: table.uniqueId,
        outletTableCombinationId: uuidv4(),
      };
    }

    setResultedCombination(resultedCombinationTemp);

  }, [allTableListData, selectedSectionId]);

  useEffect(() => {
    // let tempTableData = [];
    // let tempTableCom = [];
    // for (let i = 0; i < roomData.length; i++) {
    //   tempTableCom.push({
    //     roomName: roomData[i].roomName,
    //     expand: true,
    //     tableData: [...Array(5)].map((d, index) => {
    //       return {
    //         key: index,
    //         fromTable: roomData[i].roomName,
    //         tableName: String(index) + 'A',
    //         online: true,
    //         min: 5,
    //         max: 6,
    //         turnTime: '1h 45m',
    //         priority: 'Medium',
    //         isTick: false,
    //       };
    //     }),
    //   });
    //   tempTableData.push({
    //     roomName: roomData[i].roomName,
    //     expand: true,
    //     tableData: [...Array(5)].map((d, index) => {
    //       return {
    //         key: index,
    //         fromTable: roomData[i].roomName,
    //         tableName: String(index) + ' table',
    //         online: true,
    //         min: 2,
    //         max: 6,
    //         roomArea: '',
    //         turnTime: '1h 45m',
    //         priority: 'Medium',
    //         expandOption: false,
    //       };
    //     }),
    //   });
    // }
    // setAllTableListData(tempTableData);

    // setAllTableCombinationListData(tempTableCom);

    let tempOneMonth = [];
    for (let i = 1; i <= 31; i++) {
      tempOneMonth.push({ label: i + '', value: false });
    }
    setOneMonth(tempOneMonth);

    // console.log('refreshed');
  }, []);

  // testing purpose
  // const renderEvent = (
  //   event: ICalendarEvent<MyCustomEventType>,
  //   touchableOpacityProps: CalendarTouchableOpacityProps,
  // ) => (
  //   <TouchableOpacity {...touchableOpacityProps}>
  //     <Text>{`My custom event: with a color: `}</Text>
  //   </TouchableOpacity>
  // )

  const createOrUpdateTableCombination = () => {
    if (selectedCombination && selectedCombination.outletTableCombinationId) {
      // means existing

      var body = {
        // tableId: selectedSlotId,
        capacity: selectedCombination.capacity,
        code: selectedCombination.tableCode,
        outletSectionId: selectedCombination.outletSectionId,
        outletId: currOutlet.uniqueId,

        isOnline: selectedCombination.isOnline || true,
        paxMin: selectedCombination.paxMin || 1,
        paxMax: selectedCombination.paxMax || 6,
        capacity: selectedCombination.capacity || 6,
        turnTime: selectedCombination.turnTime || 60,
        priority: selectedCombination.priority || RESERVATION_PRIORITY.MEDIUM,

        toJoinedTableIdList: selectedCombination.toJoinedTableIdList || [],

        outletTableCombinationId: selectedCombination.outletTableCombinationId,
      };

      ApiClient.POST(API.updateOutletTableCombination, body, false)
        .then((result) => {
          if (result && result.status === 'success') {
            // setAddTableModal(false);

            Alert.alert('Success', 'Table combination has been updated');
          } else {
            Alert.alert('Error', result.message);

            // setAddTableModal(false);
          }
        })
        .catch((err) => console.log(err));
    }
    else if (resultedCombination && resultedCombination.outletTableCombinationId) {
      // means is new

      var body = {
        // tableId: selectedSlotId,
        capacity: resultedCombination.capacity,
        code: resultedCombination.tableCode,
        outletSectionId: resultedCombination.outletSectionId,
        outletId: currOutlet.uniqueId,

        isOnline: resultedCombination.isOnline || true,
        paxMin: resultedCombination.paxMin || 1,
        paxMax: resultedCombination.paxMax || 6,
        capacity: resultedCombination.capacity || 6,
        turnTime: resultedCombination.turnTime || 60,
        priority: resultedCombination.priority || RESERVATION_PRIORITY.MEDIUM,

        toJoinedTableIdList: resultedCombination.toJoinedTableIdList || [],
      };

      ApiClient.POST(API.createOutletTableCombination, body, false)
        .then((result) => {
          if (result && result.uniqueId) {
            // setAddTableModal(false);

            Alert.alert('Success', 'Table combination has been created');
          } else {
            Alert.alert('Error', result.message);

            // setAddTableModal(false);
          }
        })
        .catch((err) => console.log(err));
    }
  };

  const renderEvents = (event, touchableOpacityProps) => {
    const [checkEvent, setCheckEvent] = useState(true);
    return (
      <TouchableOpacity
        {...touchableOpacityProps}
        {...(touchableOpacityProps.style[2].backgroundColor = checkEvent
          ? 'rgb(66, 133, 244)'
          : '#D3D3D3')}>
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={{ color: Colors.whiteColor }}>{`${event.title}`}</Text>
          <TouchableOpacity
            style={{ marginTop: 5, marginBottom: 5 }}
            onPress={() => {
              checkEvent ? setCheckEvent(false) : setCheckEvent(true);
            }}>
            {checkEvent ? (
              <MaterialIcons
                name="check-circle"
                size={20}
                color={Colors.whiteColor}
              />
            ) : (
              <MaterialIcons
                name="circle"
                size={20}
                color={Colors.whiteColor}
              />
            )}
          </TouchableOpacity>
          <Text style={{ color: Colors.whiteColor, marginTop: 'auto' }}>
            {`${moment(event.start).format('HH:mm') +
              ' - ' +
              moment(event.end).format('HH:mm')
              }`}
            {/* {`${event.time}`} */}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderPacing = (item, index) => {
    return (
      <View
        style={{
          paddingHorizontal: 10,
          borderWidth: 1,
          width: '100%',
          borderRadius: 10,
          height: 45,
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: windowHeight * 0.01,
        }}>
        <View style={styles.flexOne}>
          <Text>9:00 AM</Text>
        </View>
        <View style={styles.flexOne}>
          <Switch
            value={switchActive}
            onSyncPress={(value) => {
              setSwitchActive(!switchActive);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
          />
        </View>
        <View
          style={[
            styles.flexOne,
            {
              flexDirection: 'row',
              alignItems: 'center',
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              setDummyAuto(true);
              setDummyCapped(false);
            }}
            style={{
              backgroundColor: dummyAuto
                ? Colors.primaryColor
                : Colors.lightPrimary,
              borderLeftTopRadius: 5,
              borderLeftBottomRadius: 5,
              paddingHorizontal: 15,
              height: switchMerchant ? 30 : 35,
              justifyContent: 'center',
            }}>
            <Text
              style={{
                color: dummyAuto ? Colors.whiteColor : Colors.primaryColor,
              }}>
              Auto
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setDummyAuto(false);
              setDummyCapped(true);
            }}
            style={{
              backgroundColor: dummyCapped
                ? Colors.primaryColor
                : Colors.lightPrimary,
              paddingHorizontal: 15,
              borderRightTopRadius: 5,
              borderRightBottomRadius: 5,
              height: switchMerchant ? 30 : 35,
              justifyContent: 'center',
            }}>
            <Text
              style={{
                color: dummyCapped ? Colors.whiteColor : Colors.primaryColor,
              }}>
              Capped
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.flexOne}>
          <TextInput
            defaultValue={basicReserveName}
            onChangeText={(text) => {
              setBasicReserveName(text);
            }}
            placeholderTextColor={Colors.descriptionColor}
            editable={dummyCapped}
            placeholder={'30'}
            style={{
              backgroundColor: Colors.fieldtBgColor,
              width: '50%',
              height: switchMerchant ? 30 : 35,
              borderRadius: 5,
              padding: 5,
              marginVertical: 5,
              // borderWidth: 1,
              borderColor: '#E5E5E5',
              paddingLeft: 10,
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-Regular',
            }}
          />
        </View>
        <View style={styles.flexOne}>
          <TextInput
            defaultValue={basicReserveName}
            onChangeText={(text) => {
              setBasicReserveName(text);
            }}
            placeholderTextColor={Colors.descriptionColor}
            editable={dummyCapped}
            placeholder={'10'}
            style={{
              backgroundColor: Colors.fieldtBgColor,
              width: '50%',
              height: switchMerchant ? 30 : 35,
              borderRadius: 5,
              padding: 5,
              marginVertical: 5,
              // borderWidth: 1,
              borderColor: '#E5E5E5',
              paddingLeft: 10,
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-Regular',
            }}
          />
        </View>
      </View>
    );
  };

  // Render room draggable flatlist
  const renderRoomItem = ({ item, drag, isActive, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'white',
          margin: 3,
          padding: 10,
          borderRadius: 10,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignContent: 'center',
          textAlignVertical: 'center',
          alignItems: 'center',

          borderWidth: 0.5,
          borderColor: 'grey',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          elevation: 2,
          zIndex: 10001 + roomData.length - index,
        }}>
        <View style={styles.flexOne}>
          <TouchableOpacity
            onPress={() => {
              // for learning some method
              // console.log(item);
              // console.log([...roomData]);
              // console.log([
              //   ...roomData.filter((key) => {
              //     return key.tables === 0;
              //   }),
              // ]);
              // end here
            }}
            onPressIn={drag}
            disabled={isActive}>
            <Plus name="menu" size={30} color={Colors.blackColor} style={{}} />
          </TouchableOpacity>
        </View>
        <View style={styles.flexOne}>
          <Switch
            value={item.online}
            onSyncPress={(value) => {
              let tempData = [...roomData];
              tempData = tempData.map((task, index) => {
                if (item.roomName === task.roomName) {
                  return { ...task, online: value };
                }
                return task;
              });
              setRoomData(tempData);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
          />
        </View>
        <View style={{ flex: 3 }}>
          <View
            style={{
              width: '90%',
              height: 30,
              backgroundColor: 'gainsboro',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 5,
            }}>
            <TextInput
              defaultValue={item.roomName}
              onChangeText={(text) => {
                var tempData = [...roomData];
                for (let i = 0; i < roomData.length; i++) {
                  if (item.roomName === roomData[i].roomName) {
                    tempData[i].roomName = text;
                    break;
                  }
                }
                setRoomData(tempData);
              }}
              // placeholderTextColor={Colors.descriptionColor}
              placeholder={item.roomName}
              style={{
                textAlignVertical: 'bottom',
                // textAlign: 'center',
                width: '95%',
                paddingTop: 0,
                top: 4,
              }}
            />
          </View>
        </View>
        <View style={styles.flexOne}>
          <Text style={{ width: '80%' }}>{item.tables}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.capacity}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.combinations}</Text>
        </View>
        <View style={{ flex: 0.5 }}>
          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                'Warning',
                'Are you sure you want remove this item?',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      let tempRemoveData = 0;
                      let tempData = [...roomData];
                      for (let i = 0; i < roomData.length; i++) {
                        if (roomData[i].key === item.key) {
                          tempRemoveData = i;
                          break;
                        }
                      }
                      tempData.splice(tempRemoveData, 1);
                      setRoomData(tempData);
                      // console.log(roomData);
                    },
                  },
                ],
                { cancelable: true },
              );
            }}
            style={{ alignItems: 'center' }}>
            <Plus
              name="trash-2"
              size={25}
              color={Colors.blackColor}
              style={{ width: '75%' }}
            />
          </TouchableOpacity>
        </View>
        <View style={styles.flexOne}>
          <TouchableOpacity
            onPress={() => { }}
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                textAlign: 'center',
                textAlignVertical: 'center',
                paddingLeft: 10,
                paddingRight: 10,
                backgroundColor: 'gainsboro',
                borderRadius: 3,
                width: windowWidth * 0.05,
                height: 33,
              }}>
              View
            </Text>
            <Plus name="chevron-right" size={25} color={Colors.blackColor} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  // Render table draggable flatlist
  const renderTableItem = ({ item, drag, isActive, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'white',
          margin: 3,
          paddingVertical: 10,
          borderRadius: 10,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignContent: 'center',
          textAlignVertical: 'center',
          alignItems: 'center',

          borderWidth: 0.5,
          borderColor: 'grey',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          elevation: 2,
          zIndex: 10001 + roomData.length - index,
        }}>
        <View style={{ flex: 0.5 }}>
          <TouchableOpacity
            style={{ paddingLeft: 10 }}
            onPressIn={drag}
            disabled={isActive}>
            <Plus name="menu" size={30} color={Colors.blackColor} style={{}} />
          </TouchableOpacity>
        </View>
        <View style={{ flex: 0.5 }}>
          <Switch
            value={item.isOnline}
            onSyncPress={(value) => {
              let tempData = [...allTableCombinationListData];
              // loop for each table
              tempData = tempData.map((table) => {
                if (item.fromTable == table.roomName)
                  // loop for each row
                  for (let i = 0; i < table.tableData.length; i++) {
                    if (item.key == table.tableData[i].key) {
                      let tempTable = table;
                      tempTable.tableData[i].isOnline = value;
                      return tempTable;
                    }
                  }
                return table;
              });
              setAllTableCombinationListData(tempData);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
            style={{ height: 22 }}
          />
        </View>
        <View style={{ flex: 1.5 }}>
          <View
            style={{
              width: '90%',
              height: 30,
              backgroundColor: 'gainsboro',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 5,
            }}>
            <TextInput
              defaultValue={item.tableCode}
              onChangeText={(text) => {
                var tempData = [...allTableCombinationListData];
                for (let i = 0; i < roomData.length; i++) {
                  if (item.roomName === roomData[i].roomName) {
                    for (let j = 0; j < allTableCombinationListData.length; j++) {
                      if (item.outletTableId === allTableCombinationListData[j].outletTableId) {
                        tempData[j].tableCode = text;
                      }
                      break;
                    }
                  }
                }
                setAllTableCombinationListData(tempData);
              }}
              // placeholderTextColor={Colors.descriptionColor}
              placeholder={item.roomName}
              style={{
                textAlignVertical: 'bottom',
                // textAlign: 'center',
                width: '95%',
                paddingTop: 0,
                top: 4,
              }}
            />
          </View>
        </View>

        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={minMaxOptions}
                value={item.paxMin}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].paxMin = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={minMaxOptions}
                value={item.paxMin}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].paxMin = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={minMaxOptions}
                value={item.capacity}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].capacity = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={minMaxOptions}
                value={item.capacity}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].capacity = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={roomAreaOption}
                value={item.outletSectionId ? item.outletSectionId : ''}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].outletSectionId = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={roomAreaOption}
                value={item.outletSectionId ? item.outletSectionId : ''}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].outletSectionId = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={turnTimeOption}
                value={item.turnTime}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].turnTime = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={turnTimeOption}
                value={item.turnTime}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].turnTime = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={priorityOption}
                value={item.priority}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].priority = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={priorityOption}
                value={item.priority}
                onValueChange={(value) => {
                  let tempData = [...allTableCombinationListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].priority = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableCombinationListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View
          style={[
            {
              alignItems: 'flex-end',
              flex: 0.3,
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              let tempData = [...allTableCombinationListData];
              // loop for each table
              for (let i = 0; i < tempData.length; i++) {
                if (allTableCombinationListData[i].roomName === item.fromTable) {
                  for (let j = 0; j < tempData[i].tableData.length; j++) {
                    if (tempData[i].tableData[j].key === item.key) {
                      tempData[i].tableData[j].expandOption =
                        !item.expandOption;
                      break;
                    }
                  }
                }
              }
              setAllTableCombinationListData(tempData);
              // console.log(tempData);
              // // console.log(item);
            }}
            style={{}}>
            <Plus
              name="more-vertical"
              size={25}
              color={Colors.blackColor}
              style={{}}
            />
          </TouchableOpacity>

          {/* Popup more options */}
          <View>
            {item.expandOption ? (
              <View
                style={[
                  {
                    // position: 'absolute',
                    width: 200,
                    justifyContent: 'center',
                    alignItems: 'center',
                    // marginLeft: -110,
                    // marginTop: -110,
                    // zIndex: 1,
                    flexDirection: 'column',
                    backgroundColor: '#FFFFFF',
                    borderWidth: 1,
                    borderColor: Colors.highlightColor,
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 2,
                  },
                  switchMerchant ? { height: 25 } : {},
                ]}>
                <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      Alert.alert('Delete table');
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          { marginLeft: 5, justifyContent: 'center' },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Delete table
                      </Text>
                    </View>
                    <View
                      style={[
                        {
                          paddingLeft: 12,
                          justifyContent: 'center',
                        },
                        switchMerchant ? {} : {},
                      ]}>
                      <Plus
                        name="trash-2"
                        size={13}
                        color={Colors.blackColor}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      Alert.alert('Duplicate');
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          { marginLeft: 5, justifyContent: 'center' },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Duplicate
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      Alert.alert('Create combination');
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          {
                            marginLeft: 5,
                            justifyContent: 'center',
                            color: 'deepskyblue',
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Create combination
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
          </View>
        </View>
      </View>
    );
  };
  // Render table combination draggable flatlist
  const renderTableCombinationItem = ({ item, drag, isActive, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'white',
          marginVertical: 5,
          paddingVertical: 10,
          borderRadius: 10,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignContent: 'center',
          textAlignVertical: 'center',
          alignItems: 'center',

          borderWidth: 0.5,
          borderColor: 'grey',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          elevation: 2,
        }}>
        <View style={{ flex: 0.5 }}>
          <TouchableOpacity
            onPressIn={drag}
            disabled={isActive}
            style={{ paddingLeft: 10 }}>
            <Plus name="menu" size={30} color={Colors.blackColor} style={{}} />
          </TouchableOpacity>
        </View>
        <View style={{ flex: 0.5 }}>
          <Switch
            value={item.isOnline}
            onSyncPress={(value) => {
              let tempData = [...allTableCombinationListData];
              // loop for each table
              tempData = tempData.map((table) => {
                if (item.fromTable == table.roomName)
                  // loop for each row
                  for (let i = 0; i < table.tableData.length; i++) {
                    if (item.key == table.tableData[i].key) {
                      let tempTable = table;
                      tempTable.tableData[i].isOnline = value;
                      return tempTable;
                    }
                  }
                return table;
              });
              setAllTableCombinationListData(tempData);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
          />
        </View>
        <View style={{ flex: 1.5 }}>
          <View
            style={{
              width: '90%',
              height: 30,
              backgroundColor: 'gainsboro',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 5,
            }}>
            <Text
              style={{
                textAlignVertical: 'center',
                textAlign: 'center',
                // height: 33,
              }}>
              {item.tableCode}
            </Text>
          </View>
        </View>

        <View style={styles.flexOne}>
          {/* <DropDownPicker
            items={minMaxOptions}
            placeholder={item.paxMin}
            style={{
              width: '70%',
              // paddingVertical: 0,
              // borderWidth: 0,
              // zIndex: 10001 + 24 - index * 4,
            }}
            arrowColor={'black'}
            arrowSize={switchMerchant ? 8 : 14}
            arrowStyle={{ fontWeight: 'bold' }}
            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
            dropDownMaxHeight={150}
            dropDownStyle={{
              width: '70%',
              // width: switchMerchant ? 30 : 60,
              // height: switchMerchant ? 100 : 120,
              // backgroundColor: Colors.fieldtBgColor,
              // borderRadius: 10,
              // borderWidth: 1,
              // textAlign: 'left',
              // position: 'relative',
              // position: 'absolute',
              // zIndex: 2,
              // elevation: 10001 + 24 - index,
            }}
            globalTextStyle={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 12,
              color: Colors.fontDark,
              marginLeft: 5,
            }}
            onChangeItem={(item) => { }}
          /> */}

          <View style={styles.tabletRnPickerViewStyle}>
            <RNPickerSelect
              placeholder={{}}
              style={{
                inputAndroidContainer: {
                  height: 35,
                  justifyContent: 'center',
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
                inputAndroid: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                inputIOS: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                viewContainer: {
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  height: 35,
                  width: '90%',
                  justifyContent: 'center',
                  fontSize: 16,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
              }}
              items={minMaxOptions}
              value={item.paxMin}
              onValueChange={(value) => {
                var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                let tempData = [...allTableCombinationListData];
                // loop for each table
                tempData = tempData.map((table) => {
                  if (item.fromTable == table.roomName)
                    // loop for each row
                    for (let i = 0; i < table.tableData.length; i++) {
                      if (item.key == table.tableData[i].key) {
                        let tempTable = table;
                        tempTable.tableData[i].paxMin = parseInt(parsedValue);
                        return tempTable;
                      }
                    }
                  return table;
                });
                setAllTableCombinationListData(tempData);
              }}
            />
          </View>
        </View>
        <View style={styles.flexOne}>
          {/* <DropDownPicker
            items={minMaxOptions}
            placeholder={item.max}
            style={{
              width: '70%',
              // width: switchMerchant ? 30 : 60,
              // paddingVertical: 0,
              // borderWidth: 0,
            }}
            arrowColor={'black'}
            arrowSize={switchMerchant ? 8 : 14}
            arrowStyle={{ fontWeight: 'bold' }}
            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
            dropDownMaxHeight={150}
            dropDownStyle={{
              width: '70%',
              // width: switchMerchant ? 30 : 60,
              // height: switchMerchant ? 100 : 120,
              // backgroundColor: Colors.fieldtBgColor,
              // borderRadius: 10,
              // borderWidth: 1,
              // textAlign: 'left',
              // position: 'relative',
            }}
            globalTextStyle={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 12,
              color: Colors.fontDark,
              marginLeft: 5,
            }}
            onChangeItem={(item) => { }}
          /> */}
          <View style={styles.tabletRnPickerViewStyle}>
            <RNPickerSelect
              placeholder={{}}
              style={{
                inputAndroidContainer: {
                  height: 35,
                  justifyContent: 'center',
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
                inputAndroid: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                inputIOS: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                viewContainer: {
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  height: 35,
                  width: '90%',
                  justifyContent: 'center',
                  fontSize: 16,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
              }}
              items={minMaxOptions}
              value={item.capacity}
              onValueChange={(value) => {
                var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                let tempData = [...allTableCombinationListData];
                // loop for each table
                tempData = tempData.map((table) => {
                  if (item.fromTable == table.roomName)
                    // loop for each row
                    for (let i = 0; i < table.tableData.length; i++) {
                      if (item.key == table.tableData[i].key) {
                        let tempTable = table;
                        tempTable.tableData[i].capacity = parseInt(parsedValue);
                        return tempTable;
                      }
                    }
                  return table;
                });
                setAllTableCombinationListData(tempData);
              }}
            />
          </View>
        </View>
        <View style={styles.flexOne}>
          {/* <DropDownPicker
            items={turnTimeOption}
            placeholder={item.turnTime}
            style={{
              width: '80%',
              // width: switchMerchant ? 50 : 100,
              // paddingVertical: 0,
              // borderWidth: 0,
            }}
            arrowColor={'black'}
            arrowSize={switchMerchant ? 8 : 14}
            arrowStyle={{ fontWeight: 'bold' }}
            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
            dropDownMaxHeight={150}
            dropDownStyle={{
              width: '80%',
              // width: switchMerchant ? 50 : 100,
              // height: switchMerchant ? 100 : 120,
              // backgroundColor: Colors.fieldtBgColor,
              // borderRadius: 10,
              // borderWidth: 1,
              // textAlign: 'left',
              // position: 'relative',
            }}
            globalTextStyle={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 12,
              color: Colors.fontDark,
              marginLeft: 5,
            }}
            onChangeItem={(item) => { }}
          /> */}

          <View style={styles.tabletRnPickerViewStyle}>
            <RNPickerSelect
              placeholder={{}}
              style={{
                inputAndroidContainer: {
                  height: 35,
                  justifyContent: 'center',
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
                inputAndroid: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                inputIOS: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                viewContainer: {
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  height: 35,
                  width: '90%',
                  justifyContent: 'center',
                  fontSize: 16,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
              }}
              items={turnTimeOption}
              value={item.turnTime}
              onValueChange={(value) => {
                let tempData = [...allTableCombinationListData];
                // loop for each table
                tempData = tempData.map((table) => {
                  if (item.fromTable == table.roomName)
                    // loop for each row
                    for (let i = 0; i < table.tableData.length; i++) {
                      if (item.key == table.tableData[i].key) {
                        let tempTable = table;
                        tempTable.tableData[i].turnTime = value;
                        return tempTable;
                      }
                    }
                  return table;
                });
                setAllTableCombinationListData(tempData);
              }}
            />
          </View>
        </View>
        <View style={styles.flexOne}>
          {/* <DropDownPicker
            items={priorityOption}
            placeholder={item.priority}
            style={{
              width: '80%',
              // width: switchMerchant ? 50 : 100,
              // paddingVertical: 0,
              // borderWidth: 0,
            }}
            arrowColor={'black'}
            arrowSize={switchMerchant ? 8 : 14}
            arrowStyle={{ fontWeight: 'bold' }}
            itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
            dropDownMaxHeight={150}
            dropDownStyle={{
              width: '80%',
              // width: switchMerchant ? 50 : 100,
              // height: switchMerchant ? 100 : 120,
              // backgroundColor: Colors.fieldtBgColor,
              // borderRadius: 10,
              // borderWidth: 1,
              // textAlign: 'left',
              // position: 'relative',
            }}
            globalTextStyle={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 12,
              color: Colors.fontDark,
              marginLeft: 5,
            }}
            onChangeItem={(item) => { }}
          /> */}

          <View style={styles.tabletRnPickerViewStyle}>
            <RNPickerSelect
              placeholder={{}}
              style={{
                inputAndroidContainer: {
                  height: 35,
                  justifyContent: 'center',
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
                inputAndroid: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                inputIOS: {
                  //backgroundColor: '#fafafa',
                  color: 'black',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  borderRadius: 5,
                  width: '100%',
                  paddingHorizontal: 10,
                  height: 35,
                  paddingLeft: 12,
                  textAlign: 'center',
                },
                viewContainer: {
                  backgroundColor: '#fafafa',
                  borderRadius: 4,
                  height: 35,
                  width: '90%',
                  justifyContent: 'center',
                  fontSize: 16,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                },
              }}
              items={priorityOption}
              value={item.priority}
              onValueChange={(value) => {
                let tempData = [...allTableCombinationListData];
                // loop for each table
                tempData = tempData.map((table) => {
                  if (item.fromTable == table.roomName)
                    // loop for each row
                    for (let i = 0; i < table.tableData.length; i++) {
                      if (item.key == table.tableData[i].key) {
                        let tempTable = table;
                        tempTable.tableData[i].priority = value;
                        return tempTable;
                      }
                    }
                  return table;
                });
                setAllTableCombinationListData(tempData);
              }}
            />
          </View>
        </View>
        <View style={{ flex: 0.5 }}>
          <TouchableOpacity
            onPress={() => {
              setIsNew(false);
              setShowEditTableModal(true);

              setSelectedCombination(item);

              setSelectedSectionId(item.outletSectionId);
            }}
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                textAlign: 'center',
                textAlignVertical: 'center',
                paddingHorizontal: 10,
                backgroundColor: 'gainsboro',
                borderRadius: 3,
                height: 33,
              }}>
              Edit
            </Text>
          </TouchableOpacity>
        </View>
        <View style={{ flex: 0.3 }}>
          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                'Info',
                `Are you sure you want to remove table combination ${item.tableCode}?`,
                [
                  {
                    text: 'NO',
                    onPress: () => {
                      // deleteOutletTable();
                    },
                  },
                  {
                    text: 'YES',
                    onPress: () => {
                      var body = {
                        outletId: currOutlet.uniqueId,
                        tableCombinationId: item.outletTableCombinationId,
                      };

                      ApiClient.POST(API.deleteOutletTableCombination, body)
                        // APILocal.deleteOutletTable({ body: body, uid: firebaseUid })
                        .then((result) => {
                          if (result && result.status === 'success') {
                            // setSeatingModal(false);

                            Alert.alert('Info', 'Table combination has been removed');
                          } else {
                            Alert.alert('Failed to remove table combination');
                          }
                        })
                        .catch((err) => {
                          // console.log(err);
                        });
                    },
                  },
                ],
                { cancelable: false },
              );
            }}
            style={{ alignItems: 'flex-start' }}>
            <Plus
              name="trash-2"
              size={25}
              color={Colors.blackColor}
              style={{}}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  //Render table combination edit modal
  const renderEditTableModalItem = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'white',
          marginVertical: 3,
          // paddingHorizontal: 10,
          borderRadius: 10,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignContent: 'center',
          textAlignVertical: 'center',
          alignItems: 'center',
          width: windowWidth * 0.73,

          borderWidth: 0.5,
          borderColor: 'grey',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          elevation: 2,
        }}>
        <View style={styles.flexOne}>
          <CheckBox
            disabled={false}
            value={item.isTick}
            onValueChange={(newValue) => {
              var tickedTablesNum = allTableListData.find(rowData => rowData.outletSectionId === item.outletSectionId).tableData.filter(t => t.isTick).length;

              if (tickedTablesNum >= 4 && newValue) {
                Alert.alert('Info', 'Cannot join more than 4 tables.');
              }
              else {
                let tempData = [...allTableListData];
                // loop for each table

                tempData = tempData.map((table) => {
                  if (item.fromTable == table.roomName)
                    // loop for each row
                    for (let i = 0; i < table.tableData.length; i++) {
                      if (item.key == table.tableData[i].key) {
                        // check first

                        let tempTable = table;
                        tempTable.tableData[i].isTick = newValue;
                        return tempTable;
                      }
                    }
                  return table;
                });
                setAllTableListData(tempData);
                // console.log(tempData);
              }
            }}
            tintColors={{ true: Colors.darkBgColor, false: Colors.blackColor }}
          />
        </View>
        <View style={{ flex: 3 }}>
          <Text>{item.tableCode}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.paxMin}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.capacity}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.turnTime}</Text>
        </View>
        <View style={styles.flexOne}>
          <Text>{item.priority}</Text>
        </View>
      </View>
    );
  };

  // navigation
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Venue Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: Colors.whiteColor,
        flexDirection: 'column',
      }}>
      {/* Modal start */}

      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        animationType="slide"
        transparent={true}
        visible={showEditTableModal}
        onRequestClose={() => {
          setShowEditTableModal(false);
        }}>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                height: windowHeight * 0.7,
                width: windowWidth * 0.8,
              },
            ]}>
            <View style={styles.closeButton}>
              <TouchableOpacity
                onPress={() => {
                  setShowEditTableModal(false);
                }}>
                <AntDesign
                  name="closecircle"
                  size={switchMerchant ? 15 : 25}
                  color={Colors.fieldtTxtColor}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.modalTitle}>
              <Text>{`Combination`}</Text>
            </View>

            <View style={styles.modalBody}>
              {/* Title */}
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  justifyContent: 'flex-start',
                  height: 30,
                  alignContent: 'center',
                  textAlignVertical: 'center',
                  alignItems: 'center',
                  width: windowWidth * 0.73,
                }}>
                <View style={styles.flexOne} />
                <Text style={{ flex: 3 }}>Table name</Text>
                <Text style={styles.flexOne}>Min</Text>
                <Text style={styles.flexOne}>Max</Text>
                <Text style={styles.flexOne}>Turn time (min)</Text>
                <Text style={styles.flexOne}>Booking priority</Text>
              </View>

              {/* Flat list */}
              <View style={{ flex: 7 }}>
                <FlatList
                  // ref={flatListRef}
                  // HARD CODED FIRST TABLE DATA INTO THE MODAL ? ################
                  // data={allTableCombinationListData[0].tableData}
                  data={
                    allTableListData.find(rowData => rowData.outletSectionId === selectedSectionId)
                      ?
                      (allTableListData.find(rowData => rowData.outletSectionId === selectedSectionId).tableData || [])
                      :
                      []
                  }
                  renderItem={renderEditTableModalItem}
                  keyExtractor={(item, index) => String(index)}
                  // style={{marginTop: 10, paddingHorizontal: 10,}}
                  initialNumToRender={8}
                  nestedScrollEnabled={true}
                  contentContainerStyle={{}}
                />
              </View>
            </View>

            {
              resultedCombination && resultedCombination.outletTableCombinationId
                ?
                <View style={{ alignItems: 'center', marginVertical: 10 }}>
                  <Text style={{ marginBottom: 10 }}>Combination Result</Text>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        height: 30,
                        alignContent: 'center',
                        textAlignVertical: 'center',
                        alignItems: 'center',
                        width: windowWidth * 0.73,
                      }}>
                      <Text style={{ flex: 0.5, paddingLeft: 10 }}>Online</Text>
                      <Text style={{ flex: 1.5 }}>Combination name</Text>
                      <Text style={styles.flexOne}>Min</Text>
                      <Text style={styles.flexOne}>Max</Text>
                      <Text style={styles.flexOne}>Turn time</Text>
                      {/* <Text style={styles.flexOne}>Short time</Text> */}
                      <Text style={styles.flexOne}>Booking priority</Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderRadius: 5,
                        width: windowWidth * 0.73,
                        paddingVertical: 5,
                      }}>
                      <View
                        style={{
                          flex: 0.5,
                          paddingLeft: 10,
                          justifyContent: 'center',
                        }}>
                        <Switch
                          value={resultedCombination.isOnline}
                          onSyncPress={(value) => {
                            // setSwitchOnline(!switchOnline);

                            setResultedCombination({
                              ...resultedCombination,
                              isOnline: value,
                            });
                          }}
                          circleColorActive={Colors.whiteColor}
                          circleColorInactive={Colors.fieldtTxtColor}
                          backgroundActive={Colors.primaryColor}
                        />
                      </View>
                      <View style={{ flex: 1.5 }}>
                        <View
                          style={{
                            width: '90%',
                            height: 35,
                            backgroundColor: 'gainsboro',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 5,
                          }}>
                          <TextInput
                            defaultValue={resultedCombination.tableCode}
                            onChangeText={(text) => {
                              // setComNameModal(text);

                              setResultedCombination({
                                ...resultedCombination,
                                tableCode: text,
                              });
                            }}
                            onFocus={(text) => {
                              // setComNameModal(text);

                              setResultedCombination({
                                ...resultedCombination,
                                tableCode: text,
                              });
                            }}
                            style={{
                              textAlignVertical: 'bottom',
                              // textAlign: 'center',
                              width: '95%',
                              paddingTop: 0,
                              top: 4,
                            }}
                          />
                        </View>
                      </View>
                      <View style={styles.flexOne}>
                        {switchMerchant ? (
                          <View style={styles.phoneRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              useNativeAndroidPickerStyle={false}
                              style={{
                                inputAndroid: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={minMaxOptions}
                              value={resultedCombination.paxMin}
                              onValueChange={(value) => {
                                // setMinModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  paxMin: value,
                                });
                              }}
                            />
                          </View>
                        ) : (
                          <View style={styles.tabletRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              style={{
                                inputAndroidContainer: {
                                  height: 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: 35,
                                  width: '90%',
                                  justifyContent: 'center',
                                  fontSize: 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={minMaxOptions}
                              value={resultedCombination.paxMin}
                              onValueChange={(value) => {
                                // setMinModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  paxMin: value,
                                });
                              }}
                            />
                          </View>
                        )}
                      </View>
                      <View style={styles.flexOne}>
                        {switchMerchant ? (
                          <View style={styles.phoneRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              useNativeAndroidPickerStyle={false}
                              style={{
                                inputAndroid: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={minMaxOptions}
                              value={resultedCombination.capacity}
                              onValueChange={(value) => {
                                // setMinModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  capacity: value,
                                });
                              }}
                            />
                          </View>
                        )
                          :
                          (
                            <View style={styles.tabletRnPickerViewStyle}>
                              <RNPickerSelect
                                placeholder={{}}
                                style={{
                                  inputAndroidContainer: {
                                    height: 35,
                                    justifyContent: 'center',
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                  inputAndroid: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: '100%',
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: '100%',
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  viewContainer: {
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    height: 35,
                                    width: '90%',
                                    justifyContent: 'center',
                                    fontSize: 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                }}
                                items={minMaxOptions}
                                value={resultedCombination.capacity}
                                onValueChange={(value) => {
                                  // setMaxModal(value);

                                  setResultedCombination({
                                    ...resultedCombination,
                                    capacity: value,
                                  });
                                }}
                              />
                            </View>
                          )
                        }
                      </View>
                      <View style={styles.flexOne}>
                        {switchMerchant ? (
                          <View style={styles.phoneRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              useNativeAndroidPickerStyle={false}
                              style={{
                                inputAndroid: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={turnTimeOption}
                              value={resultedCombination.turnTime}
                              onValueChange={(value) => {
                                // setTurnTimeModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  turnTime: value,
                                });
                              }}
                            />
                          </View>
                        ) : (
                          <View style={styles.tabletRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              style={{
                                inputAndroidContainer: {
                                  height: 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: 35,
                                  width: '90%',
                                  justifyContent: 'center',
                                  fontSize: 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={turnTimeOption}
                              value={resultedCombination.turnTime}
                              onValueChange={(value) => {
                                // setTurnTimeModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  turnTime: value,
                                });
                              }}
                            />
                          </View>
                        )}
                      </View>
                      {/* <View style={styles.flexOne}>
                        {switchMerchant ? (
                          <View style={styles.phoneRnPickerViewStyle}>
                            <RNPickerSelect
                              useNativeAndroidPickerStyle={false}
                              style={{
                                inputAndroid: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={turnTimeOption}
                              value={shortTimeModal}
                              onValueChange={(value) => {
                                setShortTimeModal(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View style={styles.tabletRnPickerViewStyle}>
                            <RNPickerSelect
                              style={{
                                inputAndroidContainer: {
                                  height: 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: 35,
                                  width: '90%',
                                  justifyContent: 'center',
                                  fontSize: 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={turnTimeOption}
                              value={shortTimeModal}
                              onValueChange={(value) => {
                                setShortTimeModal(value);
                              }}
                            />
                          </View>
                        )}
                      </View> */}
                      <View style={styles.flexOne}>
                        {switchMerchant ? (
                          <View style={styles.phoneRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              useNativeAndroidPickerStyle={false}
                              style={{
                                inputAndroid: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: 10,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={priorityOption}
                              value={resultedCombination.priority}
                              onValueChange={(value) => {
                                // setBookingModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  priority: value,
                                });
                              }}
                            />
                          </View>
                        ) : (
                          <View style={styles.tabletRnPickerViewStyle}>
                            <RNPickerSelect
                              placeholder={{}}
                              style={{
                                inputAndroidContainer: {
                                  height: 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: '100%',
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: 35,
                                  width: '90%',
                                  justifyContent: 'center',
                                  fontSize: 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={priorityOption}
                              value={resultedCombination.priority}
                              onValueChange={(value) => {
                                // setBookingModal(value);

                                setResultedCombination({
                                  ...resultedCombination,
                                  priority: value,
                                });
                              }}
                            />
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
                :
                <View style={{ marginTop: 10 }}>
                  <Text>
                    Select the tables you want to add as a combination
                  </Text>
                </View>
            }

            <View style={styles.modalBodyRow}>
              <View style={styles.modalBodyRow}>
                <TouchableOpacity
                  style={{
                    width: '40%',
                    height: 35,
                    borderRadius: 8,
                    backgroundColor: Colors.darkBgColor,
                    alignContent: 'center',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => {
                    createOrUpdateTableCombination();

                    setShowEditTableModal(false);
                  }}>
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      textAlignVertical: 'center',
                      textAlign: 'center',
                    }}>
                    {isNew
                      ? 'Add table combination'
                      : 'Update table combination'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </ModalView>

      {/* Modal end */}

      {/* Top bar */}
      <View
        style={{
          flexDirection: 'row',
          backgroundColor: Colors.darkBgColor,
          justifyContent: 'space-between',
          alignItems: 'center',
          flex: 0.8,
        }}>
        <View style={{ flex: 0.25 }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
            onPress={() => {
              props.navigation.navigate('NewSettingsScreen');
            }}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
              }}>
              <Plus name="x" size={30} color={Colors.whiteColor} style={{}} />
            </View>
          </TouchableOpacity>
        </View>

        {/* <View
          style={[
            styles.flexOne,
            {
              backgroundColor:
                openPage == 'room' ? Colors.modalBgColor : Colors.darkBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              CommonStore.update((s) => {
                s.venueSettingPage = 'room';
              });
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: openPage == 'room' ? 'underline' : 'none',
              }}>
              Room setup
            </Text>
          </TouchableOpacity>
        </View> */}
        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.darkBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              // CommonStore.update((s) => {
              //   s.venueSettingPage = 'table';
              // });
              navigation.navigate('VenueSettingsTableSetupScreen');
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'none',
              }}>
              Table setup
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.modalBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              //   CommonStore.update((s) => {
              //     s.venueSettingPage = 'tableCombinations';
              //   });
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'underline',
              }}>
              Table Combinations
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.darkBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              //   CommonStore.update((s) => {
              //     s.venueSettingPage = 'reservation';
              //   });
              navigation.navigate('VenueSettingsReservationScreen');
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'none',
              }}>
              Reservation Availability
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Page Content */}
      <View style={{ flex: 9 }}>
        <View
          style={{
            margin: 10,
          }}>
          <ScrollView>
            {/* Each table view */}
            <View
              style={{ flexDirection: 'column', justifyContent: 'flex-start' }}>
              {allTableCombinationListData.map((table) => {
                return (
                  <View style={{ flex: 2 }}>
                    {/* hide/unhide table button */}
                    <View
                      style={{
                        padding: 10,
                        justifyContent: 'flex-start',
                        flexDirection: 'row',
                        marginBottom: 10,

                        alignItems: 'center',
                      }}>
                      <View style={{ flex: 1 }}>
                        <TouchableOpacity
                          style={{
                            alignItems: 'center',
                            alignContent: 'center',
                            flexDirection: 'row',
                          }}
                          onPress={() => {
                            let tempData = [...allTableCombinationListData];
                            for (
                              let i = 0;
                              i < allTableCombinationListData.length;
                              i++
                            ) {
                              if (
                                table.roomName ===
                                allTableCombinationListData[i].roomName
                              ) {
                                tempData[i].expand =
                                  !allTableCombinationListData[i].expand;
                                break;
                              }
                            }
                            setAllTableCombinationListData(tempData);
                          }}>
                          <Text style={{ paddingHorizontal: 5 }}>
                            {table.roomName}
                          </Text>
                          {table.expand ? (
                            <Plus
                              name="chevron-down"
                              size={20}
                              color={Colors.blackColor}
                              style={{ width: '75%' }}
                            />
                          ) : (
                            <Plus
                              name="chevron-up"
                              size={20}
                              color={Colors.blackColor}
                              style={{ width: '75%' }}
                            />
                          )}
                        </TouchableOpacity>
                      </View>

                      <View style={{ flex: 8 }} />

                      <View style={{ flex: 1 }}>
                        {/* <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            marginLeft: 10,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            // save all tables for current section

                            var tableList = [];

                            if (allTableCombinationListData.find(row => row.outletSectionId === table.outletSectionId)) {
                              tableList = allTableCombinationListData.find(row => row.outletSectionId === table.outletSectionId).tableData;
                            }

                            var body = {
                              // tableId: selectedSlotId,
                              // tableList: table.tableData,
                              tableList: tableList,
                            };

                            // ApiClient.POST(API.updateOutletTableMultiple, body, false)
                            APILocal.updateOutletTableMultiple({ body: body, uid: firebaseUid })
                              .then((result) => {
                                if (result && result.status === 'success') {
                                  // setAddTableModal(false);

                                  Alert.alert('Success', 'Saved successfully.');
                                } else {
                                  Alert.alert('Error', result.message);

                                  // setAddTableModal(false);
                                }
                              })
                              .catch((err) => console.log(err));
                          }}>
                          <Text
                            style={[
                              {
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Bold',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            SAVE
                          </Text>
                        </TouchableOpacity> */}
                      </View>
                    </View>

                    {table.expand ? (
                      <View>
                        {/* Table title */}
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            height: 30,
                            alignContent: 'center',
                            textAlignVertical: 'center',
                            alignItems: 'center',
                            // borderWidth: 1,
                            // marginHorizontal: 4,
                          }}>
                          <View style={{ flex: 0.5 }} />
                          <Text style={{ flex: 0.5 }}>Online</Text>
                          <Text style={{ flex: 1.5 }}>Combination name</Text>
                          <Text style={styles.flexOne}>Min</Text>
                          <Text style={styles.flexOne}>Max</Text>
                          <Text style={styles.flexOne}>Turn time</Text>
                          <Text style={styles.flexOne}>Booking priority</Text>
                          <Text style={{ flex: 0.5 }}>Edit tables</Text>
                          <Text style={{ flex: 0.3 }}>Delete</Text>
                        </View>

                        {/* Table content */}
                        <View
                          style={{
                            flexDirection: 'column',
                          }}>
                          {/* Draggable Flatlist */}
                          <DraggableFlatList
                            data={table.tableData}
                            style={{
                              maxHeight: windowHeight * 0.5,
                            }}
                            onDragEnd={({ data }) => {
                              let tempData = [...allTableCombinationListData];
                              tempData = tempData.map((table) => {
                                if (data[0].fromTable == table.roomName)
                                  return { ...table, tableData: data };
                                return table;
                              });
                              setAllTableCombinationListData(tempData);
                            }}
                            keyExtractor={(item, index) => index.toString()}
                            renderItem={renderTableCombinationItem}
                            contentContainerStyle={{}}
                            // scrollEnabled={false}
                            renderPlaceholder={() => (
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: 'darkgrey',
                                  borderRadius: 8,
                                }}
                              />
                            )}
                          />

                          {/* Add table combination button */}
                          <View
                            style={{
                              flex: 1,
                              marginTop: 10,
                              marginHorizontal: 5,
                              marginBottom: 20,
                              padding: 10,
                              flexDirection: 'row',
                            }}>
                            <View style={{ flex: 1 }}>
                              <TouchableOpacity
                                onPress={() => {
                                  let tempData = [
                                    ...allTableCombinationListData,
                                  ];
                                  tempData.map((item, index) => {
                                    // console.log(item.tableData);
                                    for (
                                      let i = 0;
                                      i < item.tableData.length;
                                      i++
                                    ) {
                                      tempData[0].tableData[i].isTick = false;
                                    }
                                  });
                                  setAllTableCombinationListData(tempData);
                                  setIsNew(true);
                                  setShowEditTableModal(true);

                                  setSelectedCombination(null);

                                  setSelectedSectionId(table.outletSectionId);
                                }}
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'flex-start',
                                  alignContent: 'flex-start',
                                  textAlignVertical: 'center',
                                  alignItems: 'center',
                                }}>
                                <Plus
                                  name="plus"
                                  size={25}
                                  color="green"
                                  style={{
                                    alignContent: 'center',
                                    alignItems: 'center',
                                    padding: 7,
                                    borderWidth: 1,
                                    width: 40,
                                    height: 40,
                                    borderRadius: 5,
                                  }}
                                />
                                <Text
                                  style={{
                                    marginHorizontal: 4,
                                    paddingHorizontal: 4,
                                    textAlignVertical: 'center',
                                  }}>
                                  Add table combination
                                </Text>
                              </TouchableOpacity>
                            </View>
                            <View style={{ flex: 9 }} />
                          </View>
                        </View>
                      </View>
                    ) : (
                      <View></View>
                    )}
                  </View>
                );
              })}
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.5,
    height: Dimensions.get('window').height * 0.2,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
  },
  flexOne: {
    flex: 1,
    // borderWidth: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    // height: windowHeight * 0.5,
    // width: windowWidth * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.02,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.01,
    top: Dimensions.get('window').height * 0.02,
    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    // flex: 1,
    alignItems: 'center',
    // borderWidth: 1,
  },
  modalBody: {
    flex: 8,
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexDirection: 'column',
  },
  modalBodyRow: {
    flex: 1,
    justifyContent: 'space-around',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 28,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  addModalBodySize: {
    width: Dimensions.get('window').width * 0.3,
  },
  addModalBodyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: Dimensions.get('window').width * 0.01,
    paddingVertical: Dimensions.get('window').height * 0.05,
  },
  addModalButton: {
    backgroundColor: Colors.primaryColor,
    paddingVertical: Dimensions.get('window').height * 0.01,
    paddingHorizontal: Dimensions.get('window').width * 0.02,
    borderRadius: 5,
  },
  // tabletRnPickerStyle: {
  //   inputAndroidContainer: {
  //     height: 35,
  //     justifyContent: 'center',
  //     backgroundColor: '#fafafa',
  //     borderRadius: 4,
  //     shadowColor: '#000',
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.22,
  //     shadowRadius: 3.22,
  //     elevation: 1,
  //   },
  //   inputAndroid: {
  //     //backgroundColor: '#fafafa',
  //     color: 'black',
  //     fontFamily: 'NunitoSans-Regular',
  //     fontSize: 16,
  //     borderWidth: 1,
  //     borderColor: Colors.primaryColor,
  //     borderRadius: 5,
  //     width: 145,
  //     paddingHorizontal: 10,
  //     height: 35,
  //     paddingLeft: 12,
  //     textAlign: 'center',
  //   },
  //   inputIOS: {
  //     //backgroundColor: '#fafafa',
  //     color: 'black',
  //     fontFamily: 'NunitoSans-Regular',
  //     fontSize: 16,
  //     borderWidth: 1,
  //     borderColor: Colors.primaryColor,
  //     borderRadius: 5,
  //     width: 145,
  //     paddingHorizontal: 10,
  //     height: 35,
  //     paddingLeft: 12,
  //     textAlign: 'center',
  //   },
  //   viewContainer: {
  //     backgroundColor: '#fafafa',
  //     borderRadius: 4,
  //     height: 35,
  //     width: 145,
  //     justifyContent: 'center',
  //     fontSize: 16,
  //     shadowColor: '#000',
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.22,
  //     shadowRadius: 3.22,
  //     elevation: 1,
  //   },
  // },
  tabletRnPickerViewStyle: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  // phoneRnPickerStyle: {
  //   inputAndroid: {
  //     fontSize: 10,
  //     paddingVertical: 5,
  //     color: 'black',
  //     textAlign: 'center',
  //   },
  //   inputIOS: {
  //     fontSize: 10,
  //     paddingVertical: 5,
  //     color: 'black',
  //     textAlign: 'center',
  //   },
  // },
  phoneRnPickerViewStyle: {
    backgroundColor: '#fafafa',
    // backgroundColor: 'green',
    borderRadius: 4,
    height: Dimensions.get('window').height * 0.08,
    width: Dimensions.get('window').width * 0.13,
    justifyContent: 'center',
    fontSize: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    // left: windowWidth * -0.002,
  },
  spaceBetweenFullWidth: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    marginVertical: Dimensions.get('window').height * 0.01,
    height: isTablet() ? 40 : 35,
  },
});

export default VenueSettingsCombinationScreen;
