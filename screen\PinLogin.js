import { Text } from "react-native-fast-text";
import React, { Component } from 'react'
import {
    StyleSheet,
    Image,
    View,
    TextInput,
    TouchableOpacity,
    Alert,
    Modal as ModalComponent,
    Dimensions,
    Platform,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import Icon from 'react-native-vector-icons/Ionicons'
import * as User from '../util/User';
import * as Token from '../util/Token';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
    getTransformForScreenOutsideNavigation,
    isTablet, performResize
} from '../util/common';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import auth from '@react-native-firebase/auth';
import { APP_TYPE, ROLE_TYPE } from '../constant/common';
// import { es } from 'date-fns/locale';
import AIcon from 'react-native-vector-icons/AntDesign';
import moment from 'moment';
import NetInfo from "@react-native-community/netinfo";
import APILocal from '../util/apiLocalReplacers';
import { connectToPrinter } from '../util/printer';
// import { storageMMKV } from '../util/storageMMKV';
import Entypo from 'react-native-vector-icons/Entypo';
import { OutletStore } from '../store/outletStore';
import { TableStore } from '../store/tableStore';

//const switchMerchant = CommonStore.useState(s => s.switchMerchant);
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

class PinLogin extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {
            email: null,
            password: null,
            pinNo: "",
            firstPin: "",
            openingAmount: "",
            showModal1: false,
            showModal2: false,
            showModal3: false,
            showModal4: false,
            options: null,
            currentShift: "",
            clockoutModal: false,

            isAuthenticating: false,

            clockOutName: '',
            clockOutDateTime: '',
            clockOutHours: '',
        }
    }


    componentDidMount() {
        this.checkAsyncStorage();
    }

    async checkAsyncStorage() {
        const email = await AsyncStorage.getItem('email');

        if (email) {
            // means login using email/password before

        }
        else {
            this.props.switchLogin();
        }
    }

    _logout = async () => {
        await AsyncStorage.clear();
        Token.clear();
        User.setlogin(false);
        User.getRefreshMainScreen();

        global.funcSwitchShowApp(false);
    }

    // function here

    _pinLogin = async () => {
        // validate pin

        this.setState({
            isAuthenticating: true,
        });

        try {
            // check for any outletid in async storage
            const currOutletId = await AsyncStorage.getItem('currOutletId');

            // if not then cannot login (alert need sign in by admin/owner ?) 
            if (currOutletId == null) {
                Alert.alert(
                    'Alert',
                    'Please sign in as admin/owner',
                    [
                        { text: 'OK', onPress: () => { } },
                    ],
                    { cancelable: false }
                )
                return;
            }

            NetInfo.fetch().then(async state => {
                // console.log("Connection type", state.type);
                // console.log("Is connected?", state.isInternetReachable);
                // console.log(state);

                // if (false) { // for offline testing
                if (state.isInternetReachable) {
                    // normal scenario

                    let body = {
                        pin: this.state.pinNo,
                        outletId: currOutletId,
                    }

                    // if have check the pin in firebase with that outletid 
                    const res = await ApiClient.POST(API.logInWithPin, body);

                    console.log(res);

                    // need to get the custom token to sign in
                    if (res && res.status == 'success') {
                        const { data } = res;
                        const { token } = data;

                        // await AsyncStorage.setItem('isPrintingKDAndOS', '0');

                        const userCredential = await auth().signInWithCustomToken(token);

                        const { user } = userCredential;
                        let firebaseToken = await user.getIdToken();

                        ApiClient.GET(`${API.getToken + firebaseToken}&app=${APP_TYPE.MERCHANT}`).then(async (result) => {
                            // setLoading(false);              

                            if (result && result.merchantId) {
                                if (result && result.token) {
                                    Token.setToken(result.token);
                                    Token.setRefreshToken(result.refreshToken);

                                    AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                                    // global.forceShowApp = true;

                                    CommonStore.update(s => {
                                        s.isUserActive = true;
                                    });

                                    global.udiData.ssa1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                    // switch showApp
                                    this.props.switchShowApp(true);

                                    global.isOnLoginPage = false;
                                    global.isOnPinLoginPage = false;

                                    if (result.noSignoutFN !== undefined) {
                                        global.noSignoutFN = result.noSignoutFN;
                                    }

                                    if (result.noSignoutC !== undefined) {
                                        global.noSignoutC = result.noSignoutC;
                                    }

                                    if (result.noSignoutI !== undefined) {
                                        global.noSignoutI = result.noSignoutI;
                                    }

                                    // performResize(
                                    //     {
                                    //         windowPhysicalPixels: {
                                    //             // height: 2160,
                                    //             // width: 1620,
                                    //             // scale: 2,
                                    //             height: global.windowWidthOriginal,
                                    //             width: global.windowHeightOriginal,
                                    //             scale: global.fontScaleOriginal
                                    //         },
                                    //     },
                                    //     'iPad 9th Generation',
                                    //     false,
                                    //     false,
                                    //     true,
                                    //     global.windowWidthOriginal,
                                    //     global.windowHeightOriginal,
                                    //     global.fontScaleOriginal,
                                    // );

                                    ApiClient.GET(API.userAdmin).then(async (userData) => {
                                        User.setUserData(userData);
                                        User.setName(userData.name);
                                        User.setRefreshToken(userData.refreshToken);
                                        User.setUserId(userData.firebaseUid);
                                        User.setlogin(true);
                                        User.setMerchantId(userData.merchantId);
                                        User.setOutletId(userData.outletId);

                                        global.currUserRole = userData.role;

                                        if (userData.noSignoutFN !== undefined) {
                                            global.noSignoutFN = userData.noSignoutFN;
                                        }

                                        if (userData.noSignoutC !== undefined) {
                                            global.noSignoutC = userData.noSignoutC;
                                        }

                                        if (userData.noSignoutI !== undefined) {
                                            global.noSignoutI = userData.noSignoutI;
                                        }

                                        UserStore.update(s => {
                                            s.firebaseUid = userData.firebaseUid;
                                            s.merchantId = userData.merchantId;

                                            s.role = userData.role;

                                            s.isAlphaUser = userData.isAlphaUser ? true : false;

                                            s.isBetaUser = userData.isBetaUser ? true : false;

                                            s.privileges = userData.privileges;
                                            s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                                            s.pinNo = userData.pinNo || '';

                                            s.userManagedCategory = userData.userManagedCategory ? userData.userManagedCategory : null;
                                            s.isIndividualShift = userData.isIndividualShift ? userData.isIndividualShift : false;

                                            s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                                        });

                                        global.privileges_state = userData.privileges;

                                        if (
                                            // 2025-06-17 14:47 PM - Commented out
                                            userData.role === ROLE_TYPE.ADMIN && userData.pinNo === this.state.pinNo
                                            // userData.role === ROLE_TYPE.ADMIN
                                        ) {
                                            global.privileges = [
                                                "EMPLOYEES",
                                                "OPERATION",
                                                "PRODUCT",
                                                "INVENTORY",
                                                "INVENTORY_COMPOSITE",
                                                "DOCKET",
                                                "VOUCHER",
                                                "PROMOTION",
                                                "CRM",
                                                "LOYALTY",
                                                "TRANSACTIONS",
                                                "REPORT",
                                                "RESERVATIONS",

                                                // for action
                                                'REFUND_ORDER',

                                                'SETTINGS',

                                                'QUEUE',

                                                'OPEN_CASH_DRAWER',

                                                'KDS',

                                                'UPSELLING',

                                                // for Kitchen

                                                'REJECT_ITEM',
                                                'CANCEL_ORDER',
                                                //'REFUND_tORDER',

                                                'MANUAL_DISCOUNT',
                                            ];
                                        } else {
                                            global.privileges = global.privileges_state || [];
                                        }

                                        if (userData.role === ROLE_TYPE.ADMIN) {
                                            // await AsyncStorage.setItem(
                                            //     'email',
                                            //     email
                                            // );
                                            // await AsyncStorage.setItem(
                                            //     'password',
                                            //     password
                                            // );

                                            AsyncStorage.setItem('last.accessToken', result.token ? result.token : '');

                                            AsyncStorage.setItem(
                                                'last.userData',
                                                userData ? JSON.stringify(userData) : JSON.stringify({})
                                            );

                                            AsyncStorage.setItem(
                                                'last.refreshToken',
                                                userData.refreshToken ? userData.refreshToken : ''
                                            );
                                        }

                                        AsyncStorage.setItem(
                                            'loggedIn',
                                            "true"
                                        );

                                        AsyncStorage.setItem(
                                            'userData',
                                            userData ? JSON.stringify(userData) : JSON.stringify({})
                                        );

                                        AsyncStorage.setItem(
                                            'refreshToken',
                                            userData.refreshToken ? userData.refreshToken : ''
                                        );

                                        ////////////////////////////////////

                                        AsyncStorage.setItem(
                                            'merchantId',
                                            userData.merchantId ? userData.merchantId : ''
                                        );

                                        AsyncStorage.setItem(
                                            'role',
                                            userData.role ? userData.role : ''
                                        );

                                        AsyncStorage.setItem(
                                            'firebaseUid',
                                            userData.firebaseUid ? userData.firebaseUid : ''
                                        );

                                        if (userData.isAlphaUser) {
                                            AsyncStorage.setItem(
                                                'isAlphaUser',
                                                '1'
                                            );
                                        }
                                        else {
                                            AsyncStorage.setItem(
                                                'isAlphaUser',
                                                '0'
                                            );
                                        }

                                        if (userData.isBetaUser) {
                                            AsyncStorage.setItem(
                                                'isBetaUser',
                                                '1'
                                            );
                                        }
                                        else {
                                            AsyncStorage.setItem(
                                                'isBetaUser',
                                                '0'
                                            );
                                        }

                                        AsyncStorage.setItem(
                                            'privileges',
                                            JSON.stringify(userData.privileges || [])
                                        );

                                        await AsyncStorage.setItem(
                                            'screensToBlock',
                                            JSON.stringify(userData.screensToBlock || [])
                                        );

                                        AsyncStorage.setItem(
                                            'currOutletId',
                                            userData.outletId ? userData.outletId : '',
                                        );

                                        MerchantStore.update(s => {
                                            s.currOutletId = userData.outletId;
                                        });

                                        global.signInAlready = true;

                                        global.udiData.cl1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                        this.props.checkLogin(true);

                                        CommonStore.update(s => {
                                            s.isAuthenticating = false;
                                        });

                                        // clock in 
                                        let dateTime = Date.now();

                                        let body = {
                                            employeeId: userData.firebaseUid,
                                            loginTime: dateTime,

                                            merchantId: userData.merchantId,
                                            outletId: userData.outletId,
                                        }

                                        // 2022-06-16 - No need first
                                        // // ApiClient.POST(API.updateUserClockInOut, body)
                                        // APILocal.updateUserClockInOut({ body, uid: userData.firebaseUid })
                                        //     .then((result) => {
                                        //         // console.log('updateUserClockIn', result);
                                        //     });

                                        ///////////////////////////////

                                        // 2023-10-30 - to set the supportCodeData again, from previous support login

                                        const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
                                        const supportCodeData = JSON.parse(supportCodeDataRaw);

                                        console.log('supportCodeData');
                                        console.log(supportCodeData);

                                        if (supportCodeData) {
                                            global.supportCodeData = supportCodeData;
                                        }

                                        ///////////////////////////////

                                        this.setState({
                                            isAuthenticating: false,
                                        });

                                        CommonStore.update((s) => {
                                            s.isCheckingOutTakeaway = false;
                                            s.selectedOutletTable = {};
                                            s.checkingOutTakeawayOrder = {};
                                            s.checkingOutTakeawayOrderList = [];
                                        })

                                        TableStore.update((s) => {
                                            s.isLeaveTablePaymentSummary = false;
                                        })
                                    });
                                }
                                else {
                                    Alert.alert('Login failed', "Invalid merchant account", [
                                        {
                                            text: "OK", onPress: () => {
                                                // setLoadingModal(false)
                                            }
                                        }
                                    ],
                                        { cancelable: false });

                                    CommonStore.update(s => {
                                        s.isAuthenticating = false;
                                    });

                                    this.setState({
                                        isAuthenticating: false,
                                    });
                                }
                            } else {
                                Alert.alert('Login failed', "Invalid merchant account", [
                                    {
                                        text: "OK", onPress: () => {
                                            // setLoadingModal(false)
                                        }
                                    }
                                ],
                                    { cancelable: false });

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });

                                this.setState({
                                    isAuthenticating: false,
                                });
                            }
                        });
                    }
                    else if (res === undefined) {
                        global.udiData.cl2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        this.props.checkLogin(true);
                    }
                    else {
                        // can try to login to the support user

                        const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
                        const supportCodeData = JSON.parse(supportCodeDataRaw);

                        if (supportCodeData && this.state.pinNo === supportCodeData.pinNo) {
                            // const { data } = res;
                            // const { token } = data;

                            var resAnonymous = await auth().signInAnonymously(
                                // email,
                                // password
                            );

                            let userAnonymous = resAnonymous.user;
                            let firebaseToken = await userAnonymous.getIdToken();

                            // const userCredential = await auth().signInWithCustomToken(token);

                            // const user = userCredential.user;
                            // let firebaseToken = await user.getIdToken();

                            // await AsyncStorage.setItem('isPrintingKDAndOS', '0');

                            ApiClient.GET(`${API.getTokenForSupport + firebaseToken}&app=${APP_TYPE.MERCHANT}&code=${supportCodeData.code}`).then(async (result) => {
                                // setLoading(false);              

                                if (result && result.merchantId) {
                                    if (result && result.token) {
                                        Token.setToken(result.token);
                                        Token.setRefreshToken(result.refreshToken);

                                        await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                                        // global.forceShowApp = true;

                                        CommonStore.update(s => {
                                            s.isUserActive = true;
                                        });

                                        global.udiData.ssa2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                        // switch showApp
                                        this.props.switchShowApp(true);

                                        global.isOnLoginPage = false;
                                        global.isOnPinLoginPage = false;

                                        if (result.noSignoutFN !== undefined) {
                                            global.noSignoutFN = result.noSignoutFN;
                                        }

                                        if (result.noSignoutC !== undefined) {
                                            global.noSignoutC = result.noSignoutC;
                                        }

                                        if (result.noSignoutI !== undefined) {
                                            global.noSignoutI = result.noSignoutI;
                                        }

                                        // performResize(
                                        //     {
                                        //         windowPhysicalPixels: {
                                        //             // height: 2160,
                                        //             // width: 1620,
                                        //             // scale: 2,
                                        //             height: global.windowWidthOriginal,
                                        //             width: global.windowHeightOriginal,
                                        //             scale: global.fontScaleOriginal
                                        //         },
                                        //     },
                                        //     'iPad 9th Generation',
                                        //     false,
                                        //     false,
                                        //     true,
                                        //     global.windowWidthOriginal,
                                        //     global.windowHeightOriginal,
                                        //     global.fontScaleOriginal,
                                        // );

                                        ApiClient.GET(API.userAdminForSupport + supportCodeData.code).then(async (userData) => {
                                            CommonStore.update(s => {
                                                s.supportCodeData = (userData && userData.supportCode) ? userData.supportCode : null;
                                            });

                                            await AsyncStorage.setItem('supportCodeData', (userData && userData.supportCode) ? JSON.stringify(userData.supportCode) : '');

                                            User.setUserData(userData);
                                            User.setName(userData.name);
                                            User.setRefreshToken(userData.refreshToken);
                                            User.setUserId(userData.firebaseUid);
                                            User.setlogin(true);
                                            User.setMerchantId(userData.merchantId);
                                            User.setOutletId(userData.outletId);

                                            global.currUserRole = userData.role;

                                            if (userData.noSignoutFN !== undefined) {
                                                global.noSignoutFN = userData.noSignoutFN;
                                            }

                                            if (userData.noSignoutC !== undefined) {
                                                global.noSignoutC = userData.noSignoutC;
                                            }

                                            if (userData.noSignoutI !== undefined) {
                                                global.noSignoutI = userData.noSignoutI;
                                            }

                                            UserStore.update(s => {
                                                s.firebaseUid = userData.firebaseUid;
                                                s.merchantId = userData.merchantId;
                                                s.role = userData.role;

                                                s.isAlphaUser = userData.isAlphaUser ? true : false;

                                                s.isBetaUser = userData.isBetaUser ? true : false;

                                                s.privileges = userData.privileges;
                                                s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                                                s.pinNo = userData.pinNo || '';

                                                s.userManagedCategory = userData.userManagedCategory ? userData.userManagedCategory : null;
                                                s.isIndividualShift = userData.isIndividualShift ? userData.isIndividualShift : false;

                                                s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                                            });

                                            global.privileges_state = userData.privileges;

                                            if (userData.role === ROLE_TYPE.ADMIN && userData.pinNo === this.state.pinNo) {
                                                global.privileges = [
                                                    "EMPLOYEES",
                                                    "OPERATION",
                                                    "PRODUCT",
                                                    "INVENTORY",
                                                    "INVENTORY_COMPOSITE",
                                                    "DOCKET",
                                                    "VOUCHER",
                                                    "PROMOTION",
                                                    "CRM",
                                                    "LOYALTY",
                                                    "TRANSACTIONS",
                                                    "REPORT",
                                                    "RESERVATIONS",

                                                    // for action
                                                    'REFUND_ORDER',

                                                    'SETTINGS',

                                                    'QUEUE',

                                                    'OPEN_CASH_DRAWER',

                                                    'KDS',

                                                    'UPSELLING',

                                                    // for Kitchen

                                                    'REJECT_ITEM',
                                                    'CANCEL_ORDER',
                                                    //'REFUND_tORDER',

                                                    'MANUAL_DISCOUNT',
                                                ];
                                            } else {
                                                global.privileges = global.privileges_state || [];
                                            }

                                            // if (userData.role === ROLE_TYPE.ADMIN) {
                                            //     // await AsyncStorage.setItem(
                                            //     //     'email',
                                            //     //     email
                                            //     // );
                                            //     // await AsyncStorage.setItem(
                                            //     //     'password',
                                            //     //     password
                                            //     // );

                                            //     await AsyncStorage.setItem('last.accessToken', result.token);

                                            //     await AsyncStorage.setItem(
                                            //         'last.userData',
                                            //         JSON.stringify(userData)
                                            //     );

                                            //     await AsyncStorage.setItem(
                                            //         'last.refreshToken',
                                            //         userData.refreshToken
                                            //     );
                                            // }

                                            await AsyncStorage.setItem(
                                                'loggedIn',
                                                "true"
                                            );

                                            await AsyncStorage.setItem(
                                                'userData',
                                                userData ? JSON.stringify(userData) : JSON.stringify(userData)
                                            );

                                            await AsyncStorage.setItem(
                                                'refreshToken',
                                                userData.refreshToken ? userData.refreshToken : ''
                                            );

                                            ////////////////////////////////////

                                            await AsyncStorage.setItem(
                                                'merchantId',
                                                userData.merchantId ? userData.merchantId : ''
                                            );

                                            await AsyncStorage.setItem(
                                                'role',
                                                userData.role ? userData.role : ''
                                            );

                                            await AsyncStorage.setItem(
                                                'firebaseUid',
                                                userData.firebaseUid ? userData.firebaseUid : ''
                                            );

                                            if (userData.isAlphaUser) {
                                                await AsyncStorage.setItem(
                                                    'isAlphaUser',
                                                    '1'
                                                );
                                            }
                                            else {
                                                await AsyncStorage.setItem(
                                                    'isAlphaUser',
                                                    '0'
                                                );
                                            }

                                            if (userData.isBetaUser) {
                                                await AsyncStorage.setItem(
                                                    'isBetaUser',
                                                    '1'
                                                );
                                            }
                                            else {
                                                await AsyncStorage.setItem(
                                                    'isBetaUser',
                                                    '0'
                                                );
                                            }

                                            await AsyncStorage.setItem(
                                                'privileges',
                                                JSON.stringify(userData.privileges || [])
                                            );

                                            await AsyncStorage.setItem(
                                                'screensToBlock',
                                                JSON.stringify(userData.screensToBlock || [])
                                            );

                                            await AsyncStorage.setItem(
                                                'currOutletId',
                                                userData.outletId ? userData.outletId : '',
                                            );

                                            MerchantStore.update(s => {
                                                s.currOutletId = userData.outletId;
                                            });

                                            global.signInAlready = true;

                                            global.udiData.cl3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                            this.props.checkLogin(true);

                                            CommonStore.update(s => {
                                                s.isAuthenticating = false;
                                            });

                                            // clock in 
                                            // let dateTime = Date.now();

                                            // let body = {
                                            //     employeeId: userData.firebaseUid,
                                            //     loginTime: dateTime,

                                            //     merchantId: userData.merchantId,
                                            //     outletId: userData.outletId,
                                            // }

                                            // // ApiClient.POST(API.updateUserClockInOut, body)
                                            // APILocal.updateUserClockInOut({ body: body, uid: userData.firebaseUid })
                                            //     .then((result) => {
                                            //         // console.log('updateUserClockIn', result);
                                            //     });

                                            this.setState({
                                                isAuthenticating: false,
                                            });

                                            CommonStore.update((s) => {
                                                s.isCheckingOutTakeaway = false;
                                                s.selectedOutletTable = {};
                                                s.checkingOutTakeawayOrder = {};
                                                s.checkingOutTakeawayOrderList = [];
                                            })

                                            TableStore.update((s) => {
                                                s.isLeaveTablePaymentSummary = false;
                                            })

                                            const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
                                            const supportCodeData = JSON.parse(supportCodeDataRaw);

                                            console.log('supportCodeData');
                                            console.log(supportCodeData);

                                            if (supportCodeData) {
                                                global.supportCodeData = supportCodeData;
                                            }
                                        });
                                    }
                                    else {
                                        Alert.alert('Login failed', "Invalid merchant account", [
                                            {
                                                text: "OK", onPress: () => {
                                                    // setLoadingModal(false)
                                                }
                                            }
                                        ],
                                            { cancelable: false });

                                        CommonStore.update(s => {
                                            s.isAuthenticating = false;
                                        });

                                        this.setState({
                                            isAuthenticating: false,
                                        });
                                    }
                                } else {
                                    Alert.alert('Login failed', "Invalid merchant account", [
                                        {
                                            text: "OK", onPress: () => {
                                                // setLoadingModal(false)
                                            }
                                        }
                                    ],
                                        { cancelable: false });

                                    CommonStore.update(s => {
                                        s.isAuthenticating = false;
                                    });

                                    this.setState({
                                        isAuthenticating: false,
                                    });
                                }
                            });
                        }
                        else {
                            Alert.alert(`Incorrect Pin`, `Please try again.`);

                            this.setState({
                                isAuthenticating: false,
                            });
                        }
                    }
                }
                else {
                    // offline scenario

                    // get stored employee data first

                    const storedEmployeesRaw = await AsyncStorage.getItem('employees');

                    let storedEmployees = [];
                    if (storedEmployeesRaw) {
                        storedEmployees = JSON.parse(storedEmployeesRaw);

                        if (typeof storedEmployees !== 'object') {
                            storedEmployees = [];
                        }
                    }

                    if (storedEmployees && storedEmployees.length > 0) {
                        // means got synced employee data, can continue


                    }
                    else {
                        Alert.alert(`Info`, `Please sign in using the admin account under internet connected environment first.`);

                        this.setState({
                            isAuthenticating: false,
                        });

                        return;
                    }

                    let userData = storedEmployees.find(employee => employee.pinNo === this.state.pinNo);

                    if (userData) {
                        // found the employee with matched pin no

                        // help to sign in first

                        console.log('sign in now');

                        // var resAnonymous = await auth().signInAnonymously();

                        // console.log('after auth sign in');

                        // let userAnonymous = resAnonymous.user;
                        // let firebaseToken = await userAnonymous.getIdToken();

                        // const { data } = res;
                        // const { token } = data;

                        // const userCredential = await auth().signInWithCustomToken(token);

                        // const { user } = userCredential;
                        // let firebaseToken = await user.getIdToken();

                        AsyncStorage.multiGet(['accessToken', 'refreshToken']).then(async (items, err) => {
                            if (items) {
                                Token.setToken(items[0]);
                                Token.setRefreshToken(items[1]);

                                // AsyncStorage.setItem('accessToken', result.token);

                                // global.forceShowApp = true;

                                CommonStore.update(s => {
                                    s.isUserActive = true;
                                });

                                global.udiData.ssa3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                // switch showApp
                                this.props.switchShowApp(true);

                                global.isOnLoginPage = false;
                                global.isOnPinLoginPage = false;

                                // performResize(
                                //     {
                                //         windowPhysicalPixels: {
                                //             // height: 2160,
                                //             // width: 1620,
                                //             // scale: 2,
                                //             height: global.windowWidthOriginal,
                                //             width: global.windowHeightOriginal,
                                //             scale: global.fontScaleOriginal
                                //         },
                                //     },
                                //     'iPad 9th Generation',
                                //     false,
                                //     false,
                                //     true,
                                //     global.windowWidthOriginal,
                                //     global.windowHeightOriginal,
                                //     global.fontScaleOriginal,
                                // );

                                User.setUserData(userData);
                                User.setName(userData.name);
                                User.setRefreshToken(userData.refreshToken);
                                User.setUserId(userData.firebaseUid);
                                User.setlogin(true);
                                User.setMerchantId(userData.merchantId);
                                User.setOutletId(userData.outletId);

                                global.currUserRole = userData.role;

                                UserStore.update(s => {
                                    s.firebaseUid = userData.firebaseUid;
                                    s.merchantId = userData.merchantId;
                                    s.role = userData.role;

                                    s.isAlphaUser = userData.isAlphaUser ? true : false;

                                    s.isBetaUser = userData.isBetaUser ? true : false;

                                    s.privileges = userData.privileges;
                                    s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                                    s.pinNo = userData.pinNo || '';

                                    s.userManagedCategory = userData.userManagedCategory ? userData.userManagedCategory : null;
                                    s.isIndividualShift = userData.isIndividualShift ? userData.isIndividualShift : false;

                                    s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                                });

                                global.privileges_state = userData.privileges;

                                if (userData.role === ROLE_TYPE.ADMIN && userData.pinNo === this.state.pinNo) {
                                    global.privileges = [
                                        "EMPLOYEES",
                                        "OPERATION",
                                        "PRODUCT",
                                        "INVENTORY",
                                        "INVENTORY_COMPOSITE",
                                        "DOCKET",
                                        "VOUCHER",
                                        "PROMOTION",
                                        "CRM",
                                        "LOYALTY",
                                        "TRANSACTIONS",
                                        "REPORT",
                                        "RESERVATIONS",

                                        // for action
                                        'REFUND_ORDER',

                                        'SETTINGS',

                                        'QUEUE',

                                        'OPEN_CASH_DRAWER',

                                        'KDS',

                                        'UPSELLING',

                                        // for Kitchen

                                        'REJECT_ITEM',
                                        'CANCEL_ORDER',
                                        //'REFUND_tORDER',

                                        'MANUAL_DISCOUNT',
                                    ];
                                } else {
                                    global.privileges = global.privileges_state || [];
                                }

                                if (userData.role === ROLE_TYPE.ADMIN) {
                                    // await AsyncStorage.setItem(
                                    //     'email',
                                    //     email
                                    // );
                                    // await AsyncStorage.setItem(
                                    //     'password',
                                    //     password
                                    // );

                                    AsyncStorage.setItem('last.accessToken', items[0] ? items[0] : '');

                                    AsyncStorage.setItem(
                                        'last.userData',
                                        userData ? JSON.stringify(userData) : JSON.stringify({})
                                    );

                                    AsyncStorage.setItem(
                                        'last.refreshToken',
                                        userData.refreshToken ? userData.refreshToken : ''
                                    );
                                }

                                AsyncStorage.setItem(
                                    'loggedIn',
                                    "true"
                                );

                                AsyncStorage.setItem(
                                    'userData',
                                    userData ? JSON.stringify(userData) : JSON.stringify({})
                                );

                                AsyncStorage.setItem(
                                    'refreshToken',
                                    userData.refreshToken ? userData.refreshToken : ''
                                );

                                ////////////////////////////////////

                                AsyncStorage.setItem(
                                    'merchantId',
                                    userData.merchantId ? userData.merchantId : ''
                                );

                                AsyncStorage.setItem(
                                    'role',
                                    userData.role ? userData.role : ''
                                );

                                AsyncStorage.setItem(
                                    'firebaseUid',
                                    userData.firebaseUid ? userData.firebaseUid : ''
                                );

                                if (userData.isAlphaUser) {
                                    AsyncStorage.setItem(
                                        'isAlphaUser',
                                        '1'
                                    );
                                }
                                else {
                                    AsyncStorage.setItem(
                                        'isAlphaUser',
                                        '0'
                                    );
                                }

                                if (userData.isBetaUser) {
                                    AsyncStorage.setItem(
                                        'isBetaUser',
                                        '1'
                                    );
                                }
                                else {
                                    AsyncStorage.setItem(
                                        'isBetaUser',
                                        '0'
                                    );
                                }

                                AsyncStorage.setItem(
                                    'privileges',
                                    JSON.stringify(userData.privileges || [])
                                );

                                await AsyncStorage.setItem(
                                    'screensToBlock',
                                    JSON.stringify(userData.screensToBlock || [])
                                );

                                AsyncStorage.setItem(
                                    'currOutletId',
                                    userData.outletId ? userData.outletId : '',
                                );

                                MerchantStore.update(s => {
                                    s.currOutletId = userData.outletId;
                                });

                                global.signInAlready = true;

                                global.udiData.cl4 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                                this.props.checkLogin(true);

                                CommonStore.update(s => {
                                    s.isAuthenticating = false;
                                });

                                // clock in 
                                let dateTime = Date.now();

                                let body = {
                                    employeeId: userData.firebaseUid,
                                    loginTime: dateTime,

                                    merchantId: userData.merchantId,
                                    outletId: userData.outletId,
                                }

                                // 2022-06-16 - No need first
                                // // ApiClient.POST(API.updateUserClockInOut, body)
                                // APILocal.updateUserClockInOut({ body, uid: userData.firebaseUid })
                                //     .then((result) => {
                                //         // console.log('updateUserClockIn', result);
                                //     });

                                this.setState({
                                    isAuthenticating: false,
                                });

                                CommonStore.update((s) => {
                                    s.isCheckingOutTakeaway = false;
                                    s.selectedOutletTable = {};
                                    s.checkingOutTakeawayOrder = {};
                                    s.checkingOutTakeawayOrderList = [];
                                })

                                TableStore.update((s) => {
                                    s.isLeaveTablePaymentSummary = false;
                                })
                            }
                        });
                    }
                    else {
                        // didn't found the employee with matched pin no

                        Alert.alert(`Incorrect Pin`, `Please try again.`);

                        this.setState({
                            isAuthenticating: false,
                        });


                    }
                }
            });
        }
        catch (error) {
            // console.log(error);

            this.setState({
                isAuthenticating: false,
            });
        }
    }

    _clockInUser = async () => {
        // validate pin

        this.setState({
            isAuthenticating: true,
        });

        try {
            // check for any outletid in async storage
            const currOutletId = await AsyncStorage.getItem('currOutletId');

            // if not then cannot login (alert need sign in by admin/owner ?) 
            if (currOutletId == null) {
                Alert.alert(
                    'Alert',
                    'Please sign in as admin/owner',
                    [
                        { text: 'OK', onPress: () => { } },
                    ],
                    { cancelable: false }
                )
                return;
            }

            const userSnapshot = await firestore()
                .collection(Collections.User)
                .where('pinNo', '==', this.state.pinNo)
                .where('outletId', '==', currOutletId)
                .limit(1)
                .get();

            var user = null;

            if (!userSnapshot.empty) {
                user =
                    userSnapshot.docs[0].data();
            }

            if (user) {
                // clock in 
                let dateTime = Date.now();

                let body = {
                    employeeId: user.firebaseUid,
                    loginTime: dateTime,

                    merchantId: user.merchantId,
                    outletId: user.outletId,
                }

                // ApiClient.POST(API.updateUserClockInOut, body)
                APILocal.updateUserClockInOut({ body, uid: user.firebaseUid })
                    .then((result) => {
                        // console.log('updateUserClockIn', result);
                    });


                Alert.alert('Info', `Clocked in for ${user.name} successfully.`)
            }
            else {

            }

            this.setState({
                isAuthenticating: false,
            });

            // Token.clear();

            // await AsyncStorage.multiRemove([
            //     // 'accessToken',
            //     // 'userData',
            //     // 'refreshToken',
            // ]);
        }
        catch (error) {
            // console.log(error);

            this.setState({
                isAuthenticating: false,
            });
        }
    }

    _clockOutUser = async () => {
        // validate pin

        this.setState({
            isAuthenticating: true,
        });

        try {
            // check for any outletid in async storage
            const currOutletId = await AsyncStorage.getItem('currOutletId');

            // if not then cannot login (alert need sign in by admin/owner ?) 
            if (currOutletId == null) {
                Alert.alert(
                    'Alert',
                    'Please sign in as admin/owner',
                    [
                        { text: 'OK', onPress: () => { } },
                    ],
                    { cancelable: false }
                )
                return;
            }

            const userSnapshot = await firestore()
                .collection(Collections.User)
                .where('pinNo', '==', this.state.pinNo)
                .where('outletId', '==', currOutletId)
                .limit(1)
                .get();

            var user = null;

            if (!userSnapshot.empty) {
                user =
                    userSnapshot.docs[0].data();
            }

            if (user) {
                // clock in 
                let dateTime = Date.now();

                let body = {
                    employeeId: user.firebaseUid,
                    logoutTime: dateTime,

                    merchantId: user.merchantId,
                    outletId: user.outletId,
                }

                // ApiClient.POST(API.updateUserClockInOut, body)
                APILocal.updateUserClockInOut({ body, uid: user.firebaseUid })
                    .then(async (result) => {
                        // console.log('updateUserClockOut', result);

                        const employeeClockSnapshot = await firestore()
                            .collection(Collections.EmployeeClock)
                            .where('firebaseUid', '==', user.firebaseUid)
                            .limit(1)
                            .get();

                        var employeeClock = null;

                        if (!employeeClockSnapshot.empty) {
                            employeeClock =
                                employeeClockSnapshot.docs[0].data();
                        }

                        if (employeeClock) {
                            var clockRecords = [];

                            if (employeeClock &&
                                employeeClock.clockRecords &&
                                employeeClock.clockRecords.length > 0) {
                                clockRecords = employeeClock.clockRecords;
                            }
                            else {
                                // find another one

                                const employeeClockSliceSnapshot = await firestore()
                                    .collection(Collections.EmployeeClockSlice)
                                    .where('firebaseUid', '==', user.firebaseUid)
                                    .orderBy('updatedAt', 'desc')
                                    .limit(1)
                                    .get();

                                var employeeClockSlice = null;

                                if (!employeeClockSliceSnapshot.empty) {
                                    employeeClockSlice =
                                        employeeClockSliceSnapshot.docs[0].data();
                                }

                                if (employeeClockSlice &&
                                    employeeClockSlice.clockRecords &&
                                    employeeClockSlice.clockRecords.length > 0) {
                                    clockRecords = employeeClockSlice.clockRecords;
                                }
                            }

                            if (clockRecords.length > 0) {
                                var lastRecord = clockRecords[clockRecords.length - 1];

                                this.setState({
                                    clockOutName: user.name,

                                    clockOutDateTime: lastRecord.clockOutTime ? moment(lastRecord.clockOutTime).format('HH:mm') : moment().format('HH:mm'),

                                    clockOutHours: lastRecord.clockInTime && lastRecord.clockOutTime
                                        ?
                                        ((moment(lastRecord.clockOutTime).diff(lastRecord.clockInTime, 'minute') / 60).toFixed(2))
                                        :
                                        '0.00',

                                    clockoutModal: true,
                                });
                            }
                        }

                        this.setState({
                            isAuthenticating: false,
                        });

                        // Alert.alert('Info', 'Clocked out successfully.')

                        // Token.clear();

                        // await AsyncStorage.multiRemove([
                        //     'accessToken',
                        //     'userData',
                        //     'refreshToken',
                        // ]);
                    });
            }
            else {

            }

            this.setState({
                isAuthenticating: false,
            });

        }
        catch (error) {
            // console.log(error);

            this.setState({
                isAuthenticating: false,
            });
        }
    }

    onButtonPress(key) {
        if (key >= 0) {
            if (this.state.pinNo.length < 4) {
                var currPinNo = this.state.pinNo + key;

                this.setState({ pinNo: this.state.pinNo + key }, async () => {
                    // if (this.state.pinNo.length === 4 && key != -1) {
                    //     this._pinLogin();
                    // }

                    // CommonStore.update(s => {
                    //     s.enteredPinNo = currPinNo;
                    // });

                    // await AsyncStorage.setItem('enteredPinNo', currPinNo);
                    // storageMMKV.set('enteredPinNo', currPinNo);
                    await global.watermelonDBDatabase.localStorage.set('enteredPinNo', currPinNo);
                })
            }
        } else {
            if (this.state.pinNo.length > 0)
                var currPinNo = this.state.pinNo.slice(0, key);

            this.setState({ pinNo: this.state.pinNo.slice(0, key) }, async () => {
                // CommonStore.update(s => {
                //     s.enteredPinNo = currPinNo;
                // });

                // await AsyncStorage.setItem('enteredPinNo', currPinNo);
                // storageMMKV.set('enteredPinNo', currPinNo);
                await global.watermelonDBDatabase.localStorage.set('enteredPinNo', currPinNo);
            });
        }

    }

    // onOpenShiftBtn(key) {
    //     var decimal = this.state.openingAmount.split(".")[1]

    //     if (key >= 0 || key == ".") {
    //         if (this.state.openingAmount.includes("."))
    //             if (this.state.openingAmount.length < 12 && decimal.length < 2)
    //                 this.setState({ openingAmount: this.state.openingAmount + key }, function () {

    //                 })
    //         if (!this.state.openingAmount.includes(".")) {
    //             if (this.state.openingAmount.length < 12)
    //                 this.setState({ openingAmount: this.state.openingAmount + key }, function () {
    //                 })

    //         }
    //     } else {
    //         if (this.state.openingAmount.length > 0)
    //             this.setState({ openingAmount: this.state.openingAmount.slice(0, key) }, function () {
    //             })
    //     }

    // }

    // openShiftAmount() {

    //     var body = {
    //         outletId: User.getOutletId(),
    //         amount: this.state.openingAmount,
    //     }
    //     ApiClient.POST(API.openShift, body).then((result) => {
    //         if (result.success.id == User.getOutletId()) {

    //             Alert.alert("Success", "Opening amount has been set", [
    //                 { text: "OK", onPress: () => { } }
    //             ],
    //                 { cancelable: false })
    //         }
    //     });
    // }

    // getShiftStatus(outletId) {
    //     ApiClient.GET(API.getCurrentShift + outletId, false).then((result) => {
    //         if (result.true.length == 0 || result.true[0].status == 0) {
    //             this.setState({
    //                 showModal1: true
    //             });
    //             return;
    //         }
    //         else {
    //             this.props.checkPinLogin(true);
    //         }
    //     });
    // }

    // function end

    render() {
        return (
            <View style={[styles.container, {
                ...getTransformForScreenOutsideNavigation(),
            }]}>

                {console.log('pinLogin isTablet')}
                {console.log(isTablet())}

                <View style={styles.logoContainer}>
                    <Image style={[styles.logo, !isTablet() ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]} resizeMode="contain" source={require('../assets/image/logo.png')} />
                    <Text style={[styles.logoTxt, !isTablet() ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                        marginTop: 0,
                    } : {}]}>Unlimited Perks</Text>

                </View>
                <View style={styles.loginContainer}>
                    <View style={styles.shiftView}>
                        {/* <View style={styles.shiftButton}>
                            <Text style={styles.shiftText}>SHIFT CLOSED</Text>
                        </View> */}
                    </View>

                    <View style={{
                        // flex: 10, 
                        height: '100%',
                        alignItems: 'center',

                        paddingTop: '3%',
                    }}>
                        <View style={[{
                            width: '65%',
                            // flex: 4,
                            height: '30%',

                            // backgroundColor: 'red',
                        }, !isTablet() ? {
                            // flex: 5
                        } : {}]}>
                            <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 40 : 40, fontWeight: 'bold' }, !isTablet() ? {
                                fontSize: Dimensions.get('screen').width / 50,
                            } : {}]}>Login</Text>
                            {isTablet() ? <Text style={[{ fontSize: 16, paddingTop: 10, color: Colors.fieldtTxtColor }, !isTablet() ? {
                                fontSize: Dimensions.get('screen').width / 70,
                                paddingTop: 5,
                            } : {}]}>Please enter your unique PIN to continue</Text> : <></>}
                            <View style={[styles.pinContainer, !isTablet() ? {
                                paddingTop: Dimensions.get('screen').width / 50,
                            } : {}]}>
                                {/* <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{this.state.pinNo.length > 0 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{this.state.pinNo.length > 1 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{this.state.pinNo.length > 2 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{this.state.pinNo.length > 3 ? "•" : null}</Text></View> */}
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{this.state.pinNo.length > 0 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{this.state.pinNo.length > 1 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{this.state.pinNo.length > 2 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{this.state.pinNo.length > 3 ? <Entypo name='dot-single' size={35} /> : null}</View>
                            </View>
                        </View>
                        <View style={[{
                            width: '40%',
                            // flex: 7,
                            height: '45%',

                            // backgroundColor: 'blue',
                        }, !isTablet() ? {
                            width: '50%',
                            // flex: 12
                        } : {}]}>
                            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                !isTablet() ? { height: '30%' } : {}]}>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin1'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(1);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, {}, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>1</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin2'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(2);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>2</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin3'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(3);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>3</Text></View>
                                    </TouchableOpacity>
                                </View>
                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                !isTablet() ? { height: '30%' } : {}]}>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin4'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(4);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>4</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin5'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(5);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>5</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin6'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(6);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>6</Text></View>
                                    </TouchableOpacity>
                                </View>
                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                !isTablet() ? { height: '30%' } : {}]}>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin7'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(7);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>7</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin8'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(8);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>8</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPin9'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                this.onButtonPress(9);
                                            });
                                        }}>
                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>9</Text></View>
                                    </TouchableOpacity>
                                </View>
                                <View style={{ flexDirection: 'row', width: '100%' }}>
                                    <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                    !isTablet() ? { height: '30%' } : {}]}>
                                        <TouchableOpacity
                                            testID='PinLogin.buttonPinClockIn'
                                            disabled={this.state.isAuthenticating}
                                            onPress={() => {
                                                requestAnimationFrame(() => {
                                                    if (this.state.pinNo.length === 4) {
                                                        this._clockInUser();
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                                    }
                                                });
                                            }}>
                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}
                                            ><Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                            } : {}]}>Clock</Text>
                                                <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                    fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                } : {}]}>in</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            testID='PinLogin.buttonPin0'
                                            onPress={() => {
                                                requestAnimationFrame(() => {
                                                    this.onButtonPress(0);
                                                });
                                            }}>
                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>0</Text></View>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            testID='PinLogin.buttonPinClockOut'
                                            disabled={this.state.isAuthenticating}
                                            onPress={() => {
                                                requestAnimationFrame(() => {
                                                    if (this.state.pinNo.length === 4) {
                                                        this._clockOutUser();
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                                    }
                                                });

                                                // this.setState({ clockoutModal: true })
                                            }}>
                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}
                                            ><Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                            } : {}]}>Clock</Text>
                                                <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                    fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                } : {}]}>out</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <TouchableOpacity
                                        testID='PinLogin.buttonPinDelete'
                                        onPress={() => {
                                            requestAnimationFrame(() => {
                                                if (this.state.pinNo.length > 0) { this.onButtonPress(-1) }
                                            });
                                        }}>
                                        <View style={[styles.pinBtn,
                                        { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 },
                                        { backgroundColor: 'white', marginLeft: Dimensions.get('window').width * 0.025 }]}><Icon name="backspace-outline" size={!isTablet() ? (0.525 * (Dimensions.get('window').width * 0.04)) : isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? 0.525 * (Dimensions.get('window').width * 0.04) : (0.525 * (Dimensions.get('window').width * 0.05))} /></View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>

                        <TouchableOpacity
                            testID='PinLogin.buttonLogin'
                            disabled={this.state.isAuthenticating}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',

                                marginTop: '2%', // 0%
                                marginBottom: '2%',

                                width: '40%',
                            }}
                            onPress={async () => {
                                requestAnimationFrame(async () => {
                                    if (this.state.pinNo.length === 4) {
                                        this._pinLogin();

                                        await connectToPrinter('192.168.1.2'); // try to let the bluetooth popup appeared
                                    }
                                    else {
                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                    }
                                });
                            }}>
                            <View style={[Styles.button, {
                                marginTop: 0,
                                marginVertical: 0,
                                paddingVertical: 10,

                                width: '100%',
                            }, !isTablet() ? {
                                paddingVertical: 5,
                            } : {}]}>
                                <Text style={[{ color: '#ffffff', fontSize: 18 }, !isTablet() ? {
                                    fontSize: Dimensions.get('screen').width / 50,
                                } : {}]}>
                                    {this.state.isAuthenticating ? 'LOADING...' : 'LOGIN'}
                                </Text>
                            </View>
                        </TouchableOpacity>

                        {isTablet() ? <View style={{
                            backgroundColor: 'white',
                            // flex: 0.5,
                            // height: '6%',

                            // backgroundColor: 'yellow',
                        }}>
                            <Text style={[{ color: Colors.fieldtTxtColor }, !isTablet() ? {
                                fontSize: Dimensions.get('screen').width / 70,
                            } : {}]}>Don't have your unique PIN? Refer to your manager for it</Text>
                        </View> : <></>}

                        <View style={{
                            backgroundColor: 'white',
                            // flex: 2.5,
                            // height: '6%',
                            flexDirection: 'row',

                            // backgroundColor: 'purple',
                        }}>
                            <Text style={[{ color: Colors.fieldtTxtColor, marginRight: 10, }, !isTablet() ? {
                                fontSize: Dimensions.get('screen').width / 70,
                            } : {}]}>Switch login?{' '}</Text>
                            <TouchableOpacity
                                testID='PinLogin.buttonEmail'
                                onPress={async () => {
                                    requestAnimationFrame(async () => {
                                        this.props.switchLogin();

                                        global.isOnLoginPage = true;
                                        global.isOnPinLoginPage = false;

                                        // performResize(
                                        //     {
                                        //         windowPhysicalPixels: {
                                        //             // height: 2160,
                                        //             // width: 1620,
                                        //             // scale: 2,
                                        //             height: global.windowWidthOriginal,
                                        //             width: global.windowHeightOriginal,
                                        //             scale: global.fontScaleOriginal
                                        //         },
                                        //     },
                                        //     'iPad 9th Generation',
                                        //     false,
                                        //     false,
                                        //     true,
                                        //     global.windowWidthOriginal,
                                        //     global.windowHeightOriginal,
                                        //     global.fontScaleOriginal,
                                        // );
                                    });
                                }}>
                                <Text style={[{ color: Colors.primaryColor }, !isTablet() ? {
                                    fontSize: Dimensions.get('screen').width / 70,
                                } : {}]}>Email</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>

                <ModalView
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.clockoutModal}
                    transparent>
                    <View style={styles.modalContainer}>
                        <View
                            style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => { this.setState({ clockoutModal: false }); }}>
                                <AIcon
                                    name="closecircle"
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text
                                    style={[{ fontFamily: 'NunitoSans-Bold', fontSize: 24, }]}>
                                    Clock-Out
                                </Text>
                            </View>
                            <View style={styles.modalBody}>
                                <View style={{ marginBottom: 20 }}>
                                    <Text
                                        style={{ fontFamily: 'NunitoSans-Bold', fontSize: 56, }}>
                                        {this.state.clockOutDateTime}
                                    </Text>
                                </View>
                                <View style={{ marginBottom: 20 }}>
                                    <Text
                                        style={{ fontFamily: 'NunitoSans-Regular', fontSize: 16, }}>
                                        {this.state.clockOutName}
                                    </Text>
                                </View>
                                <View style={{}}>
                                    <Text
                                        style={{ fontFamily: 'NunitoSans-Regular', fontSize: 16, }}>
                                        Previous Shift
                                    </Text>
                                    <Text
                                        style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16, textAlign: 'center' }}>
                                        {`${this.state.clockOutHours} hours`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </View>
                </ModalView>
            </View >
        )
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logoTxt: {
        color: Colors.whiteColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
    },
    loginTxt: {
        color: Colors.mainTxtColor,
        fontWeight: "600",
        fontSize: 30
    },
    description: {
        color: Colors.descriptionColor,
        paddingVertical: 10
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 20,
    },
    checkBox: {
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: Colors.descriptionColor,
        width: 30,
        height: 30,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    floatbtn: {
        zIndex: 1,
        position: 'absolute',
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3
    },
    loginImg: {
        width: undefined,
        height: '100%',
        resizeMode: 'cover'
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
    },
    loginContainer: {
        backgroundColor: Colors.whiteColor,
        flex: 1
    },
    shiftView: {
        alignSelf: 'center',
        justifyContent: 'center',
        flex: 0.4,
        borderRadius: 12,
    },
    shiftButton: {
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 30,
        paddingHorizontal: 60,
        paddingVertical: 15,
    },
    shiftText: {
        fontSize: 25,
        color: Colors.fieldtTxtColor
    },
    logoContainer: {
        backgroundColor: Colors.darkBgColor,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
    },
    pinBox: {
        backgroundColor: Colors.fieldtBgColor,
        // width: 60,
        // height: 60,
        width: Dimensions.get('window').width * 0.045,
        height: Dimensions.get('window').width * 0.045,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 5,

        borderColor: Colors.secondaryColor,
        borderWidth: 0.5,
    },
    pinContainer: {
        flexDirection: 'row',
        paddingTop: 25,
        justifyContent: 'space-between'
    },
    pinBtn: {
        backgroundColor: Colors.fieldtBgColor,
        // width: 70,
        // height: 70,
        width: Dimensions.get('window').width * 0.05,
        height: Dimensions.get('window').width * 0.05,
        marginBottom: !isTablet() ? 6 : 12,
        alignContent: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        borderColor: Colors.primaryColor,
        borderWidth: 0.5,
    },
    pinNo: {
        // fontSize: 25,
        fontSize: 0.35 * (Dimensions.get('window').width * 0.04),
        fontWeight: 'bold',
    },
    modalTitle: {
        alignItems: 'center',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.25,
        width: Dimensions.get('window').width * 0.35,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        paddingHorizontal: Dimensions.get('window').width * 0.015,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },

})
export default PinLogin