import React, { Component } from 'react'
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, FlatList, TouchableOpacityBase, Button, Dimensions } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AIcon from 'react-native-vector-icons/AntDesign'
import EIcon from 'react-native-vector-icons/Entypo'
import DropDownPicker from 'react-native-dropdown-picker';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { ClipPath } from 'react-native-svg';
import Plus from '../assets/svg/Plus.svg';
import QRCode from 'react-native-qrcode-svg';
import moment from 'moment'
import OrderModal from './components/OrderModal';
	
// import { back } from 'react-native/Libraries/Animated/src/Easing';
// import OutletMenuScreen from '../phonescreen/OutletMenuScreen';
// import MenuItemDetailsScreen from '../phonescreen/MenuItemDetailsScreen';

class TableScreen extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {

            sectionArea: [],
            currentSectionArea: null,
            addSectionAreaModel: false,
            addTableModal: false,
            deleteTableModal: false,
            seatingModal: false,
            selectedSlotId: null,
            displayQrModal: false,
            viewTableOrderModal: false,
            orderDisplayIndividual: false,
            availableSeats: [],
            seatingPax: 0,
            totalTables: 0,
            changeTable: false,
            tableCapacity: '',
            tableCode: '',
            tableList: '',
            changeTableId: '',
            onRemove: '',


        }
    }

    componentDidMount() {
        this.getSectionArea();
        this.tableExchange();
        setInterval(() => { this.getSectionArea() }, 5000)
    }

    completePayment() {

        var body = {
            orderId: this.state.orderId
        }
        ApiClient.POST(API.orderDonePayment, body).then(result => {
            if (result) {
                this.setState({ refresh: true })
            }
        })

    }
    calculateColor(time) {
        if (time >= 20) {
            return Colors.tabRed;
        }
        else if (time < 20 && time >= 15) {
            return Colors.tabYellow;
        }
        else if (time < 15 || !time) {
            return Colors.tabGrey;
        }
    }

    calculateColorText(time) {
        if (time >= 20) {
            return Colors.whiteColor;
        }
        else if (time < 20 && time >= 15) {
            return Colors.blackColor;
        }
        else if (time < 15 || !time) {
            return Colors.whiteColor;
        }
    }

    getSectionArea() {
        ApiClient.GET(API.tableSection + User.getOutletId()).then((result) => {
            if (this.state.currentSectionArea != null) {
                this.setState({
                    sectionArea: result,
                })
                this.getTableBySection(this.state.currentSectionArea);

            } else {
                this.setState({
                    sectionArea: result,
                    currentSectionArea: result[0].section
                })
                this.getTableBySection(result[0].section);
            }
        }).catch(err => { console.log(err) })
    }

    addSectionArea() {
        var body = {
            outletId: User.getOutletId(),
            section: this.state.section
        }
        ApiClient.POST(API.postSection, body).then(result => {

            if (result.success.length > 0) {
                this.getSectionArea()
                this.setState({ addSectionAreaModel: false })
                Alert.alert("Section has been added")
            } else {
                Alert.alert("Error adding section")
            }
        }).catch(err => { console.log(err) })
    }

    tableExchange() {
        var body = {
            table1: this.state.selectedTableId,
            table2: this.state.changeTableId
        }

        ApiClient.POST(API.tableExchange, body).then(result => {
            if (result !== null) {
                this.setState({
                    changeTableId: '',
                    changeTable: false,
                    displayQrModal: false,
                    viewTableOrderModal: false
                })
                this.getTableBySection(this.state.currentSectionArea)
            }
        }).catch(err => { console.log(err) })
    }


                       
                 

    deleteSectionArea(sectionName) {
        var body = {
            outletId: User.getOutletId(),
            section: sectionName
        }
        ApiClient.POST(API.deleteSectionTable, body).then(result => {
            if (result.success) {
                this.getSectionArea()
                Alert.alert("Section has been removed")
            }

            else {
                !this.getSectionArea()
                Alert.alert("Section has occupied tables")
            }
        }).catch(err => { console.log(err) })
    }

    // deleteTable(selectedTableId) {
    //     var body = {
    //         tableId: selectedTableId
    //     }
    //     ApiClient.POST(API.deleteTable + selectedTableId, body, false).then(result => {
    //         if (result) {
    //             this.getTableBySection(this.state.currentSectionArea)
    //             this.setState({ deleteTableModal: false })
    //             console.log("deleteTable" , result)
    //         }
    //     }).catch(err => { console.log(err) })
    // }

    // deleteTable(selectedTableId) {
    //     if (this.getTableBySection(this.state.currentSectionArea) ||
    //         this.setState({ deleteTableModal: false })) {
    //         Alert.alert(
    //             'Error',
    //             'This table has customers');
    //         return;
    //     } else {
    //         var body = {
    //             tableId: selectedTableId
    //         };
    //         ApiClient.POST(API.deleteTable + selectedTableId, body, false).then(result => {
    //             if (result =
    //                 !this.getTableBySection(this.state.currentSectionArea) ||
    //                 !this.setState({ deleteTableModal: false })) {
    //                 Alert.alert("Table has been deleted");
    //                 console.log("deleteTable", result)
    //             }

    //         }).catch(err => { console.log(err) })
    //     }
    // };

    deleteTable(selectedTableId) {
        if (result =
            this.getTableBySection(this.state.currentSectionArea) ||
            this.setState({ deleteTableModal: true })) {
            Alert.alert(
                'Error',
                'This table is occupied');
            return;
        } else {
            var body = {
                tableId: selectedTableId
            }
            ApiClient.POST(API.deleteTable + selectedTableId, body, false).then(result => {
                if (result =
                    !this.getTableBySection(this.state.currentSectionArea) ||
                    !this.setState({ deleteTableModal: false })) {
                    Alert.alert("Table has been removed");
                    console.log("deleteTable", result)
                }

            }).catch(err => { console.log(err) })
        }
    };

    getTableBySection(sectionName) {
        ApiClient.GET(API.sectionTable + '?outletId=' + User.getOutletId() + "&sectionName=" + sectionName).then(result => {
            const tableBySlots = result.sort((a, b) =>
                a.slot - b.slot)
            var i = 0;
            const totalTables = result.filter(table =>
                table.code != null
            )
            const seatedTables = result.filter(table =>
                table.seated != 0
            )

            var totalOccupiedSeats = 0;
            for (const tables of seatedTables) {
                totalOccupiedSeats = totalOccupiedSeats + tables.seated
            }
            var totalSeats = 0;
            for (const tables of result) {
                totalSeats = totalSeats + tables.capacity
            }
            const tableList = tableBySlots
            .filter((item) => item.code !== null)
                .map((table) => {
                    return (
                        {
                            label: table.code,
                            value: table.id
                        }
                    )
                })

            this.setState({
                tableSlots: tableBySlots,
                tableList: tableList,
                totalTables: totalTables,
                seatedTables: seatedTables.length,
                totalOccupiedSeats: totalOccupiedSeats,
                totalSeats: totalSeats
            })
        }).catch(err => { console.log(err) })
    }
    onRemove = id => e => {
        setorderInfo(orderInfo.filter(orderInfo => orderInfo.id !== id));
    };

    addTable() {
        if (
            !this.state.selectedSlotId ||
            !this.state.tableCapacity ||
            !this.state.tableCode
        ) {
            Alert.alert(
                'Error',
                'Please fill in the information'
            );
            return;
        } else {
            var body = {
                tableId: this.state.selectedSlotId,
                capacity: this.state.tableCapacity,
                code: this.state.tableCode,
            };
            ApiClient.POST(API.postTable, body, false).then((result) => {

                if (result.success) {
                    this.getTableBySection(this.state.currentSectionArea)
                    this.setState({ addTableModal: true })
                    
                    console.log("ResultAddTable", result)
                    console.log("TABLECODE", this.state.tableCode)
                }
                else if (result.findDuplicate){
                    this.setState({ addTableModal: false });
                    Alert.alert(
                        'Error',
                        result.findDuplicate
                    )
                }
                  
            }).catch(err => console.log(err))
        }
    }

    occupyingSeats(seatedPax) {
        if (!this.state.selectedTableId || !this.state.seatingPax) {
            Alert.alert(
                'Error',
                'Please fill in the information'
            );
            return;
        } else {
            var body = {
                tableId: this.state.selectedTableId,
                pax: seatedPax,
                outletId: User.getOutletId(),
            };
            ApiClient.POST(API.addCustomer, body, false).then((result) => {
                if (result.outletId == User.getOutletId()) {
                    this.getTableBySection(this.state.currentSectionArea)
                    this.setState({ seatingModal: false })
                    Alert.alert("Table seated!")
                }
            });
        }
    }

    getQrCode(selectedTableId) {
        var body = {
            tableId: selectedTableId
        }
        ApiClient.POST(API.qrTableGenerate, body, false).then(result => {
            this.setState({ qrData: result })
        }).catch(err => { console.log(err) })

    }

    getOrderByTable(tableId) {
        ApiClient.GET(API.tableOrder + '?tableId=' + tableId).then((result) => {
            this.getOrderHistory()
            this.setState({
                orderInfo: result,
                orderId: result.id,
                tableOrder: result.orderItems,
                total: result.finalPrice,

            })
        }).catch(err => { console.log(err) })
    }

    addSectionButton = () => {
        return (
            <TouchableOpacity onPress={() => { this.setState({ addSectionAreaModel: true }) }}>
                <View style={[styles.sectionAreaButton, { width: Dimensions.get('screen').height * 0.04, height: Dimensions.get('screen').height * 0.037, marginRight: 6 }]}>
                    <Text style={styles.sectionAreaButtonTxt}> + </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // checkOrderItem(id) {
    //     ApiClient.POST(API.orderDeliver + id).then(result => {
    //         if (result === true)
    //             this.getTableWithDetails()
    //     }).catch(err => Alert('Error', 'Something went wrong'))
    // }
    checkOrderItem(id, e, i) {
        ApiClient.POST(API.orderDeliver + id).then(result => {
            if (result === true)
                this.getOrderHistory()
            //console.log('getOrderHistory');
            //console.log(getOrderHistory);
        }).catch(err => Alert('Error', 'Something went wrong'))
        //.catch(err => {console.log(err)})
    }

    uncheck(id, e, i) {
        ApiClient.POST(API.orderDeliverUndo + id).then(result => {
            if (result === true)
                this.getOrderHistory()
            //console.log('getOrderHistory');
            //console.log(getOrderHistory);
        }).catch(err => Alert('Error', 'Something went wrong'))
        //.catch(err => {console.log(err)})
    }

    renderSection = ({ item }) => {
        return (
            <View style={{ flexDirection: 'row' }}>
                <TouchableOpacity
                    onPress={() => { this.setState({ currentSectionArea: item.section }); this.getTableBySection(item.section) }}>
                    <View style={[styles.sectionAreaButton, item.section == this.state.currentSectionArea ? { backgroundColor: Colors.primaryColor } : null]}>
                        <Text style={[styles.sectionAreaButtonTxt, item.section == this.state.currentSectionArea ? { color: Colors.whiteColor } : null]}>{item.section}</Text>
                        <TouchableOpacity
                            style={{ position: 'absolute', right: 5 }}
                            onPress={() => { this.deleteSectionArea(item.section) }}
                            onValueChange={() => { this.checkOrderItem(item.id) }}
                        >
                            <EIcon name={"cross"} size={15} color={this.state.currentSectionArea == item.section ? '#FFFFFF' : '#4E9F7D'} />
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderTableLayout = ({ item }) => {
        if (item.code == null) { //no table
            return (
                <TouchableOpacity
                    style={styles.tableSlotDisplay}
                    onPress={() => { this.setState({ addTableModal: true, selectedSlotId: item.id }) }}>
                    <Plus height={Dimensions.get('screen').width * 0.06} width={Dimensions.get('screen').width * 0.06} />
                </TouchableOpacity>
            )
        } else if (item.code != null && item.capacity != null && item.seated == 0) { // not seated tables
            return (
                <TouchableOpacity
                    style={[styles.emptyTableDisplay, { alignItems: 'center', justifyContent: 'center', zIndex: 10 }]}
                    onPress={() => {
                        this.setState({
                            selectedTableId: item.id,
                            selectedTableCode: item.code,
                            selectedCapacity: item.capacity,
                            seatingModal: true
                        });
                    }}
                    onLongPress={() => {
                        this.setState({
                            deleteTableModal: true,
                            selectedTableCode: item.code,
                            selectedTableId: item.id
                        });
                    }}
                >
                    <View style={[styles.emptyTableDisplay, this.state.changeTable ? { height: Dimensions.get('screen').width * 0.23, width: Dimensions.get('screen').width * 0.23, position: 'absolute', elevation: 50, borderWidth: 3, borderColor: Colors.primaryColor, zIndex: 10 } : null]}>
                        <View style={{ flex: 1, justifyContent: 'space-between' }}>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                <Text>-</Text>
                                <Text style={styles.tableCode}>{item.code}</Text>
                            </View>
                            <View style={{ alignItems: 'center' }}>
                                <Text style={[styles.tableCode, { fontSize: 12 }]}>
                                    Seats: {item.seated}/{item.capacity}
                                </Text>
                            </View>
                            <View style={{ alignItems: 'flex-end' }}>
                                <Text>-</Text>
                            </View>
                        </View>
                    </View>
                </TouchableOpacity>
            )
        } else if (item.seated > 0 && item.orders.length == 0) { //seated but no orders
            return (
                <TouchableOpacity
                    style={[styles.emptyTableDisplay, { backgroundColor: this.calculateColor(item.estimateTime) }]}
                    onPress={() => {
                        this.setState({
                            selectedTableId: item.id,
                            selectedTableCode: item.code,
                            seatedModal: true,
                            displayQrModal: true
                        });
                        this.getQrCode(item.id)
                    }}
                    onLongPress={() => {
                        this.setState({
                            changeTable: !this.state.changeTable,
                            selectedTableCode: item.code,
                            selectedTableId: item.id,
                            selectedTableSeated: item.seated
                        });
                    }}
                >
                    <View style={{ flex: 1, justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={{ color: Colors.whiteColor }}>-</Text>
                            <Text style={[styles.tableCode, { color: Colors.whiteColor }]}>{item.code}</Text>
                        </View>
                        <View style={{ alignItems: 'center' }}>
                            <Text style={[styles.tableCode, { fontSize: 12, color: Colors.whiteColor }]}>
                                Seats: {item.seated}/{item.capacity}
                            </Text>
                        </View>
                        <View style={{ alignItems: 'flex-end' }}>
                            <Text style={{ color: Colors.whiteColor }}>-</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            )
        } else if (item.seated > 0 && item.orders.length > 0) { //seated with orders
            return (
                <TouchableOpacity
                    style={[styles.emptyTableDisplay, { backgroundColor: this.calculateColor(item.estimateTime) }]}
                    onPress={() => {

                        this.setState({
                            selectedTableId: item.id,
                            selectedTableCode: item.code,
                            viewTableOrderModal: true,

                            selectedTableOrder: item.orders[0].id,
                            selectedTableSeats: item.capacity,
                            selectedTableCustomers: item.seated,
                        });
                        this.getOrderByTable(item.id)
                        this.getQrCode(item.id)
                    }}
                    onLongPress={() => {
                        this.setState({
                            changeTable: !this.state.changeTable,
                            selectedTableCode: item.code,
                            selectedTableId: item.id,
                            selectedTableSeated: item.seated
                        });
                    }}
                >
                    <View style={{ flex: 1, justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', height: "33%" }}>
                            <Text style={{ fontSize: Dimensions.get('screen').width * 0.025, color: this.calculateColorText(item.estimateTime) }}>{moment(item.orders[item.orders.length - 1].orderedAt).format('LT')}</Text>
                            <Text style={[styles.tableCode, { color: this.calculateColorText(item.estimateTime) }]}>{item.code}</Text>
                        </View>
                        <View style={{ alignItems: 'center', justifyContent: 'center', height: "33%" }}>
                            <Text style={[styles.tableCode, { fontSize: 12, color: this.calculateColorText(item.estimateTime) }]}>
                                {Math.trunc(item.estimateTime)} mins
                            </Text>
                        </View>
                        <View style={{ alignItems: 'flex-end', justifyContent: 'center', height: "33%" }}>
                            <Text style={{ fontSize: Dimensions.get('screen').width * 0.025, color: this.calculateColorText(item.estimateTime) }}>Seats: {item.seated}/{item.capacity}</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            )
        }
    }

    // renderTableOrder = ({ item, remarks }) => {
    //     return (
    //         <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', height: Dimensions.get('screen').width * 0.1, marginBottom: 10 }}>
    //             <View style={{ flex: 2, alignItems: 'flex-start' }}>
    //                 <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.034 }}>{item.name}
    //                 </Text>
    //             </View>
    //             <View style={{ flex: 1, alignItems: 'center' }}>
    //                 <Text style={{ fontSize: 15, color: 'grey' }}>{item.remarks}</Text>
    //             </View>


    //             <View style={{ flex: 1, alignItems: 'center' }}>
    //                 <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.034 }}>x {item.quantity}</Text>
    //             </View>
    //             <View style={{ flex: 1, alignItems: 'flex-end' }}>
    //                 <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.034 }}>RM {item.price}</Text>
    //             </View>
    //         </View>
    //     )
    // }

    renderTableOrder = ({ item, index }) => {
        return (
            <View style={{ paddingHorizontal: 30, paddingVertical: 20, marginTop: 10, }}>
                <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                    <View style={{ flex: 2, justifyContent: 'center', alignItems: 'flex-start', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18, marginLeft: 60 }}>{item.name}</Text>
                    </View>
                    <View style={{ flex: 1, alignItems: 'center' }}>
                        <Text style={{ fontSize: 15, color: 'grey' }}>{item.remarks}</Text>
                    </View>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18 }}>x {item.quantity}</Text>
                    </View>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18 }}>RM {item.price}</Text>
                    </View>
                </View>
            </View>
        )
    }

    render() {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.fieldtBgColor }}>

                {this.state.changeTable ? <View style={{ position: 'absolute', width: Dimensions.get('screen').width, height: Dimensions.get('screen').height, backgroundColor: Colors.modalBgColor, zIndex: 1, opacity: 0.99 }} /> : null}
                

                
                <View style={{ flex: 0.8, justifyContent: 'center', flexDirection: 'row', alignItems: 'center' }}>
                    <FlatList
                        contentContainerStyle={styles.sectionAreaFlatList}
                        data={this.state.sectionArea}
                        renderItem={this.renderSection}
                        keyExtractor={(item, index) => String(index)}
                        horizontal={true}
                        showsHorizontalScrollIndicator={false}
                        ListFooterComponent={this.addSectionButton}
                    />
                </View>
                <View style={{ flex: 8, alignItems: "center" }}>
                    <FlatList
                        data={this.state.tableSlots}
                        showsVerticalScrollIndicator={false}
                        renderItem={this.renderTableLayout}
                        keyExtractor={(item, index) => String(index)}
                        numColumns={4}
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <View style={{ backgroundColor: 'white', alignItems: 'center', flexDirection: 'row' }}>
                        <View style={{ flex: 1, alignItems: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.06 }}>{this.state.seatedTables}<Text style={{ color: Colors.fieldtTxtColor, fontSize: Dimensions.get('screen').width * 0.06 }}>/{this.state.totalTables.length}</Text></Text>
                            <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.03 }}>TABLES</Text>
                        </View>
                        <View style={{ flex: 1, alignItems: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.06 }}>{this.state.totalOccupiedSeats}<Text style={{ color: Colors.fieldtTxtColor, fontSize: Dimensions.get('screen').width * 0.06 }}>/{this.state.totalSeats}</Text></Text>
                            <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.03 }}>SEATS</Text>
                        </View>
                    </View>
                </View>

                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.addSectionAreaModel}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ addSectionAreaModel: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={styles.modalTitleText}>Add Section</Text>
                                <Text style={styles.modalDescText}>Fill in the section name</Text>
                            </View>
                            <View style={styles.modalBody}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <Text style={styles.modalBodyText}>Name </Text>
                                    <TextInput
                                        onChangeText={(sectionName) => { this.setState({ section: sectionName }) }}
                                        style={{ width: Dimensions.get('screen').width * 0.4, height: 40, backgroundColor: Colors.fieldtBgColor, marginLeft: 10, borderRadius: 8 }}
                                        placeholder={"E.g: Floor 1"}
                                        textAlign={'center'}
                                        maxLength={12}
                                    />
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { this.addSectionArea() }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Save</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.addTableModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ addTableModal: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={styles.modalTitleText}>Add Table</Text>
                                <Text style={styles.modalDescText}>Fill in the table details</Text>
                            </View>
                            <View style={styles.modalBody}>
                                <View style={{ flexDirection: 'row', width: '100%' }}>
                                    <Text style={styles.modalBodyText}>Code </Text>
                                    <TextInput
                                        onChangeText={(code) => { this.setState({ tableCode: code }) }}
                                        style={{ width: Dimensions.get('screen').width * 0.4, height: 40, backgroundColor: Colors.fieldtBgColor, marginLeft: 10, borderRadius: 8 }}
                                        placeholder={"E.g: A1"}
                                        textAlign={'center'}
                                        maxLength={12}
                                    />
                                </View>
                                <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%' }}>
                                    <Text style={styles.modalBodyText}>Pax </Text>
                                    <TextInput
                                        onChangeText={(pax) => { this.setState({ tableCapacity: pax }) }}
                                        style={{ width: Dimensions.get('screen').width * 0.4, height: 40, backgroundColor: Colors.fieldtBgColor, marginLeft: 10, borderRadius: 8 }}
                                        placeholder={"E.g: 6"}
                                        keyboardType={"numeric"}
                                        textAlign={'center'}
                                        maxLength={12}
                                    />
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { 
                                    this.addTable(); 
                                }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Save</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.deleteTableModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ deleteTableModal: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalBody}>
                                <Text style={styles.modalTitleText}>Delete Table {this.state.selectedTableCode}? </Text>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { this.deleteTable(this.state.selectedTableId) }}
                                onValueChange={() => { this.checkOrderItem(item.id) }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Confirm</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                {/* 
                <Modal
                    style={{ flex: 1 }}
                    visible={this.state.deleteTableModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ deleteTableModal: false });
                                }}>
                                <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalBody}>
                                <Text style={styles.modalTitleText}>Delete Table {this.state.selectedTableCode}? </Text>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { this.deleteTable(this.state.selectedTableId) }}
                                onValueChange={() => { this.checkOrderItem(item.id) }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Confirm</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal> */}

                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.seatingModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={{
                            height: Dimensions.get('screen').width * 0.8,
                            width: Dimensions.get('screen').width * 0.8,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: Dimensions.get('screen').width * 0.07,
                            padding: Dimensions.get('screen').width * 0.07,
                            alignItems: 'center',
                            justifyContent: 'space-between'
                        }}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ seatingModal: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={styles.modalTitleText}>Seating</Text>
                                <View style={styles.tableSlotDisplay}>
                                    <Text style={[styles.modalTitleText, { fontSize: Dimensions.get('screen').width * 0.07 }]}>{this.state.selectedTableCode}</Text>
                                </View>
                            </View>
                            <View style={[styles.modalBody, { width: "100%", alignItems: 'center', }]}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%' }}>
                                    <Text style={[styles.modalBodyText, { flex: 1, fontFamily: 'NunitoSans-Bold' }]}>Table {this.state.selectedTableCode}</Text>
                                    <View style={{ justifyContent: 'space-between', flexDirection: 'row', flex: 1.0, borderWidth: StyleSheet.hairlineWidth, borderColor: Colors.fieldtTxtColor }}>
                                        <TouchableOpacity
                                            style={{ backgroundColor: Colors.primaryColor, width: Dimensions.get('screen').width * 0.065, height: Dimensions.get('screen').width * 0.065, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => { this.state.seatingPax >= 1 ? this.setState({ seatingPax: this.state.seatingPax - 1 }) : null }}>
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 10, color: Colors.whiteColor }}>-</Text>
                                        </TouchableOpacity>
                                        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.primaryColor }}>{this.state.seatingPax}</Text>
                                        </View>
                                        <TouchableOpacity
                                            style={{ backgroundColor: Colors.primaryColor, width: Dimensions.get('screen').width * 0.065, height: Dimensions.get('screen').width * 0.065, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => { this.setState({ seatingPax: this.state.seatingPax + 1 }) }}>
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 10, color: Colors.whiteColor }}>+</Text>
                                        </TouchableOpacity>
                                    </View>
                                    <View style={{ flex: 1, alignItems: 'center' }}>
                                        <Text style={{ fontFamily: 'NunitoSans-Regular' }}>Capacity {this.state.selectedCapacity}</Text>
                                    </View>
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { this.occupyingSeats(this.state.seatingPax) }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Seated</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.displayQrModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ displayQrModal: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 22
                                }}>Table {this.state.selectedTableCode}</Text>
                            </View>
                            <View style={styles.modalBody}>
                                <QRCode
                                    value={JSON.stringify(this.state.qrData)}
                                    size={Dimensions.get('screen').width * 0.55}
                                    logoBackgroundColor='transparent'
                                />
                            </View>

                            <View style={{ width: '100%', flexDirection: 'row', justifyContent: 'space-between' }}>
                                <Button title="Switch Table" color={Colors.primaryColor} onPress={() => this.setState({ changeTable: true })} />
                                <OrderModal tableId={this.state.selectedTableId} close={() => this.setState({ displayQrModal: false })} />
                             
               {/* <Button title="ORDER" color={Colors.primaryColor} onPress={() => { this.props.navigation.navigate('MenuItemDetails',  { params: { tableCode: this.state.tableCode, } }) }}/> */}
        
                                {/* <OutletMenuScreen tableId={this.state.selectedTableId} close={() => this.setState({ displayQrModal: false })} /> */}
                                {/* <MenuItemDetailsScreen tableId={this.state.selectedTableId} close={() => this.setState({ displayQrModal: false })} /> */}
                            </View>
                        </View>
                    </View>
                </Modal>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.changeTable}
                    transparent={true}
                >
                    <View style={styles.modalContainer}>
                        <View style={styles.modalView}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ changeTable: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={styles.modalTitleText}>
                                    Switch Table
                                </Text>
                            </View>
                            <View style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
                                <Text style={styles.modalBodyText}>
                                    Current Table: {this.state.selectedTableCode}
                                </Text>
                                <Text style={styles.modalBodyText}>
                                    TO
                                </Text>
                                <View style={{ flex: 1 }}>
                                    <DropDownPicker
                                        items={this.state.tableList}
                                        placeholder={"Select Table"}
                                        placeholderStyle={{ color: 'black' }}
                                        labelStyle={{ color: 'black' }}
                                        containerStyle={{ height: 50, width: 120 }}
                                        dropDownStyle={{ height: 100, width: 120 }}
                                        onChangeItem={item => { this.setState({ changeTableId: item.value }) }}
                                    />
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.modalSaveButton}
                                onPress={() => { this.tableExchange() }}>
                                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Save</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.viewTableOrderModal}
                    transparent={true}>
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, { width: Dimensions.get('screen').width * 0.9, height: Dimensions.get('screen').height * 0.6 }]}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    this.setState({ viewTableOrderModal: false, displayQrModal: false });
                                }}>
                                {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </TouchableOpacity>
                            <View style={styles.modalTitle}>
                                <Text style={styles.modalTitleText}>Table {this.state.selectedTableCode}</Text>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-around', width: '100%', alignItems: 'center', marginTop: 10 }}>
                                    <Text style={[styles.modalTitleText, { fontSize: Dimensions.get('screen').width * 0.04, marginBottom: 0 }]}>Order #{this.state.selectedTableOrder}</Text>
                                    <Text style={[styles.modalTitleText, { fontSize: Dimensions.get('screen').width * 0.04, marginBottom: 0 }]}>Seats: {this.state.selectedTableCustomers}/{this.state.selectedTableSeats}</Text>
                                    <TouchableOpacity
                                        onPress={() => { this.setState({ displayQrModal: true }) }}
                                        style={{ position: 'absolute', right: 10 }}>
                                        <Image style={{ height: Dimensions.get('screen').width * 0.1, width: Dimensions.get('screen').width * 0.1 }} resizeMode="contain" source={require('../assets/image/qr.png')} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ width: Dimensions.get('screen').width * 0.9, height: '10%', marginTop: 20, flexDirection: 'row' }}>
                                <TouchableOpacity
                                    style={{ flex: 1, backgroundColor: this.state.orderDisplayIndividual ? Colors.lightPrimary : Colors.primaryColor, alignItems: 'center', justifyContent: 'center' }}
                                    onPress={() => { this.setState({ orderDisplayIndividual: false }) }}>
                                    <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.034, color: this.state.orderDisplayIndividual ? Colors.blackColor : Colors.whiteColor }}>Summary</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={{ flex: 1, backgroundColor: this.state.orderDisplayIndividual ? Colors.primaryColor : Colors.lightPrimary, alignItems: 'center', justifyContent: 'center' }}
                                    onPress={() => { this.setState({ orderDisplayIndividual: true }) }}>
                                    <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.034, color: this.state.orderDisplayIndividual ? Colors.whiteColor : Colors.blackColor }}>Individual</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={[styles.modalBody, { width: '100%', marginTop: 20, }]}>
                                {this.state.orderDisplayIndividual ?
                                    null :
                                    <FlatList
                                        style={{ width: '100%', height: '100%' }}
                                        data={this.state.tableOrder}
                                        renderItem={this.renderTableOrder}
                                        keyExtractor={(item, index) => String(index)}
                                    />}
                            </View>
                            <View style={{ flex: 0.4, justifyContent: 'space-between', width: Dimensions.get('screen').width * 0.4, flexDirection: 'row', alignSelf: 'flex-end' }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.045, color: Colors.primaryColor }}>TOTAL</Text>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.045, color: Colors.primaryColor }}>RM {this.state.total}</Text>
                            </View>
                            <View style={{ flex: 0.2 }}>
                                <TouchableOpacity
                                    style={{ backgroundColor: Colors.primaryColor, borerRadius: 8, width: Dimensions.get('screen').width * 0.4, height: Dimensions.get('screen').width * 0.1, alignItems: 'center', justifyContent: 'center', borderRadius: Dimensions.get('screen').width * 0.015 }}
                                    onPress={() => {
                                        this.setState({
                                            deleteTableModal: true,
                                            viewTableOrderModal: false
                                        });
                                    }}>
                                    <Modal
                                        supportedOrientations={['landscape', 'portrait']}
                                        style={{ flex: 1 }}
                                        visible={this.state.deleteTableModal}
                                        transparent={true}>
                                        <View style={styles.modalContainer}>
                                            <View style={styles.modalView}>
                                                <TouchableOpacity
                                                    style={styles.closeButton}
                                                    onPress={() => {
                                                        this.setState({ deleteTableModal: false });
                                                    }}>
                                                    {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                                                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                                </TouchableOpacity>
                                                <View style={styles.modalBody}>
                                                    <Text style={styles.modalTitleText}>Delete Table {this.state.selectedTableCode}? </Text>
                                                </View>
                                                <TouchableOpacity
                                                    style={styles.modalSaveButton}
                                                    onPress={() => { this.deleteTable(this.state.selectedTableId) }}
                                                    onValueChange={() => { this.checkOrderItem(item.id) }}>
                                                    <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Confirm</Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </Modal>
                                    <TouchableOpacity
                                        style={{ backgroundColor: Colors.primaryColor, borerRadius: 8, width: Dimensions.get('screen').width * 0.4, height: Dimensions.get('screen').width * 0.1, alignItems: 'center', justifyContent: 'center', borderRadius: Dimensions.get('screen').width * 0.015 }}
                                        onPress={() => { this.deleteTable(this.state.selectedTableId) }}
                                        onValueChange={() => { this.checkOrderItem(item.id) }}>
                                        <Text style={{ fontSize: Dimensions.get('screen').width * 0.04, fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor }}>CHECKOUT</Text>
                                    </TouchableOpacity>
                                    {/* <Text style={{ fontSize: Dimensions.get('screen').width * 0.04, fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor }}>CHECKOUT</Text> */}
                                </TouchableOpacity>


                                {/* <TouchableOpacity
                                     style={styles.modalSaveButton}
                                    onPress={() => { this.deleteTable(this.state.selectedTableId) }}
                                onValueChange={() => { this.checkOrderItem(item.id) }}>
                                    <Text style={{ fontSize: Dimensions.get('screen').width * 0.04, fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor }}>CHECKOUT</Text>
                                </TouchableOpacity> */}
                            </View>


                        </View>
                    </View>
                </Modal>

            </View>
        )
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row'
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
        marginTop: 10,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
        fontFamily: 'NunitoSans-Regular'
    },
    sectionAreaButton: {
        marginLeft: 5,
        width: Dimensions.get('screen').width * 0.28,
        backgroundColor: Colors.whiteColor,
        height: Dimensions.get('screen').height * 0.04,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    sectionAreaButtonTxt: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 12,
        color: Colors.primaryColor
    },
    sectionAreaFlatList: {
        height: '100%',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Dimensions.get('screen').width * 1,
        width: Dimensions.get('screen').width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get('screen').width * 0.07,
        padding: Dimensions.get('screen').width * 0.07,
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.04,
        top: Dimensions.get('screen').width * 0.04
    },
    modalTitle: {
        alignItems: 'center',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'

    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        marginBottom: 10,
        fontSize: 22
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 16,
        color: Colors.fieldtTxtColor
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 16,
        width: "20%"

    },
    modalSaveButton: {

        width: Dimensions.get('screen').width * 0.3,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8
    },
    tableSlotDisplay: {
        width: 75,
        //Dimensions.get('screen').width * 0.2,
        height: 75,
        //Dimensions.get('screen').width * 0.2,
        margin: 5,
        borderRadius: Dimensions.get('screen').width * 0.02,
        padding: Dimensions.get('screen').width * 0.01,
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        justifyContent: 'center',

    },
    emptyTableDisplay: {
        backgroundColor: Colors.whiteColor,
        width: 75,
        //Dimensions.get('screen').width * 0.25,
        height: 75,
        //Dimensions.get('screen').width * 0.25,
        margin: 5,
        borderRadius: Dimensions.get('screen').width * 0.02,
        padding: Dimensions.get('screen').width * 0.015
    },
    tableCode: {
        fontFamily: 'NunitoSans-Bold',
        fontSize: 15
    }

})
export default TableScreen



