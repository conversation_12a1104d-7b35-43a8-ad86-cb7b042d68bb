import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Modal as ModalComponent,
  PermissionsAndroid,
  ActivityIndicator,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Switch from 'react-native-switch-pro';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AIcon from 'react-native-vector-icons/AntDesign';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import RNPickerSelect from 'react-native-picker-select';
// import { Picker } from '@react-native-picker/picker';
// import { ceil } from 'react-native-reanimated';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from 'react-native-modal-datetime-picker';
import moment from 'moment';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'rn-fetch-blob';
import {
  isTablet
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { v4 as uuidv4 } from 'uuid';
import {
  PURCHASE_ORDER_STATUS,
  PURCHASE_ORDER_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import {
  convertArrayToCSV,
  uploadImageToFirebaseStorage,
  generateEmailReport,
} from '../util/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import {
//   Table,
//   TableWrapper,
//   Row,
//   Rows,
//   Col,
//   Cols,
//   Cell,
// } from 'react-native-table-component';
import { Platform } from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);
import XLSX from 'xlsx';
const RNFS = require('@dr.pogodin/react-native-fs');
import { writeFile, readFile, DocumentDirectoryPath } from '@dr.pogodin/react-native-fs';
import AsyncImage from '../components/asyncImage';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const CreateGuests = (props) => {
  const dummyData = [
    {
      key: 1,
      name: 'Ah Boy',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 2,
      name: 'Becky',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 3,
      name: 'Candice',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 4,
      name: 'Zebra',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 5,
      name: 'Giraffe',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 6,
      name: 'test6',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 7,
      name: 'Ah Girl',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 8,
      name: 'Girl',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
    {
      key: 9,
      name: 'Boy',
      contact: '012390981237',
      guestTags: '',
      guestNotes: '',
      reservationPast: 0,
      reservationNext: 0,
      lastVisit: '',
      noShow: 0,
    },
  ];

  const dummyItems = [
    {
      label: 'Gold',
      value: 'Gold',
    },
    {
      label: 'Silver',
      value: 'Silver',
    },
    {
      label: 'Bronze',
      value: 'Bronze',
    },
  ];

  const dummyRadio = [
    {
      label: 'Male',
      value: 'Male',
    },
    {
      label: 'Female',
      value: 'Female',
    },
    {
      label: 'Other',
      value: 'Other',
    },
  ];

  const renderGuest = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('CreateGuests');
        }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            borderBottomLeftRadius: 5,
            borderBottomRightRadius: 5,
            marginLeft: 5,
            height: Dimensions.get('screen').height * 0.1,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View
            style={{
              flex: 1.5,
              flexDirection: 'row',
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            <Image
              style={{
                width: Dimensions.get('screen').height * 0.05,
                height: Dimensions.get('screen').height * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
            <Text
              style={{
                color: Colors.primaryColor,
                // fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {`${item.name}\n${item.contact}`}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  const alphabets = [
    { alphabet: 'A' },
    { alphabet: 'B' },
    { alphabet: 'C' },
    { alphabet: 'D' },
    { alphabet: 'E' },
    { alphabet: 'F' },
    { alphabet: 'G' },
    { alphabet: 'H' },
    { alphabet: 'I' },
    { alphabet: 'J' },
    { alphabet: 'K' },
    { alphabet: 'L' },
    { alphabet: 'M' },
    { alphabet: 'N' },
    { alphabet: 'O' },
    { alphabet: 'P' },
    { alphabet: 'Q' },
    { alphabet: 'R' },
    { alphabet: 'S' },
    { alphabet: 'T' },
    { alphabet: 'U' },
    { alphabet: 'V' },
    { alphabet: 'W' },
    { alphabet: 'X' },
    { alphabet: 'Y' },
    { alphabet: 'Z' },
  ];

  const renderHeader = ({ item, index }) => {
    var alphabet = item.alphabet.toLowerCase();
    return (
      <View style={{ backgroundColor: Colors.highlightColor }}>
        {/* Header */}
        <View
          style={{
            marginTop: 10,
            marginLeft: 5,
            backgroundColor: Colors.lightGrey,
            height: 60,
            flexDirection: 'row',
            borderTopLeftRadius: 10,
            borderTopRightRadius: 10,
            borderBottomWidth: StyleSheet.hairlineWidth,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View style={{ flex: 1.5, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'flex-start',
                fontFamily: 'NunitoSans-Bold',
                marginLeft: 10,
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              {`${item.alphabet}`}
            </Text>
          </View>
        </View>
        {/* Body FlatList        */}
        <FlatList
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
          renderItem={renderGuest}
          data={dummyData.filter((item) => {
            if (item.name[0].toLowerCase() === alphabet) {
              if (search !== '') {
                return item.name.toLowerCase().includes(search.toLowerCase());
              } else {
                return true;
              }
            }
          })}
          extraData={dummyData.filter((item) => {
            if (item.name[0].toLowerCase() === alphabet) {
              if (search !== '') {
                return item.name.toLowerCase().includes(search.toLowerCase());
              } else {
                return true;
              }
            }
          })}
        />
      </View>
    );
  };

  const { navigation } = props;
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const [search, setSearch] = useState('');
  // modal usestate
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [loyaltyStatus, setLoyaltyStatus] = useState(false);
  const [marketingStatus, setMarketingStatus] = useState(false);
  const [gender, setGender] = useState('');
  const [selectGender, setSelectGender] = useState(null);
  const [language, setLanguage] = useState(null);
  const [showCreateGuest, setShowCreateGuest] = useState(false);
  const [tierModal, setTierModal] = useState(false);
  const [tier, setTier] = useState('');
  const [checkSilverTier, setCheckSilverTier] = useState(false);
  const [checkGoldenTier, setCheckGoldenTier] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const selectedGuest = CommonStore.useState((s) => s.selectedGuestEdit);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const createCRMUser = async (isAutoPush = false) => {
    if (
      // !customerAvatar ||
      // !customerName ||
      !customerPhone ||
      !customerName
      // !customerEmail
      // !customerAddress
    ) {
      Alert.alert(
        'Error',
        'Please fill in all required information:\nProfile Image\nName\nContact number\nUsername\nEmail Address',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
      return;
    } else {
      ///////////////////////////////////

      if (selectedCustomerEdit === null) {
        ///////////////////////////////////
        // upload image

        var profileImageImagePath = '';
        var profileImageCommonIdLocal = uuidv4();

        if (image && imageType) {
          // promotionCommonIdLocal = uuidv4();

          profileImageImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
          );
        }

        // means new item

        var body = {
          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutletId,

          avatar: profileImageImagePath,
          isImageChanged: isImageChanged,
          email: email || '',
          gender: gender,
          name: firstName,
          number: phone,
          uniqueName: customerUsername || '',

          timeline: {},

          commonId: profileImageCommonIdLocal,
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        ApiClient.POST(API.createCRMUser, body, false).then((result) => {
          if (result && result.status === 'success') {
            createCRMUserReservation(result.crmUser);

            Alert.alert(
              'Success',
              'Customer has been created',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
      } else if (selectedCustomerEdit !== null) {
        // means existing item

        ///////////////////////////////////
        // upload image

        var profileImageImagePath = '';
        var profileImageCommonIdLocal = selectedCustomerEdit.commonId;

        if (
          image &&
          imageType &&
          selectedCustomerEdit.firebaseUid === undefined
        ) {
          // promotionCommonIdLocal = uuidv4();

          profileImageImagePath = await uploadImageToFirebaseStorage(
            {
              uri: image,
              type: imageType,
            },
            `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
          );
        }

        var body = {
          crmUserId: selectedCustomerEdit.uniqueId,
          firebaseUid: selectedCustomerEdit.firebaseUid,

          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutletId,

          avatar: profileImageImagePath,
          isImageChanged: isImageChanged,
          email: customerEmail || '',
          gender: customerGender,
          firstName: firstName,
          number: customerPhone,
          uniqueName: customerUsername || '',

          commonId: profileImageCommonIdLocal,
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        ApiClient.POST(API.updateCRMUser, body, false).then((result) => {
          if (result && result.status === 'success') {
            //createCRMUserReservation(selectedCustomerEdit);

            Alert.alert(
              'Success',
              'Customer has been updated',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setFirstName('');
                    setLastName('');
                    setPhone('');
                    setEmail('');

                    navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
      }
    }
  };

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width * 0.12 } : {}
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Guests
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: Dimensions.get('screen').height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}>
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: Dimensions.get('screen').height * 0.05,
              height: Dimensions.get('screen').height * 0.05,
              borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: Dimensions.get('screen').height * 0.035,
                height: Dimensions.get('screen').height * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: Dimensions.get('screen').width * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}

      <View>
        {/* Search bar and Create guest button */}
        <View style={switchMerchant ?
          { flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, justifyContent: 'space-between', width: '100%' }
          :
          { flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, justifyContent: 'space-between', width: Dimensions.get('screen').width * 0.87, alignSelf: 'center', marginHorizontal: 30, marginTop: 16 }
        }>
          <View
            style={{
              alignSelf: 'flex-start'
              //flex: 1,
              // borderWidth: 1,
            }}>
            <TouchableOpacity
              style={{
                marginBottom: switchMerchant ? 0 : 0,
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                marginTop: 5,
                paddingLeft: switchMerchant ? 0 : Platform.OS === 'ios' ? 0 : '2%'
              }}
              onPress={() => {
                props.navigation.navigate('Guests');
                // console.log('here', selectedGuest);
              }}>
              <Icon name="chevron-left" size={switchMerchant ? 20 : 30} color={Colors.primaryColor} />
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 14 : 17,
                color: Colors.primaryColor,
                marginBottom: Platform.OS === 'ios' ? 0 : 2,
                marginLeft: '-0.5%'
              }}>
                Back
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              alignSelf: 'flex-start'
            }}>
            <View
              style={{
                width: switchMerchant ? 200 : 250,
                height: switchMerchant ? 35 : 40,
                backgroundColor: 'white',
                borderRadius: 5,
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                //alignSelf: 'flex-end'
              }}>
              <Icon
                name="search"
                size={switchMerchant ? 13 : 18}
                color={Colors.primaryColor}
                style={{ marginLeft: 15 }}
              />
              <TextInput
                // editable={!loading}
                underlineColorAndroid={Colors.whiteColor}
                style={{
                  // width: switchMerchant ? 180 : 220,
                  // fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Regular',
                  paddingLeft: 5,
                  height: 45,
                }}
                clearButtonMode="while-editing"
                placeholder=" Search"
                placeholderTextColor={Platform.select({
                  ios: '#a9a9a9',
                })}
                onChangeText={(text) => {
                  // setSearch(text.trim());
                  setSearch(text);
                }}
                value={search}
              />
            </View>
          </View>
        </View>

        {/* Left Side */}
        {/* Guest FlatList */}
        <View style={{
          //backgroundColor: Colors.whiteColor,
          width: switchMerchant
            ? Dimensions.get('screen').width * 0.8
            : Dimensions.get('screen').width * 0.87,
          height: Dimensions.get('screen').height * 0.65,
          minHeight: Dimensions.get('screen').height * 0.01,
          marginTop: 5,
          marginHorizontal: switchMerchant ? 0 : 30,
          marginBottom: 0,
          alignSelf: 'center',
          // borderRadius: 5,
          // shadowOpacity: 0,
          // shadowColor: '#000',
          // shadowOffset: {
          //   width: 0,
          //   height: 2,
          // },
          // shadowOpacity: 0.22,
          // shadowRadius: 3.22,
          // elevation: 3,
        }}>
          <View style={{ flexDirection: 'row' }}>
            <View style={{}}>
              <FlatList
                style={styles.flatList}
                nestedScrollEnabled={true}
                showsVerticalScrollIndicator={false}
                renderItem={renderHeader}
                data={alphabets.filter((item) => {
                  var alphabet = item.alphabet.toLowerCase();
                  var ret = dummyData.filter((item) => {
                    if (item.name[0].toLowerCase() === alphabet) {
                      return true;
                    }
                  });
                  if (ret.length > 0) {
                    if (search !== '') {
                      return item.alphabet
                        .toLowerCase()
                        .includes(search[0].toLowerCase());
                    } else {
                      return true;
                    }
                  }
                })}
                extraData={alphabets.filter((item) => {
                  var alphabet = item.alphabet.toLowerCase();
                  var ret = dummyData.filter((item) => {
                    if (item.name[0].toLowerCase() === alphabet) {
                      return true;
                    }
                  });
                  if (ret.length > 0) {
                    if (search !== '') {
                      return item.alphabet
                        .toLowerCase()
                        .includes(search[0].toLowerCase());
                    } else {
                      return true;
                    }
                  }
                })}
                keyExtractor={(item, index) => String(index)}
              />
            </View>

            {/* Right Side */}
            {/* Modal for create Guests */}
            <ScrollView style={{
              width: switchMerchant
                ? Dimensions.get('screen').width * 0.8
                : Dimensions.get('screen').width * 0.87,
              height: Dimensions.get('screen').width * 0.52,
              marginTop: 10,
              marginLeft: 10,
              borderRadius: 5,
              // flexDirection: 'row',
              // paddingVertical: 20,
              // paddingHorizontal: 15,
              //marginTop: 10,
              backgroundColor: 'white',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>
              <View
                style={{
                  flexDirection: 'row',
                  //backgroundColor: 'white',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: 20
                }}>
                <View style={{ flex: 0.6 }}>
                  <Image
                    style={{
                      width: Dimensions.get('screen').height * 0.2,
                      height: Dimensions.get('screen').height * 0.2,
                      alignSelf: 'flex-start',
                      borderRadius: 50,
                    }}
                    // {...selectedGuest == null ? (
                    //   <AsyncImage
                    //     style={{
                    //       width: switchMerchant ? 30 : 40,
                    //       height: switchMerchant ? 30 : 40,
                    //       alignSelf: 'center',
                    //       borderRadius: 100,
                    //       marginHorizontal: 5,
                    //     }}
                    //     source={{
                    //       uri: selectedGuest.avatar,
                    //     }}
                    //     item={selectedGuest}
                    //     hideLoading={true}
                    //   />
                    // ) : (
                    //   <Image
                    //     style={{
                    //       width: switchMerchant ? 30 : 40,
                    //       height: switchMerchant ? 30 : 40,
                    //       alignSelf: 'center',
                    //       borderRadius: 100,
                    //       marginHorizontal: 5,
                    //     }}
                    //     source={require('../assets/image/profile-pic.jpg')}
                    //     hideLoading={true}
                    //   />
                    // )}
                    source={require('../assets/image/profile-pic.jpg')}

                  />
                </View>
                <View
                  style={{
                    flex: 1.4,
                    //borderBottomWidth: 1,
                    height: 70,
                    borderColor: '#D3D3D3',
                  }}>

                </View>
              </View>

              <View style={{ flexDirection: 'row', marginLeft: 20 }}>
                <View style={{ flexDirection: 'column' }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 10,
                        marginTop: 10,
                        width: 90,
                      }}>
                      First Name
                    </Text><Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        //marginLeft: 5,
                        marginTop: 10,
                        width: 10,
                        color: 'red'
                      }}>
                      *
                    </Text>
                    <TextInput
                      //editable={selectedCustomerEdit === null}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 100 : Platform.OS == 'ios' ? 150 : 200,
                        height: switchMerchant ? 30 : 35,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        marginLeft: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholder="Add First Name"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setFirstName(text);
                      }}
                      //defaultValue={customerName}
                      value={firstName}
                    />
                    {/* <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      marginLeft: 10,
                      //   width: switchMerchant ? 240 : 370,
                      //   height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      borderColor: '#E5E5E5',
                      fontSize: switchMerchant ? 10 : 16,
                    }}
                    placeholderStyle={{padding: 5}}
                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                    placeholder="Add First Name"
                    onChangeText={(text) => {
                      setFirstName(text);
                    }}
                    value={firstName}
                  /> */}
                  </View>
                  <View style={{ flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 10,
                        marginTop: 10,
                        width: 100,
                      }}>
                      Phone (+60)
                    </Text>
                    <View style={{}}>
                      <TextInput
                        //editable={selectedCustomerEdit === null}
                        style={{
                          flexDirection: 'row',
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 100 : Platform.OS == 'ios' ? 150 : 200,
                          height: switchMerchant ? 30 : 35,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          marginLeft: 5,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="Phone"
                        keyboardType="numeric"
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {
                          setPhone(text);
                        }}
                        value={phone}
                      />
                      {/* <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        //marginLeft: 10,
                        //   width: switchMerchant ? 240 : 370,
                        //   height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        borderColor: '#E5E5E5',
                        fontSize: switchMerchant ? 10 : 16,
                      }}
                      placeholderStyle={{padding: 5}}
                      placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                      placeholder="Phone"
                      keyboardType="numeric"
                      onChangeText={(text) => {
                        setPhone(text);
                      }}
                      value={phone}
                    /> */}
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', }}>
                    <Text
                      style={{
                        width: 100,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 10,
                        marginTop: 10,
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Language
                    </Text>
                    <DropDownPicker
                      containerStyle={{
                        height: switchMerchant ? 30 : 35,
                        zIndex: 2,
                        marginLeft: 5,
                        marginTop: 10,
                      }}
                      arrowColor={'black'}
                      arrowSize={switchMerchant ? 10 : 20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.fieldtTxtColor,
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      style={{
                        width: switchMerchant ? 70 : Platform.OS == 'ios' ? 150 : 200,
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      value={language}
                      items={[
                        { label: 'English', value: 'English' },
                        { label: 'Malay', value: 'Malay' },
                        { label: 'Mandarin', value: 'Mandarin' },
                      ]}
                      onChangeItem={(item) => {
                        setLanguage(item);
                      }}
                      placeholder={'Select '}

                      dropDownStyle={{
                        height: 100,
                        width: 200,
                        //marginLeft: 10,
                        backgroundColor: Colors.fieldtBgColor,
                      }}

                      itemStyle={{
                        justifyContent: 'flex-start',
                        marginLeft: 10,
                        zIndex: 2,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    />
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 30 }}>
                    <Text
                      style={[
                        styles.textSize,
                        {
                          width: 200,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: 10,
                          fontSize: switchMerchant ? 10 : 16,
                          marginTop: 10
                        },
                      ]}>
                      Add to Loyalty program
                    </Text>
                    <View style={{ flexDirection: 'row', marginTop: 10 }}>

                      <Switch
                        width={42}
                        style={{
                          marginLeft: 10,
                          marginTop: 5,
                        }}
                        value={loyaltyStatus}
                        onSyncPress={(statusTemp) =>
                          // setState({ status: status })
                          setLoyaltyStatus(statusTemp)
                        }
                        circleColorActive={Colors.whiteColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive={Colors.tabCyan}
                      />
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', marginTop: 10 }}>
                    <Text
                      style={[
                        styles.textSize,
                        {
                          width: 200,
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 16,
                          marginLeft: 10,
                        },
                      ]}>
                      Receiving Marketing
                    </Text>
                    <Switch
                      width={42}
                      style={{
                        marginLeft: 10,
                        marginTop: 5,
                      }}
                      value={marketingStatus}
                      onSyncPress={(statusTemp) =>
                        // setState({ status: status })
                        setMarketingStatus(statusTemp)
                      }
                      circleColorActive={Colors.whiteColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive={Colors.tabCyan}
                    />
                  </View>
                </View>

                <View style={{ flexDirection: 'column' }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 50,
                        marginTop: 10,
                        width: 100,
                      }}>
                      Last Name
                    </Text>
                    <TextInput
                      //editable={selectedCustomerEdit === null}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 100 : Platform.OS == 'ios' ? 150 : 200,
                        height: switchMerchant ? 30 : 35,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        marginLeft: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholder="Add Last Name"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setLastName(text);
                      }}
                      //defaultValue={customerName}
                      value={lastName}
                    />
                    {/* <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      marginLeft: 10,
                      //   width: switchMerchant ? 240 : 370,
                      //   height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      borderColor: '#E5E5E5',
                      fontSize: switchMerchant ? 10 : 16,
                    }}
                    placeholderStyle={{padding: 5}}
                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                    placeholder="Add Last Name"
                    onChangeText={(text) => {
                      setLastName(text);
                    }}
                    value={lastName}
                  /> */}
                  </View>
                  <View style={{ flexDirection: 'row' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 50,
                        marginTop: 10,
                        width: 100,
                      }}>
                      Email
                    </Text>
                    <TextInput
                      //editable={selectedCustomerEdit === null}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 100 : Platform.OS == 'ios' ? 150 : 200,
                        height: switchMerchant ? 30 : 35,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        marginLeft: 5,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholder="Add Email"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setEmail(text);
                      }}
                      value={email}
                    />
                    {/* <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      marginLeft: 10,
                      //   width: switchMerchant ? 240 : 370,
                      //   height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      borderColor: '#E5E5E5',
                      fontSize: switchMerchant ? 10 : 16,
                    }}
                    placeholderStyle={{padding: 5}}
                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                    placeholder="Add Email"
                    onChangeText={(text) => {
                      setEmail(text);
                    }}
                    value={email}
                  /> */}
                  </View>
                  <View style={{
                    flexDirection: 'row',
                    marginTop: 10
                  }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: 50,
                        marginTop: 10,
                        width: 95,
                      }}>
                      Gender
                    </Text>
                    <View style={{
                      marginTop: 12
                    }}>
                      <RadioForm formHorizontal={true} animation={true}>
                        {/* To create radio buttons, loop through your array of options */}
                        {dummyRadio.map((obj, i) => (
                          <RadioButton labelHorizontal={true} key={i}>
                            <RadioButtonLabel
                              obj={obj}
                              index={i}
                              labelHorizontal={true}
                              onPress={() => {
                                setGender(obj.value);
                                setSelectGender(i);
                              }}
                              labelStyle={{ fontSize: 14, color: 'gray' }}
                              labelWrapStyle={{ marginRight: Platform.OS == 'ios' ? -3 : 0 }}
                            />
                            {/*  You can set RadioButtonLabel before RadioButtonInput */}
                            <RadioButtonInput
                              obj={obj}
                              index={i}
                              isSelected={selectGender === i}
                              onPress={() => {
                                setGender(obj.value);
                                setSelectGender(i);
                                // console.log('i: ', selectGender);
                                // console.log('gender: ', gender);
                              }}
                              borderWidth={1}
                              buttonInnerColor={'rgb(30, 134, 151)'}
                              buttonOuterColor={
                                selectGender === i ? '#2196f3' : '#000'
                              }
                              buttonSize={15}
                              buttonOuterSize={20}
                              buttonStyle={{}}
                              buttonWrapStyle={{ marginLeft: Platform.OS == 'ios' ? 5 : 10 }}
                              style={{ marginTop: 20 }}
                            />
                          </RadioButton>
                        ))}
                      </RadioForm>
                    </View>
                  </View>
                </View>
              </View>
              <View>

                <View style={{ flexDirection: 'row', marginTop: 30 }}>
                  <Text
                    style={{
                      width: 100,
                      fontFamily: 'NunitoSans-Bold',
                      marginLeft: 30,
                      marginBottom: 5,
                      fontSize: switchMerchant ? 10 : 16,
                    }}>
                    Tier
                  </Text>
                  <TouchableOpacity
                    style={{
                      borderWidth: 1,
                      borderColor: '#D3D3D3',
                      borderRadius: 5,
                      justifyContent: 'center',
                      marginLeft: 10,
                      height: 35,
                      width: switchMerchant ? 100 : 200,
                    }}
                    onPress={() => {
                      setTierModal(true);
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                      <View style={{ flex: 1 }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            marginLeft: 10,
                          }}>
                          {tier}
                        </Text>
                      </View>
                      <View style={{ flex: 1 }}>
                        <MaterialIcons
                          name="keyboard-arrow-down"
                          size={25}
                          color="#D3D3D3"
                          style={{ alignSelf: 'flex-end', marginRight: 5 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                  <View style={{ height: 100 }} />
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </View>
      {/* Modal Tier */}
      <ModalView
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={tierModal}
        transparent={true}>
        <View style={[styles.modalContainer]}>
          <View
            style={[
              styles.modalView,
              { height: Dimensions.get('screen').height * 0.4 },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
              }}>
              <View style={{ flex: 0.5 }}>
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    setTierModal(false);
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 16 : 18,
                      color: 'gray',
                    }}>
                    Reset
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={{ flex: 1.5, marginBottom: 10 }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                  }}>
                  Loyalty Tiers
                </Text>
              </View>
              <View style={{ flex: 0.5 }}>
                <TouchableOpacity
                  onPress={() => {
                    setTierModal(false);
                  }}>
                  <AIcon
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                    style={{ alignSelf: 'flex-end', marginRight: 20 }}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                flex: 1,
                marginTop: 10,
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
                justifyContent: 'center',
              }}>
              <View style={{ flexDirection: 'row' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginLeft: 10 }}>Silver Member</Text>
                </View>
                <View style={{ flex: 0.8 }}>
                  <MaterialCommunityIcons
                    name="lock"
                    size={20}
                    style={{ alignSelf: 'flex-end', paddingTop: 5 }}
                  />
                </View>
                <View style={{ flex: 0.2 }}>
                  <CheckBox
                    width={42}
                    style={{
                      marginRight: 10,
                      alignSelf: 'flex-end',
                      borderRadius: 15,
                    }}
                    value={checkSilverTier}
                    onValueChange={() =>
                    // setState({ status: status })
                    {
                      setTier('Silver Member');
                      setCheckGoldenTier(false);
                      setCheckSilverTier(true);
                    }
                    }
                  />
                </View>
              </View>
            </View>
            <View
              style={{
                flex: 1,
                marginTop: 10,
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
                justifyContent: 'center',
              }}>
              <View style={{ flexDirection: 'row' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginLeft: 10 }}>Golden Member</Text>
                </View>
                <View style={{ flex: 1 }}>
                  <CheckBox
                    width={42}
                    style={{
                      marginRight: 10,
                      alignSelf: 'flex-end',
                      borderRadius: 15,
                    }}
                    value={checkGoldenTier}
                    onValueChange={() => {
                      // setState({ status: status })
                      setCheckSilverTier(false);
                      setCheckGoldenTier(true);
                      setTier('Golden Member');
                    }}
                  />
                </View>
              </View>
            </View>
            <View style={{ flex: 1 }}></View>
            <View style={{ flex: 1 }}></View>
            <View style={{ flex: 1 }}></View>
            <View style={{ flex: 1 }}></View>
          </View>
        </View>
      </ModalView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  confirmBox: {
    width: Dimensions.get('screen').width * 0.4,
    height: Dimensions.get('screen').height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  flatList: {
    height: 100,
    //marginLeft: 10,
    width: Dimensions.get('screen').width * 0.25,
    // paddingVertical: 20,
    // paddingHorizontal: 15,
    //marginTop: 10,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    // elevation: 3,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },

  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 350,
    height: 50,
    //flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  topBar: {
    flexDirection: 'row',
    height: Dimensions.get('screen').height * 0.05,
    width: Dimensions.get('screen').width * 0.91,
    backgroundColor: Colors.lightGrey,
    justifyContent: 'flex-start',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailsContainer: {
    height: Dimensions.get('screen').width * 0.52,
    margin: 10,
    // flexDirection: 'row',
    // paddingVertical: 20,
    // paddingHorizontal: 15,
    //marginTop: 10,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').height * 0.7,
    width: Dimensions.get('screen').width * 0.4,
    backgroundColor: Colors.whiteColor,
    //borderRadius: Dimensions.get('screen').width * 0.03,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.02,
    paddingHorizontal: Dimensions.get('screen').width * 0,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.04,
    top: Dimensions.get('screen').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 24,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: 130,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  ManageFilterBox: {
    //width: Dimensions.get('screen').width * 0.27,
    //height: Platform.OS === 'ios' ?Dimensions.get('screen').height * 0.23: Dimensions.get('screen').height * 0.24,
    //width: Platform.OS === 'ios' ? Dimensions.get('screen').width * 0.4 : Dimensions.get('screen').width * 0.33,
    height:
      Platform.OS === 'ios' ? 180 : Dimensions.get('screen').height * 0.24,
    width: Platform.OS === 'ios' ? 400 : Dimensions.get('screen').width * 0.33,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    position: 'absolute',
    marginTop: Platform.OS === 'ios' ? '13%' : '13%',
    marginLeft: Platform.OS === 'ios' ? '12%' : '12%',
    //left: Platform.OS === 'ios' ? '38%' : '0%',
    //top: Platform.OS === 'ios' ? '46%' : '0%',
    shadowColor: '#000',
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.32,
    shadowRadius: 3.22,
    elevation: 10,
    zIndex: 1000,
    borderRadius: 10,
    //borderTopLeftRadius: 0,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  tierDropdown: {
    flex: 1,
    borderRadius: 10,
  },
});

export default CreateGuests;
