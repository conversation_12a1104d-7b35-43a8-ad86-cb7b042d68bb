diff --git a/node_modules/react-native/Libraries/Components/TextInput/TextInput.js b/node_modules/react-native/Libraries/Components/TextInput/TextInput.js
index a377366..ff6442e 100644
--- a/node_modules/react-native/Libraries/Components/TextInput/TextInput.js
+++ b/node_modules/react-native/Libraries/Components/TextInput/TextInput.js
@@ -8,14 +8,14 @@
  * @format
  */
 
-import type {HostComponent} from '../../Renderer/shims/ReactNativeTypes';
+import type { HostComponent } from '../../Renderer/shims/ReactNativeTypes';
 import type {
   PressEvent,
   ScrollEvent,
   SyntheticEvent,
 } from '../../Types/CoreEventTypes';
-import type {ViewProps} from '../View/ViewPropTypes';
-import type {TextInputType} from './TextInput.flow';
+import type { ViewProps } from '../View/ViewPropTypes';
+import type { TextInputType } from './TextInput.flow';
 
 import usePressability from '../../Pressability/usePressability';
 import flattenStyle from '../../StyleSheet/flattenStyle';
@@ -32,14 +32,14 @@ import TextInputState from './TextInputState';
 import invariant from 'invariant';
 import nullthrows from 'nullthrows';
 import * as React from 'react';
-import {useCallback, useLayoutEffect, useRef, useState} from 'react';
+import { useCallback, useLayoutEffect, useRef, useState } from 'react';
 
-type ReactRefSetter<T> = {current: null | T, ...} | ((ref: null | T) => mixed);
+type ReactRefSetter<T> = { current: null | T, ... } | ((ref: null | T) => mixed);
 type TextInputInstance = React.ElementRef<HostComponent<mixed>> & {
   +clear: () => void,
   +isFocused: () => boolean,
-  +getNativeRef: () => ?React.ElementRef<HostComponent<mixed>>,
-  +setSelection: (start: number, end: number) => void,
+    +getNativeRef: () => ? React.ElementRef < HostComponent < mixed >>,
+      +setSelection: (start: number, end: number) => void,
 };
 
 let AndroidTextInput;
@@ -67,20 +67,20 @@ if (Platform.OS === 'android') {
 export type ChangeEvent = SyntheticEvent<
   $ReadOnly<{|
     eventCount: number,
-    target: number,
-    text: string,
+      target: number,
+        text: string,
   |}>,
 >;
 
 export type TextInputEvent = SyntheticEvent<
   $ReadOnly<{|
     eventCount: number,
-    previousText: string,
-    range: $ReadOnly<{|
-      start: number,
-      end: number,
+      previousText: string,
+        range: $ReadOnly < {|
+          start: number,
+            end: number,
     |}>,
-    target: number,
+  target: number,
     text: string,
   |}>,
 >;
@@ -88,9 +88,9 @@ export type TextInputEvent = SyntheticEvent<
 export type ContentSizeChangeEvent = SyntheticEvent<
   $ReadOnly<{|
     target: number,
-    contentSize: $ReadOnly<{|
-      width: number,
-      height: number,
+      contentSize: $ReadOnly < {|
+        width: number,
+          height: number,
     |}>,
   |}>,
 >;
@@ -106,29 +106,29 @@ export type FocusEvent = TargetEvent;
 
 type Selection = $ReadOnly<{|
   start: number,
-  end: number,
+    end: number,
 |}>;
 
 export type SelectionChangeEvent = SyntheticEvent<
   $ReadOnly<{|
     selection: Selection,
-    target: number,
+      target: number,
   |}>,
 >;
 
 export type KeyPressEvent = SyntheticEvent<
   $ReadOnly<{|
     key: string,
-    target?: ?number,
-    eventCount?: ?number,
+      target ?: ? number,
+      eventCount ?: ? number,
   |}>,
 >;
 
 export type EditingEvent = SyntheticEvent<
   $ReadOnly<{|
     eventCount: number,
-    text: string,
-    target: number,
+      text: string,
+        target: number,
   |}>,
 >;
 
@@ -260,111 +260,111 @@ type IOSProps = $ReadOnly<{|
    */
   clearButtonMode?: ?('never' | 'while-editing' | 'unless-editing' | 'always'),
 
-  /**
-   * If `true`, clears the text field automatically when editing begins.
-   * @platform ios
-   */
-  clearTextOnFocus?: ?boolean,
-
-  /**
-   * Determines the types of data converted to clickable URLs in the text input.
-   * Only valid if `multiline={true}` and `editable={false}`.
-   * By default no data types are detected.
-   *
-   * You can provide one type or an array of many types.
-   *
-   * Possible values for `dataDetectorTypes` are:
-   *
-   * - `'phoneNumber'`
-   * - `'link'`
-   * - `'address'`
-   * - `'calendarEvent'`
-   * - `'none'`
-   * - `'all'`
-   *
-   * @platform ios
-   */
-  dataDetectorTypes?:
-    | ?DataDetectorTypesType
-    | $ReadOnlyArray<DataDetectorTypesType>,
-
-  /**
-   * If `true`, the keyboard disables the return key when there is no text and
-   * automatically enables it when there is text. The default value is `false`.
-   * @platform ios
-   */
-  enablesReturnKeyAutomatically?: ?boolean,
-
-  /**
-   * An optional identifier which links a custom InputAccessoryView to
-   * this text input. The InputAccessoryView is rendered above the
-   * keyboard when this text input is focused.
-   * @platform ios
-   */
-  inputAccessoryViewID?: ?string,
-
-  /**
-   * Determines the color of the keyboard.
-   * @platform ios
-   */
-  keyboardAppearance?: ?('default' | 'light' | 'dark'),
-
-  /**
-   * Provide rules for your password.
-   * For example, say you want to require a password with at least eight characters consisting of a mix of uppercase and lowercase letters, at least one number, and at most two consecutive characters.
-   * "required: upper; required: lower; required: digit; max-consecutive: 2; minlength: 8;"
-   * @platform ios
-   */
-  passwordRules?: ?PasswordRules,
-
-  /*
-   * If `true`, allows TextInput to pass touch events to the parent component.
-   * This allows components to be swipeable from the TextInput on iOS,
-   * as is the case on Android by default.
-   * If `false`, TextInput always asks to handle the input (except when disabled).
-   * @platform ios
-   */
-  rejectResponderTermination?: ?boolean,
-
-  /**
-   * If `false`, scrolling of the text view will be disabled.
-   * The default value is `true`. Does only work with 'multiline={true}'.
-   * @platform ios
-   */
-  scrollEnabled?: ?boolean,
-
-  /**
-   * If `false`, disables spell-check style (i.e. red underlines).
-   * The default value is inherited from `autoCorrect`.
-   * @platform ios
-   */
-  spellCheck?: ?boolean,
-
-  /**
-   * Give the keyboard and the system information about the
-   * expected semantic meaning for the content that users enter.
-   * `autoComplete` property accomplishes same behavior and is recommended as its supported by both platforms.
-   * Avoid using both `autoComplete` and `textContentType`, you can use `Platform.select` for differing platform behaviors.
-   * For backwards compatibility, when both set, `textContentType` takes precedence on iOS.
-   * @platform ios
-   */
-  textContentType?: ?TextContentType,
-
-  /**
-   * Set line break strategy on iOS.
-   * @platform ios
-   */
-  lineBreakStrategyIOS?: ?('none' | 'standard' | 'hangul-word' | 'push-out'),
-
-  /**
-   * If `false`, the iOS system will not insert an extra space after a paste operation
-   * neither delete one or two spaces after a cut or delete operation.
-   *
-   * The default value is `true`.
-   *
-   * @platform ios
-   */
-  smartInsertDelete?: ?boolean,
+    /**
+     * If `true`, clears the text field automatically when editing begins.
+     * @platform ios
+     */
+    clearTextOnFocus ?: ? boolean,
+
+    /**
+     * Determines the types of data converted to clickable URLs in the text input.
+     * Only valid if `multiline={true}` and `editable={false}`.
+     * By default no data types are detected.
+     *
+     * You can provide one type or an array of many types.
+     *
+     * Possible values for `dataDetectorTypes` are:
+     *
+     * - `'phoneNumber'`
+     * - `'link'`
+     * - `'address'`
+     * - `'calendarEvent'`
+     * - `'none'`
+     * - `'all'`
+     *
+     * @platform ios
+     */
+    dataDetectorTypes ?:
+    | ? DataDetectorTypesType
+        | $ReadOnlyArray < DataDetectorTypesType >,
+
+    /**
+     * If `true`, the keyboard disables the return key when there is no text and
+     * automatically enables it when there is text. The default value is `false`.
+     * @platform ios
+     */
+    enablesReturnKeyAutomatically ?: ? boolean,
+
+    /**
+     * An optional identifier which links a custom InputAccessoryView to
+     * this text input. The InputAccessoryView is rendered above the
+     * keyboard when this text input is focused.
+     * @platform ios
+     */
+    inputAccessoryViewID ?: ? string,
+
+    /**
+     * Determines the color of the keyboard.
+     * @platform ios
+     */
+    keyboardAppearance ?: ? ('default' | 'light' | 'dark'),
+
+    /**
+     * Provide rules for your password.
+     * For example, say you want to require a password with at least eight characters consisting of a mix of uppercase and lowercase letters, at least one number, and at most two consecutive characters.
+     * "required: upper; required: lower; required: digit; max-consecutive: 2; minlength: 8;"
+     * @platform ios
+     */
+    passwordRules ?: ? PasswordRules,
+
+    /*
+     * If `true`, allows TextInput to pass touch events to the parent component.
+     * This allows components to be swipeable from the TextInput on iOS,
+     * as is the case on Android by default.
+     * If `false`, TextInput always asks to handle the input (except when disabled).
+     * @platform ios
+     */
+    rejectResponderTermination ?: ? boolean,
+
+    /**
+     * If `false`, scrolling of the text view will be disabled.
+     * The default value is `true`. Does only work with 'multiline={true}'.
+     * @platform ios
+     */
+    scrollEnabled ?: ? boolean,
+
+    /**
+     * If `false`, disables spell-check style (i.e. red underlines).
+     * The default value is inherited from `autoCorrect`.
+     * @platform ios
+     */
+    spellCheck ?: ? boolean,
+
+    /**
+     * Give the keyboard and the system information about the
+     * expected semantic meaning for the content that users enter.
+     * `autoComplete` property accomplishes same behavior and is recommended as its supported by both platforms.
+     * Avoid using both `autoComplete` and `textContentType`, you can use `Platform.select` for differing platform behaviors.
+     * For backwards compatibility, when both set, `textContentType` takes precedence on iOS.
+     * @platform ios
+     */
+    textContentType ?: ? TextContentType,
+
+    /**
+     * Set line break strategy on iOS.
+     * @platform ios
+     */
+    lineBreakStrategyIOS ?: ? ('none' | 'standard' | 'hangul-word' | 'push-out'),
+
+    /**
+     * If `false`, the iOS system will not insert an extra space after a paste operation
+     * neither delete one or two spaces after a cut or delete operation.
+     *
+     * The default value is `true`.
+     *
+     * @platform ios
+     */
+    smartInsertDelete ?: ? boolean,
 |}>;
 
 type AndroidProps = $ReadOnly<{| 
@@ -376,85 +376,85 @@ type AndroidProps = $ReadOnly<{|
    */
   cursorColor?: ?ColorValue,
 
-  /**
-   * When `false`, if there is a small amount of space available around a text input
-   * (e.g. landscape orientation on a phone), the OS may choose to have the user edit
-   * the text inside of a full screen text input mode. When `true`, this feature is
-   * disabled and users will always edit the text directly inside of the text input.
-   * Defaults to `false`.
-   * @platform android
-   */
-  disableFullscreenUI?: ?boolean,
-
-  importantForAutofill?: ?(
+    /**
+     * When `false`, if there is a small amount of space available around a text input
+     * (e.g. landscape orientation on a phone), the OS may choose to have the user edit
+     * the text inside of a full screen text input mode. When `true`, this feature is
+     * disabled and users will always edit the text directly inside of the text input.
+     * Defaults to `false`.
+     * @platform android
+     */
+    disableFullscreenUI ?: ? boolean,
+
+    importantForAutofill ?: ? (
     | 'auto'
-    | 'no'
-    | 'noExcludeDescendants'
-    | 'yes'
-    | 'yesExcludeDescendants'
-  ),
-
-  /**
-   * If defined, the provided image resource will be rendered on the left.
-   * The image resource must be inside `/android/app/src/main/res/drawable` and referenced
-   * like
-   * ```
-   * <TextInput
-   *  inlineImageLeft='search_icon'
-   * />
-   * ```
-   * @platform android
-   */
-  inlineImageLeft?: ?string,
-
-  /**
-   * Padding between the inline image, if any, and the text input itself.
-   * @platform android
-   */
-  inlineImagePadding?: ?number,
-
-  /**
-   * Sets the number of lines for a `TextInput`. Use it with multiline set to
-   * `true` to be able to fill the lines.
-   * @platform android
-   */
-  numberOfLines?: ?number,
-
-  /**
-   * Sets the return key to the label. Use it instead of `returnKeyType`.
-   * @platform android
-   */
-  returnKeyLabel?: ?string,
-
-  /**
-   * Sets the number of rows for a `TextInput`. Use it with multiline set to
-   * `true` to be able to fill the lines.
-   * @platform android
-   */
-  rows?: ?number,
-
-  /**
-   * When `false`, it will prevent the soft keyboard from showing when the field is focused.
-   * Defaults to `true`.
-   */
-  showSoftInputOnFocus?: ?boolean,
-
-  /**
-   * Set text break strategy on Android API Level 23+, possible values are `simple`, `highQuality`, `balanced`
-   * The default value is `simple`.
-   * @platform android
-   */
-  textBreakStrategy?: ?('simple' | 'highQuality' | 'balanced'),
-
-  /**
-   * The color of the `TextInput` underline.
-   * @platform android
-   */
-  underlineColorAndroid?: ?ColorValue,
+      | 'no'
+      | 'noExcludeDescendants'
+      | 'yes'
+      | 'yesExcludeDescendants'
+    ),
+
+    /**
+     * If defined, the provided image resource will be rendered on the left.
+     * The image resource must be inside `/android/app/src/main/res/drawable` and referenced
+     * like
+     * ```
+     * <TextInput
+     *  inlineImageLeft='search_icon'
+     * />
+     * ```
+     * @platform android
+     */
+    inlineImageLeft ?: ? string,
+
+    /**
+     * Padding between the inline image, if any, and the text input itself.
+     * @platform android
+     */
+    inlineImagePadding ?: ? number,
+
+    /**
+     * Sets the number of lines for a `TextInput`. Use it with multiline set to
+     * `true` to be able to fill the lines.
+     * @platform android
+     */
+    numberOfLines ?: ? number,
+
+    /**
+     * Sets the return key to the label. Use it instead of `returnKeyType`.
+     * @platform android
+     */
+    returnKeyLabel ?: ? string,
+
+    /**
+     * Sets the number of rows for a `TextInput`. Use it with multiline set to
+     * `true` to be able to fill the lines.
+     * @platform android
+     */
+    rows ?: ? number,
+
+    /**
+     * When `false`, it will prevent the soft keyboard from showing when the field is focused.
+     * Defaults to `true`.
+     */
+    showSoftInputOnFocus ?: ? boolean,
+
+    /**
+     * Set text break strategy on Android API Level 23+, possible values are `simple`, `highQuality`, `balanced`
+     * The default value is `simple`.
+     * @platform android
+     */
+    textBreakStrategy ?: ? ('simple' | 'highQuality' | 'balanced'),
+
+    /**
+     * The color of the `TextInput` underline.
+     * @platform android
+     */
+    underlineColorAndroid ?: ? ColorValue,
 |}>;
 
 export type Props = $ReadOnly<{|
-  ...$Diff<ViewProps, $ReadOnly<{|style: ?ViewStyleProp|}>>,
+  ...$Diff < ViewProps, $ReadOnly < {| style: ?ViewStyleProp |}>>,
   ...IOSProps,
   ...AndroidProps,
 
@@ -466,7 +466,7 @@ export type Props = $ReadOnly<{|
    * - `sentences`: first letter of each sentence (*default*).
    * - `none`: don't auto capitalize anything.
    */
-  autoCapitalize?: ?AutoCapitalize,
+  autoCapitalize ?: ? AutoCapitalize,
 
   /**
    * Specifies autocomplete hints for the system, so it can provide autofill.
@@ -539,7 +539,7 @@ export type Props = $ReadOnly<{|
    * - `tel-device`
    * - `username-new`
    */
-  autoComplete?: ?(
+  autoComplete ?: ? (
     | 'additional-name'
     | 'address-line1'
     | 'address-line2'
@@ -602,19 +602,19 @@ export type Props = $ReadOnly<{|
   /**
    * If `false`, disables auto-correct. The default value is `true`.
    */
-  autoCorrect?: ?boolean,
+  autoCorrect ?: ? boolean,
 
   /**
    * If `true`, focuses the input on `componentDidMount`.
    * The default value is `false`.
    */
-  autoFocus?: ?boolean,
+  autoFocus ?: ? boolean,
 
   /**
    * Specifies whether fonts should scale to respect Text Size accessibility settings. The
    * default is `true`.
    */
-  allowFontScaling?: ?boolean,
+  allowFontScaling ?: ? boolean,
 
   /**
    * If `true`, caret is hidden. The default value is `false`.
@@ -625,26 +625,26 @@ export type Props = $ReadOnly<{|
    * will cause cursor to be disabled as a side-effect.
    *
    */
-  caretHidden?: ?boolean,
+  caretHidden ?: ? boolean,
 
   /*
    * If `true`, contextMenuHidden is hidden. The default value is `false`.
    */
-  contextMenuHidden?: ?boolean,
+  contextMenuHidden ?: ? boolean,
 
   /**
    * Provides an initial value that will change when the user starts typing.
    * Useful for simple use-cases where you do not want to deal with listening
    * to events and updating the value prop to keep the controlled state in sync.
    */
-  defaultValue?: ?Stringish,
+  defaultValue ?: ? Stringish,
 
   /**
    * If `false`, text is not editable. The default value is `true`.
    */
-  editable?: ?boolean,
+  editable ?: ? boolean,
 
-  forwardedRef?: ?ReactRefSetter<TextInputInstance>,
+  forwardedRef ?: ? ReactRefSetter < TextInputInstance >,
 
   /**
    * `enterKeyHint` defines what action label (or icon) to present for the enter key on virtual keyboards.
@@ -659,7 +659,7 @@ export type Props = $ReadOnly<{|
    * - `search`
    * - `send`
    */
-  enterKeyHint?: ?enterKeyHintType,
+  enterKeyHint ?: ? enterKeyHintType,
 
   /**
    * `inputMode` works like the `inputmode` attribute in HTML, it determines which
@@ -676,7 +676,7 @@ export type Props = $ReadOnly<{|
    * - `email`
    * - `url`
    */
-  inputMode?: ?InputMode,
+  inputMode ?: ? InputMode,
 
   /**
    * Determines which keyboard to open, e.g.`numeric`.
@@ -708,7 +708,7 @@ export type Props = $ReadOnly<{|
    * - `visible-password`
    *
    */
-  keyboardType?: ?KeyboardType,
+  keyboardType ?: ? KeyboardType,
 
   /**
    * Specifies largest possible scale a font can reach when `allowFontScaling` is enabled.
@@ -717,29 +717,29 @@ export type Props = $ReadOnly<{|
    * `0`: no max, ignore parent/global default
    * `>= 1`: sets the maxFontSizeMultiplier of this node to this value
    */
-  maxFontSizeMultiplier?: ?number,
+  maxFontSizeMultiplier ?: ? number,
 
   /**
    * Limits the maximum number of characters that can be entered. Use this
    * instead of implementing the logic in JS to avoid flicker.
    */
-  maxLength?: ?number,
+  maxLength ?: ? number,
 
   /**
    * If `true`, the text input can be multiple lines.
    * The default value is `false`.
    */
-  multiline?: ?boolean,
+  multiline ?: ? boolean,
 
   /**
    * Callback that is called when the text input is blurred.
    */
-  onBlur?: ?(e: BlurEvent) => mixed,
+  onBlur ?: ? (e: BlurEvent) => mixed,
 
   /**
    * Callback that is called when the text input's text changes.
    */
-  onChange?: ?(e: ChangeEvent) => mixed,
+  onChange ?: ? (e: ChangeEvent) => mixed,
 
   /**
    * DANGER: this API is not stable and will change in the future.
@@ -749,13 +749,13 @@ export type Props = $ReadOnly<{|
    *
    * @platform ios
    */
-  unstable_onChangeSync?: ?(e: ChangeEvent) => mixed,
+  unstable_onChangeSync ?: ? (e: ChangeEvent) => mixed,
 
   /**
    * Callback that is called when the text input's text changes.
    * Changed text is passed as an argument to the callback handler.
    */
-  onChangeText?: ?(text: string) => mixed,
+  onChangeText ?: ? (text: string) => mixed,
 
   /**
    * DANGER: this API is not stable and will change in the future.
@@ -766,7 +766,7 @@ export type Props = $ReadOnly<{|
    *
    * @platform ios
    */
-  unstable_onChangeTextSync?: ?(text: string) => mixed,
+  unstable_onChangeTextSync ?: ? (text: string) => mixed,
 
   /**
    * Callback that is called when the text input's content size changes.
@@ -775,17 +775,17 @@ export type Props = $ReadOnly<{|
    *
    * Only called for multiline text inputs.
    */
-  onContentSizeChange?: ?(e: ContentSizeChangeEvent) => mixed,
+  onContentSizeChange ?: ? (e: ContentSizeChangeEvent) => mixed,
 
   /**
    * Callback that is called when text input ends.
    */
-  onEndEditing?: ?(e: EditingEvent) => mixed,
+  onEndEditing ?: ? (e: EditingEvent) => mixed,
 
   /**
    * Callback that is called when the text input is focused.
    */
-  onFocus?: ?(e: FocusEvent) => mixed,
+  onFocus ?: ? (e: FocusEvent) => mixed,
 
   /**
    * Callback that is called when a key is pressed.
@@ -794,7 +794,7 @@ export type Props = $ReadOnly<{|
    * the typed-in character otherwise including `' '` for space.
    * Fires before `onChange` callbacks.
    */
-  onKeyPress?: ?(e: KeyPressEvent) => mixed,
+  onKeyPress ?: ? (e: KeyPressEvent) => mixed,
 
   /**
    * DANGER: this API is not stable and will change in the future.
@@ -809,59 +809,59 @@ export type Props = $ReadOnly<{|
    *
    * @platform ios
    */
-  unstable_onKeyPressSync?: ?(e: KeyPressEvent) => mixed,
+  unstable_onKeyPressSync ?: ? (e: KeyPressEvent) => mixed,
 
   /**
    * Called when a single tap gesture is detected.
    */
-  onPress?: ?(event: PressEvent) => mixed,
+  onPress ?: ? (event: PressEvent) => mixed,
 
   /**
    * Called when a touch is engaged.
    */
-  onPressIn?: ?(event: PressEvent) => mixed,
+  onPressIn ?: ? (event: PressEvent) => mixed,
 
   /**
    * Called when a touch is released.
    */
-  onPressOut?: ?(event: PressEvent) => mixed,
+  onPressOut ?: ? (event: PressEvent) => mixed,
 
   /**
    * Callback that is called when the text input selection is changed.
    * This will be called with
    * `{ nativeEvent: { selection: { start, end } } }`.
    */
-  onSelectionChange?: ?(e: SelectionChangeEvent) => mixed,
+  onSelectionChange ?: ? (e: SelectionChangeEvent) => mixed,
 
   /**
    * Callback that is called when the text input's submit button is pressed.
    * Invalid if `multiline={true}` is specified.
    */
-  onSubmitEditing?: ?(e: EditingEvent) => mixed,
+  onSubmitEditing ?: ? (e: EditingEvent) => mixed,
 
   /**
    * Invoked on content scroll with `{ nativeEvent: { contentOffset: { x, y } } }`.
    * May also contain other properties from ScrollEvent but on Android contentSize
    * is not provided for performance reasons.
    */
-  onScroll?: ?(e: ScrollEvent) => mixed,
+  onScroll ?: ? (e: ScrollEvent) => mixed,
 
   /**
    * The string that will be rendered before text input has been entered.
    */
-  placeholder?: ?Stringish,
+  placeholder ?: ? Stringish,
 
   /**
    * The text color of the placeholder string.
    */
-  placeholderTextColor?: ?ColorValue,
+  placeholderTextColor ?: ? ColorValue,
 
   /** `readOnly` works like the `readonly` attribute in HTML.
    *  If `true`, text is not editable. The default value is `false`.
    *  See https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/readonly
    *  for more details.
    */
-  readOnly?: ?boolean,
+  readOnly ?: ? boolean,
 
   /**
    * Determines how the return key should look. On Android you can also use
@@ -895,38 +895,38 @@ export type Props = $ReadOnly<{|
    * - `route`
    * - `yahoo`
    */
-  returnKeyType?: ?ReturnKeyType,
+  returnKeyType ?: ? ReturnKeyType,
 
   /**
    * If `true`, the text input obscures the text entered so that sensitive text
    * like passwords stay secure. The default value is `false`. Does not work with 'multiline={true}'.
    */
-  secureTextEntry?: ?boolean,
+  secureTextEntry ?: ? boolean,
 
   /**
    * The start and end of the text input's selection. Set start and end to
    * the same value to position the cursor.
    */
-  selection?: ?$ReadOnly<{|
-    start: number,
-    end?: ?number,
+  selection ?: ? $ReadOnly < {|
+    start : number,
+  end ?: ? number,
   |}>,
 
   /**
    * The highlight and cursor color of the text input.
    */
-  selectionColor?: ?ColorValue,
+  selectionColor ?: ? ColorValue,
 
   /**
    * The text selection handle color.
    * @platform android
    */
-  selectionHandleColor?: ?ColorValue,
+  selectionHandleColor ?: ? ColorValue,
 
   /**
    * If `true`, all text will automatically be selected on focus.
    */
-  selectTextOnFocus?: ?boolean,
+  selectTextOnFocus ?: ? boolean,
 
   /**
    * If `true`, the text field will blur when submitted.
@@ -940,7 +940,7 @@ export type Props = $ReadOnly<{|
    * override any behavior defined by `blurOnSubmit`.
    * @see submitBehavior
    */
-  blurOnSubmit?: ?boolean,
+  blurOnSubmit ?: ? boolean,
 
   /**
    * When the return key is pressed,
@@ -960,7 +960,7 @@ export type Props = $ReadOnly<{|
    * - `'submit'` will only send a submit event and not blur the input
    * - `'blurAndSubmit`' will both blur the input and send a submit event
    */
-  submitBehavior?: ?SubmitBehavior,
+  submitBehavior ?: ? SubmitBehavior,
 
   /**
    * Note that not all Text styles are supported, an incomplete list of what is not supported includes:
@@ -979,7 +979,7 @@ export type Props = $ReadOnly<{|
    *
    * [Styles](docs/style.html)
    */
-  style?: ?TextStyleProp,
+  style ?: ? TextStyleProp,
 
   /**
    * The value to show for the text input. `TextInput` is a controlled
@@ -990,7 +990,7 @@ export type Props = $ReadOnly<{|
    * either set `editable={false}`, or set/update `maxLength` to prevent
    * unwanted edits without flicker.
    */
-  value?: ?Stringish,
+  value ?: ? Stringish,
 |}>;
 
 const emptyFunctionThatReturnsTrue = () => true;
@@ -1123,463 +1123,463 @@ function InternalTextInput(props: Props): React.Node {
     ...otherProps
   } = props;
 
-  const inputRef = useRef<null | React.ElementRef<HostComponent<mixed>>>(null);
+  const inputRef = useRef < null | React.ElementRef < HostComponent < mixed >>> (null);
 
   // eslint-disable-next-line react-hooks/exhaustive-deps
   const selection: ?Selection =
     propsSelection == null
       ? null
       : {
-          start: propsSelection.start,
-          end: propsSelection.end ?? propsSelection.start,
-        };
+        start: propsSelection.start,
+        end: propsSelection.end ?? propsSelection.start,
+      };
 
-  const [mostRecentEventCount, setMostRecentEventCount] = useState<number>(0);
-  const [lastNativeText, setLastNativeText] = useState<?Stringish>(props.value);
-  const [lastNativeSelectionState, setLastNativeSelection] = useState<{|
+  const [mostRecentEventCount, setMostRecentEventCount] = useState < number > (0);
+  const [lastNativeText, setLastNativeText] = useState <? Stringish > (props.value);
+  const [lastNativeSelectionState, setLastNativeSelection] = useState < {|
     selection: Selection,
     mostRecentEventCount: number,
-  |}>({
-    selection: {start: -1, end: -1},
-    mostRecentEventCount: mostRecentEventCount,
-  });
+  |}> ({
+  selection: { start: -1, end: -1 },
+  mostRecentEventCount: mostRecentEventCount,
+});
 
-  const lastNativeSelection = lastNativeSelectionState.selection;
+const lastNativeSelection = lastNativeSelectionState.selection;
 
-  let viewCommands;
-  if (AndroidTextInputCommands) {
-    viewCommands = AndroidTextInputCommands;
-  } else {
-    viewCommands =
-      props.multiline === true
-        ? RCTMultilineTextInputNativeCommands
-        : RCTSinglelineTextInputNativeCommands;
-  }
+let viewCommands;
+if (AndroidTextInputCommands) {
+  viewCommands = AndroidTextInputCommands;
+} else {
+  viewCommands =
+    props.multiline === true
+      ? RCTMultilineTextInputNativeCommands
+      : RCTSinglelineTextInputNativeCommands;
+}
 
-  const text =
-    typeof props.value === 'string'
-      ? props.value
-      : typeof props.defaultValue === 'string'
+const text =
+  typeof props.value === 'string'
+    ? props.value
+    : typeof props.defaultValue === 'string'
       ? props.defaultValue
       : '';
 
-  // This is necessary in case native updates the text and JS decides
-  // that the update should be ignored and we should stick with the value
-  // that we have in JS.
-  useLayoutEffect(() => {
-    const nativeUpdate: {text?: string, selection?: Selection} = {};
+// This is necessary in case native updates the text and JS decides
+// that the update should be ignored and we should stick with the value
+// that we have in JS.
+useLayoutEffect(() => {
+  const nativeUpdate: { text?: string, selection?: Selection } = {};
 
-    if (lastNativeText !== props.value && typeof props.value === 'string') {
-      nativeUpdate.text = props.value;
-      setLastNativeText(props.value);
-    }
+  if (lastNativeText !== props.value && typeof props.value === 'string') {
+    nativeUpdate.text = props.value;
+    setLastNativeText(props.value);
+  }
 
-    if (
-      selection &&
-      lastNativeSelection &&
-      (lastNativeSelection.start !== selection.start ||
-        lastNativeSelection.end !== selection.end)
-    ) {
-      nativeUpdate.selection = selection;
-      setLastNativeSelection({selection, mostRecentEventCount});
-    }
+  if (
+    selection &&
+    lastNativeSelection &&
+    (lastNativeSelection.start !== selection.start ||
+      lastNativeSelection.end !== selection.end)
+  ) {
+    nativeUpdate.selection = selection;
+    setLastNativeSelection({ selection, mostRecentEventCount });
+  }
 
-    if (Object.keys(nativeUpdate).length === 0) {
-      return;
-    }
+  if (Object.keys(nativeUpdate).length === 0) {
+    return;
+  }
 
-    if (inputRef.current != null) {
-      viewCommands.setTextAndSelection(
-        inputRef.current,
-        mostRecentEventCount,
-        text,
-        selection?.start ?? -1,
-        selection?.end ?? -1,
-      );
-    }
-  }, [
-    mostRecentEventCount,
-    inputRef,
-    props.value,
-    props.defaultValue,
-    lastNativeText,
-    selection,
-    lastNativeSelection,
-    text,
-    viewCommands,
-  ]);
-
-  useLayoutEffect(() => {
-    const inputRefValue = inputRef.current;
-
-    if (inputRefValue != null) {
-      TextInputState.registerInput(inputRefValue);
-
-      return () => {
-        TextInputState.unregisterInput(inputRefValue);
-
-        if (TextInputState.currentlyFocusedInput() === inputRefValue) {
-          nullthrows(inputRefValue).blur();
-        }
-      };
-    }
-  }, [inputRef]);
-
-  const setLocalRef = useCallback(
-    (instance: TextInputInstance | null) => {
-      inputRef.current = instance;
-
-      /*
-      Hi reader from the future. I'm sorry for this.
-
-      This is a hack. Ideally we would forwardRef to the underlying
-      host component. However, since TextInput has it's own methods that can be
-      called as well, if we used the standard forwardRef then these
-      methods wouldn't be accessible and thus be a breaking change.
-
-      We have a couple of options of how to handle this:
-      - Return a new ref with everything we methods from both. This is problematic
-        because we need React to also know it is a host component which requires
-        internals of the class implementation of the ref.
-      - Break the API and have some other way to call one set of the methods or
-        the other. This is our long term approach as we want to eventually
-        get the methods on host components off the ref. So instead of calling
-        ref.measure() you might call ReactNative.measure(ref). This would hopefully
-        let the ref for TextInput then have the methods like `.clear`. Or we do it
-        the other way and make it TextInput.clear(textInputRef) which would be fine
-        too. Either way though is a breaking change that is longer term.
-      - Mutate this ref. :( Gross, but accomplishes what we need in the meantime
-        before we can get to the long term breaking change.
-      */
-      if (instance != null) {
-        // $FlowFixMe[incompatible-use] - See the explanation above.
-        Object.assign(instance, {
-          clear(): void {
-            if (inputRef.current != null) {
-              viewCommands.setTextAndSelection(
-                inputRef.current,
-                mostRecentEventCount,
-                '',
-                0,
-                0,
-              );
-            }
-          },
-          // TODO: Fix this returning true on null === null, when no input is focused
-          isFocused(): boolean {
-            return TextInputState.currentlyFocusedInput() === inputRef.current;
-          },
-          getNativeRef(): ?React.ElementRef<HostComponent<mixed>> {
-            return inputRef.current;
-          },
-          setSelection(start: number, end: number): void {
-            if (inputRef.current != null) {
-              viewCommands.setTextAndSelection(
-                inputRef.current,
-                mostRecentEventCount,
-                null,
-                start,
-                end,
-              );
-            }
-          },
-        });
+  if (inputRef.current != null) {
+    viewCommands.setTextAndSelection(
+      inputRef.current,
+      mostRecentEventCount,
+      text,
+      selection?.start ?? -1,
+      selection?.end ?? -1,
+    );
+  }
+}, [
+  mostRecentEventCount,
+  inputRef,
+  props.value,
+  props.defaultValue,
+  lastNativeText,
+  selection,
+  lastNativeSelection,
+  text,
+  viewCommands,
+]);
+
+useLayoutEffect(() => {
+  const inputRefValue = inputRef.current;
+
+  if (inputRefValue != null) {
+    TextInputState.registerInput(inputRefValue);
+
+    return () => {
+      TextInputState.unregisterInput(inputRefValue);
+
+      if (TextInputState.currentlyFocusedInput() === inputRefValue) {
+        nullthrows(inputRefValue).blur();
       }
-    },
-    [mostRecentEventCount, viewCommands],
-  );
+    };
+  }
+}, [inputRef]);
+
+const setLocalRef = useCallback(
+  (instance: TextInputInstance | null) => {
+    inputRef.current = instance;
+
+    /*
+    Hi reader from the future. I'm sorry for this.
+
+    This is a hack. Ideally we would forwardRef to the underlying
+    host component. However, since TextInput has it's own methods that can be
+    called as well, if we used the standard forwardRef then these
+    methods wouldn't be accessible and thus be a breaking change.
+
+    We have a couple of options of how to handle this:
+    - Return a new ref with everything we methods from both. This is problematic
+      because we need React to also know it is a host component which requires
+      internals of the class implementation of the ref.
+    - Break the API and have some other way to call one set of the methods or
+      the other. This is our long term approach as we want to eventually
+      get the methods on host components off the ref. So instead of calling
+      ref.measure() you might call ReactNative.measure(ref). This would hopefully
+      let the ref for TextInput then have the methods like `.clear`. Or we do it
+      the other way and make it TextInput.clear(textInputRef) which would be fine
+      too. Either way though is a breaking change that is longer term.
+    - Mutate this ref. :( Gross, but accomplishes what we need in the meantime
+      before we can get to the long term breaking change.
+    */
+    if (instance != null) {
+      // $FlowFixMe[incompatible-use] - See the explanation above.
+      Object.assign(instance, {
+        clear(): void {
+          if (inputRef.current != null) {
+            viewCommands.setTextAndSelection(
+              inputRef.current,
+              mostRecentEventCount,
+              '',
+              0,
+              0,
+            );
+          }
+        },
+        // TODO: Fix this returning true on null === null, when no input is focused
+        isFocused(): boolean {
+          return TextInputState.currentlyFocusedInput() === inputRef.current;
+        },
+        getNativeRef(): ?React.ElementRef<HostComponent<mixed>> {
+          return inputRef.current;
+        },
+        setSelection(start: number, end: number): void {
+          if (inputRef.current != null) {
+            viewCommands.setTextAndSelection(
+              inputRef.current,
+              mostRecentEventCount,
+              null,
+              start,
+              end,
+            );
+          }
+        },
+      });
+    }
+  },
+  [mostRecentEventCount, viewCommands],
+);
 
-  const ref = useMergeRefs<TextInputInstance>(setLocalRef, props.forwardedRef);
+const ref = useMergeRefs < TextInputInstance > (setLocalRef, props.forwardedRef);
 
-  const _onChange = (event: ChangeEvent) => {
-    const currentText = event.nativeEvent.text;
-    props.onChange && props.onChange(event);
-    props.onChangeText && props.onChangeText(currentText);
+const _onChange = (event: ChangeEvent) => {
+  const currentText = event.nativeEvent.text;
+  props.onChange && props.onChange(event);
+  props.onChangeText && props.onChangeText(currentText);
 
-    if (inputRef.current == null) {
-      // calling `props.onChange` or `props.onChangeText`
-      // may clean up the input itself. Exits here.
-      return;
-    }
+  if (inputRef.current == null) {
+    // calling `props.onChange` or `props.onChangeText`
+    // may clean up the input itself. Exits here.
+    return;
+  }
 
-    setLastNativeText(currentText);
-    // This must happen last, after we call setLastNativeText.
-    // Different ordering can cause bugs when editing AndroidTextInputs
-    // with multiple Fragments.
-    // We must update this so that controlled input updates work.
-    setMostRecentEventCount(event.nativeEvent.eventCount);
-  };
+  setLastNativeText(currentText);
+  // This must happen last, after we call setLastNativeText.
+  // Different ordering can cause bugs when editing AndroidTextInputs
+  // with multiple Fragments.
+  // We must update this so that controlled input updates work.
+  setMostRecentEventCount(event.nativeEvent.eventCount);
+};
 
-  const _onChangeSync = (event: ChangeEvent) => {
-    const currentText = event.nativeEvent.text;
-    props.unstable_onChangeSync && props.unstable_onChangeSync(event);
-    props.unstable_onChangeTextSync &&
-      props.unstable_onChangeTextSync(currentText);
+const _onChangeSync = (event: ChangeEvent) => {
+  const currentText = event.nativeEvent.text;
+  props.unstable_onChangeSync && props.unstable_onChangeSync(event);
+  props.unstable_onChangeTextSync &&
+    props.unstable_onChangeTextSync(currentText);
 
-    if (inputRef.current == null) {
-      // calling `props.onChange` or `props.onChangeText`
-      // may clean up the input itself. Exits here.
-      return;
-    }
+  if (inputRef.current == null) {
+    // calling `props.onChange` or `props.onChangeText`
+    // may clean up the input itself. Exits here.
+    return;
+  }
 
-    setLastNativeText(currentText);
-    // This must happen last, after we call setLastNativeText.
-    // Different ordering can cause bugs when editing AndroidTextInputs
-    // with multiple Fragments.
-    // We must update this so that controlled input updates work.
-    setMostRecentEventCount(event.nativeEvent.eventCount);
-  };
+  setLastNativeText(currentText);
+  // This must happen last, after we call setLastNativeText.
+  // Different ordering can cause bugs when editing AndroidTextInputs
+  // with multiple Fragments.
+  // We must update this so that controlled input updates work.
+  setMostRecentEventCount(event.nativeEvent.eventCount);
+};
 
-  const _onSelectionChange = (event: SelectionChangeEvent) => {
-    props.onSelectionChange && props.onSelectionChange(event);
+const _onSelectionChange = (event: SelectionChangeEvent) => {
+  props.onSelectionChange && props.onSelectionChange(event);
 
-    if (inputRef.current == null) {
-      // calling `props.onSelectionChange`
-      // may clean up the input itself. Exits here.
-      return;
-    }
+  if (inputRef.current == null) {
+    // calling `props.onSelectionChange`
+    // may clean up the input itself. Exits here.
+    return;
+  }
 
-    setLastNativeSelection({
-      selection: event.nativeEvent.selection,
-      mostRecentEventCount,
-    });
-  };
+  setLastNativeSelection({
+    selection: event.nativeEvent.selection,
+    mostRecentEventCount,
+  });
+};
 
-  const _onFocus = (event: FocusEvent) => {
-    TextInputState.focusInput(inputRef.current);
-    if (props.onFocus) {
-      props.onFocus(event);
-    }
-  };
+const _onFocus = (event: FocusEvent) => {
+  TextInputState.focusInput(inputRef.current);
+  if (props.onFocus) {
+    props.onFocus(event);
+  }
+};
 
-  const _onBlur = (event: BlurEvent) => {
-    TextInputState.blurInput(inputRef.current);
-    if (props.onBlur) {
-      props.onBlur(event);
-    }
-  };
+const _onBlur = (event: BlurEvent) => {
+  TextInputState.blurInput(inputRef.current);
+  if (props.onBlur) {
+    props.onBlur(event);
+  }
+};
 
-  const _onScroll = (event: ScrollEvent) => {
-    props.onScroll && props.onScroll(event);
-  };
+const _onScroll = (event: ScrollEvent) => {
+  props.onScroll && props.onScroll(event);
+};
 
-  let textInput = null;
+let textInput = null;
 
-  const multiline = props.multiline ?? false;
+const multiline = props.multiline ?? false;
 
-  let submitBehavior: SubmitBehavior;
-  if (props.submitBehavior != null) {
-    // `submitBehavior` is set explicitly
-    if (!multiline && props.submitBehavior === 'newline') {
-      // For single line text inputs, `'newline'` is not a valid option
-      submitBehavior = 'blurAndSubmit';
-    } else {
-      submitBehavior = props.submitBehavior;
-    }
-  } else if (multiline) {
-    if (props.blurOnSubmit === true) {
-      submitBehavior = 'blurAndSubmit';
-    } else {
-      submitBehavior = 'newline';
-    }
+let submitBehavior: SubmitBehavior;
+if (props.submitBehavior != null) {
+  // `submitBehavior` is set explicitly
+  if (!multiline && props.submitBehavior === 'newline') {
+    // For single line text inputs, `'newline'` is not a valid option
+    submitBehavior = 'blurAndSubmit';
   } else {
-    // Single line
-    if (props.blurOnSubmit !== false) {
-      submitBehavior = 'blurAndSubmit';
-    } else {
-      submitBehavior = 'submit';
-    }
+    submitBehavior = props.submitBehavior;
+  }
+} else if (multiline) {
+  if (props.blurOnSubmit === true) {
+    submitBehavior = 'blurAndSubmit';
+  } else {
+    submitBehavior = 'newline';
   }
+} else {
+  // Single line
+  if (props.blurOnSubmit !== false) {
+    submitBehavior = 'blurAndSubmit';
+  } else {
+    submitBehavior = 'submit';
+  }
+}
 
-  const accessible = props.accessible !== false;
-  const focusable = props.focusable !== false;
+const accessible = props.accessible !== false;
+const focusable = props.focusable !== false;
 
-  const {
+const {
+  editable,
+  hitSlop,
+  onPress,
+  onPressIn,
+  onPressOut,
+  rejectResponderTermination,
+} = props;
+
+const config = React.useMemo(
+  () => ({
+    hitSlop,
+    onPress: (event: PressEvent) => {
+      onPress?.(event);
+      if (editable !== false) {
+        if (inputRef.current != null) {
+          inputRef.current.focus();
+        }
+      }
+    },
+    onPressIn: onPressIn,
+    onPressOut: onPressOut,
+    cancelable: Platform.OS === 'ios' ? !rejectResponderTermination : null,
+  }),
+  [
     editable,
     hitSlop,
     onPress,
     onPressIn,
     onPressOut,
     rejectResponderTermination,
-  } = props;
-
-  const config = React.useMemo(
-    () => ({
-      hitSlop,
-      onPress: (event: PressEvent) => {
-        onPress?.(event);
-        if (editable !== false) {
-          if (inputRef.current != null) {
-            inputRef.current.focus();
-          }
-        }
-      },
-      onPressIn: onPressIn,
-      onPressOut: onPressOut,
-      cancelable: Platform.OS === 'ios' ? !rejectResponderTermination : null,
-    }),
-    [
-      editable,
-      hitSlop,
-      onPress,
-      onPressIn,
-      onPressOut,
-      rejectResponderTermination,
-    ],
-  );
-
-  // Hide caret during test runs due to a flashing caret
-  // makes screenshot tests flakey
-  let caretHidden = props.caretHidden;
-  if (Platform.isTesting) {
-    caretHidden = true;
-  }
-
-  // TextInput handles onBlur and onFocus events
-  // so omitting onBlur and onFocus pressability handlers here.
-  const {onBlur, onFocus, ...eventHandlers} = usePressability(config) || {};
+  ],
+);
+
+// Hide caret during test runs due to a flashing caret
+// makes screenshot tests flakey
+let caretHidden = props.caretHidden;
+if (Platform.isTesting) {
+  caretHidden = true;
+}
 
-  let _accessibilityState;
-  if (
-    accessibilityState != null ||
-    ariaBusy != null ||
-    ariaChecked != null ||
-    ariaDisabled != null ||
-    ariaExpanded != null ||
-    ariaSelected != null
-  ) {
-    _accessibilityState = {
-      busy: ariaBusy ?? accessibilityState?.busy,
-      checked: ariaChecked ?? accessibilityState?.checked,
-      disabled: ariaDisabled ?? accessibilityState?.disabled,
-      expanded: ariaExpanded ?? accessibilityState?.expanded,
-      selected: ariaSelected ?? accessibilityState?.selected,
-    };
-  }
+// TextInput handles onBlur and onFocus events
+// so omitting onBlur and onFocus pressability handlers here.
+const { onBlur, onFocus, ...eventHandlers } = usePressability(config) || {};
+
+let _accessibilityState;
+if (
+  accessibilityState != null ||
+  ariaBusy != null ||
+  ariaChecked != null ||
+  ariaDisabled != null ||
+  ariaExpanded != null ||
+  ariaSelected != null
+) {
+  _accessibilityState = {
+    busy: ariaBusy ?? accessibilityState?.busy,
+    checked: ariaChecked ?? accessibilityState?.checked,
+    disabled: ariaDisabled ?? accessibilityState?.disabled,
+    expanded: ariaExpanded ?? accessibilityState?.expanded,
+    selected: ariaSelected ?? accessibilityState?.selected,
+  };
+}
 
-  const style = flattenStyle<TextStyleProp>(props.style);
-
-  if (Platform.OS === 'ios') {
-    const RCTTextInputView =
-      props.multiline === true
-        ? RCTMultilineTextInputView
-        : RCTSinglelineTextInputView;
-
-    const useMultilineDefaultStyle =
-      props.multiline === true &&
-      (style == null ||
-        (style.padding == null &&
-          style.paddingVertical == null &&
-          style.paddingTop == null));
-
-    const useOnChangeSync =
-      (props.unstable_onChangeSync || props.unstable_onChangeTextSync) &&
-      !(props.onChange || props.onChangeText);
-
-    textInput = (
-      <RCTTextInputView
-        // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.
-        ref={ref}
-        {...otherProps}
-        {...eventHandlers}
-        accessibilityState={_accessibilityState}
-        accessible={accessible}
-        submitBehavior={submitBehavior}
-        caretHidden={caretHidden}
-        dataDetectorTypes={props.dataDetectorTypes}
-        focusable={tabIndex !== undefined ? !tabIndex : focusable}
-        mostRecentEventCount={mostRecentEventCount}
-        nativeID={id ?? props.nativeID}
-        onBlur={_onBlur}
-        onKeyPressSync={props.unstable_onKeyPressSync}
-        onChange={_onChange}
-        onChangeSync={useOnChangeSync === true ? _onChangeSync : null}
-        onContentSizeChange={props.onContentSizeChange}
-        onFocus={_onFocus}
-        onScroll={_onScroll}
-        onSelectionChange={_onSelectionChange}
-        onSelectionChangeShouldSetResponder={emptyFunctionThatReturnsTrue}
-        selection={selection}
-        selectionColor={selectionColor}
-        style={StyleSheet.compose(
-          useMultilineDefaultStyle ? styles.multilineDefault : null,
-          style,
-        )}
-        text={text}
-      />
-    );
-  } else if (Platform.OS === 'android') {
-    const autoCapitalize = props.autoCapitalize || 'sentences';
-    const _accessibilityLabelledBy =
-      props?.['aria-labelledby'] ?? props?.accessibilityLabelledBy;
-    const placeholder = props.placeholder ?? '';
-    let children = props.children;
-    const childCount = React.Children.count(children);
-    invariant(
-      !(props.value != null && childCount),
-      'Cannot specify both value and children.',
-    );
-    if (childCount > 1) {
-      children = <Text>{children}</Text>;
-    }
-    // For consistency with iOS set cursor/selectionHandle color as selectionColor
-    const colorProps = {
-      selectionColor,
-      selectionHandleColor:
-        selectionHandleColor === undefined
-          ? selectionColor
-          : selectionHandleColor,
-      cursorColor: cursorColor === undefined ? selectionColor : cursorColor,
-    };
-    textInput = (
-      /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match up
-       * exactly with the props for TextInput. This will need to get fixed */
-      /* $FlowFixMe[incompatible-type] the types for AndroidTextInput don't
-       * match up exactly with the props for TextInput. This will need to get
-       * fixed */
-      /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput don't
-       * match up exactly with the props for TextInput. This will need to get
-       * fixed */
-      <AndroidTextInput
-        // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.
-        ref={ref}
-        {...otherProps}
-        {...colorProps}
-        {...eventHandlers}
-        accessibilityState={_accessibilityState}
-        accessibilityLabelledBy={_accessibilityLabelledBy}
-        accessible={accessible}
-        autoCapitalize={autoCapitalize}
-        submitBehavior={submitBehavior}
-        caretHidden={caretHidden}
-        children={children}
-        disableFullscreenUI={props.disableFullscreenUI}
-        focusable={tabIndex !== undefined ? !tabIndex : focusable}
-        mostRecentEventCount={mostRecentEventCount}
-        nativeID={id ?? props.nativeID}
-        numberOfLines={props.rows ?? props.numberOfLines}
-        onBlur={_onBlur}
-        onChange={_onChange}
-        onFocus={_onFocus}
-        /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match
-         * up exactly with the props for TextInput. This will need to get fixed
-         */
-        /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput
-         * don't match up exactly with the props for TextInput. This will need
-         * to get fixed */
-        onScroll={_onScroll}
-        onSelectionChange={_onSelectionChange}
-        placeholder={placeholder}
-        style={style}
-        text={text}
-        textBreakStrategy={props.textBreakStrategy}
-      />
-    );
+const style = flattenStyle < TextStyleProp > (props.style);
+
+if (Platform.OS === 'ios') {
+  const RCTTextInputView =
+    props.multiline === true
+      ? RCTMultilineTextInputView
+      : RCTSinglelineTextInputView;
+
+  const useMultilineDefaultStyle =
+    props.multiline === true &&
+    (style == null ||
+      (style.padding == null &&
+        style.paddingVertical == null &&
+        style.paddingTop == null));
+
+  const useOnChangeSync =
+    (props.unstable_onChangeSync || props.unstable_onChangeTextSync) &&
+    !(props.onChange || props.onChangeText);
+
+  textInput = (
+    <RCTTextInputView
+      // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.
+      ref={ref}
+      {...otherProps}
+      {...eventHandlers}
+      accessibilityState={_accessibilityState}
+      accessible={accessible}
+      submitBehavior={submitBehavior}
+      caretHidden={caretHidden}
+      dataDetectorTypes={props.dataDetectorTypes}
+      focusable={tabIndex !== undefined ? !tabIndex : focusable}
+      mostRecentEventCount={mostRecentEventCount}
+      nativeID={id ?? props.nativeID}
+      onBlur={_onBlur}
+      onKeyPressSync={props.unstable_onKeyPressSync}
+      onChange={_onChange}
+      onChangeSync={useOnChangeSync === true ? _onChangeSync : null}
+      onContentSizeChange={props.onContentSizeChange}
+      onFocus={_onFocus}
+      onScroll={_onScroll}
+      onSelectionChange={_onSelectionChange}
+      onSelectionChangeShouldSetResponder={emptyFunctionThatReturnsTrue}
+      selection={selection}
+      selectionColor={selectionColor}
+      style={StyleSheet.compose(
+        useMultilineDefaultStyle ? styles.multilineDefault : null,
+        style,
+      )}
+      text={text}
+    />
+  );
+} else if (Platform.OS === 'android') {
+  const autoCapitalize = props.autoCapitalize || 'sentences';
+  const _accessibilityLabelledBy =
+    props?.['aria-labelledby'] ?? props?.accessibilityLabelledBy;
+  const placeholder = props.placeholder ?? '';
+  let children = props.children;
+  const childCount = React.Children.count(children);
+  invariant(
+    !(props.value != null && childCount),
+    'Cannot specify both value and children.',
+  );
+  if (childCount > 1) {
+    children = <Text>{children}</Text>;
   }
-  return (
-    <TextAncestor.Provider value={true}>{textInput}</TextAncestor.Provider>
+  // For consistency with iOS set cursor/selectionHandle color as selectionColor
+  const colorProps = {
+    selectionColor,
+    selectionHandleColor:
+      selectionHandleColor === undefined
+        ? selectionColor
+        : selectionHandleColor,
+    cursorColor: cursorColor === undefined ? selectionColor : cursorColor,
+  };
+  textInput = (
+    /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match up
+     * exactly with the props for TextInput. This will need to get fixed */
+    /* $FlowFixMe[incompatible-type] the types for AndroidTextInput don't
+     * match up exactly with the props for TextInput. This will need to get
+     * fixed */
+    /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput don't
+     * match up exactly with the props for TextInput. This will need to get
+     * fixed */
+    <AndroidTextInput
+      // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.
+      ref={ref}
+      {...otherProps}
+      {...colorProps}
+      {...eventHandlers}
+      accessibilityState={_accessibilityState}
+      accessibilityLabelledBy={_accessibilityLabelledBy}
+      accessible={accessible}
+      autoCapitalize={autoCapitalize}
+      submitBehavior={submitBehavior}
+      caretHidden={caretHidden}
+      children={children}
+      disableFullscreenUI={props.disableFullscreenUI}
+      focusable={tabIndex !== undefined ? !tabIndex : focusable}
+      mostRecentEventCount={mostRecentEventCount}
+      nativeID={id ?? props.nativeID}
+      numberOfLines={props.rows ?? props.numberOfLines}
+      onBlur={_onBlur}
+      onChange={_onChange}
+      onFocus={_onFocus}
+      /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match
+       * up exactly with the props for TextInput. This will need to get fixed
+       */
+      /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput
+       * don't match up exactly with the props for TextInput. This will need
+       * to get fixed */
+      onScroll={_onScroll}
+      onSelectionChange={_onSelectionChange}
+      placeholder={placeholder}
+      style={style}
+      text={text}
+      textBreakStrategy={props.textBreakStrategy}
+    />
   );
 }
+return (
+  <TextAncestor.Provider value={true}>{textInput}</TextAncestor.Provider>
+);
+}
 
 const enterKeyHintToReturnTypeMap = {
   enter: 'default',
@@ -1679,7 +1679,7 @@ const autoCompleteWebToTextContentTypeMap = {
 const ExportedForwardRef: React.AbstractComponent<
   React.ElementConfig<typeof InternalTextInput>,
   TextInputInstance,
-  // $FlowFixMe[incompatible-call]
+// $FlowFixMe[incompatible-call]
 > = React.forwardRef(function TextInput(
   {
     allowFontScaling = true,
@@ -1730,9 +1730,9 @@ const ExportedForwardRef: React.AbstractComponent<
       autoComplete={
         Platform.OS === 'android'
           ? // $FlowFixMe[invalid-computed-prop]
-            // $FlowFixMe[prop-missing]
-            autoCompleteWebToAutoCompleteAndroidMap[autoComplete] ??
-            autoComplete
+          // $FlowFixMe[prop-missing]
+          autoCompleteWebToAutoCompleteAndroidMap[autoComplete] ??
+          autoComplete
           : undefined
       }
       textContentType={
@@ -1741,11 +1741,12 @@ const ExportedForwardRef: React.AbstractComponent<
           : Platform.OS === 'ios' &&
             autoComplete &&
             autoComplete in autoCompleteWebToTextContentTypeMap
-          ? // $FlowFixMe[invalid-computed-prop]
+            ? // $FlowFixMe[invalid-computed-prop]
             // $FlowFixMe[prop-missing]
             autoCompleteWebToTextContentTypeMap[autoComplete]
-          : textContentType
+            : textContentType
       }
+      selectTextOnFocus={true}
       {...restProps}
       forwardedRef={forwardedRef}
       style={style}
@@ -1765,11 +1766,11 @@ ExportedForwardRef.State = {
 };
 
 export type TextInputComponentStatics = $ReadOnly<{|
-  State: $ReadOnly<{|
+  State: $ReadOnly < {|
     currentlyFocusedInput: typeof TextInputState.currentlyFocusedInput,
-    currentlyFocusedField: typeof TextInputState.currentlyFocusedField,
-    focusTextInput: typeof TextInputState.focusTextInput,
-    blurTextInput: typeof TextInputState.blurTextInput,
+      currentlyFocusedField: typeof TextInputState.currentlyFocusedField,
+        focusTextInput: typeof TextInputState.focusTextInput,
+          blurTextInput: typeof TextInputState.blurTextInput,
   |}>,
 |}>;
 
diff --git a/node_modules/react-native/index.js b/node_modules/react-native/index.js
index f087b70..34c67d2 100644
--- a/node_modules/react-native/index.js
+++ b/node_modules/react-native/index.js
@@ -746,4 +746,32 @@ if (__DEV__) {
       );
     },
   });
+
+  Object.defineProperty(module.exports, 'ColorPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').ColorPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'EdgeInsetsPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').EdgeInsetsPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'PointPropType', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').PointPropType;
+    },
+  });
+
+  Object.defineProperty(module.exports, 'ViewPropTypes', {
+    configurable: true,
+    get() {
+      return require('deprecated-react-native-prop-types').ViewPropTypes;
+    },
+  });
 }
