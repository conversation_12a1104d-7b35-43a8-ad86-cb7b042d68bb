
import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Colors from '../../constant/Colors';
import { getCachedUrlContent, getImageFromFirebaseStorage } from '../../util/common';
import WebView from 'react-native-webview';

const WebViewProcess = props => {
    const {
        source,
        item,
        style,
        hideLoading,
    } = props;

    const webViewRef = React.useRef(null);
    const reloadsCount = React.useRef(0);
    const onLoadInjected = `
    window.addEventListener('load', (event) => {
      window.ReactNativeWebView.postMessage('InjectNewCode');
    });
  `;

    const injectNewJsCode = () => {
        webViewRef.currentF.injectJavaScript(`alert('new code injected')`);
        reloadsCount.current++;
    }

    return (
        <WebView
            ref={webViewRef}
            pullToRefreshEnabled
            source={{ html: '<html><body></body></html>' }}
            onMessage={({ nativeEvent }) => {
                // if (nativeEvent.data === 'InjectNewCode') {
                //     // skip the inital load event
                //     if (reloadsCount.current > 0) {
                //         injectNewJsCode()
                //     }
                //     reloadsCount.current++
                // }
            }}
            injectedJavaScript={onLoadInjected}
        />
    );
};

export default WebViewProcess;