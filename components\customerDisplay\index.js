
import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    useWindowDimensions,
    Alert,
    Platform,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Colors from '../../constant/Colors';
import { fixRNDimensionsManual, getCachedUrlContent, getImageFromFirebaseStorage, logToFile } from '../../util/common';
import ExternalDisplay, {
    useExternalDisplay,
} from 'react-native-external-display'
import {
    ScrollView,
} from 'react-native-gesture-handler'
import { TableStore } from '../../store/tableStore';
import AsyncImage from '../asyncImage';
import { MerchantStore } from '../../store/merchantStore';
import { CommonStore } from '../../store/commonStore';
import TablePaymentSummary from '../../screen/components/tablePaymentSummary';

const CustomerDisplay = props => {
    const {
    } = props;

    const [externalScreenExisted, setExternalScreenExisted] = useState(false);

    const screens = useExternalDisplay({
        onScreenConnect: () => {
            // Alert.alert('Display connected');

            logToFile('display connected');
            // Alert.alert('display connected');

            setExternalScreenExisted(true);

            if (Platform.OS === 'ios') {
                fixRNDimensionsManual();
            }
        },
        onScreenChange: () => {
            // Alert.alert('Display disconnected');

            logToFile('screen changed');
            // Alert.alert('screen connected');
        },
        onScreenDisconnect: (error) => {
            // Alert.alert('Display error', error);

            logToFile(`display disconnected: ${error}`);
            // Alert.alert(`display disconnected: ${error}`);

            setExternalScreenExisted(false);
        }
    });

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [currOutlet, setCurrOutlet] = useState({});

    const renderPaymentSummary = TableStore.useState(s => s.renderPaymentSummary);
    const renderReceipt = TableStore.useState(s => s.renderReceipt);
    const merchantLogo = MerchantStore.useState((s) => s.logo);

    const currOutletRaw = MerchantStore.useState((s) => s.currOutlet);

    const customerDisplaySupport = CommonStore.useState((s) => s.customerDisplaySupport);

    useEffect(() => {
        let currOutletNew = {};

        if (currOutletRaw && currOutletRaw.uniqueId) {
            currOutletNew = {
                ...currOutletRaw,

                fcs: currOutletRaw.fcs !== undefined ? currOutletRaw.fcs : false,
                fcsFb: currOutletRaw.fcsFb !== undefined ? currOutletRaw.fcsFb : false,
                // fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : `{ "width": "0%", "height": "0%" }`,
                fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : JSON.stringify({
                    "width": renderPaymentSummary || renderReceipt ? '100%' : "0%",
                    "height": renderPaymentSummary || renderReceipt ? '100%' : "0%"
                }),
                // fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : ``,
                fcsStyleMain: currOutletRaw.fcsStyleMain !== undefined ? currOutletRaw.fcsStyleMain : ``,
                ghrv: currOutletRaw.ghrv !== undefined ? currOutletRaw.ghrv : false,
                ghrvStyle: currOutletRaw.ghrvStyle !== undefined ? currOutletRaw.ghrvStyle : ``,
                rnedPointerEvents: currOutletRaw.rnedPointerEvents !== undefined ? currOutletRaw.rnedPointerEvents : '',

                fcsZindex: currOutletRaw.fcsZindex !== undefined ? currOutletRaw.fcsZindex : undefined,

                fcsStyleMain: currOutletRaw.fcsStyleMain !== undefined ? currOutletRaw.fcsStyleMain : ``,
            };
        }
        else {
            currOutletNew = {
                fcs: currOutletRaw.fcs !== undefined ? currOutletRaw.fcs : false,
                fcsFb: currOutletRaw.fcsFb !== undefined ? currOutletRaw.fcsFb : false,
                // fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : `{ "width": "0%", "height": "0%" }`,
                fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : JSON.stringify({
                    "width": "0%",
                    "height": "0%"
                }),
                // fcsStyle: currOutletRaw.fcsStyle !== undefined ? currOutletRaw.fcsStyle : ``,
                fcsStyleMain: currOutletRaw.fcsStyleMain !== undefined ? currOutletRaw.fcsStyleMain : ``,
                ghrv: currOutletRaw.ghrv !== undefined ? currOutletRaw.ghrv : false,
                ghrvStyle: currOutletRaw.ghrvStyle !== undefined ? currOutletRaw.ghrvStyle : ``,
                rnedPointerEvents: currOutletRaw.rnedPointerEvents !== undefined ? currOutletRaw.rnedPointerEvents : '',

                fcsZindex: currOutletRaw.fcsZindex !== undefined ? currOutletRaw.fcsZindex : undefined,
            };
        }

        setCurrOutlet(currOutletNew);
    }, [
        currOutletRaw,

        renderPaymentSummary,
        renderReceipt,
    ]);

    return (<>
        {
            (externalScreenExisted ||
                (currOutlet && currOutlet.fcs) ||
                customerDisplaySupport
            )
                ?
                <>
                    {
                        (
                            // !renderPaymentSummary
                            // &&
                            // !renderReceipt
                            true
                        )
                            ?
                            <ExternalDisplay
                                style={{
                                    // flex: 1,

                                    // width: '0%',
                                    // height: '0%',
                                    zIndex: -1,

                                    ...((currOutlet && currOutlet.fcsStyle) && {
                                        // width: '0%',
                                        // height: '0%',

                                        ...JSON.parse(currOutlet.fcsStyle),
                                    }),
                                }}
                                mainScreenStyle={{
                                    // flex: 1,

                                    ...((currOutlet && currOutlet.fcsStyleMain) && {
                                        ...JSON.parse(currOutlet.fcsStyleMain),
                                    }),
                                }}
                                ghrv={(currOutlet && currOutlet.ghrv) ? currOutlet.ghrv : ''}
                                ghrvStyle={{
                                    ...((currOutlet && currOutlet.ghrvStyle) && {
                                        ...JSON.parse(currOutlet.ghrvStyle),
                                    }),
                                }}
                                rnedPointerEvents={(currOutlet && currOutlet.rnedPointerEvents) ? currOutlet.rnedPointerEvents : ''}
                                // fallbackInMainScreen={(renderPaymentSummary || renderReceipt) ? true : false}
                                fallbackInMainScreen={(currOutlet && currOutlet.fcsFb !== undefined) ? currOutlet.fcsFb : false}
                                screen={Object.keys(screens)[0]}

                                {
                                ...((currOutlet && currOutlet.fcsZindex !== undefined) && {
                                    zIndex: currOutlet.fcsZindex,
                                })
                                }
                            >
                                {renderPaymentSummary || renderReceipt ?
                                    <TablePaymentSummary
                                        showCustomerDisplay={true}
                                    />
                                    :
                                    <ScrollView
                                        style={{
                                            width: '100%',
                                            height: '100%',

                                            ...((currOutlet && currOutlet.svStyle) && {
                                                ...JSON.parse(currOutlet.svStyle),
                                            }),
                                        }}
                                        contentContainerStyle={{
                                            width: '100%',
                                            height: '100%',

                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',

                                            ...((currOutlet && currOutlet.svContainerStyle) && {
                                                ...JSON.parse(currOutlet.svContainerStyle),
                                            }),
                                        }}>
                                        {merchantLogo ?
                                            <AsyncImage
                                                source={{ uri: merchantLogo }}
                                                style={{
                                                    width: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                    height: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                    marginBottom: 10,
                                                    marginTop: 0,
                                                    alignSelf: 'center',

                                                    ...((currOutlet && currOutlet.svContainerImageStyle) && {
                                                        ...JSON.parse(currOutlet.svContainerImageStyle),
                                                    }),
                                                }} />
                                            :
                                            <Image
                                                style={[{
                                                    width: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,
                                                    height: (currOutlet && currOutlet.fcsLogo !== undefined) ? currOutlet.fcsLogo : 180,

                                                    ...((currOutlet && currOutlet.svContainerImageStyle) && {
                                                        ...JSON.parse(currOutlet.svContainerImageStyle),
                                                    }),
                                                }]}
                                                resizeMode="contain"
                                                source={require('../../assets/image/logo.png')}
                                            />
                                        }
                                    </ScrollView>
                                }
                            </ExternalDisplay>
                            :
                            <></>
                    }
                </>
                :
                <></>
        }
    </>);
};

export default CustomerDisplay;