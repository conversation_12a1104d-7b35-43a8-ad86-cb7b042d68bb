import ApiClient from './ApiClient';
import API from '../constant/API';
import {
  CANCEL_REASON_CODE,
  GRAB_ORDER_STATUS_MANUAL,
  GRAB_ORDER_STATE
} from '../constant/grab.js'

/**
 * Manually accept or reject Grab Food orders
 * @param {String} grabOrderId - The grab order ID
 * @param {GRAB_ORDER_STATUS_MANUAL} status - The status to set (accept/reject)
 * @returns {Promise<void>} A promise that resolves when the operation is complete
 */
export const grabManualAcceptRejectOrder = async ( grabOrderId, status ) => {
  // if (!Object.values(GRAB_ORDER_STATUS_MANUAL).includes(status)) {
  //   throw new Error(`Invalid status: ${status}. Must be either ${GRAB_ORDER_STATUS_MANUAL.APPROVED} or ${GRAB_ORDER_STATUS_MANUAL.REJECTED}`);
  // }

  const body = {
    odId: grabOrderId,
    status: status
  }

  const res = await ApiClient.POST(API.grabManualAcceptRejectOrder, body);
  return res;
};

/**
 * Cancel Grab Food Orders
 * @param {String} grabOrderId - The grab order ID
 * @param {CANCEL_REASON_CODE} status - Code of the reason for cancelling the order
 * @returns {Promise<void>} A promise that resolves when the operation is complete
 */
export const grabCancelOrder = async ( grabOrderId, reasonCode ) => {
  if (!global.currOutlet?.odGrabMID) {
    throw new Error('odGrabMID is required but was null or undefined')
  }

  const body = {
    odId: grabOrderId,
    odGrabMID: global.currOutlet.odGrabMID,
    cancelCode: reasonCode
  }

  const res = await ApiClient.POST(API.grabCancelOrder, body);
  return res;
}

// grabOrderRejectItems

// export const grabUpdateOrderTime = async () => {

// };

/**
 * Mark Order as Ready
 * @param {String} grabOrderId - The grab order Id to be mark as ready
 * @returns {Promise<void>} A promise that resolves when the operation is complete
 */
export const grabMarkOrderReady = async ( grabOrderId ) => {
  const body = { odId: grabOrderId }

  const res = await ApiClient.POST(API.grabMarkOrderReady, body);
  return res;
};

/**
 * Update Grab Food Order Delivery State
 * @param {String} grabOrderId - The grab order Id to be mark as ready
 * @param {GRAB_ORDER_STATE} statusFrom - The grabFood Order Previous State
 * @param {GRAB_ORDER_STATE} statusTo - The grabFood Order Next State
 * @returns {Promise<void>} A promise that resolves when the operation is complete
 */
export const grabUpdateDeliveryState = async ( grabOrderId, statusFrom, statusTo ) => {

  const body = {
    odId: grabOrderId,
    statusFrom,
    statusTo
  }

  const res = await ApiClient.POST(API.grabUpdateDeliveryState, body);
  return res;
};

export const grabSelfServeJourney = async ( outletId ) => {

  const body = {
    outletId: outletId,
  }

  const res = await ApiClient.POST(API.grabSelfServeJourney, body);
  return res;
};