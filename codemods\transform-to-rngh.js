/**
 * Codemod to replace React Native FlatList, ScrollView, and TouchableOpacity
 * with react-native-gesture-handler equivalents.
 */

export default function transformer(file, api) {
    const j = api.jscodeshift;
    const root = j(file.source);

    // Step 1: Update import statements
    const reactNativeImports = root.find(j.ImportDeclaration, {
        source: { value: 'react-native' },
    });

    reactNativeImports.forEach((path) => {
        const specifiersToRemove = [];
        const gestureHandlerSpecifiers = [];

        path.node.specifiers.forEach((specifier) => {
            const importedComponent = specifier.imported.name;

            if (
                importedComponent === 'FlatList' ||
                importedComponent === 'ScrollView' 
                // ||
                // importedComponent === 'TouchableOpacity'
            ) {
                // Mark the component for removal from 'react-native'
                specifiersToRemove.push(specifier);
                // Add the component to the gesture handler specifiers
                gestureHandlerSpecifiers.push(j.importSpecifier(j.identifier(importedComponent)));
            }
        });

        if (specifiersToRemove.length > 0) {
            // Remove the specified components from 'react-native' import
            path.node.specifiers = path.node.specifiers.filter(
                (specifier) => !specifiersToRemove.includes(specifier)
            );

            // Create or update 'react-native-gesture-handler' import
            let gestureHandlerImport = root.find(j.ImportDeclaration, {
                source: { value: 'react-native-gesture-handler' },
            });

            if (gestureHandlerImport.size() === 0) {
                // If no existing import, create a new one
                const newImport = j.importDeclaration(
                    gestureHandlerSpecifiers,
                    j.literal('react-native-gesture-handler')
                );
                path.insertAfter(newImport);
            } else {
                // If already exists, add the new specifiers to the existing one
                gestureHandlerImport.get(0).node.specifiers.push(...gestureHandlerSpecifiers);
            }
        }
    });

    // Step 2: Replace JSX component names
    const componentMapping = {
        FlatList: 'FlatList',
        ScrollView: 'ScrollView',
        // TouchableOpacity: 'TouchableOpacity',
    };

    root
        .find(j.JSXIdentifier)
        .filter((path) => Object.keys(componentMapping).includes(path.node.name))
        .forEach((path) => {
            path.node.name = componentMapping[path.node.name];
        });

    return root.toSource();
}