import { Text } from "react-native-fast-text";
import React from 'react';
import { View, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import Colors from '../constant/Colors';

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    paddingVertical: 20,
  },
  text: {
    color: '#4a4a4a',
    fontSize: 15,
  },
  separator: {
    flex: 1,
    height: 1,
    backgroundColor: '#e4e4e4',
    marginLeft: 10,
  },
  leftAction: {
    backgroundColor: '#388e3c',
    justifyContent: 'center',
    flex: 1,
  },
  rightAction: {
    backgroundColor: '#dd2c00',
    justifyContent: 'center',
    flex: 1,
    alignItems: 'flex-end',
  },
  actionText: {
    color: '#fff',
    fontFamily: 'NunitoSans-Bold',
    padding: 20,
  },
  currentMenuPic: {
    backgroundColor: Colors.secondaryColor,
    width: 50,
    height: 50,
    borderRadius: 8
  },
});

export const Separator = () => <View style={styles.separator} />;


const RightActions = ({ progress, dragX, onPress }) => {
  const scale = dragX.interpolate({
    inputRange: [-100, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.rightAction}>
        <Animated.Text style={[styles.actionText, { transform: [{ scale }] }]}>
          Delete
        </Animated.Text>
      </View>
    </TouchableOpacity>
  );
};

const ListItem = ({ onSwipeFromLeft, onRightPress, items }) => (
  <View>

    {items.map(i => {
      return (
        <Swipeable
          // renderLeftActions={LeftActions}
          onSwipeableLeftOpen={onSwipeFromLeft}

          renderRightActions={(progress, dragX) => (
            <RightActions progress={progress} dragX={dragX} onPress={onRightPress} />
          )}>
          <View style={{ flex: 1, flexDirection: 'row', marginLeft: 10, alignContent: 'center', alignItems: 'center', paddingBottom: 10 }}>
            <View style={styles.currentMenuPic}></View>
            <View style={{ marginLeft: 15, marginVertical: 10 }}>
              <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{i.name}</Text>
              <Text style={{ fontSize: 14, color: Colors.primaryColor, fontFamily: 'NunitoSans-Regular' }}>RM {i.price}</Text>
            </View>
          </View>
        </Swipeable>
      )
    })}


  </View>
);
// }

export default ListItem;
