import React, { useState, Component, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  Image,
  ScrollView,
  TouchableOpacity,
  Button,
  TextInput,
} from 'react-native';
import Styles from '../constant/Styles';
import * as ImagePicker from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import storage from '@react-native-firebase/storage';
import Colors from '../constant/Colors';
import { UserStore } from '../store/userStore';
import { ROLE_TYPE } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import AsyncStorage from '@react-native-async-storage/async-storage';

const imagePickerOptions = {
  mediaType: 'photo',
  quality: 0.5,
  includeBase64: false,
};

const DrawerScreen = props => {
  const {
    navigation,
  } = props;

  const [resourcePath, setResourcePath] = useState({});
  const [loading, setLoading] = useState(false);

  const [parsedRoleName, setParsedRoleName] = useState('');

  const userName = UserStore.useState(s => s.name);
  const userRole = UserStore.useState(s => s.role);
  const userNumber = UserStore.useState(s => s.number);
  const userEmail = UserStore.useState(s => s.email);

  useEffect(() => {
    if (userRole) {
      switch (userRole) {
        case ROLE_TYPE.ADMIN:
        case ROLE_TYPE.LEGACY:
          setParsedRoleName('Owner');
          break;
        case ROLE_TYPE.STORE_MANAGER:
          setParsedRoleName('Manager');
          break;
        case ROLE_TYPE.FRONTLINER:
          setParsedRoleName('Waiter')
          break;
      }
    }
  }, [userRole]);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={() => { props.navigation.goBack(); }}>
        <View style={{
          marginLeft: 10,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-start',
          marginTop: 10,
          opacity: 0.8,
        }}>
          <Icon
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{
            }}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              marginTop: -3,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -2,
      }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          Profile
        </Text>
      </View>
    ),
    headerRight: () => (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => { props.navigation.navigate('Profile') }}>
          <Image style={{
            width: 32,
            height: 32,
            marginTop: 8,
            marginRight: 25,
          }} source={require('../assets/image/drawer.png')} />
        </TouchableOpacity>
      </View>
    ),
  });

  const selectFile = () => {
    var options = {
      title: 'Select Image',
      customButtons: [
        {
          name: 'customOptionKey',
          title: 'Choose file from Custom Option',
        },
      ],
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };

    ImagePicker.launchImageLibrary(imagePickerOptions, (res) => {
      console.log('Response = ', res);
      if (res.didCancel) {
        //console.log('User cancelled image picker');
      } else {
        let source = res;
        setResourcePath(source);
      }
    });

  };

  const uploadImage = async () => {
    const { uri } = image;
    const fileName = uri.substring(uri.lastIndexOf('/') + 1);
    const uploadUri = Platform.OS === 'ios' ? uri.replace('file://', '') : uri;
    setUploading(true);
    setTransferred(0);
    try {
      await task;
    } catch (e) {
      console.error(e);
    }
    const task = storage()
      .ref(fileName)
      .putFile(uploadUri);
    // set progress state
    task.on('state_changed', snapshot => {
      setTransferred(
        Math.round(snapshot.bytesTransferred / snapshot.totalBytes) * 10000
      );
    });

    setUploading(false);
    Alert.alert(
      'Photo uploaded!',
      'Your photo has been uploaded to our Firebase Cloud Storage'
    );
    setImage(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={{ alignSelf: 'center' }}>
          <View style={styles.container}>

            {/* <Image
              source={{ uri: resourcePath.uri }}
            /> */}


            <TouchableOpacity onPress={selectFile} style={styles.button}>
              <>
                {resourcePath && resourcePath.fileName &&
                  <Image
                    source={{
                      // uri: 'data:image/jpeg;base64,' + resourcePath.data,
                      uri: resourcePath.uri,
                    }}
                    style={{
                      width: 100,
                      height: 100,
                      borderBottomLeftRadius: 64,
                      borderBottomRightRadius: 64,
                      borderTopLeftRadius: 64,
                      borderTopRightRadius: 64,
                      borderWidth: 1,
                      borderColor: '#41444B',
                    }}
                  />
                }
                {!(resourcePath && resourcePath.fileName) &&
                  <Icon
                    name="ios-add"
                    size={37}
                    color="#DFD8C8"
                    style={{ marginLeft: 2 }}></Icon>
                }
              </>
            </TouchableOpacity>

            <View style={styles.infoTitle}>
              <Text
                style={[
                  styles.text,
                  { color: 'black', fontSize: 30, paddingBottom: 5 },
                ]}>
                {userName}
              </Text>
              <Text
                style={[
                  styles.text,
                  { color: 'black', fontSize: 14, paddingBottom: 5 },
                ]}>
                - {parsedRoleName} -
              </Text>
            </View>

            <View style={styles.infoContainer}>
              <Text
                style={[
                  styles.text,
                  { color: '#AEB5BC', fontSize: 14, paddingBottom: 1 },
                ]}>
                Email
              </Text>
              <View style={styles.firstText}>
                <TextInput
                  editable={false}
                  placeholderTextColor="black"
                  placeholder={userEmail}
                  style={styles.textInput_Style}
                  underlineColorAndroid="transparent"
                />
              </View>

              <Text
                style={[
                  styles.text,
                  { color: '#AEB5BC', fontSize: 14, paddingBottom: 1 },
                ]}>
                Contact
              </Text>

              <View style={styles.firstText}>
                <TextInput
                  editable={false}
                  placeholderTextColor="black"
                  placeholder={userNumber}
                  style={styles.textInput_Style}
                  underlineColorAndroid="transparent"
                  keyboardType="phone-pad"
                />
              </View>
              <TouchableOpacity disabled={loading} onPress={() => { ApiClient.logoutUser() }}>
                <View style={[Styles.button, { marginTop: 20, width: 250, paddingVertical: 16, }]}>
                  <Text style={{ color: '#ffffff', fontSize: 18 }}>{loading ? "LOADING..." : "LOGOUT"}</Text>
                </View>
              </TouchableOpacity >

              {/* <View style={[Styles.button, styles.logButton,{ marginTop: 0 ,width:100}]}>
              <Icon
                name="log-out-outline"
                title="Logout"
                color="white"
                onPress={() => ApiClient.logoutUser() }><Text>LOGOUT</Text>
              </Icon>
              </View> */}

              <TouchableOpacity disabled={loading} onPress={async () => {
                await AsyncStorage.setItem('switchMerchant', '1');

                CommonStore.update(s => {
                  s.switchMerchant = true;
                });
              }}>
                <View style={[Styles.button, { marginTop: 0, width: 250, paddingVertical: 16, }]}>
                  <Text style={{ color: '#ffffff', fontSize: 18 }}>{loading ? "LOADING..." : "SWITCH TO MERCHANT"}</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  button: {
    width: 100,
    height: 100,
    backgroundColor: '#41444B',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
    marginBottom: 12,
  },
  buttonText: {
    textAlign: 'center',
    fontSize: 15,
    color: '#fff',
  },
  text: {
    fontFamily: 'NunitoSans-Bold',
    color: '#52575D',
  },
  profileImage: {
    width: 200,
    height: 200,
    borderRadius: 100,
    overflow: 'hidden',
  },
  add: {
    backgroundColor: '#41444B',
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 45,
    height: 45,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoTitle: {
    alignSelf: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  infoContainer: {
    alignSelf: 'center',
    alignItems: 'flex-start',
    marginTop: 16,
  },
  firstText: {
    paddingBottom: 13,
  },
  profileImage: {
    width: 200,
    height: 200,
    borderRadius: 50,
    overflow: 'hidden',
  },
  image: {
    flex: 1,
    height: undefined,
    width: undefined,
  },
  textInput_Style: {
    width: 250,
    height: 40,
    borderColor: '#009688',
    borderWidth: 1,
    backgroundColor: '#fff',
    textAlign: 'center',

  },
  logButton: {
    paddingTop: 20,
  }
});

export default DrawerScreen;

