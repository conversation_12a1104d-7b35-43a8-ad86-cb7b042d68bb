import { Model } from '@nozbe/watermelondb'
import { field, text, writer } from '@nozbe/watermelondb/decorators';

export default class RazerPayoutTransaction extends Model {
  static table = 'RazerPayoutTransaction'

  @field('bankAccountName') bankAccountName
  @field('bankAccountNumber') bankAccountNumber
  @field('bankCode') bankCode
  @field('bankType') bankType
  @field('bqDt') bqDt
  @field('clientEmail') clientEmail
  @field('clientId') clientId
  @field('clientName') clientName
  @field('clientPhone') clientPhone
  @field('companyName') companyName
  @field('contactEmail') contactEmail
  @field('contactMobile') contactMobile
  @field('country') country
  @field('createdAt') createdAt
  @field('deletedAt') deletedAt
  @field('isSettledOnSameDay') isSettledOnSameDay
  @field('merchantId') merchantId
  @field('merchantLogo') merchantLogo
  @field('merchantName') merchantName
  @field('outletCover') outletCover
  @field('outletCycleFunds') outletCycleFunds
  @field('outletCycleKoodooPayoutsActual') outletCycleKoodooPayoutsActual
  @field('outletCycleKoodooPayoutsExpected') outletCycleKoodooPayoutsExpected
  @field('outletCycleMerchantOverdueAmounts') outletCycleMerchantOverdueAmounts
  @field('outletCycleMerchantPayoutsActual') outletCycleMerchantPayoutsActual
  @field('outletCycleMerchantPayoutsExpected') outletCycleMerchantPayoutsExpected
  @field('outletCycleMerchantPendingAmounts') outletCycleMerchantPendingAmounts
  @field('outletCycleMerchantPendingRefundOrdersAmount') outletCycleMerchantPendingRefundOrdersAmount
  @field('outletCycleMerchantRefundOrdersAmount') outletCycleMerchantRefundOrdersAmount
  @field('outletCycleMerchantToReturnByKoodooFeeAmount') outletCycleMerchantToReturnByKoodooFeeAmount
  @field('outletCycleRazerPayouts') outletCycleRazerPayouts
  @field('outletId') outletId
  @field('outletName') outletName
  @field('overdueAmountBackup') overdueAmountBackup
  @field('payeeID') payeeID
  @field('payoutFee') payoutFee
  @field('picFullName') picFullName
  @field('picNRICPassport') picNRICPassport
  @field('prevOverdueAmount') prevOverdueAmount
  @field('prevPendingAmount') prevPendingAmount
  @field('prevStockUpAmount') prevStockUpAmount
  @field('processingRate') processingRate
  @field('razerMerchantMassId') razerMerchantMassId
  @field('razerMerchantReferenceId') razerMerchantReferenceId
  @field('remarks') remarks
  @field('startCreatedAt') startCreatedAt
  @field('stockUpAmount') stockUpAmount
  @field('stockUpAmountBackup') stockUpAmountBackup
  @field('uniqueId') uniqueId
  @field('updatedAt') updatedAt
  @field('userOrdersFigures') userOrdersFigures
  @field('v') v


  // @writer async insertTransactions(transactions) {
  //   await this.batch(
  //     transactions.map(transaction => {
  //       try {
  //         return this.collections
  //           .get(Collections.RazerPayoutTransaction)
  //           .prepareCreate(data => {
  //             // transaction.uniqueId = data.uniqueId;

  //             data = transaction;
  //           });
  //       } catch (e) {
  //         console.log(e);
  //       }
  //     })
  //   )
  // }
};
