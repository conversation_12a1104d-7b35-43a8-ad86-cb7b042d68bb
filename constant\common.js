export const APP_ENV = {
    UAT: 'UAT',
    LIVE: 'LIVE',
};

export const DELAY_LONG_PRESS_TIME = 2000;

export const ACCUMULATOR_ID = {
    GLOBAL: 'a7336454-dc5e-4ab7-9f9a-c9268116a8f1',
};

export const ACCUMULATOR_TYPE = {
    GLOBAL: 'GLOBAL',
};

export const APP_TYPE = {
    MERCHANT: 'MERCHANT',
    WAITER: 'WAITER',
    USER: 'USER',

    WEB_ORDER: 'WEB_ORDER',
};

export const ROLE_TYPE = {
    ADMIN: 'admin',
    LEGACY: 'legacy',
    FRONTLINER: 'frontliner',
    STORE_MANAGER: 'store_manager',
};

export const ROLE_TYPE_PARSED = {
    admin: 'Admin',
    legacy: 'Legacy',
    frontliner: 'Frontliner',
    store_manager: 'Store Manager',
};

export const USER_POINTS_TRANSACTION_TYPE = {
    EARN: 'EARN',
    REDEEM: 'REDEEM',
    EXPIRED: 'EXPIRED',
    INVALID: 'INVALID',
};

export const USER_ORDER_STATUS = {
    ORDER_RECEIVED: 'ORDER_RECEIVED',

    ORDER_AUTHORIZED: 'ORDER_AUTHORIZED',

    ORDER_PREPARING: 'ORDER_PREPARING',
    ORDER_PREPARED: 'ORDER_PREPARED',
    ORDER_DELIVERED: 'ORDER_DELIVERED',
    ORDER_COMPLETED: 'ORDER_COMPLETED',

    ORDER_REJECTED_BY_MERCHANT: 'ORDER_REJECTED_BY_MERCHANT',

    ORDER_CANCELLED_BY_MERCHANT: 'ORDER_CANCELLED_BY_MERCHANT',
    ORDER_CANCELLED_BY_USER: 'ORDER_CANCELLED_BY_USER',

    ORDER_SENDER_REJECTED: 'ORDER_SENDER_REJECTED',
    ORDER_SENDER_CANCELED: 'ORDER_SENDER_CANCELED',
    ORDER_SENDER_EXPIRED: 'ORDER_SENDER_EXPIRED',
};

export const USER_ORDER_STATUS_PARSED = {
    ORDER_RECEIVED: 'Received',
    ORDER_PREPARING: 'Preparing',
    ORDER_PREPARED: 'Prepared',
    ORDER_DELIVERED: 'Delivered',
    ORDER_COMPLETED: 'Completed',

    ORDER_CANCELLED_BY_MERCHANT: 'Cancelled',
    ORDER_CANCELLED_BY_USER: 'Cancelled',
};

export const USER_ORDER_REFUND_STATUS = {
    RECEIVED: 'RECEIVED',
    AUTHORIZED: 'AUTHORIZED',
    PROCESSING: 'PROCESSING',
    COMPLETED: 'COMPLETED',
    REJECTED: 'REJECTED',
    EXPIRED: 'EXPIRED',
};

export const ORDER_TYPE = {
    DINEIN: 'DINEIN',
    DELIVERY: 'DELIVERY',
    PICKUP: 'PICKUP',
    PRE_ORDER: 'PRE_ORDER',
    // TAKEAWAY: 'TAKEAWAY',
};

export const ORDER_TYPE_SUB = {
    NORMAL: 'NORMAL',
    OTHER_DELIVERY: 'OTHER_DELIVERY',
};

export const MERCHANT_TYPE = {
    FOOD_BEVERAGE: 'FOOD_BEVERAGE',
    SERVICES: 'SERVICES',
    RETAIL: 'RETAIL',
};

export const ORDER_TYPE_PARSED = {
    DINEIN: 'Dine In',
    DELIVERY: 'Delivery',
    PICKUP: 'Takeaway',
    PRE_ORDER: 'Pre-Order',
};

export const ORDER_TYPE_DROP_DOWN_LIST = [
    {
        label: 'Dine In',
        value: ORDER_TYPE.DINEIN,
    },
    {
        label: 'Takeaway',
        value: ORDER_TYPE.PICKUP,
    },
    {
        label: 'Delivery',
        value: ORDER_TYPE.DELIVERY,
    },
    {
        label: 'Pre-Order',
        value: ORDER_TYPE.PRE_ORDER,
    },
];

export const CHANNEL_TYPE = {
    DINEIN_QR: 'DINEIN_QR',
    DINEIN_POS: 'DINEIN_POS',

    PICKUP_QR: 'PICKUP_QR',
    PICKUP_POS: 'PICKUP_POS',
};

export const CHANNEL_TYPE_DROP_DOWN_LIST = [
    {
        label: 'Dine In (All)',
        value: ORDER_TYPE.DINEIN,
    },

    {
        label: 'Dine In (QR)',
        value: CHANNEL_TYPE.DINEIN_QR,
    },

    {
        label: 'Dine In (POS)',
        value: CHANNEL_TYPE.DINEIN_POS,
    },

    {
        label: 'Takeaway (All)',
        value: ORDER_TYPE.PICKUP,
    },

    {
        label: 'Takeaway (QR)',
        value: CHANNEL_TYPE.PICKUP_QR,
    },

    {
        label: 'Takeaway (POS)',
        value: CHANNEL_TYPE.PICKUP_POS,
    },

    {
        label: 'Delivery',
        value: ORDER_TYPE.DELIVERY,
    },
    {
        label: 'Pre-Order',
        value: ORDER_TYPE.PRE_ORDER,
    },


];

export const RING_TOP_BAR = {
    RING_SCREEN: 'RING_SCREEN',
}

export const RING_TOP_BAR_PARSED = {
    RING_SCREEN: 'Ring Screen',
}

export const RING_TOP_BAR_SORT = {
    RING_SCREEN: RING_TOP_BAR.RING_SCREEN,
}

export const STOCK_STATUS = {
    ALL: 'ALL',
    BELOW_IDEAL_STOCK: 'BELOW_IDEAL_STOCK',
    BELOW_WARNING_STOCK: 'BELOW_WARNING_STOCK',
};

export const STOCK_STATUS_PARSED = {
    ALL: 'All',
    BELOW_IDEAL_STOCK: 'Below Ideal Stock',
    BELOW_WARNING_STOCK: 'Below Warning Stock',
}

export const USER_RESERVATION_STATUS = {
    ALL: 'ALL',
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    SEATED: 'SEATED',

    NO_SHOW: 'NO_SHOW',
};

export const USER_RESERVATION_STATUS_PARSED = {
    ALL: 'All',
    PENDING: 'Pending',
    ACCEPTED: 'Accepted',
    CANCELED: 'Cancelled',
    SEATED: 'Seated',

    NO_SHOW: 'No Show',
};

export const USER_QUEUE_STATUS = {
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    SEATED: 'SEATED',
    SERVED: 'SERVED',

    NO_SHOW: 'NO_SHOW',
    NOTIFIED: 'NOTIFIED',
};

export const USER_QUEUE_STATUS_PARSED = {
    PENDING: 'Waiting',
    ACCEPTED: 'Accepted',
    CANCELED: 'Cancelled',
    SEATED: 'Seated',
    SERVED: 'Served',

    NO_SHOW: 'No Show',
    NOTIFIED: 'Notified',
};

export const USER_RING_STATUS = {
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    ATTENDED: 'ATTENDED',
};

export const USER_ORDER_PRIORITY = {
    NORMAL: 0,
    HIGH: 1,
};

export const COURIER_CODE = {
    LALAMOVE: 'LALAMOVE',
    MRSPEEDY: 'MRSPEEDY',
    IN_HOUSE: 'IN-HOUSE',
};

export const LALAMOVE_STATUS = {
    ASSIGNING_DRIVER: 'ASSIGNING_DRIVER',
    ON_GOING: 'ON_GOING',
    PICKED_UP: 'PICKED_UP',
    COMPLETED: 'COMPLETED',
    REJECTED: 'REJECTED',
    CANCELED: 'CANCELED',
    EXPIRED: 'EXPIRED',
};

export const LALAMOVE_STATUS_PARSED = {
    ASSIGNING_DRIVER: 'Assigning',
    ON_GOING: 'On the way',
    PICKED_UP: 'Delivering',
    COMPLETED: 'Delivered',
    REJECTED: 'Rejected',
    CANCELED: 'Cancelled',
    EXPIRED: 'Expired',
};

export const MRSPEEDY_STATUS = {
    new: 'new',
    available: 'available',
    active: 'active',
    completed: 'completed',
    reactivated: 'reactivated',
    draft: 'draft',
    canceled: 'canceled',
    delayed: 'delayed',
};

export const MRSPEEDY_STATUS_PARSED = {
    new: 'New',
    available: 'Available',
    active: 'Active',
    completed: 'Completed',
    reactivated: 'Reactivated',
    draft: 'Draft',
    canceled: 'Cancelled',
    delayed: 'Delayed',
};

export const COURIER_INFO_DICT = {
    [COURIER_CODE.LALAMOVE]: {
        name: 'Lalamove',
        img: require('../assets/image/courier-lalamove.png'),
    },
    [COURIER_CODE.MRSPEEDY]: {
        name: 'MrSpeedy',
        img: require('../assets/image/courier-mrspeedy.png'),
    },
}

export const COURIER_DROPDOWN_LIST = [
    {
        label: 'Lalamove',
        value: COURIER_CODE.LALAMOVE,
    },
    {
        label: 'MrSpeedy',
        value: COURIER_CODE.MRSPEEDY,
    },
    // {
    //     label: 'In-House',
    //     value: COURIER_CODE.IN_HOUSE,
    // }
];

export const MERCHANT_VOUCHER_STATUS = {
    DRAFT: 'DRAFT',
    ACTIVE: 'ACTIVE',
};

export const MERCHANT_VOUCHER_STATUS_PARSED = {
    DRAFT: 'Draft',
    ACTIVE: 'Published',
};

export const MERCHANT_VOUCHER_CODE_FORMAT = {
    AUTO_GENERATED: 'AUTO_GENERATED',
    GENERIC: 'GENERIC',
    UNIQUE: 'UNIQUE',
};

export const MERCHANT_VOUCHER_CODE_FORMAT_UNIQUE = {
    ALPHA_NUMERICAL: 'ALPHA_NUMERICAL',
    ALPHA: 'ALPHA',
    NUMERIC: 'NUMERIC',
};

export const MERCHANT_VOUCHER_TYPE = {
    CASH_VOUCHER: 'CASH_VOUCHER',
    PERCENTAGE_VOUCHER: 'PERCENTAGE_VOUCHER',
    BUNDLE_VOUCHER: 'BUNDLE_VOUCHER',
    COMPLIMENTARY_VOUCHER: 'COMPLIMENTARY_VOUCHER',
};

export const MERCHANT_VOUCHER_TYPE_PARSED = {
    CASH_VOUCHER: 'Cash Voucher',
    PERCENTAGE_VOUCHER: 'Percentage Voucher',
    BUNDLE_VOUCHER: 'Bundle Voucher',
    COMPLIMENTARY_VOUCHER: 'Complimentary Voucher',
};

export const SEGMENT_TYPE = {
    NONE: 'NONE',
};

export const PURCHASE_ORDER_STATUS = {
    CREATED: 'CREATED',
    ORDERED: 'ORDERED',
    PARTIALLY_RECEIVED: 'PARTIALLY_RECEIVED',
    ALL_RECEIVED: 'ALL_RECEIVED',

    CANCELLED: 'CANCELLED',

    READY: 'READY', // for central kitchen to mark as ready

    RECEIVED_ACTUAL: 'RECEIVED_ACTUAL',

    COMPLETED: 'COMPLETED',
};

export const PURCHASE_ORDER_STATUS_PARSED = {
    CREATED: 'Created',
    ORDERED: 'Ordered',
    PARTIALLY_RECEIVED: 'Partially Sending',
    ALL_RECEIVED: 'All Sending',

    CANCELLED: 'Cancelled',

    READY: 'Ready', // for central kitchen to mark as ready

    RECEIVED_ACTUAL: 'Received',

    COMPLETED: 'Completed',
};

export const STOCK_TRANSFER_STATUS = {
    CREATED: 'CREATED',
    ORDERED: 'ORDERED',
    PARTIALLY_RECEIVED: 'PARTIALLY_RECEIVED',
    COMPLETED: 'COMPLETED',
};

export const STOCK_TRANSFER_STATUS_PARSED = {
    CREATED: 'Pending',
    ORDERED: 'Ordered',
    PARTIALLY_RECEIVED: 'Partially',
    COMPLETED: 'Completed',
};

export const STOCK_TAKE_STATUS = {
    CREATED: 'CREATED',
    ORDERED: 'ORDERED',
    PARTIALLY_RECEIVED: 'PARTIALLY_RECEIVED',
    APPROVED: 'APPROVED',
    COMPLETED: 'COMPLETED',
};

export const STOCK_TAKE_STATUS_PARSED = {
    CREATED: 'Created',
    ORDERED: 'Ordered',
    PARTIALLY_RECEIVED: 'Partially',
    APPROVED: 'Approved',
    COMPLETED: 'Completed',
};

export const OUTLET_SHIFT_STATUS = {
    OPENED: 'OPENED',
    CLOSED: 'CLOSED',
};

export const USER_ORDER_PAYMENT_OPTIONS = {
    ONLINE: 'ONLINE',
    OFFLINE: 'OFFLINE',
    ALL: 'ALL',
};

export const USER_ORDER_PAYMENT_OPTIONS_PARSED = {
    ONLINE: 'Online Banking',
    OFFLINE: 'Offline',
    ALL: 'All',
};

export const TIMEZONE = {
    KUALA_LUMPUR: 'Asia/Kuala_Lumpur',
};

//////////////////////////////////////////

export const CHART_HOURLY_LABEL_LIST = [
    '00',
    '01',
    '02',
    '03',
    '05',
    '06',
    '07',
    '08',
    '09',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
];

export const WEEK = {
    0: 'monday',
    1: 'tuesday',
    2: 'wednesday',
    3: 'thursday',
    4: 'friday',
    5: 'saturday',
    6: 'sunday',
};

export const NOTIFICATIONS_TYPE = {
    USER_ORDER: 'USER_ORDER',
    USER_RING: 'USER_RING',
    PROMOTION_NOTIFICATION_MANUAL: 'PROMOTION_NOTIFICATION_MANUAL',
    PROMOTION_NOTIFICATION_AUTO: 'PROMOTION_NOTIFICATION_AUTO',
    USER_ORDER_COURIER_ACTION: 'USER_ORDER_COURIER_ACTION',

    MERCHANT_BATCH_UPLOAD_PRODUCTS: 'MERCHANT_BATCH_UPLOAD_PRODUCTS',
    MERCHANT_BATCH_UPLOAD_CRM_USERS: 'MERCHANT_BATCH_UPLOAD_CRM_USERS',

    WAITER_KITCHEN_READY: 'WAITER_KITCHEN_READY',

    MERCHANT_BATCH_UPLOAD_INVENTORIES: 'MERCHANT_BATCH_UPLOAD_INVENTORIES',

    MERCHANT_LOW_STOCK_ALERT: 'MERCHANT_LOW_STOCK_ALERT',
    MERCHANT_RESERVATION_REMINDER: 'MERCHANT_RESERVATION_REMINDER',

    PROMOTION_NOTIFICATION_LOCATION_BASED: 'PROMOTION_NOTIFICATION_LOCATION_BASED',

    MERCHANT_QUEUE_REMINDER: 'MERCHANT_QUEUE_REMINDER',

    USER_RESERVATION_INFORM: 'USER_RESERVATION_INFORM',
    USER_QUEUE_INFORM: 'USER_QUEUE_INFORM',

    MERCHANT_LOW_STOCK_ALERT_PRODUCT: 'MERCHANT_LOW_STOCK_ALERT_PRODUCT',

    GENERAL_NOTIFICATION: 'GENERAL_NOTIFICATION',

    SCHEDULE_ORDER: 'SCHEDULE_ORDER',
};


export const NOTIFICATIONS_ID = {
    USER_ORDER: '1',
    USER_RING: '2',
    PROMOTION_NOTIFICATION_MANUAL: '3',
    PROMOTION_NOTIFICATION_AUTO: '4',
    USER_ORDER_COURIER_ACTION: '5',

    MERCHANT_BATCH_UPLOAD_PRODUCTS: '6',
    MERCHANT_BATCH_UPLOAD_CRM_USERS: '7',

    WAITER_KITCHEN_READY: '8',

    MERCHANT_BATCH_UPLOAD_INVENTORIES: '9',

    MERCHANT_LOW_STOCK_ALERT: '10',

    MERCHANT_RESERVATION_REMINDER: '11',

    PROMOTION_NOTIFICATION_LOCATION_BASED: '12',

    MERCHANT_QUEUE_REMINDER: '13',

    USER_RESERVATION_INFORM: '14',
    USER_QUEUE_INFORM: '15',

    MERCHANT_LOW_STOCK_ALERT_PRODUCT: '16',

    GENERAL_NOTIFICATION: '17',

    SCHEDULE_ORDER: '18',
};

const NOTIFICATIONS_CHANNELS_VERSIONS = 'v1.04'; // v4

export const NOTIFICATIONS_CHANNEL = {
    USER_ORDER: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_ORDER}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    USER_RING: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_RING}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    PROMOTION_NOTIFICATION_MANUAL: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    PROMOTION_NOTIFICATION_AUTO: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    USER_ORDER_COURIER_ACTION: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_ORDER_COURIER_ACTION}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_BATCH_UPLOAD_PRODUCTS: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_PRODUCTS}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    MERCHANT_BATCH_UPLOAD_CRM_USERS: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_CRM_USERS}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    WAITER_KITCHEN_READY: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.WAITER_KITCHEN_READY}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_BATCH_UPLOAD_INVENTORIES: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_INVENTORIES}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_LOW_STOCK_ALERT: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_RESERVATION_REMINDER: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    PROMOTION_NOTIFICATION_LOCATION_BASED: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_LOCATION_BASED}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_QUEUE_REMINDER: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_QUEUE_REMINDER}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    USER_RESERVATION_INFORM: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_RESERVATION_INFORM}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    USER_QUEUE_INFORM: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_QUEUE_INFORM}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    MERCHANT_LOW_STOCK_ALERT_PRODUCT: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    GENERAL_NOTIFICATION: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.GENERAL_NOTIFICATION}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,

    SCHEDULE_ORDER: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.SCHEDULE_ORDER}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
};

export const NOTIFICATIONS_CHANNEL_LIST = [
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_ORDER,
        channelName: 'User Order',
        id: NOTIFICATIONS_ID.USER_ORDER,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_RING,
        channelName: 'User Ring',
        id: NOTIFICATIONS_ID.USER_RING,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_MANUAL,
        channelName: 'Promotion Notification Manual',
        id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_AUTO,
        channelName: 'Promotion Notification Auto',
        id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_ORDER_COURIER_ACTION,
        channelName: 'User Order Courier Action',
        id: NOTIFICATIONS_ID.USER_ORDER_COURIER_ACTION,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_PRODUCTS,
        channelName: 'Merchant Batch Upload Products',
        id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_PRODUCTS,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_CRM_USERS,
        channelName: 'Merchant Batch Upload CRM Users',
        id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_CRM_USERS,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.WAITER_KITCHEN_READY,
        channelName: 'Waiter Kitchen Ready',
        id: NOTIFICATIONS_ID.WAITER_KITCHEN_READY,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_INVENTORIES,
        channelName: 'Merchant Batch Upload Inventories',
        id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_INVENTORIES,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_LOW_STOCK_ALERT,
        channelName: 'Merchant Low Stock Alert',
        id: NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_RESERVATION_REMINDER,
        channelName: 'Merchant Reservation Reminder',
        id: NOTIFICATIONS_ID.MERCHANT_RESERVATION_REMINDER,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_LOCATION_BASED,
        channelName: 'Promotion Notification Location Based',
        id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_LOCATION_BASED,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_QUEUE_REMINDER,
        channelName: 'Merchant Queue Reminder',
        id: NOTIFICATIONS_ID.MERCHANT_QUEUE_REMINDER,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.USER_RESERVATION_INFORM,
        channelName: 'User Reservation Inform',
        id: NOTIFICATIONS_ID.USER_RESERVATION_INFORM,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_QUEUE_INFORM,
        channelName: 'User Queue Inform',
        id: NOTIFICATIONS_ID.USER_QUEUE_INFORM,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.MERCHANT_LOW_STOCK_ALERT_PRODUCT,
        channelName: 'Merchant Low Stock Alert Product',
        id: NOTIFICATIONS_ID.MERCHANT_LOW_STOCK_ALERT_PRODUCT,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.GENERAL_NOTIFICATION,
        channelName: 'General Notification',
        id: NOTIFICATIONS_ID.GENERAL_NOTIFICATION,
    },

    {
        channelId: NOTIFICATIONS_CHANNEL.SCHEDULE_ORDER,
        channelName: 'Schedule Order',
        id: NOTIFICATIONS_ID.SCHEDULE_ORDER,
    },
];


export const USER_SORT_FIELD_TYPE = {
    NAME_ASC: 'NAME_ASC',
    NAME_DESC: 'NAME_DESC',
    NUMBER_ASC: ' NUMBER_ASC',
    NUMBER_DESC: ' NUMBER_DESC',
    GENDER_ASC: 'GENDER_ASC',
    GENDER_DESC: 'GENDER_DESC',
    DOB_ASC: 'DOB_ASC',
    DOB_DESC: 'DOB_DESC',
    RACE_ASC: 'RACE_ASC',
    RACE_DESC: 'RACE_DESC',
    TIER_ASC: 'TIER_ASC',
    TIER_DESC: 'TIER_DESC',
    STATUS_ASC: 'STATUS_ASC',
    STATUS_DESC: 'STATUS_DESC',

    REVIEW_ASC: 'REVIEW_ASC',
    REVIEW_DESC: 'REVIEW_DESC',
    RATING_ASC: 'RATING_ASC',
    RATING_DESC: 'RATING_DESC',
    OUTLET_NAME_ASC: 'OUTLET_NAME_ASC',
    OUTLET_NAME_DESC: 'OUTLET_NAME_DESC',
    CREATED_AT_ASC: 'CREATED_AT_ASC',
    CREATED_AT_DESC: 'CREATED_AT_DESC',

    PAST_SPENT_ASC: 'PAST_SPENT_ASC',
    PAST_SPENT_DESC: 'PAST_SPENT_DESC',
};

export const USER_SORT_FIELD_TYPE_VALUE = {
    NAME_ASC: 'name',
    NAME_DESC: 'name',
    NUMBER_ASC: 'number',
    NUMBER_DESC: 'number',
    GENDER_ASC: 'gender',
    GENDER_DESC: 'gender',
    DOB_ASC: 'dob',
    DOB_DESC: 'dob',
    RACE_ASC: 'race',
    RACE_DESC: 'race',
    TIER_ASC: 'levelName',
    Tier_ASC: 'levelName',
    STATUS_ASC: 'status',
    STATUS_DESC: 'status',

    REVIEW_ASC: 'review',
    REVIEW_DESC: 'review',
    RATING_ASC: 'rating',
    RATING_DESC: 'rating',
    OUTLET_NAME_ASC: 'outletName',
    OUTLET_NAME_DESC: 'outletName',
    CREATED_AT_ASC: 'createdAt',
    CREATED_AT_DESC: 'createdAt',

    PAST_SPENT_ASC: 'pastSpent',
    PAST_SPENT_DESC: 'pastSpent',
};

export const PRODUCT_SORT = {
    PRODUCT_SKU_ASC: 'PRODUCT_SKU_ASC',
    PRODUCT_SKU_DESC: 'PRODUCT_SKU_DESC',
    PRODUCT_NAME_ASC: 'PRODUCT_NAME_ASC',
    PRODUCT_NAME_DESC: 'PRODUCT_NAME_DESC',
    PRODUCT_PRICE_ASC: 'PRODUCT_PRICE_ASC',
    PRODUCT_PRICE_DESC: 'PRODUCT_PRICE_DESC',
    PRODUCT_STOCK_ASC: 'PRODUCT_STOCK_ASC',
    PRODUCT_STOCK_DESC: 'PRODUCT_STOCK_DESC',

    PRODUCT_GROUP_NAME_ASC: 'PRODUCT_GROUP_NAME_ASC',
    PRODUCT_GROUP_NAME_DESC: 'PRODUCT_GROUP_NAME_DESC',
}
export const PRODUCT_SORT_VALUE = {
    PRODUCT_SKU_ASC: 'skuMerchant',
    PRODUCT_SKU_DESC: 'skuMerchant',
    PRODUCT_NAME_ASC: 'name',
    PRODUCT_NAME_DESC: 'name',
    PRODUCT_PRICE_ASC: 'price',
    PRODUCT_PRICE_DESC: 'price',
    PRODUCT_STOCK_ASC: 'stockCount',
    PRODUCT_STOCK_DESC: 'stockCount',

    PRODUCT_GROUP_NAME_ASC: 'groupName',
    PRODUCT_GROUP_NAME_DESC: 'groupName',
}

export const PRODUCT_SORT_COMPARE_OPERATOR = {
    ASC: 'ASC',
    DESC: 'DESC',
}
export const PRODUCT_SORT_FIELD_TYPE_COMPARE = {
    PRODUCT_SKU_ASC: PRODUCT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_SKU_DESC: PRODUCT_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_NAME_ASC: PRODUCT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_NAME_DESC: PRODUCT_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_PRICE_ASC: PRODUCT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_PRICE_DESC: PRODUCT_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_STOCK_ASC: PRODUCT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_STOCK_DESC: PRODUCT_SORT_COMPARE_OPERATOR.DESC,

    PRODUCT_GROUP_NAME_ASC: PRODUCT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_GROUP_NAME_DESC: PRODUCT_SORT_COMPARE_OPERATOR.DESC,
}

export const PAYMENT_SORT_FIELD_TYPE = {
    PAYMENT_NAME_ASC: 'PAYMENT_NAME_ASC',
    PAYMENT_NAME_DESC: 'PAYMENT_NAME_DESC',
    PAYMENT_CREATED_DATE_ASC: 'PAYMENT_CREATED_DATE_ASC',
    PAYMENT_CREATED_DATE_DESC: 'PAYMENT_CREATED_DATE_DESC',
    PAYMENT_UPDATED_DATE_ASC: 'PAYMENT_UPDATED_DATE_ASC',
    PAYMENT_UPDATED_DATE_DESC: 'PAYMENT_UPDATED_DATE_DESC',
    PAYMENT_STATUS_ASC: 'PAYMENT_STATUS_ASC',
    PAYMENT_STATUS_DESC: 'PAYMENT_STATUS_DESC',
    PAYMENT_DEPOSIT_ASC: 'PAYMENT_DEPOSIT_ASC',
    PAYMENT_DEPOSIT_DESC: 'PAYMENT_DEPOSIT_DESC',
}

export const PAYMENT_SORT_FIELD_TYPE_VALUE = {
    PAYMENT_NAME_ASC: 'name',
    PAYMENT_NAME_DESC: 'name',
    PAYMENT_CREATED_DATE_ASC: 'createdAt',
    PAYMENT_CREATED_DATE_DESC: 'createdAt',
    PAYMENT_UPDATED_DATE_ASC: 'updatedAt',
    PAYMENT_UPDATED_DATE_DESC: 'updatedAt',
    PAYMENT_STATUS_ASC: 'isActive',
    PAYMENT_STATUS_DESC: 'isActive',
    PAYMENT_DEPOSIT_ASC: 'isDepo',
    PAYMENT_DEPOSIT_DESC: 'isDepo',
}

export const QUEUE_SORT_FIELD_TYPE = {
    QUEUE_ID_ASC: 'QUEUE_ID_ASC',
    QUEUE_ID_DESC: 'QUEUE_ID_DESC',
    NAME_ASC: 'NAME_ASC',
    NAME_DESC: 'NAME_DESC',
    DATE_TIME_ASC: 'DATE_TIME_ASC',
    DATE_TIME_DESC: 'DATE_TIME_DESC',
    CAPACITY_ASC: 'CAPACITY_ASC',
    CAPACITY_DESC: 'CAPACITY_DESC',
    WAITING_TIME_ASC: 'WAITING_TIME_ASC',
    WAITING_TIME_DESC: 'WAITING_TIME_DESC',
    STATUS_ASC: 'STATUS_ASC',
    STATUS_DESC: 'STATUS_DESC',
}

export const QUEUE_SORT_FIELD_TYPE_VALUE = {
    QUEUE_ID_ASC: 'number',
    QUEUE_ID_DESC: 'number',
    NAME_ASC: 'userName',
    NAME_DESC: 'userName',
    DATE_TIME_ASC: 'createdAt',
    DATE_TIME_DESC: 'createdAt',
    CAPACITY_ASC: 'pax',
    CAPACITY_DESC: 'pax',
    WAITING_TIME_ASC: 'updatedAt',
    WAITING_TIME_DESC: 'updatedAt',
    STATUS_ASC: 'status',
    STATUS_DESC: 'status',
}

export const DINE_IN_SORT_FIELD_TYPE = {
    TABLE_CODE_ASC: 'TABLE_CODE_ASC',
    TABLE_CODE_DESC: 'TABLE_CODE_DESC',
    ORDER_ID_ASC: 'ORDER_ID_ASC',
    ORDER_ID_DESC: 'ORDER_ID_DESC',
    ORDER_DATE_ASC: 'ORDER_DATE_ASC',
    ORDER_DATE_DESC: 'ORDER_DATE_DESC',
    WAITER_NAME_ASC: 'WAITER_NAME_ASC',
    WAITER_NAME_DESC: 'WAITER_NAME_DESC',
    WAITING_TIME_ASC: 'WAITING_TIME_ASC',
    WAITING_TIME_DESC: 'WAITING_TIME_DESC',
    PAYMENT_DETAILS_ASC: 'PAYMENT_DETAILS_ASC',
    PAYMENT_DETAILS_DESC: 'PAYMENT_DETAILS_DESC',
    FINAL_PRICE_ASC: 'FINAL_PRICE_ASC',
    FINAL_PRICE_DESC: 'FINAL_PRICE_DESC',
}

export const DINE_IN_SORT_FIELD_TYPE_VALUE = {
    TABLE_CODE_ASC: 'tableCode',
    TABLE_CODE_DESC: 'tableCode',
    ORDER_ID_ASC: 'orderId',
    ORDER_ID_DESC: 'orderId',
    ORDER_DATE_ASC: 'orderDate',
    ORDER_DATE_DESC: 'orderDate',
    WAITER_NAME_ASC: 'waiterName',
    WAITER_NAME_DESC: 'waiterName',
    WAITING_TIME_ASC: 'updatedAt',
    WAITING_TIME_DESC: 'updatedAt',
    PAYMENT_DETAILS_ASC: 'paymentDetails',
    PAYMENT_DETAILS_DESC: 'paymentDetails',
    FINAL_PRICE_ASC: 'finalPrice',
    FINAL_PRICE_DESC: 'finalPrice',
}

export const SEGMENT_SORT_FIELD_TYPE = {
    SEGMENT_GROUP_ASC: 'SEGMENT_GROUP_ASC',
    SEGMENT_GROUP_DESC: 'SEGMENT_GROUP_DESC'
}

export const SEGMENT_SORT_FIELD_TYPE_VALUE = {
    SEGMENT_GROUP_ASC: 'name',
    SEGMENT_GROUP_DESC: 'name',
}

export const CATALOG_SORT_FIELD_TYPE = {
    CATALOG_NAME_ASC: 'CATALOG_NAME_ASC',
    CATALOG_NAME_DESC: 'CATALOG_NAME_DESC'
}

export const CATALOG_SORT_FIELD_TYPE_VALUE = {
    CATALOG_NAME_ASC: 'catalogName',
    CATALOG_NAME_DESC: 'catalogName',
}

// export const USER_SORT_FIELD_TYPE_COMPARE = {
//     Permanently placed
//     NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
//     NUMBER_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     NUMBER_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
//     GENDER_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     GENDER_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
//     DOB_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     DOB_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
//     RACE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     RACE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
//     STATUS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
//     STATUS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
// }

export const REPORT_SORT_COMPARE_OPERATOR = {
    ASC: 'ASC',
    DESC: 'DESC',
}

export const PAYMENT_SORT_COMPARE_OPERATOR = {
    ASC: 'ASC',
    DESC: 'DESC',
}

export const WOILIST_SORT_COMPARE_OPERATOR = {
    ASC: 'ASC',
    DESC: 'DESC',
}

export const WOLIST_SORT_COMPARE_OPERATOR = {
    ASC: 'ASC',
    DESC: 'DESC',
}

export const WOLIST_SORT_FIELD_TYPE = {
    DOCUMENT_NO_ASC: 'DOCUMENT_NO_ASC',
    DOCUMENT_NO_DESC: 'DOCUMENT_NO_DESC',
    ISSUES_DATE_ASC: 'ISSUES_DATE_ASC',
    ISSUES_DATE_DESC: 'ISSUES_DATE_DESC',
    DESCRIPTION_ASC: 'DESCRIPTION_ASC',
    DESCRIPTION_DESC: 'DESCRIPTION_DESC',
    STATUS_ASC: 'STATUS_ASC',
    STATUS_DESC: 'STATUS_DESC'
};

export const WOILIST_SORT_FIELD_TYPE = {
    SALES_ORDER_NO_ASC: 'SALES_ORDER_NO_ASC',
    SALES_ORDER_NO_DESC: 'SALES_ORDER_NO_DESC',
    PRODUCT_ASC: 'PRODUCT_ASC',
    PRODUCT_DESC: 'PRODUCT_DESC',
    OUTLET_ASC: 'OUTLET_ASC',
    OUTLET_DESC: 'OUTLET_DESC',
    INGREDIENTS_ASC: 'INGREDIENTS_ASC',
    INGREDIENTS_DESC: 'INGREDIENTS_DESC',
    UNIT_ASC: 'UNIT_ASC',
    UNIT_DESC: 'UNIT_DESC',
    STATUS_ASC: 'STATUS_ASC',
    STATUS_DESC: 'STATUS_DESC',
    BALANCE_QTY_ASC: 'BALANCE_QTY_ASC',
    BALANCE_QTY_DESC: 'BALANCE_QTY_DESC',
    USAGE_QTY_ASC: 'USAGE_QTY_ASC',
    USAGE_QTY_DESC: 'USAGE_QTY_DESC'
};

export const WOLIST_SORT_FIELD_TYPE_VALUE = {
    DOCUMENT_NO_ASC: 'woId',
    DOCUMENT_NO_DESC: 'woId',
    ISSUES_DATE_ASC: 'orderDate',
    ISSUES_DATE_DESC: 'orderDate',
    DESCRIPTION_ASC: 'sourceOutletName',
    DESCRIPTION_DESC: 'sourceOutletName',
    STATUS_ASC: 'status',
    STATUS_DESC: 'status',
}

export const WOILIST_SORT_FIELD_TYPE_VALUE = {
    SALES_ORDER_NO_ASC: 'userOrderIdHuman',
    SALES_ORDER_NO_DESC: 'userOrderIdHuman',
    PRODUCT_ASC: 'name',
    PRODUCT_DESC: 'name',
    OUTLET_ASC: 'merchantName',
    OUTLET_DESC: 'merchantName',
    INGREDIENTS_ASC: 'osiName',
    INGREDIENTS_DESC: 'osiName',
    UNIT_ASC: 'osiUnit',
    UNIT_DESC: 'osiUnit',
    STATUS_ASC: 'status',
    STATUS_DESC: 'status',
    BALANCE_QTY_ASC: 'osiQuantity',
    BALANCE_QTY_DESC: 'osiQuantity',
    USAGE_QTY_ASC: 'usage',
    USAGE_QTY_DESC: 'usage',
}

export const WOLIST_SORT_FIELD_TYPE_COMPARE = {
    DOCUMENT_NO_ASC: WOLIST_SORT_COMPARE_OPERATOR.ASC,
    DOCUMENT_NO_DESC: WOLIST_SORT_COMPARE_OPERATOR.DESC,
    ISSUES_DATE_ASC: WOLIST_SORT_COMPARE_OPERATOR.ASC,
    ISSUES_DATE_DESC: WOLIST_SORT_COMPARE_OPERATOR.DESC,
    DESCRIPTION_ASC: WOLIST_SORT_COMPARE_OPERATOR.ASC,
    DESCRIPTION_DESC: WOLIST_SORT_COMPARE_OPERATOR.DESC,
    STATUS_ASC: WOLIST_SORT_COMPARE_OPERATOR.ASC,
    STATUS_DESC: WOLIST_SORT_COMPARE_OPERATOR.DESC
}

export const WOILIST_SORT_FIELD_TYPE_COMPARE = {
    SALES_ORDER_NO_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    SALES_ORDER_NO_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    OUTLET_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    OUTLET_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    INGREDIENTS_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    INGREDIENTS_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    UNIT_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    UNIT_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    STATUS_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    STATUS_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    BALANCE_QTY_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    BALANCE_QTY_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC,
    USAGE_QTY_ASC: WOILIST_SORT_COMPARE_OPERATOR.ASC,
    USAGE_QTY_DESC: WOILIST_SORT_COMPARE_OPERATOR.DESC
}

export const REPORT_SORT_FIELD_TYPE = {
    DATE_TIME_ASC: 'DATE_TIME_ASC',
    DATE_TIME_DESC: 'DATE_TIME_DESC',
    TOTAL_SALES_ASC: 'TOTAL_SALES_ASC',
    TOTAL_SALES_DESC: 'TOTAL_SALES_DESC',
    TOTAL_SALES_RETURN_ASC: 'TOTAL_SALES_RETURN_ASC',
    TOTAL_SALES_RETURN_DESC: 'TOTAL_SALES_RETURN_DESC',
    TOTAL_TRANSACTION_ASC: 'TOTAL_TRANSACTION_ASC',
    TOTAL_TRANSACTION_DESC: 'TOTAL_TRANSACTION_DESC',
    TOTAL_DISCOUNT_ASC: 'TOTAL_DISCOUNT_ASC',
    TOTAL_DISCOUNT_DESC: 'TOTAL_DISCOUNT_DESC',
    DISCOUNT_ASC: 'DISCOUNT_ASC',
    DISCOUNT_DESC: 'DISCOUNT_DESC',
    COST_ASC: 'COST_ASC',
    COST_DESC: 'COST_DESC',
    TAX_ASC: 'TAX_ASC',
    TAX_DESC: 'TAX_DESC',
    SERVICE_CHARGE_ASC: 'SERVICE_CHARGE_ASC',
    SERVICE_CHARGE_DESC: 'SERVICE_CHARGE_DESC',
    GP_ASC: 'GP_ASC',
    GP_DESC: 'GP_ASC',
    NET_SALES_ASC: 'NET_SALES_ASC',
    NET_SALES_DESC: 'NET_SALES_DESC',
    AVERAGE_NET_SALES_ASC: 'AVERAGE_NET_SALES_ASC',
    AVERAGE_NET_SALES_DESC: 'AVERAGE_NET_SALES_DESC',
    CASH_SALES_ASC: 'CASH_SALES_ASC',
    CASH_SALES_DESC: 'CASH_SALES_DESC',
    GROSS_PROFIT_ASC: 'GROSS_PROFIT_ASC',
    GROSS_PROFIT_DESC: 'GROSS_PROFIT_DESC',

    USER_ORDER_ID_ASC: 'USER_ORDER_ID_ASC',
    USER_ORDER_ID_DESC: 'USER_ORDER_ID_DESC',
    USER_ORDER_DATE_TIME_ASC: 'USER_ORDER_DATE_TIME_ASC',
    USER_ORDER_DATE_TIME_DESC: 'USER_ORDER_DATE_TIME_DESC',
    USER_ORDER_SALES_ASC: 'USER_ORDER_SALES_ASC',
    USER_ORDER_SALES_DESC: 'USER_ORDER_SALES_DESC',
    USER_ORDER_TOTAL_DISCOUNT_ASC: 'USER_ORDER_TOTAL_DISCOUNT_ASC',
    USER_ORDER_TOTAL_DISCOUNT_DESC: 'USER_ORDER_TOTAL_DISCOUNT_DESC',
    USER_ORDER_DISCOUNT_ASC: 'USER_ORDER_DISCOUNT_ASC',
    USER_ORDER_DISCOUNT_DESC: 'USER_ORDER_DISCOUNT_DESC',
    USER_ORDER_TAX_ASC: 'USER_ORDER_TAX_ASC',
    USER_ORDER_TAX_DESC: 'USER_ORDER_TAX_DESC',
    USER_ORDER_SERVICE_CHARGE_ASC: 'USER_ORDER_SERVICE_CHARGE_ASC',
    USER_ORDER_SERVICE_CHARGE_DESC: 'USER_ORDER_SERVICE_CHARGE_DESC',
    USER_ORDER_GP_ASC: 'USER_ORDER_GP_ASC',
    USER_ORDER_GP_DESC: 'USER_ORDER_GP_DESC',
    USER_ORDER_SALES_RETURN_ASC: 'USER_ORDER_SALES_RETURN_ASC',
    USER_ORDER_SALES_RETURN_DESC: 'USER_ORDER_SALES_RETURN_DESC',
    USER_ORDER_NET_SALES_ASC: 'USER_ORDER_NET_SALES_ASC',
    USER_ORDER_NET_SALES_DESC: 'USER_ORDER_NET_SALES_DESC',
    USER_ORDER_OUTLET_NAME_ASC: 'USER_ORDER_OUTLET_NAME_ASC',
    USER_ORDER_OUTLET_NAME_DESC: 'USER_ORDER_OUTLET_NAME_DESC',
    USER_ORDER_EMPLOYEE_NAME_ASC: 'USER_ORDER_EMPLOYEE_NAME_ASC',
    USER_ORDER_EMPLOYEE_NAME_DESC: 'USER_ORDER_EMPLOYEE_NAME_DESC',
    USER_ORDER_USER_NAME_ASC: 'USER_ORDER_USER_NAME_ASC',
    USER_ORDER_USER_NAME_DESC: 'USER_ORDER_USER_NAME_DESC',
    USER_ORDER_TYPE_ASC: 'USER_ORDER_TYPE_ASC',
    USER_ORDER_TYPE_DESC: 'USER_ORDER_TYPE_DESC',
    USER_ORDER_COURIER_NAME_ASC: 'USER_ORDER_COURIER_NAME_ASC',
    USER_ORDER_COURIER_NAME_DESC: 'USER_ORDER_COURIER_NAME_DESC',
    USER_ORDER_ORDER_ID_ASC: 'USER_ORDER_ORDER_ID_ASC',
    USER_ORDER_ORDER_ID_DESC: 'USER_ORDER_ORDER_ID_DESC',

    CART_ITEM_NAME_ASC: 'CART_ITEM_NAME_ASC',
    CART_ITEM_NAME_DESC: 'CART_ITEM_NAME_DESC',
    CART_ITEM_ORDER_TYPE_ASC: 'CART_ITEM_ORDER_TYPE_ASC',
    CART_ITEM_ORDER_TYPE_DESC: 'CART_ITEM_ORDER_TYPE_DESC',
    CART_ITEM_REMARKS_ASC: 'CART_ITEM_REMARKS_ASC',
    CART_ITEM_REMARKS_DESC: 'CART_ITEM_REMARKS_DESC',
    CART_ITEM_QUANTITY_ASC: 'CART_ITEM_QUANTITY_ASC',
    CART_ITEM_QUANTITY_DESC: 'CART_ITEM_QUANTITY_DESC',
    CART_ITEM_PRICE_ASC: 'CART_ITEM_PRICE_ASC',
    CART_ITEM_PRICE_DESC: 'CART_ITEM_PRICE_DESC',

    OPEN_DATE_ASC: 'OPEN_DATE_ASC',
    OPEN_DATE_DESC: 'OPEN_DATE_DESC',
    CLOSE_DATE_ASC: 'CLOSE_DATE_ASC',
    CLOSE_DATE_DESC: 'CLOSE_DATE_DESC',

    PRODUCT_CATEGORY_ASC: 'PRODUCT_CATEGORY_ASC',
    PRODUCT_CATEGORY_DESC: 'PRODUCT_CATEGORY_DESC',
    TOTAL_ITEMS_SOLD_ASC: 'TOTAL_ITEMS_SOLD_ASC',
    TOTAL_ITEMS_SOLD_DESC: 'TOTAL_ITEMS_SOLD_DESC',
    AVERAGE_COST_ASC: 'AVERAGE_COST_ASC',
    AVERAGE_COST_DESC: 'AVERAGE_COST_DESC',

    PAYMENT_METHOD_ASC: 'PAYMENT_METHOD_ASC',
    PAYMENT_METHOD_DESC: 'PAYMENT_METHOD_DESC',

    PRODUCT_NAME_ASC: 'PRODUCT_NAME_ASC',
    PRODUCT_NAME_DESC: 'PRODUCT_NAME_DESC',
    // PRODUCT_CATEGORY_ASC: 'PRODUCT_CATEGORY_ASC',
    // PRODUCT_CATEGORY_DESC: 'PRODUCT_CATEGORY_DESC',
    PRODUCT_SKU_ASC: 'PRODUCT_SKU_ASC',
    PRODUCT_SKU_DESC: 'PRODUCT_SKU_DESC',
    TOTAL_ITEMS_ASC: 'TOTAL_ITEMS_ASC',
    TOTAL_ITEMS_DESC: 'TOTAL_ITEMS_DESC',

    PRODUCT_GROUP_NAME_ASC: 'PRODUCT_GROUP_NAME_ASC',
    PRODUCT_GROUP_NAME_DESC: 'PRODUCT_GROUP_NAME_DESC',

    ADD_ON_NAME_ASC: 'ADD_ON_NAME_ASC',
    ADD_ON_NAME_DESC: 'ADD_ON_NAME_DESC',
    ADD_ON_CHOICES_ASC: 'ADD_ON_CHOICES_ASC',
    ADD_ON_CHOICES_DESC: 'ADD_ON_CHOICES_DESC',
    ITEM_NET_SALES_ASC: 'ITEM_NET_SALES_ASC',
    ITEM_NET_SALES_DESC: 'ITEM_NET_SALES_DESC',

    ONLINE_TRANSACTION_ASC: 'ONLINE_TRANSACTION_ASC',
    ONLINE_TRANSACTION_DESC: 'ONLINE_TRANSACTION_DESC',
    OFFLINE_TRANSACTION_ASC: 'OFFLINE_TRANSACTION_ASC',
    OFFLINE_TRANSACTION_DESC: 'OFFLINE_TRANSACTION_DESC',

    ONLINE_SALES_ASC: 'ONLINE_SALES_ASC',
    ONLINE_SALES_DESC: 'ONLINE_SALES_DESC',
    OFFLINE_SALES_ASC: 'OFFLINE_SALES_ASC',
    OFFLINE_SALES_DESC: 'OFFLINE_SALES_DESC',

    OPEN_BALANCE_ASC: 'OPEN_BALANCE_ASC',
    OPEN_BALANCE_DESC: 'OPEN_BALANCE_DESC',
    CLOSE_BALANCE_ASC: 'CLOSE_BALANCE_ASC',
    CLOSE_BALANCE_DESC: 'CLOSE_BALANCE_DESC',


    VOUCHER_NAME_ASC: 'VOUCHER_NAME_ASC',
    VOUCHER_NAME_DESC: 'VOUCHER_NAME_DESC',
    VOUCHER_TYPE_ASC: 'VOUCHER_TYPE_ASC',
    VOUCHER_TYPE_DESC: 'VOUCHER_TYPE_DESC',
    VOUCHER_VIEWED_ASC: 'VOUCHER_VIEWED_ASC',
    VOUCHER_VIEWED_DESC: 'VOUCHER_VIEWED_DESC',
    VOUCHER_REDEMPTION_ASC: 'VOUCHER_REDEMPTION_ASC',
    VOUCHER_REDEMPTION_DESC: 'VOUCHER_REDEMPTION_DESC',
    VOUCHER_USED_ASC: 'VOUCHER_USED_ASC',
    VOUCHER_USED_DESC: 'VOUCHER_USED_DESC',
    VOUCHER_USERNAME_ASC: 'VOUCHER_USERNAME_ASC',
    VOUCHER_USERNAME_DESC: 'VOUCHER_USERNAME_DESC',
    VOUCHER_REDEEMED_DATE_ASC: 'VOUCHER_REDEEMED_DATE_ASC',
    VOUCHER_REDEEMED_DATE_DESC: 'VOUCHER_REDEEMED_DATE_DESC',
    VOUCHER_USED_DATE_ASC: 'VOUCHER_USED_DATE_ASC',
    VOUCHER_USED_DATE_DESC: 'VOUCHER_USED_DATE_DESC',


    PROMOTION_TITLE_ASC: 'PROMOTION_TITLE_ASC',
    PROMOTION_TITLE_DESC: 'PROMOTION_TITLE_DESC',
    PROMOTION_TYPE_ASC: 'PROMOTION_TYPE_ASC',
    PROMOTION_TYPE_DESC: 'PROMOTION_TYPE_DESC',
    PROMOTION_START_DATE_ASC: 'PROMOTION_START_DATE_ASC',
    PROMOTION_START_DATE_DESC: 'PROMOTION_START_DATE_DESC',

    OD_COMM_ASC: 'OD_COMM_ASC',
    OD_COMM_DESC: 'OD_COMM_DESC',
};

export const REPORT_SORT_FIELD_TYPE_VALUE = {
    DATE_TIME_ASC: 'dateTimeRaw',
    DATE_TIME_DESC: 'dateTimeRaw',
    TOTAL_SALES_ASC: 'totalSales',
    TOTAL_SALES_DESC: 'totalSales',
    TOTAL_SALES_RETURN_ASC: 'totalSalesReturn',
    TOTAL_SALES_RETURN_DESC: 'totalSalesReturn',
    TOTAL_TRANSACTION_ASC: 'totalTransactions',
    TOTAL_TRANSACTION_DESC: 'totalTransactions',
    TOTAL_DISCOUNT_ASC: 'totalDiscount',
    TOTAL_DISCOUNT_DESC: 'totalDiscount',
    DISCOUNT_ASC: 'discount',
    DISCOUNT_DESC: 'discount',
    COST_ASC: 'amc',
    COST_DESC: 'amc',
    TAX_ASC: 'tax',
    TAX_DESC: 'tax',
    SERVICE_CHARGE_ASC: 'serviceCharge',
    SERVICE_CHARGE_DESC: 'serviceCharge',
    GP_ASC: 'gp',
    GP_DESC: 'gp',
    NET_SALES_ASC: 'netSales',
    NET_SALES_DESC: 'netSales',
    AVERAGE_NET_SALES_ASC: 'averageNetSales',
    AVERAGE_NET_SALES_DESC: 'averageNetSales',
    CASH_SALES_ASC: 'cashSales',
    CASH_SALES_DESC: 'cashSales',
    GROSS_PROFIT_ASC: 'itemCostPrice',
    GROSS_PROFIT_DESC: 'itemCostPrice',

    USER_ORDER_ID_ASC: 'orderId',
    USER_ORDER_ID_DESC: 'orderId',
    USER_ORDER_DATE_TIME_ASC: 'completedDate',
    USER_ORDER_DATE_TIME_DESC: 'completedDate',
    USER_ORDER_SALES_ASC: 'finalPrice',
    USER_ORDER_SALES_DESC: 'finalPrice',
    USER_ORDER_TOTAL_DISCOUNT_ASC: 'discount',
    USER_ORDER_TOTAL_DISCOUNT_DESC: 'discount',
    USER_ORDER_DISCOUNT_ASC: 'discountPercentage',
    USER_ORDER_DISCOUNT_DESC: 'discountPercentage',
    USER_ORDER_TAX_ASC: 'tax',
    USER_ORDER_TAX_DESC: 'tax',
    USER_ORDER_SERVICE_CHARGE_ASC: 'serviceCharge',
    USER_ORDER_SERVICE_CHARGE_DESC: 'serviceCharge',
    USER_ORDER_GP_ASC: 'gp',
    USER_ORDER_GP_DESC: 'gp',
    USER_ORDER_SALES_RETURN_ASC: 'totalPrice',
    USER_ORDER_SALES_RETURN_DESC: 'totalPrice',
    USER_ORDER_NET_SALES_ASC: 'totalPrice',
    USER_ORDER_NET_SALES_DESC: 'totalPrice',
    USER_ORDER_OUTLET_NAME_ASC: 'outletName',
    USER_ORDER_OUTLET_NAME_DESC: 'outletName',
    USER_ORDER_EMPLOYEE_NAME_ASC: 'waiterName',
    USER_ORDER_EMPLOYEE_NAME_DESC: 'waiterName',
    USER_ORDER_USER_NAME_ASC: 'userName',
    USER_ORDER_USER_NAME_DESC: 'userName',
    USER_ORDER_TYPE_ASC: 'orderType',
    USER_ORDER_TYPE_DESC: 'orderType',
    USER_ORDER_COURIER_NAME_ASC: 'courierCode',
    USER_ORDER_COURIER_NAME_DESC: 'courierCode',
    USER_ORDER_ORDER_ID_ASC: 'orderId',
    USER_ORDER_ORDER_ID_DESC: 'orderId',

    CART_ITEM_NAME_ASC: 'itemName',
    CART_ITEM_NAME_DESC: 'itemName',
    CART_ITEM_ORDER_TYPE_ASC: 'orderType',
    CART_ITEM_ORDER_TYPE_DESC: 'orderType',
    CART_ITEM_REMARKS_ASC: 'remarks',
    CART_ITEM_REMARKS_DESC: 'remarks',
    CART_ITEM_QUANTITY_ASC: 'quantity',
    CART_ITEM_QUANTITY_DESC: 'quantity',
    CART_ITEM_PRICE_ASC: 'price',
    CART_ITEM_PRICE_DESC: 'price',

    OPEN_DATE_ASC: 'openDate',
    OPEN_DATE_DESC: 'openDate',
    CLOSE_DATE_ASC: 'closeDate',
    CLOSE_DATE_DESC: 'closeDate',

    PRODUCT_CATEGORY_ASC: 'productCategory',
    PRODUCT_CATEGORY_DESC: 'productCategory',
    TOTAL_ITEMS_SOLD_ASC: 'totalItemsSold',
    TOTAL_ITEMS_SOLD_DESC: 'totalItemsSold',
    AVERAGE_COST_ASC: 'averageCost',
    AVERAGE_COST_DESC: 'averageCost',

    PAYMENT_METHOD_ASC: 'paymentDetails',
    PAYMENT_METHOD_DESC: 'paymentDetails',

    PRODUCT_NAME_ASC: 'productName',
    PRODUCT_NAME_DESC: 'productName',
    PRODUCT_CATEGORY_ASC: 'productCategory',
    PRODUCT_CATEGORY_DESC: 'productCategory',
    PRODUCT_SKU_ASC: 'productSku',
    PRODUCT_SKU_DESC: 'productSku',
    TOTAL_ITEMS_ASC: 'totalItems',
    TOTAL_ITEMS_DESC: 'totalItems',

    PRODUCT_GROUP_NAME_ASC: 'groupName',
    PRODUCT_GROUP_NAME_DESC: 'groupName',

    ADD_ON_NAME_ASC: 'addOnName',
    ADD_ON_NAME_DESC: 'addOnName',
    ADD_ON_CHOICES_ASC: 'addOnChoices',
    ADD_ON_CHOICES_DESC: 'addOnChoices',
    ITEM_NET_SALES_ASC: 'itemNetSales',
    ITEM_NET_SALES_DESC: 'itemNetSales',

    ONLINE_TRANSACTION_ASC: 'onlineTransaction',
    ONLINE_TRANSACTION_DESC: 'onlineTransaction',
    OFFLINE_TRANSACTION_ASC: 'offlineTransaction',
    OFFLINE_TRANSACTION_DESC: 'offlineTransaction',

    ONLINE_SALES_ASC: 'onlineSales',
    ONLINE_SALES_DESC: 'onlineSales',
    OFFLINE_SALES_ASC: 'offlineSales',
    OFFLINE_SALES_DESC: 'offlineSales',

    OPEN_BALANCE_ASC: 'openAmount',
    OPEN_BALANCE_DESC: 'openAmount',
    CLOSE_BALANCE_ASC: 'closeAmount',
    CLOSE_BALANCE_DESC: 'closeAmount',

    VOUCHER_NAME_ASC: 'voucherName',
    VOUCHER_NAME_DESC: 'voucherName',
    VOUCHER_TYPE_ASC: 'voucherType',
    VOUCHER_TYPE_DESC: 'voucherType',
    VOUCHER_VIEWED_ASC: 'totalViews',
    VOUCHER_VIEWED_DESC: 'totalViews',
    VOUCHER_REDEMPTION_ASC: 'totalRedeems',
    VOUCHER_REDEMPTION_DESC: 'totalRedeems',
    VOUCHER_USED_ASC: 'totalUses',
    VOUCHER_USED_DESC: 'totalUses',
    VOUCHER_USERNAME_ASC: 'userName',
    VOUCHER_USERNAME_DESC: 'userName',
    VOUCHER_REDEEMED_DATE_ASC: 'redeemDate',
    VOUCHER_REDEEMED_DATE_DESC: 'redeemDate',
    VOUCHER_USED_DATE_ASC: 'usedDate',
    VOUCHER_USED_DATE_DESC: 'usedDate',

    PROMOTION_TITLE_ASC: 'promotionName',
    PROMOTION_TITLE_DESC: 'promotionName',
    PROMOTION_TYPE_ASC: 'promotionType',
    PROMOTION_TYPE_DESC: 'promotionType',
    PROMOTION_START_DATE_ASC: 'startDate',
    PROMOTION_START_DATE_DESC: 'startDate',

    OD_COMM_ASC: 'grabComm',
    OD_COMM_DESC: 'grabComm',
};

export const PAYMENT_SORT_FIELD_TYPE_COMPARE = {
    PAYMENT_NAME_ASC: PAYMENT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_NAME_DESC: PAYMENT_SORT_COMPARE_OPERATOR.DESC,
    PAYMENT_CREATED_DATE_ASC: PAYMENT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_CREATED_DATE_DESC: PAYMENT_SORT_COMPARE_OPERATOR.DESC,
    PAYMENT_UPDATED_DATE_ASC: PAYMENT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_UPDATED_DATE_DESC: PAYMENT_SORT_COMPARE_OPERATOR.DESC,
    PAYMENT_STATUS_ASC: PAYMENT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_STATUS_DESC: PAYMENT_SORT_COMPARE_OPERATOR.DESC,
}

export const REPORT_SORT_FIELD_TYPE_COMPARE = {
    DATE_TIME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    DATE_TIME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_SALES_RETURN_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_SALES_RETURN_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_TRANSACTION_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_TRANSACTION_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_DISCOUNT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_DISCOUNT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    DISCOUNT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    DISCOUNT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    COST_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    COST_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TAX_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TAX_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    SERVICE_CHARGE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    SERVICE_CHARGE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    GP_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    GP_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    NET_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    NET_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    AVERAGE_NET_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    AVERAGE_NET_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CASH_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CASH_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    GROSS_PROFIT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    GROSS_PROFIT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    USER_ORDER_ID_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_ID_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_DATE_TIME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_DATE_TIME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_TOTAL_DISCOUNT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_TOTAL_DISCOUNT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_DISCOUNT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_DISCOUNT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_TAX_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_TAX_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_SERVICE_CHARGE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_SERVICE_CHARGE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_GP_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_GP_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_SALES_RETURN_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_SALES_RETURN_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_NET_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_NET_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_OUTLET_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_OUTLET_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_EMPLOYEE_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_EMPLOYEE_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_USER_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_USER_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_TYPE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_TYPE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_COURIER_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_COURIER_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    USER_ORDER_ORDER_ID_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    USER_ORDER_ORDER_ID_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    CART_ITEM_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CART_ITEM_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CART_ITEM_ORDER_TYPE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CART_ITEM_ORDER_TYPE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CART_ITEM_REMARKS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CART_ITEM_REMARKS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CART_ITEM_QUANTITY_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CART_ITEM_QUANTITY_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CART_ITEM_PRICE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CART_ITEM_PRICE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    OPEN_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OPEN_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CLOSE_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CLOSE_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    PRODUCT_CATEGORY_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_CATEGORY_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_ITEMS_SOLD_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_ITEMS_SOLD_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    AVERAGE_COST_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    AVERAGE_COST_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    PAYMENT_METHOD_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_METHOD_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    PRODUCT_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_CATEGORY_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_CATEGORY_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    PRODUCT_SKU_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PRODUCT_SKU_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TOTAL_ITEMS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TOTAL_ITEMS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    ADD_ON_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ADD_ON_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    ADD_ON_CHOICES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ADD_ON_CHOICES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    ITEM_NET_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ITEM_NET_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    ONLINE_TRANSACTION_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ONLINE_TRANSACTION_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    OFFLINE_TRANSACTION_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OFFLINE_TRANSACTION_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    ONLINE_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ONLINE_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    OFFLINE_SALES_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OFFLINE_SALES_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    OPEN_BALANCE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OPEN_BALANCE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CLOSE_BALANCE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CLOSE_BALANCE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    //Temporary placed
    NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    NUMBER_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    NUMBER_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    GENDER_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    GENDER_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    DOB_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    DOB_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    RACE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    RACE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    TIER_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TIER_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    STATUS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    STATUS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    REVIEW_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    REVIEW_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    RATING_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    RATING_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    OUTLET_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OUTLET_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CREATED_AT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CREATED_AT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    QUEUE_ID_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    QUEUE_ID_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    DATE_TIME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    DATE_TIME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    CAPACITY_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    CAPACITY_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    WAITING_TIME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    WAITING_TIME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    STATUS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    STATUS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    SEGMENT_GROUP_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    SEGMENT_GROUP_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    //DINE_IN_SORT
    TABLE_CODE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    TABLE_CODE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    ORDER_ID_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ORDER_ID_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    ORDER_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    ORDER_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    WAITER_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    WAITER_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    WAITING_TIME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    WAITING_TIME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    PAYMENT_DETAILS_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PAYMENT_DETAILS_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    FINAL_PRICE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    FINAL_PRICE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,


    VOUCHER_NAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_NAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_TYPE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_TYPE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_VIEWED_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_VIEWED_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_REDEMPTION_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_REDEMPTION_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_USED_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_USED_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_USERNAME_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_USERNAME_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_REDEEMED_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_REDEEMED_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    VOUCHER_USED_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    VOUCHER_USED_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    PROMOTION_TITLE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PROMOTION_TITLE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    PROMOTION_TYPE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PROMOTION_TYPE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
    PROMOTION_START_DATE_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PROMOTION_START_DATE_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    PAST_SPENT_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    PAST_SPENT_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,

    OD_COMM_ASC: REPORT_SORT_COMPARE_OPERATOR.ASC,
    OD_COMM_DESC: REPORT_SORT_COMPARE_OPERATOR.DESC,
};

// const PAYMENT_CHANNEL_NAME_PARSED = {
//     'FPX-TPA': 'PayNet',
// };

const TABLE_PAGE_SIZE = {
    TEN: 10,
    TWENTY: 20,
    THIRTY: 30,
    FIFTY: 50,
    ALL: 'ALL',
};

export const TABLE_PAGE_SIZE_DROPDOWN_LIST = [
    {
        label: '10',
        value: 10,
    },
    {
        label: '20',
        value: 20,
    },
    {
        label: '30',
        value: 30,
    },
    {
        label: '50',
        value: 50,
    },
    {
        label: '100',
        value: 100,
    },
    // {
    //     label: '500',
    //     value: 500,
    // },
];

export const MODE_ADD_CART = {
    NORMAL: 'NORMAL',
    PAYMENT_SUMMARY_EDIT: 'PAYMENT_SUMMARY_EDIT',
    PAYMENT_SUMMARY_ADD: 'PAYMENT_SUMMARY_ADD',
};

export const OFFLINE_PAYMENT_METHOD_TYPE = {
    CREDIT_CARD: {
        name: 'Credit Card',
        channel: 'Offline-Credit-Card',
    },
    PAYPAL: {
        name: 'PayPal',
        channel: 'Offline-PayPal',
    },
    GRABPAY: {
        name: 'GrabPay',
        channel: 'Offline-GrabPay',
    },
    CASH: {
        name: 'Cash',
        channel: 'Offline-Cash',
    },
    USE_CREDIT: {
        name: 'Use Credit',
        channel: 'Offline-Use-Credit',
    },
    BANK_TRANSFER: {
        name: 'Bank Transfer',
        channel: 'Offline-Bank-Transfer',
    },
    ALIPAY: {
        name: 'Alipay',
        channel: 'Offline-Alipay',
    },
    BOOST: {
        name: 'Boost',
        channel: 'Offline-Boost',
    },
    FAVEPAY: {
        name: 'favePAY',
        channel: 'Offline-favePAY',
    },
    TOUCHNGO_EWALLET: {
        name: 'TouchnGo',
        channel: 'Offline-TouchnGo-eWallet',
    },
    WECHAT_PAY: {
        name: 'WeChat Pay',
        channel: 'Offline-WeChat-Pay',
    },
    AMEX: {
        name: 'Amex',
        channel: 'Offline-Credit-Amex',
    },
    BCARD_POINTS: {
        name: 'BCard',
        channel: 'Offline-Bcard-Points',
    },
    EPAY: {
        name: 'e-pay',
        channel: 'Offline-e-pay',
    },
    MAYBANK_QRPAY: {
        name: 'Maybank',
        channel: 'Offline-Maybank-QRPAY',
    },
    RAZER_CASH: {
        name: 'RAZER CASH',
        channel: 'Offline-RAZER-CASH',
    },
    RAZER_PAY: {
        name: 'RAZER PAY',
        channel: 'Offline-RAZER-PAY',
    },
    WEBCASH: {
        name: 'WEBCASH',
        channel: 'Offline-WEBCASH',
    },
    DEBIT_CARD: {
        name: 'Debit Card',
        channel: 'Offline-Debit-Card',
    },
    VISA: {
        name: 'VISA',
        channel: 'Offline-Credit-VISA',
    },
    MASTERCARD: {
        name: 'MasterCard',
        channel: 'Offline-Credit-MasterCard',
    },
};

export const OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST = [
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.CASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.CASH,
        image: require('../assets/image/coin.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.USE_CREDIT.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.USE_CREDIT,
        image: require('../assets/image/coin.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER,
        image: require('../assets/image/LeftRight.png'),
    },
    // {
    //     name: OFFLINE_PAYMENT_METHOD_TYPE.CREDIT_CARD.name,
    //     value: OFFLINE_PAYMENT_METHOD_TYPE.CREDIT_CARD,
    //     image: require('../assets/image/offline_payment_method_credit_card.png'),
    // },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.DEBIT_CARD.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.DEBIT_CARD,
        image: require('../assets/image/offline_payment_method_visa_mastercard_amex.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.VISA.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.VISA,
        image: require('../assets/image/offline_payment_method_visa.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.MASTERCARD.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.MASTERCARD,
        image: require('../assets/image/offline_payment_method_mastercard.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.AMEX.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.AMEX,
        image: require('../assets/image/offline_payment_method_amex.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.PAYPAL.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.PAYPAL,
        image: require('../assets/image/offline_payment_method_paypal.png'),

    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.GRABPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.GRABPAY,
        image: require('../assets/image/offline_payment_method_grabpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.ALIPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.ALIPAY,
        image: require('../assets/image/offline_payment_method_alipay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BOOST.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BOOST,
        image: require('../assets/image/offline_payment_method_boost.png'),
    },
    // {
    //     name: OFFLINE_PAYMENT_METHOD_TYPE.FAVEPAY.name,
    //     value: OFFLINE_PAYMENT_METHOD_TYPE.FAVEPAY,
    //     image: require('../assets/image/offline_payment_method_fave_pay.png'),
    // },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.TOUCHNGO_EWALLET.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.TOUCHNGO_EWALLET,
        image: require('../assets/image/offline_payment_method_touchngo_ewallet.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.WECHAT_PAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.WECHAT_PAY,
        image: require('../assets/image/offline_payment_method_wechat_pay.png'),
    },

    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BCARD_POINTS.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BCARD_POINTS,
        image: require('../assets/image/offline_payment_method_bcard_points.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.EPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.EPAY,
        image: require('../assets/image/offline_payment_method_epay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY,
        image: require('../assets/image/offline_payment_method_maybank_qrpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_PAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_PAY,
        image: require('../assets/image/offline_payment_method_razerpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_CASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_CASH,
        image: require('../assets/image/offline_payment_method_razer_cash.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.WEBCASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.WEBCASH,
        image: require('../assets/image/offline_payment_method_webcash.png'),
    },
];

export const OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST_LOYALTY = [
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.CASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.CASH,
        image: require('../assets/image/coin.png'),
    },
    // {
    //     name: OFFLINE_PAYMENT_METHOD_TYPE.USE_CREDIT.name,
    //     value: OFFLINE_PAYMENT_METHOD_TYPE.USE_CREDIT,
    //     image: require('../assets/image/coin.png'),
    // },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BANK_TRANSFER,
        image: require('../assets/image/LeftRight.png'),
    },
    // {
    //     name: OFFLINE_PAYMENT_METHOD_TYPE.CREDIT_CARD.name,
    //     value: OFFLINE_PAYMENT_METHOD_TYPE.CREDIT_CARD,
    //     image: require('../assets/image/offline_payment_method_credit_card.png'),
    // },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.DEBIT_CARD.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.DEBIT_CARD,
        image: require('../assets/image/offline_payment_method_visa_mastercard_amex.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.VISA.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.VISA,
        image: require('../assets/image/offline_payment_method_visa.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.MASTERCARD.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.MASTERCARD,
        image: require('../assets/image/offline_payment_method_mastercard.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.AMEX.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.AMEX,
        image: require('../assets/image/offline_payment_method_amex.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.PAYPAL.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.PAYPAL,
        image: require('../assets/image/offline_payment_method_paypal.png'),

    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.GRABPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.GRABPAY,
        image: require('../assets/image/offline_payment_method_grabpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.ALIPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.ALIPAY,
        image: require('../assets/image/offline_payment_method_alipay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BOOST.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BOOST,
        image: require('../assets/image/offline_payment_method_boost.png'),
    },
    // {
    //     name: OFFLINE_PAYMENT_METHOD_TYPE.FAVEPAY.name,
    //     value: OFFLINE_PAYMENT_METHOD_TYPE.FAVEPAY,
    //     image: require('../assets/image/offline_payment_method_fave_pay.png'),
    // },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.TOUCHNGO_EWALLET.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.TOUCHNGO_EWALLET,
        image: require('../assets/image/offline_payment_method_touchngo_ewallet.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.WECHAT_PAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.WECHAT_PAY,
        image: require('../assets/image/offline_payment_method_wechat_pay.png'),
    },

    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.BCARD_POINTS.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.BCARD_POINTS,
        image: require('../assets/image/offline_payment_method_bcard_points.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.EPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.EPAY,
        image: require('../assets/image/offline_payment_method_epay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.MAYBANK_QRPAY,
        image: require('../assets/image/offline_payment_method_maybank_qrpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_PAY.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_PAY,
        image: require('../assets/image/offline_payment_method_razerpay.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_CASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.RAZER_CASH,
        image: require('../assets/image/offline_payment_method_razer_cash.png'),
    },
    {
        name: OFFLINE_PAYMENT_METHOD_TYPE.WEBCASH.name,
        value: OFFLINE_PAYMENT_METHOD_TYPE.WEBCASH,
        image: require('../assets/image/offline_payment_method_webcash.png'),
    },
];

export const OFFLINE_BILL_TYPE = {
    SUMMARY: 'SUMMARY',
    INDIVIDUAL: 'INDIVIDUAL',
    PRODUCT: 'PRODUCT',
};

export const POS_ONLINE_PAYMENT_METHOD = {
    RAZER_TOP: {
        name: 'Contactless',
        channel: 'RAZER_TOP',
        image: require('../assets/image/offline_payment_method_credit_card.png'),
    },
};

export const TICK_LIST_TYPE = {
    UNTICK: 'UNTICK',
    NORMAL: 'NORMAL',
    DELIVERED: 'DELIVERED',
    CANCELLED: 'CANCELLED',
};

export const CART_BEER_DOCKET_ACTION = {
    PURCHASE_DOCKET: 'PURCHASE_DOCKET',
    EXTEND_DOCKET: 'EXTEND_DOCKET',
    REDEEM_DOCKET: 'REDEEM_DOCKET',
};

export const EMAIL_REPORT_TYPE = {
    EXCEL: 'EXCEL',
    CSV: 'CSV',

    PDF: 'PDF',
};

export const PAYMENT_CHANNEL_NAME_PARSED = {
    'CIMB-Clicks': 'CIMB',
    'HLB-ONL': 'HLB',
    'FPX-TPA': 'FPX',
    'FPX_BIMB': 'BIMB',
    'FPX_PBB': 'PBB',
    'RHB-ONL': 'RHB',
    'ALB-Paymex': 'ALB',
    'MB2u': 'MAYBANK',
    'Affin-EPG': 'Affin',
    'Cash-epay': 'epay',
    'Offline-Cash': 'Cash',
    'Offline-Credit-Card': 'Credit Card',
    'Offline-Bank-Transfer': 'Bank Transfer',
    'Offline-Alipay': 'Alipay',
    'Offline-Boost': 'Boost',
    'Offline-favePAY': 'favePAY',
    'Offline-TouchnGo-eWallet': 'TouchnGo eWallet',
    'Offline-WeChat-Pay': 'WeChat Pay',
    'Offline-Credit-Amex': 'Amex',
    'Offline-Bcard-Points': 'Bcard Points',
    'Offline-e-pay': 'e-pay',
    'Offline-Maybank-QRPAY': 'Maybank QRPAY',
    'Offline-RAZER-CASH': 'Razer Cash',
    'Offline-RAZER-PAY': 'RazerPay',
    'Offline-WEBCASH': 'WebCash',
    'Offline-Debit-Card': 'Debit Card',
    'Offline-Credit-VISA': 'VISA',
    'Offline-Credit-MasterCard': 'MasterCard',
    'Offline-Use-Credit': 'Use Credit',
};

export const EXPAND_TAB_TYPE = {
    DASHBOARD: 'DASHBOARD',
    OPERATION: 'OPERATION',
    PRODUCT: 'PRODUCT',
    INVENTORY: 'INVENTORY',
    INVENTORY_COMPOSITE: 'INVENTORY_COMPOSITE',
    DOCKET: 'DOCKET',
    VOUCHER: 'VOUCHER',
    PROMOTION: 'PROMOTION',
    CRM: 'CRM',
    LOYALTY: 'LOYALTY',
    TRANSACTIONS: 'TRANSACTIONS',
    REPORT: 'REPORT',
    EMPLOYEES: 'EMPLOYEES',
    SETTINGS: 'SETTINGS',
    LOGOUT: 'LOGOUT',
    RESERVATIONS: 'RESERVATIONS',

    UPSELLING: 'UPSELLING',

    NONE: 'NONE',
};

export const RESET_DATA_TYPE = {
    USER_ORDER: 'USER_ORDER',
    CRM_USER: 'CRM_USER',
};

export const QUEUE_QR_SALT = 'com mykoodoo merchant queue qr';
export const TABLE_QR_SALT = 'com mykoodoo merchant table qr';
export const ORDER_REGISTER_QR_SALT = 'com mykoodoo saas api order register qr';

export const PRIVILEGES_NAME = {
    // for Modules
    OPERATION: 'OPERATION',
    PRODUCT: 'PRODUCT',
    INVENTORY: 'INVENTORY',
    INVENTORY_COMPOSITE: 'INVENTORY_COMPOSITE',
    DOCKET: 'DOCKET',
    VOUCHER: 'VOUCHER',
    PROMOTION: 'PROMOTION',
    CRM: 'CRM',
    LOYALTY: 'LOYALTY',
    TRANSACTIONS: 'TRANSACTIONS',
    REPORT: 'REPORT',
    EMPLOYEES: 'EMPLOYEES',
    RESERVATIONS: 'RESERVATIONS',
    // for action
    REFUND_ORDER: 'REFUND_ORDER',
    ORDER: 'ORDER',

    SETTINGS: 'SETTINGS',

    QUEUE: 'QUEUE',

    OPEN_CASH_DRAWER: 'OPEN_CASH_DRAWER',

    KDS: 'KDS',

    UPSELLING: 'UPSELLING',

    // for Kitchen

    REJECT_ITEM: 'REJECT_ITEM',
    CANCEL_ORDER: 'CANCEL_ORDER',
    //REFUND_tORDER : 'REFUND_tORDER',

    EXPORT_EINOIVCE: 'EXPORT_EINVOICE',

    MANUAL_DISCOUNT: 'MANUAL_DISCOUNT',
};

export const SCREEN_NAME = {
    //Operation
    Ordering: 'Ordering',
    Order_Dashboard: 'Order_Dashboard',
    Kitchen: 'Kitchen',
    Table: 'Table',
    Dine_In: 'Dine_In',
    Takeaway: 'Takeaway',
    Other_D: 'Other_D',
    Queue: 'Queue',
    History: 'History',

    //Reservation
    Manage_Reservation: 'Manage_Reservation',
    Overview_Reservation: 'Overview_Reservation',
    Deposit_Setting: 'Deposit_Setting',
    Interval_Setting: 'Interval_Setting',
    Analytics: 'Analytics',
    Reservation_Setting: 'Reservation_Setting',

    //Product
    Product_Category: 'Product_Category',
    Variant_AddOn: 'Variant_AddOn',
    Menu_Display: 'Menu_Display',
    Manage_Preorder: 'Manage_Preorder',
    Catalog: 'Catalog',

    //Invetory
    Supplier: 'Supplier',
    Inventory_Overview: 'Inventory_Overview',
    Purchase_Order: 'Purchase_Order',
    Stock_Transfer: 'Stock_Transfer',
    Stock_Take: 'Stock_Take',
    Stock_Refund: 'Stock_Refund',

    //Invetory_composite
    Supplier_Composite: 'Supplier_Composite',
    Inventory_Overview_Composite: 'Inventory_Overview_Composite',
    Purchase_Order_Composite: 'Purchase_Order_Composite',
    Stock_Transfer_Composite: 'Stock_Transfer_Composite',
    Stock_Take_Composite: 'Stock_Take_Composite',

    //Docket
    Active_Docket: 'Active_Docket',
    Expired_Docket: 'Expired_Docket',
    Redeemed_Docket: 'Redeemed_Docket',
    Manage_Docket: 'Manage_Docket',

    //Voucher
    Voucher_List: 'Voucher_List',
    Add_Voucher: 'Add_Voucher',
    Voucher_Report: 'Voucher_Report',

    //Promotion
    Promotion_List: 'Promotion_List',
    Add_Promotion: 'Add_Promotion',
    Promotion_Report: 'Promotion_Report',

    //CRM
    Customer: 'Customer',
    Segment: 'Segment',
    Customer_Review: 'Customer_Review',

    //Loyalty
    Loyalty_Campaign: 'Loyalty_Campaign',
    Sign_Up_Reward: 'Sign_Up_Reward',
    Stamps: 'Stamps',
    Pay_Earn: 'Pay_Earn',
    Reward_Redemption: 'Reward_Redemption',
    Credit_Type: 'Credit_Type',
    Credit_Type_Report: 'Credit_Type_Report',
    Loyalty_Report: 'Loyalty_Report',

    Upselling_List: 'Upselling_List',
    Upselling_Campaign: 'Upselling_Campaign',
    Upselling_Report: 'Upselling_Report',
    Upselling_Revenue_Report: 'Upselling_Revenue_Report',

    //Transaction
    All_Transcation: 'All_Transcation',

    //Report
    Dashboard: 'Dashboard',
    Analysis_Report: 'Analysis_Report',
    Aov_Report: 'Aov_Report',
    Order_Count_Report: 'Order_Count_Report',
    Revisit_Count_Report: 'Revisit_Count_Report',
    Overview_Report: 'Overview_Report',
    Revisit_Report: 'Revisit_Report',
    Upselling_Report: 'Upselling_Report',
    Product_Report: 'Product_Report',
    Category_Report: 'Category_Report',
    Variant_Report: 'Variant_Report',
    Add_On_Report: 'Add_On_Report',
    Channel_Report: 'Channel_Report',
    Payment_Report: 'Payment_Report',
    Shift_Report: 'Shift_Report',
    PayIn_Out_Report: 'PayIn_Out_Report',
    Refund_Report: 'Refund_Report',
    Category_Product_Report: 'Category_Product_Report',

    //Employee
    Manage_Employee: 'Manage_Employee',
    Active_Log: 'Active_Log',
    Employee_Timesheet: 'Employee_Timesheet',

    //Setting
    General_Setting: 'General_Setting',
    Shift_Setting: 'Shift_Setting',
    Receipt_Setting: 'Receipt_Setting',
    Order_Setting: 'Order_Setting',
    Tax_Setting: 'Tax_Setting',
    Printer_Setting: 'Printer_Setting',
    Payment_Setting: 'Payment_Setting',
    Loyalty_Setting: 'Loyalty_Setting',
}

export const SCREEN_NAME_PARSED = {
    //Operation
    Ordering: 'Ordering',
    Order_Dashboard: 'Order Dashboard',
    Kitchen: 'Kitchen',
    Table: 'Table',
    Dine_In: 'Dine In',
    Takeaway: 'Takeaway',
    Other_D: 'Other D',
    Queue: 'Queue',
    History: 'History',

    //Reservation
    Manage_Reservation: 'Manage Reservation',
    Overview_Reservation: 'Overview Reservation',
    Deposit_Setting: 'Deposit Setting',
    Interval_Setting: 'Interval Setting',
    Analytics: 'Analytics',
    Reservation_Setting: 'Reservation Setting',

    //Product
    Product_Category: 'Product Category',
    Variant_AddOn: 'Variant Add-On',
    Menu_Display: 'Menu Display',
    Manage_Preorder: 'Manage Preorder',
    Catalog: 'Catalog',

    //Invetory
    Supplier: 'Supplier',
    Inventory_Overview: 'Inventory Overview',
    Purchase_Order: 'Purchase Order',
    Stock_Transfer: 'Stock Transfer',
    Stock_Take: 'Stock Take',
    Stock_Refund: 'Stock Refund',

    //Invetory_composite
    Supplier_Composite: 'Supplier Composite',
    Inventory_Overview_Composite: 'Inventory Overview Composite',
    Purchase_Order_Composite: 'Puchase Order Composite',
    Stock_Transfer_Composite: 'Stock Transfer Composite',
    Stock_Take_Composite: 'Stock Take Composite',

    //Docket
    Active_Docket: 'Active Docket',
    Expired_Docket: 'Expired Docket',
    Redeemed_Docket: 'Redeemed Docket',
    Manage_Docket: 'Manage Docket',

    //Voucher
    Voucher_List: 'Voucher List',
    Add_Voucher: 'Add Voucher',
    Voucher_Report: 'Voucher Report',

    //Promotion
    Promotion_List: 'Promotion List',
    Add_Promotion: 'Add Promotion',
    Promotion_Report: 'Promotion Report',

    //CRM
    Customer: 'Customer',
    Segment: 'Segment',

    //Loyalty
    Loyalty_Campaign: 'Loyalty Campaign',
    Sign_Up_Reward: 'Sign Up Reward',
    Stamps: 'Stamps',
    Pay_Earn: 'Pay Earn',
    Reward_Redemption: 'Reward Redemption',
    Credit_Type: 'Credit Type',
    Credit_Type_Report: 'Credit Type Report',
    Loyalty_Report: 'Loyalty Report',

    Upselling_List: 'Upselling List',
    Upselling_Campaign: 'Upselling Campaign',
    Upselling_Report: 'Upselling Report',
    Upselling_Revenue_Report: 'Upselling Revenue Report',

    //Transaction
    All_Transcation: 'All Transcation',

    //Report
    Dashboard: 'Dashboard',
    Analysis_Report: 'Analysis Report',
    Aov_Report: 'Aov_Report',
    Order_Count_Report: 'Order Count Report',
    Revisit_Count_Report: 'Revisit Count Report',
    Overview_Report: 'Overview Report',
    Revisit_Report: 'Revisit Report',
    Upselling_Report: 'Upselling Report',
    Product_Report: 'Product Report',
    Category_Report: 'Category Report',
    Variant_Report: 'Variant Report',
    Add_On_Report: 'Add On Report',
    Channel_Report: 'Channel Report',
    Payment_Report: 'Payment Report',
    Shift_Report: 'Shift Report',
    PayIn_Out_Report: 'PayIn Out Report',
    Refund_Report: 'Refund Report',
    Category_Product_Report: 'Category Product Report',

    //Employee
    Manage_Employee: 'Manage Employee',
    Active_Log: 'Active Log',
    Employee_Timesheet: 'Employee Timesheet',

    //Setting
    General_Setting: 'General Setting',
    Shift_Setting: 'Shift Setting',
    Receipt_Setting: 'Receipt Setting',
    Order_Setting: 'Order Setting',
    Tax_Setting: 'Tax Setting',
    Printer_Setting: 'Printer Setting',
    Payment_Setting: 'Payment Setting',
    Loyalty_Setting: 'Loyalty Setting',
}

export const USER_ORDER_ACTION = {
    SPLIT_USER_ORDER_BILL: 'SPLIT_USER_ORDER_BILL',
    DELETE_OUTLET_TABLE: 'DELETE_OUTLET_TABLE',
    CREATE_OUTLET_SECTION: 'CREATE_OUTLET_SECTION',
    DELETE_OUTLET_SECTION: 'DELETE_OUTLET_SECTION',
    TABLE_EXCHANGE: 'TABLE_EXCHANGE',
    CREATE_OUTLET_TABLE: 'CREATE_OUTLET_TABLE',
    UPDATE_OUTLET_TABLE: 'UPDATE_OUTLET_TABLE',
    JOIN_OUTLET_TABLE: 'JOIN_OUTLET_TABLE',
    SPLIT_OUTLET_TABLE: 'SPLIT_OUTLET_TABLE',
    CHECKOUT_OUTLET_TABLE_V2: 'CHECKOUT_OUTLET_TABLE_V2',
    SPLIT_AND_JOIN_USER_ORDER: 'SPLIT_AND_JOIN_USER_ORDER',
    REFUND_USER_ORDER: 'REFUND_USER_ORDER',
    UNDO_REFUND_USER_ORDER: 'UNDO_REFUND_USER_ORDER',

    CANCEL_ORDER_ITEM: 'CANCEL_ORDER_ITEM',
    UNDO_CANCEL_ORDER_ITEM: 'UNDO_CANCEL_ORDER_ITEM',
    DELIVER_ORDER_ITEM: 'DELIVER_ORDER_ITEM',
    UNDO_DELIVER_ORDER_ITEM: 'UNDO_DELIVER_ORDER_ITEM',

    CANCEL_USER_ORDER: 'CANCEL_USER_ORDER',

    SHIFT_PAY_IN: 'SHIFT_PAY_IN',
    SHIFT_PAY_OUT: 'SHIFT_PAY_OUT',

    ADD_PRODUCT: 'ADD_PRODUCT',
    EDIT_PRODUCT: 'EDIT_PRODUCT',
    DELETE_PRODUCT: 'DELETE_PRODUCT',

    ADD_PRODUCT_CATEGORY: 'ADD_PRODUCT_CATEGORY',
    EDIT_PRODUCT_CATEGORY: 'EDIT_PRODUCT_CATEGORY',
    DELETE_PRODUCT_CATEGORY: 'DELETE_PRODUCT_CATEGORY',

    UPDATE_VARIANT_ADDON_VISIBILITY: 'UPDATE_VARIANT_ADDON_VISIBILITY',
};

export const USER_ORDER_ACTION_PARSED = {
    SPLIT_USER_ORDER_BILL: 'Split user order bill',
    DELETE_OUTLET_TABLE: 'Delete outlet table',
    CREATE_OUTLET_SECTION: 'Create outlet section',
    DELETE_OUTLET_SECTION: 'Delete outlet section',
    TABLE_EXCHANGE: 'Switch outlet table',
    CREATE_OUTLET_TABLE: 'Create outlet table',
    UPDATE_OUTLET_TABLE: 'Edit outlet table',
    JOIN_OUTLET_TABLE: 'Join outlet tables',
    SPLIT_OUTLET_TABLE: 'Split outlet table',
    CHECKOUT_OUTLET_TABLE_V2: 'Checkout outlet table',
    SPLIT_AND_JOIN_USER_ORDER: 'Split and join user order',
    REFUND_USER_ORDER: 'Refund user order',
    UNDO_REFUND_USER_ORDER: 'Undo refund user order',

    CANCEL_ORDER_ITEM: 'Reject order item',
    UNDO_CANCEL_ORDER_ITEM: 'Undo reject order item',
    DELIVER_ORDER_ITEM: 'Deliver order item',
    UNDO_DELIVER_ORDER_ITEM: 'Undo deliver order item',

    CANCEL_USER_ORDER: 'Cancel user order',

    SHIFT_PAY_IN: 'Pay in shift',
    SHIFT_PAY_OUT: 'Pay out shift',

    ADD_PRODUCT: 'Add product',
    EDIT_PRODUCT: 'Edit product',
    DELETE_PRODUCT: 'Delete product',

    ADD_PRODUCT_CATEGORY: 'Add product category',
    EDIT_PRODUCT_CATEGORY: 'Edit product category',
    DELETE_PRODUCT_CATEGORY: 'Delete product category',

    UPDATE_VARIANT_ADDON_VISIBILITY: 'Toggle variant/addon on/off',
};

export const RESERVATION_PRIORITY = {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
};

export const RESERVATIONS_SHIFT_TYPE = {
    DOES_NOT_REPEAT: 'DOES_NOT_REPEAT',
    DAILY: 'DAILY',
    WEEKLY: 'WEEKLY',
    MONTHLY: 'MONTHLY', // combined to weekly
    YEARLY: 'YEARLY', // combined to weekly
};

export const REFUND_REASON_LIST = [
    { label: 'Wrong/invalid Items', value: 'Wrong/Invalid Items' },
    { label: 'Out of Stock', value: 'Out of Stock' },
    { label: 'Customer Rejected', value: 'Customer Rejected' },
];

export const SHIFT_PAY_TYPE = {
    'PAY_IN': 'PAY_IN',
    'PAY_OUT': 'PAY_OUT',
};

export const SHIFT_PAY_TYPE_PARSED = {
    'PAY_IN': 'Pay In',
    'PAY_OUT': 'Pay Out',
};

export const CHARGES_TYPE = {
    'AMOUNT_BASED': 'AMOUNT_BASED',
    'PERCENTAGE_BASED': 'PERCENTAGE_BASED',
};

export const CHARGES_TYPE_DROPDOWN_LIST = [
    {
        label: 'Amount',
        value: 'AMOUNT_BASED',
    },
    {
        label: 'Percentage',
        value: 'PERCENTAGE_BASED',
    },
];

export const PRODUCT_PRICE_TYPE = {
    FIXED: 'FIXED',
    VARIABLE: 'VARIABLE',
    UNIT: 'UNIT',
};

export const PRODUCT_PRICE_TYPE_DROPDOWN_LIST = [
    {
        label: 'Fixed',
        value: 'FIXED',
    },
    {
        label: 'Variable',
        value: 'VARIABLE',
    },
    {
        label: 'Unit',
        value: 'UNIT',
    },
];

export const USER_INFO_TO_COLLECTED_TYPE = {
    EMAIL: 'EMAIL',
    BIRTHDAY: 'BIRTHDAY',
};

export const USER_INFO_TO_COLLECTED_TYPE_DROPDOWN_LIST = [
    {
        label: 'Email',
        value: 'EMAIL',
    },
    {
        label: 'Birthday',
        value: 'BIRTHDAY',
    },
];

export const CASH_DRAWER_OPEN_EVENT_TYPE = {
    ALWAYS: 'ALWAYS',
    ASK_FIRST: 'ASK_FIRST',
    DISABLED: 'DISABLED',
};

export const CASH_DRAWER_OPEN_EVENT_TYPE_DROPDOWN_LIST = [
    {
        label: 'Always',
        value: 'ALWAYS',
    },
    {
        label: 'Ask First',
        value: 'ASK_FIRST',
    },
    {
        label: 'Disabled',
        value: 'DISABLED',
    },
];

export const RECEIPT_PRINTING_EVENT_TYPE = {
    ALWAYS: 'ALWAYS',
    ASK_FIRST: 'ASK_FIRST',
    // DISABLED: 'DISABLED',
};

export const RECEIPT_PRINTING_EVENT_TYPE_DROPDOWN_LIST = [
    {
        label: 'Always',
        value: 'ALWAYS',
    },
    {
        label: 'Ask First',
        value: 'ASK_FIRST',
    },
    // {
    //     label: 'Disabled',
    //     value: 'DISABLED',
    // },
];

export const KD_PRINT_EVENT_TYPE = {
    DELIVER: 'DELIVER',
    REJECT: 'REJECT',
    UNDO_DELIVER: 'UNDO_DELIVER',
    UNDO_REJECT: 'UNDO_REJECT',

    CANCEL: 'CANCEL',

    SWITCH_TABLE: 'SWITCH_TABLE',
};

export const KD_PRINT_EVENT_TYPE_DROPDOWN_LIST = [
    {
        label: 'Deliver',
        value: 'DELIVER',
    },
    {
        label: 'Reject',
        value: 'REJECT',
    },
    {
        label: 'Undo Deliver',
        value: 'UNDO_DELIVER',
    },
    {
        label: 'Undo Reject',
        value: 'UNDO_REJECT',
    },
    {
        label: 'Cancel',
        value: 'CANCEL',
    },
    {
        label: 'Switch Table',
        value: 'SWITCH_TABLE',
    },
];

export const KD_PRINT_VARIATION = {
    SUMMARY: 'SUMMARY',
    SUMMARY_CATEGORY: 'SUMMARY_CATEGORY',
    INDIVIDUAL: 'INDIVIDUAL',
};

export const KD_PRINT_VARIATION_DROPDOWN_LIST = [
    {
        label: 'Summary',
        value: 'SUMMARY',
    },
    {
        label: 'Summary (Category)',
        value: 'SUMMARY_CATEGORY',
    },
    {
        label: 'Individual',
        value: 'INDIVIDUAL',
    },
];

export const USER_IDLE_SIGN_OUT_EVENT_TYPE = {
    'NEVER': 'NEVER',
    '30_SEC': '30_SEC',
    '1_MIN': '1_MIN',
    '2_MIN': '2_MIN',
    '5_MIN': '5_MIN',
    '10_MIN': '10_MIN',
    '30_MIN': '30_MIN',
};

export const USER_IDLE_SIGN_OUT_EVENT_TYPE_PARSED = {
    // 'NEVER': 'NEVER',
    '30_SEC': 0.5,
    '1_MIN': 1,
    '2_MIN': 2,
    '5_MIN': 5,
    '10_MIN': 10,
    '30_MIN': 30,
};

export const USER_IDLE_SIGN_OUT_EVENT_TYPE_DROPDOWN_LIST = [
    {
        label: 'Never',
        value: 'NEVER',
    },
    {
        label: '30 seconds',
        value: '30_SEC',
    },
    {
        label: '1 minute',
        value: '1_MIN',
    },
    {
        label: '2 minutes',
        value: '2_MIN',
    },
    {
        label: '5 minutes',
        value: '5_MIN',
    },
    {
        label: '10 minutes',
        value: '10_MIN',
    },
    {
        label: '30 minutes',
        value: '30_MIN',
    },
];

export const KD_FONT_SIZE = {
    SMALL: 'SMALL',
    NORMAL: 'NORMAL',
    LARGE: 'LARGE',
    EXTRA_LARGE: 'EXTRA_LARGE',
};

export const KD_FONT_SIZE_DROPDOWN_LIST = [
    {
        label: 'Small',
        value: 'SMALL',
    },
    {
        label: 'Normal',
        value: 'NORMAL',
    },
    {
        label: 'Large',
        value: 'LARGE',
    },
    {
        label: 'Extra Large',
        value: 'EXTRA_LARGE',
    },
];

export const UNIT_TYPE = {
    'KILOGRAM': 'Kilogram',
    'GRAM': 'Gram',
    'LITRE': 'Litre',
    'MILLILITRE': 'Millilitre',
};

export const UNIT_TYPE_SHORT = {
    'Kilogram': 'kg',
    'Gram': 'g',
    'Litre': 'l',
    'Millilitre': 'ml',
};

export const DEFAULT_CRM_TAG = {
    DINEIN: 'Dine In',
    PICKUP: 'Takeaway',
    DELIVERY: 'Delivery',
    PRE_ORDER: 'Pre-Order',

    MORNING: 'Morning',
    AFTERNOON: 'Afternoon',
    NIGHT: 'Night',
};

export const WEB_ORDER_VARIANT_LAYOUT = {
    HORIZONTAL: 'HORIZONTAL',
    VERTICAL: 'VERTICAL',
};

export const WEB_ORDER_UPSELLING_LAYOUT = {
    NORMAL: 'NORMAL',
    LARGE: 'LARGE',
}

export const WEB_ORDER_LIST_LAYOUT = {
    GRID: 'GRID',
    LIST: 'LIST',
};

export const REPORT_DATA_SIZE_LIMIT = {
    _50: 50,
    _100: 100,
    _200: 200,
    _300: 300,
    _400: 400,
    _500: 500,
    _1000: 1000,
    _1500: 1500,
    _2000: 2000,
};

export const ORDER_TYPE_DETAILS = {
    POS: 'POS',

    QR: 'QR',

    QR_ONLINE: 'QR_ONLINE',
    QR_OFFLINE: 'QR_OFFLINE',
};

export const ORDER_TYPE_DETAILS_DROPDOWN_LIST = [
    {
        label: 'POS',
        value: 'POS',
    },
    {
        label: 'QR',
        value: 'QR',
    },
    // {
    //     label: 'QR Online',
    //     value: 'QR_ONLINE',
    // },
    // {
    //     label: 'QR Offline',
    //     value: 'QR_OFFLINE',
    // },
];

export const BANK_CODE = {
    AIBBMYKL: 'AIBBMYKL',
    ALSRMYK1: 'ALSRMYK1',
    ARBKMYKL: 'ARBKMYKL',
    BIMBMYKL: 'BIMBMYKL',
    BKRMMYK1: 'BKRMMYK1',
    BMMBMYKL: 'BMMBMYKL',
    BNMAMYKL: 'BNMAMYKL',
    BSNAMYK1: 'BSNAMYK1',
    CIBBMYKL: 'CIBBMYKL',
    CITIMYKL: 'CITIMYKL',
    CTBBMYKL: 'CTBBMYKL',
    HLBBMYKL: 'HLBBMYKL',
    HLIBMYKL: 'HLIBMYKL',
    MBBEMYKL: 'MBBEMYKL',
    MBISMYKL: 'MBISMYKL',
    MFBBMYKL: 'MFBBMYKL',
    OCBCMYKL: 'OCBCMYKL',
    PBBEMYKL: 'PBBEMYKL',
    PHBMMYKL: 'PHBMMYKL',
    PIBEMYK1: 'PIBEMYK1',
    RHBAMYKL: 'RHBAMYKL',
    RHBBMYKL: 'RHBBMYKL',
    SCBLMYKX: 'SCBLMYKX',
    UOVBMYKL: 'UOVBMYKL',
};

export const BANK_CODE_DROPDOWN_LIST = [
    {
        label: 'AFFIN BANK BHD ',
        value: 'PHBMMYKL',
    },
    {
        label: 'AFFIN ISLAMIC BANK BHD',
        value: 'AIBBMYKL',
    },
    {
        label: 'ALLIANCE BANK MALAYSIA BHD ',
        value: 'MFBBMYKL',
    },
    {
        label: 'ALLIANCE ISLAMIC BANK BHD',
        value: 'ALSRMYK1',
    },
    {
        label: 'AMBANK BHD',
        value: 'ARBKMYKL',
    },
    {
        label: 'BANK ISLAM BHD ',
        value: 'BIMBMYKL',
    },
    {
        label: 'BANK KERJASAMA RAKYAT ',
        value: 'BKRMMYK1',
    },
    {
        label: 'BANK MUAMALAT (M) BHD',
        value: 'BMMBMYKL',
    },
    {
        label: 'BANK NEGARA MALAYSIA',
        value: 'BNMAMYKL',
    },
    {
        label: 'BANK SIMPANAN NASIONAL',
        value: 'BSNAMYK1',
    },
    {
        label: 'CIMB BANK BHD ',
        value: 'CIBBMYKL',
    },
    {
        label: 'CIMB ISLAMIC BANK BHD',
        value: 'CTBBMYKL',
    },
    {
        label: 'CITIBANK BHD',
        value: 'CITIMYKL',
    },
    {
        label: 'HONG LEONG BANK BHD ',
        value: 'HLBBMYKL',
    },
    {
        label: 'HONG LEONG ISLAMIC BANK BHD',
        value: 'HLIBMYKL',
    },
    {
        label: 'MALAYAN BANKING BHD',
        value: 'MBBEMYKL',
    },
    {
        label: 'MAYBANK ISLAMIC BHD',
        value: 'MBISMYKL',
    },
    {
        label: 'OCBC BANK (M) BHD',
        value: 'OCBCMYKL',
    },
    {
        label: 'PUBLIC BANK BHD',
        value: 'PBBEMYKL',
    },
    {
        label: 'PUBLIC ISLAMIC BANK BHD ',
        value: 'PIBEMYK1',
    },
    {
        label: 'RHB BANK BHD',
        value: 'RHBBMYKL',
    },
    {
        label: 'RHB ISLAMIC BANK BERHAD ',
        value: 'RHBAMYKL',
    },
    {
        label: 'STANDARD CHARTERED BANK BHD',
        value: 'SCBLMYKX',
    },
    {
        label: 'UNITED OVERSEAS BANK (M) BHD ',
        value: 'UOVBMYKL',
    },
];

export const PRINTER_USER_PRIORITY = {
    HIGHEST: 100,
    HIGH: 75,
    NORMAL: 50,
    LOW: 25,
    LOWEST: 1,
};

export const PRINTER_USER_PRIORITY_DROPDOWN_LIST = [
    {
        label: 'Highest',
        value: 100,
    },
    {
        label: 'High',
        value: 75,
    },
    {
        label: 'Normal',
        value: 50,
    },
    {
        label: 'Low',
        value: 25,
    },
    {
        label: 'Lowest',
        value: 1,
    },
];

export const KD_ITEM_STATUS = {
    PENDING: 'PENDING',
    DELIVERED: 'DELIVERED',
    CANCELLED: 'CANCELLED',
};

export const UPSELL_BY_TYPE = {
    CUSTOMER: 'CUSTOMER',
    ORDER_ITEM: 'ORDER_ITEM',
};

export const UPSELL_BY_TYPE_DROPDOWN_LIST = [
    {
        label: 'By Order Item',
        value: 'ORDER_ITEM',
    },
    {
        label: 'By Customer',
        value: 'CUSTOMER',
    },
];

export const KDS_ACTION_TYPE = {
    DELIVER: 'DELIVER',
    REJECT: 'REJECT',
    UNDO_DELIVER: 'UNDO_DELIVER',
    UNDO_REJECT: 'UNDO_REJECT',
};

export const UPSELLING_SECTION = {
    AFTER_CART: 'AFTER_CART',
    AFTER_CHECKOUT: 'AFTER_CHECKOUT',
    AFTER_CHECKOUT_RECOMMENDATION: 'AFTER_CHECKOUT_RECOMMENDATION',
};

export const UPSELLING_SECTION_CODE = {
    AFTER_CART: '1',
    AFTER_CHECKOUT: '2A',
    AFTER_CHECKOUT_RECOMMENDATION: '2B',
    IN_CART: '3',
};

export const UPSELLING_SECTION_DROPDOWN_LIST = [
    {
        label: 'After add to cart',
        value: 'AFTER_CART',
    },
    {
        label: 'After clicked cart',
        value: 'AFTER_CHECKOUT',
    },
    {
        label: 'After clicked cart (dedicated)',
        value: 'AFTER_CHECKOUT_RECOMMENDATION',
    },
];

export const REPORT_DISPLAY_TYPE = {
    'DAY': 'DAY',
    'SHIFT': 'SHIFT',
};

export const DATE_COMPARE_TYPE = {
    'IS_SAME': 'IS_SAME',
    'IS_SAME_OR_AFTER': 'IS_SAME_OR_AFTER',
    'IS_SAME_OR_BEFORE': 'IS_SAME_OR_BEFORE',
    'IS_AFTER': 'IS_AFTER',
    'IS_BEFORE': 'IS_BEFORE',
};

export const CUSTOMER_RATING_WORDING = {
    1: 'Terrible',
    2: 'Bad',
    3: 'Okay',
    4: 'Good',
    5: 'Great!',
};

export const QR_OPERATION_TIME = {
    FOLLOW_SHIFT: 'FOLLOW_SHIFT',
    FOLLOW_OPERATION_HOURS: 'FOLLOW_OPERATION_HOURS',
    FOLLOW_OPERATION_HOURS_AND_SHIFT: 'FOLLOW_OPERATION_HOURS_AND_SHIFT',
};

export const VOUCHER_ACTIVATION_DAYS_DROPDOWN_LIST = [
    {
        label: '0',
        value: 0,
    },
    {
        label: '1',
        value: 1,
    },
];

export const VOUCHER_EXPIRATION_DAYS_DROPDOWN_LIST = [
    {
        label: '7',
        value: 7,
    },
    {
        label: '8',
        value: 8,
    },
    {
        label: '9',
        value: 9,
    },
    {
        label: '10',
        value: 10,
    },
    {
        label: '11',
        value: 11,
    },
    {
        label: '12',
        value: 12,
    },
    {
        label: '13',
        value: 13,
    },
    {
        label: '14',
        value: 14,
    },
    {
        label: '15',
        value: 15,
    },
    {
        label: '16',
        value: 16,
    },
    {
        label: '17',
        value: 17,
    },
    {
        label: '18',
        value: 18,
    },
    {
        label: '19',
        value: 19,
    },
    {
        label: '20',
        value: 20,
    },
    {
        label: '21',
        value: 21,
    },
    {
        label: '22',
        value: 22,
    },
    {
        label: '23',
        value: 23,
    },
    {
        label: '24',
        value: 24,
    },
    {
        label: '25',
        value: 25,
    },
    {
        label: '26',
        value: 26,
    },
    {
        label: '27',
        value: 27,
    },
    {
        label: '28',
        value: 28,
    },
    {
        label: '29',
        value: 29,
    },
    {
        label: '30',
        value: 30,
    },
];

export const LOYALTY_CAMPAIGN_BATCH_TYPE = {
    SEQUENCE: 'SEQUENCE',
    INFINITE: 'INFINITE',
};

export const OTHER_DELIVERY_PARTNER_TYPES = {
    INHOUSE: 'INHOUSE',
    FOOD_PANDA: 'FOOD_PANDA',
    GRAB_FOOD: 'GRAB_FOOD',
    GRAB_FOOD_PICKUP: 'GRAB_FOOD_PICKUP',
    GRAB_MART: 'GRAB_MART',
    PANDA_MART: 'PANDA_MART',
    SHOPEE_FOOD: 'SHOPEE_FOOD',
    MISI: 'MISI',
};

export const OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST = [
    {
        label: 'Inhouse',
        value: OTHER_DELIVERY_PARTNER_TYPES.INHOUSE,
    },
    {
        label: 'Food Panda',
        value: OTHER_DELIVERY_PARTNER_TYPES.FOOD_PANDA,
    },
    {
        label: 'Grab Food',
        value: OTHER_DELIVERY_PARTNER_TYPES.GRAB_FOOD,
    },
    {
        label: 'Grab Food Pickup',
        value: OTHER_DELIVERY_PARTNER_TYPES.GRAB_FOOD_PICKUP,
    },
    {
        label: 'Grab Mart',
        value: OTHER_DELIVERY_PARTNER_TYPES.GRAB_MART,
    },
    {
        label: 'Panda Mart',
        value: OTHER_DELIVERY_PARTNER_TYPES.PANDA_MART,
    },
    {
        label: 'ShopeeFood',
        value: OTHER_DELIVERY_PARTNER_TYPES.SHOPEE_FOOD,
    },
    {
        label: 'Misi',
        value: OTHER_DELIVERY_PARTNER_TYPES.MISI,
    },
];

export const DISCOUNT_SEQUENCE_TYPES = {
    FIRST: 'FIRST',
    LOWEST_PRICE: 'LOWEST_PRICE',
    HIGHEST_PRICE: 'HIGHEST_PRICE',
};

export const DISCOUNT_SEQUENCE_TYPE_DROPDOWN_LIST = [
    {
        label: 'First',
        value: DISCOUNT_SEQUENCE_TYPES.FIRST,
    },
    {
        label: 'Lowest Price',
        value: DISCOUNT_SEQUENCE_TYPES.LOWEST_PRICE,
    },
    {
        label: 'Highest Price',
        value: DISCOUNT_SEQUENCE_TYPES.HIGHEST_PRICE,
    },
];
export const SPECIAL_TAGS = {
    'BEST_SELLERS': 'Best Sellers',
    'RECOMMENDED': 'Recommended',
    'FAVOURITES': 'Favourites',
};

export const SPECIAL_TAGS_DROPDOWN_LIST = [
    {
        label: 'Best Sellers',
        value: SPECIAL_TAGS.BEST_SELLERS,
    },
    {
        label: 'Recommended',
        value: SPECIAL_TAGS.RECOMMENDED,
    },
    {
        label: 'Favourites',
        value: SPECIAL_TAGS.FAVOURITES,
    },
];

export const ORDER_PROCESS_TYPE = {
    REFUND: 'REFUND',
    UNDO_REFUND: 'UNFO_REFUND',
};

export const FIRST_START_KEY = {
    ONE: '1',
    TWO: '2',
};

export const OUTLET_DISPLAY_PAIRING_TYPE = {
    A: 'A',
    B: 'B',
    C: 'C',
    D: 'D',
};

export const OUTLET_DISPLAY_PAIRING_TYPE_DROPDOWN_LIST = [
    {
        label: 'Pair A',
        value: OUTLET_DISPLAY_PAIRING_TYPE.A,
    },
    {
        label: 'Pair B',
        value: OUTLET_DISPLAY_PAIRING_TYPE.B,
    },
    {
        label: 'Pair C',
        value: OUTLET_DISPLAY_PAIRING_TYPE.C,
    },
    {
        label: 'Pair D',
        value: OUTLET_DISPLAY_PAIRING_TYPE.D,
    },
];

export const OUTLET_DISPLAY_PAIRING_DEVICE = {
    HOST: 'HOST',
    DISPLAY: 'DISPLAY',
};

export const OUTLET_DISPLAY_PAIRING_DEVICE_DROPDOWN_LIST = [
    {
        label: 'Host',
        value: OUTLET_DISPLAY_PAIRING_DEVICE.HOST,
    },
    {
        label: 'Display',
        value: OUTLET_DISPLAY_PAIRING_DEVICE.DISPLAY,
    },
];

export const RESERVATION_SCREEN_SECTION = {
    MAIN: 'MAIN',
    SELECT_TABLE: 'SELECT_TABLE',
};

export const WA_LANGUAGE_CODE_DROPDOWN_LIST = [
    {
        label: 'English(US)',
        value: 'en_US',
    },
    {
        label: 'English(UK)',
        value: 'en_GB',
    },
    {
        label: 'Chinese(CN)',
        value: 'zh_CN',
    },
    {
        label: 'Chinese(TW)',
        value: 'zh_TW',
    },
    {
        label: 'Malay',
        value: 'ms',
    },
];
export const COST_USAGE_WASTAGE_LEVEL = {
    CATEGORY: 'CATEGORY',
    PRODUCT: 'PRODUCT',
    ORDER_SPECIFIC: 'ORDER_SPECIFIC',
};
