/*
 FusionCharts JavaScript Library
 Copyright FusionCharts Technologies LLP
 License Information at <http://www.fusioncharts.com/license>
 FusionCharts JavaScript Library
 Copyright FusionCharts Technologies LLP
 License Information at <http://www.fusioncharts.com/license>

 @version 3.4.1
*/
FusionCharts.register("module",["private","modules.renderer.js-charts",function(){function Ca(a){var q={left:a.offsetLeft,top:a.offsetTop};for(a=a.offsetParent;a;)q.left+=a.offsetLeft,q.top+=a.offsetTop,a!==Ia.body&&a!==Ia.documentElement&&(q.left-=a.scrollLeft,q.top-=a.scrollTop),a=a.offsetParent;return q}function ta(a,q){for(var c=[],d=0,l=a.length;d<l;d++)c[d]=q.call(a[d],a[d],d,a);return c}function fa(a,q){var c=q?360:V;a=(a||0)%c;return 0>a?c+a:a}function Ka(a,q){return a<=z?a:q<=z?q:q>a?0:q}
function Ma(a,q,c,d,l){return ea((q-c[1]-d.top)/l,a-c[0]-d.left)}function La(a,q,c,d,l,b,s,ga,e,r){"object"===typeof a&&(q=a.y,c=a.r,d=a.innerR,l=a.radiusYFactor,b=a.depth,s=a.seriesGroup,ga=a.renderer,a=a.x);if(0>l||1<=l)l=.6;a=a||0;q=q||0;c=c||1;d=d||0;b=b||0;this.renderer=ga;this.hasOnePoint=e;this.use3DLighting=r;this.cx=a;this.cy=q;this.rx=c;this.ry=c*l;this.radiusYFactor=l;this.isDoughnut=0<d;this.innerRx=d;this.innerRy=d*l;this.depth=b;this.leftX=a-c;this.rightX=a+c;this.leftInnerX=a-d;this.rightInnerX=
a+d;this.depthY=q+b;this.topY=q-this.ry;this.bottomY=this.depthY+this.ry;this.bottomBorderGroup=ga.group("bottom-border",s).attr({transform:"t0,"+b});this.outerBackGroup=ga.group("outer-back-Side",s);this.slicingWallsBackGroup=ga.group("slicingWalls-back-Side",s);this.innerBackGroup=ga.group("inner-back-Side",s);this.innerFrontGroup=ga.group("inner-front-Side",s);this.slicingWallsFrontGroup=ga.group("slicingWalls-front-Side",s);this.topGroup=ga.group("top-Side",s);this.moveCmdArr=["M"];this.lineCmdArr=
["L"];this.closeCmdArr=["Z"];this.centerPoint=[a,q];this.leftPoint=[this.leftX,q];this.topPoint=[a,this.topY];this.rightPoint=[this.rightX,q];this.bottomPoint=[a,q+this.ry];this.leftDepthPoint=[this.leftX,this.depthY];this.rightDepthPoint=[this.rightX,this.depthY];this.leftInnerPoint=[this.leftInnerX,q];this.rightInnerPoint=[this.rightInnerX,q];this.leftInnerDepthPoint=[this.leftInnerX,this.depthY];this.rightInnerDepthPoint=[this.rightInnerX,this.depthY];this.pointElemStore=[];this.slicingWallsArr=
[];a=["A",this.rx,this.ry,0,0,1,this.rightX,q];c=["A",this.rx,this.ry,0,0,1,this.leftX,q];d=["A",this.rx,this.ry,0,0,0,this.rightX,this.depthY];l=["A",this.rx,this.ry,0,0,0,this.leftX,this.depthY];b=["A",this.innerRx,this.innerRy,0,0,0,this.rightInnerX,q];q=["A",this.innerRx,this.innerRy,0,0,0,this.leftInnerX,q];s=["A",this.innerRx,this.innerRy,0,0,1,this.rightInnerX,this.depthY];ga=["A",this.innerRx,this.innerRy,0,0,1,this.leftInnerX,this.depthY];this.isDoughnut?(this.topBorderPath=this.moveCmdArr.concat(this.leftPoint,
a,c,this.moveCmdArr,this.leftInnerPoint,b,q),this.topPath=this.moveCmdArr.concat(this.leftPoint,a,c,this.lineCmdArr,this.leftInnerPoint,b,q,this.closeCmdArr),this.innerFrontPath=this.moveCmdArr.concat(this.leftInnerPoint,b,this.lineCmdArr,this.rightInnerDepthPoint,ga,this.closeCmdArr),this.innerBackPath=this.moveCmdArr.concat(this.rightInnerPoint,q,this.lineCmdArr,this.leftInnerDepthPoint,s,this.closeCmdArr)):this.topBorderPath=this.topPath=this.moveCmdArr.concat(this.leftPoint,a,c,this.closeCmdArr);
this.outerBackPath=this.moveCmdArr.concat(this.leftPoint,a,this.lineCmdArr,this.rightDepthPoint,l,this.closeCmdArr);this.outerFrontPath=this.moveCmdArr.concat(this.rightPoint,c,this.lineCmdArr,this.leftDepthPoint,d,this.closeCmdArr);this.clipPathforOuter=["M",this.leftX,this.topY,"L",this.rightX,this.topY,this.rightX,this.bottomY,this.leftX,this.bottomY,"Z"];this.clipPathforInner=["M",this.leftInnerX,this.topY,"L",this.rightInnerX,this.topY,this.rightInnerX,this.bottomY,this.leftInnerX,this.bottomY,
"Z"];this.clipPathforNoClip=["M",this.leftInnerX,this.topY,"L",this.leftInnerX,this.bottomY,"Z"];this.colorObjs=[]}var qa=this,y=qa.hcLib,ra=y.Raphael,t=qa.window,Ia=t.document,U=y.BLANKSTRING,Ua=y.createTrendLine,h=y.pluck,za=y.getValidValue,Fa=y.parseTooltext,f=y.pluckNumber,Ga=y.getFirstValue,Va=y.getDefinedColor,ha=y.parseUnsafeString,wa=y.FC_CONFIG_STRING,Aa=y.extend2,Da=y.getDashStyle,G=y.toRaphaelColor,Ra=y.toPrecision,Wa=y.stubFN,ma=y.hasSVG,ya=y.each,Ea=y.TOUCH_THRESHOLD_PIXELS,Na=y.CLICK_THRESHOLD_PIXELS,
xa=y.plotEventHandler,b=y.hasTouch?Ea:Na,e="rgba(192,192,192,"+(y.isIE?.002:1E-6)+")",g=8===t.document.documentMode?"visible":"",k=Math,$=k.sin,Y=k.cos,ea=k.atan2,W=k.round,R=k.min,n=k.max,P=k.abs,L=k.PI,x=k.ceil,u=k.floor,M=k.sqrt,ja=L/180,aa=180/L,z=Math.PI,Oa=z/2,V=2*z,va=z+Oa,da=y.graphics.getColumnColor,ba=y.getFirstColor,ca=y.setLineHeight,Sa=y.pluckFontSize,sa=y.getFirstAlpha,ia=y.graphics.getDarkColor,ka=y.graphics.getLightColor,pa=y.graphics.convertColor,Pa=y.COLOR_TRANSPARENT,Ta=y.POSITION_CENTER,
Za=y.POSITION_TOP,Xa=y.POSITION_BOTTOM,$a=y.POSITION_RIGHT,ab=y.POSITION_LEFT,Ya=y.hashify,m=y.chartAPI,bb=y.graphics.mapSymbolName,t=m.singleseries,Z=y.COMMASTRING,Ba=y.ZEROSTRING,Ja=y.ONESTRING,Ha=y.HUNDREDSTRING,Qa=y.PXSTRING,cb=y.COMMASPACE;m("column2d",{standaloneInit:!0,friendlyName:"Column Chart",creditLabel:!1,rendererId:"cartesian"},m.column2dbase);m("column3d",{friendlyName:"3D Column Chart",defaultSeriesType:"column3d",defaultPlotShadow:1,is3D:!0,fireGroupEvent:!0,defaultZeroPlaneHighlighted:!1},
m.column2d);m("bar2d",{friendlyName:"Bar Chart",isBar:!0,defaultSeriesType:"bar",spaceManager:m.barbase},m.column2d);m("bar3d",{friendlyName:"3D Bar Chart",defaultSeriesType:"bar3d",defaultPlotShadow:1,fireGroupEvent:!0,is3D:!0,defaultZeroPlaneHighlighted:!1},m.bar2d);m("line",{friendlyName:"Line Chart",standaloneInit:!0,creditLabel:!1,rendererId:"cartesian"},m.linebase);m("area2d",{friendlyName:"Area Chart",standaloneInit:!0,creditLabel:!1,rendererId:"cartesian"},m.area2dbase);m("pie2d",{friendlyName:"Pie Chart",
standaloneInit:!0,defaultSeriesType:"pie",defaultPlotShadow:1,reverseLegend:1,alignCaptionWithCanvas:0,sliceOnLegendClick:!0,rendererId:"pie",point:function(a,q,c,d,l){a=l[wa];var b=this.colorManager,s=a.is3d,ga=f(d.plotborderthickness),e=f(ga,s?.1:1),r=f(d.enablemultislicing,1),X=f(d.use3dlighting,1),p=X?f(d.radius3d,d["3dradius"],90):100,C=f(d.showzeropies,1),g=f(d.showpercentintooltip,1),k=f(d.showlabels,1),v=f(d.showvalues,1),m=f(d.showpercentvalues,d.showpercentagevalues,0),w=h(d.tooltipsepchar,
d.hovercapsepchar,cb),db=h(d.labelsepchar,w),K=h(d.plotbordercolor,d.piebordercolor),A=l[wa].numberFormatter,$=c.length,Y=f(d.plotborderdashed,0),J=f(d.plotborderdashlen,5),B=f(d.plotborderdashgap,4),F=f(d.showvalueinlegend,0),H=f(d.showlabelinlegend,1),W=f(d.valuebeforelabelinlegend,0),ua=f(d.showvalueaspercentinlegend,1),I=f(d.reverseplotorder,0),S=h(d.legendsepchar,", "),Q=l.plotOptions.series.dataLabels.style,T=0,n=[],z,N,O,u,E,D,R,x,ea,L,V,t,y,P,G,la,aa,M,ja=-1;la=q.centerLabelConfig={label:ha(h(d.defaultcenterlabel,
"")),font:h(d.centerlabelfont,Q.fontFamily),fontSize:f(d.centerlabelfontsize,parseInt(Q.fontSize,10)),color:ba(h(d.centerlabelcolor,d.valuefontcolor,a.inCanvasStyle.color,"555555")),alpha:f(d.centerlabelalpha,100),bold:f(d.centerlabelbold,Q.fontWeight),italic:f(d.centerlabelitalic,Q.style),bgColor:h(d.centerlabelbgcolor,""),bgAlpha:f(d.centerlabelbgalpha,100),borderColor:h(d.centerlabelbordercolor,Q.borderColor),borderAlpha:f(d.centerlabelborderalpha,100),borderThickness:f(d.centerlabelborderthickness,
Q.borderThickness),borderRadius:f(d.centerlabelborderradius,Q.borderRadius),textPadding:f(d.centerlabeltextpadding,Q.borderPadding),padding:f(d.centerlabelpadding,2),bgOval:f(d.centerlabelbgoval,0),shadow:f(d.showcenterlabelshadow,0),hoverColor:d.centerlabelhovercolor&&ba(h(d.centerlabelhovercolor)),hoverAlpha:f(d.centerlabelhoveralpha),toolText:ha(h(d.centerlabeltooltext,""))};100<p&&(p=100);0>p&&(p=0);f(d.showlegend,0)&&(l.legend.enabled=!0,l.legend.reversed=!Boolean(f(d.reverselegend,0)),q.showInLegend=
!0);for(Q=0;Q<$;Q+=1)O=c[Q],N=A.getCleanValue(O.value,!0),null===N||!C&&0===N||(n.push(O),T+=N);0===T&&(n=[]);q.enableRotation=1<n.length?f(d.enablerotation,1):0;q.alphaAnimation=f(d.alphaanimation,1);q.is3D=s;q.placeLabelsInside=d.placevaluesinside;q.use3DLighting=X;q.pieYScale=f(d.pieyscale,40);1>q.pieYScale&&(q.pieYScale=1);100<=q.pieYScale&&(q.pieYScale=80);q.pieYScale/=100;q.pieSliceDepth=f(d.pieslicedepth,15);1>q.pieSliceDepth&&(q.pieSliceDepth=1);q.managedPieSliceDepth=q.pieSliceDepth;q.enableMultiSlicing=
!!r;s&&d.showplotborder!=Ja&&!ga&&(q.showBorderEffect=1);for(Q=n.length-1;0<=Q;Q-=1){O=n[Q];N=A.getCleanValue(O.value,!0);z=ha(h(O.label,O.name,U));$=h(O.color,b.getPlotColor(Q));E=h(O.alpha,d.plotfillalpha);D=h(O.bordercolor,K);R=h(O.borderalpha,d.plotborderalpha,d.pieborderalpha);s&&(D||void 0!==R)&&(q.showBorderEffect=0);D=h(D,ka($,s?90:25)).split(Z)[0];R=d.showplotborder==Ba?Ba:h(R,E,"80");E=h(E,Ha);C={opacity:Math.max(E,R)/100};if(c=Boolean(f(O.issliced,d.issliced,0)))r||(-1!==ja&&(q.data[n.length-
ja-1].sliced=!1),ja=Q),a.preSliced=c;ga=(P=f(O.dashed,Y))?Da(h(O.dashlen,J),h(O.dashgap,B),e):void 0;u=za(ha(h(O.tooltext,a.tooltext)));L=A.percentValue(N/T*100);V=A.dataLabels(N)||U;ea=1===f(O.showlabel,k)?z:U;x=1===(t=f(O.showvalue,v))?1===m?L:V:U;y=za(ha(O.displayvalue));x=void 0!==y&&t?y:x!==U&&ea!==U?ea+db+x:h(ea,x);void 0!==u?u=Fa(u,[1,2,3,5,6,7,14,24,25],{formattedValue:V,label:z,yaxisName:ha(d.yaxisname),xaxisName:ha(d.xaxisname),percentValue:L,sum:A.dataLabels(T),unformattedSum:T},O,d):(u=
z,t=g?L:V,u=u!=U?u+w+t:t);t=H?z:U;F&&(G=ua?A.legendPercentValue(N/T*100):A.legendValue(N),t=W?G+(t&&S+t):(t&&t+S)+G);P=this.pointHoverOptions(O,q,{plotType:"pie",use3DLighting:X,color:$,alpha:E,borderWidth:e,borderColor:D,borderAlpha:R,borderDashed:P,borderDashGap:h(O.dashgap,B),borderDashLen:f(O.dashlen,J),radius3D:p,shadow:C});z={label:h((aa=O.centerlabel||d.centerlabel)&&this.replaceMacros(aa,["\\$value","\\$percentValue","\\$displayValue","\\$label"],[V,L,void 0===y?"":y,z]),""),font:la.font,
fontSize:f(O.centerlabelfontsize,la.fontSize),color:ba(h(O.centerlabelcolor,la.color)),alpha:f(O.centerlabelalpha,la.alpha),bold:f(O.centerlabelbold,la.bold),italic:f(O.centerlabelitalic,la.italic),bgColor:h(O.centerlabelbgcolor,la.bgColor),bgAlpha:f(O.centerlabelbgalpha,la.bgAlpha),borderColor:h(O.centerlabelbordercolor,la.borderColor),borderAlpha:f(O.centerlabelborderalpha,la.borderAlpha),borderThickness:la.borderThickness,borderRadius:la.borderRadius,textPadding:la.textPadding,padding:la.padding,
bgOval:la.bgOval,shadow:la.shadow,hoverColor:(M=h(O.centerlabelhovercolor,la.hoverColor))&&ba(M),hoverAlpha:f(O.centerlabelhoveralpha,la.hoverAlpha),toolText:h(O.centerlabeltooltext,"")};q.data.push({displayValue:x,categoryLabel:ea,showInLegend:t!==U,y:N,name:t,shadow:C,toolText:u,color:this.getPointColor($,E,p),_3dAlpha:E,borderColor:pa(D,R),borderWidth:e,link:za(O.link),sliced:c,dashStyle:ga,doNotSlice:h(d.enableslicing,Ja)!=Ja,hoverEffects:P.enabled&&P.options,rolloverProperties:P.enabled&&P.rolloverOptions,
centerLabelConfig:z})}I&&(q.reversePlotOrder=!0,q.data&&q.data.reverse());q.valueTotal=T;l.legend.enabled=d.showlegend===Ja?!0:!1;q.startAngle=f(d.startingangle,0);l.chart.startingAngle=h(1<n.length?d.startingangle:0,0);return q},replaceMacros:function(a,q,c){for(var d=q.length||0,l;d--;)l=new RegExp(q[d],"gi"),a=a.replace(l,c[d]);return a},containsMacro:function(a,q){for(var c=q.length||0,d;c--;)if(d=new RegExp(q[c],"gi"),d=a.match(d))return!0;return!1},getPointColor:function(a,q,c){var d,l;a=ba(a);
q=sa(q);100>c&&ma?(d=Math.floor(85*(100-.35*c))/100,d=ia(a,d),l=Math.floor(50*(100+c))/100,a=ka(a,l),q={FCcolor:{color:a+Z+d,alpha:q+Z+q,ratio:c+",100",radialGradient:!0,gradientUnits:"userSpaceOnUse"}}):q={FCcolor:{color:a+Z+a,alpha:q+Z+q,ratio:"0,100"}};return q},configureAxis:function(a,q){var c=0,d=a[wa],l=q.chart,b=a.xAxis.labels.style,s,ga;s=(s=Ga(l.valuebordercolor,U))?pa(s,f(l.valueborderalpha,l.valuealpha,100)):U;b={fontFamily:h(l.valuefont,b.fontFamily),fontSize:h(l.valuefontsize,parseInt(b.fontSize,
10))+Qa,lineHeight:b.lineHeight,color:pa(h(l.valuefontcolor,b.color),f(l.valuefontalpha,l.valuealpha,100)),fontWeight:f(l.valuefontbold)?"bold":"normal",fontStyle:f(l.valuefontitalic)?"italic":"normal",border:s||l.valuebgcolor?f(l.valueborderthickness,1)+"px solid":void 0,borderColor:s,borderThickness:f(l.valueborderthickness,1),borderPadding:f(l.valueborderpadding,2),borderRadius:f(l.valueborderradius,0),backgroundColor:l.valuebgcolor?pa(l.valuebgcolor,f(l.valuebgalpha,l.valuealpha,100)):U,borderDash:f(l.valueborderdashed,
0)?Da(f(l.valueborderdashlen,4),f(l.valueborderdashgap,2),f(l.valueborderthickness,1)):"none"};a.plotOptions.series.dataLabels.style=b;delete d.x;delete d[0];delete d[1];a.chart.plotBorderColor=a.chart.plotBackgroundColor=Pa;d=d.pieDATALabels=[];if(1===a.series.length&&(ga=a.series[0].data)&&0<(c=a.series[0].data.length)&&a.plotOptions.series.dataLabels.enabled)for(;c--;)ga[c]&&void 0!==za(ga[c].displayValue)&&d.push(ga[c].displayValue)},spaceManager:function(a,q,c,d){var l=a[wa],b=l.is3d,s=this.name,
ga=this.colorManager,e=this.smartLabel||l.smartLabel,r=f(l.pieDATALabels&&l.pieDATALabels.length,0),X=0,p=q.chart,C=f(p.managelabeloverflow,0),g=f(p.slicingdistance),k=l.preSliced||p.enableslicing!==Ba||p.showlegend===Ja&&p.interactivelegend!==Ba?P(f(g,20)):0,v=f(p.pieradius,0),m=f(p.enablesmartlabels,p.enablesmartlabel,1),w=m?f(p.skipoverlaplabels,p.skipoverlaplabel,1):0,$=f(p.issmartlineslanted,1),K=r?f(p.labeldistance,p.nametbdistance,5):k,A=f(p.smartlabelclearance,5);c-=a.chart.marginRight+a.chart.marginLeft;
var Y=d-(a.chart.marginTop+a.chart.marginBottom);d=R(Y,c);var W=h(p.smartlinecolor,ga.getColor("plotFillColor")),J=f(p.smartlinealpha,100),B=f(p.smartlinethickness,.7),F=a.plotOptions.series.dataLabels,H=F.style,z=r?f(parseInt(H.lineHeight,10),12):0,ga=a.series[0]||{},ua=ga.pieYScale,I=ga.pieSliceDepth;d=0===v?.15*d:v;var S=0,S=2*d,Q=f("doughnut2d"===s?0:p.placevaluesinside);F.connectorWidth=B;F.connectorPadding=f(p.connectorpadding,5);F.connectorColor=pa(W,J);r&&(m&&(K=A),K+=k);A=S+2*(z+K);Y-=this.titleSpaceManager(a,
q,c,n(A<Y?Y-A:Y/2,parseFloat(a.title.style.lineHeight,10)));p.showlegend===Ja&&(h(p.legendposition,Xa).toLowerCase()!==$a?Y-=this.placeLegendBlockBottom(a,q,c,Y/2,!0):c-=this.placeLegendBlockRight(a,q,c/3,Y,!0));e.setStyle(H);if(1!==r)for(;r--;)q=e.getOriSize(l.pieDATALabels[r]),X=n(X,q.width);0===v&&(b?(Y-=I,S=R(c/2-X-k,(Y/2-z)/ua)-K):S=R(c/2-X-k,Y/2-z)-K,S>=d?d=S:g||(k=K=n(R(K-(d-S),k),10)));b&&(r=Y-2*(d*ua+z),I>r&&(ga.managedPieSliceDepth=I-r));a.plotOptions.pie3d.slicedOffset=a.plotOptions.pie.slicedOffset=
k;a.plotOptions.pie3d.size=a.plotOptions.pie.size=2*d;a.plotOptions.series.dataLabels.distance=K;a.plotOptions.series.dataLabels.isSmartLineSlanted=$;a.plotOptions.series.dataLabels.enableSmartLabels=m;a.plotOptions.series.dataLabels.skipOverlapLabels=w;a.plotOptions.series.dataLabels.manageLabelOverflow=C;a.plotOptions.series.dataLabels.placeLabelsInside=Q;if("doughnut2d"===s||"doughnut3d"===s)if(s=f(p.doughnutradius,0),r=(r=f(p.use3dlighting,1))?f(p.radius3d,p["3dradius"],50):100,100<r&&(r=100),
0>r&&(r=0),p=0===s||s>=d?d/2:s,a.plotOptions.pie3d.innerSize=a.plotOptions.pie.innerSize=2*p,0<r&&ma&&(p=parseInt(p/d*100,10),s=(100-p)/2,r=parseInt(s*r/100,10),p=p+Z+r+Z+2*(s-r)+Z+r,a.series[0]&&a.series[0].data))for(C=a.series[0].data,a=0,r=C.length;a<r;a+=1)s=C[a],s.color.FCcolor&&(s.color.FCcolor.ratio=p,s.rolloverProperties.color&&(s.rolloverProperties.color.FCcolor.ratio=p))},creditLabel:!1,eiMethods:{isPlotItemSliced:function(a){var q=this.jsVars.hcObj,c,d,l;return q&&q.datasets&&q.datasets[0]&&
(c=q.datasets[0].data)&&(l=c.length)&&c[a=l-a-1]&&(d=c[a].plot)&&d.sliced},slicePlotItem:function(a,q){var c=this.jsVars.hcObj,d,l,b,s;return c&&c.datasets&&(d=c.datasets[0])&&(l=d.data)&&(s=l.length)&&l[a=d.reversePlotOrder?a:s-a-1]&&(b=l[a].plot)&&((!!q!==b.sliced||void 0===q)&&c.plotGraphicClick.call(b)||b.sliced)},centerLabel:function(a,q){var c=this.jsVars.hcObj,d=c.options,l=d.series[0],d=d.plotOptions.pie.innerSize,b=c.canvasLeft+.5*c.canvasWidth,s=c.canvasTop+.5*c.canvasHeight,e=l.centerLabelConfig,
f;if("object"!==typeof q)q=e;else for(f in e)void 0===q[f]&&(q[f]=e[f]);q.label=a;l.centerLabelConfig=q;d&&c.drawDoughnutCenterLabel(a||"",b,s,d,d,q,!0)},startingAngle:function(a,q){var c=this.jsVars.hcObj,d=c.datasets[0].plot,l="pie"===c.options.chart.defaultSeriesType,b,s=(b=c.datasets[0].startAngle)*(l?-aa:1)+(0>(l?-1:1)*b?360:0);if(!isNaN(a)){if(d.singletonCase||d.isRotating)return;a+=q?s:0;l?((l=c.options.series[0]).startAngle=-a*ja,c.rotate(d,l)):c.rotate(a);s=a}return W(100*((s%=360)+(0>s?
360:0)))/100}}},t);m.pie2d.eiMethods.togglePieSlice=m.pie2d.eiMethods.sliceDataItem=m.pie2d.eiMethods.slicePlotItem;m.pie2d.eiMethods.enableSlicingMovement=m.pie2d.eiMethods.enablelink=function(){qa.raiseWarning(this,"1301081430","run","JSRenderer~enablelink()","Method deprecated.")};m("pie3d",{friendlyName:"3D Pie Chart",defaultSeriesType:"pie3d",rendererId:"pie3d",creditLabel:!1,fireGroupEvent:!0,getPointColor:function(a){return a},defaultPlotShadow:0},m.pie2d);m("doughnut2d",{friendlyName:"Doughnut Chart",
getPointColor:function(a,q,c){var d;a=ba(a);q=sa(q);100>c&&ma?(d=ia(a,u(100*(85-.2*(100-c)))/100),a=ka(a,u(100*(100-.5*c))/100),q={FCcolor:{color:d+","+a+","+a+","+d,alpha:q+","+q+","+q+","+q,radialGradient:!0,gradientUnits:"userSpaceOnUse",r:c}}):q={FCcolor:{color:a+","+a,alpha:q+","+q,ratio:"0,100"}};return q}},m.pie2d);m("doughnut3d",{friendlyName:"3D Doughnut Chart",defaultSeriesType:"pie3d",rendererId:"pie3d",fireGroupEvent:!0,getPointColor:m.pie3d,defaultPlotShadow:0},m.doughnut2d);m("pareto2d",
{standaloneInit:!0,friendlyName:"Pareto Chart",point:function(a,q,c,d,l){a=c.length;var b=0,s=0,e={},g=this.colorManager,r=/3d$/.test(l.chart.defaultSeriesType),X=this.isBar,p=h(360-d.plotfillangle,90),C=h(d.showplotborder,r?Ba:Ja)===Ja?r?1:f(d.plotborderthickness,1):0,k=l.chart.useRoundEdges,Y=h(d.tooltipsepchar,", "),v=h(d.plotbordercolor,g.getColor("plotBorderColor")).split(Z)[0],m=d.showplotborder==Ba?Ba:h(d.plotborderalpha,d.plotfillalpha,Ha),w=l.xAxis,$=f(d.showcumulativeline,1),K=l[wa],A=K.axisGridManager,
n=K.x,z=d.showtooltip!=Ba,J=[],B=f(d.use3dlighting,1),F=l[wa].numberFormatter,H=f(d.showlinevalues,d.showvalues),W=f(d.plotborderdashed,0),ua,I=f(d.plotborderdashlen,5),S=f(d.plotborderdashgap,4),Q=ha(d.xaxisname),T=ha(d.yaxisname),u=K.numberFormatter,t=q,N,O,R,E,D,x,ea,L,V,y,P,G,aa,M,la,ja,oa,ia,na,va,ca,ka,fa,m=r?d.showplotborder?m:Ba:m,v=r?h(d.plotbordercolor,"#FFFFFF"):v;R=f(d.useplotgradientcolor,1)?Va(d.plotgradientcolor,g.getColor("plotGradientColor")):U;for(la=O=0;O<a;O+=1)na=c[O],c[O].vline?
A.addVline(w,na,la,l):(N=F.getCleanValue(na.value,!0),null!==N&&(s+=na.value=N,J.push(na),la+=1));a=J.length;J.sort(function(a,c){return c.value-a.value});$&&0<s?(y=f(d.linedashed,0),va=ba(h(d.linecolor,g.getColor("plotBorderColor"))),e=h(d.linealpha,100),P=f(d.linedashlen,5),E=f(d.linedashgap,4),O=f(d.linethickness,2),aa={opacity:e/100},M=f(d.drawanchors,d.showanchors),void 0===M&&(M=e!=Ba),oa=f(d.anchorborderthickness,1),ia=f(d.anchorsides,0),ua=f(d.anchorradius,3),ja=ba(h(d.anchorbordercolor,va)),
N=ba(h(d.anchorbgcolor,g.getColor("anchorBgColor"))),la=sa(h(d.anchoralpha,Ha)),c=sa(h(d.anchorbgalpha,la))*la/100,y=y?Da(P,E,O):void 0,E=Boolean(f(na.anchorshadow,d.anchorshadow,0)),P=this.pointHoverOptions(na,q,{plotType:"anchor",anchorBgColor:N,anchorAlpha:la,anchorBgAlpha:c,anchorAngle:h(d.anchorstartangle,90),anchorBorderThickness:oa,anchorBorderColor:ja,anchorBorderAlpha:la,anchorSides:ia,anchorRadius:ua,shadow:G}),e={yAxis:1,data:[],type:"line",color:{FCcolor:{color:va,alpha:e}},lineWidth:O,
marker:{enabled:M,shadow:E&&1<=ua?{opacity:la/100}:!1,fillColor:{FCcolor:{color:N,alpha:c}},lineColor:{FCcolor:{color:ja,alpha:la}},lineWidth:oa,radius:ua,symbol:bb(ia),startAngle:h(d.anchorstartangle,90)}},t=[t,e],K[1]||(K[1]={}),K[1].stacking100Percent=!0):("1"!==d.showsecondarylimits&&(d.showsecondarylimits="0"),"1"!==d.showdivlinesecondaryvalue&&(d.showdivlinesecondaryvalue="0"));K[1]||(K[1]={});K[1].stacking100Percent=!0;for(O=0;O<a;O+=1)na=J[O],G=f(na.showlabel,d.showlabels,1),c=ha(G?Ga(na.label,
na.name):U),A.addXaxisCat(w,O,O,c),b+=N=na.value,E=f(na.dashed,W),D=h(na.dashgap,S),x=h(na.dashlen,I),ea=h(na.color,g.getPlotColor()),L=h(na.alpha,d.plotfillalpha,Ha),V=h(na.ratio,d.plotfillratio),G={opacity:L/100},ca=h(na.alpha,m)+U,va=da(ea+Z+R.replace(/,+?$/,""),L,V,p,k,v+U,ca+U,X,r),ua=E?Da(x,D,C):"none",la=b/s*100,oa=F.percentValue(la),ja=null===N?N:u.dataLabels(N),ia=za(ha(na.displayvalue)),ia=f(na.showvalue,K.showValues)?void 0!==ia?ia:ja:U,K.showTooltip?void 0!==(M=za(ha(h(na.tooltext,K.tooltext))))?
(ka={formattedValue:ja,label:c,yaxisName:T,xaxisName:Q,cumulativeValue:b,cumulativeDataValue:u.dataLabels(b),cumulativePercentValue:oa,sum:u.dataLabels(s),unformattedSum:s},fa=[1,2,3,5,6,7,20,21,22,23,24,25],M=Fa(M,fa,ka,na,d)):M=null===ja?!1:c!==U?c+K.tooltipSepChar+ja:ja:M=U,D=this.pointHoverOptions(na,q,{plotType:"column",is3d:r,isBar:X,use3DLighting:B,isRoundEdged:k,color:ea,gradientColor:R,alpha:L,ratio:V,angle:p,borderWidth:C,borderColor:v,borderAlpha:ca,borderDashed:E,borderDashGap:D,borderDashLen:x,
shadow:G}),E=h(na.link),q.data.push({link:E,toolText:M,displayValue:ia,categoryLabel:c,y:N,shadow:G,color:va[0],borderColor:va[1],borderWidth:C,use3DLighting:B,dashStyle:ua,tooltipConstraint:this.tooltipConstraint,hoverEffects:D.enabled&&D.options,rolloverProperties:D.enabled&&D.rolloverOptions}),this.pointValueWatcher(l,N),$&&(N=za(ha(h(na.cumulativeplottooltext,d.cumulativeplottooltext))),G=1==H?oa:0===H||ia===U?U:oa,M=z?void 0!==N?Fa(N,fa||[1,2,3,5,6,7,20,21,22,23,24,25],ka||{formattedValue:ja,
label:c,yaxisName:T,xaxisName:Q,cumulativeValue:b,cumulativeDataValue:u.dataLabels(b),cumulativePercentValue:oa,sum:u.dataLabels(s),unformattedSum:s},na,d):(c!==U?c+Y:U)+oa:U,e.data.push({shadow:aa,color:e.color,marker:e.marker,y:la,toolText:M,displayValue:G,categoryLabel:c,link:E,dashStyle:y,hoverEffects:P.enabled&&P.options,rolloverProperties:P.enabled&&P.rolloverOptions}));n.catCount=a;return t},defaultSeriesType:"column",isDual:!0,creditLabel:!1,rendererId:"cartesian"},t);m("pareto3d",{friendlyName:"3D Pareto Chart",
defaultSeriesType:"column3d",fireGroupEvent:!0,defaultPlotShadow:1,is3D:!0},m.pareto2d);m("mscolumn2d",{standaloneInit:!0,friendlyName:"Multi-series Column Chart",creditLabel:!1,rendererId:"cartesian"},m.mscolumn2dbase);m("mscolumn3d",{defaultSeriesType:"column3d",friendlyName:"Multi-series 3D Column Chart",defaultPlotShadow:1,fireGroupEvent:!0,is3D:!0,defaultZeroPlaneHighlighted:!1},m.mscolumn2d);m("msbar2d",{friendlyName:"Multi-series Bar Chart",isBar:!0,defaultSeriesType:"bar",spaceManager:m.barbase},
m.mscolumn2d);m("msbar3d",{defaultSeriesType:"bar3d",friendlyName:"Multi-series 3D Bar Chart",fireGroupEvent:!0,defaultPlotShadow:1,is3D:!0,defaultZeroPlaneHighlighted:!1},m.msbar2d);m("msline",{standaloneInit:!0,friendlyName:"Multi-series Line Chart",creditLabel:!1,rendererId:"cartesian"},m.mslinebase);m("msarea",{standaloneInit:!0,friendlyName:"Multi-series Area Chart",creditLabel:!1,rendererId:"cartesian"},m.msareabase);m("stackedcolumn2d",{friendlyName:"Stacked Column Chart",isStacked:!0},m.mscolumn2d);
m("stackedcolumn3d",{friendlyName:"3D Stacked Column Chart",isStacked:!0},m.mscolumn3d);m("stackedbar2d",{friendlyName:"Stacked Bar Chart",isStacked:!0},m.msbar2d);m("stackedbar3d",{friendlyName:"3D Stacked Bar Chart",isStacked:!0},m.msbar3d);m("stackedarea2d",{friendlyName:"Stacked Area Chart",isStacked:!0,areaAlpha:100,showSum:0},m.msarea);m("marimekko",{friendlyName:"Marimekko Chart",isValueAbs:!0,distributedColumns:!0,isStacked:!0,xAxisMinMaxSetter:Wa,postSeriesAddition:function(a,q){var c=a[wa],
d=0,l=a.xAxis,b=100/c.marimekkoTotal,s=[],e=a.series,g=0,r=Aa({},a.plotOptions.series.dataLabels.style),X=parseInt(r.fontSize,10),p=f(q.chart.plotborderthickness,1),C=a.chart.rotateValues,k=f(q.chart.rotatexaxispercentvalues,0),m=-.5*p-(p%2+(k?1:0)+!a.chart.plotBorderWidth),v=k?X/2*1.2:0,Y=C?270:0,w=c[0],$=w.stacking100Percent,K=!$,A=c.inCanvasStyle,h=this.numberFormatter,n=q.categories&&q.categories[0]&&q.categories[0].category||[],J=0,B=[],F,H,z,ua,I,S,Q,T,t,R,p=[];c.isXYPlot=!0;c.distributedColumns=
!0;l.min=0;l.max=100;l.labels.enabled=!1;l.gridLineWidth=0;l.alternateGridColor=Pa;F=w.stack;q.chart.interactivelegend="0";w=0;for(H=a.xAxis.plotLines.length;w<H;w+=1)z=l.plotLines[w],z.isGrid&&(z.isCat=!0,s[z.value]=z,z._hideLabel=!0);for(w=H=0;w<n.length;w+=1)n[w].vline||(J+=B[H]=h.getCleanValue(n[w].widthpercent||0),H+=1);z=F.floatedcolumn&&F.floatedcolumn[0]||[];if(100===J&&(z&&z.length)!==H)for(;H--;)z[H]||(z[H]={p:null});J=W(J);if(z)for(I=0,H=z.length;I<H;){R=z[I];d+=ua=R&&R.p||0;Q=100===J?
B[I]:ua*b;S=g+Q/2;T=g+Q;p.push(T);for(w=0;w<e.length;w+=1)if(a.series[w].visible=!0,n=a.series[w].data[I],n._FCX=g,n._FCW=Q,t=h.percentValue(n.y/ua*100),n.toolText=Fa(n.toolText,[14,24,25,111,112],{xAxisPercentValue:h.percentValue(Q),percentValue:t,sum:h.dataLabels(ua),unformattedSum:ua}),$){if(n.y||0===n.y)F=n.y/ua*100,n.y=F,n.showPercentValues&&(n.displayValue=t);if(n.previousY||0===n.previousY)n.previousY=n.previousY/ua*100}c.showStackTotal&&a.xAxis.plotLines.push({value:S,width:0,isVline:K,isTrend:!K,
_isStackSum:1,zIndex:4,label:{align:Ta,textAlign:Ta,rotation:Y,style:r,verticalAlign:Za,offsetScale:K?0>ua?R.n:R.p:void 0,offsetScaleIndex:0,y:0>ua?270===C?4:X:-4,x:0,text:h.yAxis(Ra(ua,10))}});s[I]&&(s[I].value=S,s[I]._weight=Q,s[I]._hideLabel=!1);I+=1;c.showXAxisPercentValues&&I<H&&a.xAxis.plotLines.push({value:T,width:0,isVine:!0,label:{align:Ta,textAlign:k?ab:Ta,rotation:k?270:0,backgroundColor:"#ffffff",backgroundOpacity:1,borderWidth:"1px",borderType:"solid",borderColor:A.color,style:{color:A.color,
fontSize:A.fontSize,fontFamily:A.fontFamily,lineHeight:A.lineHeight},verticalAlign:Xa,y:m,x:v,text:this.numberFormatter.percentValue(T)},zIndex:5});g=T}I=0;for(H=s.length;I<H;I+=1)s[I]&&s[I]._hideLabel&&(s[I].value=null);w=0;for(H=a.xAxis.plotLines.length;w<H;w+=1)if(z=l.plotLines[w],z.isVline&&!z._isStackSum&&(c=z.value))c-=.5,d=p[u(c)],b=p[x(c)],z.value=d+(b-d)*(c-u(c))},defaultSeriesType:"floatedcolumn"},m.stackedcolumn2d);m("msstackedcolumn2d",{friendlyName:"Multi-series Stacked Column Chart",
series:function(a,q,c){var d,l,b,s,e=q[wa],g=0,r,X;r=[];var p;q.legend.enabled=Boolean(f(a.chart.showlegend,1));if(a.dataset&&0<a.dataset.length){this.categoryAdder(a,q);d=0;for(l=a.dataset.length;d<l;d+=1)if(p=a.dataset[d].dataset)for(b=0,s=p.length;b<s;b+=1,g+=1)r={hoverEffects:this.parseSeriesHoverOptions(a,q,p[b],c),visible:!f(p[b].initiallyhidden,0),data:[],numColumns:l,columnPosition:d},X=Math.min(e.oriCatTmp.length,p[b].data&&p[b].data.length),r=this.point(c,r,p[b],a.chart,q,X,g,d),q.series.push(r);
if(this.isDual&&a.lineset&&0<a.lineset.length)for(b=0,s=a.lineset.length;b<s;b+=1,g+=1)d=a.lineset[b],r={hoverEffects:this.parseSeriesHoverOptions(a,q,d,c),visible:!f(d.initiallyhidden,0),data:[],yAxis:1,type:"line"},X=Math.min(e.oriCatTmp.length,d.data&&d.data.length),q.series.push(m.msline.point.call(this,"msline",r,d,a.chart,q,X,g));this.configureAxis(q,a);a.trendlines&&Ua(a.trendlines,q.yAxis,q[wa],this.isDual,this.isBar)}},postSpaceManager:function(a,q,c){var d=a[wa],b,e,s;m.base.postSpaceManager.call(this);
if(this.isStacked&&d.showStackTotal&&(b=a.chart,a=(q=a.xAxis)&&q.plotLines,b=c-b.marginLeft-b.marginRight,c=d.plotSpacePercent,d=d[0].stack,d=d.column&&d.column.length,e=(1-2*c)/d,q=b/(q.max-q.min),50<q*e&&.1==c))for(q=50/q,c=a&&a.length,d=-((d-1)/2)*q,s=0;s<c;s+=1)e=a[s],e._isStackSum&&(b=e._catPosition+(d+q*e._stackIndex),e.value=b)}},m.stackedcolumn2d);m("mscombi2d",{friendlyName:"Multi-series Combination Chart",standaloneInit:!0,creditLabel:!1,rendererId:"cartesian"},m.mscombibase);m("mscombi3d",
{friendlyName:"Multi-series 3D Combination Chart",series:m.mscombi2d.series,eiMethods:function(a){var q={};ya(a.split(","),function(a){q[a]=function(){qa.raiseWarning(this,"1301081430","run","JSRenderer~"+a+"()","Method not applicable.")}});return q}("view2D,view3D,resetView,rotateView,getViewAngles,fitToStage")},m.mscolumn3d);m("mscolumnline3d",{friendlyName:"Multi-series Column and Line Chart"},m.mscombi3d);m("stackedcolumn2dline",{friendlyName:"Stacked Column and Line Chart",isStacked:!0,stack100percent:0},
m.mscombi2d);m("stackedcolumn3dline",{friendlyName:"Stacked 3D Column and Line Chart",isStacked:!0,stack100percent:0},m.mscombi3d);m("mscombidy2d",{friendlyName:"Multi-series Dual Y-Axis Combination Chart",isDual:!0,secondarySeriesType:void 0},m.mscombi2d);m("mscolumn3dlinedy",{friendlyName:"Multi-series 3D Column and Line Chart",isDual:!0,secondarySeriesType:"line"},m.mscolumnline3d);m("stackedcolumn3dlinedy",{friendlyName:"Stacked 3D Column and Line Chart",isDual:!0,secondarySeriesType:"line"},
m.stackedcolumn3dline);m("msstackedcolumn2dlinedy",{friendlyName:"Multi-series Dual Y-Axis Stacked Column and Line Chart",isDual:!0,stack100percent:0,secondarySeriesType:"line"},m.msstackedcolumn2d);m("scrollcolumn2d",{friendlyName:"Scrollable Multi-series Column Chart",postSeriesAddition:m.scrollbase.postSeriesAddition,tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:40},m.mscolumn2d);m("scrollline2d",{friendlyName:"Scrollable Multi-series Line Chart",postSeriesAddition:m.scrollbase.postSeriesAddition,
tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:75},m.msline);m("scrollarea2d",{friendlyName:"Scrollable Multi-series Area Chart",postSeriesAddition:m.scrollbase.postSeriesAddition,tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:75},m.msarea);m("scrollstackedcolumn2d",{friendlyName:"Scrollable Stacked Column Chart",postSeriesAddition:function(a,q,c,d){m.base.postSeriesAddition.call(this,a,q,c,d);m.scrollbase.postSeriesAddition.call(this,a,q,c,d)},canvasborderthickness:1,
tooltipConstraint:"plot",avgScrollPointWidth:75},m.stackedcolumn2d);m("scrollcombi2d",{friendlyName:"Scrollable Combination Chart",postSeriesAddition:m.scrollbase.postSeriesAddition,tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:40},m.mscombi2d);m("scrollcombidy2d",{friendlyName:"Scrollable Dual Y-Axis Combination Chart",postSeriesAddition:m.scrollbase.postSeriesAddition,tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:40},m.mscombidy2d);m("scatter",{friendlyName:"Scatter Chart",
isXY:!0,standaloneInit:!0,defaultSeriesType:"scatter",defaultZeroPlaneHighlighted:!1,creditLabel:!1},m.scatterbase);m("bubble",{friendlyName:"Bubble Chart",standaloneInit:!0,standaloneInut:!0,defaultSeriesType:"bubble",rendererId:"bubble",point:function(a,q,c,d,b){a=f(d.ignoreemptydatasets,0);var e=!1,s=this.colorManager,ga,g,r,X,p,C,k,m,v,Y,w,$,K,A,n,z,J=f(c.showvalues,b[wa].showValues);ga=f(d.bubblescale,1);var B=h(d.negativecolor,"FF0000"),F=b.plotOptions.bubble,H=this.numberFormatter,W=q._showRegression=
f(c.showregressionline,d.showregressionline,0),u,I,S,Q;q.name=za(c.seriesname);r=Boolean(f(c.drawanchors,c.showanchors,d.drawanchors,1));m=h(c.plotfillalpha,c.bubblefillalpha,d.plotfillalpha,Ha);v=f(c.showplotborder,d.showplotborder,1);Y=ba(h(c.plotbordercolor,d.plotbordercolor,"666666"));w=h(c.plotborderthickness,d.plotborderthickness,1);$=h(c.plotborderalpha,d.plotborderalpha,"95");v=1===v?w:0;s=h(c.color,c.plotfillcolor,d.plotfillcolor,s.getPlotColor());q.marker={enabled:r,fillColor:this.getPointColor(s,
Ha),lineColor:pa(Y,v?$:0),lineWidth:v,symbol:"circle"};if(w=c.data){z=w.length;F.bubbleScale=ga;if(0===f(c.includeinlegend)||void 0===q.name)q.showInLegend=!1;W&&(q.events={hide:this.hideRLine,show:this.showRLine},u={sumX:0,sumY:0,sumXY:0,sumXsqure:0,sumYsqure:0,xValues:[],yValues:[]},I=f(c.showyonx,d.showyonx,1),S=ba(h(c.regressionlinecolor,d.regressionlinecolor,s)),Q=f(c.regressionlinethickness,d.regressionlinethickness,1),ga=sa(f(c.regressionlinealpha,d.regressionlinealpha,100)),S=pa(S,ga));for(g=
0;g<z;g+=1)(X=w[g])?(ga=H.getCleanValue(X.y),K=H.getCleanValue(X.x),A=H.getCleanValue(X.z,!0),null===ga?q.data.push({y:null,x:K}):(e=!0,k=0!==f(d.use3dlighting,X.is3d,c.is3d,d.is3d),p=ba(h(X.color,0>X.z?B:s)),C=h(X.alpha,m),n=this.getPointStub(X,ga,K,b,c,J),p=k?this.getPointColor(p,C):{FCcolor:{color:p,alpha:C}},null!==A&&(F.zMax=F.zMax>A?F.zMax:A,F.zMin=F.zMin<A?F.zMin:A),X=this.pointHoverOptions(X,q,{plotType:"bubble",is3d:k,seriesAnchorSymbol:"circle",color:p,negativeColor:B,alpha:C,borderWidth:v,
borderColor:Y,borderAlpha:$,shadow:!1}),q.data.push({y:ga,x:K,z:A,displayValue:n.displayValue,toolText:n.toolText,link:n.link,hoverEffects:X.enabled&&X.options,rolloverProperties:X.enabled&&X.rolloverOptions,marker:{enabled:r,fillColor:p,lineColor:{FCcolor:{color:Y,alpha:$}},lineWidth:v,symbol:"circle"}}),this.pointValueWatcher(b,ga,K,W&&u))):q.data.push({y:null});W&&(c={type:"line",color:S,showInLegend:!1,lineWidth:Q,enableMouseTracking:!1,marker:{enabled:!1},data:this.getRegressionLineSeries(u,
I,z),zIndex:0},q=[q,c])}a&&!e&&(q.showInLegend=!1);return q},getPointStub:function(a,q,c,d,b,e){var s=this.dataObj.chart;d=d[wa];q=null===q?q:d.numberFormatter.dataLabels(q);var ga,g=d.tooltipSepChar,r=za(ha(h(a.tooltext,b.plottooltext,d.tooltext)));d.showTooltip?void 0!==r?b=Fa(r,[4,5,6,7,8,9,10,11,12,13],{yDataValue:q,xDataValue:d.numberFormatter.xAxis(c),yaxisName:ha(s.yaxisname),xaxisName:ha(s.xaxisname)},a,s,b):null===q?b=!1:(d.seriesNameInToolTip&&(ga=h(b&&b.seriesname)),b=ga?ga+g:U,b+=c?d.numberFormatter.xAxis(c)+
g:U,b=b+q+(a.z?g+d.numberFormatter.dataLabels(a.z):U)):b=U;c=f(a.showvalue,e,d.showValues)?void 0!==h(a.displayvalue,a.name,a.label)?ha(h(a.displayvalue,a.name,a.label)):q:U;a=za(a.link);return{displayValue:c,toolText:b,link:a}}},m.scatter);m("ssgrid",{friendlyName:"Grid Component",standaloneInit:!0,defaultSeriesType:"ssgrid",rendererId:"ssgrid",chart:function(a,b){var c=this.containerElement,d=Aa({},this.dataObj),l=d.chart||(d.chart=d.graph||{}),e=this.chartInstance,s=0,g=[],k=d.data,r=k&&k.length,
X=this.smartLabel,p=this.numberFormatter,C=c.offsetHeight,Y=c.offsetWidth,$=this.colorManager,v,n,w,z,K,A,W,u,J,B,F,H,R,t,I,S,Q,T,x,L,N,O,ea,E,D,V=0;n=0;var c={_FCconf:{0:{stack:{}},1:{stack:{}},x:{stack:{}},noWrap:!1,marginLeftExtraSpace:0,marginRightExtraSpace:0,marginBottomExtraSpace:0,marginTopExtraSpace:0,marimekkoTotal:0},chart:{ignoreHiddenSeries:!1,events:{},spacingTop:0,spacingRight:0,spacingBottom:0,spacingLeft:0,marginTop:0,marginRight:0,marginBottom:0,marginLeft:0,borderRadius:0,borderColor:"#000000",
borderWidth:1,defaultSeriesType:"ssgrid",style:{fontFamily:h(l.basefont,"Verdana,sans"),fontSize:Sa(l.basefontsize,20)+Qa,color:h(l.basefontcolor,$.getColor("baseFontColor")).replace(/^#?([a-f0-9]+)/ig,"#$1")},plotBackgroundColor:Pa},labels:{smartLabel:X},colors:"AFD8F8 F6BD0F 8BBA00 FF8E46 008E8E D64646 8E468E 588526 B3AA00 008ED6 9D080D A186BE CC6600 FDC689 ABA000 F26D7D FFF200 0054A6 F7941C CC3300 006600 663300 6DCFF6".split(" "),credits:{href:y.CREDIT_HREF,text:y.CREDIT_STRING,enabled:!1},legend:{enabled:!1},
series:[],subtitle:{text:U},title:{text:U},tooltip:{enabled:!1},exporting:{buttons:{exportButton:{},printButton:{enabled:!1}}}},P=c[wa],M=v=n=V=0,ja=s=t=0;D=e.jsVars.cfgStore;e=c.chart;K=e.toolbar={button:{}};A=K.button;delete d.graph;ca(c.chart.style);e.events.click=this.linkClickFN;A.scale=f(l.toolbarbuttonscale,1.15);A.width=f(l.toolbarbuttonwidth,15);A.height=f(l.toolbarbuttonheight,15);A.radius=f(l.toolbarbuttonradius,2);A.spacing=f(l.toolbarbuttonspacing,5);A.fill=pa(h(l.toolbarbuttoncolor,
"ffffff"));A.labelFill=pa(h(l.toolbarlabelcolor,"cccccc"));A.symbolFill=pa(h(l.toolbarsymbolcolor,"ffffff"));A.hoverFill=pa(h(l.toolbarbuttonhovercolor,"ffffff"));A.stroke=pa(h(l.toolbarbuttonbordercolor,"bbbbbb"));A.symbolStroke=pa(h(l.toolbarsymbolbordercolor,"9a9a9a"));A.strokeWidth=f(l.toolbarbuttonborderthickness,1);A.symbolStrokeWidth=f(l.toolbarsymbolborderthickness,1);d=A.symbolPadding=f(l.toolbarsymbolpadding,5);A.symbolHPadding=f(l.toolbarsymbolhpadding,d);A.symbolVPadding=f(l.toolbarsymbolvpadding,
d);A=K.position=h(l.toolbarposition,"tr").toLowerCase();switch(A){case "tr":case "tl":case "br":case "bl":break;default:A="tr"}d=K.hAlign="left"===(U+l.toolbarhalign).toLowerCase()?"l":A.charAt(1);A=K.vAlign="bottom"===(U+l.toolbarvalign).toLowerCase()?"b":A.charAt(0);K.hDirection=f(l.toolbarhdirection,"r"===d?-1:1);K.vDirection=f(l.toolbarvdirection,"b"===A?-1:1);K.vMargin=f(l.toolbarvmargin,6);K.hMargin=f(l.toolbarhmargin,10);K.x=f(l.toolbarx,"l"===d?0:a);K.y=f(l.toolbary,"t"===A?0:b);void 0!==
h(l.clickurl)&&(e.link=l.clickurl,e.style.cursor="pointer");v=f(D.showpercentvalues,l.showpercentvalues,0);n=h(D.numberitemsperpage,l.numberitemsperpage);f(D.showshadow,l.showshadow,0);s=h(D.basefont,l.basefont,"Verdana,sans");w=Sa(D.basefontsize,l.basefontsize,10);w+=Qa;z=ba(h(D.basefontcolor,l.basefontcolor,$.getColor("baseFontColor")));d=ba(h(D.alternaterowbgcolor,l.alternaterowbgcolor,$.getColor("altHGridColor")));K=h(D.alternaterowbgalpha,l.alternaterowbgalpha,$.getColor("altHGridAlpha"))+U;
A=f(D.listrowdividerthickness,l.listrowdividerthickness,1);W=ba(h(D.listrowdividercolor,l.listrowdividercolor,$.getColor("borderColor")));u=f(D.listrowdivideralpha,l.listrowdivideralpha,$.getColor("altHGridAlpha"))+15+U;J=f(D.colorboxwidth,l.colorboxwidth,8);B=f(D.colorboxheight,l.colorboxheight,8);F=f(D.navbuttonradius,l.navbuttonradius,7);H=ba(h(D.navbuttoncolor,l.navbuttoncolor,$.getColor("canvasBorderColor")));R=ba(h(D.navbuttonhovercolor,l.navbuttonhovercolor,$.getColor("altHGridColor")));t=
f(D.textverticalpadding,l.textverticalpadding,3);I=f(D.navbuttonpadding,l.navbuttonpadding,5);S=f(D.colorboxpadding,l.colorboxpadding,10);Q=f(D.valuecolumnpadding,l.valuecolumnpadding,10);T=f(D.namecolumnpadding,l.namecolumnpadding,5);x=f(D.borderthickness,l.borderthickness,1);L=ba(h(D.bordercolor,l.bordercolor,$.getColor("borderColor")));N=h(D.borderalpha,l.borderalpha,$.getColor("borderAlpha"))+U;O=h(D.bgcolor,l.bgcolor,"FFFFFF");ea=h(D.bgalpha,l.bgalpha,Ha);E=h(D.bgratio,l.bgratio,Ha);D=h(D.bgangle,
l.bgangle,Ba);e.borderRadius=x/16;e.borderWidth=x;e.borderColor=G({FCcolor:{color:L,alpha:N}});e.backgroundColor={FCcolor:{color:O,alpha:ea,ratio:E,angle:D}};e.borderRadius=f(l.borderradius,0);D={fontFamily:s,fontSize:w,color:z};ca(D);X.setStyle(D);for(s=0;s<r;s+=1)if(w=k[s],x=p.getCleanValue(w.value),L=ha(Ga(w.label,w.name)),z=ba(h(w.color,$.getPlotColor())),h(w.alpha,l.plotfillalpha,Ha),L!=U||null!=x)g.push({value:x,label:L,color:z}),V+=x,ja+=1;for(s=0;s<ja;s+=1)w=g[s],x=w.value,w.dataLabel=w.label,
w.displayValue=v?p.percentValue(x/V*100):p.dataLabels(x),k=X.getOriSize(w.displayValue),M=Math.max(M,k.width+Q);n?n>=ja?(v=C/ja,n=ja):(p=C-2*(I+F),v=p/n):(V=parseInt(D.lineHeight,10),V=Math.max(V+2*t,B),n=C/V,n>=ja?(v=C/ja,n=ja):(p=C-2*(I+F),n=Math.floor(p/V),v=p/n));t=Y-S-J-T-M-Q;s=S+J+T;p=h(l.basefont,"Verdana,sans");M=Sa(l.basefontsize,10);$=h(l.basefontcolor,$.getColor("baseFontColor"));k=h(l.outcnvbasefont,p);w=Sa(l.outcnvbasefontsize,M);r=w+Qa;l=h(l.outcnvbasefontcolor,$).replace(/^#?([a-f0-9]+)/ig,
"#$1");M+=Qa;$=$.replace(/^#?([a-f0-9]+)/ig,"#$1");P.trendStyle=P.outCanvasStyle={fontFamily:k,color:l,fontSize:r};ca(P.trendStyle);P.inCanvasStyle={fontFamily:p,fontSize:M,color:$};c.tooltip.style={fontFamily:p,fontSize:M,lineHeight:void 0,color:$};c.tooltip.shadow=!1;e.height=C;e.width=Y;e.rowHeight=v;e.labelX=s;e.colorBoxWidth=J;e.colorBoxHeight=B;e.colorBoxX=S;e.valueX=S+J+T+t+Q;e.valueColumnPadding=Q;e.textStyle=D;e.listRowDividerAttr={"stroke-width":A,stroke:{FCcolor:{color:W,alpha:u}}};e.alternateRowColor=
{FCcolor:{color:d,alpha:K}};e.navButtonRadius=F;e.navButtonPadding=I;e.navButtonColor=H;e.navButtonHoverColor=R;e.lineHeight=parseInt(D.lineHeight,10);C=[];l=0;P=!0;for(s=0;s<ja&0!==n;s+=1)0===s%n&&(C.push({data:[],visible:P}),P=!1,l+=1),w=g[s],Y=X.getSmartText(w.dataLabel,t,v),C[l-1].data.push({label:Y.text,originalText:Y.tooltext,displayValue:w.displayValue,y:w.value,color:w.color});c.series=C;m.base.parseExportOptions.call(this,c);c.tooltip.enabled=!!c.exporting.enabled;return c},creditLabel:!1},
m.base);m("renderer.bubble",{drawPlotBubble:function(a,q){var c=this,d=c.options,l=d.chart,g=d.plotOptions.series,s=g.dataLabels&&g.dataLabels.style||{},ga={fontFamily:s.fontFamily,fontSize:s.fontSize,lineHeight:s.lineHeight,fontWeight:s.fontWeight,fontStyle:s.fontStyle},s=c.paper,k=c.elements,r=a.items,X=a.graphics=a.graphics||[],p=c.xAxis[q.xAxis||0],C=c.yAxis[q.yAxis||0],$=a.data,Y=!1!==(d.tooltip||{}).enabled,v,n,g=isNaN(+g.animation)&&g.animation.duration||1E3*g.animation,w=!1===q.visible?"hidden":
"visible",d=d.plotOptions.bubble,m=d.zMax,d=d.bubbleScale,z=R(c.canvasHeight,c.canvasWidth)/8,m=M(m),A,h,u,J,B,F,H,t,x,I,S;A=c.layers;h=A.dataset=A.dataset||s.group("dataset-orphan");var Q=A.tracker,T,V,L=function(a){xa.call(this,c,a)},N=function(a,d,b){return function(q){a.attr(d);xa.call(this,c,q,b)}};c.addCSSDefinition(".fusioncharts-datalabels .fusioncharts-label",ga);A.datalabels?A.datalabels.attr("class","fusioncharts-datalabels"):A.datalabels=s.group({"class":"fusioncharts-datalabels"},"datalables").insertAfter(h);
ga=h.bubble=h.bubble||s.group("bubble",h);l.clipBubbles&&!ga.attrs["clip-rect"]&&ga.attr({"clip-rect":k["clip-canvas"]});A=0;for(h=$.length;A<h;A+=1){u=$[A];I=S=V=null;x=u.marker;if(null!==u.y&&x&&x.enabled){J=u.link;l=u.toolText;B=f(u.x,A);F=u.y;k={index:A,link:J,value:F,y:F,x:B,z:u.z,displayValue:u.displayValue,toolText:u.toolText,id:a.userID,datasetIndex:a.index,datasetName:a.name,visible:a.visible};t=C.getAxisPosition(F);H=p.getAxisPosition(B);n=M(u.z);T=W(n*z/m)*d||0;n=v={};u.hoverEffects&&(n=
{fill:G(x.fillColor),"stroke-width":x.lineWidth,stroke:G(x.lineColor),r:T},v=u.rolloverProperties,v={fill:G(v.fillColor),"stroke-width":v.lineWidth,stroke:G(v.lineColor),r:T*v.scale});I=s.circle(H,t,0,ga).attr({fill:G(x.fillColor),"stroke-width":x.lineWidth,stroke:G(x.lineColor),visibility:w}).animate({r:T||0},g,"easeOut",c.getAnimationCompleteFn());if(J||Y)T<b&&(T=b),S=s.circle(H,t,T,Q).attr({cursor:J?"pointer":"",stroke:e,"stroke-width":x.lineWidth,fill:e,ishot:!!J,visibility:w});(S||I).data("eventArgs",
k).click(L).hover(N(I,v,"DataPlotRollOver"),N(I,n,"DataPlotRollOut")).tooltip(l);r[A]={index:A,x:B,y:F,z:u.z,value:F,graphic:I,dataLabel:V,tracker:S};V=c.drawPlotLineLabel(a,q,A,H,t)}else r[A]={index:A,x:B,y:F};V&&X.push(V);I&&X.push(I);S&&X.push(S)}a.visible=!1!==q.visible;return a}},m["renderer.cartesian"]);m("renderer.ssgrid",{drawGraph:function(){var a=this.options.series,b=this.elements,c=b.plots,d=a.length,l;c||(c=this.plots=this.plots||[],b.plots=c);this.drawSSGridNavButton();for(l=0;l<d;l++)(b=
c[l])||c.push(b={items:[],data:a[l].data}),a[l].data&&a[l].data.length&&this.drawPlot(b,a[l]);1<d&&this.nenagitePage(0)},drawPlot:function(a){var b=a.data,c=this.paper,d=this.options.chart,l=d.colorBoxHeight,e=d.colorBoxWidth,s=d.colorBoxX,g=d.labelX,k=d.valueX,r=d.rowHeight,f=d.width,p=d.listRowDividerAttr,C=p["stroke-width"],p=G(p.stroke),$=C%2/2,Y=d.textStyle,v=this.layers,v=v.dataset=v.dataset||c.group("dataset-orphan"),d=G(d.alternateRowColor);a=a.items;var n=0,w,m,h,A;b&&b.length||(b=[]);p=
{stroke:p,"stroke-width":C};A=0;for(C=b.length;A<C;A+=1)h=b[A],m=h.y,w=a[A]={index:A,value:m,graphic:null,dataLabel:null,dataValue:null,alternateRow:null,listRowDivider:null,hot:null},null!==m&&void 0!==m&&(0===A%2&&(w.alternateRow=c.rect(0,n,f,r,0,v).attr({fill:d,"stroke-width":0})),m=W(n)+$,w.listRowDivider=c.path(["M",0,m,"L",f,m],v).attr(p),w.graphic=c.rect(s,n+r/2-l/2,e,l,0,v).attr({fill:h.color,"stroke-width":0,stroke:"#000000"}),m=w.dataLabel=c.text().attr({text:h.label,title:h.originalText||
"",x:g,y:n+r/2,fill:Y.color,"text-anchor":"start"}).css(Y),v.appendChild(m),w=w.dataValue=c.text().attr({text:h.displayValue,title:h.originalText||"",x:k,y:n+r/2,fill:Y.color,"text-anchor":"start"}).css(Y),v.appendChild(w),n+=r);m=W(n)+$;c.path(["M",0,m,"L",f,m],v).attr(p)},drawSSGridNavButton:function(){var a=this,b=a.paper,c=a.options,d=c.chart,l=c.series,e=d.navButtonColor,s=d.navButtonHoverColor,c=d.navButtonRadius,g=.67*c,f=d.navButtonPadding+g+(l&&l[0].data&&l[0].data.length*d.rowHeight)+.5*
c,d=d.width-20,r,k,p,C;1<l.length&&(C=a.naviigator=b.group("navigation"),a.navElePrv=l=b.group(C),r=b.path(["M",20,f,"L",20+c+g,f-g,20+c,f,20+c+g,f+g,"Z"]).attr({fill:e,"stroke-width":0,cursor:"pointer"}),l.appendChild(r),p=b.circle(20+c,f,c).attr({fill:Pa,"stroke-width":0,cursor:"pointer"}).mouseover(function(){r.attr({fill:s,cursor:"pointer"})}).mouseout(function(){r.attr({fill:e})}).click(function(){a.nenagitePage(-1)}),l.appendChild(p),a.navEleNxt=l=b.group(C),k=b.path(["M",d,f,"L",d-c-g,f-g,
d-c,f,d-c-g,f+g,"Z"]).attr({fill:e,"stroke-width":0,cursor:"pointer"}),l.appendChild(k),b=b.circle(d-c,f,c).attr({fill:Pa,"stroke-width":0,cursor:"pointer"}).mouseover(function(){k.attr({fill:s})}).mouseout(function(){k.attr({fill:e})}).click(function(){a.nenagitePage(1)}),l.appendChild(b))},nenagitePage:function(a){var b=this.plots,c=b.length;a=(this.currentSeriesIndex||0)+(a||0);var d,l=function(a){a.graphic&&a.graphic.hide();a.dataLabel&&a.dataLabel.hide();a.dataValue&&a.dataValue.hide();a.alternateRow&&
a.alternateRow.hide();a.listRowDivider&&a.listRowDivider.hide()};if(b[a]){for(d=c;d--;)ya(b[d].items,l);ya(b[a].items,function(a){a.graphic&&a.graphic.show();a.dataLabel&&a.dataLabel.show();a.dataValue&&a.dataValue.show();a.alternateRow&&a.alternateRow.show();a.listRowDivider&&a.listRowDivider.show()});this.currentSeriesIndex=a;qa.raiseEvent("pageNavigated",{pageId:a,data:this.options.series[a].data},this.logic.chartInstance);0===a?this.navElePrv.hide():this.navElePrv.show();a===c-1?this.navEleNxt.hide():
this.navEleNxt.show()}}},m["renderer.root"]);La.prototype={getArcPath:function(a,b,c,d,l,e,s,g,f,r){return c==l&&d==e?[]:["A",s,g,0,r,f,l,e]},parseColor:function(a,b){var c,d,l,e,s,g,f,r,k,p,C=b/2,$,m,v,n,w;w=3;this.use3DLighting?(c=ia(a,80),d=ia(a,75),g=ka(a,85),f=ka(a,70),r=ka(a,40),k=ka(a,50),ka(a,30),p=ka(a,65),ia(a,85),l=ia(a,69),e=ia(a,75),s=ia(a,95)):(w=10,c=ia(a,90),d=ia(a,87),g=ka(a,93),f=ka(a,87),r=ka(a,80),p=k=ka(a,85),ka(a,80),s=ia(a,85),l=ia(a,75),e=ia(a,80));$=d+Z+g+Z+f+Z+g+Z+d;v=b+
Z+b+Z+b+Z+b+Z+b;m=d+Z+a+Z+g+Z+a+Z+d;n=C+Z+C+Z+C+Z+C+Z+C;r=d+Z+a+Z+r+Z+a+Z+d;l=e+Z+g+Z+k+Z+g+Z+l;e="FFFFFF"+Z+"FFFFFF"+Z+"FFFFFF"+Z+"FFFFFF"+Z+"FFFFFF";w=0+Z+C/w+Z+b/w+Z+C/w+Z+0;return{frontOuter:{FCcolor:{gradientUnits:"userSpaceOnUse",x1:this.leftX,y1:0,x2:this.rightX,y2:0,color:l,alpha:v,angle:0,ratio:"0,20,15,15,50"}},backOuter:{FCcolor:{gradientUnits:"userSpaceOnUse",x1:this.leftX,y1:0,x2:this.rightX,y2:0,color:r,alpha:n,angle:0,ratio:"0,62,8,8,22"}},frontInner:{FCcolor:{gradientUnits:"userSpaceOnUse",
x1:this.leftInnerX,y1:0,x2:this.rightInnerX,y2:0,color:m,alpha:n,angle:0,ratio:"0,25,5,5,65"}},backInner:{FCcolor:{gradientUnits:"userSpaceOnUse",x1:this.leftInnerX,y1:0,x2:this.rightInnerX,y2:0,color:$,alpha:v,angle:0,ratio:"0,62,8,8,22"}},topBorder:{FCcolor:{gradientUnits:"userSpaceOnUse",x1:this.leftX,y1:0,x2:this.rightX,y2:0,color:e,alpha:w,angle:0,ratio:"0,20,15,15,50"}},topInnerBorder:{FCcolor:{gradientUnits:"userSpaceOnUse",x1:this.leftInnerX,y1:0,x2:this.rightInnerX,y2:0,color:e,alpha:w,angle:0,
ratio:"0,50,15,15,20"}},top:ma?{FCcolor:{gradientUnits:"userSpaceOnUse",radialGradient:!0,cx:this.cx,cy:this.cy,r:this.rx,fx:this.cx-.3*this.rx,fy:this.cy*****this.ry,color:p+Z+s,alpha:b+Z+b,ratio:"0,100"}}:{FCcolor:{gradientUnits:"objectBoundingBox",color:f+Z+f+Z+g+Z+d,alpha:b+Z+b+Z+b+Z+b,angle:-72,ratio:"0,8,15,77"}},bottom:G(pa(a,C)),startSlice:G(pa(c,b)),endSlice:G(pa(c,b))}},rotate:function(a){if(!this.hasOnePoint){for(var b=this.pointElemStore,c=0,d=b.length,l;c<d;c+=1)l=b[c],l=l._confObject,
l.start+=a,l.end+=a,this.updateSliceConf(l);this.refreshDrawing()}},refreshDrawing:function(){return function(){var a=this.slicingWallsArr,b=0,c,d=a.length,l,e,s,g,f=this.slicingWallsFrontGroup,r=this.slicingWallsBackGroup;a:{var k=a[0]&&a[0]._conf.index,p,C;g=k<=z;l=1;for(c=a.length;l<c;l+=1)if(C=a[l]._conf.index,p=C<=z,p!=g||C<k)break a;l=0}for(;b<d;b+=1,l+=1)l===d&&(l=0),c=a[l],g=c._conf.index,g<Oa?f.appendChild(c):g<=z?(e?c.insertBefore(e):f.appendChild(c),e=c):g<va?(s?c.insertBefore(s):r.appendChild(c),
s=c):r.appendChild(c)}}(),updateSliceConf:function(a,b){var c=this.getArcPath,d=a.start,l=a.end,e=fa(d),s=fa(l),g,f,r,k,p,C,n,m,v,h,w,u,K,A,x,W,J=this.cx,B=this.cy,F=this.rx,H=this.ry,t=F+(ma?-1:2),R=H+(ma?-1:2),I=this.innerRx,S=this.innerRy,Q=this.depth,T=this.depthY,L=a.elements,P,N,O,ea,E,D,y;g=Y(e);f=$(e);r=Y(s);k=$(s);p=J+F*g;C=B+H*f;n=J+t*g;m=B+R*f;P=C+Q;N=J+F*r;O=B+H*k;v=J+t*r;h=B+R*k;ea=O+Q;this.isDoughnut?(w=J+I*g,u=B+S*f,x=u+Q,K=J+I*r,A=B+S*k,W=A+Q,a.startSlice=["M",p,C,"L",p,P,w,x,w,u,
"Z"],a.endSlice=["M",N,O,"L",N,ea,K,W,K,A,"Z"]):(a.startSlice=["M",p,C,"L",p,P,J,T,J,B,"Z"],a.endSlice=["M",N,O,"L",N,ea,J,T,J,B,"Z"]);ma?(c=(e>s?V:0)+s-e,a.clipTopPath=this.isDoughnut?["M",p,C,"A",F,H,0,c>z?1:0,1,N,O,"L",K,A,"A",I,S,0,c>z?1:0,0,w,u,"Z"]:["M",p,C,"A",F,H,0,c>z?1:0,1,N,O,"L",this.cx,this.cy,"Z"],a.clipOuterFrontPath1=this.clipPathforNoClip,a.clipTopBorderPath=["M",n,m,"A",t,R,0,c>z?1:0,1,v,h,"L",N,O,N,O+1,"A",F,H,0,c>z?1:0,0,p,C+1,"L",p,C,"Z"],d!=l?e>s?e<z?(a.clipOuterFrontPath=["M",
this.rightX,B,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,this.rightX,B+Q,"Z"],a.clipOuterFrontPath1=["M",this.leftX,B,"A",F,H,0,0,0,p,C,"v",Q,"A",F,H,0,0,1,this.leftX,B+Q,"Z"],a.clipOuterBackPath=["M",this.rightX,B,"A",F,H,0,1,0,this.leftX,B,"v",Q,"A",F,H,0,1,1,this.rightX,B+Q,"Z"],this.isDoughnut&&(a.clipInnerBackPath=["M",this.rightInnerX,B,"A",I,S,0,1,0,this.leftInnerX,B,"v",Q,"A",I,S,0,1,1,this.rightInnerX,B+Q,"Z"],a.clipInnerFrontPath=["M",this.rightInnerX,B,"A",I,S,0,0,1,K,A,"v",Q,"A",I,S,0,0,0,
this.rightInnerX,B+Q,"Z","M",this.leftInnerX,B,"A",I,S,0,0,0,w,u,"v",Q,"A",I,S,0,0,1,this.leftInnerX,B+Q,"Z"])):s>z?(a.clipOuterFrontPath=["M",this.rightX,B,"A",F,H,0,1,1,this.leftX,B,"v",Q,"A",F,H,0,1,0,this.rightX,B+Q,"Z"],a.clipOuterBackPath=["M",this.leftX,B,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,this.leftX,B+Q,"Z","M",this.rightX,B,"A",F,H,0,0,0,p,C,"v",Q,"A",F,H,0,0,1,this.rightX,B+Q,"Z"],this.isDoughnut&&(a.clipInnerFrontPath=["M",this.rightInnerX,B,"A",I,S,0,1,1,this.leftInnerX,B,"v",Q,"A",
I,S,0,1,0,this.rightInnerX,B+Q,"Z"],a.clipInnerBackPath=["M",this.leftInnerX,B,"A",I,S,0,0,1,K,A,"v",Q,"A",I,S,0,0,0,this.leftInnerX,B+Q,"Z","M",this.rightInnerX,B,"A",I,S,0,0,0,w,u,"v",Q,"A",I,S,0,0,1,this.rightInnerX,B+Q,"Z"])):(a.clipOuterFrontPath=["M",this.rightX,B,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,this.rightX,B+Q,"Z"],a.clipOuterBackPath=["M",p,C,"A",F,H,0,0,1,this.rightX,B,"v",Q,"A",F,H,0,0,0,p,P,"Z"],this.isDoughnut&&(a.clipInnerFrontPath=["M",this.rightInnerX,B,"A",I,S,0,0,1,K,A,"v",
Q,"A",I,S,0,0,0,this.rightInnerX,B+Q,"Z"],a.clipInnerBackPath=["M",w,u,"A",I,S,0,0,1,this.rightInnerX,B,"v",Q,"A",I,S,0,0,0,w,x,"Z"])):e<z?s>z?(a.clipOuterFrontPath=["M",p,C,"A",F,H,0,0,1,this.leftX,B,"v",Q,"A",F,H,0,0,0,p,P,"Z"],a.clipOuterBackPath=["M",this.leftX,B,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,this.leftX,B+Q,"Z"],this.isDoughnut&&(a.clipInnerFrontPath=["M",w,u,"A",I,S,0,0,1,this.leftInnerX,B,"v",Q,"A",I,S,0,0,0,w,x,"Z"],a.clipInnerBackPath=["M",this.leftInnerX,B,"A",I,S,0,0,1,K,A,"v",Q,
"A",I,S,0,0,0,this.leftInnerX,B+Q,"Z"])):(a.clipOuterFrontPath=["M",p,C,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,p,P,"Z"],a.clipOuterBackPath=this.clipPathforNoClip,this.isDoughnut&&(a.clipInnerFrontPath=["M",w,u,"A",I,S,0,0,1,K,A,"v",Q,"A",I,S,0,0,0,w,x,"Z"],a.clipInnerBackPath=this.clipPathforNoClip)):(a.clipOuterFrontPath=this.clipPathforNoClip,a.clipOuterBackPath=["M",p,C,"A",F,H,0,0,1,N,O,"v",Q,"A",F,H,0,0,0,p,P,"Z"],this.isDoughnut&&(a.clipInnerFrontPath=this.clipPathforNoClip,a.clipInnerBackPath=
["M",w,u,"A",I,S,0,0,1,K,A,"v",Q,"A",I,S,0,0,0,w,x,"Z"])):a.clipOuterFrontPath=a.clipOuterBackPath=a.clipInnerBackPath=a.clipInnerFrontPath=this.clipPathforNoClip,b||(a.elements.startSlice._conf.index=e,a.elements.endSlice._conf.index=s,a.elements.frontOuter._conf.index=Ka(s,e),a.elements.frontOuter1&&(a.elements.frontOuter1._conf.index=e,a.elements.frontOuter1.attr("litepath",[a.clipOuterFrontPath1])),a.thisElement.attr("litepath",[a.clipTopPath]),a.elements.bottom.attr("litepath",[a.clipTopPath]),
a.elements.bottomBorder.attr("litepath",[a.clipTopPath]),a.elements.topBorder&&a.elements.topBorder.attr("litepath",[a.clipTopBorderPath]),a.elements.frontOuter.attr("litepath",[a.clipOuterFrontPath]),a.elements.backOuter.attr("litepath",[a.clipOuterBackPath]),this.isDoughnut&&(a.elements.backInner.attr("litepath",[a.clipInnerBackPath]),a.elements.frontInner.attr("litepath",[a.clipInnerFrontPath]),a.elements.backInner._conf.index=Ka(s,e)),this.hasOnePoint?(a.elements.startSlice.hide(),a.elements.endSlice.hide()):
(a.elements.startSlice.attr("litepath",[a.startSlice]).show(),a.elements.endSlice.attr("litepath",[a.endSlice]).show()))):(n=this.moveCmdArr,m=this.lineCmdArr,v=this.closeCmdArr,E=this.centerPoint,h=this.leftPoint,t=this.topPoint,R=this.rightPoint,Q=this.bottomPoint,D=this.leftDepthPoint,y=this.rightDepthPoint,g=this.leftInnerPoint,f=this.rightInnerPoint,r=this.leftInnerDepthPoint,k=this.rightInnerDepthPoint,a.clipOuterFrontPath1=[],d!=l?(e>s?e<z?(d=c(J,B,p,C,this.leftX,B,F,H,1,0),l=c(J,B,this.leftX,
B,this.rightX,B,F,H,1,0),O=c(J,B,this.rightX,B,N,O,F,H,1,0),a.clipOuterBackPath=n.concat(h,l,m,y,c(J,T,this.rightX,T,this.leftX,T,F,H,0,0),v),a.clipOuterFrontPath1=n.concat([p,C],d,m,D,c(J,T,this.leftX,T,p,P,F,H,0,0),v),a.clipOuterFrontPath=n.concat(R,O,m,[N,ea],c(J,T,N,ea,this.rightX,T,F,H,0,0),v),a.clipTopBorderPath=n.concat([p,C],d,l,O),this.isDoughnut?(p=c(J,B,K,A,this.rightInnerX,B,I,S,0,0),C=c(J,B,this.rightInnerX,B,this.leftInnerX,B,I,S,0,0),u=c(J,B,this.leftInnerX,B,w,u,I,S,0,0),a.clipInnerBackPath=
n.concat(f,C,m,r,c(J,T,this.leftInnerX,T,this.rightInnerX,T,I,S,1,0),v),a.clipInnerFrontPath=n.concat(g,u,m,[w,x],c(J,T,w,x,this.leftInnerX,T,I,S,1,0),v,n,[K,A],p,m,k,c(J,T,this.rightInnerX,T,K,W,I,S,1,0),v),a.clipTopPath=a.clipTopBorderPath.concat(m,[K,A],p,C,u,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(n,[K,A],p,C,u)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)):s>z?(d=c(J,B,p,C,this.rightX,B,F,H,1,0),l=c(J,B,this.rightX,B,this.leftX,B,F,H,1,0),O=c(J,B,this.leftX,B,N,O,F,H,1,0),a.clipOuterFrontPath=
n.concat(R,l,m,D,c(J,T,this.leftX,T,this.rightX,T,F,H,0,0),v),a.clipOuterBackPath=n.concat([p,C],d,m,y,c(J,T,this.rightX,T,p,P,F,H,0,0),v,n,h,O,m,[N,ea],c(J,T,N,ea,this.leftX,T,F,H,0,0),v),a.clipTopBorderPath=n.concat([p,C],d,l,O),this.isDoughnut?(p=c(J,B,K,A,this.leftInnerX,B,I,S,0,0),C=c(J,B,this.leftInnerX,B,this.rightInnerX,B,I,S,0,0),u=c(J,B,this.rightInnerX,B,w,u,I,S,0,0),a.clipInnerFrontPath=n.concat(g,C,m,k,c(J,T,this.rightInnerX,T,this.leftInnerX,T,I,S,1,0),v),a.clipInnerBackPath=n.concat(f,
u,m,[w,x],c(J,T,w,x,this.rightInnerX,T,I,S,1,0),v,n,[K,A],p,m,r,c(J,T,this.leftInnerX,T,K,W,I,S,1,0),v),a.clipTopPath=a.clipTopBorderPath.concat(m,[K,A],p,C,u,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(n,[K,A],p,C,u)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)):(d=c(J,B,p,C,this.rightX,B,F,H,1,0),l=c(J,B,this.rightX,B,N,O,F,H,1,0),a.clipOuterFrontPath=n.concat(R,l,m,[N,ea],c(J,T,N,ea,this.rightX,T,F,H,0,0),v),a.clipOuterBackPath=n.concat([p,C],d,m,y,c(J,T,this.rightX,T,p,P,F,H,0,0),v),
a.clipTopBorderPath=n.concat([p,C],d,l),this.isDoughnut?(p=c(J,B,K,A,this.rightInnerX,B,I,S,0,0),C=c(J,B,this.rightInnerX,B,w,u,I,S,0,0),a.clipInnerFrontPath=n.concat([K,A],p,m,k,c(J,T,this.rightInnerX,T,K,W,I,S,1,0),v),a.clipInnerBackPath=n.concat(f,C,m,[w,x],c(J,T,w,x,this.rightInnerX,T,I,S,1,0),v),a.clipTopPath=a.clipTopBorderPath.concat(m,[K,A],p,C,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(n,[K,A],p,C)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)):e<z?s>z?(d=c(J,B,p,C,this.leftX,B,
F,H,1,0),l=c(J,B,this.leftX,B,N,O,F,H,1,0),a.clipOuterBackPath=n.concat(h,l,m,[N,ea],c(J,T,N,ea,this.leftX,T,F,H,0,0),v),a.clipOuterFrontPath=n.concat([p,C],d,m,D,c(J,T,this.leftX,T,p,P,F,H,0,0),v),a.clipTopBorderPath=n.concat([p,C],d,l),this.isDoughnut?(p=c(J,B,K,A,this.leftInnerX,B,I,S,0,0),C=c(J,B,this.leftInnerX,B,w,u,I,S,0,0),a.clipInnerBackPath=n.concat([K,A],p,m,r,c(J,T,this.leftInnerX,T,K,W,I,S,1,0),v),a.clipInnerFrontPath=n.concat(g,C,m,[w,x],c(J,T,w,x,this.leftInnerX,T,I,S,1,0),v),a.clipTopPath=
a.clipTopBorderPath.concat(m,[K,A],p,C,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(n,[K,A],p,C)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)):(d=c(J,B,p,C,N,O,F,H,1,0),a.clipOuterBackPath=n.concat([p,C]),a.clipTopBorderPath=a.clipOuterBackPath.concat(d),a.clipOuterFrontPath=a.clipTopBorderPath.concat(m,[N,ea],c(J,T,N,ea,p,P,F,H,0,0),v),this.isDoughnut?(p=c(J,B,K,A,w,u,I,S,0,0),a.clipInnerBackPath=n.concat([K,A]),a.clipTopPath=a.clipTopBorderPath.concat(m,[K,A],p,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(n,
[K,A],p),a.clipInnerFrontPath=a.clipInnerBackPath.concat(p,m,[w,x],c(J,T,w,x,K,W,I,S,1,0),v)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)):(d=c(J,B,p,C,N,O,F,H,1,0),a.clipOuterFrontPath=n.concat([p,C]),a.clipTopBorderPath=a.clipOuterFrontPath.concat(d),a.clipOuterBackPath=a.clipTopBorderPath.concat(m,[N,ea],c(J,T,N,ea,p,P,F,H,0,0),v),this.isDoughnut?(p=c(J,B,K,A,w,u,I,S,0,0),a.clipInnerFrontPath=n.concat([K,A]),a.clipTopPath=a.clipTopBorderPath.concat(m,[K,A],p,v),a.clipTopBorderPath=a.clipTopBorderPath.concat(a.clipInnerFrontPath,
p),a.clipInnerBackPath=a.clipInnerFrontPath.concat(p,m,[w,x],c(J,T,w,x,K,W,I,S,1,0),v)):a.clipTopPath=a.clipTopBorderPath.concat(m,E,v)),d=n.concat(h,m,R),p=n.concat(t,m,Q),a.clipTopPath=a.clipTopPath.concat(d,p),a.clipOuterFrontPath=a.clipOuterFrontPath.concat(d),a.clipOuterFrontPath1=a.clipOuterFrontPath1.concat(d),a.clipOuterBackPath=a.clipOuterBackPath.concat(d),this.isDoughnut&&(p=n.concat(g,m,f),a.clipInnerFrontPath=a.clipInnerFrontPath.concat(p),a.clipInnerBackPath=a.clipInnerBackPath.concat(p))):
(a.clipTopPath=a.clipOuterFrontPath=a.clipOuterBackPath=[],this.isDoughnut&&(a.clipInnerFrontPath=a.clipInnerBackPath=[])),b||(a.elements.startSlice._conf.index=e,a.elements.endSlice._conf.index=s,a.elements.frontOuter._conf.index=Ka(s,e),a.elements.frontOuter1&&(a.elements.frontOuter1._conf.index=e,L.frontOuter1.attr({path:a.clipOuterFrontPath1})),a.thisElement.attr({path:a.clipTopPath}),L.topBorder.attr({path:a.clipTopBorderPath}),L.bottom.attr({path:a.clipTopPath}),L.bottomBorder.attr({path:a.clipTopBorderPath}),
L.frontOuter.attr({path:a.clipOuterFrontPath}),L.backOuter.attr({path:a.clipOuterBackPath}),this.isDoughnut&&(L.frontInner.attr({path:a.clipInnerFrontPath}),L.backInner.attr({path:a.clipInnerBackPath})),this.hasOnePoint?(a.elements.startSlice.hide(),a.elements.endSlice.hide()):(a.elements.startSlice.attr({path:a.startSlice}).show(),a.elements.endSlice.attr({path:a.endSlice}).show())))},onPlotHover:function(a,b){var c=this.pointElemStore[a]._confObject,d=c.thisElement,l=c.elements,e=this.colorObjs[a],
s=e.hoverProps,g=b?s.hoverColorObj:e.color,f=e.showBorderEffect,r=b?s.borderColor:e.borderColor,e=b?s.borderWidth:e.borderWidth;ma?(s={fill:G(g.top),"stroke-width":0},1!==f&&(s.stroke=r,s["stroke-width"]=e),d._attr(s),f&&l.topBorder.attr({fill:G(g.topBorder),"stroke-width":0})):(d._attr({fill:G(g.top),"stroke-width":0}),l.topBorder.attr({stroke:r,"stroke-width":e}));l.bottom.attr({fill:G(g.bottom),"stroke-width":0});l.bottomBorder.attr({stroke:r,"stroke-width":e});l.frontOuter.attr({fill:G(g.frontOuter),
"stroke-width":0});l.backOuter.attr({fill:G(g.backOuter),"stroke-width":0});l.startSlice.attr({fill:G(g.startSlice),stroke:r,"stroke-width":e});l.endSlice.attr({fill:G(g.endSlice),stroke:r,"stroke-width":e});d=fa(c.start);c=fa(c.end);(d>c?V:0)+c-d>z&&l.frontOuter1.attr({fill:G(g.frontOuter),"stroke-width":0});this.isDoughnut&&(l.frontInner.attr({fill:G(g.frontInner),"stroke-width":0}),l.backInner.attr({fill:G(g.backInner),"stroke-width":0}))},createSlice:function(){var a={stroke:!0,strokeWidth:!0,
"stroke-width":!0,dashstyle:!0,"stroke-dasharray":!0,translateX:!0,translateY:!0,"stroke-opacity":!0,transform:!0,fill:!0,opacity:!0,ishot:!0,start:!0,end:!0,cursor:!0},b=function(b,c){var d,e,l=this,q=l._confObject,g,f=q.elements,k,m,n=q.Pie3DManager;"string"===typeof b&&void 0!==c&&null!==c&&(d=b,b={},b[d]=c);if(b&&"string"!==typeof b){void 0!==b.cx&&(b.start=b.cx);void 0!==b.cy&&(b.end=b.cy);for(d in b)if(e=b[d],a[d])if(q[d]=e,"ishot"===d||"cursor"===d){g={};g[d]=e;for(k in f)f[k].attr(g);l._attr(g)}else if("transform"===
d){for(k in f)f[k].attr({transform:b[d]});l._attr({transform:b[d]})}else"stroke"===d||"strokeWidth"===d||"stroke-width"===d||"dashstyle"===d||"stroke-dasharray"===d?(g={},g[d]=e,f.topBorder&&f.topBorder.attr(g),f.startSlice.attr(g),f.endSlice.attr(g),f.bottomBorder.attr(g)):"fill"===d||"start"!==d&&"end"!==d||(m=!0);else l._attr(d,e);m&&(n.updateSliceConf(q),n.refreshDrawing())}else l=l._attr(b);return l},c=function(a,b,c,d){var e=this._confObject.elements,l;for(l in e)if(c)e[l].drag(b,c,d);else e[l].on(a,
b);return c?this.drag(b,c,d):this._on(a,b)},d=function(){var a=this._confObject.elements,b;for(b in a)a[b].hide();return this._hide()},e=function(){var a=this._confObject.elements,b;for(b in a)a[b].show();return this._show()},g=function(){var a=this._confObject,b=a.elements,c;for(c in b)b[c].destroy();ma&&(a.clipTop.destroy(),a.clipOuterFront.destroy(),a.clipOuterBack.destroy(),a.clipOuterFront1&&a.clipOuterFront1.destroy(),a.clipInnerFront&&a.clipInnerFront.destroy(),a.clipInnerBack&&a.clipInnerBack.destroy());
return this._destroy()};return function(a,f,k,r,m,p,n,$,Y,v){var h=this.renderer;k=this.parseColor(k,r);a={start:a,end:f,elements:{},Pie3DManager:this};f=this.slicingWallsArr;r=a.elements;var w,u=ma?"litepath":"path";v&&(this.colorObjs[n]={color:k,borderColor:m,borderWidth:p,showBorderEffect:!1},v.hoverColorObj=this.parseColor(v.color,v.alpha),this.colorObjs[n].hoverProps=v);this.updateSliceConf(a,!0);ma?(v={fill:G(k.top),"stroke-width":0},1!==Y&&(v.stroke=m,v["stroke-width"]=p),v=h[u](a.clipTopPath,
this.topGroup).attr(v),Y&&(r.topBorder=h[u](a.clipTopBorderPath,this.topGroup).attr({fill:G(k.topBorder),"stroke-width":0}))):(v=h[u](a.clipTopPath,this.topGroup).attr({fill:G(k.top),"stroke-width":0}),r.topBorder=h[u](a.clipTopBorderPath,this.topGroup).attr({stroke:m,"stroke-width":p}));r.bottom=h[u](a.clipTopPath,this.bottomBorderGroup).attr({fill:G(k.bottom),"stroke-width":0});r.bottomBorder=h[u](ma?a.clipTopPath:a.clipTopBorderPath,this.bottomBorderGroup).attr({stroke:m,"stroke-width":p});r.frontOuter=
h[u](a.clipOuterFrontPath,this.slicingWallsFrontGroup).attr({fill:G(k.frontOuter),"stroke-width":0});r.backOuter=h[u](a.clipOuterBackPath,this.outerBackGroup).attr({fill:G(k.backOuter),"stroke-width":0});r.startSlice=h[u](a.startSlice,this.slicingWallsFrontGroup).attr({fill:G(k.startSlice),stroke:m,"stroke-width":p});r.endSlice=h[u](a.endSlice,this.slicingWallsFrontGroup).attr({fill:G(k.endSlice),stroke:m,"stroke-width":p});m=fa(a.start);p=fa(a.end);Y=(m>p?V:0)+p-m;Y>z&&(r.frontOuter1=h[u](a.clipOuterFrontPath1,
this.slicingWallsFrontGroup).attr({fill:G(k.frontOuter),"stroke-width":0}),r.frontOuter1._conf={index:m,isStart:.5,pIndex:n},ma&&(a.clipOuterFront1=a.clipOuterFrontPath1));r.frontOuter._conf={index:Ka(p,m),isStart:.5,pIndex:n};r.startSlice._conf={index:m,isStart:0,pIndex:n};r.endSlice._conf={index:p,isStart:1,pIndex:n};this.hasOnePoint&&(r.startSlice.hide(),r.endSlice.hide());this.isDoughnut?(r.frontInner=h[u](a.clipInnerFrontPath,this.innerFrontGroup).attr({fill:G(k.frontInner),"stroke-width":0}),
r.backInner=h[u](a.clipInnerBackPath,this.innerBackGroup).attr({fill:G(k.backInner),"stroke-width":0}),r.backInner._conf={index:Ka(p,m),isStart:.5,pIndex:n},Y>z?ma?f.push(r.startSlice,r.frontOuter1,r.frontOuter,r.backInner,r.endSlice):f.push(r.startSlice,r.frontOuter1,r.frontOuter,r.endSlice):ma?f.push(r.startSlice,r.frontOuter,r.backInner,r.endSlice):f.push(r.startSlice,r.frontOuter,r.endSlice)):Y>z?f.push(r.startSlice,r.frontOuter1,r.frontOuter,r.endSlice):f.push(r.startSlice,r.frontOuter,r.endSlice);
if(void 0!==$){for(w in r)r[w].tooltip($);v.tooltip($)}ma&&(a.clipTop=a.clipTopPath,a.clipOuterFront=a.clipOuterFrontPath,a.clipOuterBack=a.clipOuterBackPath,this.isDoughnut&&(a.clipInnerFront=a.clipInnerFrontPath,a.clipInnerBack=a.clipInnerBackPath));v._confObject=a;a.thisElement=v;v._destroy=v.destroy;v.destroy=g;v._show=v.show;v.show=e;v._hide=v.hide;v.hide=d;v._on=v.on;v.on=c;v._attr=v.attr;v.attr=b;this.pointElemStore.push(v);return v}}()};La.prototype.constructor=La;m("renderer.pie3d",{type:"pie3d",
isHovered:!1,translate:function(){var a=0,b=this.options,c=b.series[0],d=b.plotOptions.series.dataLabels,e=b.plotOptions.pie3d,g=h(c.startAngle,0)%360,s=c.managedPieSliceDepth,m=c.slicedOffset=e.slicedOffset,n=this.canvasWidth,r=this.canvasHeight,X=[this.canvasLeft+.5*n,this.canvasTop+.5*r-.5*s],p,C,z,x,v,b=c.data,t,w=R(n,r),ea,K,A,P=d.distance,L=c.pieYScale,J=c.pieSliceDepth,B=c.slicedOffsetY=m*L;X.push(e.size,e.innerSize||0);X=ta(X,function(a,b){return(ea=/%$/.test(a))?[n,r-s,w,w][b]*parseInt(a,
10)/100:a});X[2]/=2;X[3]/=2;X.push(X[2]*L);X.push((X[2]+X[3])/2);X.push(X[5]*L);c.getX=function(a,b){z=k.asin((a-X[1])/(X[2]+P));return X[0]+(b?-1:1)*Y(z)*(X[2]+P)};c.center=X;ya(b,function(b){a+=b.y});c.labelsRadius=X[2]+P;c.labelsRadiusY=c.labelsRadius*L;c.quadrantHeight=(r-s)/2;c.quadrantWidth=n/2;x=-g*ja;x=W(1E3*x)/1E3;v=x+V;e=f(parseInt(d.style.fontSize,10),10)+4;c.maxLabels=u(c.quadrantHeight/e);c.labelFontSize=e;c.connectorPadding=f(d.connectorPadding,5);c.isSmartLineSlanted=h(d.isSmartLineSlanted,
!0);c.connectorWidth=f(d.connectorWidth,1);c.enableSmartLabels=d.enableSmartLabels;c.Pie3DManager||(c.Pie3DManager=new La(X[0],X[1],X[2],X[3],L,J,this.layers.dataset,this.paper,1===c.data.length,c.use3DLighting));ya(b,function(b){p=x;t=a?b.y/a:0;x=W(1E3*(x+t*V))/1E3;x>v&&(x=v);C=x;b.shapeArgs={start:W(1E3*p)/1E3,end:W(1E3*C)/1E3};b.centerAngle=z=(C+p)/2%V;b.slicedTranslation=[W(Y(z)*m),W($(z)*B)];K=Y(z)*X[2];c.radiusY=A=$(z)*X[4];b.tooltipPos=[X[0]+.7*K,X[1]+A];b.percentage=100*t;b.total=a})},drawPlotPie3d:function(a,
b){this.translate();var c=this,d=a.items,e=a.data,g=c.options,k=g.plotOptions,m=k.series,n=c.layers,r=c.elements.plots[0],h=c.datasets[0],k=k.series.dataLabels,p=m.dataLabels.style,m=f(a.moveDuration,m.animation.duration),u=c.paper,z=g.tooltip||{},z=z&&!1!==z.enabled,x=h.slicedOffset,v=h.slicedOffsetY,W=c.plotGraphicClick,w=c.plotDragMove,t=c.plotDragStart,K=c.plotDragEnd,A=c.plotMouseDown,L=c.plotMouseUp,P=c.plotRollOver,J=c.plotRollOut,B=!!c.datasets[0].enableRotation,F=b.showBorderEffect,H=e.length,
g=g.chart.usePerPointLabelColor,R={fontFamily:p.fontFamily,fontSize:p.fontSize,lineHeight:p.lineHeight,fontWeight:p.fontWeight,fontStyle:p.fontStyle},ea=function(a){return function(){c.legendClick(a,!0,!1)}},I=function(a){return function(){return c.getEventArgs(a)}},S=function(a){return function(b,c,d,e,l){w.call(a,b,c,d,e,l)}},Q=function(a){return function(b,c,d){t.call(a,b,c,d)}},T=function(a){return function(){K.call(a)}},V=function(a){return function(){A.call(a)}},y=function(a){return function(b){L.call(a,
b)}},N=function(a){return function(b){J.call(a,b)}},O=function(a){return function(b){P.call(a,b)}},M,E,D,ja,aa,Z,ia,U,va,ba,ca,da;e&&H||(e=[]);r.singletonCase=1===H;r.chartPosition=Ca(c.container);r.pieCenter=h.center;r.timerThreshold=30;for(da=-1;++da<H;)D=e[da],M=D.y,ja=D.displayValue,Z=D.sliced,ba=D.shapeArgs,U=D.centerAngle,ca=D.toolText,ia=(aa=!!D.link)||B||!D.doNotSlice,null===M||void 0===M||(E=d[da])||(b.data[da].plot=E=d[da]={chart:c,index:da,seriesData:r,value:M,angle:U,link:D.link,shapeArgs:ba,
slicedX:Z&&!r.singletonCase?Y(U)*x:0,slicedY:Z&&!r.singletonCase?$(U)*v:0,sliced:Z,labelText:ja,name:D.name,label:D.name,percentage:D.percentage,toolText:ca,originalIndex:H-da-1,graphic:h.Pie3DManager.createSlice(ba.start,ba.end,D.color,D._3dAlpha,D.borderColor,D.borderWidth,da,z?ca:"",F,D.rolloverProperties)},b.data[da].legendClick=ea(E),b.data[da].getEventArgs=I(E),E.graphic.plotItem=E,E.graphic.data("plotItem",E),E.transX=Y(U)*x,E.transY=$(U)*v,E.slicedTranslation="t"+E.transX+","+E.transY,M={index:b.reversePlotOrder?
da:H-1-da,link:D.link,value:D.y,displayValue:D.displayValue,categoryLabel:D.categoryLabel,isSliced:D.sliced,toolText:D.toolText},E.graphic.attr({transform:"t"+E.slicedX+","+E.slicedY,ishot:ia,cursor:aa?"pointer":""}).click(W).drag(S(E),Q(E),T(E)).mousedown(V(E.graphic)).mouseup(y(E.graphic)).data("groupId",da).data("eventArgs",M).mouseover(O(E)).mouseout(N(E)),void 0!==ja&&(E.dataLabel=u.text(n.dataset).css(R).attr({text:ja,title:D.originalText||"",fill:(g?G(D.color):p.color)||"#000000","text-bound":[p.backgroundColor,
p.borderColor,p.borderThickness,p.borderPadding,p.borderRadius,p.borderDash],visibility:"hidden",ishot:ia,cursor:aa?"pointer":""}).data("eventArgs",M).hover(O(E),N(E)).click(W).mousedown(A,E.dataLabel).mouseup(L,E.dataLabel).data("plotItem",E),0<k.distance&&(va=k.connectorWidth)&&k.enableSmartLabels&&(E.connector=u.path("M 0 0 l 0 0",n.dataset).attr({"stroke-width":va,stroke:k.connectorColor||"#606060",visibility:"hidden",ishot:ia,cursor:aa?"pointer":""}).data("eventArgs",M).click(W).hover(O(E),N(E)).mousedown(A,
E.connector).mouseup(L,E.connector).data("plotItem",E))));h.Pie3DManager.refreshDrawing();0<m?c.animate(d,m):c.placeDataLabels(!1,d)},rotate:function(a){var b=this.datasets[0],c=this.elements.plots[0].items,d=b.slicedOffset,e=b.slicedOffsetY,g=b.startAngle,k;a=isNaN(a)?-b._lastAngle:a;k=(a-g)%360;b.startAngle=f(a,b.startAngle)%360;k=-(k*L)/180;b.Pie3DManager&&b.Pie3DManager.rotate(k);ya(c,function(a){var b=a.graphic,c=a.shapeArgs,q=c.start+=k,c=c.end+=k,g=a.angle=fa((q+c)/2),q=a.sliced,c=Y(g),g=$(g);
a.slicedTranslation=[W(c*d),W(g*e)];a.transX=a.slicedTranslation[0];a.transY=a.slicedTranslation[1];a.slicedX=q?Y(k)*d:0;a.slicedY=q?$(k)*e:0;b&&q&&a.graphic.attr({transform:"t"+a.slicedTranslation[0]+","+a.slicedTranslation[1]})});this.placeDataLabels(!0,c)},plotRollOver:function(a){var b=this.chart,c=b.datasets[0].Pie3DManager;this.seriesData.isRotating||(xa.call(this.graphic,b,a,"DataPlotRollOver"),c.colorObjs[this.index]&&c.onPlotHover(this.index,!0));b.isHovered=!0},plotRollOut:function(a){var b=
this.chart,c=b.datasets[0].Pie3DManager;this.seriesData.isRotating||(xa.call(this.graphic,b,a,"DataPlotRollOut"),c.colorObjs[this.index]&&c.onPlotHover(this.index,!1));b.isHovered=!1},plotDragStart:function(a,b,c){var d=this.seriesData,e=this.chart.datasets[0];e.enableRotation&&(a=Ma.call(c,a,b,d.pieCenter,d.chartPosition,e.pieYScale),e.dragStartAngle=a,e._lastAngle=-e.startAngle,e.startingAngleOnDragStart=e.startAngle)},plotDragEnd:function(){var a=this.chart,b=a.datasets[0],c=b.Pie3DManager,d=b.startAngle,
e=this.seriesData,g={hcJSON:{series:[{startAngle:d}]}};a.disposed||Aa(a.logic.chartInstance.jsVars._reflowData,g,!0);e.isRotating&&(setTimeout(function(){e.isRotating=!1},0),qa.raiseEvent("rotationEnd",{startingAngle:fa(d,!0),changeInAngle:d-b.startingAngleOnDragStart},a.logic.chartInstance),!a.isHovered&&c.colorObjs[this.index]&&c.onPlotHover(this.index,!1))},plotDragMove:function(a,b,c,d,e){var g=this.chart;a=g.datasets[0];b=this.seriesData;g.options.series[0].enableRotation&&!b.singletonCase&&
(b.isRotating||(b.isRotating=!0,qa.raiseEvent("rotationStart",{startingAngle:fa(a.startAngle,!0)},g.logic.chartInstance)),c=Ma.call(e,c,d,b.pieCenter,b.chartPosition,a.pieYScale),d=c-a.dragStartAngle,a.dragStartAngle=c,b.moveDuration=0,a._lastAngle+=180*d/L,c=(new Date).getTime(),!a._lastTime||a._lastTime+b.timerThreshold<c)&&(a._lastTime||g.rotate(),b.timerId=setTimeout(function(){g.disposed&&g.disposing||g.rotate()},b.timerThreshold),a._lastTime=c)},animate:function(a,b){var c,d,e,g=a.length,k,
f,m,n=this,h,p=function(){n.disposed||n.disposing||n.placeDataLabels(!1,a)};if(n.datasets[0].alphaAnimation)n.layers.dataset.attr({opacity:0}),n.layers.dataset.animate({opacity:1},b,"ease-in",function(){n.disposed||n.disposing||n.placeDataLabels(!1,a)});else for(c=0;c<g;c++)k=a[c],f=k.graphic,m=k.shapeArgs,k=2*L,f&&(f.attr({start:k,end:k}),h=m.start,m=m.end,d?f.animateWith(d,e,{cx:h-k,cy:m-k},b,"ease-in"):(e=ra.animation({cx:h-k,cy:m-k},b,"ease-in",p),d=f.animate(e)))},placeDataLabels:function(){var a=
function(a,b){return a.point.value-b.point.value},b=function(a,b){return a.angle-b.angle},c=["start","start","end","end"],d=[-1,1,1,-1],e=[1,1,-1,-1];return function(m,s){var h=this.datasets[0],u=this.smartLabel,r=this.options.plotOptions.series.dataLabels,X=r.style,p=f(x(parseFloat(X.lineHeight)),12),C=Ga(r.placeInside,!1),t=r.skipOverlapLabels,L=r.manageLabelOverflow,v=r.connectorPadding,ea=r.connectorWidth,w,M,K=0<r.distance,A=h.center,y=A[1],ja=A[0],J=A[2],B=A[4],F=[[],[],[],[]],H,G,aa,I=this.canvasLeft,
S=this.canvasTop,Q=this.canvasWidth,T,Z,U,N,O,ia,E,D,da,ba,ca,ka=h.labelsRadius,fa=W(100*h.labelsRadiusY)/100,pa=h.labelFontSize,sa=pa,ha=sa/2,v=[v,v,-v,-v],ma=h.maxLabels,la=h.isSmartLineSlanted,ra=h.enableSmartLabels,oa,h=h.pieSliceDepth/2;m||u.setStyle(X);if(1==s.length)N=s[0],oa=N.dataLabel,N.slicedTranslation=[I,S],oa&&(oa.attr({visibility:g,"text-anchor":"middle",x:ja,y:y+ha-2}),oa.x=ja);else if(C)ya(s,function(a){if(oa=a.dataLabel){ca=a.angle;ba=y+A[6]*$(ca)+ha-2;E=ja+A[5]*Y(ca);oa.x=E;oa._x=
E;oa.y=ba;if(a.sliced){a=a.slicedTranslation;var b=a[1]-S;E+=a[0]-I;ba+=b}oa.attr({visibility:g,align:"middle",x:E,y:ba})}});else{ya(s,function(a){if(oa=a.dataLabel)ca=a.angle,0>ca&&(ca=V+ca),H=0<=ca&&ca<Oa?1:ca<z?2:ca<va?3:0,F[H].push({point:a,angle:ca})});for(aa=C=4;aa--;){if(t&&(N=F[aa].length-ma,0<N))for(F[aa].sort(a),G=F[aa].splice(0,N),Z=0,U=G.length;Z<U;Z+=1)N=G[Z].point,N.dataLabel.attr({visibility:"hidden"}),N.connector&&N.connector.attr({visibility:"hidden"});F[aa].sort(b)}aa=n(F[0].length,
F[1].length,F[2].length,F[3].length);fa=n(R(aa,ma)*sa,fa+sa);F[1].reverse();F[3].reverse();for(u.setStyle(X);C--;){Z=F[C];U=Z.length;t||(sa=U>ma?fa/U:pa,ha=sa/2);N=U*sa;X=fa;for(aa=0;aa<U;aa+=1,N-=sa)M=P(fa*$(Z[aa].angle)),X<M?M=X:M<N&&(M=N),X=(Z[aa].oriY=M)-sa;G=c[C];U=fa-(U-1)*sa;X=0;for(aa=Z.length-1;0<=aa;aa-=1,U+=sa)N=Z[aa].point,ca=Z[aa].angle,O=N.sliced,oa=N.dataLabel,M=P(fa*$(ca)),M<X?M=X:M>U&&(M=U),X=M+sa,D=(M+Z[aa].oriY)/2,M=ja+e[C]*ka*Y(k.asin(D/fa)),D*=d[C],D+=y,da=y+B*$(ca),ia=ja+J*Y(ca),
(2>C&&M<ia||1<C&&M>ia)&&(M=ia),E=M+v[C],ba=D+ha-2,w=E+v[C],oa.x=w,oa._x=w,L&&(T=1<C?w-this.canvasLeft:this.canvasLeft+Q-w,T=u.getSmartText(N.labelText,T,p),oa.attr({text:T.text,title:T.tooltext||""})),ca<z&&(D+=h,da+=h,ba+=h),oa.y=ba,O&&(O=N.transX,T=N.transY,E+=O,M+=O,ia+=O,da+=T,w+=O),oa.attr({visibility:g,"text-anchor":G,x:w,y:D}),K&&ea&&ra&&(w=N.connector,N.connectorPath=M=["M",ia,da,"L",la?M:ia,D,E,D],w?(w.attr({path:M}),w.attr("visibility",g)):N.connector=w=this.paper.path(M).attr({"stroke-width":ea,
stroke:r.connectorColor||"#606060",visibility:g}))}}}}()},m["renderer.piebase"]);m("renderer.pie",{drawDoughnutCenterLabel:function(a,b,c,d,e,k,f){var m=this.options.series[0];k=k||m.lastCenterLabelConfig;var n=this.paper,r=this.smartLabel,h=this.layers.dataset,p=this.elements,$=k.padding,u=2*k.textPadding,Y={fontFamily:k.font,fontSize:k.fontSize+"px",lineHeight:1.2*k.fontSize+"px",fontWeight:k.bold?"bold":"",fontStyle:k.italic?"italic":""},v=1.414*(.5*d-$)-u;e=1.414*(.5*e-$)-u;var z;r.setStyle(Y);
r=r.getSmartText(a,v,e);(e=p.doughnutCenterLabel)?(e.attr("text")!==a&&this.centerLabelChange(a),z=p.centerLabelOvalBg):(k.bgOval&&(p.centerLabelOvalBg=z=n.circle(b,c,.5*d-$,h)),e=p.doughnutCenterLabel=n.text(h).hover(this.centerLabelRollover,this.centerLabelRollout).click(this.centerLabelClick),e.chart=this);a?(e.css(Y).attr({x:b,y:c,text:r.text,visibility:g,title:k.toolText?"":r.tooltext||"",fill:G({FCcolor:{color:k.color,alpha:k.alpha}}),"text-bound":k.bgOval?"none":[G({FCcolor:{color:k.bgColor,
alpha:k.bgAlpha}}),G({FCcolor:{color:k.borderColor,alpha:k.borderAlpha}}),k.borderThickness,k.textPadding,k.borderRadius]}).tooltip(k.toolText),k.bgOval&&z&&z.attr({visibility:g,fill:Ya(k.bgColor),"fill-opacity":k.bgAlpha/100,stroke:Ya(k.borderColor),"stroke-width":k.borderThickness,"stroke-opacity":k.borderAlpha/100})):(e.attr("visibility","hidden"),z&&z.attr("visibility","hidden"));f&&(m.lastCenterLabelConfig=k)},centerLabelRollover:function(){var a=this.chart,b=a.fusionCharts,c=a.options.series[0].lastCenterLabelConfig,
b={height:b.args.height,width:b.args.width,pixelHeight:b.ref.offsetHeight,pixelWidth:b.ref.offsetWidth,id:b.args.id,renderer:b.args.renderer,container:b.options.containerElement,centerLabelText:c&&c.label};this.attr("text")&&qa.raiseEvent("centerLabelRollover",b,a.logic.chartInstance,this,a.hoverOnCenterLabel)},centerLabelRollout:function(){var a=this.chart,b=a.fusionCharts,c=a.options.series[0].lastCenterLabelConfig,b={height:b.args.height,width:b.args.width,pixelHeight:b.ref.offsetHeight,pixelWidth:b.ref.offsetWidth,
id:b.args.id,renderer:b.args.renderer,container:b.options.containerElement,centerLabelText:c&&c.label};this.attr("text")&&qa.raiseEvent("centerLabelRollout",b,a.logic.chartInstance,this,a.hoverOffCenterLabel)},centerLabelClick:function(){var a=this.chart,b=a.fusionCharts,c=a.options.series[0].lastCenterLabelConfig,b={height:b.args.height,width:b.args.width,pixelHeight:b.ref.offsetHeight,pixelWidth:b.ref.offsetWidth,id:b.args.id,renderer:b.args.renderer,container:b.options.containerElement,centerLabelText:c&&
c.label};this.attr("text")&&qa.raiseEvent("centerLabelClick",b,a.logic.chartInstance)},centerLabelChange:function(a){var b=this.fusionCharts;qa.raiseEvent("centerLabelChanged",{height:b.args.height,width:b.args.width,pixelHeight:b.ref.offsetHeight,pixelWidth:b.ref.offsetWidth,id:b.args.id,renderer:b.args.renderer,container:b.options.containerElement,centerLabelText:a},this.logic.chartInstance)},hoverOnCenterLabel:function(){var a=this.chart.options.series[0].lastCenterLabelConfig;(a.hoverColor||a.hoverAlpha)&&
this.attr({fill:G({FCcolor:{color:a.hoverColor||a.color,alpha:a.hoverAlpha||a.alpha}})})},hoverOffCenterLabel:function(){var a=this.chart.options.series[0].lastCenterLabelConfig;(a.hoverColor||a.hoverAlpha)&&this.attr({fill:G({FCcolor:{color:a.color,alpha:a.alpha}})})},drawPlotPie:function(a,b){var c=this,d=a.items,e=a.data,k=c.options,m=k.series[0],n=k.plotOptions,h=n.pie,r=n.series,u=c.layers,p=u.dataset,C=c.elements.plots[0],n=n.series.dataLabels,x=r.dataLabels.style,W=r.shadow,r=f(a.moveDuration,
r.animation.duration),v=c.paper,t=k.tooltip||{},t=t&&!1!==t.enabled,w,L=((b.startAngle*=-z/180)||0)%V,K=h.slicedOffset,A=b.valueTotal,M=V/A,ea=c.canvasLeft+.5*c.canvasWidth,J=c.canvasTop+.5*c.canvasHeight,B=.5*h.size,h=.5*(h.innerSize||0),F=c.plotGraphicClick,H=c.plotDragMove,P=c.plotDragStart,R=c.plotDragEnd,I=c.plotMouseDown,S=c.plotMouseUp,Q=c.plotRollOver,T=c.plotRollOut,aa=!!c.datasets[0].enableRotation,y=e.length,k=k.chart.usePerPointLabelColor,N=m.centerLabelConfig,O=N.label,ja={fontFamily:x.fontFamily,
fontSize:x.fontSize,lineHeight:x.lineHeight,fontWeight:x.fontWeight,fontStyle:x.fontStyle},E,D,Z,U,ia,ca,da,ba,va,fa,sa,la=a.shadowGroup,ka,oa,ha,na,pa,ma=function(a){return function(){c.legendClick(a,!0,!1)}},qa=function(a){return function(){return c.getEventArgs(a)}},Oa=function(){c.disposed||c.disposing||c.paper.ca.redrawDataLabels||(c.placeDataLabels(!1,d,a),c.paper.ca.redrawDataLabels=c.redrawDataLabels)};e&&y||(e=[]);la||(la=a.shadowGroup=v.group(p).toBack());C.singletonCase=1===y;C.chartPosition||
(C.chartPosition=Ca(c.container));C.pieCenter=[ea,J];C.timerThreshold=30;fa=va=L;for(ka=y;ka--;)D=e[ka],Z=D.y,U=D.displayValue,ca=D.sliced,w=D.toolText,da=(ia=!!D.link)||aa||!D.doNotSlice,null!==Z&&void 0!==Z&&(E=D.color.FCcolor,E.r=B,E.cx=ea,E.cy=J,D.rolloverProperties&&(E=D.rolloverProperties.color.FCcolor,E.r=B,E.cx=ea,E.cy=J),fa=va,va-=C.singletonCase?V:Z*M,ba=.5*(va+fa),r?na=pa=L:(na=va,pa=fa),(E=d[ka])||(b.data[ka].plot=E=d[ka]={chart:c,index:ka,seriesData:C,value:Z,angle:ba,slicedX:Y(ba)*K,
slicedY:$(ba)*K,sliced:ca,labelText:U,toolText:w,label:D.name,link:D.link,percentage:A?Z*A/100:0,originalIndex:y-ka-1,color:D.color,borderColor:D.borderColor,borderWidth:D.borderWidth,rolloverProperties:D.rolloverProperties,center:[ea,J],innerDiameter:2*h,centerLabelConfig:D.centerLabelConfig,graphic:v.ringpath(ea,J,B,h,na,pa,u.dataset).attr({"stroke-width":D.borderWidth,"stroke-linejoin":"round",stroke:D.borderColor,fill:G(D.color),"stroke-dasharray":D.dashStyle,redrawDataLabels:L,ishot:da,cursor:ia?
"pointer":""}).shadow(W&&D.shadow,la).drag(H,P,R).mousedown(I).mouseup(S).hover(Q,T)},E.graphic.click(F),t&&E.graphic.tooltip(w),b.data[ka].legendClick=ma(E),b.data[ka].getEventArgs=qa(E),E.graphic.data("plotItem",E),w={index:b.reversePlotOrder?ka:y-1-ka,link:D.link,value:D.y,displayValue:D.displayValue,categoryLabel:D.categoryLabel,isSliced:D.sliced,toolText:D.toolText},E.graphic.data("eventArgs",w),void 0!==U&&(E.dataLabel=v.text(p).css(ja).attr({text:U,fill:(k?G(D.color):x.color)||"#000000","text-bound":[x.backgroundColor,
x.borderColor,x.borderThickness,x.borderPadding,x.borderRadius,x.borderDash],ishot:da,visibility:"hidden"}).click(F).drag(H,P,R).mousedown(I).mouseup(S).hover(Q,T).data("eventArgs",w).hide(),E.dataLabel.data("plotItem",E),0<n.distance&&(sa=n.connectorWidth)&&n.enableSmartLabels&&(E.connector=v.path("M 0 0 l 0 0",p).attr({"stroke-width":sa,stroke:n.connectorColor||"#606060",visibility:g,ishot:!0}).click(F).data("eventArgs",w).drag(H,P,R).mousedown(I).mouseup(S).hover(Q,T),E.connector.data("plotItem",
E)))),E.angle=ba,E.transX=Y(ba)*K,E.transY=$(ba)*K,E.slicedTranslation="t"+Y(ba)*K+","+$(ba)*K,r?oa?E.graphic.animateWith(oa,ha,{ringpath:[ea,J,B,h,va,fa],transform:E.sliced?E.slicedTranslation:""},r,"easeIn"):(ha=ra.animation({ringpath:[ea,J,B,h,va,fa],redrawDataLabels:c,transform:E.sliced?E.slicedTranslation:""},r,"easeIn",Oa),oa=E.graphic.animate(ha)):E.graphic.attr({transform:E.sliced?E.slicedTranslation:""}));O&&h&&c.drawDoughnutCenterLabel(O,ea,J,2*h,2*h,N,!0);m.lastCenterLabelConfig=N;r?m.doughnutCenterLabel&&
m.doughnutCenterLabel.attr({"fill-opacity":0}).animate(ra.animation({"fill-opacity":100},100).delay(100<r?r-100:0)):c.placeDataLabels(!1,d,a)},rotate:function(a,b){var c=a.items,d=a.data,e=this.options.plotOptions.pie,g=e.slicedOffset,k=V/b.valueTotal,f=this.canvasLeft+.5*this.canvasWidth,m=this.canvasTop+.5*this.canvasHeight,n=.5*e.size,e=.5*(e.innerSize||0),h,p,u,x,z;u=(b.startAngle||0)%V;for(z=d.length;z--;)h=d[z],p=h.y,null!==p&&void 0!==p&&(h=c[z],x=u,u-=h.seriesData.singletonCase?V:p*k,p=.5*
(u+x),h.angle=p,h.transX=Y(p)*g,h.transY=$(p)*g,h.slicedTranslation="t"+Y(p)*g+","+$(p)*g,h.graphic.attr({ringpath:[f,m,n,e,u,x],transform:h.sliced?h.slicedTranslation:""}));this.placeDataLabels(!0,c,a)}},m["renderer.piebase"])},[3,2,2,"sr4"]]);
FusionCharts.register("module",["private","modules.renderer.js-zoomline",function(){var Ca=this,ta=Ca.hcLib,fa=Ca.window,Ka=/msie/i.test(fa.navigator.userAgent)&&!fa.opera,Ma=ta.chartAPI,La=ta.chartAPI,qa=ta.extend2,y=ta.raiseEvent,ra=ta.pluck,t=ta.pluckNumber,Ia=ta.getFirstColor,U=ta.graphics.convertColor,Ua=ta.bindSelectionEvent,h=ta.createTrendLine,za=ta.parseUnsafeString,Fa=ta.regescape,f=ta.Raphael,Ga=ta.hasTouch,Va=ta.getMouseCoordinate,ha=ta.FC_CONFIG_STRING,wa="rgba(192,192,192,"+(Ka?.002:
1E-6)+")",Aa=fa.Math,Da=Aa.ceil,G=Aa.floor,Ra=Aa.max,Wa=Aa.min,ma=Aa.cos,ya=Aa.sin,Ea=fa.parseFloat,Na=fa.parseInt,xa;qa(ta.eventList,{zoomed:"FC_Zoomed",pinned:"FC_Pinned",resetzoomchart:"FC_ResetZoomChart"});Ma("zoomline",{friendlyName:"Zoomable and Panable Multi-series Line Chart",rendererId:"zoomline",standaloneInit:!0,hasVDivLine:!0,defaultSeriesType:"stepzoom",canvasborderthickness:1,defaultPlotShadow:1,chart:function(){var b=this.base.chart.apply(this,arguments),e=b[ha],g=this.dataObj.chart,
k=this.colorManager.getColor("canvasBorderColor");qa(b.chart,{animation:!1,zoomType:"x",canvasPadding:t(g.canvaspadding,0),scrollColor:Ia(ra(g.scrollcolor,this.colorManager.getColor("altHGridColor"))),scrollShowButtons:!!t(g.scrollshowbuttons,1),scrollHeight:t(g.scrollheight,16)||16,scrollBarFlat:e.flatScrollBars,allowPinMode:t(g.allowpinmode,1),skipOverlapPoints:t(g.skipoverlappoints,1),showToolBarButtonTooltext:t(g.showtoolbarbuttontooltext,1),btnResetChartTooltext:ra(g.btnresetcharttooltext,"Reset Chart"),
btnZoomOutTooltext:ra(g.btnzoomouttooltext,"Zoom out one level"),btnSwitchToZoomModeTooltext:ra(g.btnswitchtozoommodetooltext,"<strong>Switch to Zoom Mode</strong><br/>Select a subset of data to zoom into it for detailed view"),btnSwitchToPinModeTooltext:ra(g.btnswitchtopinmodetooltext,"<strong>Switch to Pin Mode</strong><br/>Select a subset of data and compare with the rest of the view"),pinPaneFill:U(ra(g.pinpanebgcolor,k),t(g.pinpanebgalpha,15)),zoomPaneFill:U(ra(g.zoompanebgcolor,"#b9d5f1"),t(g.zoompanebgalpha,
30)),zoomPaneStroke:U(ra(g.zoompanebordercolor,"#3399ff"),t(g.zoompaneborderalpha,80)),crossline:{enabled:t(g.showcrossline,1),line:{"stroke-width":t(g.crosslinethickness,1),stroke:Ia(ra(g.crosslinecolor,"#000000")),"stroke-opacity":t(g.crosslinealpha,20)/100},labelEnabled:t(g.showcrosslinelabel,g.showcrossline,1),labelstyle:{fontSize:Ea(g.crosslinelabelsize)?Ea(g.crosslinelabelsize)+"px":e.outCanvasStyle.fontSize,fontFamily:ra(g.crosslinelabelfont,e.outCanvasStyle.fontFamily)},valueEnabled:t(g.showcrosslinevalues,
g.showcrossline,1),valuestyle:{fontSize:Ea(g.crosslinevaluesize)?Ea(g.crosslinevaluesize)+"px":e.inCanvasStyle.fontSize,fontFamily:ra(g.crosslinevaluefont,e.inCanvasStyle.fontFamily)}}});return b},preSeriesAddition:function(){var b=this.dataObj,e=b.chart,g=this.hcJSON,k=g[ha],f=this.smartLabel,h=t(e.compactdatamode,0),ea=ra(e.dataseparator,"|"),W=t(e.showlabels,1),R=e.labeldisplay&&e.labeldisplay.toLowerCase(),n=W&&t(e.labelheight),P="rotate"===R?270:t(e.rotatelabels,1)?270:0,L=g.xAxis.labels.style,
x=Ea(L.lineHeight),u=g.chart.labelPadding=t(e.labelpadding,.2*x)+g.chart.plotBorderWidth,M,y,aa,z=0,U=-1,V,G,da;0>n&&(n=void 0);0>u&&(u=(g.chart.plotBorderWidth||0)+2);M=(M=b.categories)&&M[0]||{};b=M.category;delete M.category;g.categories=R=qa({data:y=h&&b&&b.split&&b.split(ea)||b||[],rotate:P,wrap:"none"!==R},M);void 0!==b&&(M.category=b);M=y.length||0;if(V=!h&&W&&0!==n&&M||0){for(;V--;)y[V]=y[V]&&(aa=y[V].label||"")&&((G=aa.length)>z&&(z=G,U=V,aa)||aa)||"";z&&(aa=y[U])}else if(h&&M&&!n)if(P){h=
fa.document.createElement("div");n=fa.document.createElement("span");h.setAttribute("class","fusioncharts-zoomline-localsmartlabel");h.style.cssText="display:block;width:1px;position:absolute;";for(da in L)h.style[da]=L[da];n.innerHTML=b.replace(/\s*/g,"").replace(/\{br\}/ig,"<br />").replace(new RegExp(Fa(ea),"g")," ");h.appendChild(n);fa.document.body.appendChild(h);n=n.offsetWidth||void 0;h.parentNode.removeChild(h)}else aa=y[M-1]||y[0];void 0!==n&&0!==n||!W||(aa?(f.setStyle(L),aa=f.getSmartText(aa),
n=P?aa.width:aa.height):n=x*(P&&3||1));n>.3*k.height&&(n=.3*k.height);R.labelHeight=n&&n+6||0;R.show=n&&W||0;R.css=qa({},L);P?(R.css.rotation=P,R.css["text-anchor"]="end"):R.css["vertical-align"]="top";g.xAxis.min=0;g.xAxis.max=M&&M-1||0;n+=t(e.scrollheight,16)||16;g.chart.marginBottom+=u;k.marginBottomExtraSpace+=n;ra(e.caption,e.subcaption)||(k.marginTopExtraSpace+=16)},series:function(){var b=this.dataObj,e=b.chart,g=b.dataset,k=this.hcJSON,f=k[ha],Y=f[0],ea=k.series,W=t(e.yaxismaxvalue),R=t(e.yaxisminvalue),
n=t(e.forceyaxislimits,0),P=t(e.compactdatamode,0),L=ra(e.dataseparator,"|"),x=Fa(e.indecimalseparator),u=Fa(e.inthousandseparator),M=t(e.drawanchors,e.showanchors,1),y=!!t(e.showlegend,1),aa,z,U,V,G,da=Infinity,ba=-Infinity,ca;G=k.categories.data.length;if(g&&g.length&&G){x&&(x=new RegExp(x,"g"));u&&(u=new RegExp(u,"g"));!u&&!x&&P&&n&&void 0!==W&&void 0!==R?(n=!0,ba=Ra(W,R),da=Wa(R,W)):n=!1;W=0;for(R=g.length;W<R;W++){aa=g[W];U=aa.data;delete aa.data;P?(V=U||"",u&&(V=V.replace(u,"")),x&&(V=V.replace(x,
".")),V=V.split(L)):V=U||[];V.length>G&&(V.length=G);ca=V.length;if(P){if(!n)for(;ca--;)z=Ea(V[ca]),isNaN(z)&&(z=void 0),z>ba&&(ba=z),z<=da&&(da=z),V[ca]=z}else for(;ca--;)z=V[ca]&&V[ca].value||"",u&&(z=z.replace(u,"")),x&&(z=z.replace(x,".")),z=Ea(z),isNaN(z)&&(z=void 0),z>ba&&(ba=z),z<=da&&(da=z),V[ca]=z;ea.push(z={index:W,type:"zoomline",data:V,name:aa.seriesname||"",showInLegend:aa.seriesname&&t(aa.includeinlegend,1)&&y||!1,showAnchors:t(aa.drawanchors,aa.showanchors,M),visible:!t(aa.initiallyhidden,
0),lineWidth:2});V.length=G;void 0!==U&&(aa.data=U);z.attrs=this.seriesGraphicsAttrs(aa);aa=z.attrs.anchors;z.color=z.attrs.graphics.stroke;z.ancorRadius=aa.r+aa["stroke-width"]/2;z.marker={fillColor:aa.fill,lineColor:aa.stroke,lineWidth:1,symbol:"circle"}}-Infinity!==ba&&Infinity!==da||(ba=da=void 0);n=Na(t(e.displaystartindex,1),10)-1;L=Na(t(e.displayendindex,G||2),10)-1;1>(g=t(e.pixelsperpoint,15))&&(g=1);(ea=t(e.pixelsperlabel,e.xaxisminlabelwidth,k.categories.rotate?20:60))<g&&(ea=g);(0>n||n>=
(G-1||1))&&(n=0);(L<=n||L>(G-1||1))&&(L=G-1||1);k.stepZoom={cnd:t(e.connectnulldata,0),amrd:t(e.anchorminrenderdistance,20),nvl:t(e.numvisiblelabels,0),cdm:P,oppp:g,oppl:ea,dsi:n,dei:L,vdl:L-n,dmax:Y.max=ba,dmin:Y.min=da,clen:G,offset:0,step:1,llen:0,alen:0,ddsi:n,ddei:L,ppc:0};this.configureAxis(k,b);b.trendlines&&h(b.trendlines,k.yAxis,f,!1,this.isBar)}},seriesGraphicsAttrs:function(b){var e=this.dataObj.chart,g="0"!=(b.dashed||e.linedashed||"0"),k,h,g={"stroke-width":t(b.linethickness,e.linethickness,
2),stroke:Ia(ra(b.color,e.linecolor,this.colorManager.getPlotColor())),"stroke-opacity":t(b.alpha,e.linealpha,100)/100,"stroke-dasharray":g?[t(b.linedashlen,e.linedashlen,5),t(b.linedashgap,e.linedashgap,4)]:"none","stroke-linejoin":"round","stroke-linecap":"round"};k=qa({},g);h=g["stroke-width"]+t(e.pinlinethicknessdelta,1);k["stroke-width"]=0<h&&h||0;k["stroke-dasharray"]=[3,2];return{graphics:g,pin:k,shadow:{opacity:g["stroke-opacity"],apply:t(e.showshadow,+!f.vml)},anchors:{"stroke-linejoin":"round",
"stroke-linecap":"round",r:t(b.anchorradius,e.anchorradius,g["stroke-width"]+2),stroke:Ia(ra(b.anchorbordercolor,e.anchorbordercolor,g.stroke)),"stroke-opacity":t(b.anchorborderalpha,e.anchorborderalpha,100)/100,"stroke-width":t(b.anchorborderthickness,e.anchorborderthickness,g["stroke-width"]),fill:Ia(ra(b.anchorbgcolor,e.anchorbgcolor,"#ffffff")),"fill-opacity":t(b.anchorbgalpha,e.anchorbgalpha,100)/100,opacity:t(b.anchoralpha,e.anchoralpha,100)/100},anchorShadow:t(e.anchorshadow,e.showshadow,+!f.vml)&&
{apply:!0,opacity:t(b.anchoralpha,e.anchoralpha,100)/100}}},eiMethods:{zoomOut:function(){var b=this.jsVars,e;if(b&&(e=b.hcObj))return e.zoomOut&&b.hcObj.zoomOut()},zoomTo:function(b,e){var g=this.jsVars,k;if(g&&(k=g.hcObj))return k.zoomRange&&g.hcObj.zoomRange(b,e)},resetChart:function(){var b=this.jsVars,e;b&&(e=b.hcObj)&&(e.pinRangePixels&&b.hcObj.pinRangePixels(),e.resetZoom&&b.hcObj.resetZoom())},setZoomMode:function(b){var e=this.jsVars,g;e&&(g=e.hcObj)&&g.activatePin&&g.activatePin(!b)},getViewStartIndex:function(){var b=
this.jsVars,e;if(b&&b.hcObj&&(e=b.hcObj._zoominfo))return e.ddsi},getViewEndIndex:function(){var b=this.jsVars,e;if(b&&b.hcObj&&(e=b.hcObj._zoominfo))return b=e.ddei-1,(b>=e.clen?e.clen:b)-1}}},Ma.msline);La("renderer.zoomline",{resetZoom:function(){var b=this._zoomhistory,e=this.options.stepZoom;if(!b.length)return!1;b.length=0;this.zoomTo(e.dsi,e.dei)&&y("zoomReset",this._zoomargs,this.fusionCharts,[this.fusionCharts.id]);return!0},zoomOut:function(){var b=this._zoomhistory.pop(),e=this.options.stepZoom,
g,k,f;b?(g=b.dsi,k=b.dei):this._prezoomed&&(g=0,k=e.clen-1);(f=this.zoomTo(g,k))&&Ca.raiseEvent("zoomedout",f,this.fusionCharts);return!0},zoomRangePixels:function(b,e){var g=this._zoomhistory,k=this._zoominfo,f=k.ppp,k=k.ddsi,h;g.push(this._zoominfo);(h=this.zoomTo(k+G(b/f),k+G(e/f)))?Ca.raiseEvent("zoomedin",h,this.fusionCharts):g.pop()},zoomRange:function(b,e){var g=this._zoomhistory,k;g.push(this._zoominfo);(k=this.zoomTo(+b,+e))?Ca.raiseEvent("zoomedin",k,this.fusionCharts):g.pop()},zoomTo:function(b,
e){var g=this.xlabels.data,k=this._zoominfo,f=this._zoomhistory,h=k.clen;0>b&&(b=0);b>=h-1&&(b=h-1);e<=b&&(e=b+1);e>h-1&&(e=h-1);if(b===e||b===k.dsi&&e===k.dei)return!1;this.pinRangePixels();k=qa({},k);k.dsi=b;k.dei=e;k=this._zoominfo=k;this.updatePlotZoomline();this.zoomOutButton[k.vdl===k.clen-1?"hide":"show"]();this.resetButton[f.length?"show":"hide"]();this.elements.zoomscroller.attr({"scroll-ratio":k.vdl/(h-!!h),"scroll-position":[k.dsi/(h-k.vdl-1),!0]});g={level:f.length+1,startIndex:b,startLabel:g[b],
endIndex:e,endLabel:g[e]};y("zoomed",g,this.fusionCharts,[this.fusionCharts.id,b,e,g.startLabel,g.endLabel,g.level]);return g},activatePin:function(b){var e=this._zoominfo,g=this.options.chart,k=this.pinButton;if(k&&e.pinned^(b=!!b))return b||this.pinRangePixels(),y("zoomModeChanged",{pinModeActive:b},this.fusionCharts,[]),g.showToolBarButtonTooltext&&k.tooltip(g[b&&"btnSwitchToZoomModeTooltext"||"btnSwitchToPinModeTooltext"]||""),k.attr("button-active",b),e.pinned=b},pinRangePixels:function(b,e){var g=
this.paper,k=this.elements,f=this.xlabels.data,h=this._zoominfo,t=this.layers.zoompin,W=k.pinrect,R=k["clip-pinrect"],n=this._pingrouptransform,P=this.plots,L=e-b,x,u;if(h&&t&&W){if(b===e)return t.hide(),k.pintracker.hide(),this.pinButton.attr("button-active",!1),h.pinned=!1;for(u=P.length;u--;)W=P[u],x=W.pinline,x||(x=W.pinline=g.path(void 0,t).attr(W.attrPin)),x.attr("path",W.graphic.attrs.path);R[0]=b+this.canvasLeft;R[2]=L;t.attr({"clip-rect":R,transform:n}).show();k.pintracker.__pindragdelta=
0;k.pintracker.show().attr({transform:n,x:b,width:L});b=this.getValuePixel(b);e=this.getValuePixel(e);y("pinned",{startIndex:b,endIndex:e,startLabel:f[b],endLabel:f[e]},this.fusionCharts,[this.fusionCharts.id,b,e,f[b],f[e]]);return h.pinned=!0}},getValuePixel:function(b){var e=this._zoominfo;return e.ddsi+G(b/e.ppp)},getParsedLabel:function(b){var e=this.xlabels;return e.parsed[b]||(e.parsed[b]=za(e.data[b]||""))},drawGraph:function(){var b=this,e=b.paper,g=b.canvasLeft,k=b.canvasTop,h=b.canvasWidth,
Y=b.canvasHeight,t=b.options,W=t.chart,R=W.plotBorderWidth,n=W.useRoundEdges,P=W.showToolBarButtonTooltext,L=W.crossline,x=b.layers,u=b.toolbar,M=b.elements,y=W.allowPinMode,G=t.categories,z=!1,U,V,fa,da,ba,ca,ha;ca=b._zoominfo=qa({},t.stepZoom);b._zoomhistory=[];ca.clen&&(z=b._prezoomed=ca.dei-ca.dsi<ca.clen-1,ba=b._visw=b.canvasWidth-2*W.canvasPadding,da=b._visx=b.canvasLeft+W.canvasPadding,b._visout=-(b.chartHeight+b.canvasHeight+1E3),b.base.drawGraph.apply(b,arguments),b._ypvr=b.yAxis[0]&&b.yAxis[0].pixelValueRatio||
0,ha=b._ymin||(b._ymin=b.yAxis[0].endY),b._yminValue=b.yAxis[0].min,t=x.dataset.attr("clip-rect",[b._visx,b.canvasTop,b._visw,b.canvasHeight]),fa=x.scroll||(x.scroll=e.group("scroll").insertAfter(x.layerAboveDataset)),b.xlabels=[],b.xlabels.show=G.show,b.xlabels.height=G.labelHeight,b.xlabels.wrap=G.wrap,b.xlabels.rotate=G.rotate,b.xlabels.data=G.data||[],b.xlabels.parsed=[],b.xlabels.css=G.css,b.xlabels.group=e.group("zoomline-plot-xlabels",x.datalabels),x.datalabels.transform(["T",da,k+Y+W.scrollHeight+
W.labelPadding]),b._lcmd=G.rotate?"y":"x",y&&(y=f.crispBound(0,k-ha,0,Y,R),U=M["clip-pinrect"]=[y.x,k,y.width,y.height],V=x.zoompin=e.group("zoompin").insertBefore(t).transform(b._pingrouptransform=["T",da,ha]).hide(),M.pinrect=e.rect(0,k-ha,b._visw,Y,x.zoompin).attr({"stroke-width":0,stroke:"none",fill:W.pinPaneFill,"shape-rendering":"crisp",ishot:!0}),M.pintracker=e.rect(x.tracker).attr({transform:V.transform(),x:0,y:k-ha,width:0,height:Y,stroke:"none",fill:wa,ishot:!0,cursor:f.svg&&"ew-resize"||
"e-resize"}).drag(function(b){var e=da+b+this.__pindragdelta,g=this.__pinboundleft,k=this.__pinboundright,h=this.data("cliprect").slice(0);e<g?e=g:e>k&&(e=k);V.transform(["T",e,ha]);M.pintracker.transform(V.transform());f.svg||(h[0]=h[0]+e-da-this.__pindragdelta,V.attr("clip-rect",h));this.__pindragoffset=b},function(){this.__pinboundleft=0-U[0]+da+g;this.__pinboundright=this.__pinboundleft+ba-U[2];this.data("cliprect",V.attr("clip-rect"));V._.clipispath=!0},function(){V._.clipispath=!1;this.__pindragdelta=
this.__pindragoffset;delete this.__pindragoffset;delete this.__pinboundleft;delete this.__pinboundright}),b.pinButton=u.add("pinModeIcon",function(){b.activatePin(!b._zoominfo.pinned)},{tooltip:P&&W.btnSwitchToPinModeTooltext||""})),R++,y=f.crispBound(g-R,k+Y+R,h+R+R,W.scrollHeight,R),R--,M.zoomscroller=e.scroller(y.x+(n&&-1||R%2),y.y-(n&&4||2),y.width-(!n&&2||0),y.height,!0,{showButtons:W.scrollShowButtons,scrollRatio:ca.vdl/(ca.clen-!!ca.clen),scrollPosition:[ca.dsi/(ca.clen-ca.vdl-1),!1],displayStyleFlat:W.scrollBarFlat},
fa).attr({fill:W.scrollColor,r:n&&2||0}).scroll(b.updatePlotZoomline,b),n&&M.zoomscroller.shadow(!0),function(){var e;f.eve.on("raphael.scroll.start."+M.zoomscroller.id,function(g){e=g;b.crossline&&b.crossline.disable(!0);Ca.raiseEvent("scrollstart",{scrollPosition:g},b.logic.chartInstance)});f.eve.on("raphael.scroll.end."+M.zoomscroller.id,function(g){b.crossline&&b.crossline.disable(!1);Ca.raiseEvent("scrollend",{prevScrollPosition:e,scrollPosition:g},b.logic.chartInstance)})}(),Ua(b,{attr:{stroke:W.zoomPaneStroke,
fill:W.zoomPaneFill,strokeWidth:0},selectionStart:function(){},selectionEnd:function(e){var k=e.selectionLeft-g;e=k+e.selectionWidth;b.crossline&&b.crossline.hide();b[b._zoominfo.pinned?"pinRangePixels":"zoomRangePixels"](k,e)}}),b.zoomOutButton=u.add("zoomOutIcon",function(){b.zoomOut()},{tooltip:P&&W.btnZoomOutTooltext||""})[z&&"show"||"hide"](),b.resetButton=u.add("resetIcon",function(){b.resetZoom()},{tooltip:P&&W.btnResetChartTooltext||""}).hide(),y=b.resetButton.attr("fill"),y[2]="rgba(255,255,255,0)",
b.resetButton.attr("fill",[y[0],y[1],y[2],y[3]]),L&&0!==L.enabled&&(b.crossline=new xa(b,L)),b.updatePlotZoomline())},drawPlotZoomline:function(b,e){var g=this.paper,k=e.attrs,f=e.visible,h=f?"show":"hide",t=this.layers.dataset,y=b.group||(b.group=g.group("plot-zoomline-dataset",t)),t=b.anchorGroup||(b.anchorGroup=g.group("plot-zoomline-anchors",t)),g=b.graphic||(b.graphic=g.path(void 0,y)),R=["T",this._visx,this._ymin||(this._ymin=this.yAxis[0].endY)];y.transform(R)[h]();t.transform(R)[h]();b.graphic=
g.attr(k.graphics).shadow(k.shadow);b.attrPin=k.pin;b.visible=f;b.anchors=[];b.anchors.show=e.showAnchors;b.anchors.attrs=k.anchors;b.anchors.attrsShadow=k.anchorShadow;b.anchors.left=-(k.anchors.r+.5*k.anchors["stroke-width"]);b.anchors.right=this._visw-b.anchors.right},updatePlotZoomline:function(b,e){var g=this.paper,k=this._ypvr,f=this._visw,h=this.xlabels,t=h.css,y=h.group,R=this.plots,n,P,L,x,u,M,G;!e&&(e=this._zoominfo);L=e.oppp;x=e.vdl=e.dei-e.dsi;u=e.ppl=e.nvl?f/e.nvl:e.oppl;f=e.step=(P=
e.ppp=f/x)<L?Da(L/P):1;u=e.lskip=Da(Ra(u,Ea(t.lineHeight))/P/f);void 0!==b?(L=(e.clen-x-1)*b,e.offset=(L-(L=Na(L)))*P,M=L+x):(L=e.dsi,M=e.dei,e.offset=0);x=e.norm=L%f;e.ddsi=L-=x;e.ddei=M=M+2*f-x;e.pvr=k;e._ymin=this._ymin;e._yminValue=this._yminValue;k=h.show?Da((M-L)/f/u):0;x=e.llen-1;e.llen=k;G=e.ppc=P*u*f;if(k>x)for(u=x,x=k;u<x;u++)(n=h[u])&&n.show()||(h[u]=g.text(0,0,"",y).css(t));else for(u=k,x+=1;u<x;u++)h[u].hide();k=P*f<e.amrd?0:Da((M-L)/f);t=k-e.alen;e.alen=k;h.wrap&&(h.rotate?(h._width=
h.height,h._height=G):(h._width=G,h._height=h.height));for(f=R.length;f--;){y=R[f];n=y.anchors;if(n.show&&t){P=n.attrs;u=0;for(x=k;u<x;u++)n[u]=n[u]&&n[u].show()||g.circle(P,y.anchorGroup);u=k;for(x=n.length;u<x;u++)n[u]&&n[u].hide()}this.drawPlotZoomlineGraphics(e,y.data,y.graphic,n,!f&&h)}fa.FC_DEV_ENVIRONMENT&&fa.jQuery&&(FusionCharts["debugger"].enable()?(this.debug=this.debug||(fa.jQuery("#fc-zoominfo").length||fa.jQuery("body").append('<pre id="fc-zoominfo">'),fa.jQuery("#fc-zoominfo").css({position:"absolute",
left:"10px",top:"0","pointer-events":"none",opacity:.7,width:"250px",zIndex:"999",border:"1px solid #cccccc","box-shadow":"1px 1px 3px #cccccc",background:"#ffffff"})),this.debug.text(JSON.stringify(e,0,2))):(this.debug&&fa.jQuery("#fc-zoominfo").remove(),delete this.debug))},drawPlotZoomlineGraphics:function(b,e,g,k,f){var h=this.smartLabel,t=[],y=!b.cnd,R=b.ddei,n=b.clen,P=b.step,L=b.lskip,x=b.ppp,u=b.offset,M=b.pvr,G=this._visw,U=this._visout,z=this._lcmd,fa="M",V,ha,da=f&&f[0],ba,ca;k=k[0];var ma=
{},sa={},ia,ka=0,pa,ra,qa=-b.norm,ta=b.ddsi,wa=0;da&&(f.group.transform(["T",-u,0]),ra=f.wrap,ba=f._height,ca=f._width,ra&&h.setStyle(f.css));for(;ta<=R;ta+=P,qa+=P)pa=ka/3+wa,ia=qa*x,void 0===(V=e[ta])?(y&&(fa="M"),ha=U,f=ia-u,V=U,wa++):(t[ka++]=fa,t[ka++]=ha=f=ia-u,t[ka++]=V=(V-b._yminValue)*M,fa="L"),k&&(k=k.attr((ma.cx=ha,ma.cy=V,ma)).next),!da||pa%L||(pa=da.attrs,ha=this.getParsedLabel(ta),f=0>f||f>G?U:ia,da._prevtext===ha?delete sa.text:sa.text=da._prevtext=ha,pa[z]===f?delete sa[z]:sa[z]=f,
ra&&ha&&(sa.text=h.getSmartText(ha,ca,ba).text),da=da.attr(sa).next);R>=n&&(void 0!==(V=e[n-1])&&(t[ka++]="L",t[ka++]=(qa-(R-n))*x-u,t[ka++]=V*M),k&&k.attr((ma.cx=U,ma.cy=U,ma)));g.attr("path",t)},legendClick:function(b){var e=!b.visible,g=e?"show":"hide";b.group[g]();b.anchorGroup[g]();this.base.legendClick.apply(this,arguments);return b.visible=e},dispose:function(){var b;this.crossline&&(this.crossline.dispose(),delete this.crossline);(b=this.elements.pintracker)&&(b.undrag(),delete this.elements.pintracker);
delete this.zoomOutButton;delete this.resetButton;delete this.pinButton;this.xlabels&&(this.xlabels.length=0);delete this.xlabels;this.base.dispose.apply(this)}},La["renderer.cartesian"]);xa=function(b,e){var g=b.paper,f=this.left=b._visx,h=this.width=b._visw,t=this.top=b.canvasTop,y=this.height=b.canvasHeight,G=this._visout=b._visout,R=this.plots=b.plots,n=b.layers.dataset,P,L=e.labelstyle,x=e.valuestyle;P=this.group=g.group("crossline-labels",n).attr({transform:["T",f,b._ymin]});this.tracker=g.rect(f,
t,h,y,n).attr({stroke:"none","stroke-width":0,fill:wa}).toFront().mousedown(this.onMouseDown,this).mouseup(this.onMouseUp,this,!0).mouseout(this.onMouseOut,this).mousemove(this.onMouseMove,this);Ga&&this.tracker.touchstart(this.onMouseMove,this);this.container=b.container;this.line=g.path(void 0,n).attr(qa({path:["M",f,t,"l",0,y]},e.line)).toBack();f=this.labels=e.valueEnabled&&g.set();e.labelEnabled&&(this.positionLabel=g.text(G,t+y+(b.options.chart.scrollHeight||0)+2.5,"").insertAfter(b.xlabels.group.parent).css(L).attr({"vertical-align":"top",
"text-bound":["rgba(255,255,255,1)","rgba(0,0,0,1)",1,2.5]}));this.hide();this.pixelRatio=b._ypvr;this.yminValue=b._yminValue;this.positionLabels=b.xlabels||{data:[],parsed:[]};this.getZoomInfo=function(){return b._zoominfo};this.getDataIndexFromPixel=function(e){return b.getValuePixel(e)};this.getPositionLabel=function(e){return b.getParsedLabel(e)};if(e.valueEnabled)for(t=0,y=R.length;t<y;t++)L=R[t],L=L.graphic.attrs.stroke,f.push(g.text(0,G,"",P).css(x).attr({fill:L,"text-bound":["rgba(255,255,255,0.8)",
"rgba(0,0,0,0.2)",1,2.5]}))};xa.prototype.disable=function(b){void 0!==b&&(this.disabled=!!b)&&this.visible&&this.hide();return this.disabled};xa.prototype.onMouseOut=function(){this.hide()};xa.prototype.onMouseDown=function(){!Ga&&this.hide();this._mouseIsDown=!0};xa.prototype.onMouseUp=function(){!Ga&&this.hide();delete this._mouseIsDown};xa.prototype.onMouseMove=function(b){if(!(this.disabled||this._mouseIsDown&&!Ga)){var e=this.getZoomInfo(),g=this.line,f=this.left,h=e.step,t=e.ppp*h;b=Va(this.container,
b).chartX-f;var y;b=(b+=t/2+e.offset)-b%t;y=(y=this.getDataIndexFromPixel(Da(b)))+y%h;b-=e.offset;g.transform(["T",G(b),0]);this.hidden&&this.show();if(y!==this.position||this.hidden)this.position=y,this.lineX=b,this.updateLabels()}};xa.prototype.updateLabels=function(){var b=this.labels,e=this.plots,g=this.width,f=this.position,h=this.lineX,t=G(h),y=this.pixelRatio,W=this.yminValue,R=this._visout,n,P;b&&b.forEach(function(b,x){n=e[x];P=n.data[f];b.attr({text:P+"",x:t,y:void 0!==P&&n.visible?(P-W)*
y:R,"text-anchor":0>=h&&"start"||h>=g&&"end"||"middle"})});this.positionLabel&&this.positionLabel.attr({x:h+this.left,text:this.getPositionLabel(f)})};xa.prototype.show=function(){this.disabled||(this.hidden=!1,this.group.attr("visibility","visible"),this.line.attr("visibility","visible"),this.positionLabel&&this.positionLabel.attr("visibility","visible"))};xa.prototype.hide=function(){this.hidden=!0;this.group.attr("visibility","hidden");this.line.attr("visibility","hidden");this.positionLabel&&
this.positionLabel.attr("visibility","hidden")};xa.prototype.dispose=function(){for(var b in this)this.hasOwnProperty(b)&&delete this[b]};f.addSymbol({pinModeIcon:function(b,e,f){var k=.5*f,h=b-f,t=b+f,y=b-k,G=b+k,R=b+.5,n=R+1,P=R****,L=e-f,x=e+k,u=e-k,k=e+(f-k);return["M",h,L,"L",y,u,y,k,h,x,b-.5,x,b,e+f+.5,R,x,t,x,G,k,G,u,t,L,P,L,P,u,P,k,n,k,n,u,P,u,P,L,"Z"]},zoomOutIcon:function(b,e,g){b-=.2*g;e-=.2*g;var k=.8*g,h=f.rad(43),t=f.rad(48),y=b+k*ma(h),h=e+k*ya(h),G=b+k*ma(t),t=e+k*ya(t),R=f.rad(45),
n=y+g*ma(R),P=h+g*ya(R),L=G+g*ma(R);g=t+g*ya(R);return["M",y,h,"A",k,k,0,1,0,G,t,"Z","M",y+1,h+1,"L",n,P,L,g,G+1,t+1,"Z","M",b-2,e,"L",b+2,e,"Z"]},resetIcon:function(b,e,f){var k=b-f,h=(Aa.PI/2+Aa.PI)/2;b+=f*ma(h);var h=e+f*ya(h),t=2*f/3;return["M",k,e,"A",f,f,0,1,1,b,h,"L",b+t,h-1,b+2,h+t-.5,b,h]}})}]);
