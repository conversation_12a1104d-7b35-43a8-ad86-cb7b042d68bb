/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */

import React, { useEffect, useCallback, useState, useRef, useMemo } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  StatusBar,
  LayoutChangeEvent,
  Dimensions,
  Platform,
  useWindowDimensions,
  DevSettings,
  AppState,
  TouchableOpacity,
  Modal as ModalComponent,
  Text,
  FlatList,
  PanResponder,
  NativeModules,
  NativeEventEmitter,
  NativeEventSubscription,
  DeviceEventEmitter,
  InteractionManager,
  ActivityIndicator,
  Alert,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import {
  fixRNDimensionsOrientationChanged,
  getTransformForModalInsideNavigation,
  isOutletDisplay,
  isTablet, isTabletStatic, logToFile, performResize, sendTextEmail, showAlertForFailedPrinting, uploadLogFile
} from './util/common';
import { isTablet as isTabletOriginal } from 'react-native-device-detection';
import TabletMainScreen from './screen/MainScreen';
import PhoneMainScreen from './phonescreen/MainScreen';
import { CommonStore } from './store/commonStore';
import Orientation, {
  OrientationType,
  useOrientationChange,
} from 'react-native-orientation-locker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import { useNetInfo } from '@react-native-community/netinfo';
import moment from 'moment';
import 'moment/locale/en-sg';
import {
  checkRNReload,
  fixRNDimensions,
  removeRNReload,
  saveRNReload,
  waitForSeconds,
} from './util/common';
import {
  checkPrinterTasksLocking,
  connectToPrinter,
  detectOwnSunmiPrinter,
  disconnectPrinter,
  initSunmiPrinters,
  detectOwnIminPrinter,
  initIminPrinters,
  initPrinterBle,
  PrinterTaskScheduler,
} from './util/printer';
import { default as TabletScreenSwitcher } from 'react-native-tablet-switcher';
// import { default as PhoneScreenSwitcher } from 'react-native-phone-switcher';
import NetInfo from '@react-native-community/netinfo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Fontisto from 'react-native-vector-icons/Fontisto';
import Entypo from 'react-native-vector-icons/Entypo';
import Colors from './constant/Colors';
import { MemoryMonitor } from './util/memory';
import { GiftedChat, Avatar, utils } from 'react-native-gifted-chat';
import { CHATBOT_TEMPLATE, CHATBOT_USER } from './constant/chatbot';
import { toChatMessage, toChatMessageUserDelegate } from './util/chatbot';
import { UserStore } from './store/userStore';
import ChatMessage from './components/chatMessage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
import { OutletStore } from './store/outletStore';
import { MerchantStore } from './store/merchantStore';
import ApiClient from './util/ApiClient';
import * as User from './util/User';
import * as Token from './util/Token';
import API from './constant/API';
import { useKeyboard } from './hooks';
import { APP_ENV, APP_TYPE, KD_FONT_SIZE, KD_PRINT_VARIATION, MODE_ADD_CART, ORDER_TYPE, ORDER_TYPE_SUB, OUTLET_DISPLAY_PAIRING_DEVICE, OUTLET_DISPLAY_PAIRING_TYPE, OUTLET_SHIFT_STATUS, REPORT_DISPLAY_TYPE, ROLE_TYPE, TABLE_QR_SALT } from './constant/common';
import { BLEPrinter, GPrinterLegacy, NetPrinter } from '@conodene/react-native-thermal-receipt-printer-image-qr';
import BackgroundService from 'react-native-background-actions';
// import EventEmitter from 'react-native/Libraries/vendor/emitter/_EventEmitter';\
// import EventEmitter from 'eventemitter3';
import { Collections } from './constant/firebase';
import { startSnapshotListeners } from './util/snapshotListeners';
// import { storageMMKV } from './util/storageMMKV';
import { GestureHandlerRootView, ScrollView } from 'react-native-gesture-handler';
// import { ActivityIndicator } from 'react-native-paper';
import { CODEPAGE, PRINTER_COMMAND_TYPE, PRINTER_MODEL_TYPE, PRINTER_TASK_TYPE } from './constant/printer';
import { FileLogger } from "react-native-file-logger";
import PinUnlockModal from './components/pinUnlockModal';
import Hashids from 'hashids';
import { TableStore } from './store/tableStore';
import CustomerDisplay from './components/customerDisplay';
import PopupBanner from './components/popupBanner';
// import { SunmiCardReader } from 'sunmi-card-reader';

const hashids = new Hashids(TABLE_QR_SALT);
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

// import BleManager from 'react-native-ble-manager';
const EventEmitter = require('eventemitter3');
const alphabet = '0123456789';
const nanoid = customAlphabet(alphabet, 7);

// if (__DEV__) {
//   // console = {};
//   // // console.log = () => { };
//   // console.error = () => { };
//   // console.assert = () => { };
//   // console.clear = () => { };
//   // console.count = () => { };
//   // console.countReset = () => { };
//   // console.debug = () => { };
//   // console.dir = () => { };
//   // console.dirxml = () => { };
//   // console.exception = () => { };
//   // console.group = () => { };
//   // console.groupCollapsed = () => { };
//   // console.groupEnd = () => { };
//   // console.info = () => { };
//   // console.profile = () => { };
//   // console.profileEnd = () => { };
//   // console.table = () => { };
//   // console.time = () => { };
//   // console.timeEnd = () => { };
//   // console.timeLog = () => { };
//   // console.timeStamp = () => { };
//   // console.trace = () => { };
// }

FileLogger.configure({
  captureConsole: false,
  maximumFileSize: 1024 * 1024 * 5, // 5 mb
  maximumNumberOfFiles: 1,
});

moment.locale('en-sg');

moment.updateLocale('en-sg', {
  ordinal: function (number, token) {
    var b = number % 10;
    var output =
      ~~((number % 100) / 10) === 1
        ? 'th'
        : b === 1
          ? 'st'
          : b === 2
            ? 'nd'
            : b === 3
              ? 'rd'
              : 'th';
    return number + output;
  },
});

Orientation.addLockListener(fixRNDimensions);

Orientation.addOrientationListener(fixRNDimensionsOrientationChanged);

Orientation.addDeviceOrientationListener(fixRNDimensionsOrientationChanged);

Orientation.lockToLandscape();

global.openPaymentSummary = false;

global.listenerUserOrderSelectedTableTimerId = null;

global.hideOrderModal = false;

global.reportTableTimerId = null;
global.reportTableTimerId2 = null;
global.stackedDiscountAlertShowed = false;
global.stackedDiscountAlertShowedTimer = null;

global.eligiblePromoCodeTimer = null;

global.eligibleVoucherTimer = null;
global.ineligibleVoucherTimer = null;
global.setupOrderListenersHistoryTimerId = null;

global.excludePrinterIdDict = {};

global.tablePaymentSummaryVoucherToggleLoading = false;

global.getApplicableUserVouchersTimerId = null;
global.getApplicableUserVouchersLoading = false;

global.outletPaymentMethodsDict = {};

global.lastConnectedPrinterIp = '';
global.gprinterLastConnectedPrinterIp = '';

global.searchCrmUserList = [];

global.selectedTaggableVoucher = {};

global.duid = '';

global.qrDateTimeEncryptedConsumed = false;

global.isOnLoginPage = true;
global.isOnPinLoginPage = false;
global.selectedOrderToPayUserIdPrev = '';

global.eiStatusParsed = '';

AppState.addEventListener('change', async (nextAppState) => {
  logToFile(`[app] state changed: ${nextAppState}`);

  if (nextAppState === 'background' || nextAppState === 'inactive') {
    await removeRNReload();

    // logToFile(`[app] turned to background or inactive`);
  }

  if (
    nextAppState === "active"
  ) {
    // logToFile(`[app] turned to active`);

    if (global.simulateTabletMode) {
      setTimeout(() => {
        if (
          // global.isOnLoginPage || global.isOnPinLoginPage
          true
        ) {
          performResize(
            {
              windowPhysicalPixels: {
                height: 2160,
                width: 1620,
                scale: 2,
              },
            },
            'iPad 9th Generation',
            false,
            false,
            true,
            global.windowWidthOriginal,
            global.windowHeightOriginal,
            global.fontScaleOriginal,
          );
        }

        if (
          // global.isOnLoginPage || global.isOnPinLoginPage
          true
        ) {
          setTimeout(() => {
            performResize(
              {
                windowPhysicalPixels: {
                  height: 2160,
                  width: 1620,
                  scale: 2,
                },
              },
              'iPad 9th Generation',
              false,
              false,
              true,
              global.windowWidthOriginal,
              global.windowHeightOriginal,
              global.fontScaleOriginal,
            );
          }, 25);

          setTimeout(() => {
            performResize(
              {
                windowPhysicalPixels: {
                  height: 2160,
                  width: 1620,
                  scale: 2,
                },
              },
              'iPad 9th Generation',
              false,
              false,
              true,
              global.windowWidthOriginal,
              global.windowHeightOriginal,
              global.fontScaleOriginal,
            );
          }, 50);

          if (
            global.isOnLoginPage ||
            global.isOnPinLoginPage
          ) {
            setTimeout(() => {
              performResize(
                {
                  windowPhysicalPixels: {
                    height: 2160,
                    width: 1620,
                    scale: 2,
                  },
                },
                'iPad 9th Generation',
                false,
                false,
                true,
                global.windowWidthOriginal,
                global.windowHeightOriginal,
                global.fontScaleOriginal,
              );
            }, 80);
          }
        }
      }, 0);
    }

    console.log("App has come to the foreground!");
  }
});

global.movingItemsToTable = false;

global.isLoadingUserOrderSelectedTable = false;
global.isLoadingUserOrderSelectedTableInnerUpdates = false;

global.checkingOutTakeawayOrderPrev = {};

////////////////////////////////////////////////////

global.isLoadingTimer = null;

global.firstStartDict = {};

////////////////////////////////////////////////////


// 2024-09-26 - optimization related



////////////////////////////////////////////////////

global.draggableParam = {
  minX: 0,
  maxX: 0,
  minY: 0,
  maxY: 0,

  marginX: 0,
  marginY: 0,
};
global.draggableTablePosDict = {};

global.isBlePrinterTestPrinted = false;
global.isBlePrinterInited = false;

global.currOutletShift = {};
global.currOutletShiftStatus = false;

global.blePrintersDict = {};
global.labelPrintersDict = {};
global.lanLabelPrintersDict = {};

global.sunmiSerialNo = '';
global.sunmiPrinterModal = '';
global.sunmiPrinterVersion = '';
global.sunmiPrinters = [];
global.sunmiPrintersDict = {};

global.detectSunmiPrintersInterval = null;

global.iminSerialNo = '';
global.iminPrinterModal = '';
global.iminPrinterVersion = '';
global.iminPrinters = [];
global.iminPrintersDict = {};

global.detectIminPrintersInterval = null;

////////////////////////////////////////////////////

global.mutateUserOrdersInterval = null;

global.timeCheckItemTableInterval = null;

global.merchantLogoUrlOnline = '';
global.merchantName = '';

global.appEnv = APP_ENV.LIVE;

global.checkClosedTablesInterval = null;

global.forceShowApp = null; // for xiaomi pad 6 auto sign out issue fixes

global.sendDebugTextTimer = null;

global.funcSwitchShowApp = () => { };

global.signInAlready = false;

global.udiData = {};

global.noSignoutFN = undefined;
global.noSignoutC = undefined;
global.noSignoutI = undefined;

global.outletSupplyItemsDict = {};
global.outletSupplyItemsSkuDict = {};

global.cacheActualUserOrderDict = {};

global.requestAnimationFrame = (cb) => {
  cb && cb();
};

global.refreshRateTimer = null;

global.isUserActiveNow = false;
global.uanTimerId = null;
global.uanI = 0; // user active now interval (second)

global.reservationConfig = {
  printKdOsBeforeMinutes: 60,
};

global.queueConfig = {

};

// global.checkingOutTakeawayTimestampUse = null;

global.crmUsersDtUse = null;
global.userOrdersUse = null;

global.crmUsersDt = Date.now();
global.userOrdersDt = Date.now();

global.ssUserOrderMetadataId = null;
global.ssUserOrderActiveId = null;
global.ssUserOrderMonthlyId = null;

global.viewTableOrderModalPrev = false;
global.hideOrderModal = false;

global.pinUnlockModalShowing = false;
global.pinUnlockCallback = async () => { };
global.currPinId = null;
global.currPinName = null;

global.ptBatchPairs = [];
global.pteBatchPairs = [];

global.ptStartDatePrev = null;
global.ptEndDatePrev = null;

global.ptDateTimestamp = Date.now();
global.payoutTransactions = [];
global.payoutTransactionsExtend = [];

global.ptDict = {};
global.pteDict = {};

global.sideBarScreenYDict = {};

global.userOrderMetadataList = [];

global.currPrinterConnectedTime = Date.now();
global.isReconnectingToTimeoutPrinter = false;

global.printingTaskIdDict = {};

global.printerTasksLocking = false;

global.printerPendingTasksPrioritizedNum = 0;
global.printerPendingTasksNum = 0;
global.printerObjList = [];
global.outletItemsDict = {};

global.isPrintingNow = false;
global.isSnapshotChanging = true;
global.isCheckingIncomingOrders = false;
global.isAuthorizingTakeawayOrders = false;

global.isCheckingInterruptedTasksNow = false;

/////////////////////////////////////////////////////////////

global.outletKdEventTypes = [];
global.outletKdVariation = KD_PRINT_VARIATION.SUMMARY;
global.outletKdFontSize = KD_FONT_SIZE.NORMAL;
global.outletKdHeaderFontSize = KD_FONT_SIZE.EXTRA_LARGE;
global.outletKdPrintSku = false;

global.outletToPrintVariantAddonTitle = true;

global.outletKdPrintUserInfo = false;
global.outletPrintReceiptWhenPaidOnline = false;
global.outletToggleOfflineMode = false;

global.outletToggleDisableAutoPrint = false;
global.outletToggleDisablePrintingAlert = false;

global.odPairingType = OUTLET_DISPLAY_PAIRING_TYPE.A;
global.odPairingDevice = OUTLET_DISPLAY_PAIRING_DEVICE.HOST;

global.routerIpv4 = '';

global.outletAutoPrintPaySlip = true;

global.outletShortenShiftReport = false;

global.outletShortenReceipt = false;

/////////////////////////////////////////////////////////////

global.rcL = true;
global.rcMnP = true;
global.rcA = true;
global.rcMn = true;
global.rcOi = true;
global.rcCnP = true;
global.rcOd = true;
global.rcCd = true;
global.rcSd = true;
global.rcR = true;
global.rcT = true;
global.rcC = true;
global.rcTyo = true;
global.rcPbKd = true;
global.hRm = false;

/////////////////////////////////////////////////////////////


global.allOutletsCategoriesDict = {};
global.outletCategoriesDict = {}; // for print KD twice
global.outletItemsInfoDict = {};

global.outletTablesDict = {};

/////////////////////////////////////////////////////////////

global.privileges = [];
global.privileges_state = [];

/////////////////////////////////////////////////////////////

global.warningOutletItemList = [];
global.warningOutletSupplyItemList = [];

/////////////////////////////////////////////////////////////

global.tokenFcm = '';

/////////////////////////////////////////////////////////////

// global.dropdownPickerDict = {};

/////////////////////////////////////////////////////////////

global.isPlacingOrder = false;
global.isAddingToCart = false;

global.isPayingAndCheckingOutOrder = false;
global.isCheckingOutOrder = false;

/////////////////////////////////////////////////////////////

global.availablePromotions = [];
// global.merchantPromotionsAppliedDict = {};

/////////////////////////////////////////////////////////////

global.currUserRole = 'admin';

/////////////////////////////////////////////////////////////

global.employees = [];

console.log('App.js > global.currOutlet');
console.log(global.currOutlet);

global.allOutlets = [];

global.currOutlet = {
  reportDisplayType: REPORT_DISPLAY_TYPE.DAY,

  icitnTime: 3,
};

/////////////////////////////////////////////////////////////

global.isPaymentLoading = false;
global.isSplitAndJoinUserOrderRunning = false;

/////////////////////////////////////////////////////////////

global.logFileInterval = null;
global.reprintInterval = null;

/////////////////////////////////////////////////////////////

global.printerTaskQueueInterval = null;

global.subscriberListenToReservationChanges = () => { };
global.subscriberListenToUserChangesMerchant = () => { };
global.subscriberListenToMerchantIdChangesMerchant = () => { };
global.subscriberListenToCurrOutletIdChangesWaiter = () => { };
global.subscriberListenToCurrOutletIdChangesMerchant = () => { };
global.subscriberListenToSelectedOutletItemChanges = () => { };
global.subscriberListenToSelectedOrderToPayUserIdChanges = () => { };
global.subscriberListenToSelectedOutletTableIdChanges = () => { };
global.subscriberListenToCommonChangesMerchant = () => { };
global.subscriberListenToSelectedCustomerChDraggableangesMerchant = () => { };
global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant = () => { };
global.subscriberListenToUserOrderHistoricalChanges = () => { };

global.subscriberListenToOutletReviewHistoricalChanges = () => { };

global.subscriberListenToDisplayScreenChanges = () => { };

global.subscriberSelectedTableOrderChanges = () => { };
global.subscriberUserOrderPrinting = () => { };
global.subscriberUserOrderSelectedTable = () => { };
global.subscriberUserOrderTakeaway = () => { };
global.subscriberMultiUserOrderTakeaway = () => { };

global.subscriberUserOrderActiveTable = () => { };

/////////////////////////////////////////////////////////////

// interval based instead of while loop to create non-blocking experience

const taskQueueIntervalTime = Platform.OS === 'ios' ? 200 : 200; // 500

// const printerTaskQueueInterval = setInterval(() => {
//   startPrinterTaskQueueV2();
// }, taskQueueIntervalTime); // 5000

/////////////////////////////////////////////////////////////

const memoryMonitor = new MemoryMonitor();
memoryMonitor.start();

/////////////////////////////////////////////////////////////

global.snapshots = {};
global.emitter = new EventEmitter();
// global.emitter = new NativeEventEmitter(NativeModules);
// const emitter = new EventEmitter();

const runBackgroundTasks = async () => {
  global.printerTaskQueueInterval = setInterval(() => {
    startPrinterTaskQueueV2();
  }, taskQueueIntervalTime); // 5000

  // while (BackgroundService.isRunning()) {
  // }

  // 2024-04-02 - no need keeping snapshot listener caller alive

  startSnapshotListeners();

  // await new Promise(() => { // Never resolve
  //   // // Listen to Something1 to start a task
  //   // Something1.addEventListener('event', () => {
  //   //   // Starts a new "task"
  //   // };
  //   // // Listen to Something2 to start another task
  //   // Something2.addEventListener('event', () => {
  //   //   // Starts a new "task"
  //   // };

  //   try {
  //     // console.log('========================');
  //     // console.log('global.emitter');
  //     // console.log(global.emitter);
  //     // console.log('========================');

  //     startSnapshotListeners();
  //   }
  //   catch (ex) {
  //     console.error('=======================');
  //     console.error(ex);
  //     console.error('=======================');
  //   }
  // });
}

BackgroundService.start(runBackgroundTasks, {
  taskName: 'KooDoo Services',
  taskTitle: 'KooDoo Services',
  taskDesc: 'Monitoring printer status...',
  taskIcon: {
    name: 'ic_launcher',
    type: 'mipmap',
  },
  color: '##4E9F7D',
  // linkingURI: 'yourSchemeHere://chat/jane', // See Deep Linking for more info
  parameters: {
    delay: 1000,
  },
});

///////////////////////////////////////////////////////

// 2022-09-29 - Printer queue-based flow

const baseMillisecondToWait = Platform.OS === 'ios' ? 500 : 500; // 500

const startPrinterTaskQueueV2 = async () => {
  // choose the one that have most tasks OR receipt first OR queue date (in future can add as selection in settings for user to choose)

  // console.log(`[pr] global.isPrintingNow: ${global.isPrintingNow}`);
  // console.log(`[pr] global.isSnapshotChanging: ${global.isSnapshotChanging}`);
  // console.log(`[pr] global.isAuthorizingTakeawayOrders: ${global.isAuthorizingTakeawayOrders}`);

  // console.log(`[pr] global.printerPendingTasksNum: ${global.printerPendingTasksNum}`);

  // console.log(`[pr] global.printerPendingTasksPrioritizedNum: ${global.printerPendingTasksPrioritizedNum}`);

  if (global.currOutlet && global.currOutlet.prtRawLogs) {
    logToFile(`[pr] ${global.isCheckingInterruptedTasksNow} | ${global.printerPendingTasksNum} | ${global.isPrintingNow} | ${global.printerPendingTasksPrioritizedNum}`);
    logToFile(`[pr] ${global.currOutlet.printIgnoreSnapshot} | ${global.isSnapshotChanging} | ${global.isAuthorizingTakeawayOrders} | ${global.isReconnectingToTimeoutPrinter} | ${global.uanI}`);
  }

  if (
    !global.isCheckingInterruptedTasksNow &&
    global.printerPendingTasksNum > 0 &&
    !global.isPrintingNow &&
    (
      global.printerPendingTasksPrioritizedNum > 0 // prioritize tasks even snapshot changing
      ||
      (
        (
          (global.currOutlet && global.currOutlet.printIgnoreSnapshot) ||
          !global.isSnapshotChanging
        )
        &&
        !global.isAuthorizingTakeawayOrders
      )
    )
    &&
    !global.isReconnectingToTimeoutPrinter // might happened to certain printers, when connected at 13:00pm, but executed tasks at 13:01pm (over 30 seconds)
    &&
    // 2024-03-29 - added 1 more configurable checking, if user active (interacting with screen) now, no need print first
    (
      global.uanI > 0
        ?
        (!global.isUserActiveNow)
        :
        true
    )
  ) {
    logToFile(`[pr] cycle`);

    let waitingTime = baseMillisecondToWait; // 0

    if (global.currOutlet.bmtwIos && global.currOutlet.bmtwAnd) {
      if (Platform.OS === 'ios') {
        waitingTime = global.currOutlet.bmtwIos;
      }
      else {
        waitingTime = global.currOutlet.bmtwAnd;
      }
    }

    global.printerObjList = global.printerObjList.sort((curr, prev) => {
      return (curr.priority >= prev.priority || curr.frequency >= prev.frequency || curr.tasks.find(task => task.isPrioritized)) ? -1 : 1;
    }).sort((curr, prev) => {
      return curr.userPriority >= prev.userPriority ? -1 : 1;
    });

    console.log(`[pr] global.printerObjList`);
    console.log(global.printerObjList);

    // let printerObj = global.printerObjList.reduce((prev, curr) => {
    //   if (
    //     curr.tasks.length > prev.tasks.length
    //     // curr.priority >= prev.priority
    //   ) {
    //     return curr;
    //   }
    //   else {
    //     return prev;
    //   }
    // }, {
    //   tasks: [],
    //   priority: 0,
    //   frequency: 0,
    // });

    let printerObj = global.printerObjList.find(obj => {
      if (obj.tasks.length > 0) {
        return true;
      }
    });

    console.log(`[pr] printerObj`);
    console.log(printerObj);

    if (printerObj && printerObj.tasks && printerObj.tasks.length >= 0) {
      logToFile(`[pr] printerObj.ip | tasks.length: ${printerObj.ip} | ${printerObj.tasks.length}`);
    }

    if (printerObj && printerObj.tasks && printerObj.tasks.length > 0) {
      let failedToConnected = false;

      // connect to printer first

      logToFile(`[pr]`);

      global.isPrintingNow = true;

      let tasks = [
        ...printerObj.tasks,
      ];

      console.log(`[pr] connect to printer: ${printerObj.ip} at ${moment().format('hh:mm:ss')}`);

      logToFile(`[pr] connect to printer: ${printerObj.ip} at ${moment().format('hh:mm:ss')}`);

      await disconnectPrinter(printerObj);

      let result = false;

      let tasksDirectNum = tasks.filter(task => {
        if (task.taskType === PRINTER_TASK_TYPE.PRINT_USER_ORDER_RECEIPT ||
          task.taskType === PRINTER_TASK_TYPE.OPEN_CASH_DRAWER) {
          return true;
        }
        else {
          return false;
        }
      }).length;

      console.log(`printer scheduler: connecting to: ${printerObj.ip}`);

      logToFile(`printer scheduler: connecting to: ${printerObj.ip}`);

      // InteractionManager.runAfterInteractions(async () => {

      // });

      if (tasksDirectNum === tasks.length) {
        // means all is print receipt and cash drawer, retry times set to 1 can d

        result = await connectToPrinter(printerObj.ip,
          printerObj.rtIosD ? printerObj.rtIosD : 4,
          printerObj.rtAndD ? printerObj.rtAndD : 4, 'APP');
      }
      else {
        result = await connectToPrinter(printerObj.ip,
          printerObj.rtIosId ? printerObj.rtIosId : 5,
          printerObj.rtAndId ? printerObj.rtAndId : 5, 'APP');
      }

      global.currPrinterConnectedTime = Date.now();

      console.log(`[pr] ${printerObj.ip} printer result: ${result} at ${moment().format('hh:mm:ss')}`);
      logToFile(`[pr] ${printerObj.ip} printer result: ${result} at ${moment().format('hh:mm:ss')}`);

      let printerObjIndex = global.printerObjList.findIndex(obj => obj.uniqueId === printerObj.uniqueId);

      if (result) {
        const lastJobCooldownTime = (printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 50;

        setTimeout(async () => {
          logToFile(`[pr] ${printerObj.ip} printer tasks.length: ${tasks.length} at ${moment().format('hh:mm:ss')}`);
          console.log(`[pr] ${printerObj.ip} printer tasks.length: ${tasks.length} at ${moment().format('hh:mm:ss')}`);
          tasks.map(currTask => {
            logToFile(`task id ${currTask.taskId}`);
            console.log(`task id ${currTask.taskId}`);
            if (currTask.taskInfo && currTask.taskInfo.orderId) {
              logToFile(`order id: ${currTask.taskInfo.orderId}`);
              console.log(`order id: ${currTask.taskInfo.orderId}`);
            }
          });

          for (let i = 0; i < tasks.length; i++) {
            if (global.currOutlet && global.currOutlet.prevWTAlg) {
              // old algo

              waitingTime += (i *
                (printerObj.cooldownTime ? printerObj.cooldownTime : 650)
                + (i * (global.currOutlet.gprinterForceConnect > 0 ? global.currOutlet.gprinterForceConnect : 0))
              ); // 500 || baseMillisecondToWait
            }
            else {
              // new algo

              // 2023-10-21 - Fixes for printing time (instead of accumulating)
              waitingTime = (i *
                (printerObj.cooldownTime ? printerObj.cooldownTime : 650)
                + (i * (global.currOutlet.gprinterForceConnect > 0 ? global.currOutlet.gprinterForceConnect : 0))
              ); // 500 || baseMillisecondToWait       
            }

            setTimeout(async () => {
              // InteractionManager.runAfterInteractions(async () => {

              // });

              logToFile(`task id: ${tasks[i].taskId ? tasks[i].taskId : ''}`);
              console.log(`task id: ${tasks[i].taskId ? tasks[i].taskId : ''}`);
              if (tasks[i].taskInfo && tasks[i].taskInfo.orderId) {
                logToFile(`order id: ${tasks[i].taskInfo.orderId}`);
                console.log(`order id: ${tasks[i].taskInfo.orderId}`);
              }

              let resultGprinter = false;
              try {
                logToFile(`gprinterForceConnect: ${global.currOutlet.gprinterForceConnect}`);
                console.log(`gprinterForceConnect: ${global.currOutlet.gprinterForceConnect}`);
                logToFile(`i: ${i}`);
                console.log(`i: ${i}`);

                if (i > 0 && global.currOutlet.gprinterForceConnect > 0) {
                  logToFile(`gprinterForceConnect in: ${global.currOutlet.gprinterForceConnect}`);
                  console.log(`gprinterForceConnect in: ${global.currOutlet.gprinterForceConnect}`);

                  if (global.lanLabelPrintersDict[printerObj.ip] &&
                    global.lanLabelPrintersDict[printerObj.ip].commandType === PRINTER_COMMAND_TYPE.TSCPOS &&
                    global.lanLabelPrintersDict[printerObj.ip].modelType === PRINTER_MODEL_TYPE.GPRINTER
                    &&
                    Platform.OS === 'ios'
                  ) {
                    logToFile(`gprinter checking`);
                    console.log(`gprinter checking`);

                    resultGprinter = await connectToPrinter(printerObj.ip,
                      printerObj.rtIosId ? printerObj.rtIosId : 5,
                      printerObj.rtAndId ? printerObj.rtAndId : 5, 'APP');

                    logToFile(resultGprinter);
                    console.log(resultGprinter);
                  }
                }
              }
              catch (ex) {
                logToFile(ex);
                console.error(ex);
              }

              if (resultGprinter && global.currOutlet.gprinterFcDelay > 0) {
                setTimeout(async () => {
                  await tasks[i].callback();
                }, global.currOutlet.gprinterFcDelay ? global.currOutlet.gprinterFcDelay : 100);
              }
              else {
                await tasks[i].callback();
              }

              ////////////////////////////////////

              // 2025-03-18 - clear the tasks data after callback

              if (global.currOutlet && global.currOutlet.gcCbAuto !== undefined) {
                setTimeout(() => {
                  tasks[i].callback = null;
                }, global.currOutlet.gcCbAuto);
              }

              // ////////////////////////////////////

              // // 2025-02-01 - here mark the tasks done - printer task v2

              // if (global.currOutlet.ptV2) {

              // }

              // ////////////////////////////////////

              if (tasks[i].isPrioritized) {
                global.printerPendingTasksPrioritizedNum--;
              }

              // global.printerPendingTasksNum--;

              (function (index, delay) {
                console.log('[pr] compare if last index');
                console.log(`index: ${index}`);

                logToFile(`[pr] compare if last index: ${printerObj.ip}`);
                logToFile(`index: ${index}, ${printerObj.ip}`);

                console.log(`tasks.length - 1: ${tasks.length - 1}`);

                /////////////////////////////////////////////////////////

                // 2023-10-28 - Here can mark an 'update' to the order, to indicate this order been printed

                /////////////////////////////////////////////////////////

                if (index === tasks.length - 1) {
                  // means last one

                  console.log(`[pr] setTimeout at ${moment().format('hh:mm:ss')}, for ${((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 1000)} ms`);
                  logToFile(`[pr] setTimeout at ${moment().format('hh:mm:ss')}, for ${((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 1000)} ms, ${printerObj.ip}`);

                  // setTimeout(() => {
                  //   console.log(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}`);

                  //   global.isPrintingNow = false;
                  // }, ((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 50)); // original is 1000

                  setTimeout(() => {
                    console.log(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}`);
                    logToFile(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}, lastJobCooldownTime: ${lastJobCooldownTime} ms, ${printerObj.ip}`);

                    global.isPrintingNow = false;
                  }, lastJobCooldownTime); // original is 1000
                }

                /////////////////////////////////////////////////////////

                if (!global.supportCodeData && global.currOutlet && !global.currOutlet.reprintOff) {
                  if (tasks[i].orderUniqueId && tasks[i].taskInfo && tasks[i].taskInfo.orderId) {
                    // const orderPTasks = storageMMKV.getString(`${tasks[i].orderUniqueId}.pTasks`);
                    // if (orderPTasks) {
                    //   // means got already

                    //   storageMMKV.set(`${tasks[i].orderUniqueId}.pTasks`, (parseInt(orderPTasks) - 1).toFixed(0));
                    // }
                    // else {
                    //   storageMMKV.set(`${tasks[i].orderUniqueId}.pTasks`, (-1).toFixed(0));
                    // }

                    // storageMMKV.set(`${tasks[i].orderUniqueId}.pTasksPopup`, '0');

                    try {
                      firestore()
                        .collection(Collections.UserOrderMetadataV2)
                        .doc(tasks[i].orderUniqueId)
                        .update({
                          pTasks: firestore.FieldValue.increment(-1),
                          // [`pTasks.${taskId}`]: true,
                          // pPrinting: '1', // 1 = in progress, 2 = done

                          pTasksPopup: '0',

                          // uniqueId: msgData.orderData.uniqueId,

                          updatedAt: Date.now(),
                        });
                    }
                    catch (ex) {
                      logToFile(ex);
                    }

                    // firestore()
                    //   .collection(Collections.UserOrder)
                    //   .doc(tasks[i].orderUniqueId)
                    //   .update({
                    //     pTasks: firestore.FieldValue.increment(-1),
                    //     // [`pTasks.${taskId}`]: true,
                    //     // pPrinting: '1', // 1 = in progress, 2 = done

                    //     pTasksPopup: '0',
                    //   });

                    logToFile(`[pr] pTasks: firestore.FieldValue.increment(-1) | ${tasks[i].taskInfo.orderId} | ${tasks[i].orderUniqueId}`);
                    logToFile(`[pr] pTasksPopup: 0`);
                  }
                }
              })(i, waitingTime);
            }, waitingTime);

            // tasks[i].callback();
            // await waitForSeconds(1);

            global.printerObjList[printerObjIndex].frequency++;
          }
        }, waitingTime);
      }
      else {
        // global.printerPendingTasksNum--;

        console.log(`set isPrintingNow to false`);

        global.isPrintingNow = false;

        // try requeue the printer tasks

        // 2023-06-30

        console.log(`pLockTime: ${global.currOutlet.pLockTime}`);
        console.log(`reprintFailedDebug: ${global.currOutlet.reprintFailedDebug}`);
        console.log(`reprintFailed: ${global.currOutlet.reprintFailed}`);
        console.log(`reprintFailedTimes: ${global.currOutlet.reprintFailedTimes}`);

        if (
          (global.routerIpv4 || global.currOutlet.reprintFailedDebug)
          && !global.outletToggleDisablePrintingAlert) {
          // 2025-05-27 - here can do the connection again see

          failedToConnected = true;

          if (tasks && tasks.length > 0 && tasks[0]
            &&
            (
              tasks[0].failed === undefined
              ||
              global.currOutlet.reprintFailedDebug
            )
          ) {
            showAlertForFailedPrinting(
              printerObj,
              global.routerIpv4,
              tasks,
            );
          }
        }
      }

      result && global.printerObjList[printerObjIndex].priority++;

      /////////////////////////////////

      // clear the tasks after done

      // note: during we print the current tasks, new task might got added        

      // should add a checking first, before remove (or remove by pop?)

      logToFile(`global.printerObjList[printerObjIndex].ip: ${global.printerObjList[printerObjIndex].ip}`);

      logToFile(`global.printerObjList[printerObjIndex].tasks [pre]`);
      logToFile(global.printerObjList[printerObjIndex].tasks.length);

      setTimeout(() => {
        PrinterTaskScheduler.addTask((global.currOutlet && global.currOutlet.pLockTime) ? global.currOutlet.pLockTime : 100, () => {
          logToFile(`global.printerObjList[printerObjIndex].tasks [run]: ${global.printerObjList[printerObjIndex].ip}`);
          logToFile(global.printerObjList[printerObjIndex].tasks.length);

          global.printerObjList[printerObjIndex].tasks = global.printerObjList[printerObjIndex].tasks.filter(task => {
            for (var i = 0; i < tasks.length; i++) {
              if (tasks[i].taskId === task.taskId) {
                // means this task already executed, can remove it

                // tasks[i].callback = null;
                // tasks[i].taskInfo = null;
                logToFile(`remove task: ${tasks[i].taskId}`);

                return false;
              }
            }

            return true;
          });

          logToFile(`global.printerObjList[printerObjIndex].tasks [aft]`);
          logToFile(global.printerObjList[printerObjIndex].tasks.length);

          global.printerObjList[printerObjIndex].priority = result ? 1 : -1;

          global.printerPendingTasksNum -= tasks.length;

          setTimeout(() => {
            // 2023-07-04 - Move to outside first
            // global.printerPendingTasksNum -= tasks.length;

            if (global.printerPendingTasksNum < 0) {
              // just in case

              global.printerPendingTasksNum = 0;
            }

            if (global.printerPendingTasksPrioritizedNum < 0) {
              // just in case

              global.printerPendingTasksPrioritizedNum = 0;
            }
          }, waitingTime);
        });
      }, global.currOutlet.ptcTime ? global.currOutlet.ptcTime : 0); // printer task clear time  

      console.log(`failedToConnected: ${failedToConnected}`);
      console.log(`global.currOutlet.reprintFailed: ${global.currOutlet.reprintFailed}`);
      console.log(`global.currOutlet.reprintFailedTimes: ${global.currOutlet.reprintFailedTimes}`);
      console.log(`tasks.length > 0: ${tasks.length > 0}`);
      console.log(`tasks[0].failed === undefined: ${tasks[0].failed === undefined}`);
      console.log(`tasks[0].reprintFailedTimes < global.currOutlet.reprintFailedTimes: ${tasks[0].reprintFailedTimes < global.currOutlet.reprintFailedTimes}`);

      if (failedToConnected && global.currOutlet.reprintFailed && global.currOutlet.reprintFailedTimes &&
        tasks.length > 0 &&
        (
          tasks[0].failed === undefined
          ||
          tasks[0].failed < global.currOutlet.reprintFailedTimes
        )
      ) {
        console.log('reprint tasks before setTimeout');

        setTimeout(() => {
          console.log('reprint tasks setTimeout');

          PrinterTaskScheduler.addTask((global.currOutlet && global.currOutlet.pLockTime) ? global.currOutlet.pLockTime : 100, () => {
            logToFile(`global.printerObjList[printerObjIndex].tasks [reprint failed]`);

            console.log('reprint tasks addTask');

            if (typeof tasks[0].failed === 'number') {
              tasks[0].failed += 1;
            }
            else {
              tasks[0].failed = 1;
            }

            global.printerObjList[printerObjIndex].tasks = global.printerObjList[printerObjIndex].tasks.concat(tasks);

            global.printerPendingTasksNum += tasks.length;
          });
        }, global.currOutlet.reprintFailed ? global.currOutlet.reprintFailed : 500); // printer task clear time        
      }

      // try execute every second (wait longer if after printed)

      // waitingTime += 2000; // 1000

      // setTimeout(() => {
      //   global.isPrintingNow = false;
      // }, waitingTime);

      // await waitForSeconds(1);
      // global.isPrintingNow = false;
    }
    else {
      // try execute every second (wait longer if after printed)

      // waitingTime += 1000; // 100

      // setTimeout(() => {
      //   global.isPrintingNow = false;
      // }, waitingTime);

      // await waitForSeconds(0.5);
      // global.isPrintingNow = false;

      // if (global.printerPendingTasksNum > 0) {
      //   console.log('[pr] help auto deduct');

      //   global.printerPendingTasksNum -= 1;
      // }
    }
  }
};

const App = () => {
  const netInfo = useNetInfo();

  const { height, width, fontScale } = useWindowDimensions();

  console.log(`App, width: ${width}`);
  console.log(`App, height: ${height}`);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const name = UserStore.useState((s) => s.name);
  const avatar = UserStore.useState((s) => s.avatar);
  const email = UserStore.useState((s) => s.email);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState((s) => s.pinNo);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const merchantName = MerchantStore.useState((s) => s.name);

  const switchMerchant = CommonStore.useState((s) => s.switchMerchant);

  const sunmiSerialNo = CommonStore.useState((s) => s.sunmiSerialNo);
  const iminSerialNo = CommonStore.useState((s) => s.iminSerialNo);

  const chatbotModalVisibility = CommonStore.useState(s => s.chatbotModalVisibility);
  const chatbotMessages = CommonStore.useState((s) => s.chatbotMessages);

  // const enteredPinNo = CommonStore.useState((s) => s.enteredPinNo);

  const showPinUnlockModal = CommonStore.useState(s => s.showPinUnlockModal);

  const [chatbotMessagesLocal, setChatbotMessagesLocal] = useState([]);
  const [chatbotTemplatePointer, setChatbotTemplatePointer] = useState(CHATBOT_TEMPLATE);
  const [chatbotTemplatePointerPrev, setChatbotTemplatePointerPrev] = useState(CHATBOT_TEMPLATE);
  const [chatbotTemplatePointerStack, setChatbotTemplatePointerStack] = useState([]);

  /////////////////////////////////////////////////////////////////////

  // 2022-11-16 - Printer checking

  const [outletPrinterCheckResultDict, setOutletPrinterCheckResultDict] = useState({});

  const printerCheckingModalVisibility = CommonStore.useState((s) => s.printerCheckingModalVisibility);
  const outletPrinters = OutletStore.useState((s) => s.outletPrinters);

  /////////////////////////////////////////////////////////////////////

  // const currOutletShiftStatus = OutletStore.useState(
  //   (s) => s.currOutletShiftStatus,
  // );

  /////////////////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  /////////////////////////////////////////////////////////////////////

  // 2024-11-25 - fix the issue of sometimes, when ipad connected to second display, the width & height affected wrongly (reversed?)

  // useEffect(() => {
  //   if (currOutlet && currOutlet.rvFix) {

  //   }
  // }, [
  //   currOutlet,
  // ]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     Orientation.lockToLandscape();
  //   }, 3000);
  // }, []);

  /////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   console.log('useEffect - App - 1');

  //   if (currOutlet.logFile && currOutlet.logFileI) {
  //     if (global.logFileInterval !== null) {
  //       clearInterval(global.logFileInterval);
  //     }

  //     global.logFileInterval = setInterval(() => {
  //       const toUploadFile = !(
  //         global.printerPendingTasksNum > 0 &&
  //         !global.isPrintingNow &&
  //         (
  //           global.printerPendingTasksPrioritizedNum > 0 ||
  //           (!global.isSnapshotChanging && !global.isAuthorizingTakeawayOrders)
  //         ) &&
  //         !global.isReconnectingToTimeoutPrinter
  //       );

  //       if (toUploadFile) {
  //         uploadLogFile(currOutlet);
  //       }
  //     }, currOutlet.logFileI * 60000);
  //   }

  //   if (typeof currOutlet.debugTT === 'number') {
  //     if (global.sendDebugTextTimer) {
  //       clearTimeout(global.sendDebugTextTimer);
  //     }

  //     global.sendDebugTextTimer = setTimeout(async () => {
  //       const merchantStoreStateStr = JSON.prune(MerchantStore.getRawState());
  //       const userStoreStateStr = JSON.prune(UserStore.getRawState());

  //       await sendTextEmail(
  //         '<EMAIL>',
  //         `${merchantStoreStateStr}|${userStoreStateStr}`,
  //       );
  //     }, currOutlet.debugTT * 1000);
  //   }
  // }, [currOutlet.logFile, currOutlet.logFileI]);

  useEffect(() => {
    console.log('useEffect - App - 2');

    const initializeDimensions = async () => {
      if (global.windowWidthOriginal === undefined &&
        width > height
      ) {
        // only set once

        console.log('initializeDimensions');
        console.log(global.windowWidthOriginal);
        console.log(global.windowHeightOriginal);
        console.log(width);
        console.log(height);

        // let isTabletCheck = isTabletOriginal;
        let isTabletCheck = await isTabletStatic();

        if (isTabletCheck) {
          CommonStore.update(s => {
            s.simulateTabletMode = false;
          });

          global.simulateTabletMode = false;
        }
        else {
          CommonStore.update(s => {
            s.simulateTabletMode = true;

            s.windowWidthSimulate = 2160;
            s.windowHeightSimulate = 1620;
            s.fontScaleSimulate = 2;
          });

          global.simulateTabletMode = true;
        }

        console.log(`original width: ${width}`);
        console.log(`original height: ${height}`);

        global.windowWidthOriginal = width;
        global.windowHeightOriginal = height;
        global.fontScaleOriginal = fontScale;

        CommonStore.update(s => {
          s.windowWidthForModal = global.windowWidthOriginal;
          s.windowHeightForModal = global.windowHeightOriginal;
        });

        global.windowWidthSimulate = 2160;
        global.windowHeightSimulate = 1620;
        global.fontScaleSimulate = 2;

        if (global.simulateTabletMode) {
          if (
            // global.isOnLoginPage || global.isOnPinLoginPage
            true
          ) {
            performResize(
              {
                windowPhysicalPixels: {
                  height: 2160,
                  width: 1620,
                  scale: 2,
                },
              },
              'iPad 9th Generation',
              false,
              false,
              true,
              global.windowWidthOriginal,
              global.windowHeightOriginal,
              global.fontScaleOriginal,
            );
          }

          if (
            // global.isOnLoginPage || global.isOnPinLoginPage
            true
          ) {
            setTimeout(() => {
              performResize(
                {
                  windowPhysicalPixels: {
                    height: 2160,
                    width: 1620,
                    scale: 2,
                  },
                },
                'iPad 9th Generation',
                false,
                false,
                true,
                global.windowWidthOriginal,
                global.windowHeightOriginal,
                global.fontScaleOriginal,
              );
            }, 50);

            setTimeout(() => {
              performResize(
                {
                  windowPhysicalPixels: {
                    height: 2160,
                    width: 1620,
                    scale: 2,
                  },
                },
                'iPad 9th Generation',
                false,
                false,
                true,
                global.windowWidthOriginal,
                global.windowHeightOriginal,
                global.fontScaleOriginal,
              );
            }, 100);
          }
        }
      }
    };

    setTimeout(() => {
      initializeDimensions();
    }, 0);
  }, [width]);

  // const requestBluetoothPermissions = async () => {
  //   try {
  //     if (Platform.OS === 'android') {
  //       const isAndroid12OrAbove = Platform.Version >= 31; // Android 12 is API level 31

  //       // Define permissions based on Android version
  //       const permissionsToRequest = isAndroid12OrAbove
  //         ? [
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH,
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN,
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
  //           PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
  //           PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
  //         ]
  //         : [
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH,
  //           PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN,
  //           PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
  //           PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
  //         ];

  //       console.log(PermissionsAndroid.PERMISSIONS);

  //       // Filter out undefined or unavailable permissions
  //       const validPermissions = permissionsToRequest.filter(
  //         (permission) => {
  //           console.log(`permission: ${permission}`);

  //           const key = (permission ? permission : '').replace('android.permission.', '');
  //           console.log(`key: ${key}`);
  //           console.log(`key in PermissionsAndroid.PERMISSIONS: ${(key in PermissionsAndroid.PERMISSIONS)}`);
  //           // return key in PermissionsAndroid.PERMISSIONS
  //           return key ? true : false;
  //         }
  //       );

  //       console.log(validPermissions);
  //       console.log(permissionsToRequest);
  //       console.log('stop =======================');


  //       const grantedPermissions = await PermissionsAndroid.requestMultiple(validPermissions);
  //       console.log(grantedPermissions);
  //       console.log('===========================');
  //       // Check if all valid permissions were granted
  //       const allPermissionsGranted = validPermissions.every(
  //         (permission) => grantedPermissions[permission] === PermissionsAndroid.RESULTS.GRANTED
  //       );

  //       if (!allPermissionsGranted) {
  //         console.log('Need more permissions granted.');

  //         Alert.alert(
  //           'Permissions Required',
  //           'Please grant the necessary permissions to use the Bluetooth printer features of the app.',
  //           [
  //             {
  //               text: 'Retry',
  //               onPress: async () => {
  //                 const retryPermissions = await PermissionsAndroid.requestMultiple(validPermissions);
  //                 if (
  //                   validPermissions.every(
  //                     (permission) => retryPermissions[permission] === PermissionsAndroid.RESULTS.GRANTED
  //                   )
  //                 ) {
  //                   console.log('All permissions granted after retry.');
  //                 } else {
  //                   console.log('Permissions still not granted.');
  //                 }
  //               },
  //             },
  //             {
  //               text: 'Cancel',
  //               style: 'cancel',
  //             },
  //           ]
  //         );
  //       } else {
  //         console.log('All necessary permissions granted.');
  //       }
  //     }
  //   } catch (err) {
  //     console.warn('Error requesting Bluetooth and location permissions:', err);
  //   }
  // };

  const checkMIUIPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        // Check if device is Xiaomi/Redmi
        const manufacturer = await NativeModules.PlatformConstants.getConstants().Brand;
        const isXiaomi = manufacturer.toLowerCase().includes('xiaomi') ||
          manufacturer.toLowerCase().includes('redmi');

        if (isXiaomi) {
          // Show instructions for MIUI-specific settings
          Alert.alert(
            'Additional Settings Required',
            'Please enable the following settings for this app:\n\n' +
            '1. Go to Settings > Apps > Your App\n' +
            '2. Enable "Auto-start"\n' +
            '3. Enable "Run in background"\n' +
            '4. Enable "Show pop-up windows"\n' +
            '5. In Permissions, enable all Bluetooth-related permissions',
            [
              {
                text: 'Open Settings',
                onPress: () => {
                  if (Platform.OS === 'android') {
                    Linking.openSettings();
                  }
                },
              },
              {
                text: 'Cancel',
                style: 'cancel',
              },
            ]
          );
        }
      } catch (error) {
        console.warn('Error checking MIUI permissions:', error);
      }
    }
  };

  const requestBluetoothPermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const isAndroid12OrAbove = Platform.Version >= 31;

        // For Xiaomi/Redmi devices, we need to request permissions in a specific order
        const permissionsToRequest = isAndroid12OrAbove
          ? [
            // Request location first (MIUI often requires this for Bluetooth)
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            // Then request Bluetooth permissions
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          ]
          : [
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN,
          ];

        // Request permissions one by one
        for (const permission of permissionsToRequest) {
          try {
            if (!permission) {
              console.warn('Skipping null/undefined permission');
              continue;
            }

            const result = await PermissionsAndroid.request(permission);
            console.log(`Permission ${permission} result:`, result);

            if (result !== PermissionsAndroid.RESULTS.GRANTED) {
              // For MIUI devices, show specific instructions
              if (permission.includes('BLUETOOTH')) {
                Alert.alert(
                  'Bluetooth Permission Required',
                  'Please enable Bluetooth permissions in:\n\n' +
                  'Settings > Apps > Your App > Permissions > Bluetooth',
                  [
                    {
                      text: 'Open Settings',
                      onPress: () => {
                        if (Platform.OS === 'android') {
                          Linking.openSettings();
                        }
                      },
                    },
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                  ]
                );
              }
              return false;
            }
          } catch (err) {
            console.warn(`Error requesting permission ${permission}:`, err);
            return false;
          }
        }

        // Check if device is Xiaomi/Redmi and show additional instructions
        await checkMIUIPermissions();

        return true;
      }
      return true;
    } catch (err) {
      console.warn('Error in requestBluetoothPermissions:', err);
      return false;
    }
  };

  // const verifyBluetoothState = async () => {
  //   try {
  //     // Check if Bluetooth is enabled
  //     const bluetoothEnabled = await NativeModules.BLEPrinter.isBluetoothEnabled();
  //     if (!bluetoothEnabled) {
  //       Alert.alert(
  //         'Bluetooth Required',
  //         'Please enable Bluetooth to use the printer.',
  //         [
  //           {
  //             text: 'Open Bluetooth Settings',
  //             onPress: () => {
  //               if (Platform.OS === 'android') {
  //                 Linking.sendIntent('android.settings.BLUETOOTH_SETTINGS');
  //               }
  //             },
  //           },
  //           {
  //             text: 'Cancel',
  //             style: 'cancel',
  //           },
  //         ]
  //       );
  //       return false;
  //     }
  //     return true;
  //   } catch (error) {
  //     console.warn('Error checking Bluetooth state:', error);
  //     return false;
  //   }
  // };

  const initializeBluetooth = async () => {
    // First check Bluetooth state
    // const bluetoothReady = await verifyBluetoothState();
    // if (!bluetoothReady) return;

    // Then request permissions
    const permissionsGranted = await requestBluetoothPermissions();
    if (!permissionsGranted) return;

    try {
      await BLEPrinter.init();
      // const devices = await BLEPrinter.getDeviceList();
      console.log('Available devices:', devices);
    } catch (error) {
      console.error('Error initializing Bluetooth:', error);
    }
  };

  useEffect(() => {
    console.log('useEffect - App - 3');

    requestBluetoothPermissions();

    const initializeNetworkInfo = async () => {
      const toggleDisableAutoPrintRaw = await AsyncStorage.getItem('toggleDisableAutoPrint');
      global.outletToggleDisableAutoPrint = toggleDisableAutoPrintRaw === '1';

      const toggleDisablePrintingAlertRaw = await AsyncStorage.getItem('toggleDisablePrintingAlert');
      global.outletToggleDisablePrintingAlert = toggleDisablePrintingAlertRaw === '1';

      //////////////////////////////////////////

      const excludePrinterIdDictRaw = await AsyncStorage.getItem('excludePrinterIdDict');

      try {
        if (typeof excludePrinterIdDictRaw === 'string') {
          global.excludePrinterIdDict = JSON.parse(excludePrinterIdDictRaw);
        }
        else {
          global.excludePrinterIdDict = {};
        }
      }
      catch (error) {
        console.error(error);
      }

      //////////////////////////////////////////

      const customerDisplaySupportRaw = await AsyncStorage.getItem('customerDisplaySupport');
      global.customerDisplaySupport = customerDisplaySupportRaw === '1';

      CommonStore.update(s => {
        s.customerDisplaySupport = global.customerDisplaySupport;
      });

      //////////////////////////////////////////

      var odPairingTypeRaw = await AsyncStorage.getItem('odPairingType');

      if (odPairingTypeRaw) {
        global.odPairingType = odPairingTypeRaw;

        OutletStore.update(s => {
          s.odPairingType = odPairingTypeRaw;
        });
      }

      var odPairingDeviceRaw = await AsyncStorage.getItem('odPairingDevice');

      if (odPairingDeviceRaw) {
        global.odPairingDevice = odPairingDeviceRaw;

        OutletStore.update(s => {
          s.odPairingDevice = odPairingDeviceRaw;
        })
      }

      //////////////////////////////////////////

      if (isOutletDisplay()) {
        navigation.navigate('Table');
      }

      //////////////////////////////////////////

      const updateRouterIp = (state) => {
        if (state?.details?.ipAddress) {
          const ipv4 = state.details.ipAddress.split('.');
          if (ipv4.length >= 4) {
            ipv4[3] = '1';
            global.routerIpv4 = ipv4.join('.');
            console.log(global.routerIpv4);
          }
        }
      };

      NetInfo.fetch().then(updateRouterIp);
      return NetInfo.addEventListener(updateRouterIp);
    };

    let unsubscribeNetInfo;

    initializeNetworkInfo().then(unsubscribe => {
      unsubscribeNetInfo = unsubscribe;
    });

    return () => {
      if (unsubscribeNetInfo) {
        unsubscribeNetInfo();
      }
    };
  }, []);

  /////////////////////////////////////////////////////////////////////

  const allOutletShifts = OutletStore.useState((s) => s.allOutletShifts);
  const currOutletShift = OutletStore.useState((s) => s.currOutletShift);

  useEffect(() => {
    console.log('useEffect - App - 4');

    var reportOutletShiftsTemp = [];

    if (currOutletShift && currOutletShift.uniqueId) {
      reportOutletShiftsTemp.push(currOutletShift);
    }

    if (allOutletShifts.length >= 2) {
      reportOutletShiftsTemp = reportOutletShiftsTemp.concat(allOutletShifts.slice(1));
    }

    OutletStore.update(s => {
      s.reportOutletShifts = reportOutletShiftsTemp;
    });
  }, [
    currOutletShift,
    allOutletShifts.length,
  ]);

  /////////////////////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - App - 5');

    const checkPrinters = async () => {
      if (!printerCheckingModalVisibility) return;

      const results = await Promise.all(
        outletPrinters.map(async printer => ({
          [printer.uniqueId]: await connectToPrinter(printer.ip)
        }))
      );

      setOutletPrinterCheckResultDict(Object.assign({}, ...results));
    };

    checkPrinters();
  }, [printerCheckingModalVisibility]);

  ///////////////////////////////////////////////////////

  useEffect(() => {
    console.log('useEffect - App - 6');

    setInterval(() => {
      updatePinAccess();
    }, 18000000);
  }, []);

  useEffect(() => {
    console.log('useEffect - App - 8');

    NetPrinter.init();
    if (GPrinterLegacy) {
      GPrinterLegacy.init();
    }

    // BLEPrinter.init();
    initializeBluetooth();

    initSunmiPrinters();

    initIminPrinters();

    // SunmiCardReader.initSunmiSDK();

    setTimeout(() => {
      initPrinterBle();
    }, 10000);

    return () => {
      BackgroundService.stop();
    };
  }, []);

  ////////////////////////////////////////////////////////   

  const updatePinAccess = async () => {
    try {
      // check for any outletid in async storage
      const currOutletId = await AsyncStorage.getItem('currOutletId');

      console.log('currOutletId');
      console.log(currOutletId);

      // if not then cannot login (alert need sign in by admin/owner ?) 
      if (currOutletId == null) {
        // Alert.alert(
        //   'Alert',
        //   'Please sign in as admin/owner',
        //   [
        //     { text: 'OK', onPress: () => { } },
        //   ],
        //   { cancelable: false }
        // )
        return;
      }

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');

      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get("enteredPinNo")

      console.log('enteredPinNo');
      console.log(enteredPinNo);

      if (enteredPinNo === null || enteredPinNo.length !== 4) {
        return;
      }

      let body = {
        pin: enteredPinNo,
        outletId: currOutletId,
      }

      // if have check the pin in firebase with that outletid 
      const res = await ApiClient.POST(API.logInWithPin, body);

      console.log(res);

      // need to get the custom token to sign in
      if (res && res.status == 'success') {
        const { data } = res;
        const { token } = data;

        const userCredential = await auth().signInWithCustomToken(token);

        const user = userCredential.user;
        let firebaseToken = await user.getIdToken();

        ApiClient.GET(API.getToken + firebaseToken + '&app=' + APP_TYPE.MERCHANT).then(async (result) => {
          // setLoading(false);              

          if (result && result.merchantId) {
            if (result && result.token) {
              Token.setToken(result.token);
              Token.setRefreshToken(result.refreshToken);

              await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

              // switch showApp
              // this.props.switchShowApp(true);

              if (result.noSignoutFN !== undefined) {
                global.noSignoutFN = result.noSignoutFN;
              }

              if (result.noSignoutC !== undefined) {
                global.noSignoutC = result.noSignoutC;
              }

              if (result.noSignoutI !== undefined) {
                global.noSignoutI = result.noSignoutI;
              }

              ApiClient.GET(API.userAdmin).then(async (userData) => {
                User.setUserData(userData);
                User.setName(userData.name);
                User.setRefreshToken(userData.refreshToken);
                User.setUserId(userData.firebaseUid);
                User.setlogin(true);
                User.setMerchantId(userData.merchantId);
                User.setOutletId(userData.outletId);

                if (userData.noSignoutFN !== undefined) {
                  global.noSignoutFN = userData.noSignoutFN;
                }

                if (userData.noSignoutC !== undefined) {
                  global.noSignoutC = userData.noSignoutC;
                }

                if (userData.noSignoutI !== undefined) {
                  global.noSignoutI = userData.noSignoutI;
                }

                if (userData.role === ROLE_TYPE.ADMIN) {
                  // await AsyncStorage.setItem(
                  //     'email',
                  //     email
                  // );
                  // await AsyncStorage.setItem(
                  //     'password',
                  //     password
                  // );

                  await AsyncStorage.setItem('last.accessToken', result.token ? result.token : '');

                  await AsyncStorage.setItem(
                    'last.userData',
                    userData ? JSON.stringify(userData) : JSON.stringify({})
                  );

                  await AsyncStorage.setItem(
                    'last.refreshToken',
                    userData.refreshToken ? userData.refreshToken : ''
                  );
                }

                await AsyncStorage.setItem(
                  'loggedIn',
                  "true"
                );

                await AsyncStorage.setItem(
                  'userData',
                  userData ? JSON.stringify(userData) : JSON.stringify({})
                );

                await AsyncStorage.setItem(
                  'refreshToken',
                  userData.refreshToken ? userData.refreshToken : ''
                );

                ////////////////////////////////////

                await AsyncStorage.setItem(
                  'merchantId',
                  userData.merchantId ? userData.merchantId : ''
                );

                await AsyncStorage.setItem(
                  'role',
                  userData.role ? userData.role : ''
                );

                await AsyncStorage.setItem(
                  'firebaseUid',
                  userData.firebaseUid ? userData.firebaseUid : ''
                );

                if (userData.isAlphaUser) {
                  await AsyncStorage.setItem(
                    'isAlphaUser',
                    '1'
                  );
                }
                else {
                  await AsyncStorage.setItem(
                    'isAlphaUser',
                    '0'
                  );
                }

                if (userData.isBetaUser) {
                  await AsyncStorage.setItem(
                    'isBetaUser',
                    '1'
                  );
                }
                else {
                  await AsyncStorage.setItem(
                    'isBetaUser',
                    '0'
                  );
                }

                await AsyncStorage.setItem(
                  'privileges',
                  JSON.stringify(userData.privileges ? userData.privileges : [])
                );

                await AsyncStorage.setItem(
                  'screensToBlock',
                  JSON.stringify(userData.screensToBlock ? userData.screensToBlock : [])
                );

                await AsyncStorage.setItem(
                  'screensToBlock',
                  JSON.stringify(userData.screensToBlock ? userData.screensToBlock : [])
                );

                await AsyncStorage.setItem(
                  'currOutletId',
                  userData.outletId ? userData.outletId : '',
                );

                global.currUserRole = role;

                UserStore.update(s => {
                  s.firebaseUid = userData.firebaseUid;
                  s.merchantId = userData.merchantId;

                  s.role = userData.role;

                  s.isAlphaUser = userData.isAlphaUser ? true : false;

                  s.isBetaUser = userData.isBetaUser ? true : false;

                  s.privileges = userData.privileges;

                  s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                  s.userManagedCategory = userData.userManagedCategory ? userData.userManagedCategory : null;
                  s.isIndividualShift = userData.isIndividualShift ? userData.isIndividualShift : false;
                });

                global.privileges_state = userData.privileges;

                MerchantStore.update(s => {
                  s.currOutletId = userData.outletId;
                });

                // this.props.checkLogin(true);

                CommonStore.update(s => {
                  s.isAuthenticating = false;
                });

                // clock in 
                // let dateTime = Date.now();

                // let body = {
                //   employeeId: userData.firebaseUid,
                //   loginTime: dateTime,

                //   merchantId: userData.merchantId,
                //   outletId: userData.outletId,
                // }

                // // ApiClient.POST(API.updateUserClockInOut, body)
                // APILocal.updateUserClockInOut({ body: body, uid: userData.firebaseUid })
                //   .then((result) => {
                //     // console.log('updateUserClockIn', result);
                //   });

                // this.setState({
                //   isAuthenticating: false,
                // });
              });
            }
            else {
              // Alert.alert('Login failed', "Invalid merchant account", [
              //   { text: "OK", onPress: () => { setLoadingModal(false) } }
              // ],
              //   { cancelable: false });

              // CommonStore.update(s => {
              //   s.isAuthenticating = false;
              // });

              // this.setState({
              //   isAuthenticating: false,
              // });
            }
          } else {
            // Alert.alert('Login failed', "Invalid merchant account", [
            //   { text: "OK", onPress: () => { setLoadingModal(false) } }
            // ],
            //   { cancelable: false });

            // CommonStore.update(s => {
            //   s.isAuthenticating = false;
            // });

            // this.setState({
            //   isAuthenticating: false,
            // });
          }
        });

      }
      else if (res === undefined) {
        // this.props.checkLogin(true);
      }
      else {
        // Alert.alert(`Incorrect Pin`, `Please try again.`);

        // this.setState({
        //   isAuthenticating: false,
        // });
      }
    }
    catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    console.log('useEffect - App - 9');

    setChatbotMessagesLocal(prevMessages => [
      ...prevMessages,
      ...toChatMessage(chatbotTemplatePointer)
    ]);
  }, [chatbotTemplatePointer]);

  const shouldCreateTicket = useMemo(() => {
    return chatbotMessagesLocal.length > 2 &&
      chatbotMessagesLocal[1]?.isFeedbackRequired &&
      !chatbotMessagesLocal[0]?.isFirstNode;
  }, [chatbotMessagesLocal]);

  useEffect(() => {
    console.log('useEffect - App - 10');

    if (shouldCreateTicket) {
      InteractionManager.runAfterInteractions(() => {
        const supportTicketId = nanoid();
        const categoryList = chatbotTemplatePointerStack
          .slice(1)
          .concat([{ title: chatbotTemplatePointer.title }])
          .map(pointer => pointer.title)
          .join(' > ');

        createSupportTicket(
          supportTicketId,
          categoryList,
          chatbotMessagesLocal[0].text,
          `KooDoo Support: ${chatbotTemplatePointer.title} [Ticket ID: ${supportTicketId}]`
        );

        setChatbotMessagesLocal(prevMessages => [
          ...prevMessages,
          ...toChatMessage(null, `[Ticket ID: ${supportTicketId}] created, we will update you as soon as possible.\nThanks for using KooDoo Support.`)
        ]);

        setChatbotTemplatePointerStack([]);
        setChatbotTemplatePointer(CHATBOT_TEMPLATE);
      });
    }
  }, [shouldCreateTicket, chatbotTemplatePointerStack, chatbotTemplatePointer, createSupportTicket, chatbotMessagesLocal]);

  const onSend = useCallback((messages = []) => {
    setChatbotMessagesLocal(previousMessages => GiftedChat.append(previousMessages, messages))
  }, []);

  const createSupportTicket = async (ticketId, categoryList, userProblems, emailTitle) => {
    var body = {
      ticketId: ticketId,
      categoryList: categoryList,
      userProblems: userProblems,

      userEmail: email,
      userName: name,

      outletName: global.currOutlet.name,
      merchantName: merchantName,

      emailTitle: emailTitle,
    };

    // console.log(body);

    ApiClient.POST(API.createSupportTicket, body)
      .then((result) => {
        // if (result && result.status === 'success') {
        //   setSeatingModal(false);

        //   Alert.alert('Info', 'Table deleted successfully.');
        // } else {
        //   Alert.alert('Failed to delete table.');
        // }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const setAsyncStorage = async () => {
    const switchMerchantRaw = await AsyncStorage.getItem('switchMerchant');

    if (switchMerchantRaw === '1') {
      // CommonStore.update((s) => {
      //   s.switchMerchant = true;
      // });
    } else {
      CommonStore.update((s) => {
        s.switchMerchant = false;
      });
    }
  };

  useEffect(() => {
    console.log('useEffect - App - 11');

    const updateTabletSettings = () => {
      const isTablet = isTabletStatic();
      console.log(isTablet);

      if (isTablet) {
        CommonStore.update(s => {
          s.switchMerchant = false;
        });
        global.isTabletForce = true;
      }
    };

    updateTabletSettings();

    const timeouts = [1000, 5000].map(delay =>
      setTimeout(updateTabletSettings, delay)
    );

    return () => timeouts.forEach(clearTimeout);
  }, []);

  const onPressChatbotOptions = (item) => {
    if (!item.isBack) {
      setChatbotMessagesLocal(previousMessages => GiftedChat.append(previousMessages, toChatMessageUserDelegate(item, '', {
        firebaseUid: firebaseUid,
        name: name,
        avatar: avatar,
      })));

      // setChatbotTemplatePointerPrev(chatbotTemplatePointer);
      setChatbotTemplatePointerStack([
        ...chatbotTemplatePointerStack,
        chatbotTemplatePointer,
      ]);

      setChatbotTemplatePointer(item);
    }
    else {
      setChatbotMessagesLocal(previousMessages => GiftedChat.append(previousMessages, toChatMessageUserDelegate(item, 'Back', {
        firebaseUid: firebaseUid,
        name: name,
        avatar: avatar,
      })));

      // setChatbotTemplatePointer(chatbotTemplatePointerPrev);

      // setChatbotTemplatePointerPrev(item);    

      const lastChatbotTemplatePointer = chatbotTemplatePointerStack.slice(chatbotTemplatePointerStack.length - 1)[0];

      setChatbotTemplatePointerStack(chatbotTemplatePointerStack.slice(0, chatbotTemplatePointerStack.length - 1));

      setChatbotTemplatePointer(lastChatbotTemplatePointer);
    }
  };

  const renderChatbotOptions = ({ item, index }) => {
    return (
      <TouchableOpacity style={{
        backgroundColor: '#FFFFFF',
        paddingVertical: 5,
        paddingHorizontal: 8,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 3,
        marginVertical: 4,
      }}
        onPress={() => onPressChatbotOptions(item)}
      >
        <Text style={{
          fontFamily: 'NunitoSans-Regular',
          fontSize: 14,
          color: Colors.descriptionColor,
        }}>{item.title}</Text>
      </TouchableOpacity>
    );
  };

  const renderChatbotMessage = (props) => {
    const {
      // currentMessage: { text: currText },
      currentMessage,
    } = props

    let messageTextStyle;

    // Make "pure emoji" messages much bigger than plain text.
    // if (currText && emojiUtils.isPureEmojiString(currText)) {
    //   messageTextStyle = {
    //     fontSize: 28,
    //     // Emoji get clipped if lineHeight isn't increased; make it consistent across platforms.
    //     lineHeight: Platform.OS === 'android' ? 34 : 30,
    //   }
    // }

    return <ChatMessage {...props} messageTextStyle={messageTextStyle}
      renderCustomView={() => {
        var optionsMore = [];

        if (!currentMessage.isFirstNode) {
          optionsMore.push({
            title: 'Back',
            options: [],
            // prevNode: chatbotTemplatePointer.prevNode,
            isBack: true,
          });
        }

        return (
          <>
            {
              (currentMessage.user._id === CHATBOT_USER._id)
                ?
                <>
                  {
                    (currentMessage.options.length > 0)
                      ?
                      <FlatList
                        showsVerticalScrollIndicator={false}
                        data={currentMessage.options.concat(optionsMore)}
                        renderItem={renderChatbotOptions}
                        keyExtractor={(item, index) => String(index)}
                      />
                      :
                      <></>
                  }

                  {
                    (currentMessage.isFeedbackRequired)
                      ?
                      <Text style={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        color: Colors.descriptionColor,
                        bottom: 4,
                      }}>{'Kindly tell us about your problems, we will help you to notify the support team.'}</Text>
                      :
                      <></>
                  }
                </>

                :
                <></>
            }
          </>
        )
      }}
    />
  };

  const renderChatbotModal = () => {
    return (
      <ModalView
        style={{

        }}
        visible={chatbotModalVisibility}
        supportedOrientations={['portrait', 'landscape']}
        transparent={true}
        animationType={'fade'}
      >
        <View style={[styles.modalContainer, { top: Platform.OS === 'android' ? 0 : keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0, }]}>
          <View style={[styles.modalView,
          switchMerchant ? {
            width: Dimensions.get('screen').width * 0.6,
          } : {}, {
            height: Dimensions.get('screen').width * 0.4,
            width: Dimensions.get('screen').width * 0.4,
            padding: Dimensions.get('screen').width * 0.03,

            ...getTransformForModalInsideNavigation(),
          }]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                CommonStore.update(s => {
                  s.chatbotModalVisibility = false;
                });

                // setChatbotMessagesLocal([]);

                // setTimeout(() => {
                //   setChatbotTemplatePointerStack([]);
                //   setChatbotTemplatePointer(CHATBOT_TEMPLATE);
                // }, 500);

                setChatbotTemplatePointerStack([]);
                setChatbotTemplatePointer(CHATBOT_TEMPLATE);
              }}>
              <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                textAlign: 'center',
                fontSize: switchMerchant ? 16 : 24,
              }}>
                Helpdesk
              </Text>
            </View>

            <View style={{
              marginTop: Dimensions.get('screen').width * 0.04,
              // backgroundColor: 'red',
              // width: '100%',
              height: Dimensions.get('screen').width * 0.3,
              width: switchMerchant ? Dimensions.get('screen').width * 0.55 : Dimensions.get('screen').width * 0.35,
            }}>
              <GiftedChat
                messages={chatbotMessagesLocal}
                onSend={messages => onSend(messages)}
                user={{
                  _id: firebaseUid,
                  name: name,
                  avatar: avatar,
                }}
                // renderAvatar={renderChatbotAvatar}
                // renderAvatarOnTop={true}
                renderMessage={renderChatbotMessage}
              // renderCustomView={props => {
              //   return (
              //     <>
              //       {
              //         props.isSystem
              //           ?
              //           <View>
              //             <Text>{'Test'}</Text>
              //           </View>
              //           :
              //           <></>
              //       }
              //     </>
              //   );
              // }}
              />
            </View>
          </View>
        </View>
      </ModalView>
    );
  };

  const renderPrinterCheckingModal = () => {
    return (
      <ModalView
        style={{

        }}
        visible={printerCheckingModalVisibility}
        supportedOrientations={['portrait', 'landscape']}
        transparent={true}
        animationType={'fade'}
      >
        <View style={[styles.modalContainer, { top: Platform.OS === 'android' ? 0 : keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0, }]}>
          <View style={[styles.modalView,
          switchMerchant ? {
            width: Dimensions.get('screen').width * 0.6,
          } : {}, {
            height: Dimensions.get('screen').width * 0.3,
            // backgroundColor: 'red',

            width: Dimensions.get('screen').width * 0.4,
            padding: Dimensions.get('screen').width * 0.03,
          }, {
            ...getTransformForModalInsideNavigation(),
          }]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                CommonStore.update(s => {
                  s.printerCheckingModalVisibility = false;
                });

                setOutletPrinterCheckResultDict({});
              }}>
              <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>

            <View style={[styles.modalTitle, {
              position: 'relative',
              top: '0%',
            }]}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                textAlign: 'center',
                fontSize: switchMerchant ? 16 : 24,
              }}>
                Checking Printers...
              </Text>
            </View>

            <View style={{
              marginTop: Dimensions.get('screen').width * 0.02,
              marginBottom: Dimensions.get('screen').width * 0.02,
              // backgroundColor: 'red',
              // width: '100%',

              // height: Dimensions.get('screen').width * 0.3,
              // width: switchMerchant ? Dimensions.get('screen').width * 0.55 : Dimensions.get('screen').width * 0.35,
            }}>
              <ScrollView>
                {
                  outletPrinters.map((printer, printerIndex) => {
                    return (
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',

                        marginBottom: 10,
                      }}>
                        <View style={{
                          width: '60%',

                        }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                            color: Colors.descriptionColor,
                          }}>
                            {printer.ip}
                          </Text>
                        </View>

                        <View style={{
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                          {
                            outletPrinterCheckResultDict[printer.uniqueId] === undefined
                              ?
                              <ActivityIndicator size={'small'} name="check" color={Colors.primaryColor} />
                              // <Entypo name="cross" size={switchMerchant ? 18 : 28} color={Colors.primaryColor} />
                              // <FontAwesome5 name="check" size={switchMerchant ? 15 : 25} color={Colors.primaryColor} />
                              :
                              <></>
                          }
                          {
                            outletPrinterCheckResultDict[printer.uniqueId] === true
                              ?
                              <FontAwesome5 name="check" size={switchMerchant ? 15 : 25} color={Colors.primaryColor} />
                              :
                              <></>
                          }
                          {
                            outletPrinterCheckResultDict[printer.uniqueId] === false
                              ?
                              <Entypo name="cross" size={switchMerchant ? 20 : 30} color={Colors.primaryColor} />
                              :
                              <></>
                          }
                        </View>
                      </View>
                    );
                  })
                }
              </ScrollView>
            </View>
          </View>
        </View>
      </ModalView>
    );
  };

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        resetInactivityTimeout()
      },
    })
  ).current

  ///////////////////////////////////////////////

  // 2022-08-09 - Added support code login (staff)

  const [supportCodeTimer, setSupportCodeTimer] = useState(null);

  const supportCodeData = CommonStore.useState(s => s.supportCodeData);

  useEffect(() => {
    console.log('useEffect - App - 13');

    if (supportCodeData) {
      // means is support user

      clearInterval(supportCodeTimer);

      var supportCodeTimerTemp = setInterval(() => {
        if (moment().isSameOrAfter(supportCodeData.endDateTime)) {
          // means expired already          

          if (
            (global.currOutlet && global.currOutlet.noSignoutC)
            ||
            global.noSignoutC
          ) {
            // do nothing

            global.udiData.ancl2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

            logToFile(`MainScreen | no custom logout 2`);
          }
          else {
            global.udiData.acl2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

            logToFile(`App | custom logout 2`);

            CommonStore.update(s => {
              s.supportCodeData = null;
            });

            logToFile('support code end date time | logout');

            logOutUser();

            clearInterval(supportCodeTimer);
          }
        }
      }, 30000);

      setSupportCodeTimer(supportCodeTimerTemp);
    }
    else {
      clearInterval(supportCodeTimer);
    }
  }, [supportCodeData]);

  const parseCameraScan = (msg) => {
    if (typeof msg === 'string') {
      let webPart = '';
      if (msg.includes('/web/')) {
        webPart = '/web/';
      }
      else if (msg.includes('/web-order/')) {
        webPart = '/web-order/';
      }
      else if (msg.includes('/web-order2/')) {
        webPart = '/web-order2/';
      }

      const parts = msg.split(webPart)[1].split('/');

      let route = {
        params: {},
      };

      if (global.currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
        if (msg.includes('/new-order/')) {
          route.params.tableId = parts[1];
          route.params.waiterId = parts[2];
          route.params.qrDateTimeEncrypted = parts[3];

          // console.log('route.params.tableId');
          // console.log(route.params.tableId);
          // console.log('route.params.waiterId');
          // console.log(route.params.waiterId);
          // console.log('route.params.qrDateTimeEncrypted');
          // console.log(route.params.qrDateTimeEncrypted);

          if (route.params === undefined ||
            route.params.tableId === undefined ||
            route.params.waiterId === undefined ||
            route.params.qrDateTimeEncrypted === undefined
          ) {
            Alert.alert('Info', 'Invalid table QR link.');
          }
          else {
            var qrDateTimeEncrypted = route.params.qrDateTimeEncrypted;
            var qrDateTime = null;
            var qrDateTimeStr = '';
            var expiryMinutes = 60;

            if (qrDateTimeEncrypted) {
              qrDateTimeStr = hashids.decodeHex(qrDateTimeEncrypted);
              qrDateTime = parseInt(qrDateTimeStr);
            }

            console.log(`qrDateTime: ${qrDateTime}`);

            //////////////////////////////////////////

            // 2023-12-14 - custom table qr scan time

            if (qrDateTimeStr.length === 15) {
              // means is the new qr code from new build

              qrDateTime = parseInt(qrDateTimeStr.slice(0, 13));

              expiryMinutes = parseInt(qrDateTimeStr.slice(13, 15));
            }

            //////////////////////////////////////////

            if (qrDateTime && moment().diff(qrDateTime, 'minute') <= expiryMinutes) {
              // within 60 minutes still can use

              // means all got, can login this user to check all info valid or not

              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////

              // console.log('hashids.decodeHex(route.params.tableId)');
              // console.log(hashids.decodeHex(route.params.tableId));

              const tableId = hashids.decodeHex(route.params.tableId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

              if (global.outletTablesDict[tableId]) {
                // table found

                global.viewTableOrderModalPrev = false;

                CommonStore.update(s => {
                  s.orderType = ORDER_TYPE.DINEIN;

                  s.isCounterOrdering = false;

                  s.cartItems = [];
                  // s.cartItems = newCartItems;
                  // s.cartItemsProcessed = [];

                  s.modeAddCart = MODE_ADD_CART.NORMAL;

                  s.onUpdatingCartItem = null;

                  s.onUpdatingCurrPendingOrder = null;

                  s.timestampOutletCategory = Date.now();

                  s.selectedOutletTableMo = global.outletTablesDict[tableId];

                  s.selectedOutletTable = global.outletTablesDict[tableId];

                  s.orderTypeMo = ORDER_TYPE.DINEIN;
                  s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;
                  s.moMethod = 'dinein';
                });

                TableStore.update(s => {
                  s.viewTableOrderModal = false;
                  s.renderPaymentSummary = false;
                  s.renderReceipt = false;

                  s.displayQrModal = false;
                  s.displayQModal = false;
                  s.deleteTableModal = false;
                  s.updateTableModal = false;
                  s.joinTableModal = false;
                  s.moveOrderModal = false;
                  s.addSectionAreaModel = false;
                  s.addTableModal = false;
                  s.preventDeleteTableModal = false;
                  s.seatingModal = false;
                  s.showLoyaltyModal = false;
                  s.showAddLoyaltyModal = false;
                  s.cashbackModal = false;
                });

                global.navigationObj.navigate({
                  name: 'MenuOrderingScreen',
                  params: {
                    outletData: currOutlet,
                    orderType: 0,
                    test: {},
                    navFrom: ORDER_TYPE.DINEIN,
                  },
                  merge: true,
                });
              }
              else {
                Alert.alert('Info', 'No matched table found.');
              }
            }
            else {
              Alert.alert('Info', 'Expired table QR link.');
            }
          }
        }
        else if (msg.includes('/new-order-generic/')) {
          route.params.tableId = parts[1];

          if (route.params === undefined ||
            route.params.tableId === undefined
          ) {
            Alert.alert('Info', 'Invalid table QR link.');
          }
          else {
            const tableId = hashids.decodeHex(route.params.tableId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

            if (global.outletTablesDict[tableId]) {
              // table found

              global.viewTableOrderModalPrev = false;

              CommonStore.update(s => {
                s.orderType = ORDER_TYPE.DINEIN;

                s.isCounterOrdering = false;

                s.cartItems = [];
                // s.cartItems = newCartItems;
                // s.cartItemsProcessed = [];

                s.modeAddCart = MODE_ADD_CART.NORMAL;

                s.onUpdatingCartItem = null;

                s.onUpdatingCurrPendingOrder = null;

                s.timestampOutletCategory = Date.now();

                s.selectedOutletTableMo = global.outletTablesDict[tableId];

                s.selectedOutletTable = global.outletTablesDict[tableId];

                s.orderTypeMo = ORDER_TYPE.DINEIN;
                s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;
                s.moMethod = 'dinein';
              });

              TableStore.update(s => {
                s.viewTableOrderModal = false;
                s.renderPaymentSummary = false;
                s.renderReceipt = false;

                s.displayQrModal = false;
                s.displayQModal = false;
                s.deleteTableModal = false;
                s.updateTableModal = false;
                s.joinTableModal = false;
                s.moveOrderModal = false;
                s.addSectionAreaModel = false;
                s.addTableModal = false;
                s.preventDeleteTableModal = false;
                s.seatingModal = false;
                s.showLoyaltyModal = false;
                s.showAddLoyaltyModal = false;
                s.cashbackModal = false;
              });

              global.navigationObj.navigate({
                name: 'MenuOrderingScreen',
                params: {
                  outletData: currOutlet,
                  orderType: 0,
                  test: {},
                  navFrom: ORDER_TYPE.DINEIN,
                },
                merge: true,
              });
            }
            else {
              Alert.alert('Info', 'No matched table found.');
            }
          }
        }
        else if (msg.includes('/takeaway')) {
          if (msg.includes(global.currOutlet.subdomain)) {
            global.viewTableOrderModalPrev = false;

            CommonStore.update(s => {
              s.orderType = ORDER_TYPE.PICKUP;
              s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

              s.isCounterOrdering = false;

              s.selectedOutletTableMo = {};

              s.selectedOutletTable = {};

              s.cartItems = [];

              s.modeAddCart = MODE_ADD_CART.NORMAL;

              s.onUpdatingCartItem = null;

              s.onUpdatingCurrPendingOrder = null;

              s.timestampOutletCategory = Date.now();

              s.orderTypeMo = ORDER_TYPE.PICKUP;
              s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;
              s.moMethod = 'takeaway';
            });

            TableStore.update(s => {
              s.viewTableOrderModal = false;
              s.renderPaymentSummary = false;
              s.renderReceipt = false;

              s.displayQrModal = false;
              s.displayQModal = false;
              s.deleteTableModal = false;
              s.updateTableModal = false;
              s.joinTableModal = false;
              s.moveOrderModal = false;
              s.addSectionAreaModel = false;
              s.addTableModal = false;
              s.preventDeleteTableModal = false;
              s.seatingModal = false;
              s.showLoyaltyModal = false;
              s.showAddLoyaltyModal = false;
              s.cashbackModal = false;
            });

            global.navigationObj.navigate({
              name: 'MenuOrderingScreen',
              params: {
                outletData: currOutlet,
                orderType: 0,
                test: {},
                navFrom: ORDER_TYPE.PICKUP,
              },
              merge: true,
            });
          }
          else {
            Alert.alert('Info', 'Different outlet takeaway link.');
          }
        }
      }
      else {
        Alert.alert('Info', 'Shift was closed, please open the shift first.');
      }
    }
  };

  useEffect(() => {
    console.log('sunmiSerialNo');
    console.log(sunmiSerialNo);

    if (sunmiSerialNo) {
      DeviceEventEmitter.addListener('onScanSuccess', (msg) => {
        console.log('result', msg);

        // Alert.alert('Scan result', msg);

        parseCameraScan(msg);
      });

      return () => DeviceEventEmitter.removeAllListeners('onScanSuccess');
    }
  }, [
    sunmiSerialNo,
  ]);

  // useEffect(() => {
  //   console.log('iminSerialNo');
  //   console.log(iminSerialNo);

  //   if (iminSerialNo) {
  //     DeviceEventEmitter.addListener('onScanSuccess', (msg) => {
  //       console.log('result', msg);

  //       // Alert.alert('Scan result', msg);

  //       parseCameraScan(msg);
  //     });

  //     return () => DeviceEventEmitter.removeAllListeners('onScanSuccess');
  //   }
  // }, [
  //   iminSerialNo,
  // ]);

  ///////////////////////////////////////////////

  console.log('c-render - App.js')

  ///////////////////////////////////////////////

  return (
    <>
      <GestureHandlerRootView>
        {isTablet() || switchMerchant ? (
          <>
            {isTablet() ? (
              <>
                {__DEV__ ? (
                  <TabletScreenSwitcher
                    hideButton={true}
                    landscapeMode={true}
                    {...panResponder.panHandlers}
                  >
                    {/* <> */}
                    <StatusBar
                      barStyle="dark-content"
                      backgroundColor="#355F4A"
                    />

                    {renderChatbotModal()}

                    {printerCheckingModalVisibility && renderPrinterCheckingModal()}

                    {showPinUnlockModal ? <PinUnlockModal /> : <></>}

                    <PopupBanner popupMsg={currOutlet?.popupMsg} />

                    <TabletMainScreen />
                    {/* </> */}
                  </TabletScreenSwitcher>
                ) : (

                  <>
                    <StatusBar
                      barStyle="dark-content"
                      backgroundColor="#355F4A"
                    />

                    {renderChatbotModal()}

                    {printerCheckingModalVisibility && renderPrinterCheckingModal()}

                    {showPinUnlockModal ? <PinUnlockModal /> : <></>}

                    <PopupBanner popupMsg={currOutlet?.popupMsg} />

                    <TabletMainScreen />
                  </>
                )}
              </>
            ) : (
              <>
                {__DEV__ ? (
                  <>
                    <StatusBar
                      barStyle="dark-content"
                      backgroundColor="#355F4A"
                    />

                    {renderChatbotModal()}

                    {printerCheckingModalVisibility && renderPrinterCheckingModal()}

                    {showPinUnlockModal ? <PinUnlockModal /> : <></>}

                    <PopupBanner popupMsg={currOutlet?.popupMsg} />

                    <TabletMainScreen {...panResponder.panHandlers} />
                  </>
                ) : (
                  <>
                    <StatusBar
                      barStyle="dark-content"
                      backgroundColor="#355F4A"
                    />

                    {renderChatbotModal()}

                    {printerCheckingModalVisibility && renderPrinterCheckingModal()}

                    {showPinUnlockModal ? <PinUnlockModal /> : <></>}

                    <PopupBanner popupMsg={currOutlet?.popupMsg} />

                    <TabletMainScreen {...panResponder.panHandlers} />
                  </>
                )}
              </>
            )}
          </>
        ) : (
          <>
            <StatusBar barStyle="dark-content" backgroundColor="#355F4A" />

            {showPinUnlockModal ? <PinUnlockModal /> : <></>}

            <PopupBanner popupMsg={currOutlet?.popupMsg} />

            <TabletMainScreen {...panResponder.panHandlers} />
          </>
        )}
      </GestureHandlerRootView>

      {/* <CustomerDisplay /> */}
    </>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.4,
    width: Dimensions.get('screen').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '5%',
    position: 'absolute',
  },
});

export default App;