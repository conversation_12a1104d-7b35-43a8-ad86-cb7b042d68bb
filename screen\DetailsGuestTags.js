import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
    StyleSheet,
    Image,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    Button,
    Modal,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    TextInputBase,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Switch from 'react-native-switch-pro';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
    isTablet
} from '../util/common';
// import RNPopover from 'react-native-popover-menu';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
    DELAY_LONG_PRESS_TIME,
    MODE_ADD_CART,
    OFFLINE_BILL_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    OFFLINE_PAYMENT_METHOD_TYPE,
    ORDER_TYPE,
    USER_ORDER_PRIORITY,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
    getObjectDiff,
    isObjectEqual,
    listenToCurrOutletIdChangesWaiter,
    listenToSelectedOutletItemChanges,
    naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import GCalendar from '../assets/svg/GCalendar';
import FusionCharts from 'react-native-fusioncharts';
// import {CountryPicker} from "react-native-country-codes-picker/components/CountryPicker";

import {
    CHART_DATA,
    CHART_TYPE,
    FS_LIBRARY_PATH,
    CHART_Y_AXIS_DROPDOWN_LIST,
    CHART_FIELD_COMPARE_DROPDOWN_LIST,
    CHART_FIELD_NAME_DROPDOWN_LIST,
    CHART_FIELD_TYPE,
    CHART_FIELD_COMPARE_DICT,
    CHART_PERIOD,
} from '../constant/chart';
import {
    filterChartItems,
    getDataForChartDashboardTodaySales,
    getDataForSalesLineChart,
} from '../util/chart';
import RadioForm, { RadioButton, RadioButtonInput, RadioButtonLabel } from 'react-native-simple-radio-button';

const DetailsGuestTags = (props) => {
    const { navigation } = props
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [showReservations, setShowReservations] = useState(true);
    const [showFinished, setShowFinished] = useState(false);
    const [showWaitlist, setShowWaitlist] = useState(false);
    const [search, setSearch] = useState('');
    const [selectDietIndex, setSelectDietIndex] = useState(null);
    const [selectSpecialIndex, setSelectSpecialIndex] = useState(null);
    const [dietRestrictions, setDietRestrictions] = useState(false);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const [guestTagSpecialData, setGuestTagSpecialData] = useState([
        {
            id: 0,
            diet: 'No gluten',
            checked: false
        },
        {
            id: 1,
            diet: 'No Peanut',
            checked: false
        },
        {
            id: 2,
            diet: 'Vegan',
            checked: false
        },
        {
            id: 3,
            diet: 'Education',
            checked: false
        },
        {
            id: 4,
            diet: 'Halal',
            checked: false
        },
        {
            id: 5,
            diet: 'No Eggs',
            checked: false
        }]);

    const [guestTagDietData, setGuestTagDietData] = useState([
        {
            id: 0,
            diet: 'No gluten',
            checked: false
        },
        {
            id: 1,
            diet: 'No Peanut',
            checked: false
        },
        {
            id: 2,
            diet: 'Vegan',
            checked: false
        },
        {
            id: 3,
            diet: 'Education',
            checked: false
        },
        {
            id: 4,
            diet: 'Halal',
            checked: false
        },
        {
            id: 5,
            diet: 'No Eggs',
            checked: false
        }]);

    const dummyData = [
        {
            time: '3.00pm',
            name: 'Ah Boy',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'In-house'
        },
        {
            time: '3.00pm',
            name: 'Ah Girl',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Candice',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Girl',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Boy',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Zebra',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Hippo',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Lion',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'King',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
    ]

    const renderGuestTags = ({ item, index }) => {
        return (
            <View
                style={{
                    backgroundColor: 'blue',
                    borderRadius: 5,
                    marginBottom: 5,
                    width: Dimensions.get('screen').width * 0.075,
                    height: Dimensions.get('screen').height * 0.04,
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'center'
                }}>
                <Text style={[styles.table, { color: Colors.whiteColor }]}>
                    {`${item.tag}`}
                </Text>
            </View>
        )
    }

    // Function to let each checkbox in flalist to checked on its own
    const checkedSpecial = (id) => {
        var data = [...guestTagSpecialData]
        data[id].checked = data[id].checked === true ? data[id].checked = false : data[id].checked = true
        setGuestTagSpecialData(data)
    }

    // Function to let each checkbox in flalist to checked on its own
    const checkedDiet = (id) => {
        var data = [...guestTagDietData]
        data[id].checked = data[id].checked === true ? data[id].checked = false : data[id].checked = true
        setGuestTagDietData(data)
    }

    const renderGuestTagDietData = ({ item, index }) => {
        return (
            <View style={{
                height: 70,
                width: Dimensions.get('screen').width * 0.3,
                height: Dimensions.get('screen').height * 0.08,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderBottomLeftRadius: 5,
                borderBottomRightRadius: 5,
                paddingHorizontal: 15,
                backgroundColor: '#ffffff',
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
            }}>
                <View style={{ flex: 1 }}>
                    <Text>
                        {`${item.diet}`}
                    </Text>
                </View>
                <View style={{ flex: 1 }}>
                    <CheckBox
                        width={42}
                        style={{
                            marginRight: 10,
                            alignSelf: 'flex-end',
                            borderRadius: 15
                        }}
                        value={item.checked}
                        onValueChange={() => {
                            checkedDiet(index)
                        }}
                    />
                </View>
            </View>
        )
    }

    const renderGuestTagSpecialData = ({ item, index }) => {
        // const [special, setSpecial] = useState(false);

        return (
            <View style={{
                height: 70,
                width: Dimensions.get('screen').width * 0.3,
                height: Dimensions.get('screen').height * 0.08,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderBottomLeftRadius: 5,
                borderBottomRightRadius: 5,
                paddingHorizontal: 15,
                backgroundColor: '#ffffff',
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
            }}>
                <View style={{ flex: 1 }}>
                    <Text>
                        {`${item.diet}`}
                    </Text>
                </View>
                <View style={{ flex: 1 }}>
                    <CheckBox
                        width={42}
                        style={{
                            marginRight: 10,
                            alignSelf: 'flex-end',
                            borderRadius: 15
                        }}
                        value={item.checked}
                        onValueChange={() => {
                            checkedSpecial(index)
                        }}
                    />
                </View>
            </View>
        )
    }

    const renderDummyData = ({ item, index }) => {
        // When Reservations is clicked
        if (showReservations) {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                    {`${item.guestTags.tag}`}
                                </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
        // When Finished/CXL is clicked
        else if (showFinished) {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                        {`${item.guestTags.tag}`}
                                    </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
        // When Waitlist is clicked
        else {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                        {`${item.guestTags.tag}`}
                                    </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
    }

    // Navigation bar
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const userName = UserStore.useState((s) => s.name);

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Details
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });
    return (
        <View style={[
            styles.container,
            !isTablet()
                ? {
                    transform: [{ scaleX: 1 }, { scaleY: 1 }]
                }
                : {},
        ]}>

            {/* Sidebar */}
            {/* <View
                style={[
                    styles.sidebar,
                    !isTablet()
                        ? {
                            width: Dimensions.get('screen').width * 0.08,
                        }
                        : {},
                    switchMerchant
                        ? {
                            width: '10%'
                        }
                        : {},
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={1}
                    expandOperation={true}
                />
            </View> */}

            <View>
                {/* Top bar */}
                <View style={{ flexDirection: 'row', width: Dimensions.get('screen').width * 0.93 }}>
                    <View style={[
                        styles.topBar,
                        { flex: 0.28 },
                        switchMerchant
                            ? {}
                            : {
                                height: Dimensions.get('screen').height * 0.07,
                                width: Dimensions.get('screen').width * 0.263,
                            }
                    ]}>
                        <TouchableOpacity
                            style={[
                                styles.topBarButton,
                                showReservations ? { backgroundColor: Colors.whiteColor } : {},
                            ]}
                            onPress={() => {
                                setShowReservations(true)
                                setShowFinished(false)
                                setShowWaitlist(false)
                            }}
                        >
                            <Text
                                style={{
                                    textAlign: 'center',
                                }}>
                                Reservations
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[
                                styles.topBarButton,
                                showFinished ? { backgroundColor: Colors.whiteColor } : {},
                            ]}
                            onPress={() => {
                                setShowReservations(false)
                                setShowFinished(true)
                                setShowWaitlist(false)
                            }}
                        >
                            <Text
                                style={{
                                    textAlign: 'center',
                                }}>
                                Finished/CXL
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.topBarButton,
                                showWaitlist ? { backgroundColor: Colors.whiteColor } : {},
                            ]}
                            onPress={() => {
                                setShowReservations(false)
                                setShowFinished(false)
                                setShowWaitlist(true)
                            }}
                        >
                            <Text
                                style={{
                                    textAlign: 'center',
                                }}>
                                Waitlist
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style={[styles.rightTopBar, { flexDirection: 'row', flex: 0.7 }]}>
                        <View style={{
                            flex: 1,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <TouchableOpacity
                                onPress={() => { props.navigation.navigate('GuestDetailsScreen') }}
                                style={{
                                    flexDirection: 'row',
                                }}
                            >
                                <MaterialIcons
                                    name='arrow-back-ios'
                                    size={30}
                                    color='black'
                                />
                                <Text style={{ color: '#00FFFF', size: 30, paddingTop: 5 }}>
                                    Guest Details
                                </Text>
                            </TouchableOpacity>
                        </View>
                        <View style={{ flex: 1, justifyContent: 'center' }}>
                            <Text style={{ textAlign: 'center', color: 'gray' }}>
                                Guest Tags
                            </Text>
                        </View>
                        {/* Search Bar */}
                        <View
                            style={[styles.search, {
                                flex: 1,
                                marginRight: 5,
                                marginTop: 5,
                                marginBottom: 5
                            }]}>
                            <Icon
                                name="search"
                                size={13}
                                color={Colors.primaryColor}
                                style={{ marginLeft: 15 }}
                            />
                            <TextInput
                                style={[{
                                    fontSize: 13,
                                    fontFamily: 'NunitoSans-Regular',
                                    paddingLeft: 5,
                                    height: Dimensions.get('screen').height * 0.05,
                                },
                                switchMerchant ?
                                    { width: 220 }
                                    :
                                    { width: Dimensions.get('screen').width * 0.30 },
                                ]}
                                clearButtonMode="while-editing"
                                placeholder=" Search"
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                onChangeText={(text) => {
                                    // setSearch(text.trim());
                                    setSearch(text);
                                }}
                                value={search}
                            />
                        </View>
                    </View>
                </View>
                {/* Left Side */}
                {/* Show or hide between the Page */}
                {/* Show Reservation page */}
                <View style={{ flexDirection: 'row' }}>
                    <View>
                        <FlatList
                            style={styles.clickedFlatList}
                            nestedScrollEnabled={true}
                            showsVerticalScrollIndicator={false}
                            // data={dummyData}
                            renderItem={renderDummyData}
                            data={dummyData.filter((item) => {
                                if (search !== '') {
                                    return item.name
                                        .toLowerCase()
                                        .includes(search.toLowerCase());
                                } else {
                                    return true;
                                }
                            })}
                        // extraData={suppliers.filter((item) => {
                        //     if (search !== '') {
                        //     return item.name
                        //         .toLowerCase()
                        //         .includes(search.toLowerCase());
                        //     } else {
                        //     return true;
                        //     }
                        // })}
                        // renderItem={renderOrderItem}
                        // keyExtractor={(item, index) => String(index)}
                        />
                    </View>

                    {/* Right Side */}
                    <View>
                        <View style={{ flexDirection: 'row' }}>
                            <View>
                                <View style={[styles.flatListHeader,
                                {
                                    width: Dimensions.get('screen').width * 0.3,
                                    flexDirection: 'row',
                                    marginTop: 20,
                                    marginLeft: 20,
                                    backgroundColor: Colors.whiteColor,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }]}>
                                    <MaterialIcons
                                        name='star-outline'
                                        size={20}
                                    />
                                    <Text>
                                        Diet Restrictions
                                    </Text>
                                </View>
                                <View>
                                    <FlatList
                                        style={{
                                            height: Dimensions.get('screen').height * 0.8,
                                            marginLeft: 20,
                                            width: Dimensions.get('screen').width * 0.3,
                                            flexDirection: 'row',
                                            borderBottomLeftRadius: 5,
                                            borderBottomRightRadius: 5,
                                            backgroundColor: 'white',
                                        }}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTagDietData}
                                        data={guestTagDietData.filter((item) => {
                                            if (search !== '') {
                                                return item.diet
                                                    .toLowerCase()
                                                    .includes(search.toLowerCase());
                                            } else {
                                                return true;
                                            }
                                        })}
                                        extraData={guestTagDietData.filter((item) => {
                                            if (search !== '') {
                                                return item.diet
                                                    .toLowerCase()
                                                    .includes(search.toLowerCase());
                                            } else {
                                                return true;
                                            }
                                        })}
                                        keyExtractor={(item, index) => String(index)}
                                    />
                                </View>
                            </View>
                            <View>
                                <View style={[styles.flatListHeader,
                                {
                                    width: Dimensions.get('screen').width * 0.3,
                                    flexDirection: 'row',
                                    marginTop: 20,
                                    marginLeft: 20,
                                    backgroundColor: Colors.whiteColor,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }]}>
                                    <MaterialIcons
                                        name='restaurant'
                                        size={20}
                                    />
                                    <Text>
                                        Special
                                    </Text>
                                </View>
                                <View>
                                    <FlatList
                                        style={{
                                            height: Dimensions.get('screen').height * 0.8,
                                            marginLeft: 20,
                                            width: Dimensions.get('screen').width * 0.3,
                                            flexDirection: 'row',
                                            borderBottomLeftRadius: 5,
                                            borderBottomRightRadius: 5,
                                            backgroundColor: 'white',
                                        }}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTagSpecialData}
                                        data={guestTagSpecialData.filter((item) => {
                                            if (search !== '') {
                                                return item.diet
                                                    .toLowerCase()
                                                    .includes(search.toLowerCase());
                                            } else {
                                                return true;
                                            }
                                        })}
                                        extraData={guestTagSpecialData.filter((item) => {
                                            if (search !== '') {
                                                return item.diet
                                                    .toLowerCase()
                                                    .includes(search.toLowerCase());
                                            } else {
                                                return true;
                                            }
                                        })}
                                        keyExtractor={(item, index) => String(index)}
                                    />
                                </View>
                            </View>
                        </View>

                    </View>

                </View>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    rightTopBar: {
        backgroundColor: Colors.whiteColor,
        marginTop: 10
    },
    topBar: {
        flexDirection: 'row',
        height: Dimensions.get('screen').height * 0.11,
        width: Dimensions.get('screen').width * 0.785,
        backgroundColor: Colors.lightGrey,
        justifyContent: 'flex-start',
    },
    topBarButton: {
        padding: 5,
        backgroundColor: Colors.lightGrey,
        width: Dimensions.get('screen').width * 0.08,
        justifyContent: 'center',
    },
    search: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.30,
        backgroundColor: 'white',
        borderRadius: 7,
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1,
        borderColor: '#E5E5E5',
    },
    finished: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.06,
        height: Dimensions.get('screen').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: '#90ee90',
    },
    noShow: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.06,
        height: Dimensions.get('screen').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: 'black',
    },
    cancel: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.06,
        height: Dimensions.get('screen').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: '#808080',
    },
    export: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.06,
        height: Dimensions.get('screen').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 10,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    filter: {
        margin: 3,
        width: Dimensions.get('screen').width * 0.06,
        height: Dimensions.get('screen').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 10,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    flatListHeader: {
        height: 70,
        marginTop: 10,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.91,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        //marginTop: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        backgroundColor: '#D3D3D3',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    flatListBody: {
        height: 70,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.91,
        height: Dimensions.get('screen').height * 0.2,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        paddingHorizontal: 15,
        backgroundColor: '#ffffff',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    clickedFlatListHeader: {
        height: 70,
        marginTop: 10,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.25,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        //marginTop: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        backgroundColor: '#D3D3D3',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    table: {
        paddingTop: 5,
        textAlign: 'center',
        width: Dimensions.get('screen').width * 0.11,
        fontFamily: 'NunitoSans-Bold',
    },
    tableFirst: {
        paddingTop: 5,
        textAlign: 'left',
        width: Dimensions.get('screen').width * 0.11,
        fontFamily: 'NunitoSans-Bold',
    },
    flatList: {
        height: 100,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.91,
        flexDirection: 'row',
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    clickedFlatList: {
        height: Dimensions.get('screen').height * 0.8,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.25,
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    detailsContainer: {
        height: Dimensions.get('screen').width * 0.54,
        margin: 10,
        width: Dimensions.get('screen').width * 0.63,
        // flexDirection: 'row',
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderRadius: 10,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    detailsButton: {
        padding: 5,
        backgroundColor: Colors.lightGrey,
        width: Dimensions.get('screen').width * 0.35,
        height: 70,
        borderRadius: 10,
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    reservationProfileHistoryButton: {
        padding: 5,
        width: Dimensions.get('screen').width * 0.30,
        height: 55,
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    changeBackSeatButton: {
        borderRadius: 5,
        borderWidth: 1.5,
        borderColor: '#00FFFF',
        marginLeft: 20,
        justifyContent: 'center',
        alignItems: 'center',
        width: Dimensions.get('screen').width * 0.16,
        height: Dimensions.get('screen').height * 0.06,
    },
    guestButtons: {
        width: Dimensions.get('screen').width * 0.14,
        height: 70,
        margin: 10,
        // borderWidth: 0,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    reservationsContainer: {
        borderWidth: 0.3,
        borderTopLeftRadius: 2,
        borderTopRightRadius: 2,
        // margin: 10,
        marginRight: 10,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.30,
    },
    reservationsContainerBelow: {
        borderWidth: 0.3,
        borderBottomLeftRadius: 2,
        borderBottomRightRadius: 2,
        marginBottom: 10,
        marginRight: 10,
        marginLeft: 10,
        width: Dimensions.get('screen').width * 0.30,
        height: Dimensions.get('screen').height * 0.1,
    },
    editButton: {
        marginRight: 10,
        textAlign: 'right',
        color: '#00FFFF',
    },
    changeGuestButton: {
        borderWidth: 1.5,
        borderColor: '#00FFFF',
        alignItems: 'center',
        justifyContent: 'center',
        width: Dimensions.get('screen').width * 0.08,
        height: Dimensions.get('screen').height * 0.04,
        marginLeft: 130,
        borderRadius: 7,
        marginBottom: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('screen').height * 0.7,
        width: Dimensions.get('screen').width * 0.4,
        backgroundColor: Colors.whiteColor,
        //borderRadius: Dimensions.get('screen').width * 0.03,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.02,
        paddingHorizontal: Dimensions.get('screen').width * 0,
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.04,
        top: Dimensions.get('screen').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },
    informationTitle: {
        color: '#808080',
        marginLeft: 10,
        marginTop: 10
    },
    information: {
        marginLeft: 10,
        // borderBottomWidth: 1
    },
    submitButton: {
        backgroundColor: 'green',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10
    },
    reservationDetailsSecondRow: {
        flexDirection: 'column',
        // marginRight: 100
    },
    reservationDetailsFourthRow: {
        flexDirection: 'column',
        // marginRight: 100
    },
    secondRowTextHeader: {
        color: 'gray',
        textAlign: 'center'
    },
    secondRowTextBody: {
        textAlign: 'center',
    },
    fourthRowTextHeader: {
        color: 'gray',
        textAlign: 'center'
    },
    fourthRowTextBody: {
        textAlign: 'center',
    },
    tierDropdown: {
        marginTop: 5,
        marginLeft: 5,
        // alignItems: 'center',
        borderRadius: 10,
        // justifyContent: 'center',
        backgroundColor: Colors.whiteColor,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: 1,
    },
});

export default DetailsGuestTags;

