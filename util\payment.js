// import NfcManager, { NfcTech } from 'react-native-nfc-manager';
// import emv from 'node-emv';

export const readVisaCreditCard = async () => {
//     try {
//         // NfcManager.start();

//         NfcManager.cancelTechnologyRequest()
//     } catch (error) { }

//     try {
//         const commands = [
//             "00A404000E325041592E5359532E444446303100",
//             "00A4040007A00000000310100E",
//             "80A800002383212800000000000000000000000000000002500000000000097820052600E8DA935200"
//         ]

//         await NfcManager.requestTechnology([NfcTech.IsoDep])

//         const responses = []

//         for (let i = 0; i < commands.length; i++) {
//             const resp = await NfcManager.isoDepHandler.transceive(this.toByteArray(commands[i]))
//             responses.push(resp)
//         }

//         if (responses && responses.length > 2) {
//             const r = await this.getEmvInfo(this.toHexString(responses[2]))
//             if (r) {
//                 const cardInfo = this.getCardInfoVisa(r)
//                 if (cardInfo) {
//                     return {
//                         card: cardInfo.card,
//                         exp: cardInfo.exp
//                     }
//                 } else {
//                     return null
//                 }
//             } else {
//                 return null
//             }
//         } else {
//             return null
//         }
//     } catch (error) {
//         console.error(error);

//         return null
//     } finally {
//         NfcManager.cancelTechnologyRequest()
//     }
// };

export const readMasterCardCreditCard = async () => {
	// try {
    //     // NfcManager.start();

	// 	NfcManager.cancelTechnologyRequest()
    // } catch (error) { }
	
	// try {
	// 	const commands = [
	// 		"00A4040007A00000000410100E",
	// 		"80A8000002830000",
	// 		"00B2011400",
	// 		"00B2010C00",
	// 		"00B2012400",
	// 		"00B2022400"
	// 	]
		
	// 	await NfcManager.requestTechnology([NfcTech.IsoDep])
		
	// 	const responses = []
      
	// 	for (let i = 0; i < commands.length; i++) {
	// 		const resp = await NfcManager.isoDepHandler.transceive(this.toByteArray(commands[i]))
	// 		responses.push(resp)
	// 	}

	// 	if (responses && responses.length > 3) {
	// 		const r = await this.getEmvInfo(this.toHexString(responses[3]))
	// 		if (r) {
	// 			const cardInfo = this.getCardInfoMasterCard(r)
	// 			if (cardInfo) {
	// 				return {
	// 					card: cardInfo.card,
	// 					exp: cardInfo.exp
	// 				}
	// 			} else {
	// 				return null
	// 			}
	// 		} else {
	// 			return null
	// 		}
	// 	} else {
	// 		return null
	// 	}
    // } catch (error) {
    //     console.error(error);

	// 	return null
    // } finally {
	// 	NfcManager.cancelTechnologyRequest()
	// }
}

getEmvInfo = (info) => {
    // return new Promise((resolve) => {
	// 	emv.describe(info, (data) => {
	// 		if (data) {
	// 			resolve(data)
	// 		} else {
	// 			resolve(null)
	// 		}
	// 	})
    // })
}

toByteArray = (text) => {
	return text.match(/.{1,2}/g).map(b => {
		return parseInt(b, 16)
	})
}

toHexString = (byteArr) => {
    return byteArr.reduce((acc, byte) => {
		return (
			acc + ('00' + byte.toString(16).toUpperCase()).slice(-2)
		)
    }, '')
}

getCardInfoMasterCard = (responses) => {
    let res
    let end = false
    for (let i = 0; i < responses.length; i++) {
		const r = responses[i]
		if (r.tag === "70" && r.value && r.value.length > 0) {
			for (let j = 0; j < r.value.length; j++) {
				const e = r.value[j]
				if (e.tag === "5A" && e.value) {
					if (!res) {
						res = {
							card: e.value
						}
					} else {
						res.card = e.value
					}

					if (res.card && res.exp) {
						end = true
					}
				}

				if (e.tag === "5F24" && e.value) {
					if (!res) {
						res = {
							exp: e.value
						}
					} else {
						res.exp = e.value
					}

					if (res.card && res.exp) {
						end = true
					}
				}

				if (end) {
					break
				}
			}

			if (end) {
				break
			}
		}
    }
    return res
}

getEmvInfo = (info) => {
    // return new Promise((resolve) => {
    //     emv.describe(info, (data) => {
    //         if (data) {
    //             resolve(data)
    //         } else {
    //             resolve(null)
    //         }
    //     })
    // })
}

toByteArray = (text) => {
    return text.match(/.{1,2}/g).map(b => {
        return parseInt(b, 16)
    })
}

toHexString = (byteArr) => {
    return byteArr.reduce((acc, byte) => {
        return (
            acc + ('00' + byte.toString(16).toUpperCase()).slice(-2)
        )
    }, '')
}

getCardInfoVisa = (responses) => {
    let res
    let end = false
    for (let i = 0; i < responses.length; i++) {
        const r = responses[i]
        if (r.tag === "77" && r.value && r.value.length > 0) {
            for (let j = 0; j < r.value.length; j++) {
                const e = r.value[j]
                if (e.tag === "57" && e.value) {
                    const parts = e.value.split("D")
                    if (parts.length > 1) {
                        res = {
                            card: parts[0],
                            exp: parts[1].substring(0, 4)
                        }
                        end = true
                    }
                }

                if (end) {
                    break
                }
            }

            if (end) {
                break
            }
        }
    }
    return res
}

export const readGeneralCard = async () => {
	// try {
    //     const isNfcEnabled = await NfcManager.isEnabled();

    //     console.log('isNfcEnabled');
    //     console.log(isNfcEnabled);

    //     NfcManager.start();

	// 	NfcManager.cancelTechnologyRequest();
    // } catch (error) { }
	
	// try {		
	// 	await NfcManager.requestTechnology([NfcTech.NfcA]);

    //     let tagInfo = await NfcManager.getTag();
    //     tagInfo.ndefStatus = await NfcManager.ndefHandler.getNdefStatus();
		
    //     console.log('tagInfo');
	// 	console.log(tagInfo);

    //     return tagInfo;
    // } catch (error) {
    //     console.error(error);

	// 	return null;
    // } finally {
	// 	NfcManager.cancelTechnologyRequest();
	// }
}
