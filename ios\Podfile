# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, 15.5
prepare_react_native_project!

# pod 'CocoaAsyncSocket', :modular_headers => true

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'KooDooMerchant' do
  config = use_native_modules!

  use_frameworks! :linkage => :static

  # right after `use_frameworks! :linkage => :static`
  $RNFirebaseAsStaticFramework = true

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :hermes_enabled => true
  )

  pod 'rn-fetch-blob',
    :path => '../node_modules/rn-fetch-blob'

  pod 'RNCPushNotificationIOS', :path => '../node_modules/@react-native-community/push-notification-ios'
  
  # /////////////////////////////////////////////////////////////

  # Uncomment this line if you're not using auto-linking or if auto-linking causes trouble
  # pod 'WatermelonDB', path: '../node_modules/@nozbe/watermelondb'

  # WatermelonDB dependency, should not be needed on modern React Native
  # (please file an issue if this causes issues for you)
  # pod 'React-jsi', path: '../node_modules/react-native/ReactCommon/jsi', modular_headers: true

  # WatermelonDB dependency
  pod 'simdjson', path: '../node_modules/@nozbe/simdjson', modular_headers: true

  # /////////////////////////////////////////////////////////////

  target 'KooDooMerchantTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    # # Add this new block
    # installer.pods_project.targets.each do |target|
    #   target.build_configurations.each do |config|
    #     config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
    #     # Ensure proper framework search paths
    #     config.build_settings['FRAMEWORK_SEARCH_PATHS'] ||= ['$(inherited)']
    #     config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '${PODS_ROOT}/../node_modules/react-native-thermal-receipt-printer-image-qr/ios/Frameworks'
    #   end
    # end

    # Add these new configurations
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.5'
        
        # Ensure proper Swift module name
        if target.name == 'react-native-thermal-receipt-printer-image-qr'
          config.build_settings['SWIFT_OBJC_INTERFACE_HEADER_NAME'] = 'react_native_thermal_receipt_printer_image_qr-Swift.h'
          config.build_settings['SWIFT_OBJC_BRIDGING_HEADER'] = ''
        end
      end

      # if target.name == 'RNZipArchive'
      #   target.source_build_phase.files.each do |file|
      #     if file.settings && file.settings['COMPILER_FLAGS']
      #       file.settings['COMPILER_FLAGS'] = ''
      #     end
      #   end
      # end

      # target.source_build_phase.files.each do |file|
      #   file.settings = {} if file.settings.nil?
      #   file.settings['COMPILER_FLAGS'] = '' if file.settings.has_key?('COMPILER_FLAGS')
      # end

      # if target.name == 'BoringSSL-GRPC'
      #   target.source_build_phase.files.each do |file|
      #     if file.settings && file.settings['COMPILER_FLAGS']
      #       flags = file.settings['COMPILER_FLAGS'].split
      #       flags.reject! { |flag| flag = '-GCC_WARN_INHIBIT_ALL_WARNINGS' }
      #       file.settings['COMPILER_FLAGS'] = flags.join(' ')
      #     end
      #   end
      # end

      # if target.name == 'react-native-thermal-receipt-printer-image-qr'
      #   target.build_configurations.each do |config|
      #     config.build_settings['CLANG_ENABLE_MODULES'] = 'NO'
      #   end
      # end
    end
  end
end
