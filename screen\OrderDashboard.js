import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback, useRef, } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    TextInput,
    Modal,
    Platform,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView as ScrollViewGH, ScrollView, FlatList } from 'react-native-gesture-handler';
import Feather from 'react-native-vector-icons/Feather';
import Colors from '../constant/Colors';
import Close from 'react-native-vector-icons/AntDesign';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ImagePicker from 'react-native-image-picker';
import API from '../constant/API';
import { isEnabled } from 'react-native/Libraries/Performance/Systrace';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import Switch from 'react-native-switch-pro';
import Ionicon from 'react-native-vector-icons/Ionicons';
import {
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import AsyncImage from '../components/asyncImage';
import { CommonStore } from '../store/commonStore';
import AIcon from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import RNPickerSelect from 'react-native-picker-select';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { color } from 'react-native-reanimated';
import APILocal from '../util/apiLocalReplacers';
import MoMenuItemDetailsScreen from './MoMenuItemDetailsScreen';
import MoOutletMenuScreen from './MoOutletMenuScreen';
import OdOrderDetails from './OdOrderDetails';
import OdOrderList from './OdOrderList';
import MoCartScreen from './MoCartScreen';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

const OrderDashboard = React.memo((props) => {
    const { navigation, route } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [routeParamsOrderDetails, setRouteParamsOrderDetails] = useState({
        test: {},
        outletData: {},
        navFrom: '',
    });
    const [routeParamsOutletMenu, setRouteParamsOutletMenu] = useState({
        navFrom: '',
        test: {},
        outletDat: {},
        orderType: '',
    });
    const [routeParamsMenuItemDetails, setRouteParamsMenuItemDetails] = useState({
        refresh: () => { },
        menuItem: {},
        outletData: {},
        orderType: '',
    });

    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    ///////////////////////////////////////////////////////////////////

    const outletCategories = OutletStore.useState((s) => s.outletCategories);
    const outletItems = OutletStore.useState((s) => s.outletItems);

    //////////////////////////////////////////////////////////////

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const selectedProductEdit = CommonStore.useState((s) => s.selectedProductEdit,);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView,);

    //////////////////////////////////////////////////////////////

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const userId = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);

    const isOnMenu = CommonStore.useState((s) => s.isOnMenu);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const goToCart = () => {
        // console.log('Cart.getCartItem()');
        // console.log(Cart.getCartItem());

        if (Cart.getCartItem().length > 0) {
            props.navigation.navigate('MoCart', {
                test: test, outletData: outletData,
                menuItem: menuItem,
                clicked: clicked,
                clicked1: clicked1,
                clicked2: clicked2,
            })
        } else {
            Alert.alert("Info", "No items in your cart at the moment", [
                { text: "OK", onPress: () => { } }
            ],
                { cancelable: false })
        }
    }

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},

                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    {'Order Dashboard'}
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View style={[styles.container, !isTablet() ? { transform: [{ scaleX: 1 }, { scaleY: 1 }], } : {},]}>
                {/* <View style={[styles.sidebar, !isTablet() ? { width: windowWidth * 0.08, } : {},]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={2}
                        expandProduct={true}
                    />
                </View> */}
                <View style={styles.content}>

                    <View style={switchMerchant ? styles.list_PhoneScreen : styles.list}>
                        <View
                            style={[{
                                flexDirection: 'row',
                                height: windowHeight * 0.8,
                                // width: '100%',                                
                            }, switchMerchant ? {
                                // width: windowWidth * (1 - Styles.sideBarWidth) * 0.95,
                                height: windowHeight * 0.72,
                                // paddingRight: 10,
                            } : {}]}>

                            {/* left */}
                            <View
                                style={[{
                                    flex: 3,
                                    borderRightWidth: StyleSheet.hairlineWidth,
                                }, switchMerchant ? {
                                    borderRightWidth: 1,
                                } : {}]}>
                                <OdOrderList navigation={props.navigation} />

                            </View>
                            {/* right */}

                            <View style={{ flex: 3, alignItems: 'center', justifyContent: 'center' }}>
                                {/* <Text>Cart Part</Text> */}
                                <OdOrderDetails navigation={props.navigation} route={{
                                    ...props.route,
                                    params: routeParamsOrderDetails,
                                }} />
                            </View>
                        </View>
                    </View>

                </View>

            </View>
        </UserIdleWrapper>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'row',
        backgroundColor: Colors.highlightColor,
    },
    list: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.87,
        height: Dimensions.get('window').height * 0.8,
        alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    list_PhoneScreen: {
        backgroundColor: Colors.whiteColor,
        // width: Dimensions.get('window').width * 0.79,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth) * 0.945,
        height: Dimensions.get('window').height * 0.72,
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.44,
        shadowRadius: 10.32,

        elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default OrderDashboard;
