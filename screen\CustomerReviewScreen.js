import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal as ModalComponent,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from '@react-native-community/checkbox';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  logEventAnalytics,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import RNFetchBlob from 'rn-fetch-blob';
import DocumentPicker from 'react-native-document-picker';
import {
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
  autofitColumns,
  getImageFromFirebaseStorage,
  getPathForFirebaseStorageFromBlob,
  uploadFileToFirebaseStorage,
  generateEmailReport,
} from '../util/common';
import Feather from 'react-native-vector-icons/Feather';
import DropDownPicker from 'react-native-dropdown-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import { useKeyboard } from '../hooks';
import XLSX from 'xlsx';
import { zip, unzip, unzipAssets, subscribe } from 'react-native-zip-archive';
const RNFS = require('@dr.pogodin/react-native-fs');
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import Switch from 'react-native-switch-pro';
import {
  USER_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  USER_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  EMAIL_REPORT_TYPE,
  EXPAND_TAB_TYPE,
  CUSTOMER_RATING_WORDING,
} from '../constant/common';
import {
  ORDER_TYPE_PARSED,
  USER_POINTS_TRANSACTION_TYPE,
} from '../constant/common';
import Coins from '../assets/svg/Coins';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import GCalendar from '../assets/svg/GCalendar';

////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

///////////////////////////////////

///////////////////////////////////

const CustomerReviewScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////////////////////////////// UseState Here

  const [keyboardHeight] = useKeyboard();
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);

  const [list1, setList1] = useState(true);
  const [customerList, setCustomerList] = useState([]);

  const [perPage, setPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [page, setPage] = useState(0);

  const [search, setSearch] = useState('');

  const [expandViewDict, setExpandViewDict] = useState({});
  const [expandOrder, setExpandOrder] = useState(false);

  const [visible, setVisible] = useState(false);

  const [controller, setController] = useState({});

  const [oriList, setOriList] = useState([]);
  const [offset, setOffset] = useState(0);

  const [summaryAverageRating, setSummaryAverageRating] = useState(0);
  const [summaryTotalRating, setSummaryTotalRating] = useState(0);
  const [summaryTotalRatingNum, setSummaryTotalRatingNum] = useState(0);
  // const [summaryRating5Num, setSummaryRating5Num] = useState(0);
  // const [summaryRating4Num, setSummaryRating4Num] = useState(0);
  // const [summaryRating3Num, setSummaryRating3Num] = useState(0);
  // const [summaryRating2Num, setSummaryRating2Num] = useState(0);
  // const [summaryRating1Num, setSummaryRating1Num] = useState(0);
  const [summaryRatingList, setSummaryRatingList] = useState([]);

  /////////////////////////////////////////////

  const [headerSorting, setHeaderSorting] = useState([]);

  //////////////////////////////////////////////////////////////

  const [customerFirstVisitDate, setCustomerFirstVisitDate] = useState('');
  const [customerLastVisitDate, setCustomerLastVisitDate] = useState('');

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [expandTimeline, setExpandTimeline] = useState(false);

  const [isActiveMember, setIsActiveMember] = useState(true);

  const [usersExpandedDict, setUsersExpandedDict] = useState({});

  const [importModal, setImportModal] = useState(false);
  const [exportModal, setExportModal] = useState(false);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const outletReviews = OutletStore.useState((s) => s.outletReviews);

  // const selectedCustomerDineInOrders = OutletStore.useState(
  //   (s) => s.selectedCustomerDineInOrders,
  // );

  const outletReviewStartDate = CommonStore.useState((s) => s.outletReviewStartDate);
  const outletReviewEndDate = CommonStore.useState((s) => s.outletReviewEndDate);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  // const selectedCustomerOrders = OutletStore.useState(
  //   (s) => s.selectedCustomerOrders,
  // );
  // const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);

  const [phTotalPayment, setPhTotalPayment] = useState(0);

  const [exportEmail, setExportEmail] = useState('');
  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  //////////////////////////////////////////////////////////////

  const [overviewCustomersPast30Days, setOverviewCustomersPast30Days] =
    useState(0);
  const [totalCustomersSpending, setTotalCustomersSpending] = useState(0);

  const [
    averageSpendingPer30DaysEmailDict,
    setAverageSpendingPer30DaysEmailDict,
  ] = useState({});

  const allOutletsUserOrdersDone = OutletStore.useState(
    (s) => s.allOutletsUserOrdersDone,
  );
  const [pushPagingToTop, setPushPagingToTop] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////

  //////////////////////////////////// UseEffect here

  // useEffect(() => {
  //   var overviewCustomersPast30DaysTemp = 0;
  //   var overviewCustomersPast30DaysTempCount = 0;
  //   var totalCustomersSpendingTemp = 0;

  //   var averageSpendingPer30DaysEmailDictTemp = {};

  //   if (currOutletId && currOutletId.length > 0) {
  //     var currOutletUserOrdersDone = allOutletsUserOrdersDone.filter(
  //       (order) => order.outletId === currOutletId,
  //     );

  //     for (var i = 0; i < outletReviews.length; i++) {
  //       averageSpendingPer30DaysEmailDictTemp[outletReviews[i].email] = 0;

  //       var isCountedUser = false;

  //       for (var j = 0; j < currOutletUserOrdersDone.length; j++) {
  //         const finalPriceRoundedUp =
  //           Math.round(currOutletUserOrdersDone[j].finalPrice * 20) / 20;

  //         if (
  //           currOutletUserOrdersDone[j].crmUserId &&
  //           currOutletUserOrdersDone[j].crmUserId === outletReviews[i].uniqueId
  //         ) {
  //           totalCustomersSpendingTemp += finalPriceRoundedUp;

  //           if (
  //             moment(currOutletUserOrdersDone[j].completedDate).diff(
  //               moment(),
  //               'day',
  //             ) <= 30
  //           ) {
  //             if (!isCountedUser) {
  //               isCountedUser = true;
  //               overviewCustomersPast30DaysTempCount += 1;
  //             }
  //             averageSpendingPer30DaysEmailDictTemp[outletReviews[i].email] +=
  //               finalPriceRoundedUp;
  //           }

  //           continue;
  //         } else if (
  //           currOutletUserOrdersDone[j].userId === outletReviews[i].userId
  //         ) {
  //           totalCustomersSpendingTemp += finalPriceRoundedUp;

  //           if (
  //             moment(currOutletUserOrdersDone[j].completedDate).diff(
  //               moment(),
  //               'day',
  //             ) <= 30
  //           ) {
  //             if (!isCountedUser) {
  //               isCountedUser = true;
  //               overviewCustomersPast30DaysTempCount += 1;
  //             }
  //             averageSpendingPer30DaysEmailDictTemp[outletReviews[i].email] +=
  //               finalPriceRoundedUp;
  //           }

  //           continue;
  //         } else if (
  //           currOutletUserOrdersDone[j].userPhone === outletReviews[i].number
  //         ) {
  //           // check phone
  //           totalCustomersSpendingTemp += finalPriceRoundedUp;

  //           if (
  //             moment(currOutletUserOrdersDone[j].completedDate).diff(
  //               moment(),
  //               'day',
  //             ) <= 30
  //           ) {
  //             if (!isCountedUser) {
  //               isCountedUser = true;
  //               overviewCustomersPast30DaysTempCount += 1;
  //             }
  //             averageSpendingPer30DaysEmailDictTemp[outletReviews[i].email] +=
  //               finalPriceRoundedUp;
  //           }

  //           continue;
  //         }
  //       }
  //     }
  //   }

  //   overviewCustomersPast30DaysTemp = Object.entries(
  //     averageSpendingPer30DaysEmailDictTemp,
  //   )
  //     .map(([key, value]) => value)
  //     .filter((spent) => spent > 0).length;

  //   setAverageSpendingPer30DaysEmailDict(averageSpendingPer30DaysEmailDictTemp);

  //   setOverviewCustomersPast30Days(overviewCustomersPast30DaysTempCount);

  //   setTotalCustomersSpending(totalCustomersSpendingTemp);
  // }, [outletReviews, allOutletsUserOrdersDone, currOutletId]);

  useEffect(() => {
    var summaryRatingListDict = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    };

    // var summaryAverageRatingTemp = 0;
    var summaryTotalRatingTemp = 0;
    var summaryTotalRatingNumTemp = 0;

    for (let i = 0; i < outletReviews.length; i++) {
      if (summaryRatingListDict[outletReviews[i].rating] !== undefined) {
        summaryRatingListDict[outletReviews[i].rating]++;

        summaryTotalRatingTemp += outletReviews[i].rating;
        summaryTotalRatingNumTemp += 1;
      }
    }

    setSummaryAverageRating(summaryTotalRatingTemp / (summaryTotalRatingNumTemp > 0 ? summaryTotalRatingNumTemp : 1));
    setSummaryTotalRating(summaryTotalRatingTemp);
    setSummaryTotalRatingNum(summaryTotalRatingNumTemp);

    var summaryRatingListTemp = Object.entries(summaryRatingListDict).map(
      ([key, value]) => ({ key: parseInt(key), value }),
    );

    summaryRatingListTemp.sort(
      (a, b) => b.key - a.key,
    );

    setSummaryRatingList(summaryRatingListTemp);
  }, [outletReviews]);

  useEffect(() => {
    setHeaderSorting(outletReviews);
  }, [outletReviews]);

  useEffect(() => {
    setCurrentPage(1);
    setPageCount(Math.ceil(outletReviews.length / perPage));
  }, [outletReviews.length]);

  // useEffect(() => {
  //   var phTotalPaymentTemp = 0;

  //   for (var i = 0; i < selectedCustomerOrders.length; i++) {
  //     if (selectedCustomerOrders[i].completedDate) {
  //       phTotalPaymentTemp +=
  //         Math.round(selectedCustomerOrders[i].finalPrice * 20) / 20;
  //     }
  //   }

  //   setPhTotalPayment(phTotalPaymentTemp);
  // }, [selectedCustomerOrders]);

  ////////////////////////////////////

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < outletReviews.length; i++) {
      var excelRow = {
        Name: outletReviews[i].name ? outletReviews[i].name : 'N/A',
        // DOB: outletReviews[i].dob
        //   ? moment(outletReviews[i].dob).format('DD/MM/YYYY')
        //   : 'N/A',
        // Gender: outletReviews[i].gender ? outletReviews[i].gender : 'N/A',
        Email: outletReviews[i].email ? outletReviews[i].email : 'N/A',
        Phone: outletReviews[i].number ? outletReviews[i].number : 'N/A',
        'User ID': outletReviews[i].userIdHuman ? outletReviews[i].userIdHuman : 'N/A',

        'Review': outletReviews[i].review ? outletReviews[i].review : 'N/A',
        'Rating': outletReviews[i].rating ? `${outletReviews[i].rating} - ${CUSTOMER_RATING_WORDING[outletReviews[i].rating]}` : 'N/A',
        'Outlet Name': outletReviews[i].outletName ? outletReviews[i].outletName : 'N/A',
        'Date Time': outletReviews[i].createdAt ? moment(outletReviews[i].createdAt).format('YYYY MMM DD, HH:mm A') : 'N/A',
      };

      excelData.push(excelRow);
    }

    return excelData;
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    csvData.push(`Name,Email,Phone,User ID,Review,Rating,Outlet Name,Date Time`);

    for (var i = 0; i < outletReviews.length; i++) {

      var csvRow = `${outletReviews[i].name ? outletReviews[i].name : 'N/A'},${outletReviews[i].email ? outletReviews[i].email : 'N/A'},${outletReviews[i].number ? outletReviews[i].number : 'N/A'},${outletReviews[i].userIdHuman ? outletReviews[i].userIdHuman : 'N/A'},${outletReviews[i].review ? outletReviews[i].review : 'N/A'},${outletReviews[i].rating ? `${outletReviews[i].rating} - ${CUSTOMER_RATING_WORDING[outletReviews[i].rating]}` : 'N/A'},${outletReviews[i].outletName ? outletReviews[i].outletName : 'N/A'},${outletReviews[i].createdAt ? moment(outletReviews[i].createdAt).format('YYYY MMM DD HH:mm A') : 'N/A'}`;

      csvData.push(csvRow);
    }
    return csvData.join('\r\n');
  };

  const downloadPdf = () => { };

  const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-report-Product-Sales${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Product Sales Report',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Sent to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });
  };

  //////////////////////////////////// Page standardize pattern here

  const setState = () => { };

  const [sort, setSort] = useState('');

  //Start Here Sorting

  const sortCRMUsers = (dataList, userSortFieldType) => {
    var dataListTemp = [...dataList];

    const userSortFieldTypeValue =
      USER_SORT_FIELD_TYPE_VALUE[userSortFieldType];

    const userSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[userSortFieldType];

    //NAME
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '', 'en'
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '', 'en'
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //NUMBER
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //GENDER
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.GENDER_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.GENDER_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //DOB
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.DOB_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[userSortFieldTypeValue]).valueOf()
              ? moment(a[userSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[userSortFieldTypeValue]).valueOf()
              ? moment(b[userSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.DOB_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[userSortFieldTypeValue]).valueOf()
              ? moment(b[userSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[userSortFieldTypeValue]).valueOf()
              ? moment(a[userSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //RACE
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.RACE_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.RACE_DESC) {
        // dataListTemp.sort((a, b) => (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') - (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''));
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //TIER
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.TIER_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.TIER_DESC) {
        // dataListTemp.sort((a, b) => (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') - (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''));
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //STATUS
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.STATUS_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.STATUS_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    // REVIEW
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.REVIEW_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.REVIEW_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    // OUTLET NAME
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.OUTLET_NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.OUTLET_NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    return dataListTemp;
  };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_LOGO
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Reviews
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_PROFILE
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    var allOutletsStr = allOutlets
      .map(
        (item) =>
          `${item.name}:${Math.floor(Math.random() * (100 - 0 + 1)) + 0}`,
      )
      .join(';');

    // var taxName = currOutletTaxes[0].name;
    var taxName = 'SST';

    var excelColumn = {
      Name: 'Kylie Campbell',
      DOB: '1994-04-13',
      Email: '<EMAIL>',
      Gender: 'Female',
      Phone: '6018-2988415',
      'User ID': 'kylie.campbell',
      Address: '41 Jln Usj 4/9G Taman Seafield Jaya 47600 Petaling Jaya',
      'Outlets Points': allOutletsStr,
      'Image Label (png, jpg, jpeg)': 'Kylie_Campbell',
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      Name: 'Roy Cruz',
      DOB: '1989-08-21',
      Email: '<EMAIL>',
      Gender: 'Male',
      Phone: '6012-7138233',
      'User ID': 'roy.cruz',
      Address: '35 Menara Promet Jln Sultan Ismail 50250 Kuala Lumpur',
      'Outlets Points': allOutletsStr,
      'Image Label (png, jpg, jpeg)': 'Roy_Cruz',
    };
    excelTemplate.push(excelColumn2);

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;
  };

  const exportTemplate = async () => {
    try {
      const excelTemplate = convertTemplateToExcelFormat();

      const tempFolderName = `koodoo-crm-user-template-${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}`;
      const tempFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}`;

      const tempImageFolderName = 'images/Kylie_Campbell';
      const tempImageFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName}`;

      const tempImageFolderName2 = 'images/Roy_Cruz';
      const tempImageFolderPath2 = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName2}`;

      let exists = await RNFS.exists(tempFolderPath);
      if (exists) {
        // exists call delete
        await RNFS.unlink(tempFolderPath);
        // console.log("Previous Temp File Deleted", tempFolderPath);
      } else {
        // console.log("Previous Temp File Not Available", tempFolderPath)
      }

      RNFS.mkdir(tempFolderPath);
      RNFS.mkdir(tempImageFolderPath);
      RNFS.mkdir(tempImageFolderPath2);

      var templateImageUrl = '';

      // download merchant logo as example image file

      if (merchantLogo) {
        await new Promise((resolve, reject) => {
          if (
            merchantLogo.startsWith('http') ||
            merchantLogo.startsWith('file')
          ) {
            templateImageUrl = merchantLogo;

            resolve();
          } else {
            getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
              templateImageUrl = parsedUrl;

              resolve();
            });
          }
        });

        var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
        tempImageFileName = tempImageFileName.split('?')[0];

        const tempImageFilePath = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

        const downloadJob = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath,
        });

        await downloadJob.promise;

        const tempImageFilePath2 = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;

        const downloadJob2 = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath2,
        });

        await downloadJob2.promise;

        // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        var excelFile = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/koodoo-crm-user-template.xlsx`;
        var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
        var excelWorkBook = XLSX.utils.book_new();

        excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

        XLSX.utils.book_append_sheet(
          excelWorkBook,
          excelWorkSheet,
          'CRM User Template',
        );

        const workBookData = XLSX.write(excelWorkBook, {
          type: 'binary',
          bookType: 'xlsx',
        });

        RNFS.writeFile(excelFile, workBookData, 'ascii')
          .then(async (success) => {
            // console.log(`wrote file ${excelFile}`);

            // zip the folder

            const tempZipPath = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : RNFS.DownloadDirectoryPath
              }/${tempFolderName}.zip`;

            let exists = await RNFS.exists(tempZipPath);
            if (exists) {
              // exists call delete
              await RNFS.unlink(tempZipPath);
              // console.log("Previous Zip File Deleted");
            } else {
              // console.log("Previous Zip File Not Available")
            }

            zip(tempFolderPath, tempZipPath)
              .then(async (path) => {
                // console.log(`zip completed at ${path}`);

                let exists = await RNFS.exists(tempFolderPath);
                if (exists) {
                  // exists call delete
                  await RNFS.unlink(tempFolderPath);
                  // console.log("Clean Temp folder File Deleted");
                } else {
                  // console.log("Clean Temp folder File Not Available")
                }

                Alert.alert(
                  'Success',
                  `Sent to ${tempZipPath}`,
                  [{ text: 'OK', onPress: () => { } }],
                  { cancelable: false },
                );
              })
              .catch((error) => {
                console.error(error);

                Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
              });
          })
          .catch((err) => {
            // console.log(err.message);

            Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
          });
      }
      else {
        Alert.alert('Info', 'Please set the merchant logo first before proceed.');
      }
    } catch (ex) {
      console.error(ex);

      Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
    }
  };

  const importTemplateData = async () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    try {
      var res = null;
      if (Platform.OS === 'ios') {
        res = await DocumentPicker.pick({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }
      else {
        res = await DocumentPicker.pickSingle({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }

      // console.log('res');
      // console.log(res);

      // RNFetchBlob.fs.readFile(res.uri, 'base64').then(async data => {
      //   // upload to firebase storage

      //   var referenceId = uuidv4();
      //   var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      //   await uploadFileToFirebaseStorage(data, referencePath);
      // });

      var referenceId = uuidv4();
      var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      var translatedPath = '';
      if (Platform.OS === 'ios') {
        translatedPath = await getPathForFirebaseStorageFromBlob(res[0]);
      }
      else {
        translatedPath = await getPathForFirebaseStorageFromBlob(res);
      }

      // console.log('translatedPath');
      // console.log(translatedPath);

      if (Platform.OS === 'ios') {
        if (translatedPath && translatedPath.fileCopyUri) {
          translatedPath = translatedPath.fileCopyUri;
        }
      }

      await uploadFileToFirebaseStorage(translatedPath, referencePath);

      const body = {
        zipId: referenceId,
        zipPath: referencePath,

        userId: firebaseUid,
        merchantId,
        merchantName,
        outletId: currOutletId,
      };

      ApiClient.POST(API.batchCreateCRMUsers, body)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Submitted to upload queue, we will notify you once the process is done',
            );
          } else {
            Alert.alert('Error', 'Failed to import data');
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        })
        .catch((err) => {
          // console.log(err);

          Alert.alert('Error', 'Failed to import data');

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    } catch (err) {
      CommonStore.update((s) => {
        s.isLoading = false;
      });

      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        console.error(err);

        Alert.alert('Error', 'Failed to import data');
      }
    }
  };

  const actionUsersExpandedDict = (item) => {
    // for now set to one open only

    if (usersExpandedDict[item.email]) {
      // setUsersExpandedDict({
      //   ...usersExpandedDict,
      //   [item.email]: false,
      // });
      setUsersExpandedDict({
        [item.email]: false,
      });
    } else {
      // setUsersExpandedDict({
      //   ...usersExpandedDict,
      //   [item.email]: true,
      // });
      setUsersExpandedDict({
        [item.email]: true,
      });
    }
  };

  const expandOrderFunc = (param) => {
    if (expandOrder == false) {
      setExpandOrder(true);
      setExpandViewDict({
        ...expandViewDict,
        //[param.uniqueId]: true,
      });
      expandViewDict;
    } else {
      setExpandOrder(false);
      setExpandViewDict({
        ...expandViewDict,
        //[param.uniqueId]: undefined,
      });
    }
  };
  // const next = (e) => {
  //   const offset = e * perPage;
  //   setState({ offset: offset })
  //   loadMoreData()
  // }

  // const less = async () => {
  //   if (page > 0) {
  //     await setState({ page: page - 1, currentPage: currentPage - 1 })
  //     // console.log(page)
  //     var y = page
  //     pre(y)
  //   }
  // }

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  //   const loadMoreData = () => {
  //     const data = oriList;
  //     const slice = data.slice(offset, offset + perPage)
  //     setState({ list: slice, pageCount: Math.ceil(data.length / perPage) })
  // }

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 });
      // console.log(page);
      var e = page;
      next(e);
    }
  };

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          backgroundColor: '#FFFFFF',
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // width: '100%',
        }}>
        <TouchableOpacity
          onPress={() => {
            // CommonStore.update(
            //   (s) => {
            //     s.selectedCustomerEdit = item;

            //     s.timestamp = Date.now();
            //   },
            //   () => {
            //     navigation.navigate('NewCustomer');

            //     CommonStore.update((s) => {
            //       s.selectedCustomerEdit = item;
            //     });
            //   },
            // );

            // CommonStore.update(s => {
            //   s.selectedCustomerEdit = item;
            // });

            // navigation.navigate('NewCustomer');
          }}>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              marginBottom: 10,
              alignItems: 'center',
              borderBottomWidth:
                expandViewDict == true ? StyleSheet.hairlineWidth : null,
            }}>
            <View
              style={{
                flexDirection: 'row',
                marginLeft: 0,
                width: '7%',
                alignItems: 'center',
              }}>
              {item.avatar ? (
                <AsyncImage
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={{
                    uri: item.avatar,
                  }}
                  item={item}
                  hideLoading
                />
              ) : (
                <Image
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                  hideLoading
                />
              )}
              {/* <View style={{ flexDirection: 'column', marginLeft: 5 }}>
              <Text style={{ flex: 1, fontSize: 13, fontWeight: '500', textAlign: 'left', fontFamily: 'NunitoSans-Bold' }}>{item.name}</Text>
              <Text style={{ flex: 1, fontSize: 12, fontWeight: '500', textAlign: 'left', color: Colors.fieldtTxtColor }}>{item.uniqueName}</Text>
            </View> */}
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '12%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 5 }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    // fontWeight: '500',
                    textAlign: 'left',
                    fontFamily: 'NunitoSans-Bold',
                    marginBottom: 5,
                  }}>
                  {item.name}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 9 : 12,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'left',
                    color: Colors.fieldtTxtColor,
                  }}
                  numberOfLines={1}>
                  {/* {item.uniqueName} */}
                  {item.userIdHuman ? item.userIdHuman : '-'}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '15%' }}>
              <View style={{ flexDirection: 'column' }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'left',
                    marginBottom: 5,
                  }}>
                  {item.number}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'left',
                  }}
                  numberOfLines={1}>
                  {item.email}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '20%' }}>
              <View style={{ flexDirection: 'column' }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'center',
                    // marginLeft: 15,
                  }}>
                  {item.review ? item.review : 'N/A'}
                </Text>
                {/* <Text style={{ flex: 1, fontSize: 13, fontWeight: '500', textAlign: 'center' }}></Text> */}
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '15%' }}>
              <View style={{
                flexDirection: 'column',
                marginLeft: 0,

                justifyContent: 'flex-start',
                alignItems: 'flex-start',
              }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'center',

                    marginBottom: 5,
                    paddingLeft: 5,
                  }}>
                  {`${item.rating} - ${CUSTOMER_RATING_WORDING[item.rating]}`}
                </Text>

                <View style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-around",
                  width: "80%",
                  // backgroundColor: 'green',
                }}>
                  {
                    Array.from(Array(5)).map((_, starIndex) => {
                      return (
                        <View style={{
                          // marginHorizontal: 1,
                        }}>
                          <FontAwesome name={item.rating > starIndex ? "star" : "star-o"} color={Colors.primaryColor} size={20} />
                        </View>
                      );
                    })
                  }
                </View>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '15%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {item.outletName ? item.outletName : 'N/A'}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', marginLeft: 0, width: '15%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    // fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {moment(item.createdAt).format('YYYY MMM DD, HH:mm A')}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {/* {usersExpandedDict[item.email] && (
          <>
            {isLoading ? (
              <View
                style={{
                  width: '100%',
                  height: 100,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <ActivityIndicator
                  style={{}}
                  color={Colors.primaryColor}
                  size={'large'}
                />
              </View>
            ) : (
              <>
                {selectedCustomerDineInOrders.length > 0 ? (
                  // <View style={{ borderTopWidth: 1, borderColor: '#E5E5E5', }}>
                  <ScrollView
                  // showsVerticalScrollIndicator={false}
                  // style={{ height: 350, borderWidth: 1, padding: 10, margin: 10, marginHorizontal: 10, borderRadius: 5, borderColor: '#E5E5E5' }}
                  >
                    <FlatList
                      showsVerticalScrollIndicator={false}
                      data={selectedCustomerDineInOrders}
                      renderItem={renderTimelineItem}
                      keyExtractor={(item, index) => String(index)}
                      style={{ marginTop: 10, paddingTop: 10 }}
                    //initialNumToRender={4}
                    />
                  </ScrollView>
                ) : (
                  // </View>
                  <View
                    style={{
                      width: '100%',
                      height: 100,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        top: -20,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      No timeline for now.
                    </Text>
                  </View>
                )}
              </>
            )}
          </>
        )} */}
      </View>
    );
  };

  const renderSummaryItem = ({ item, index }) => {
    return (
      <View
        style={{
          //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          backgroundColor: '#FFFFFF',
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          // borderColor: '#BDBDBD',
          // borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // width: '100%',

          // backgroundColor: 'red',

          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View style={{
          width: '15%',
        }}>
          <Text
            style={{
              flex: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              // fontWeight: '500',
              textAlign: 'left',

              marginBottom: 5,
              paddingLeft: 12,
            }}>
            {`${item.key} - ${CUSTOMER_RATING_WORDING[item.key]}`}
          </Text>
        </View>

        <View style={{
          width: '70%',
        }}>
          <View style={{
            backgroundColor: Colors.tabGrey,

            width: '100%',
            height: 10,

            borderTopLeftRadius: 10,
            borderTopRightRadius: 10,
            borderBottomLeftRadius: 10,
            borderBottomRightRadius: 10,

            position: 'relative',
          }}>
            <View style={{
              backgroundColor: Colors.secondaryColor,

              width: `${(item.value / summaryTotalRatingNum * 100).toFixed(2)}%`,
              height: 10,

              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              borderBottomLeftRadius: 10,
              borderBottomRightRadius: 10,

              position: 'absolute',
              top: 0,
              left: 0,
            }} />
          </View>
        </View>

        <View style={{
          width: '15%',

          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',

          // backgroundColor: 'green',

          paddingRight: 25,
        }}>
          <Text
            style={{
              // flex: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              // fontWeight: '500',
              textAlign: 'center',

              marginBottom: 5,
              paddingLeft: 6,
            }}>
            {`${item.value}`}
          </Text>

          <Text
            style={{
              // flex: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              // fontWeight: '500',
              textAlign: 'center',

              color: Colors.tabGrey,

              marginBottom: 5,
              paddingLeft: 6,
            }}>
            {`post${item.value > 1 ? 's' : ''}`}
          </Text>
        </View>
      </View>
    );
  };

  // const renderTimelineItem = ({ item, index }) => {
  //   var detailsStr = 'N/A';
  //   var detailsList = [];

  //   // console.log(item);
  //   // console.log(item.finalPrice);

  //   const currOrder = selectedCustomerOrders.find(
  //     (order) => order.uniqueId === item.orderId,
  //   );

  //   if (item.transactionType === USER_POINTS_TRANSACTION_TYPE.EARN) {
  //     if (currOrder && currOrder.cartItems) {
  //       for (var i = 0; i < currOrder.cartItems.length; i++) {
  //         detailsList.push(currOrder.cartItems[i].name);
  //       }
  //     }
  //   } else if (item.transactionType === USER_POINTS_TRANSACTION_TYPE.REDEEM) {
  //     if (currOrder && currOrder.pointsToRedeemPackageIdList) {
  //       for (var i = 0; i < currOrder.pointsToRedeemPackageIdList.length; i++) {
  //         const currPackage =
  //           pointsRedeemPackagesDict[currOrder.pointsToRedeemPackageIdList[i]];

  //         if (currPackage) {
  //           detailsList.push(currPackage.packageName);
  //         }
  //       }
  //     }
  //   }

  //   if (detailsList.length > 0) {
  //     detailsStr = detailsList.join(', ');
  //   }

  //   return (
  //     <ScrollView showsVerticalScrollIndicator={false}>
  //       <View
  //         style={{
  //           width: '100%',
  //           alignItems: 'center',
  //           justifyContent: 'center',
  //         }}>
  //         <View
  //           style={{
  //             flexDirection: 'row',
  //             width: '66%',
  //             marginTop: 0,
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //           }}>
  //           <View style={{ flexDirection: 'column' }}>
  //             {/* <Ionicon name='ellipse' size={26} color='#4E9F7D' style={{ alignSelf: 'center', marginTop: -5 }} /> */}
  //             <View
  //               style={{
  //                 alignSelf: 'center',
  //                 borderWidth: 0,
  //                 borderColor: '#E5F1EC',
  //                 height: switchMerchant ? 140 : 160,
  //                 width: 1,
  //                 marginTop: -10,
  //                 zIndex: -1,
  //               }}
  //             />
  //           </View>
  //           <View
  //             style={{
  //               borderWidth: 1,
  //               borderRadius: 6,
  //               borderColor: '#E5E5E5',
  //               height: switchMerchant ? 120 : 160,
  //               marginLeft: 10,
  //               padding: 10,
  //               width: switchMerchant ? 400 : 500,
  //             }}>
  //             <View style={{ marginLeft: 10, marginVertical: 5 }}>
  //               {/* <Text style={{ fontSize: 16, fontFamily: 'Nunitosans-Bold' }}> */}
  //               <View
  //                 style={{
  //                   flexDirection: 'row',
  //                   justifyContent: 'space-between',
  //                 }}>
  //                 <Text
  //                   style={{
  //                     fontWeight: '600',
  //                     fontSize: switchMerchant ? 10 : 16,
  //                     color: Colors.primaryColor,
  //                   }}>
  //                   {moment(item.orderDate).format('DD MMM YYYY hh:mm A')}
  //                 </Text>
  //                 <Text
  //                   style={{
  //                     fontWeight: '600',
  //                     fontSize: switchMerchant ? 10 : 16,
  //                     color: Colors.primaryColor,
  //                   }}>
  //                   Table Code:{' '}
  //                   {/* {item.tableCode
  //                     ? item.tableCode
  //                     : outletTablesDict[item.tableId]
  //                       ? outletTablesDict[item.tableId].code
  //                       : 'N/A'} */}
  //                 </Text>
  //               </View>
  //             </View>

  //             <View style={{ flexDirection: 'column', marginLeft: 10 }}>
  //               <View
  //                 style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
  //                 <Text
  //                   style={
  //                     switchMerchant
  //                       ? { fontSize: 10, fontWeight: '600', width: '20%' }
  //                       : { fontWeight: '600', width: '20%' }
  //                   }>
  //                   Order ID:{' '}
  //                 </Text>
  //                 <TouchableOpacity
  //                   style={{
  //                     width: '80%',
  //                   }}
  //                   onPress={() => {
  //                     navigation.navigate('History');

  //                   }}>
  //                   <Text
  //                     style={{
  //                       fontSize: switchMerchant ? 10 : 16,
  //                       fontFamily: 'NunitoSans-Regular',
  //                       fontWeight: '700',
  //                     }}>
  //                     #{item.orderId}
  //                   </Text>
  //                 </TouchableOpacity>
  //               </View>

  //               <View
  //                 style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
  //                 <Text
  //                   style={{
  //                     fontSize: switchMerchant ? 10 : 16,
  //                     fontFamily: 'NunitoSans-Regular',
  //                     fontWeight: '500',
  //                     width: '20%',
  //                   }}>
  //                   {/* Remarks:: */}
  //                   Item Ordered:
  //                 </Text>
  //                 {item.cartItems && item.cartItems.length > 0 ? (
  //                   <View style={{ flexDirection: 'row', width: '100%' }}>
  //                     <Text
  //                       style={{
  //                         fontSize: switchMerchant ? 10 : 16,
  //                         fontFamily: 'NunitoSans-Regular',
  //                         fontWeight: '700',
  //                         width: '80%',
  //                       }}
  //                       numberOfLines={3}>
  //                       {item.cartItems
  //                         .map((cartItem) => cartItem.itemName)
  //                         .join(', ')}
  //                     </Text>
  //                   </View>
  //                 ) : (
  //                   <></>
  //                 )}
  //               </View>

  //               {item.cartItemsCancelled &&
  //                 item.cartItemsCancelled.length > 0 ? (
  //                 <View
  //                   style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
  //                   <Text
  //                     style={{
  //                       fontSize: switchMerchant ? 10 : 16,
  //                       fontFamily: 'NunitoSans-Regular',
  //                       fontWeight: '500',
  //                       width: '20%',
  //                     }}>
  //                     {/* Remarks: */}
  //                     Item Cancelled:
  //                   </Text>
  //                   <View style={{ flexDirection: 'row', width: '100%' }}>
  //                     <Text
  //                       style={{
  //                         fontSize: switchMerchant ? 10 : 16,
  //                         fontFamily: 'NunitoSans-Regular',
  //                         fontWeight: '700',
  //                         width: '80%',
  //                       }}>
  //                       {item.cartItemsCancelled
  //                         .map((cartItem) => cartItem.itemName)
  //                         .join(', ')}
  //                     </Text>
  //                   </View>
  //                 </View>
  //               ) : (
  //                 <></>
  //               )}

  //               <View
  //                 style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
  //                 <Text
  //                   style={{
  //                     fontSize: switchMerchant ? 10 : 16,
  //                     fontFamily: 'NunitoSans-Regular',
  //                     fontWeight: '500',
  //                     width: '20%',
  //                   }}>
  //                   Total Price:
  //                 </Text>
  //                 <Text
  //                   style={{
  //                     fontSize: switchMerchant ? 10 : 16,
  //                     fontFamily: 'NunitoSans-Regular',
  //                     fontWeight: '700',
  //                     width: '80%',
  //                   }}>
  //                   {/* {item.remarks ? item.remarks : 'N/A'} */}
  //                   RM{' '}
  //                   {item.finalPrice
  //                     .toFixed(2)
  //                     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
  //                 </Text>
  //               </View>
  //             </View>
  //           </View>
  //         </View>
  //       </View>
  //     </ScrollView>
  //   );
  // };

  /////Manage Filter

  ///////////////////////////////////////////////////////

  return (
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={6}
            expandCRM
          />
        </View> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{}}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
          }}>
          <ScrollView horizontal scrollEnabled={switchMerchant}>
            <DateTimePickerModal
              isVisible={showDateTimePicker}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date(moment(text).startOf('day'));
                CommonStore.update(s => {
                  s.outletReviewStartDate = moment(text).startOf('day');
                });
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                setShowDateTimePicker(false);
              }}
              maximumDate={moment(outletReviewEndDate).toDate()}
              date={moment(outletReviewStartDate).toDate()}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date1(moment(text).endOf('day'));
                CommonStore.update(s => {
                  s.outletReviewEndDate = moment(text).endOf('day')
                });
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
              minimumDate={moment(outletReviewStartDate).toDate()}
              date={moment(outletReviewEndDate).toDate()}
            />

            <ModalView
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible}
              transparent
              animationType="slide">
              <KeyboardAvoidingView
                behavior="padding"
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: windowHeight,
                }}>
                <View
                  style={[
                    styles.ManageFilterBox,
                    {
                      borderRadius: 12, padding: 30, paddingHorizontal: 50,
                      ...getTransformForModalInsideNavigation(),
                    },
                  ]}>
                  <View
                    style={{
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      marginTop: -15,
                      marginRight: -35,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <AntDesign
                        name={'closecircle'}
                        size={25}
                        color={'#858C94'}
                      />
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      //marginTop: 10
                    }}>
                    <Text style={{ fontSize: 26, fontFamily: 'NunitoSans-Bold' }}>
                      Manage Filter
                    </Text>
                  </View>
                  <View
                    style={{
                      borderColor: '#E5E5E5',
                      borderWidth: 1,
                      marginHorizontal: -20,
                      marginBottom: 15,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      //justifyContent: 'center',
                      alignItems: 'center',
                      //paddingLeft: 30,
                      //borderRadius: 10,
                      height: 35,
                      marginVertical: 10,
                    }}>
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 180,
                        height: 35,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Product Category'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    />
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 90,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      dropDownStyle={{
                        width: 90,
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Is'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    //onOpen={() => controller1.close()}
                    />
                  </View>
                  <View
                    style={{
                      //marginRight: '33%',
                      //flexDirection: 'row',
                      //justifyContent: 'center',
                      //alignItems: 'center',
                      //paddingLeft: 30,
                      //borderRadius: 10,
                      marginTop: 20,
                      height: 40,
                      zIndex: -1,
                    }}>
                    <TextInput
                      style={{
                        borderRadius: 5,
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        height: 35,
                        width: 200,
                        backgroundColor: Colors.fieldtBgColor,
                        paddingLeft: 5,
                      }}
                      placeholder="Enter Condition"
                      placeholderStyle={{ paddingLeft: 5 }}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={() => { }}
                    />
                  </View>
                  {/* <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#F2F3F7',
                width: 100,
                height: 40,
                borderRadius: 6,
                marginTop: 15,
              }}>
                <Text style={{
                  fontSize: 12.5,
                  Color: '#B6B6B6',
                }}>
                  Package A
                </Text>
                <TouchableOpacity
                  onPress={() => {

                  }}
                  style={{
                    marginLeft: 5
                  }}
                >
                  <AntDesign name={"close"} size={16} color={'#B6B6B6'} />
                </TouchableOpacity>
              </View> */}

                  <View
                    style={{
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      marginVertical: 20,
                      marginHorizontal: -20,
                      zIndex: -1,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      zIndex: -1,
                    }}>
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#4E9F7D',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#4E9F7D',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#4E9F7D',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#4E9F7D',
                        marginLeft: 10,
                      }}
                      onPress={() => { }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#FFFFFF',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Apply
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </ModalView>

            <ModalView
              style={
                {
                  // flex: 1
                }
              }
              visible={importModal}
              supportedOrientations={['portrait', 'landscape']}
              transparent
              animationType={'slide'}>
              <View style={styles.modalContainer}>
                <View style={[styles.modalView1, {
                  height: Dimensions.get('window').width * 0.3,
                  width: Dimensions.get('window').width * 0.4,
                  padding: Dimensions.get('window').width * 0.03,

                  ...getTransformForModalInsideNavigation(),
                }]}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={[styles.closeButton, {
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,
                    },]}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={styles.modalTitle}>
                    <Text
                      style={
                        switchMerchant
                          ? styles.modalTitleText1
                          : styles.modalTitleText
                      }>
                      Download Report
                    </Text>
                  </View>
                  <View
                    style={{
                      alignItems: 'center',
                      //top: '15%',
                    }}>
                    <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Email Address:
                      </Text>
                      <TextInput
                        placeholder="Enter your email"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 240 : 370,
                          height: switchMerchant ? 35 : 50,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 16,
                        }}
                        autoCapitalize='none'
                        onChangeText={(text) => {
                          setExportEmail(text);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL
                          })
                        }}
                        value={exportEmail}
                      />
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 15,
                        }}>
                        Send As:
                      </Text>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          marginTop: 10,
                        }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });

                              setIsLoadingExcel(true);

                              const excelData = convertDataToExcelFormat();

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.EXCEL,
                                excelData,
                                'KooDoo Reviews Report',
                                'KooDoo Reviews Report.xlsx',
                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                exportEmail,
                                'KooDoo Reviews Report',
                                'KooDoo Reviews Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });

                                  setIsLoadingExcel(false);

                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setImportModal(false);
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL
                            })
                          }}>
                          {isLoading && isLoadingExcel ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginLeft: 15,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });

                              setIsLoadingCsv(true);

                              // const csvData = convertArrayToCSV(todaySalesChart.dataSource.data);
                              const csvData = convertDataToCSVFormat();

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.CSV,
                                csvData,
                                'KooDoo Reviews Report',
                                'KooDoo Reviews Report.csv',
                                `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                exportEmail,
                                'KooDoo Reviews Report',
                                'KooDoo Reviews Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });

                                  setIsLoadingCsv(false);

                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setImportModal(false);
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV
                            })
                          }}>
                          {isLoading && isLoadingCsv ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CSV
                            </Text>
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <KeyboardAvoidingView
              style={{
                top:
                  Platform.OS === 'ios' && keyboardHeight > 0
                    ? -keyboardHeight * 1
                    : 0,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  width: windowWidth * 0.87,
                  margin: 10,
                  padding: 10,
                  paddingRight: 0,
                  alignItems: 'center',
                  alignSelf: 'center',
                  //paddingRight: windowWidth * 0.015,
                }}>
                <View style={{ marginRight: 15, flexDirection: 'row' }}>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      //width: 160,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                    onPress={() => {
                      setImportModal(true);
                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN
                      })
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon
                        name="download"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        DOWNLOAD
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <View
                    style={{
                      flexDirection: 'row',
                      marginLeft: switchMerchant ? 10 : 20,
                    }}>
                    <View
                      style={{
                        paddingHorizontal: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderRadius: 10,
                        paddingVertical: 10,
                        justifyContent: 'center',
                        backgroundColor: Colors.whiteColor,
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}>
                      <View
                        style={{ alignSelf: 'center', marginRight: 5 }}
                        onPress={() => {
                          setState({
                            pickerMode: 'date',
                            showDateTimePicker: true,
                          });
                        }}>
                        {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                        <GCalendar
                          width={switchMerchant ? 10 : 20}
                          height={switchMerchant ? 10 : 20}
                        />
                      </View>

                      <TouchableOpacity
                        onPress={() => {
                          setShowDateTimePicker(true);
                          setShowDateTimePicker1(false);
                        }}
                        style={{
                          marginHorizontal: 4,
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontSize: 10,
                                fontFamily: 'NunitoSans-Regular',
                              }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          {moment(outletReviewStartDate).format('DD MMM yyyy')}
                        </Text>
                      </TouchableOpacity>

                      <Text
                        style={
                          switchMerchant
                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                            : { fontFamily: 'NunitoSans-Regular' }
                        }>
                        -
                      </Text>

                      <TouchableOpacity
                        onPress={() => {
                          setShowDateTimePicker(false);
                          setShowDateTimePicker1(true);
                        }}
                        style={{
                          marginHorizontal: 4,
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? {
                                fontSize: 10,
                                fontFamily: 'NunitoSans-Regular',
                              }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          {moment(outletReviewEndDate).format('DD MMM yyyy')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <ModalView
                    style={
                      {
                        // flex: 1
                      }
                    }
                    visible={exportModal}
                    supportedOrientations={['portrait', 'landscape']}
                    transparent
                    animationType={'slide'}>
                    <View style={styles.modalContainer}>
                      <View style={[styles.modalViewUploadOptions, {
                        height: Dimensions.get('window').width * 0.2,
                        width: Dimensions.get('window').width * 0.3,
                        padding: Dimensions.get('window').width * 0.03,

                        ...getTransformForModalInsideNavigation(),
                      }]}>
                        <TouchableOpacity
                          style={[styles.closeButton, {
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,
                          },]}
                          onPress={() => {
                            // setState({ changeTable: false });
                            setExportModal(false);
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View style={styles.modalTitle}>
                          <Text
                            style={
                              switchMerchant
                                ? {
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'center',
                                  fontSize: 16,
                                }
                                : [styles.modalTitleText]
                            }>
                            Upload Options
                          </Text>
                        </View>
                        <View
                          style={{
                            alignItems: 'center',
                            top: switchMerchant ? '20%' : '10%',
                          }}>
                          {/* <TouchableOpacity
                    style={[styles.modalSaveButton, {
                      zIndex: -1.
                    }]}
                    onPress={() => { exportFile() }}>
                    <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Export to CSV</Text>
                  </TouchableOpacity> */}

                          {/* <TouchableOpacity
                    style={[styles.modalSaveButton, {
                      zIndex: -1.
                    }]}
                    onPress={() => { exportTemplate() }}>
                    <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Download Batch Template</Text>
                  </TouchableOpacity> */}

                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 180 : 240,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 45,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginBottom: 10,
                            }}
                            onPress={() => {
                              importTemplateData();
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP
                              })
                            }}
                            disabled={isLoading}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {isLoading ? 'LOADING...' : 'UPLOAD TEMPLATE'}
                            </Text>

                            {isLoading ? (
                              <ActivityIndicator
                                style={{
                                  marginLeft: 5,
                                }}
                                color={Colors.whiteColor}
                                size={'small'}
                              />
                            ) : (
                              <></>
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 180 : 240,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 45,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              exportTemplate();

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP
                              })
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXPORT TEMPLATE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </ModalView>

                  {/* <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      //width: 160,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginLeft: 15,
                    }}
                    onPress={() => {
                      setExportModal(true);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD
                      })
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon
                        name="upload"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        BATCH UPLOAD
                      </Text>
                    </View>
                  </TouchableOpacity> */}
                </View>

                {/* <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 15,
                    // display: 'none',
                  }}
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.selectedCustomerEdit = null;
                    });

                    navigation.navigate('NewCustomer');

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER
                    })
                  }}>
                  <View style={{}}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 10 : 20}
                      color="#FFFFFF"
                    />
                  </View>
                  <Text
                    style={{
                      marginLeft: 5,
                      color: Colors.primaryColor,
                      fontSize: switchMerchant ? 10 : 16,
                      color: '#FFFFFF',
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    CUSTOMER
                  </Text>
                </TouchableOpacity> */}

                <View
                  style={{
                    //marginTop: 10,
                    width: switchMerchant ? 200 : 250,
                    height: switchMerchant ? 35 : 40,
                    backgroundColor: 'white',
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  }}>
                  <Icon
                    name="search"
                    size={switchMerchant ? 13 : 18}
                    color={Colors.primaryColor}
                    style={{ marginLeft: 15 }}
                  />

                  <View style={{ flex: 4 }}>
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: switchMerchant ? 180 : 220,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_TB_SEARCH,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_TB_SEARCH
                        })
                      }}
                    // value={search}
                    />
                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  // width: windowWidth * 0.9,
                  marginBottom: 10,
                  justifyContent: 'center',
                }}>
                <View style={[{
                  backgroundColor: Colors.whiteColor,
                  width: Dimensions.get('window').width * 0.87,
                  // height: Dimensions.get('window').height * 0.75,
                  // marginTop: 15,
                  // marginHorizontal: 35,
                  // marginBottom: 20,

                  paddingVertical: 15,

                  alignSelf: 'center',
                  borderRadius: 5,
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,

                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }]}
                >
                  {/* left view */}
                  <View style={{
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',

                    width: '20%',

                    // backgroundColor: 'blue',
                  }}>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',

                      marginBottom: 5,
                    }}>
                      {
                        Array.from(Array(5)).map((_, starIndex) => {
                          return (
                            <View style={{
                              marginHorizontal: 5,
                            }}>
                              <FontAwesome name={summaryAverageRating > starIndex ? "star" : "star-o"} color={Colors.primaryColor} size={26} />
                            </View>
                          );
                        })
                      }
                    </View>

                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'flex-end',

                      marginBottom: 5,
                    }}>
                      <Text
                        style={{
                          fontSize: 34,
                          fontFamily: 'NunitoSans-Bold',
                          // marginTop: 15,

                          top: 5,
                        }}>
                        {`${summaryAverageRating.toFixed(1)}`}
                      </Text>

                      <Text
                        style={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Bold',
                          // marginTop: 15,

                          color: Colors.tabGrey,
                        }}>
                        {`/5`}
                      </Text>
                    </View>

                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: 'NunitoSans-SemiBold',
                        // marginTop: 15,

                        color: Colors.tabGrey,
                      }}>
                      {`Average Rating`}
                    </Text>
                  </View>

                  {/* right view */}
                  <View style={{
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',

                    // width: '70%',

                    // backgroundColor: 'green',
                  }} />
                  <FlatList
                    data={summaryRatingList}
                    extraData={summaryRatingList}
                    renderItem={renderSummaryItem}
                    keyExtractor={(item, index) => String(index)}
                    initialNumToRender={8}
                  />
                </View>
              </View>

              {/* <View
                style={{
                  flexDirection: 'row',
                  width: windowWidth * 0.9,
                  marginBottom: 10,
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: switchMerchant ? '35%' : '40%',
                    height: switchMerchant ? 60 : 100,
                    backgroundColor: Colors.tabCyan,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 25 : 30,
                    paddingVertical: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    marginRight: 10,
                  }}>
                  <View
                    style={{
                      justifyContent: 'center',
                      height: 60,
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 20 : 28,
                      }}>
                      {overviewCustomersPast30Days}
                    </Text>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 11 : 13,
                      }}>
                      Past 30 Days Customers
                    </Text>
                  </View>
                  <View>
                    <Ionicon
                      name="people-outline"
                      color={'#F7F7F7'}
                      size={switchMerchant ? 30 : 60}
                      style={{ opacity: 0.6, paddingLeft: 2 }}
                    />
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '35%' : '40%',
                    height: switchMerchant ? 60 : 100,
                    backgroundColor: Colors.tabGold,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 25 : 30,
                    paddingVertical: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}>
                  <View
                    style={{
                      justifyContent: 'center',
                      height: 60,
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 20 : 28,
                      }}>
                      RM{' '}
                      {(!isNaN(totalCustomersSpending)
                        ? totalCustomersSpending
                        : 0
                      )
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 11 : 13,
                      }}>
                      Total Customers Spending
                    </Text>
                  </View>
                  <View>
                    <Coins
                      height={switchMerchant ? 30 : 55}
                      width={switchMerchant ? 30 : 55}
                    />
                  </View>
                </View>
              </View> */}

              <View style={[styles.list1, {
                backgroundColor: Colors.whiteColor,
                width: Dimensions.get('window').width * 0.87,
                height: Dimensions.get('window').height * 0.75,
                marginTop: 15,
                marginHorizontal: 35,
                marginBottom: 20,
                alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                paddingTop: 10,
                paddingHorizontal: 5,
              }]}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    margin: 10,
                    marginBottom: 10,
                    marginTop: 12,
                    //marginLeft: 20,
                    //marginRight: 20,
                    //backgroundColor: 'red',
                    //height: '10%'
                  }}>
                  <View style={{ width: '55%', justifyContent: 'center' }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 18,
                        letterSpacing: 0.4,
                      }}>
                      Reviews
                    </Text>
                  </View>
                  <View
                    style={{
                      width: '40%',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    {/* Filter bar  */}
                    {/* <TouchableOpacity style={{

                justifyContent: 'center',
                width: '38%'
              }}
                onPress={() => {
                  setVisible(true);
                }}>
                <View style={{ justifyContent: 'center', width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: '#4E9F7D', borderRadius: 3, height: 35, alignItems: 'center' }}>
                  <Feather name='filter' size={18} color='#4E9F7D' />
                  <Text style={{
                    color: '#4E9F7D',
                    fontSize: 14,
                    fontFamily: 'Nunitosans-Bold',
                    marginLeft: 7
                  }}>
                    Manage Filter
                  </Text>
                </View>
              </TouchableOpacity> */}
                    <View style={{}} />
                    {/* Type 1 Search Bar */}
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'center', width: '53%', height: 40, alignItems: 'center', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, alignSelf: 'center' }}>
                <View style={{ flex: 3 }}>
                  <TextInput
                    placeholderTextColor='#737373'
                    style={{ marginLeft: 10, color: '#737373' }}
                    onChangeText={(text) => {
                      setSearch(text);
                    }}
                    defaultValue={search}
                  />
                </View>
                <View style={{ flex: 1, height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.primaryColor, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5 }}>
                  <Icon name='search' size={20} color={Colors.whiteColor} />
                </View>
              </View> */}

                    {/* Type 2 Search Bar */}
                    {/* <View style={{
                  marginTop: 10,
                  width: 250,
                  height: 40,
                  backgroundColor: 'white',
                  borderRadius: 5,
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}>
                  <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />

                  <View style={{ flex: 4 }}>
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: 220,
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());
                      }}
                    // value={search}
                    />
                  </View>
                </View> */}
                  </View>
                </View>

                {/****************List View Here****************/}

                <View style={{ width: '100%', marginTop: 0, borderRadius: 5 }}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      padding: 5,
                      paddingTop: 0,
                      height: '95%',
                      borderRadius: 5,
                      // shadowOffset: {
                      // width: 0,
                      // height: 1,
                      // },
                      // shadowOpacity: 0.22,
                      // shadowRadius: 2.22,
                      // elevation: 1,
                    }}>
                    <View
                      style={{
                        marginTop: 0,
                        flexDirection: 'row',
                        borderBottomWidth: 1,
                        borderColor: '#E5E5E5',
                        height: 57,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '19%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.NAME_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.NAME_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.NAME_ASC,
                              );
                            }
                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text
                              numberOfLines={2}
                              style={{
                                paddingLeft: 5,
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                                marginLeft: Platform.OS == 'ios' ? 5 : 0,
                              }}>
                              {'Name & User ID\n'}
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '15%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                          <Text
                            numberOfLines={2}
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            {'Contact Info\n'}
                          </Text>
                        </View>
                        <View style={{ marginLeft: '3%' }}>
                          {/* <TouchableOpacity onPress = {() => { 
                      setSort(USER_SORT_FIELD_TYPE.NUMBER_ASC)
                    }}>
                    <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                    </TouchableOpacity> 
                    <TouchableOpacity onPress = {() => { 
                       setSort(USER_SORT_FIELD_TYPE.NUMBER_DESC)
                    }}>
                    <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                    </TouchableOpacity>  */}
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '20%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.REVIEW_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.REVIEW_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.REVIEW_ASC,
                              );
                            }

                            // logEventAnalytics({
                            //   eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_GENDER,
                            //   eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_GENDER
                            // })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Review\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.REVIEW_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.REVIEW_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '15%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.RATING_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.RATING_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.RATING_ASC,
                              );
                            }

                            // logEventAnalytics({
                            //   eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_DOB,
                            //   eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMER_LIST_C_DOB
                            // })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Rating\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.RATING_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.RATING_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '15%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.OUTLET_NAME_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.OUTLET_NAME_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.OUTLET_NAME_ASC,
                              );
                            }
                            // logEventAnalytics({
                            //   eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_RACE,
                            //   eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_RACE
                            // })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Outlet\nName'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.OUTLET_NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.OUTLET_NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '15%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.CREATED_AT_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.CREATED_AT_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.CREATED_AT_ASC,
                              );
                            }
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Date\nTime'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.CREATED_AT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.CREATED_AT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      {/* <View
                        style={{
                          flexDirection: 'row',
                          width: '13%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.STATUS_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.STATUS_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.STATUS_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Avg Spending\nPer 30days'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.STATUS_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.STATUS_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}
                      {/* <View style={{ flex: 0.4 }} /> */}
                    </View>

                    {list1 ? (
                      // <View style={{ borderTopWidth: 1 }}>
                      (<FlatList
                        ////THIS IS FOR SEARCH////
                        showsVerticalScrollIndicator={false}
                        data={sortCRMUsers(
                          headerSorting.filter((item) => {
                            if (search !== '') {
                              const searchLowerCase = search.toLowerCase();

                              if (
                                (item.email || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (item.name || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (item.uniqueName || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (item.number || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (item.userIdHuman || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||

                                (item.review || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (`${item.rating} - ${CUSTOMER_RATING_WORDING[item.rating]}` || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (item.outletName || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                (moment(item.createdAt).format('YYYY MMM DD, HH:mm A') || '')
                                  .toLowerCase()
                                  .includes(searchLowerCase)
                              ) {
                                return true;
                              }

                              return false;
                            } else {
                              return true;
                            }
                          }),
                          sort,
                        ).slice(
                          (currentPage - 1) * perPage,
                          currentPage * perPage,
                        )}
                        // extraData={renderItem}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => String(index)}
                        style={{ marginTop: 0 }}
                      // initialNumToRender={4}
                      />)
                    ) : // </View>

                      null}
                    {/* {searchList ? (

                                <FlatList
                                    data={lists}
                                    extraData={lists}
                                    renderItem={renderSearchItem}
                                    keyExtractor={(item, index) => String(index)}
                                    initialNumToRender={8}
                                />

                            ) : null} */}
                  </View>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  //marginTop: 10,
                  width: windowWidth * 0.87,
                  alignItems: 'center',
                  alignSelf: 'center',
                  justifyContent: 'flex-end',
                  top:
                    Platform.OS == 'ios'
                      ? pushPagingToTop && keyboardHeight > 0
                        ? -keyboardHeight * 0.94
                        : 0
                      : 0,
                  // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                  // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                  // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                  borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                  paddingHorizontal:
                    pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                  // shadowOffset: {
                  //   width: 0,
                  //   height: 1,
                  // },
                  // shadowOpacity: 0.22,
                  // shadowRadius: 3.22,
                  // elevation: 1,
                  marginHorizontal: 25,
                  marginBottom: 20,
                  marginTop: switchMerchant ? 5 : 0,
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    marginRight: '1%',
                  }}>
                  Page
                </Text>
                <View
                  style={{
                    width: switchMerchant ? 65 : 70,
                    height: switchMerchant ? 20 : 35,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    justifyContent: 'center',
                    paddingHorizontal: 22,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  }}>
                  {console.log('currentPage')}
                  {console.log(currentPage)}

                  <TextInput
                    onChangeText={(text) => {
                      var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                      setCurrentPage(
                        currentPageTemp > pageCount
                          ? pageCount
                          : currentPageTemp < 1
                            ? 1
                            : currentPageTemp,
                      );

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_TB_PAGE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_TB_PAGE
                      })
                    }}
                    placeholder={currentPage.toString()}
                    placeholderStyle={{
                      fontSize: switchMerchant ? 10 : 14,
                      fontFamily: 'NunitoSans-Regular',
                    }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    style={{
                      color: 'black',
                      // fontFamily: 'NunitoSans-Regular',
                      fontSize: switchMerchant ? 10 : 14,
                      fontFamily: 'NunitoSans-Regular',
                      marginTop: Platform.OS === 'ios' ? 0 : -15,
                      marginBottom: Platform.OS === 'ios' ? 0 : -15,
                      textAlign: 'center',
                      width: '100%',
                    }}
                    value={currentPage.toString()}
                    defaultValue={currentPage.toString()}
                    keyboardType={'numeric'}
                    onFocus={() => {
                      setPushPagingToTop(true);
                    }}
                  />
                </View>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    marginLeft: '1%',
                    marginRight: '1%',
                  }}>
                  of {pageCount}
                </Text>
                <TouchableOpacity
                  style={{
                    width: switchMerchant ? 30 : 45,
                    height: switchMerchant ? 20 : 28,
                    backgroundColor: Colors.primaryColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => {
                    prevPage();

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON
                    })
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-left"
                    size={switchMerchant ? 20 : 25}
                    style={{ color: Colors.whiteColor }}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    width: switchMerchant ? 30 : 45,
                    height: switchMerchant ? 20 : 28,
                    backgroundColor: Colors.primaryColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => {
                    nextPage();

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON
                    })
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={switchMerchant ? 20 : 25}
                    style={{ color: Colors.whiteColor }}
                  />
                </TouchableOpacity>
              </View>
            </KeyboardAvoidingView>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper >)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  modalViewUploadOptions: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.85,
    height: Dimensions.get('window').height * 0.8,
    marginTop: 5,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
    marginBottom: 20,
  },
  list1: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.75,
    marginTop: 15,
    marginHorizontal: 35,
    marginBottom: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: 'center',

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  textItem: {
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: '100%',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: 'center',
    //alignContent: 'center',
    width: Dimensions.get('window').width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: 'white',
    borderRadius: 10,
    // marginLeft: '53%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',

    marginRight: Dimensions.get('window').width * Styles.sideBarWidth,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: -10,
    marginTop: 5,
    width: '97%',
    alignSelf: 'flex-end',
  },
  addButtonView: {
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    width: 155,
    height: 40,
    alignItems: 'center',
  },
  editButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    width: 74,
    height: 35,
    alignItems: 'center',
  },
  ManageFilterBox: {
    //width: Dimensions.get('window').width * 0.4,
    //height: windowHeight * 0.7,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  ManageFilterBox1: {
    width: Dimensions.get('window').width * 0.35,
    height: Dimensions.get('window').height * 0.55,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'center',
    borderColor: '#E5E5E5',
    borderWidth: 1,
    alignItems: 'center',
    alignSelf: 'center',
    //padding: 10,
    margin: 15,
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.35,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    height: Dimensions.get('window').width * 0.3,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalTitleText1: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 18,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.18,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },

  submitText: {
    height: 40,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 430,
    height: 50,
    //flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
});

export default CustomerReviewScreen;
