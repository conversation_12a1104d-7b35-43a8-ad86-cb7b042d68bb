import * as React from 'react';
import { Dimensions } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/FontAwesome5';
import HomeScreen from '../phonescreen/HomeScreen'
import ResetPasswordScreen from '../phonescreen/ResetPasswordScreen'
import OrderListScreen from '../phonescreen/OrderListScreen';
import TableScreen from '../phonescreen/TableScreen';
import TakeawayScreen from '../phonescreen/TakeAwayScreen';
import QueueScreen from '../phonescreen/QueueScreen';
import ReservationScreen from '../phonescreen/ReservationScreen';
import RingScreen from '../phonescreen/RingScreen';
import PaymentScreen from '../phonescreen/PaymentScreen';
import DrawerScreen from '../phonescreen/DrawerScreen';
import MainScreen from '../phonescreen/MainScreen';
import LoginScreen from '../phonescreen/LoginScreen';
import OutletMenuScreen from '../phonescreen/OutletMenuScreen';
import MenuItemDetailsScreen from '../phonescreen/MenuItemDetailsScreen';
import CartScreen from '../phonescreen/CartScreen';
import OrderHistoryScreen from '../phonescreen/OrderHistoryScreen';

// import KitchenScreen from '../screen/KitchenScreen';
// import TableScreen from '../screen/TableScreen';


import Colors from '../constant/Colors';
import { Platform } from 'react-native';

import Styles from '../constant/Styles';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();
const headerOption = {
  headerTitleStyle: { color: Colors.whiteColor, marginLeft: Platform.OS == 'ios' ?  0 : -0,fontFamily: 'NunitoSans-Bold', fontSize: 24,}, // bottom: Platform.OS === 'ios' ? 0 : -20 
  headerTintColor: Colors.darkColor,
  headerTitleAlign: 'center',
  headerStyle: {
    height: Dimensions.get('window').height * 0.11,
    backgroundColor: Colors.darkBgColor,
    elevation: 0,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerTintColor: 'white',
};


export default function App() {
  return (  
    <NavigationContainer>
      <Stack.Navigator>
        {/* <Stack.Screen
          name="Reset"
          component={ResetPasswordScreen}
          options={headerOption}
        />   */}
        {/* <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={headerOption}
        />  */}
        {/*
         <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={(navigation) => ({
            headerOption,
            left: () => (
              <TouchableOpacity
                onPress={() => { this.props.navigation.navigate('Profile') }}>
            <Image style={{width: 30,
        height: 30,}} source={require('../assets/image/drawer.png')}  />
            </TouchableOpacity> 
          ),
            })}
         
        />  
        */}
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={headerOption}
        />  
        <Stack.Screen
          name="OutletMenu"
          component={OutletMenuScreen}
          options={headerOption}
        />  
        <Stack.Screen
          name="MenuItemDetails"
          component={MenuItemDetailsScreen}
          options={headerOption}
        />  
        <Stack.Screen
          name="Cart"
          component={CartScreen}
          options={headerOption}
        /> 
        <Stack.Screen
          name="Main"
          component={MainScreen}
          options={headerOption}
        />  
         <Stack.Screen
          name="Profile"
          component={DrawerScreen}
          options={headerOption}
          />
        <Stack.Screen
          name="Table"
          component={TableScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Order"
          component={OrderListScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Takeaway"
          component={TakeawayScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Ring"
          component={RingScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="OrderHistory"
          component={OrderHistoryScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Reservation"
          component={ReservationScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Queue"
          component={QueueScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Payment"
          component={PaymentScreen}
          options={headerOption}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
