import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import {
  isTablet
} from '../util/common';
import { MERCHANT_VOUCHER_CODE_FORMAT, MERCHANT_VOUCHER_TYPE, SEGMENT_TYPE, EXPAND_TAB_TYPE, } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import AntDesign from 'react-native-vector-icons/AntDesign';

/////////////////////////////////////////////////////////////////////////////////////
const QUESTIONAIRE_OPTIONS = {
  SINGLE_CHOICE: 'SINGLE_CHOICE',
  MULTIPLE_CHOICE: 'MULTIPLE_CHOICE',
  REMARK: 'REMARK',
};

const QUESTIONAIRE_OPTIONS_DROPDOWN_LIST = [
  {
    label: 'Single Choice',
    value: QUESTIONAIRE_OPTIONS.SINGLE_CHOICE,
  },
  {
    label: 'Multiple Choice',
    value: QUESTIONAIRE_OPTIONS.MULTIPLE_CHOICE,
  },
  {
    label: 'Remark',
    value: QUESTIONAIRE_OPTIONS.REMARK,
  }
];


const SettingQuestionaire = props => {
  const {
    navigation,
  } = props;

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);

  const [questionaire, setQuestionaire] = useState([   //To add new questionaire
    {
      questionaireQuestion: '',
      singleChoice: [],
      multipleChoice: [],
      questionaireRemark: '',
      questionaireSpecify: '',
    }
  ]);
  const [questionaireIndex, setQuestionaireIndex] = useState(0);  //each questionaire index
  const [questionaireOption, setQuestionaireOption] = useState(QUESTIONAIRE_OPTIONS.SINGLE_CHOICE); //questionaire choices
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [questionaireQuestion, setQuestionaireQuestion] = useState('');  //Question (Merchant fill in)
  const [singleChoice, setSingleChoice] = useState([]); //Single Choice Option (answer for customer)(Merchant fill in, customer choose)
  const [singleChoiceIndex, setSingleChoiceIndex] = useState(0);
  const [multipleChoice, setMultipleChoice] = useState([]); //Multiple Choice Option (answer for customer)(Merchant fill in, customer choose)
  const [multipleChoiceIndex, setMultipleChoiceIndex] = useState(0);
  //const [questionaireRemark, setQuestionaireRemark] = useState(''); //Remark (for customer to fill in)
  //const [questionaireSpecify, setQuestionaireSpecify] = useState(''); //Specify  (for customer to fill in only)

  const [addSpecify, setAddSpecify] = useState(0);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  //////////////////////////////////////////////////////////////////////////////////////////////////////////


  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });


  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            textAlign: 'left',
            fontSize: switchMerchant ? 20 : 24,
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Questionaire
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////////////////////////////

  ////////Add New Questionaire////////
  const addQuestionFunc = () => {
    setQuestionaire([
      ...questionaire,
      {
        questionaireQuestion: '',
        singleChoice: [],
        multipleChoice: [],
        questionaireRemark: '',
        questionaireSpecify: '',
      }
    ]);
  }





  /////////////////////////////////////////////////////////////////////////
  //Render Start Here
  return (
    <View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {
    }]}>
      {/* <View style={[styles.sidebar, !isTablet() ? {
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={10} expandSettings={true} />
      </View> */}
      <View style={styles.list}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 20,
          marginBottom: 40,
          marginTop: 5,
          height: '10%',
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          paddingHorizontal: 10,
        }}>
          <View style={{ width: '55%', justifyContent: 'center' }}>
            <Text style={{ fontFamily: 'Nunitosans-bold', fontSize: 18, }}>
              Questionaire
            </Text>
          </View>
          <TouchableOpacity style={{ width: 130, height: 35, borderRadius: 7, backgroundColor: Colors.primaryColor, justifyContent: 'center' }}
            onPress={() => { props.navigation.navigate('SettingQuestionairePreview') }}
          >
            <View style={{ flexDirection: 'row', paddingHorizontal: 10, justifyContent: 'center', }}>
              <Text style={{ textAlign: 'center', fontFamily: 'Nunitosans-Bold', color: Colors.whiteColor, fontSize: 16 }}>
                Preview
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <ScrollView style={{
          borderTopWidth: 0.5,
          alignSelf: 'center',
          borderColor: '#E5E5E5',
          width: Dimensions.get('screen').width * 0.5,
        }}>
          <View style={{
            flexDirection: 'column',
            borderWidth: 1,
            borderColor: '#c4c4c4',
            borderRadius: 3,
            alignSelf: 'center',
            width: Dimensions.get('screen').width * 0.5,
            padding: 15,
          }}>
            <View style={{ marginBottom: 10 }}>
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, marginBottom: 5 }}>
                Question:
              </Text>
              <TextInput placeholder='Key in question'
                style={{
                  color: 'black',
                  width: '90%',
                  height: 40,
                  borderRadius: 5,
                  paddingHorizontal: 10,
                  padding: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                }}
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                onChangeText={(text) => { setQuestionaireQuestion(text) }}
              />
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center', height: 40, marginVertical: 10, marginBottom: 5 }}>
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16 }}>
                Option:
              </Text>
              <DropDownPicker style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 300, paddingVertical: 0, marginHorizontal: 10 }}
                dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 300, paddingVertical: 0, marginHorizontal: 10, zIndex: 1 }}
                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                arrowSize={17}
                arrowColor={'black'}
                placeholder={"Select an option"}
                // items={[{ label: 'Single choice', value: 'Single' }, { label: 'Multiple Choice', value: 'Multiple' }, {label: 'Remark', value: 'Remark' }]}
                items={QUESTIONAIRE_OPTIONS_DROPDOWN_LIST}
                //defaultValue={questionaireOption}
                onChangeItem={(item) => {
                  setQuestionaireOption(item.value);

                }}
              />
            </View>

            {questionaireOption === QUESTIONAIRE_OPTIONS.SINGLE_CHOICE ?
              <View style={{ zIndex: -1 }}>
                <View style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: 350, marginTop: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5 }}>
                  <TextInput
                    style={{}}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    placeholder='Enter an answer'
                    onChangeText={(text) => {
                      setSingleChoice(text)
                    }}
                  />
                  <TouchableOpacity style={{}}
                    onPress={() => { }}>
                    <AntDesign name="close" size={20} />
                  </TouchableOpacity>
                </View>
                <View style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: 350, marginTop: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5 }}>
                  <TextInput
                    style={{}}
                    placeholder='Enter an answer'
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      //setSingleChoice(text)
                      // setSingleChoice(singleChoice.map((singleChoiceGroup, index) => (index === singleChoiceIndex ? {
                      //   ...singleChoiceGroup,
                      //   choices: [

                      //   ]
                      // } : singleChoiceGroup)));
                    }}
                  />
                  <TouchableOpacity style={{}}
                    onPress={() => { }}>
                    <AntDesign name="close" size={20} />
                  </TouchableOpacity>
                </View>

                <View style={{ marginTop: 5, alignItems: 'center' }}>
                  <TouchableOpacity
                    style={{
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      flexDirection: 'row',
                      color: '#4cd964',
                      textAlign: 'center',
                      borderRadius: 10,
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      alignSelf: "flex-end",
                    }}
                    onPress={() => { }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                      <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                        Add
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

              </View>
              : <></>}

            {questionaireOption === QUESTIONAIRE_OPTIONS.MULTIPLE_CHOICE ?
              <View style={{ zIndex: -1 }}>
                <View style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: 350, marginTop: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5 }}>
                  <TextInput
                    style={{}}
                    placeholder='Enter an answer'
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      //setMultipleChoice(text);
                    }}
                  />
                  <TouchableOpacity style={{}}
                    onPress={() => { }}>
                    <AntDesign name="close" size={20} />
                  </TouchableOpacity>
                </View>
                <View style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: 350, marginTop: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5 }}>
                  <TextInput
                    style={{}}
                    placeholder='Enter an answer'
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={() => {
                      //setMultipleChoice(text);
                    }}
                  />
                  <TouchableOpacity style={{}}
                    onPress={() => { }}>
                    <AntDesign name="close" size={20} />
                  </TouchableOpacity>
                </View>

                <View style={{ marginTop: 5, alignItems: 'center' }}>
                  <TouchableOpacity
                    style={{
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      flexDirection: 'row',
                      color: '#4cd964',
                      textAlign: 'center',
                      borderRadius: 10,
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      alignSelf: "flex-end",
                    }}
                    onPress={() => { }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                      <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                        Add
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

              </View>
              : <></>}
            {questionaireOption === QUESTIONAIRE_OPTIONS.REMARK ?
              <View style={{ zIndex: -1, marginBottom: 18 }}>
                <View style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: 400, marginTop: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5 }}>
                  {/* <TextInput
                                    style={{ }}
                                    placeholder= 'Remark'
                                    onChangeText={(text) =>{ setQuestionaireRemark(text) }}
                                    value={questionaireRemark}
                                /> */}
                  <Text style={{ color: Colors.fieldtTxtColor }}>
                    Remark (For Customer only)
                  </Text>
                  {/* <TouchableOpacity style={{ }}
                                      onPress={() => {}}>
                                  <AntDesign name="close" size={22} />
                                </TouchableOpacity> */}
                </View>
              </View>
              : <></>}

            <View style={{ zIndex: -1 }}>
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, marginBottom: 5 }}>
                Specify:
              </Text>
              <View style={{ flexDirection: 'row', }}>
                <TextInput style={{
                  width: 350,
                  height: 40,
                  borderRadius: 5,
                  paddingHorizontal: 10,
                  padding: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  color: Colors.fieldtTxtColor,
                  justifyContent: 'center',
                  //textAlign: 'center',
                  paddingTop: 10,
                  placeholder: "Specify (For Customer Only)"
                }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  onChangeText={(text) => {
                    // setState({ note1: text });
                    setAddSpecify(text);
                  }}
                  value={addSpecify}
                />
                {/* <TextInput placeholder='specify'
                        style={{
                          color:'black',
                          width: 350,
                          height: 40,
                          borderRadius: 5,
                          paddingHorizontal: 10,
                          padding: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5'
                        }}
                        onChangeText={(text)=>{ setQuestionaireSpecify(text)}}
                        value={questionaireSpecify}
                      /> */}
                <TouchableOpacity style={{ marginLeft: 15, justifyContent: 'center' }}
                  onPress={() => { }}>
                  <Icon name="trash-2" size={22} color="#eb3446" />
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity style={[styles.submitText, { width: '90%', justifyContent: 'center' }]}
              onPress={() => {
                //addQuestionFunc() 
                setQuestionaire([
                  ...questionaire,
                  {
                    questionaireQuestion: '',
                    singleChoice: [],
                    multipleChoice: [],
                    questionaireRemark: '',
                    questionaireSpecify: '',
                  }
                ]);
              }}>
              <View style={{ flexDirection: 'row' }}>
                <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                  Add Question
                </Text>
              </View>
            </TouchableOpacity>


          </View>
        </ScrollView>



      </View>







    </View>
  )

};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('screen').width * 0.85,
    height: Dimensions.get('screen').height,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold'
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  submitText: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "center",
    marginTop: '5%'
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
  },
});

export default SettingQuestionaire;
