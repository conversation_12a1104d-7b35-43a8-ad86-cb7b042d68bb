import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal as ModalComponent,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  Touchable,
  Platform,
  useWindowDimensions,
  ViewComponent,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  MERCHANT_VOUCHER_TYPE,
  SEGMENT_TYPE,
  EXPAND_TAB_TYPE,
  ORDER_TYPE,
  OUTLET_SHIFT_STATUS,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
// import { DIMENTIONS } from 'react-native-numeric-input';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';
import GCalendar from '../assets/svg/GCalendar';
import APILocal from '../util/apiLocalReplacers';
// import { Row } from 'react-native-table-component';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { parseValidIntegerText } from '../util/common';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';

//////////////////////////////////////////////////////////////////////////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const CREDIT_SECTION = {
  STAMPS: 'STAMPS',
  TOPUP_CREDIT: 'TOPUP_CREDIT',
  POINTS: 'POINTS',
  VOUCHER: 'VOUCHER',
};

const REGISTER_SECTION = {
  STAMPS: 'STAMPS',
  TOPUP_CREDIT: 'TOPUP_CREDIT',
  POINTS: 'POINTS',
};

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const LoyaltyPayEarn = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const merchantUserId = UserStore.useState((s) => s.firebaseUid);
  const merchantUserName = UserStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const allOutletsItemsSkuDict = OutletStore.useState(
    (s) => s.allOutletsItemsSkuDict,
  );
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [startDate, setStartDate] = useState(moment().valueOf());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const selectedCustomerLCCTransactions = OutletStore.useState(
    (s) => s.selectedCustomerLCCTransactions,
  );
  const selectedCustomerLCCBalance = OutletStore.useState(
    (s) => s.selectedCustomerLCCBalance,
  );

  const selectedCustomerEdit = CommonStore.useState(
    (s) => s.selectedCustomerEdit,
  );

  const loyaltyCampaigns = OutletStore.useState((s) => s.loyaltyCampaigns);

  const selectedCustomerUserLoyaltyCampaigns = OutletStore.useState(
    (s) => s.selectedCustomerUserLoyaltyCampaigns,
  );

  const selectedCustomerUserTaggableVouchers = OutletStore.useState(
    (s) => s.selectedCustomerUserTaggableVouchers,
  );

  const [creditSection, setCreditSection] = useState(
    CREDIT_SECTION.TOPUP_CREDIT,
  );
  const [registerSection, setRegisterSection] = useState(
    REGISTER_SECTION.TOPUP_CREDIT,
  );

  const [phoneNumber, setPhoneNumber] = useState('+60');
  const [userName, setUserName] = useState('');

  const [stampModal, setStampModal] = useState(false);
  const [stampDoneModal, setStampDoneModal] = useState(false);

  const [redeemCreditModal, setRedeemCreditModal] = useState(false);
  const [addCreditModal, setAddCreditModal] = useState(false);

  const [topupCreditModal, setTopupCreditModal] = useState(false);
  const [topupCreditDoneModal, setTopupCreditDoneModal] = useState(false);

  const [pointsModal, setPointsModal] = useState(false);
  const [pointsDoneModal, setPointsDoneModal] = useState(false);

  const [amount, setAmount] = useState('');
  const [addnotes, setAddnotes] = useState(false);
  const [notetext, setNotetext] = useState('');

  const [registerModal, setRegisterModal] = useState(false);

  const [registerDetail, setRegisterDetail] = useState(false);
  const [redeemdone, setRedeemdone] = useState(false);
  const [addCreditdone, setAddCreditdone] = useState(false);
  const [deductCreditdone, setDeductCreditdone] = useState(false);
  const [rev_date, setRev_date] = useState(moment().startOf('day'));
  const [rev_date1, setRev_date1] = useState(moment().endOf('day'));
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [email, setEmail] = useState('');

  const [phoneNumberChecking, setPhoneNumberChecking] = useState(false);
  const [currCRMUser, setCurrCRMUser] = useState(null);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const [temp, setTemp] = useState('');

  /////////////////////////////////////////////////////////////

  // 2022 - Loyalty stamp earn

  const [loyaltyStampsActionItems, setLoyaltyStampsActionItems] = useState([]);

  const loyaltyStamps = OutletStore.useState((s) => s.loyaltyStamps);
  const selectedCustomerUserLoyaltyStamps = OutletStore.useState(
    (s) => s.selectedCustomerUserLoyaltyStamps,
  );

  /////////////////////////////////////////////////////////////

  // 2022 - Purchase topup credit types

  const [topupCreditTypeActionItems, setTopupCreditTypeActionItems] = useState(
    [],
  );

  const topupCreditTypes = OutletStore.useState((s) => s.topupCreditTypes);
  // const selectedCustomerUserLoyaltyStamps = OutletStore.useState(s => s.selectedCustomerUserLoyaltyStamps);

  /////////////////////////////////////////////////////////////

  // 2022-08-19 - Claim points

  const [earnedPoints, setEarnedPoints] = useState(0);

  /////////////////////////////////////////////////////////////

  // 2022 - Add Customer

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const [customerAvatar, setCustomerAvatar] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('+60');
  const [customerUsername, setCustomerUsername] = useState('');

  const [customerNextVisitDate, setCustomerNextVisitDate] = useState('');
  const [customerNextVisitTime, setCustomerNextVisitTime] = useState('');
  const [customerNextVisitDateIsChanged, setCustomerNextVisitDateIsChanged] =
    useState(false);
  const [customerNextVisitTimeIsChanged, setCustomerNextVisitTimeIsChanged] =
    useState(false);

  const [customerIsActiveMember, setCustomerIsActiveMember] = useState(true);
  const [customerTags, setCustomerTags] = useState([]);
  const [customerGender, setCustomerGender] = useState('Others');
  const [customerDob, setCustomerDob] = useState(Date.now());
  const [customerEmail, setCustomerEmail] = useState('');
  const [customerRace, setCustomerRace] = useState('');

  const [customerAddress, setCustomerAddress] = useState('');
  const [customerAddressLat, setCustomerAddressLat] = useState('');
  const [customerAddressLng, setCustomerAddressLng] = useState('');

  const [customerState, setCustomerState] = useState('');
  const [customerPostcode, setCustomerPostcode] = useState('');
  const [customerFirstVisitDate, setCustomerFirstVisitDate] = useState('');
  const [customerLastVisitDate, setCustomerLastVisitDate] = useState('');

  const [customerPhoneSecond, setCustomerPhoneSecond] = useState('');
  const [customerEmailSecond, setCustomerEmailSecond] = useState('');
  const [customerAddressSecond, setCustomerAddressSecond] = useState('');
  const [customerAddressLatSecond, setCustomerAddressLatSecond] = useState('');
  const [customerAddressLngSecond, setCustomerAddressLngSecond] = useState('');

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  const currOutletShiftStatus = OutletStore.useState((s) => s.currOutletShiftStatus);

  const createCRMUser = async (isAutoPush = false) => {
    if (
      // !customerAvatar ||
      // !customerName ||
      !phoneNumber ||
      !userName
      // !customerEmail
      // !customerAddress
    ) {
      Alert.alert(
        'Error',
        'Please fill in all required information:\nName\nContact number',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else {
      ///////////////////////////////////

      CommonStore.update((s) => {
        s.isLoading = true;
      });
      // upload image

      var profileImageImagePath = '';
      var profileImageCommonIdLocal = uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        profileImageImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
        );
      }

      // means new item

      var body = {
        merchantId,
        merchantName,
        outletId: currOutletId,

        avatar: profileImageImagePath,
        isImageChanged,
        dob: customerDob,
        email: email || '',
        gender: customerGender,
        name: userName,
        number: phoneNumber.startsWith('+')
          ? phoneNumber.slice(1)
          : phoneNumber,
        uniqueName: customerUsername || '',

        address: customerAddress,
        lat: parseFloat(customerAddressLat),
        lng: parseFloat(customerAddressLng),

        emailSecond: customerEmailSecond,
        numberSecond: customerPhoneSecond,
        addressSecond: customerAddressSecond,
        latSecond: parseFloat(customerAddressLatSecond),
        lngSecond: parseFloat(customerAddressLngSecond),

        timeline: {},

        commonId: profileImageCommonIdLocal,
      };

      // console.log(body, 'here' + isLoading);

      ApiClient.POST(API.createCRMUser, body, false)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Customer has been added',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    if (registerSection == 'POINTS') {
                      setPointsModal(true);
                    } else if (registerSection == 'STAMPS') {
                      setStampModal(true);
                    } else {
                      setTopupCreditModal(true);
                    }
                    setRegisterModal(false);
                  },
                },
              ],
              { cancelable: false },
            );
          }
        })
        .catch((err) => {
          // console.log(err, 'here here');
        });
      // CommonStore.update((s) => {
      //   s.isLoading = false;
      // });
    }
  };

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    setPhoneNumberChecking(true);

    if (phoneNumber.length === 12 || phoneNumber.length === 13) {
      checkPhoneNumberData();
    }
  }, [phoneNumber, crmUsers]);

  const checkPhoneNumberData = async () => {
    const phoneNumberParsed = phoneNumber.slice(2);
    const phoneNumberParsedCountry = phoneNumber.slice(1);

    let crmUser = crmUsers.find(
      (crmUser) =>
        crmUser.number === phoneNumberParsedCountry ||
        crmUser.number === phoneNumberParsed,
    );
    console.log(`phonenumber parsed country ${phoneNumberParsedCountry}`);
    console.log(crmUser);

    /////////////////

    if (!crmUser) {
      console.log('find user');

      const userSnapshot = await firestore()
        .collection(Collections.CRMUser)
        .where('outletId', '==', currOutletId)
        .where('number', '==', phoneNumberParsedCountry)
        .where('deletedAt', '==', null)
        .limit(1)
        .get();

      console.log('find user done');

      if (userSnapshot.size > 0) {

        crmUser = userSnapshot.docs[0].data();
        console.log('search users result:', crmUser);
      }
    }

    console.log(crmUser);

    if (crmUser) {
      // means got the same user found

      console.log('found user');

      CommonStore.update((s) => {
        s.selectedCustomerEdit = crmUser;
      });

      setPhoneNumberChecking(false);

      setCurrCRMUser(crmUser);
    } else {
      // no existing user found

      console.log('no user found');

      CommonStore.update((s) => {
        s.selectedCustomerEdit = null;
      });

      setPhoneNumberChecking(false);

      setCurrCRMUser(null);
    }
  };

  const earnPointsByAmountSpent = () => {
    var body = {
      amount: parseFloat(amount),

      userName,
      userPhone: phoneNumber.slice(1),
      // emailAddress: email,
      // dob: startDate,

      merchantId,
      merchantName,
      merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
    APILocal.earnPointsByAmountSpent({ body, uid: merchantUserId }).then(
      (result) => {
        if (result && result.status === 'success') {
          setPointsModal(false);

          setEarnedPoints(result.earnedPoints);

          setPhoneNumberChecking(false);

          setTimeout(() => {
            setPointsDoneModal(true);
          }, 1000);

          Alert.alert(
            'Success',
            'Points has been earned',
            [
              {
                text: 'OK',
                onPress: () => {
                  // setStampModal(false);                  
                  //setAmount('');
                  //setPhoneNumber('+60');
                  //setCurrCRMUser(null);                  
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          setStampModal(false);
          //setAmount('');
          //setPhoneNumber('+60');
          //setCurrCRMUser(null);
          setPhoneNumberChecking(false);
        }
      },
    );
  };

  // const claimCashback = () => {
  //   var body = {
  //     amount: parseFloat(amount),

  //     userName: userName,
  //     userPhone: phoneNumber.slice(1),
  //     // emailAddress: email,
  //     // dob: startDate,

  //     merchantId: merchantId,
  //     merchantName: merchantName,
  //     merchantLogo: merchantLogo,

  //     outletId: currOutlet.uniqueId,
  //     outletName: currOutlet.name,
  //     outletCover: currOutlet.cover,
  //   };

  //   // console.log(body);

  //   setPhoneNumberChecking(true);

  //   // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
  //   APILocal.loyaltyCampaignAddCashback({ body: body, uid: merchantUserId }).then(
  //     (result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'Cashback has been claimed',
  //           [
  //             {
  //               text: 'OK',
  //               onPress: () => {
  //                 setStampModal(false);
  //                 //setAmount('');
  //                 //setPhoneNumber('+60');
  //                 //setCurrCRMUser(null);
  //                 setPhoneNumberChecking(false);

  //                 setTimeout(() => {
  //                   setStampDoneModal(true);
  //                 }, 500);
  //               },
  //             },
  //           ],
  //           { cancelable: false },
  //         );
  //       } else {
  //         setStampModal(false);
  //         //setAmount('');
  //         //setPhoneNumber('+60');
  //         //setCurrCRMUser(null);
  //         setPhoneNumberChecking(false);
  //       }
  //     },
  //   );
  // };
  // const reedemcredit = () => {
  //   var body = {
  //     amount: parseFloat(amount),

  //     userName: userName,
  //     userPhone: phoneNumber.slice(1),
  //     // emailAddress: email,
  //     // dob: startDate,

  //     merchantId: merchantId,
  //     merchantName: merchantName,
  //     merchantLogo: merchantLogo,

  //     outletId: currOutlet.uniqueId,
  //     outletName: currOutlet.name,
  //     outletCover: currOutlet.cover,
  //   };

  //   // console.log(body);

  //   setPhoneNumberChecking(true);

  //   // ApiClient.POST(API.loyaltyCampaignAddCashback, body, false)
  //   APILocal.loyaltyCampaignAddCashback({ body: body, uid: merchantUserId }).then(
  //     (result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'Credit has been redeemed',
  //           [
  //             {
  //               text: 'OK',
  //               onPress: () => {
  //                 setRedeemCreditModal(false);
  //                 //setAmount('');
  //                 //setPhoneNumber('+60');
  //                 //setCurrCRMUser(null);
  //                 setPhoneNumberChecking(false);
  //                 setTimeout(() => {
  //                   setRedeemdone(true);
  //                 }, 500);
  //               },
  //             },
  //           ],
  //           { cancelable: false },
  //         );
  //       } else {
  //         setRedeemCreditModal(false);
  //         //setAmount('');
  //         //setPhoneNumber('+60');
  //         //setCurrCRMUser(null);
  //         setPhoneNumberChecking(false);
  //       }
  //     },
  //   );
  // };

  // const addCredit = () => {
  //   var body = {
  //     amount: parseFloat(amount),

  //     userName: userName,
  //     userPhone: phoneNumber.slice(1),

  //     merchantId: merchantId,
  //     merchantName: merchantName,
  //     merchantLogo: merchantLogo,

  //     outletId: currOutlet.uniqueId,
  //     outletName: currOutlet.name,
  //     outletCover: currOutlet.cover,
  //   };

  //   // console.log(body);

  //   setPhoneNumberChecking(true);

  //   // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
  //   APILocal.loyaltyCampaignAddCredit({ body: body, uid: merchantUserId }).then(
  //     (result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'Credit has been added',
  //           [
  //             {
  //               text: 'OK',
  //               onPress: () => {
  //                 setAddCreditModal(false);
  //                 //setAmount('');
  //                 //setPhoneNumber('+60');
  //                 //setCurrCRMUser(null);
  //                 setPhoneNumberChecking(false);
  //                 setTimeout(() => {
  //                   setTopupCreditDoneModal(true);
  //                 }, 500);
  //               },
  //             },
  //           ],
  //           { cancelable: false },
  //         );
  //       } else {
  //         setAddCreditModal(false);
  //         //setAmount('');
  //         //setPhoneNumber('+60');
  //         //setCurrCRMUser(null);
  //         setPhoneNumberChecking(false);
  //       }
  //     },
  //   );
  // };

  // const deductCredit = () => {
  //   var body = {
  //     amount: -parseFloat(amount),

  //     userName: userName,
  //     userPhone: phoneNumber.slice(1),

  //     merchantId: merchantId,
  //     merchantName: merchantName,
  //     merchantLogo: merchantLogo,

  //     outletId: currOutlet.uniqueId,
  //     outletName: currOutlet.name,
  //     outletCover: currOutlet.cover,
  //   };

  //   // console.log(body);

  //   setPhoneNumberChecking(true);

  //   // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
  //   APILocal.loyaltyCampaignAddCredit({ body: body, uid: merchantUserId }).then(
  //     (result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert(
  //           'Success',
  //           'Credit has been deducted',
  //           [
  //             {
  //               text: 'OK',
  //               onPress: () => {
  //                 setTopupCreditModal(false);
  //                 //setAmount('');
  //                 //setPhoneNumber('+60');
  //                 //setCurrCRMUser(null);
  //                 setPhoneNumberChecking(false);
  //                 setTimeout(() => {
  //                   setDeductCreditdone(true);
  //                 }, 500);
  //               },
  //             },
  //           ],
  //           { cancelable: false },
  //         );
  //       } else {
  //         setTopupCreditModal(false);
  //         //setAmount('');
  //         //setPhoneNumber('+60');
  //         //setCurrCRMUser(null);
  //         setPhoneNumberChecking(false);
  //       }
  //     },
  //   );
  // };

  const earnLoyaltyStamp = () => {
    var stampActionItems = loyaltyStampsActionItems.map((actionItem) => {
      var foundUserStamp = selectedCustomerUserLoyaltyStamps.find(
        (userStamp) => userStamp.loyaltyStampId === actionItem.stampId,
      );

      if (foundUserStamp) {
        var quantityMaxAllowedParsed = parseInt(actionItem.totalNumberOfStamp);
        var quantityToAddedParsed = parseInt(actionItem.noOfStamp);
        // var quantityUser = foundUserStamp.buyHistory.reduce((accum, history) => accum + history.noOfStamp, 0);
        // var quantityUser = foundUserStamp.stampCount;
        // if (quantityToAddedParsed + quantityUser > quantityMaxAllowedParsed) {
        //   quantityToAddedParsed = quantityMaxAllowedParsed;
        // }

        return {
          ...actionItem,
          noOfStamp: quantityToAddedParsed,
          totalNumberOfStamp: quantityMaxAllowedParsed,
        };
      } else {
        return {
          ...actionItem,
          noOfStamp: parseInt(actionItem.noOfStamp),
          totalNumberOfStamp: parseInt(actionItem.totalNumberOfStamp),
        };
      }
    });

    var body = {
      stampActionItems,

      userName,
      userPhone: phoneNumber.slice(1),

      merchantId,
      merchantName,
      merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
    APILocal.earnLoyaltyStamp({ body, uid: merchantUserId }).then(
      (result) => {
        if (result && result.status === 'success') {
          setStampModal(false);
          setPhoneNumberChecking(false);

          // setLoyaltyStampsActionItems([]);

          setTimeout(() => {
            // setAddCreditdone(true);
            setStampDoneModal(true);
          }, 1000);

          Alert.alert(
            'Success',
            'Loyalty stamp has been collected.',
            [
              {
                text: 'OK',
                onPress: () => {
                  // setAddCreditModal(false);
                  // setPhoneNumberChecking(false);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          // setAddCreditModal(false);
          // setPhoneNumberChecking(false);

          setStampModal(false);
          setPhoneNumberChecking(false);
        }
      },
    );
  };

  const earnTopupCredit = () => {
    // var stampActionItems = loyaltyStampsActionItems.map(actionItem => {
    //   var foundUserStamp = selectedCustomerUserLoyaltyStamps.find(userStamp => userStamp.loyaltyStampId === actionItem.stampId);

    //   if (foundUserStamp) {
    //     var quantityMaxAllowedParsed = parseInt(actionItem.totalNumberOfStamp);
    //     var quantityToAddedParsed = parseInt(actionItem.noOfStamp);
    //     // var quantityUser = foundUserStamp.buyHistory.reduce((accum, history) => accum + history.noOfStamp, 0);
    //     var quantityUser = foundUserStamp.stampCount;
    //     if ((quantityToAddedParsed + quantityUser) > quantityMaxAllowedParsed) {
    //       quantityToAddedParsed = quantityMaxAllowedParsed;
    //     }

    //     return {
    //       ...actionItem,
    //       noOfStamp: quantityToAddedParsed,
    //       totalNumberOfStamp: quantityMaxAllowedParsed,
    //     };
    //   }
    //   else {
    //     return {
    //       ...actionItem,
    //       noOfStamp: parseInt(actionItem.noOfStamp),
    //       totalNumberOfStamp: parseInt(actionItem.totalNumberOfStamp),
    //     };
    //   }
    // });

    var body = {
      topupCreditTypeActionItems,
      topupCreditPointTotal: topupCreditTypeActionItems.reduce(
        (accum, creditType) => accum + creditType.creditPoint,
        0,
      ),
      topupCreditPriceTotal: topupCreditTypeActionItems.reduce(
        (accum, creditType) => accum + creditType.price,
        0,
      ),
      orderId: result.order.uniqueId,

      userName,
      userPhone: phoneNumber.slice(1),

      merchantId,
      merchantName,
      merchantLogo,

      outletId: currOutlet.uniqueId,
      outletName: currOutlet.name,
      outletCover: currOutlet.cover,
    };

    // console.log(body);

    setPhoneNumberChecking(true);

    // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
    APILocal.earnTopupCredit({ body, uid: merchantUserId }).then(
      (result) => {
        if (result && result.status === 'success') {
          setTopupCreditModal(false);

          setPhoneNumberChecking(false);

          // setLoyaltyStampsActionItems([]);

          setTimeout(() => {
            setTopupCreditDoneModal(true);
          }, 1000);

          Alert.alert(
            'Success',
            'Topup credit has been earned.',
            [
              {
                text: 'OK',
                onPress: () => {
                  //setTopupCreditDoneModal(true);
                  // setAddCreditModal(false);
                  // setPhoneNumberChecking(false);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          // setAddCreditModal(false);
          // setPhoneNumberChecking(false);

          setTopupCreditModal(false);
          setPhoneNumberChecking(false);
        }
      },
    );
  };

  const PhoneonNumPadBtn = (key) => {
    var plus = phoneNumber.split('+')[1];
    if (key >= 0 || key == '+') {
      var phoneLength = 12;
      if (phoneNumber.startsWith('+6011')) {
        phoneLength = 13;
      }

      if (phoneNumber.includes('+'))
        if (phoneNumber.length < phoneLength && plus.length < phoneLength)
          setPhoneNumber(phoneNumber + key);
      if (!phoneNumber.includes('+')) {
        if (phoneNumber.length < phoneLength) setPhoneNumber(phoneNumber + key);
      }
    } else if (phoneNumber.length > 0) setPhoneNumber(phoneNumber.slice(0, key));
  };

  const onNumPadBtn = (key) => {
    var decimal = amount.split('.')[1];
    if (key >= 0 || key == '.') {
      if (amount.includes('.'))
        if (amount.length < 12 && decimal.length < 2) setAmount(amount + key);
      if (!amount.includes('.')) {
        if (amount.length < 12) setAmount(amount + key);
      }
    } else if (amount.length > 0) setAmount(amount.slice(0, key));
  };

  const deleteItem = async (item) => {
    // var updateListItemIndex = 0;
    // for (var i = 0; i < cartItems.length; i++) {
    //   if (
    //     loyaltyStampsActionItems[i].itemId === item.itemId &&
    //     loyaltyStampsActionItems[i].cartItemDate === item.cartItemDate
    //   ) {
    //     updateListItemIndex = i;
    //   }
    // }
    // setLoyaltyStampsActionItems([
    //   ...loyaltyStampsActionItems.slice(0, updateListItemIndex),
    //   ...loyaltyStampsActionItems.slice(updateListItemIndex + 1),s
    // ]);
  };

  const rightAction = (item) => {
    return (
      <View style={styles.insideSwipe}>
        <TouchableOpacity
          onPress={() => {
            deleteItem(item);
          }}>
          <View
            style={[
              styles.swipeButton,
              switchMerchant
                ? { height: Dimensions.get('screen').height * 0.285, width: 120 }
                : {},
            ]} />
        </TouchableOpacity>
      </View>
    );
  };

  const [editAmountPopup, setEditAmountPopup] = useState(false);

  const dummydata = [
    {
      name: 'Coffee',
      amount: 1,
    },
    {
      name: 'Fish',
      amount: 2,
    },
    {
      name: 'Beer',
      amount: 3,
    },
  ];

  const dummydatalist = [
    {
      name: 'Coffee',
      amount: 1,
    },
    {
      name: 'Fish',
      amount: 2,
    },
    {
      name: 'Lamb',
      amount: 3,
    },
    {
      name: 'Beer',
      amount: 3,
    },
    {
      name: 'Cake',
      amount: 3,
    },
    {
      name: 'LokLok',
      amount: 3,
    },
    {
      name: 'Beef',
      amount: 3,
    },
    {
      name: 'Chicken',
      amount: 3,
    },
    {
      name: 'Ice Cream',
      amount: 3,
    },
    {
      name: 'Beef',
      amount: 3,
    },
    {
      name: 'Chicken',
      amount: 3,
    },
    {
      name: 'Ice Cream',
      amount: 3,
    },
  ];

  const addToLoyaltyStampsActionItems = (item) => {
    var loyaltyStampsActionItemsTemp = [...loyaltyStampsActionItems];

    if (
      loyaltyStampsActionItems.find(
        (actionItem) => actionItem.stampId === item.uniqueId,
      )
    ) {
      // accumulate it up

      loyaltyStampsActionItemsTemp = loyaltyStampsActionItemsTemp.map(
        (actionItem) => {
          if (actionItem.stampId === item.uniqueId) {
            var quantityParsed = parseInt(actionItem.noOfStamp) + 1;
            if (quantityParsed > parseInt(actionItem.totalNumberOfStamp)) {
              quantityParsed = parseInt(actionItem.totalNumberOfStamp);
            }

            return {
              ...actionItem,
              noOfStamp: quantityParsed.toFixed(0),
            };
          } else {
            return actionItem;
          }
        },
      );
    } else {
      // add to list

      loyaltyStampsActionItemsTemp.push({
        name: item.name,
        stampId: item.uniqueId,
        noOfStamp: '1',
        totalNumberOfStamp: item.totalNumberOfStamp.toFixed(0),

        itemSku: '',
        loyaltyStampId: item.uniqueId,
        lsItemId: '',
        quantity: 0,
        userOrderId: '',
      });
    }

    setLoyaltyStampsActionItems(loyaltyStampsActionItemsTemp);
  };

  const addToTopupCreditTypeActionItems = (item) => {
    var topupCreditTypeActionItemsTemp = [...topupCreditTypeActionItems];

    if (
      topupCreditTypeActionItems.find(
        (actionItem) => actionItem.itemId === item.uniqueId,
      )
    ) {
      // accumulate it up

      topupCreditTypeActionItemsTemp = topupCreditTypeActionItemsTemp.map(
        (actionItem) => {
          if (actionItem.itemId === item.uniqueId) {
            var quantityParsed = parseInt(actionItem.quantity) + 1;

            return {
              ...actionItem,
              quantity: quantityParsed.toFixed(0),
              price: quantityParsed * actionItem.priceSingle,
              priceOriginal: quantityParsed * actionItem.priceSingle,
              creditPoint: quantityParsed * actionItem.creditPointSingle,
            };
          } else {
            return actionItem;
          }
        },
      );
    } else {
      // add to list

      topupCreditTypeActionItemsTemp.push({
        addOns: [],
        cartItemDate: Date.now(),
        categoryId: '',
        choices: {},
        cookedAt: null,
        deliveredAt: null,
        discountPromotions: 0,
        extraPrice: 0,
        fireOrder: false,
        image: item.image,
        isChecked: false,
        isDocket: false,
        isFreeItem: false,
        itemId: item.uniqueId,
        itemName: item.name,
        name: item.name,
        orderType: ORDER_TYPE.PICKUP,
        originCartItemId: item.uniqueId,
        prepareTime: 600,
        price: item.price, // important
        priceSingle: item.price,
        printerAreaList: [],
        printingTypeList: null,
        promotionId: '',
        remarks: '',
        quantity: '1', // important

        isTopupCreditType: true,
        creditPoint: item.value,
        creditPointSingle: item.value,

        priceOriginal: item.price,

        creditTypeId: item.uniqueId,
        batchList: item.batchList ? item.batchList : [],
        smsText: item.smsText ? item.smsText : '',
      });
    }

    setTopupCreditTypeActionItems(topupCreditTypeActionItemsTemp);
  };

  const renderCollectStampItems = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          addToLoyaltyStampsActionItems(item);
        }}>
        <View
          style={{
            //backgroundColor: '#717378',
            backgroundColor: '#fafafa',

            width: windowWidth * 0.1,
            height: windowHeight * 0.25,

            //height: switchMerchant ? windowWidth * 0.19 : '50%',
            borderRadius: 10,
            //marginLeft: 30,
            //marginTop: 10,
            margin: 5,
            zIndex: switchMerchant ? -1000 : -1,
            alignSelf: 'center',
            justifyContent: 'flex-start',
            alignContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            flex: 1,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,

            borderColor: Colors.secondaryColor,
            borderWidth: 0.5,
            // borderColor: Colors.primaryColor,
            // borderRadius: 1,
          }}>
          {item.image ? (
            <>
              <AsyncImage
                style={{
                  justifyContent: 'center',
                  width: windowWidth * 0.07,
                  height: windowWidth * 0.07,
                  alignitem: 'center',
                  alignSelf: 'center',
                  top: 15,
                }}
                resizeMode="cover"
                source={{ uri: item.image }}
                item={item}
              />
            </>
          ) : (
            <View
              style={{
                //backgroundColor: 'red',
                justifyContent: 'center',
                width: windowWidth * 0.07,
                height: windowWidth * 0.07,
                alignitem: 'center',
                alignSelf: 'center',
              }}>
              <Icon
                name="fast-food-outline"
                size={70}
                style={{ alignSelf: 'center', marginBottom: '-26%' }}
              />
            </View>
          )}
          <View
            style={{
              height: switchMerchant ? windowWidth * 0.07 : windowWidth * 0.065,
              alignItems: 'center',
              justifyContent: 'center',
              //alignSelf: 'flex-start',
              //flex: 0.24,
              padding: switchMerchant ? 5 : 5,
              paddingHorizontal: 8,
              //marginTop: 5,
              //alignItems: 'center'
            }}>
            <Text
              style={{
                color: Colors.blackColor,
                //alignSelf: 'center',
                //padding: 10,
                //position: 'absolute',
                //justifyContent: 'center',
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Bold',
                bottom: 7,
                textAlign: 'center',
              }}
              numberOfLines={switchMerchant ? (windowWidth <= 800 ? 2 : 2) : 2}>
              {item.name}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderCollectStampItemList = ({ item, index }) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          alignItems: 'center',
          paddingHorizontal: 10,
          borderRadius: 5,
          backgroundColor: Colors.fieldtBgColor,
          marginBottom: 10,
        }}>
        <Text
          style={{
            fontSize: switchMerchant ? 10 : 16,
            fontFamily: 'NunitoSans-Regular',
            width: '65%',
            paddingLeft: 20,
          }}>
          {item.name}
        </Text>
        <View style={{ justifyContent: 'center', alignSelf: 'center' }}>
          <View
            style={{
              flexDirection: 'row',
              // paddingTop: 10
            }}>
            <TouchableOpacity
              onPress={() => {
                var quantityParsed = parseInt(item.noOfStamp) - 1;
                if (quantityParsed < 1) {
                  quantityParsed = 1;
                }

                setLoyaltyStampsActionItems(
                  loyaltyStampsActionItems.map((actionItem) => {
                    if (actionItem.stampId === item.stampId) {
                      return {
                        ...actionItem,
                        noOfStamp: quantityParsed.toFixed(0),
                      };
                    } else {
                      return actionItem;
                    }
                  }),
                );
              }}>
              <View
                style={[
                  styles.addBtn,
                  { backgroundColor: Colors.descriptionColor },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowWidth * 0.03,
                    }
                    : {},
                ]}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 15,
                    fontWeight: '500',
                    color: Colors.whiteColor,
                  }}>
                  -
                </Text>
              </View>
            </TouchableOpacity>
            <View
              style={[
                styles.addBtn,
                {
                  backgroundColor: Colors.whiteColor,
                  borderWidth: StyleSheet.hairlineWidth,
                  borderColor: Colors.descriptionColor,
                  borderWidth: 1.5,

                  width: 30,

                  marginLeft: -1,
                },
                switchMerchant
                  ? {
                    width: windowWidth * 0.03,
                    height: windowWidth * 0.03,
                  }
                  : {},
              ]}>
              <TextInput
                style={[
                  {
                    fontSize: 15,
                    fontWeight: 'bold',
                    color: Colors.primaryColor,
                    ...(Platform.OS === 'ios' && {
                      paddingTop: 5,
                      paddingBottom: 5,
                    }),
                  },
                ]}
                //iOS
                placeholder={'0'}
                placeholderTextColor={Platform.select({
                  ios: '#a9a9a9',
                })}
                // clearTextOnFocus
                selectTextOnFocus
                //////////////////////////////////////////////
                //Android
                // onFocus={() => {
                //   setTemp(item.noOfStamp);
                //   setLoyaltyStampsActionItems(
                //     loyaltyStampsActionItems.map((actionItem) => {
                //       if (actionItem.stampId === item.stampId) {
                //         return {
                //           ...actionItem,
                //           noOfStamp: '',
                //         };
                //       } else {
                //         return actionItem;
                //       }
                //     }),
                //   );
                // }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                // onEndEditing={() => {
                //   if (item.noOfStamp == '') {
                //     setLoyaltyStampsActionItems(
                //       loyaltyStampsActionItems.map((actionItem) => {
                //         if (actionItem.stampId === item.stampId) {
                //           return {
                //             ...actionItem,
                //             noOfStamp: temp,
                //           };
                //         } else {
                //           return actionItem;
                //         }
                //       }),
                //     );
                //   }
                // }}
                onChangeText={(text) => {
                  setLoyaltyStampsActionItems(
                    loyaltyStampsActionItems.map((actionItem) => {
                      if (actionItem.stampId === item.stampId) {
                        return {
                          ...actionItem,
                          noOfStamp: parseValidIntegerText(text),
                        };
                      } else {
                        return actionItem;
                      }
                    }),
                  );
                }}
                value={item.noOfStamp}
                // autoFocus={editQuantity}
                keyboardType={'decimal-pad'}
                // keyboardType={'default'}
                // placeholder="0"
                underlineColorAndroid={Colors.fieldtBgColor}
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                var quantityParsed = parseInt(item.noOfStamp) + 1;
                if (quantityParsed > parseInt(item.totalNumberOfStamp)) {
                  quantityParsed = parseInt(item.totalNumberOfStamp);
                }

                setLoyaltyStampsActionItems(
                  loyaltyStampsActionItems.map((actionItem) => {
                    if (actionItem.stampId === item.stampId) {
                      return {
                        ...actionItem,
                        noOfStamp: quantityParsed.toFixed(0),
                      };
                    } else {
                      return actionItem;
                    }
                  }),
                );
              }}>
              <View
                style={[
                  styles.addBtn,
                  {
                    backgroundColor: Colors.primaryColor,
                    left: -1,
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowWidth * 0.03,
                    }
                    : {},
                ]}>
                <Text
                  style={{
                    fontSize: 15,
                    fontWeight: '500',
                    color: Colors.whiteColor,
                  }}>
                  +
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            setLoyaltyStampsActionItems([
              ...loyaltyStampsActionItems.slice(0, index),
              ...loyaltyStampsActionItems.slice(index + 1),
            ]);
          }}
          style={{
            //marginTop: 20,
            marginLeft: 25,
          }}>
          {switchMerchant ? (
            <FontAwesome name="trash-o" size={20} color={Colors.tabRed} />
          ) : (
            <FontAwesome name="trash-o" size={25} color={Colors.tabRed} />
          )}
        </TouchableOpacity>
      </View>
    );
  };

  const dummydata2 = [
    {
      name: 'RM50',
      amount: 1,
    },
    {
      name: 'RM200',
      amount: 2,
    },
  ];

  const dummydatalist2 = [
    {
      name: 'RM10',
      amount: 1,
    },
    {
      name: 'RM20',
      amount: 1,
    },
    {
      name: 'RM50',
      amount: 1,
    },
    {
      name: 'RM100',
      amount: 1,
    },
    {
      name: 'RM150',
      amount: 1,
    },
    {
      name: 'RM200',
      amount: 1,
    },
    {
      name: 'RM250',
      amount: 1,
    },
    {
      name: 'RM300',
      amount: 1,
    },
    {
      name: 'RM500',
      amount: 1,
    },
    {
      name: 'RM1000',
      amount: 1,
    },
  ];

  const renderTopupAmount = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          addToTopupCreditTypeActionItems(item);
        }}>
        <View
          style={{
            //backgroundColor: '#717378',
            backgroundColor: '#fafafa',

            width: windowWidth * 0.1,
            height: windowHeight * 0.25,

            //height: switchMerchant ? windowWidth * 0.19 : '50%',
            borderRadius: 10,
            //marginLeft: 30,
            //marginTop: 10,
            margin: 5,
            zIndex: switchMerchant ? -1000 : -1,
            alignSelf: 'center',
            justifyContent: 'flex-start',
            alignContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            flex: 1,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,

            borderColor: Colors.secondaryColor,
            borderWidth: 0.5,
          }}>
          {item.image ? (
            <>
              <AsyncImage
                style={{
                  justifyContent: 'center',
                  width: windowWidth * 0.07,
                  height: windowWidth * 0.07,
                  alignitem: 'center',
                  alignSelf: 'center',
                  top: 15,
                }}
                resizeMode="cover"
                source={{ uri: item.image }}
                item={item}
              />
            </>
          ) : (
            <View
              style={{
                //backgroundColor: 'red',
                justifyContent: 'center',
                width: windowWidth * 0.07,
                height: windowWidth * 0.07,
                alignitem: 'center',
                alignSelf: 'center',
              }}>
              <Icon
                name="fast-food-outline"
                size={70}
                style={{ alignSelf: 'center', marginBottom: '-26%' }}
              />
            </View>
          )}
          <View
            style={{
              height: switchMerchant ? windowWidth * 0.07 : windowWidth * 0.065,
              alignItems: 'center',
              justifyContent: 'center',
              //alignSelf: 'flex-start',
              //flex: 0.24,
              padding: switchMerchant ? 5 : 5,
              paddingHorizontal: 8,
              marginTop: 20,
              //alignItems: 'center'
            }}>
            {
              item.type === 'text-input'
                ?
                <TextInput
                  style={[
                    {
                      fontSize: 15,
                      fontWeight: 'bold',
                      color: Colors.primaryColor,
                      ...(Platform.OS === 'ios' && {
                        paddingTop: 5,
                        paddingBottom: 5,
                      }),
                    },
                  ]}
                  //iOS
                  placeholder={'0'}
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  clearTextOnFocus
                  //////////////////////////////////////////////
                  //Android
                  onFocus={() => {
                  }}
                  ///////////////////////////////////////////////
                  //When textinput is not selected
                  onEndEditing={() => {
                    // if (item.noOfStamp == '') {
                    // }
                  }}
                  onChangeText={(text) => {
                  }}
                  // value={item.noOfStamp}
                  // autoFocus={editQuantity}
                  keyboardType={'decimal-pad'}
                  // keyboardType={'default'}
                  // placeholder="0"
                  underlineColorAndroid={Colors.fieldtBgColor}
                />
                :
                <Text
                  style={{
                    color: Colors.blackColor,
                    //alignSelf: 'center',
                    //padding: 10,
                    //position: 'absolute',
                    //justifyContent: 'center',
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                    bottom: 7,
                    textAlign: 'center',
                  }}
                  numberOfLines={switchMerchant ? (windowWidth <= 800 ? 2 : 2) : 2}>
                  {item.name}
                </Text>
            }
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSelectedTopupAmount = ({ item, index }) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          alignItems: 'center',
          paddingHorizontal: 10,
          borderRadius: 5,
          backgroundColor: Colors.fieldtBgColor,
          marginBottom: 10,
        }}>
        <Text
          style={{
            fontSize: switchMerchant ? 10 : 16,
            fontFamily: 'NunitoSans-Regular',
            width: '65%',
            paddingLeft: 20,
          }}>
          {item.itemName || 'N/A'}
          {`\nCredit: ${item.creditPointSingle}`}
        </Text>
        <View style={{ justifyContent: 'center', alignSelf: 'center' }}>
          <View
            style={{
              flexDirection: 'row',
              // paddingTop: 10
            }}>
            <TouchableOpacity
              onPress={() => {
                var quantityParsed = parseInt(item.quantity) - 1;
                if (quantityParsed < 1) {
                  quantityParsed = 1;
                }

                setTopupCreditTypeActionItems(
                  topupCreditTypeActionItems.map((actionItem) => {
                    if (actionItem.itemId === item.itemId) {
                      return {
                        ...actionItem,
                        quantity: quantityParsed.toFixed(0),
                        price: quantityParsed * actionItem.priceSingle,
                        priceOriginal: quantityParsed * actionItem.priceSingle,
                        creditPoint:
                          quantityParsed * actionItem.creditPointSingle,
                      };
                    } else {
                      return actionItem;
                    }
                  }),
                );
              }}>
              <View
                style={[
                  styles.addBtn,
                  { backgroundColor: Colors.descriptionColor },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowWidth * 0.03,
                    }
                    : {},
                ]}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 15,
                    fontWeight: '500',
                    color: Colors.whiteColor,
                  }}>
                  -
                </Text>
              </View>
            </TouchableOpacity>
            <View
              style={[
                styles.addBtn,
                {
                  backgroundColor: Colors.whiteColor,
                  borderWidth: StyleSheet.hairlineWidth,
                  borderColor: Colors.descriptionColor,
                  borderWidth: 1.5,

                  width: 30,

                  marginLeft: -1,
                },
                switchMerchant
                  ? {
                    width: windowWidth * 0.03,
                    height: windowWidth * 0.03,
                  }
                  : {},
              ]}>
              <TextInput
                style={[
                  {
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: Colors.primaryColor,
                    paddingTop: 5,
                    paddingBottom: 5,
                    ...(Platform.OS === 'ios' && {
                      paddingTop: 5,
                      paddingBottom: 5,
                    }),
                  },
                ]}
                //iOS
                placeholder={'0'}
                placeholderTextColor={Platform.select({
                  ios: '#a9a9a9',
                })}
                // clearTextOnFocus
                selectTextOnFocus
                //////////////////////////////////////////////
                //Android
                // onFocus={() => {
                //   setTemp(item.quantity);
                //   setTopupCreditTypeActionItems(
                //     topupCreditTypeActionItems.map((actionItem) => {
                //       if (actionItem.itemId === item.itemId) {
                //         return {
                //           ...actionItem,
                //           quantity: '',
                //         };
                //       } else {
                //         return actionItem;
                //       }
                //     }),
                //   );
                // }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                // onEndEditing={() => {
                //   if (item.quantity == '') {
                //     setTopupCreditTypeActionItems(
                //       topupCreditTypeActionItems.map((actionItem) => {
                //         if (actionItem.itemId === item.itemId) {
                //           return {
                //             ...actionItem,
                //             quantity: temp,
                //           };
                //         } else {
                //           return actionItem;
                //         }
                //       }),
                //     );
                //   }
                // }}
                onChangeText={(text) => {
                  setTopupCreditTypeActionItems(
                    topupCreditTypeActionItems.map((actionItem) => {
                      var quantityParsed = parseInt(text);
                      if (isNaN(quantityParsed)) {
                        quantityParsed = 0;
                      }

                      if (actionItem.itemId === item.itemId) {
                        return {
                          ...actionItem,
                          quantity: parseValidIntegerText(text),
                          price: quantityParsed * actionItem.priceSingle,
                          priceOriginal:
                            quantityParsed * actionItem.priceSingle,
                          creditPoint:
                            quantityParsed * actionItem.creditPointSingle,
                        };
                      } else {
                        return actionItem;
                      }
                    }),
                  );
                }}
                value={item.quantity}
                // autoFocus={editQuantity}
                keyboardType={'decimal-pad'}
                // keyboardType={'default'}
                // placeholder="0"
                underlineColorAndroid={Colors.fieldtBgColor}
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                var quantityParsed = parseInt(item.quantity) + 1;
                // if (quantityParsed > parseInt(item.totalNumberOfStamp)) {
                //   quantityParsed = parseInt(item.totalNumberOfStamp);
                // }

                setTopupCreditTypeActionItems(
                  topupCreditTypeActionItems.map((actionItem) => {
                    if (actionItem.itemId === item.itemId) {
                      return {
                        ...actionItem,
                        quantity: quantityParsed.toFixed(0),
                        price: quantityParsed * actionItem.priceSingle,
                        priceOriginal: quantityParsed * actionItem.priceSingle,
                        creditPoint:
                          quantityParsed * actionItem.creditPointSingle,
                      };
                    } else {
                      return actionItem;
                    }
                  }),
                );
              }}>
              <View
                style={[
                  styles.addBtn,
                  {
                    backgroundColor: Colors.primaryColor,
                    left: -1,
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowWidth * 0.03,
                    }
                    : {},
                ]}>
                <Text
                  style={{
                    fontSize: 15,
                    fontWeight: '500',
                    color: Colors.whiteColor,
                  }}>
                  +
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            setTopupCreditTypeActionItems([
              ...topupCreditTypeActionItems.slice(0, index),
              ...topupCreditTypeActionItems.slice(index + 1),
            ]);
          }}
          style={{
            //marginTop: 20,
            marginLeft: 20,
          }}>
          {switchMerchant ? (
            <FontAwesome name="trash-o" size={20} color={Colors.tabRed} />
          ) : (
            <FontAwesome name="trash-o" size={25} color={Colors.tabRed} />
          )}
        </TouchableOpacity>
      </View>
    );
  };

  const renderLoyaltyCampaign = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          // setShowLoyaltyAutomatedCampaign(true);
          // setIsRem1(false);
          // setIsRem2(false);
          // setIsRem3(false);
          // setIs1st(false);
          // setIsRisk(true);
          // setIsLapsed(false);
          // setIsLost(false);
          // setIsBirthday(false);
          // setIsGrowth(false);
          // setIsSpender(false);
          // setIsSignup(false);

          // CommonStore.update((s) => {
          //   s.selectedLoyaltyCampaignEdit = item;
          // });

          // props.navigation.navigate('NewLoyaltyCampaign');

          // console.log(item);

          Alert.alert(
            `${item.campaignName}`,
            `Are you sure you want to redeem this voucher?`,
            [
              {
                text: 'YES',
                onPress: () => {
                  redeemUserTaggableVoucherByMerchant(item);
                },
              },
              { text: 'NO', onPress: () => { }, style: 'cancel' },
            ],
            { cancelable: false },
          );
        }}
        style={{
          paddingHorizontal: windowWidth * 0.01,
          paddingVertical: windowHeight * 0.02,
          paddingTop: windowHeight * 0.01,
          borderBottomColor: '#EBEDEF',
          borderBottomWidth: 1,
          width: '100%',
        }}>
        <View
          style={{
            // borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                // color: switch1stVisit ? Colors.primaryColor : Colors.tabRed,
                color: Colors.primaryColor,
              }}>
              <Text>{item.isActive ? 'Active' : 'Inactive'}</Text>
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {item.campaignName}
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                color: '#808B96',
              }}>
              {/* {`Send ${dropRiskVisited} after last visit - ${moment(
                riskTime,
              ).format('hh:mm A')} - ${dropRiskExp} expiration`} */}
              {item.campaignDescription}
            </Text>
          </View>
          {/* <View
            style={{
              flexDirection: 'row',
            }}>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {dropRiskVisited}
            </Text>
            <Plus
              name="chevron-right"
              size={switchMerchant ? 20 : 25}
              color={Colors.darkBgColor}
              style={{ bottom: 1 }}
            />
          </View> */}
        </View>
      </TouchableOpacity>
    );
  };

  const redeemUserTaggableVoucherByMerchant = (taggableVoucher) => {
    const userTaggableVoucher = selectedCustomerUserTaggableVouchers.find(
      (userVoucher) => {
        return (
          userVoucher.voucherId === taggableVoucher.uniqueId &&
          userVoucher.redeemDate === null
        );
      },
    );

    if (userTaggableVoucher) {
      var body = {
        // loyaltyCampaignId: loyaltyCampaign.uniqueId,
        taggableVoucherId: taggableVoucher.uniqueId,

        userTaggableVoucherId: userTaggableVoucher.uniqueId,
      };

      // ApiClient.POST(API.redeemUserTaggableVoucherByMerchant, body, false)
      APILocal.redeemUserTaggableVoucherByMerchant({
        body,
        uid: merchantUserId,
      }).then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Voucher has been redeemed',
            [
              {
                text: 'OK',
                onPress: () => {
                  // setStampModal(false);
                  // //setAmount('');
                  // //setPhoneNumber('+60');
                  // //setCurrCRMUser(null);
                  // setPhoneNumberChecking(false);
                  // setTimeout(() => {
                  //   setStampDoneModal(true);
                  // }, 500);
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          Alert.alert('Info', 'Unable to redeem voucher');

          // setStampModal(false);
          // //setAmount('');
          // //setPhoneNumber('+60');
          // //setCurrCRMUser(null);
          // setPhoneNumberChecking(false);
        }
      });
    } else {
      Alert.alert('Info', 'This voucher has been used');
    }
  };

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Pay & Earn
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {merchantUserName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });
  //////////////////////////////////////////////////////////////////

  // 2025-06-19
  const [voucherModal, setVoucherModal] = useState(false);
  const [selectedVoucher, setSelectedVoucher] = useState(null); //voucher.uniqueId
  const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);

  const activeTaggableVouchers = useMemo(() => {
    return taggableVouchers.filter(item => {
      if (item.outletId !== currOutletId) return false;

      const endTime = moment(item.promoTimeEnd);
      const endDate = moment(item.promoDateEnd).set({
        hour: endTime.get('hour'),
        minute: endTime.get('minute'),
      });

      return !moment().isSameOrAfter(endDate);
    });
  }, [taggableVouchers]);

  useEffect(() => {
    setSelectedVoucher(null);
  }, [voucherModal]);

  const renderVouchers = (item) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          padding: 10,
          borderWidth: selectedVoucher && selectedVoucher === item.uniqueId ? 3 : 1,
          borderColor: selectedVoucher && selectedVoucher === item.uniqueId ? Colors.primaryColor : Colors.fieldtTxtColor,
          borderRadius: 10,
        }}
        onPress={() => {
          setSelectedVoucher(item.uniqueId);
        }}
      >
        <View style={{ width: 'auto' }}>
          {item.image ? (
            <AsyncImage
              source={{ uri: item.image }}
              hideLoading
              style={{
                width: 65,
                height: 65,
                borderRadius: 4,
                backgroundColor: Colors.secondaryColor,
              }}
            />
          ) : (
            <View style={{
              width: 65,
              height: 65,
              borderRadius: 4,
              backgroundColor: Colors.secondaryColor,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <FontAwesome5
                name="image"
                size={35}
                color={Colors.blackColor}
              />
            </View>
          )}
        </View>

        <View style={{
          flex: 1,
          marginLeft: 10,
          justifyContent: 'center',
          alignItems: 'flex-start'
        }}>
          <Text style={{
            fontSize: 16,
            fontFamily: 'NunitoSans-Regular',
            fontWeight: '600',
            color: 'black',
            marginBottom: 5
          }}>
            {item.campaignName}
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <FontAwesome5
              name="calendar-alt"
              size={12}
              color={Colors.fieldtTxtColor}
              style={{ marginRight: 5 }}
            />
            <Text style={{
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              color: Colors.fieldtTxtColor,
              fontVariant: ['tabular-nums']
            }}>
              {`${moment(item.promoDateStart).format('DD MMM')} - ${moment(item.promoDateEnd).format('DD MMM YYYY')}`}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const sendVoucherToCustomer = async () => {
    if (!selectedVoucher) {
      Alert.alert('Error', 'Please select a voucher before proceed');
      return;
    }

    try {
      const body = {
        userPhone: phoneNumber.startsWith('+6') ? phoneNumber.substring(2) : phoneNumber,
        outletId: currOutletId,
        merchantId: merchantId,
        merchantName: merchantName,
        outletName: currOutlet.name,
        merchantLogo: merchantLogo,
        outletCover: currOutlet.cover,
        userName: userName,

        dob: startDate,

        address: '',
        lat: 0,
        lng: 0,

        taggableVoucherId: selectedVoucher,

        isVerified: true,

        userIdAnonymous: "",
        toConvertAnonymousPoints: false,

        //////////////////////

        rating: 5,
        review: 'A very good enjoyable experience.',
        toUpdateGR: false,
        outletEmail: currOutlet.email,

        //////////////////////

        loyaltyCampaignId: "",
        batchId: "",
        batchIndex: "",

        //////////////////////
      };

      const response = await ApiClient.POST(API.claimTaggableVoucherKweb, body);

      if (response.status === 'success') {
        Alert.alert('Success', 'Voucher sent to customer');
      }
      else {
        Alert.alert('Error', 'Failed to send voucher to customer');
      }
    }
    catch (error) {
      console.log('Send Voucher Error', error);
      Alert.alert('Error', 'Failed to send voucher to customer');
    }
    finally {
      setVoucherModal(false);
    }
  };

  //////////////////////////////////////////////////////////////////

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? {} : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar navigation={props.navigation} selectedTab={9} />
        </View> */}

        <DateTimePickerModal
          isVisible={showStartDatePicker}
          supportedOrientations={['portrait', 'landscape']}
          mode={'date'}
          onConfirm={(text) => {
            setStartDate(moment(text).startOf('day').valueOf());
            setShowStartDatePicker(false);
          }}
          onCancel={() => {
            setShowStartDatePicker(false);
          }}
        />
        <View>
          <View
            style={{
              alignItems: 'flex-end',
              paddingVertical: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 3 : 6,
              paddingRight: 30,
            }}
          />
          <View
            style={{
              backgroundColor: Colors.whiteColor,
              width: switchMerchant ? windowWidth * 0.8 : windowWidth * 0.87,
              height: windowHeight * 0.8,
              // marginTop: 30,
              marginHorizontal: switchMerchant ? 25 : 30,
              marginBottom: switchMerchant ? 0 : 30,
              alignSelf: 'center',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 5,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>
            {/* ///////////// Stamps /////////////// */}

            {creditSection === CREDIT_SECTION.STAMPS ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 5 : 10,
                      paddingTop: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        textAlign: 'center',
                      }}>
                      Stamp Collection
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 18,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 10,
                        textAlign: 'center',
                      }}>
                      powered by KooDoo
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }} />
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-1"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Your Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-2"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Select Your Stamp Type
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-3"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Collect Your Stamp
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                      },
                    ]} />
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                        paddingHorizontal: 20,
                        gap: 10,
                      },
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.primaryColor,
                          borderWidth:
                            creditSection == 'TOPUP_CREDIT' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'TOPUP_CREDIT'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="credit-card-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, marginTop: 15 }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Top Up Credit
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          borderWidth: creditSection == 'POINTS' ? 3.5 : 0,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'POINTS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.POINTS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="star-four-points"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Points
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabGold,
                          borderWidth: creditSection == 'STAMPS' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'STAMPS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.STAMPS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <FontAwesome5
                          name="stamp"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Stamps
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabPurple ? Colors.tabPurple : '#8e44ad',
                          borderWidth: creditSection == 'VOUCHER' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'VOUCHER'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.VOUCHER);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="ticket-percent"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Voucher
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                    paddingBottom: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 0,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {

                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                          if (currCRMUser) {
                            setStampModal(true);
                            setUserName(currCRMUser ? currCRMUser.name : '');
                          } else {
                            setRegisterModal(true);
                            setRegisterSection(REGISTER_SECTION.STAMPS);
                          }
                        }
                        else {
                          Alert.alert('Info', 'Shift was closed')
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 0,
                        }}>
                        {/*Stamp*/}
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          NEXT
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={stampModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowWidth * 0.85 : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.8,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setStampModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          <View style={{ flexDirection: 'column', flex: 1 }}>
                            <View
                              style={{
                                paddingTop: switchMerchant ? 15 : 20,
                                height: windowHeight * 0.7,
                              }}>
                              <FlatList
                                showsVerticalScrollIndicator={false}
                                // data={dummydatalist}
                                data={loyaltyStamps}
                                // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
                                // extraData={outletItems}
                                renderItem={renderCollectStampItems}
                                numColumns={3}
                                keyExtractor={(item, index) => index}
                                contentContainerStyle={{
                                  paddingLeft: 10,
                                  paddingRight: 10,
                                  paddingTop: 5,
                                  //width: windowWidth * 0.9,
                                  justifyContent: 'center',
                                  paddingBottom: 15,
                                }}
                              />
                            </View>
                          </View>
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              margin: switchMerchant ? 1 : 12,
                              paddingTop: 50,
                              height: windowHeight * 0.75,
                            }}>
                            {loyaltyStampsActionItems[0] == null ? (
                              <View
                                style={{
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowHeight * 0.596 : windowHeight * 0.626,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  No items have been selected
                                </Text>
                              </View>
                            ) : (
                              <FlatList
                                // data={dummydata}
                                data={loyaltyStampsActionItems}
                                // extraData={selectedCustomerLCCTransactions}
                                renderItem={renderCollectStampItemList}
                                keyExtractor={(item, index) => index}
                                contentContainerStyle={
                                  {
                                    // paddingLeft: 10,
                                    // paddingTop: 20,
                                    // width: windowWidth * 0.88,
                                    // backgroundColor: 'red'
                                  }
                                }
                              />
                            )}
                            <View
                              style={{
                                width: '100%',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // setStampModal(false);

                                  if (loyaltyStampsActionItems.length > 0) {
                                    earnLoyaltyStamp();
                                  } else {
                                    Alert.alert(
                                      'Please add at least one stamp to proceed.',
                                    );
                                  }

                                  // setStampDoneModal(true); // show this after success
                                }}
                                disabled={phoneNumberChecking}
                                style={{
                                  width: switchMerchant ? '50%' : '45%',
                                }}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    COLLECT
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={editAmountPopup}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: 200,
                        height: 120,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <View
                        style={{ justifyContent: 'center', alignSelf: 'center' }}>
                        <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                          <TouchableOpacity onPress={() => { }}>
                            <View
                              style={[
                                styles.addBtn,
                                { backgroundColor: Colors.descriptionColor },
                                switchMerchant
                                  ? {
                                    width: windowWidth * 0.03,
                                    height: windowWidth * 0.03,
                                  }
                                  : {},
                              ]}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 30,
                                  fontWeight: '500',
                                  color: Colors.whiteColor,
                                }}>
                                -
                              </Text>
                            </View>
                          </TouchableOpacity>
                          <View
                            style={[
                              styles.addBtn,
                              {
                                backgroundColor: Colors.whiteColor,
                                borderWidth: StyleSheet.hairlineWidth,
                                borderColor: Colors.descriptionColor,
                                borderWidth: 1.5,

                                width: 50,

                                marginLeft: -1,
                              },
                              switchMerchant
                                ? {
                                  width: windowWidth * 0.03,
                                  height: windowWidth * 0.03,
                                }
                                : {},
                            ]}>
                            <TextInput
                              style={[
                                {
                                  fontSize: switchMerchant ? 10 : 25,
                                  fontWeight: 'bold',
                                  color: Colors.primaryColor,
                                  paddingTop: 5,
                                  paddingBottom: 5,
                                },
                              ]}
                              //iOS
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => { }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => { }}
                              onChangeText={(text) => { }}
                              value={dummydata}
                              // autoFocus={editQuantity}
                              keyboardType={'decimal-pad'}
                              // keyboardType={'default'}
                              // placeholder="0"
                              underlineColorAndroid={Colors.fieldtBgColor}
                            />
                          </View>
                          <TouchableOpacity onPress={() => { }}>
                            <View
                              style={[
                                styles.addBtn,
                                {
                                  backgroundColor: Colors.primaryColor,
                                  left: -1,
                                },
                                switchMerchant
                                  ? {
                                    width: windowWidth * 0.03,
                                    height: windowWidth * 0.03,
                                  }
                                  : {},
                              ]}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 30,
                                  fontWeight: '500',
                                  color: Colors.whiteColor,
                                }}>
                                +
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View
                        style={{
                          flex: 1,
                          flexDirection: 'row',
                          alignItems: 'center',
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            setEditAmountPopup(false);
                          }}>
                          <View
                            style={{
                              borderRadius: 10,
                              backgroundColor: phoneNumberChecking
                                ? 'grey'
                                : Colors.primaryColor,
                              paddingVertical: switchMerchant ? 4 : 10,
                              paddingHorizontal: switchMerchant ? 15 : 25,
                              marginBottom: switchMerchant ? 5 : 0,
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Confirm
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={stampDoneModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.75,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <ScrollView
                        scrollEnabled
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setStampDoneModal(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            You have successfully collected your stamps
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            Start spending to get more rewards and perks!
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Stamp Types
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Stamps Collected
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {loyaltyStampsActionItems.length}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {/* RM {amount * 0.01} */}
                            {loyaltyStampsActionItems.reduce(
                              (accum, actionItem) =>
                                accum + parseInt(actionItem.noOfStamp),
                              0,
                            )}
                          </Text>
                        </View>
                        {/* <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View> */}
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }} />
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }} />
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setStampDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.STAMPS);

                              setLoyaltyStampsActionItems([]);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                DONE
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setStampDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.STAMPS);
                              setLoyaltyStampsActionItems([]);

                              setTimeout(() => {
                                setStampModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Collect more stamps?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setStampDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                              setTopupCreditTypeActionItems([]);

                              setTimeout(() => {
                                setTopupCreditModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Top up credits?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setStampDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.POINTS);

                              setTimeout(() => {
                                setPointsModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to collect points?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}
            {/* ///////////// Points /////////////// */}

            {creditSection === CREDIT_SECTION.POINTS ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 5 : 10,
                      paddingTop: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        textAlign: 'center',
                      }}>
                      Point Collection
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 18,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 10,
                        textAlign: 'center',
                      }}>
                      powered by KooDoo
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }} />
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-1"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Your Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-2"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Enter Amount Spent
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-3"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Earn Your Points
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                      },
                    ]} />
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                        paddingHorizontal: 20,
                        gap: 10,
                      },
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.primaryColor,
                          borderWidth:
                            creditSection == 'TOPUP_CREDIT' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'TOPUP_CREDIT'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="credit-card-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, marginTop: 15 }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Top Up Credit
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          borderWidth: creditSection == 'POINTS' ? 3.5 : 0,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'POINTS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.POINTS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="star-four-points"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Points
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabGold,
                          borderWidth: creditSection == 'STAMPS' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'STAMPS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.STAMPS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <FontAwesome5
                          name="stamp"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Stamps
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabPurple ? Colors.tabPurple : '#8e44ad',
                          borderWidth: creditSection == 'VOUCHER' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'VOUCHER'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.VOUCHER);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="ticket-percent"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Voucher
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                    paddingBottom: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 0,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {

                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                          if (currCRMUser) {
                            setPointsModal(true);
                            setUserName(currCRMUser ? currCRMUser.name : '');
                          } else {
                            setRegisterModal(true);
                            setRegisterSection(REGISTER_SECTION.POINTS);
                          }
                        }
                        else {
                          Alert.alert('Info', 'Shift was closed')
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 0,
                        }}>
                        {/*Points*/}
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          NEXT
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={pointsModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.2
                          : windowWidth * 0.4,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.9,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setPointsModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 15 : 20,
                              fontWeight: 'bold',
                              margin: switchMerchant ? 1 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 12,
                              paddingTop: 20,
                            }}>
                            Enter Amount Spent
                          </Text>
                          <View
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              alignItems: 'center',
                              alignSelf: 'center',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 13 : 20,
                                color: Colors.fieldtTxtColor,
                              }}>
                              RM
                            </Text>
                          </View>
                          <View style={{ justifyContent: 'center' }}>
                            <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                              {amount.length === 0 ? '0' : amount}
                            </Text>
                          </View>
                        </View>
                        <View>
                          <View
                            style={{
                              flexDirection: 'row',
                              flexWrap: 'wrap',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              alignSelf: 'center',
                              width: '55%',
                              paddingTop: switchMerchant ? 5 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 30,
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                alignItems: 'center',
                                width: '100%',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(1);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    1
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(2);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    2
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(3);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    3
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                alignItems: 'center',
                                width: '100%',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(4);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    4
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(5);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    5
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(6);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    6
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                alignItems: 'center',
                                width: '100%',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(7);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    7
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(8);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    8
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(9);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    9
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                alignItems: 'center',
                                width: '100%',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  if (amount.includes('.')) {

                                  } else {
                                    onNumPadBtn('.');
                                  }
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    .
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(0);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.pinNo,
                                      { fontSize: switchMerchant ? 10 : 20 },
                                    ]}>
                                    0
                                  </Text>
                                </View>
                              </TouchableOpacity>

                              <TouchableOpacity
                                onPress={() => {
                                  onNumPadBtn(-1);
                                }}>
                                <View
                                  style={[
                                    styles.pinBtn,
                                    switchMerchant
                                      ? {
                                        width: switchMerchant ? 35 : 70,
                                        height: switchMerchant ? 35 : 70,
                                        marginBottom: 7,
                                      }
                                      : {
                                        width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                        height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                      },
                                  ]}>
                                  <Feather
                                    name="chevron-left"
                                    size={switchMerchant ? 13 : 30}
                                    color={'black'}
                                    style={{}}
                                  />
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>

                          <View
                            style={{
                              width: '100%',
                              alignItems: 'center',
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                // setPointsModal(false);
                                // setPointsDoneModal(true);

                                earnPointsByAmountSpent();
                              }}
                              //disabled={phoneNumberChecking}
                              style={{
                                width: switchMerchant ? '50%' : '45%',
                              }}>
                              <View
                                style={{
                                  borderRadius: 10,
                                  backgroundColor: phoneNumberChecking
                                    ? 'grey'
                                    : Colors.primaryColor,
                                  paddingVertical: switchMerchant ? 5 : 10,
                                  paddingHorizontal: 25,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  marginTop: 20,
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  EARN
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={pointsDoneModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.75,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <ScrollView
                        scrollEnabled
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setPointsDoneModal(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            You have successfully earned your points
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            Start spending to get more rewards and perks!
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>

                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Points Earned
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>

                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {/* RM {amount * 0.01} */}
                            {earnedPoints.toFixed(0)}
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }} />
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }} />
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setPointsDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.STAMPS);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setPointsDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.STAMPS);
                              // setStampModal(true);
                              setLoyaltyStampsActionItems([]);

                              setTimeout(() => {
                                setStampModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Collect more stamps?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setPointsDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                              setTopupCreditTypeActionItems([]);

                              setTimeout(() => {
                                setTopupCreditModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Top up credits?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setPointsDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.POINTS);

                              setTimeout(() => {
                                setPointsModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to collect points?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}

            {/* ///////////// Top Up Credit /////////////// */}

            {creditSection === CREDIT_SECTION.TOPUP_CREDIT ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 5 : 10,
                      paddingTop: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        textAlign: 'center',
                      }}>
                      Top Up Credit
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 18,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 10,
                        textAlign: 'center',
                      }}>
                      powered by KooDoo
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }} />
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-1"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Guest Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-2"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Select Your Top Up Credit Amount
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-3"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Pay
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                      },
                    ]} />
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                        paddingHorizontal: 20,
                        gap: 10,
                      },
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.primaryColor,
                          borderWidth:
                            creditSection == 'TOPUP_CREDIT' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'TOPUP_CREDIT'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="credit-card-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, marginTop: 15 }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Top Up Credit
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          borderWidth: creditSection == 'POINTS' ? 3.5 : 0,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'POINTS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.POINTS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="star-four-points"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Points
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabGold,
                          borderWidth: creditSection == 'STAMPS' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'STAMPS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.STAMPS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <FontAwesome5
                          name="stamp"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Stamps
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabPurple ? Colors.tabPurple : '#8e44ad',
                          borderWidth: creditSection == 'VOUCHER' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'VOUCHER'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.VOUCHER);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="ticket-percent"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Voucher
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                    paddingBottom: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 0,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {

                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                          if (currCRMUser) {
                            setTopupCreditModal(true);
                            setUserName(currCRMUser ? currCRMUser.name : '');
                          } else {
                            setRegisterModal(true);
                            setRegisterSection(REGISTER_SECTION.TOPUP_CREDIT);
                          }
                        }
                        else {
                          Alert.alert('Info', 'Shift was closed')
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 0,
                        }}>
                        {/*Top Up*/}
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          NEXT
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                <ModalView
                  visible={topupCreditModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.77
                          : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowWidth * 0.85 : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.8,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setTopupCreditModal(false);
                          setAmount('');
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                      <View style={{ flexDirection: 'row', flex: 1 }}>
                        <View style={{ width: '50%', padding: 30 }}>
                          <View style={{ flexDirection: 'column', flex: 1 }}>
                            <View
                              style={{
                                paddingTop: switchMerchant ? 15 : 20,
                                height: windowHeight * 0.7,
                              }}>
                              <FlatList
                                showsVerticalScrollIndicator={false}
                                // data={dummydatalist2}
                                // data={outletItems.filter(item => item.categoryId === selectedOutletItemCategory.uniqueId)}
                                // extraData={outletItems}
                                data={topupCreditTypes
                                  // .concat({
                                  //   image: '',
                                  //   name: 'text-input',
                                  //   type: 'text-input',
                                  // })
                                }
                                renderItem={renderTopupAmount}
                                numColumns={3}
                                keyExtractor={(item, index) => index}
                                contentContainerStyle={{
                                  paddingLeft: 10,
                                  paddingRight: 10,
                                  paddingTop: 5,
                                  //width: windowWidth * 0.9,
                                  justifyContent: 'center',
                                  paddingBottom: 15,
                                }}
                              />
                            </View>
                          </View>
                        </View>

                        <View style={{ width: '50%' }}>
                          <View
                            style={{
                              margin: switchMerchant ? 1 : 12,
                              paddingTop: 50,
                              height: windowHeight * 0.75,
                            }}>
                            {topupCreditTypeActionItems[0] == null ? (
                              <View
                                style={{
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowHeight * 0.596 : windowHeight * 0.626,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  No items have been selected
                                </Text>
                              </View>
                            ) : (
                              <FlatList
                                // data={dummydata2}
                                data={topupCreditTypeActionItems}
                                // extraData={selectedCustomerLCCTransactions}
                                renderItem={renderSelectedTopupAmount}
                                keyExtractor={(item, index) => index}
                                contentContainerStyle={
                                  {
                                    // paddingLeft: 10,
                                    // paddingTop: 20,
                                    // width: windowWidth * 0.88,
                                    // backgroundColor: 'red'
                                  }
                                }
                              />
                            )}
                            <View
                              style={{
                                width: '100%',
                                alignItems: 'center',
                                alignSelf: 'center',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // should go to payment screen first, then only execute this api to topup the credit

                                  if (topupCreditTypeActionItems.length > 0) {
                                    // earnTopupCredit();

                                    if (selectedCustomerEdit) {
                                      setTopupCreditModal(false);

                                      navigation.navigate('LoyaltyTable');

                                      CommonStore.update((s) => {
                                        s.currPage = 'LoyaltyTable';
                                        s.currPageStack = [
                                          ...currPageStack,
                                          'LoyaltyTable',
                                        ];

                                        const orderId = uuidv4();

                                        s.topupCreditTypeOrder = {
                                          cartItems:
                                            topupCreditTypeActionItems.map(
                                              (cartItem) => ({
                                                ...cartItem,
                                                userOrderId: orderId,
                                                quantity: parseInt(
                                                  cartItem.quantity,
                                                ),
                                              }),
                                            ),
                                          uniqueId: orderId,
                                          outletId: currOutlet.uniqueId,
                                        };
                                      });
                                    }
                                    else {
                                      Alert.alert(
                                        'Info',
                                        'The new user still under creation process, please hold on first.',
                                      );
                                    }
                                  } else {
                                    Alert.alert(
                                      'Info',
                                      'Please add at least one credit type to proceed.',
                                    );
                                  }
                                }}
                                disabled={phoneNumberChecking}
                                style={{
                                  width: switchMerchant ? '50%' : '45%',
                                }}>
                                <View
                                  style={{
                                    borderRadius: 10,
                                    backgroundColor: phoneNumberChecking
                                      ? 'grey'
                                      : Colors.primaryColor,
                                    paddingVertical: switchMerchant ? 5 : 10,
                                    paddingHorizontal: 25,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    PAY
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={editAmountPopup}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: 200,
                        height: 120,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <View
                        style={{ justifyContent: 'center', alignSelf: 'center' }}>
                        <View style={{ flexDirection: 'row', paddingTop: 10 }}>
                          <TouchableOpacity onPress={() => { }}>
                            <View
                              style={[
                                styles.addBtn,
                                { backgroundColor: Colors.descriptionColor },
                                switchMerchant
                                  ? {
                                    width: windowWidth * 0.03,
                                    height: windowWidth * 0.03,
                                  }
                                  : {},
                              ]}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 30,
                                  fontWeight: '500',
                                  color: Colors.whiteColor,
                                }}>
                                -
                              </Text>
                            </View>
                          </TouchableOpacity>
                          <View
                            style={[
                              styles.addBtn,
                              {
                                backgroundColor: Colors.whiteColor,
                                borderWidth: StyleSheet.hairlineWidth,
                                borderColor: Colors.descriptionColor,
                                borderWidth: 1.5,

                                width: 50,

                                marginLeft: -1,
                              },
                              switchMerchant
                                ? {
                                  width: windowWidth * 0.03,
                                  height: windowWidth * 0.03,
                                }
                                : {},
                            ]}>
                            <TextInput
                              style={[
                                {
                                  fontSize: switchMerchant ? 10 : 25,
                                  fontWeight: 'bold',
                                  color: Colors.primaryColor,
                                  paddingTop: 5,
                                  paddingBottom: 5,
                                },
                              ]}
                              //iOS
                              placeholder={'0'}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              clearTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => { }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => { }}
                              onChangeText={(text) => { }}
                              value={dummydata}
                              // autoFocus={editQuantity}
                              keyboardType={'decimal-pad'}
                              // keyboardType={'default'}
                              // placeholder="0"
                              underlineColorAndroid={Colors.fieldtBgColor}
                            />
                          </View>
                          <TouchableOpacity onPress={() => { }}>
                            <View
                              style={[
                                styles.addBtn,
                                {
                                  backgroundColor: Colors.primaryColor,
                                  left: -1,
                                },
                                switchMerchant
                                  ? {
                                    width: windowWidth * 0.03,
                                    height: windowWidth * 0.03,
                                  }
                                  : {},
                              ]}>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 30,
                                  fontWeight: '500',
                                  color: Colors.whiteColor,
                                }}>
                                +
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View
                        style={{
                          flex: 1,
                          flexDirection: 'row',
                          alignItems: 'center',
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            setEditAmountPopup(false);
                          }}>
                          <View
                            style={{
                              borderRadius: 10,
                              backgroundColor: phoneNumberChecking
                                ? 'grey'
                                : Colors.primaryColor,
                              paddingVertical: switchMerchant ? 4 : 10,
                              paddingHorizontal: switchMerchant ? 15 : 25,
                              marginBottom: switchMerchant ? 5 : 0,
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Confirm
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </ModalView>
                <ModalView
                  visible={topupCreditDoneModal}
                  supportedOrientations={['landscape', 'portrait']}
                  style={{}}
                  animationType={'fade'}
                  transparent>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: Colors.modalBgColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: switchMerchant
                          ? windowWidth * 0.7
                          : windowWidth * 0.77,
                        height: switchMerchant
                          ? windowHeight * 0.85
                          : windowHeight * 0.75,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,

                        ...getTransformForModalInsideNavigation(),
                      }}>
                      <ScrollView
                        scrollEnabled
                        showsVerticalScrollIndicator={false}>
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,

                            elevation: 1000,
                            zIndex: 1000,
                          }}
                          onPress={() => {
                            setTopupCreditDoneModal(false);
                            setAmount('');
                            setAddnotes(false);
                            setNotetext('');
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View
                          style={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            paddingVertical: switchMerchant ? 15 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 24,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            You have successfully topped up your credits
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            Start spending to get more rewards and perks!
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '40%',
                            }}>
                            Phone Number
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Total Amount
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              width: '20%',
                            }}>
                            Status
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            width: switchMerchant ? '80%' : '70%',
                            justifyContent: 'center',
                            paddingTop: switchMerchant ? 5 : 10,
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {currCRMUser ? currCRMUser.name : userName}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '40%',
                            }}>
                            {currCRMUser ? currCRMUser.number : phoneNumber}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            RM1000
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                              width: '20%',
                            }}>
                            {/* RM {amount * 0.01} */}
                            Success
                          </Text>
                        </View>
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 40,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              {
                                addnotes == true
                                  ? setAddnotes(false)
                                  : setAddnotes(true);
                              }
                            }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 10 : 16,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.primaryColor,
                              }}>
                              Add notes
                            </Text>
                          </TouchableOpacity>
                        </View>
                        {addnotes == true ? (
                          <TextInput
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              borderBottomWidth: 0.5,
                              borderBottomColor: 'black',
                              width: '40%',
                              height: switchMerchant ? 35 : 40,
                              alignSelf: 'center',
                              alignItems: 'center',
                              marginVertical: switchMerchant ? 5 : 20,
                            }}
                            placeholder=""
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setNotetext(text);
                            }} />
                        ) : (
                          <View
                            style={{
                              height: switchMerchant ? 35 : 40,
                              marginVertical: switchMerchant ? 5 : 20,
                            }} />
                        )}
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            alignSelf: 'center',
                            paddingTop: switchMerchant ? 10 : 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setTopupCreditDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber('+60');
                              setCreditSection(CREDIT_SECTION.STAMPS);
                            }}>
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: Colors.primaryColor,
                                paddingVertical: switchMerchant ? 5 : 10,
                                paddingHorizontal: 50,
                                marginTop: 20,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Done
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 30,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setStampDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              //setCreditSection(CREDIT_SECTION.STAMPS);
                              // setStampModal(true);
                              setLoyaltyStampsActionItems([]);

                              setTimeout(() => {
                                setStampModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Collect more stamps?
                            </Text>
                          </TouchableOpacity>
                          <Text
                            style={{
                              marginHorizontal: 20,
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            or
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              setTopupCreditDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                              setTopupCreditTypeActionItems([]);

                              setTimeout(() => {
                                setTopupCreditModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Top up credits?
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            paddingTop: 20,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              setTopupCreditDoneModal(false);
                              setAddnotes(false);
                              setNotetext('');
                              setAmount('');
                              setPhoneNumber(`${phoneNumber}`);
                              setCreditSection(CREDIT_SECTION.POINTS);

                              setTimeout(() => {
                                setPointsModal(true);
                              }, 1000);
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 16,
                                color: Colors.primaryColor,
                              }}>
                              Want to collect points?
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </ScrollView>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}

            {/* ///////////// Voucher （bruh why they render for different section） /////////////// */}

            {creditSection === CREDIT_SECTION.VOUCHER ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  backgroundColor: Colors.highlightColor,
                }}>
                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.44
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 5 : 10,
                      paddingTop: 15,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 16 : 24,
                        textAlign: 'center',
                      }}>
                      Send Voucher
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 18,
                        fontWeight: 'bold',
                        paddingVertical: switchMerchant ? 2 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 10,
                        textAlign: 'center',
                      }}>
                      powered by KooDoo
                    </Text>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16 }} />
                  </View>
                  <View
                    style={{
                      paddingLeft: 20,
                      paddingTop: switchMerchant ? 10 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 20,
                      flex: 1,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-1"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Enter Guest Phone Number
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: switchMerchant ? 10 : 20,
                      }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-2"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          paddingVertical: switchMerchant ? 0 : 10,
                        }}>
                        Select Voucher to Sent
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.highlightColor,
                          borderRadius: 25,
                          alignSelf: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: switchMerchant ? 0 : 5,
                          paddingVertical: switchMerchant ? 0 : 5,
                          marginRight: 10,
                        }}>
                        <MaterialCommunityIcons
                          name="roman-numeral-3"
                          size={switchMerchant ? 10 : 35}
                          style={{ color: Colors.primaryColor }}
                        />
                      </View>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
                        Sent Voucher
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                      },
                    ]} />
                  <View
                    style={[
                      {
                        paddingTop: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-evenly',
                        paddingBottom: 30,
                        paddingHorizontal: 20,
                        gap: 10,
                      },
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.primaryColor,
                          borderWidth:
                            creditSection == 'TOPUP_CREDIT' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'TOPUP_CREDIT'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.TOPUP_CREDIT);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="credit-card-plus"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor, marginTop: 15 }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Top Up Credit
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabCyan,
                          borderRadius: 10,
                          borderWidth: creditSection == 'POINTS' ? 3.5 : 0,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'POINTS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.POINTS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="star-four-points"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Points
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabGold,
                          borderWidth: creditSection == 'STAMPS' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'STAMPS'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.STAMPS);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <FontAwesome5
                          name="stamp"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Stamps
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          aspectRatio: 0.8,
                          backgroundColor: Colors.tabPurple ? Colors.tabPurple : '#8e44ad',
                          borderWidth: creditSection == 'VOUCHER' ? 3.5 : 0,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 10,
                          paddingVertical: 10,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      disabled={creditSection === 'VOUCHER'}
                      onPress={() => {
                        setCreditSection(CREDIT_SECTION.VOUCHER);
                      }}>
                      <View style={{ alignItems: 'center' }}>
                        <MaterialCommunityIcons
                          name="ticket-percent"
                          size={switchMerchant ? 30 : 45}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={[
                            {
                              fontWeight: 'bold',
                              color: Colors.whiteColor,
                              fontSize: 15,
                              textAlign: 'center',
                              paddingBottom: 5,
                              paddingVertical: 10,
                            },
                          ]}>
                          Voucher
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>

                <View
                  style={{
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.42
                        : windowWidth * 0.43,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,
                    paddingBottom: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 0,
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      flex: 1,
                      paddingTop: Platform.OS === 'ios' ? 0 : 10,
                      marginTop: Platform.OS === 'ios' ? 10 : 0,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 25,
                        fontWeight: 'bold',
                      }}>
                      Enter Your Phone Number
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                      {phoneNumber}
                    </Text>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        alignSelf: 'center',
                        width: Platform.OS === 'ios' ? 270 : '45%',
                        paddingTop: switchMerchant ? 10 : 30,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(1);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              1
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(2);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              2
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(3);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              3
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(4);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              4
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(5);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              5
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(6);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              6
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(7);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              7
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(8);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              8
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(9);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              9
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignSelf: 'center',
                          alignItems: 'center',
                          width: '100%',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber.includes('+')) {

                            }
                            else {
                              PhoneonNumPadBtn('+');
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              +
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            PhoneonNumPadBtn(0);
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Text
                              style={[
                                styles.pinNo,
                                { fontSize: switchMerchant ? 10 : 20 },
                              ]}>
                              0
                            </Text>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          onPress={() => {
                            if (phoneNumber != '+6') {
                              PhoneonNumPadBtn(-1);
                            }
                          }}>
                          <View
                            style={[
                              styles.pinBtn,
                              switchMerchant
                                ? {
                                  width: switchMerchant ? 35 : 70,
                                  height: switchMerchant ? 35 : 70,
                                  marginBottom: 7,
                                }
                                : {
                                  width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                  height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                },
                            ]}>
                            <Feather
                              name="chevron-left"
                              size={switchMerchant ? 13 : 30}
                              color={'black'}
                              style={{}}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                          if (currCRMUser) {
                            setVoucherModal(true);
                            setUserName(currCRMUser ? currCRMUser.name : '');
                          } else {
                            setRegisterModal(true);
                            setRegisterSection(REGISTER_SECTION.TOPUP_CREDIT);
                          }
                        }
                        else {
                          Alert.alert('Info', 'Shift was closed')
                        }
                      }}
                      disabled={phoneNumberChecking}>
                      <View
                        style={{
                          borderRadius: 10,
                          backgroundColor: phoneNumberChecking
                            ? 'grey'
                            : Colors.primaryColor,
                          paddingVertical: switchMerchant ? 4 : 10,
                          paddingHorizontal: switchMerchant ? 15 : 25,
                          marginBottom: switchMerchant ? 5 : 0,
                        }}>
                        {/*Top Up*/}
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          NEXT
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>

                <ModalView
                  visible={voucherModal}
                  supportedOrientations={['landscape', 'portrait']}
                  animationType={'fade'}
                  transparent
                >
                  <View style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <View style={{
                      width: switchMerchant
                        ? windowWidth * 0.77
                        : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowWidth * 0.85 : windowWidth * 0.77,
                      height: switchMerchant
                        ? windowHeight * 0.85
                        : windowHeight * 0.8,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 5,
                      ...getTransformForModalInsideNavigation(),
                    }}>
                      <TouchableOpacity
                        style={{
                          position: 'absolute',
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,
                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setVoucherModal(false);
                        }}>
                        <AntDesign
                          name="closecircle"
                          size={switchMerchant ? 15 : 25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>

                      <View style={{
                        flex: 1,
                        flexDirection: 'column',
                        padding: 30,
                        alignItems: 'center',
                        gap: 10,
                      }}>
                        <Text style={{
                          fontSize: 20,
                          fontFamily: 'NunitoSans-SemiBold',
                          marginBottom: 10,
                        }}>
                          Select Voucher
                        </Text>

                        <FlatList
                          data={activeTaggableVouchers}
                          renderItem={({ item }) => renderVouchers(item)}
                          keyExtractor={(item) => item.uniqueId}
                          style={{
                            width: '60%',
                          }}
                          ItemSeparatorComponent={() => (
                            <View style={{
                              height: 10,
                              backgroundColor: 'transparent',
                            }} />
                          )}
                        />

                        <TouchableOpacity
                          style={{
                            marginHorizontal: 20,
                            paddingVertical: 10,
                            paddingHorizontal: 20,
                            borderRadius: 5,
                            backgroundColor: Colors.primaryColor,
                          }}
                          disabled={!selectedVoucher}
                          onPress={async () => {
                            await sendVoucherToCustomer();
                          }}
                        >
                          <Text style={{ color: Colors.whiteColor }}>Send Voucher</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </ModalView>
              </View>
            ) : (
              <></>
            )}

            {/* ///////////// Register /////////////// */}
            <ModalView
              visible={registerModal}
              supportedOrientations={['landscape', 'portrait']}
              style={{}}
              animationType={'fade'}
              transparent>
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: switchMerchant
                      ? windowWidth * 0.87
                      : windowWidth * 0.87,
                    height: switchMerchant
                      ? windowHeight * 0.9
                      : windowHeight * 0.85,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 5,

                    ...getTransformForModalInsideNavigation(),
                  }}>
                  <TouchableOpacity
                    style={{
                      position: 'absolute',
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setRegisterModal(false);
                      setAmount('');
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={{ flexDirection: 'row', flex: 1 }}>
                    <View style={{ width: '50%', padding: 30 }}>
                      <ScrollView style={{ flexDirection: 'column', flex: 1 }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 16 : 24,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Enter Guest Details
                        </Text>
                        <View style={{ flexDirection: 'row' }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              paddingVertical: switchMerchant ? 7 : 30,
                            }}>
                            First Name
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              paddingVertical: switchMerchant ? 7 : 30,
                              color: 'red',
                              marginLeft: 1,
                            }}>
                            *
                          </Text>
                        </View>
                        <TextInput
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            borderBottomWidth: 1,
                            borderBottomColor: 'black',
                            width: switchMerchant ? 200 : 300,
                            height: 40,
                          }}
                          placeholder=""
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          onChangeText={(text) => {
                            setUserName(text);
                          }}
                          defaultValue={userName}
                        />
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            paddingTop: switchMerchant ? 20 : 30,
                          }}>
                          Phone Number
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            paddingTop: 10,
                          }}>
                          {phoneNumber}
                        </Text>
                        {/* <View style={{ flexDirection: 'row' }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              paddingVertical: switchMerchant ? 7 : 30,
                            }}>
                            Email Address
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 16,
                              paddingVertical: switchMerchant ? 7 : 30,
                              color: 'red',
                              marginLeft: 1,
                            }}>
                            *
                          </Text>
                        </View>
                        <TextInput
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            borderBottomWidth: 1,
                            borderBottomColor: 'black',
                            width: switchMerchant ? 200 : 300,
                            height: 40,
                          }}
                          placeholder=""
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          onChangeText={(text) => {
                            setEmail(text);
                          }}
                          defaultValue={email}
                        /> */}
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            paddingVertical: switchMerchant ? 7 : 30,
                          }}>
                          Birthday
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowStartDatePicker(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: switchMerchant ? 7 : 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 100 : 140,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            // fontSize: switchMerchant ? 11 : 14,
                            borderRadius: 12,
                          }}>
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: 'black',
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                            }}>
                            {moment(startDate).format('DD MMM YYYY')}
                          </Text>
                        </TouchableOpacity>
                      </ScrollView>
                      <View
                        style={{
                          justifyContent: 'flex-start',
                          alignSelf: 'center',
                        }} />
                    </View>

                    <View style={{ width: '50%' }}>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 15 : 20,
                            fontWeight: 'bold',
                            margin: switchMerchant ? 1 : 12,
                            paddingTop: 20,
                          }}
                        >
                          Enter Your Phone Number
                        </Text>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={{ fontSize: switchMerchant ? 22 : 50 }}>
                            {phoneNumber}
                          </Text>
                        </View>
                      </View>
                      <View>
                        <View
                          style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            alignSelf: 'center',
                            width: '55%',
                            paddingTop: switchMerchant ? 5 : 30,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(1);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  1
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(2);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  2
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(3);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  3
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(4);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  4
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(5);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  5
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(6);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  6
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(7);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  7
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(8);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  8
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(9);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  9
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                if (phoneNumber.includes('+')) {

                                }
                                else {
                                  PhoneonNumPadBtn('+');
                                }
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  +
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(0);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  0
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                if (phoneNumber != '+6') {
                                  PhoneonNumPadBtn(-1);
                                }
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : {
                                      width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70,
                                      height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 55 : 70
                                    },
                                ]}>
                                <Feather
                                  name="chevron-left"
                                  size={switchMerchant ? 13 : 30}
                                  color={'black'}
                                  style={{}}
                                />
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View
                          style={{
                            width: '100%',
                            alignItems: 'center',
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity
                            onPress={async () => {
                              // createCRMUser();
                              setRegisterModal(false);
                              setTimeout(() => {
                                if (registerSection == 'POINTS') {
                                  setPointsModal(true);
                                } else if (registerSection == 'STAMPS') {
                                  setStampModal(true);
                                } else {
                                  setTopupCreditModal(true);
                                }
                              }, 1000);

                              ////////////////////////////

                              // call api to register customer (will skip if existed)

                              if (!selectedCustomerEdit) {
                                // create customer

                                var body = {
                                  userName,
                                  userPhone: phoneNumber.startsWith('+')
                                    ? phoneNumber.slice(1)
                                    : phoneNumber,

                                  merchantId,
                                  merchantName,
                                  merchantLogo,

                                  outletId: currOutlet.uniqueId,
                                  outletName: currOutlet.name,
                                  outletCover: currOutlet.cover,
                                  dob: startDate,
                                };

                                // ApiClient.POST(API.loyaltyCampaignAddCredit, body, false)
                                APILocal.createUserByNameAndPhone({ body, uid: merchantUserId })
                                  .then((result) => {
                                    if (result && result.status === 'success') {
                                      if (result.crmUser) {
                                        CommonStore.update(s => {
                                          s.selectedCustomerEdit = crmUser;
                                        });
                                      }
                                    } else {
                                    }
                                  });
                              }

                              ////////////////////////////
                            }}
                            disabled={phoneNumberChecking}>
                            {/*Register*/}
                            <View
                              style={{
                                borderRadius: 10,
                                backgroundColor: phoneNumberChecking
                                  ? 'grey'
                                  : Colors.primaryColor,
                                paddingVertical: switchMerchant ? 4 : 10,
                                paddingHorizontal: switchMerchant ? 15 : 25,
                                marginBottom: switchMerchant ? 5 : 0,
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                NEXT
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>
          </View>
        </View>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.66,
    marginTop: 10,
    marginHorizontal: 30,
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  creditbutton: {
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    width: Dimensions.get('window').width * 0.3,
  },
  creditbuttonsecond: {
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    width: Dimensions.get('window').width * 0.1,
    height: Dimensions.get('window').height * 0.15,
  },
  pinBtn: {
    backgroundColor: Colors.lightPrimary,
    width: 115,
    height: 60,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    borderColor: Colors.fieldtTxtColor,
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontFamily: 'NunitoSans-Bold',
  },
  addBtn: {
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LoyaltyPayEarn;
