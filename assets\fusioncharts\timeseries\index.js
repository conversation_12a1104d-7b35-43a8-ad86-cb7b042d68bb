import TimeSeries from"../viz/timeseries";import FusionCharts from"../core/index";import DataStore from"../datastore";import{between,equals,less,lessEquals,greater,greaterEquals,filter,select,groupBy,pipe,sort,pivot}from"../datastore/operators";import{aggregatorStore}from"../datastore/aggregators/index.js";import{before,after,parseDate,formatDate,duration,DatetimeUnits,Weekdays}from"../utils";FusionCharts.DataStore=DataStore;FusionCharts.DataStore.Operators={between:between,equals:equals,less:less,lessEquals:lessEquals,greater:greater,greaterEquals:greaterEquals,filter:filter,select:select,groupBy:groupBy,pipe:pipe,sort:sort,pivot:pivot};FusionCharts.Utils={duration:duration,before:before,after:after,parseDate:parseDate,formatDate:formatDate,DatetimeUnits:DatetimeUnits,Weekdays:Weekdays};FusionCharts.DataStore.Aggregators={aggregatorStore:aggregatorStore};export{TimeSeries};export default{name:"timeseries",type:"package",requiresFusionCharts:true,extension:FC=>FC.addDep(TimeSeries)};