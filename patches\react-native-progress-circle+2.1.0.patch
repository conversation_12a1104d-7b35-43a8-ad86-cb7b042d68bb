diff --git a/node_modules/react-native-progress-circle/src/index.js b/node_modules/react-native-progress-circle/src/index.js
index 50cb271..95269ee 100644
--- a/node_modules/react-native-progress-circle/src/index.js
+++ b/node_modules/react-native-progress-circle/src/index.js
@@ -1,11 +1,17 @@
 import React, { Component } from 'react'
 import PropTypes from 'prop-types'
-import { StyleSheet, View, ViewPropTypes,I18nManager } from 'react-native'
+import { 
+  StyleSheet, 
+  View, 
+  // ViewPropTypes,
+  I18n<PERSON>anager 
+} from 'react-native'
+import { ViewPropTypes } from 'deprecated-react-native-prop-types';
 
 // compatability for react-native versions < 0.44
 const ViewPropTypesStyle = ViewPropTypes
   ? ViewPropTypes.style
-  : View.propTypes.style
+  : ViewPropTypes
 let direction = I18nManager.isRTL? 'right' : 'left';
 const styles = StyleSheet.create({
   outerCircle: {
