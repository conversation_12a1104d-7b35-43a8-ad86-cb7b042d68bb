import firestore from '@react-native-firebase/firestore';
import storage from '@react-native-firebase/storage';
import auth from '@react-native-firebase/auth';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { OUTLET_SHIFT_STATUS, ROLE_TYPE, USER_ORDER_STATUS, USER_QUEUE_STATUS, USER_RESERVATION_STATUS, USER_RING_STATUS, NOTIFICATIONS_TYPE, NOTIFICATIONS_ID, NOTIFICATIONS_CHANNEL, ORDER_TYPE_PARSED, ORDER_TYPE, ORDER_TYPE_SUB } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import API from '../constant/API';
import moment from 'moment';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import { NotificationStore } from '../store/notificationStore';
import { printUserOrder } from './printer';
import { PRINTER_USAGE_TYPE } from '../constant/printer';

export const parseMessages = async msg => {
    try {
        const type = msg.data.type;

        if (type === NOTIFICATIONS_TYPE.USER_ORDER) {
            // console.log('incoming user order!');

            let orderTypeShow = ORDER_TYPE_PARSED[msg.data.orderType].toLowerCase();
            if (msg.data.orderType === ORDER_TYPE.PICKUP
                && msg.data.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
                    orderTypeShow = 'delivery';
            }

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.USER_ORDER,

                channelId: NOTIFICATIONS_CHANNEL.USER_ORDER,

                title: `A ${orderTypeShow} order placed!`,
                message: `Total: RM${msg.data.finalPrice}`,
                data: msg.data,
                userInfo: msg.data,
                // actions: [
                //     SURVEY_NOTIFICATION_ACTIONS.PROCEED,
                //     SURVEY_NOTIFICATION_ACTIONS.SNOOZE,
                // ],
                // date: dayjs(inputData.formData.currTime).add(1, 'minute').toDate(),
                // date: dayjs(nextTime).toDate(),
                // repeatType: 'time',
                // repeatTime: intervalTime,
            });

            // await printUserOrder(msg.data, false, [PRINTER_USAGE_TYPE.ORDER_SUMMARY]);
            // await printUserOrder(msg.data, false, [PRINTER_USAGE_TYPE.KITCHEN_DOCKET]);

            // if (msg.data.orderType === ORDER_TYPE.DINEIN) {
            //     // offline support 

            //     // order summary and docket for dine in orders will now printed once orders made, instead of printing when notifications received
            // }
            // else {
            //     await printUserOrder(msg.data, false, [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]);
            // }

            // await printUserOrder(msg.data, false, [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]);

            ////////////////////////////////////////////////

            // print order

            // if (msg.data.orderType === ORDER_TYPE.DINEIN) {
            //     await printUserOrder(
            //         msg.data,
            //         false,
            //         [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
            //         false,
            //         false,
            //         false,
            //         { isInternetReachable: true, isConnected: true },
            //     );

            //     await printUserOrder(
            //         msg.data,
            //         false,
            //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //         false,
            //         false,
            //         false,
            //         { isInternetReachable: true, isConnected: true },
            //     );
            // }
            // else {
            //     if (msg.data.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
            //         // if received don't print first, wait until authorized
            //     }
            //     else {
            //         await printUserOrder(
            //             msg.data,
            //             false,
            //             [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //             false,
            //             false,
            //             false,
            //             { isInternetReachable: true, isConnected: true },
            //         );

            //         await printUserOrder(
            //             msg.data,
            //             false,
            //             [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
            //             false,
            //             false,
            //             false,
            //             { isInternetReachable: true, isConnected: true },
            //         );                    
            //     }
            // }

            ////////////////////////////////////////////////
        }
        else if (type === NOTIFICATIONS_TYPE.USER_RING) {
            // console.log('incoming user ring!');

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.USER_RING,

                channelId: NOTIFICATIONS_CHANNEL.USER_RING,

                title: `Table ${msg.data.tableCode} ring!`,
                message: `Name: ${msg.data.userName}`,
                data: msg.data,
                userInfo: msg.data,
            });
        }
        else if (type === NOTIFICATIONS_TYPE.MERCHANT_BATCH_UPLOAD_PRODUCTS) {
            // console.log('incoming batch upload product status!');

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_PRODUCTS,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_PRODUCTS,

                title: msg.title,
                message: msg.message,
                data: msg.data,
                userInfo: msg.data,
            });
        }
        else if (type === NOTIFICATIONS_TYPE.MERCHANT_BATCH_UPLOAD_CRM_USERS) {
            // console.log('incoming batch upload crm users status!');

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_CRM_USERS,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_CRM_USERS,

                title: msg.title,
                message: msg.message,
                data: msg.data,
                userInfo: msg.data,
            });
        }
        else if (type === NOTIFICATIONS_TYPE.WAITER_KITCHEN_READY) {
            // console.log('incoming waiter kitchen ready status!');

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.WAITER_KITCHEN_READY,

                channelId: NOTIFICATIONS_CHANNEL.WAITER_KITCHEN_READY,

                title: msg.title,
                message: msg.message,
                data: msg.data,
                userInfo: msg.data,
            });
        }
        else if (type === NOTIFICATIONS_TYPE.MERCHANT_BATCH_UPLOAD_INVENTORIES) {
            // console.log('incoming batch upload inventories status!');

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_INVENTORIES,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_INVENTORIES,

                title: msg.title,
                message: msg.message,
                data: msg.data,
                userInfo: msg.data,
            });
        }
        else if (type === undefined && msg.notification && msg.notification.body) {
            // general notification

            PushNotification.localNotification({
                id: NOTIFICATIONS_ID.MERCHANT_BATCH_UPLOAD_INVENTORIES,

                channelId: NOTIFICATIONS_CHANNEL.MERCHANT_BATCH_UPLOAD_INVENTORIES,

                title: '',
                message: msg.notification.body,
                data: {},
                userInfo: {},
            });
        }
    }
    catch (ex) {
        console.error(ex);
    }
};

export const parseLocalNotifications = notifications => {
    // if (notification.action === SURVEY_NOTIFICATION_ACTIONS.PROCEED ||
    //     (notification.action !== SURVEY_NOTIFICATION_ACTIONS.SNOOZE && notification.action === undefined)) {

    // }

    // console.log(notifications);

    if (notifications.userInteraction) {
        // console.log('user tapped local notifications!');

        if (notifications.data.type === NOTIFICATIONS_TYPE.USER_ORDER) {
            // clicked user order notifications

            NotificationStore.update(s => {
                s.nUserOrder = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.USER_RING) {
            // clicked user ring notifications

            NotificationStore.update(s => {
                s.nUserRing = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.WAITER_KITCHEN_READY) {
            // clicked waiter kitchen ready notifications

            NotificationStore.update(s => {
                s.nWaiterKitchenReadyOrder = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.MERCHANT_RESERVATION_REMINDER) {
            // clicked waiter kitchen ready notifications

            NotificationStore.update(s => {
                s.nUserReservation = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.MERCHANT_QUEUE_REMINDER) {
            // clicked waiter kitchen ready notifications

            NotificationStore.update(s => {
                s.nUserQueue = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT) {
            // clicked merchant low stock alert notifications

            NotificationStore.update(s => {
                s.nOutletSupplyItemLow = notifications.data;
            });
        }
        else if (notifications.data.type === NOTIFICATIONS_TYPE.MERCHANT_LOW_STOCK_ALERT_PRODUCT) {
            // clicked merchant low stock alert notifications

            NotificationStore.update(s => {
                s.nOutletItemLow = notifications.data;
            });
        }
    }
};
