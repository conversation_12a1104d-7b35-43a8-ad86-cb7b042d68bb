/**
 * @format
 */

// In your index.js or App.js
import crashlytics from '@react-native-firebase/crashlytics';

// Enable crashlytics right away
// await crashlytics().setCrashlyticsCollectionEnabled(true);
crashlytics().setCrashlyticsCollectionEnabled(true);

import { NativeModules } from 'react-native';

import './gesture-handler';

import { AppRegistry, LogBox, Platform } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import 'react-native-get-random-values';

import messaging from '@react-native-firebase/messaging';
import database from '@react-native-firebase/database';
import firestore from '@react-native-firebase/firestore';
import { parseMessages, parseLocalNotifications } from './util/notifications';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { NOTIFICATIONS_CHANNEL_LIST, ROLE_TYPE, TIMEZONE } from './constant/common';
import moment from 'moment';
import { UserStore } from './store/userStore';
import { MerchantStore } from './store/merchantStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import schema from './model/schema';
import migrations from './model/migrations';
import RazerPayoutTransaction from './model/RazerPayoutTransation';
import { isTabletV2 } from './util/common';

////////////////////////////////////////////////////////

(async () => {
    global.isTabletCache = await isTabletV2();
})()
    .catch(error => {
        console.error('Error while checking tablet status:', error);
    });

////////////////////////////////////////////////////////

String.prototype.insert = function (index, string) {
    if (index > 0) {
        return this.substring(0, index) + string + this.substr(index);
    }

    return string + this;
};

// const originalErrorHandler = ErrorUtils.getGlobalHandler();

// ErrorUtils.setGlobalHandler((error, isFatal) => {
//   crashlytics().recordError(error);
//   originalErrorHandler(error, isFatal);
// });

// For newer React Native versions with Hermes
const exceptionHandler = async (error, isFatal) => {
    console.error('Global error caught:', error);
    await crashlytics().recordError(error);
};

// Register the exception handler with the JSI bindings if available
if (global.__registerErrorHandler) {
    global.__registerErrorHandler(exceptionHandler);
    console.error('JSI error handler registered with Crashlytics integration');
} else {
    console.warn('JSI error handler registration not available');
}

////////////////////////////////////////////////////////

// const unhandledPromiseRejectionTracker = (id, error) => {
//     crashlytics().recordError(error);
// };

// require('promise/setimmediate/rejection-tracking').enable({
//     allRejections: true,
//     onUnhandled: unhandledPromiseRejectionTracker
// });

// Add a handler for unhandled promise rejections
if (global.HermesInternal) {
    console.error('Running on Hermes - setting up promise rejection tracking');
    require('promise/setimmediate/rejection-tracking').enable({
        allRejections: true,
        onUnhandled: (id, error) => {
            console.error('Unhandled promise rejection:', error);
            crashlytics().recordError(error);
        },
        onHandled: () => { }
    });
}

////////////////////////////////////////////////////////

// First, create the adapter to the underlying database:
global.watermelonDBAdapter = new SQLiteAdapter({
    schema,
    // (You might want to comment it out for development purposes -- see Migrations documentation)
    // migrations,
    // (optional database name or file system path)
    // dbName: 'myapp',
    // (recommended option, should work flawlessly out of the box on iOS. On Android,
    // additional installation steps have to be taken - disable if you run into issues...)
    // jsi: (Platform.OS === 'ios' && !__DEV__) ? true : false, /* Platform.OS === 'ios' */
    jsi: false,
    // (optional, but you should implement this method)
    onSetUpError: error => {
        console.error(error);
        // Database failed to load -- offer the user to reload the app or log out
    }
});

// Then, make a Watermelon database from it!
global.watermelonDBDatabase = new Database({
    adapter: global.watermelonDBAdapter,
    modelClasses: [
        RazerPayoutTransaction,
    ],
});

// reset database
// global.watermelonDBDatabase
//     .write(async () => {
//         await global.watermelonDBDatabase.unsafeResetDatabase();
//     });

console.disableYellowBox = true;

database().setPersistenceCacheSizeBytes(10000000); // 10MB
// database().setPersistenceCacheSizeBytes(50000000); // 50MB
// database().setPersistenceCacheSizeBytes(99000000); // ~99MB
database().setPersistenceEnabled(true);
// database().setPersistenceEnabled(false);

// database().goOffline();
// database().goOnline();

// test commit changes

MerchantStore.subscribe(
    (s) => s,
    async (merchantStore) => {
        if (merchantStore.currOutletId) {
            // await AsyncStorage.setItem('@merchantStore', JSON.stringify({
            //     description: '',
            //     name: '',
            //     shortcode: '',
            //     logo: '',

            //     allOutlets: [],
            //     allOutletsDict: {},

            //     currOutletId: merchantStore.currOutletId,
            //     currOutlet: {},

            //     poNumber: 0,
            //     poNumberUpdatedAt: null,
            // }));

            // await AsyncStorage.setItem('@merchantStore', JSON.prune(merchantStore));

            // storageMMKV.set('@merchantStore', JSON.prune(merchantStore));
            await global.watermelonDBDatabase.localStorage.set('@merchantStore', JSON.prune(merchantStore));
        }
    },
);

const VirtualizedView = props => {
    return (
        <FlatList
            data={[]}
            ListEmptyComponent={null}
            keyExtractor={() => { }}
            renderItem={null}
            ListHeaderComponent={() => (
                <>{props.children}</>
            )}
            contentContainerStyle={props.contentContainerStyle}
        />
    );
};
export default VirtualizedView;

LogBox.ignoreAllLogs(true)

messaging().setBackgroundMessageHandler(async msg => {
    // console.log('message from background!');
    // console.log(msg);

    parseMessages(msg);
});

// Must be outside of any component LifeCycle (such as `componentDidMount`).
PushNotification.configure({
    // (required) Called when a remote is received or opened, or local notification is opened
    onNotification(notification) {
        // console.log("NOTIFICATION:", notification);

        // process the notification

        // (required) Called when a remote is received or opened, or local notification is opened
        // notification.finish();

        const { foreground, userInteraction, title, message } = notification;
        if (
            Platform.OS === 'ios' &&
            foreground &&
            // (title || message) &&
            (notification && notification.data && notification.data.type) &&
            !userInteraction
        ) {
            // PushNotification.localNotification(notification);
            parseMessages(notification);
        }

        parseLocalNotifications(notification);

        // (required) Called when a remote is received or opened, or local notification is opened
        notification.finish(PushNotificationIOS.FetchResult.NoData);
    },

    // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
    onAction(notification) {
        // console.log("ACTION:", notification.action);
        // console.log("NOTIFICATION:", notification);

        parseLocalNotifications(notification);
    },

    // IOS ONLY (optional): default: all - Permissions to register.
    permissions: {
        alert: true,
        badge: true,
        sound: true,
    },

    // Should the initial notification be popped automatically
    // default: true
    popInitialNotification: true,

    /**
     * (optional) default: true
     * - Specified if permissions (ios) and token (android and ios) will requested or not,
     * - if not, you must call PushNotificationsHandler.requestPermissions() later
     * - if you are not using remote notification or do not have Firebase installed, use this:
     *     requestPermissions: Platform.OS === 'ios'
     */
    requestPermissions: true,
});

for (let i = 0; i < NOTIFICATIONS_CHANNEL_LIST.length; i++) {
    PushNotification.createChannel(
        {
            channelId: NOTIFICATIONS_CHANNEL_LIST[i].channelId, // (required)
            channelName: NOTIFICATIONS_CHANNEL_LIST[i].channelName, // (required)
            // channelDescription: "A channel to categorise your notifications", // (optional) default: undefined.
            // playSound: false, // (optional) default: true
            // soundName: "default", // (optional) See `soundName` parameter of `localNotification` function
            // importance: 4, // (optional) default: 4. Int value of the Android notification importance
            // vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
        },
        created => {
            // console.log(`createChannel returned '${created}'`) // (optional) callback returns whether the channel was created, false means it already existed.            
        }
    );
}

////////////////////////////////////////////////////////

AppRegistry.registerComponent(appName, () => App);
