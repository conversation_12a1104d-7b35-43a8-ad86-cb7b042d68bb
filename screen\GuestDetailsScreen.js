import { Text } from "react-native-fast-text";
import React, { useState, } from 'react';
import {
    StyleSheet,
    Image,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    Button,
    Modal as ModalComponent,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    TextInputBase,
    useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Switch from 'react-native-switch-pro';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import Fontisto from 'react-native-vector-icons/Fontisto';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Coins from '../assets/svg/Coins.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Styles from '../constant/Styles';
import AIcon from 'react-native-vector-icons/AntDesign';
import {
    isTablet
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
    DELAY_LONG_PRESS_TIME,
    MODE_ADD_CART,
    OFFLINE_BILL_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    OFFLINE_PAYMENT_METHOD_TYPE,
    ORDER_TYPE,
    USER_ORDER_PRIORITY,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
    getObjectDiff,
    isObjectEqual,
    listenToCurrOutletIdChangesWaiter,
    naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import CheckBox from '@react-native-community/checkbox';
import { parseValidPriceText } from '../util/common';
import RadioForm, { RadioButton, RadioButtonInput, RadioButtonLabel } from 'react-native-simple-radio-button';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import GCalendar from '../assets/svg/GCalendar';
import FusionCharts from 'react-native-fusioncharts';
// import {CountryPicker} from "react-native-country-codes-picker/components/CountryPicker";
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;


const GuestDetailsScreen = (props) => {
    const { navigation } = props;

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    //flat list usestate
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [showReservations, setShowReservations] = useState(true);
    const [showFinished, setShowFinished] = useState(false);
    const [showWaitlist, setShowWaitlist] = useState(false);

    // flat list clicked usestate
    const [showDetailsReservations, setShowDetailsReservations] = useState(false);
    const [showDetailsGuest, setShowDetailsGuest] = useState(true);
    const [informationEdit, setInformationEdit] = useState(false);
    const [reservationTagsEdit, setReservationTagsEdit] = useState(false);
    const [reservationNotesEdit, setReservationNotesEdit] = useState(false);
    const [reservationTags, setReservationTags] = useState('');
    const [reservationNotes, setReservationNotes] = useState('');
    const [showProfile, setShowProfile] = useState(true);
    const [showHistory, setShowHistory] = useState(false);
    const [gender, setGender] = useState('');
    const [selectGender, setSelectGender] = useState(null);
    const [guestNotesEdit, setGuestNotesEdit] = useState('');
    const [communicationExpand, setCommunicationExpand] = useState('');
    const [guestNotes, setGuestNotes] = useState('');
    const [birthday, setBirthday] = useState('');
    const [age, setAge] = useState('');
    const [anniversary, setAnniversary] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [phoneCode, setPhoneCode] = useState('');
    const [loyaltyMember, setLoyaltyMember] = useState(false);
    const [receiveSMS, setReceiveSMS] = useState(false);
    const [receiveEmail, setReceiveEmail] = useState(false);
    const [receiveMarketing, setReceiveMarketing] = useState(false);
    const [professionalExpand, setProfessionalExpand] = useState(false);
    const [personalExpand, setPersonalExpand] = useState(false);
    const [countryCode, setCountryCode] = useState('+60');
    const [showCountryCode, setShowCountryCode] = useState(false);
    const [language, setLanguage] = useState('');

    // tier modal usestate
    const [tierModal, setTierModal] = useState(false);
    const [tier, setTier] = useState('');
    const [checkSilverTier, setCheckSilverTier] = useState(false);
    const [checkGoldenTier, setCheckGoldenTier] = useState(false);

    // turn time modal
    const [turnTimeModal, setTurnTimeModal] = useState(false);
    const [turnTime, setTurnTime] = useState('');
    const [selectTime, setSelectTime] = useState(null);

    //reservation modal useState
    const [manualEntry, setManualEntry] = useState(false);
    const [bill, setBill] = useState(0);
    const [loyaltyStatus, setLoyaltyStatus] = useState(false);
    const [addNotes, setAddNotes] = useState('');
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [email, setEmail] = useState('');

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    // history notes modal usestate
    const [showNotesModal, setShowNotesModal] = useState(false);

    const genderRadio = [
        {
            label: 'Male',
            value: 'Male'
        },
        {
            label: 'Female',
            value: 'Female'
        },
        {
            label: 'Other',
            value: 'Other'
        }
    ]

    const dummyData = [
        {
            time: '3.00pm',
            name: 'Ah Boy',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'In-house'
        },
        {
            time: '3.00pm',
            name: 'Ah Girl',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Candice',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Girl',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
        {
            time: '3.00pm',
            name: 'Boy',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Zebra',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Hippo',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'Lion',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        }, {
            time: '3.00pm',
            name: 'King',
            phone: '012-9129013',
            place: 'Indoor',
            two: '2',
            one: '1',
            rsvNotes: '',
            guestNotes: 'Chicken is raw',
            rsvTags: '',
            guestTags: [
                { tag: 'Regular' },
                { tag: 'Big Spender' },
                { tag: 'No Peanut' }
            ],
            created: '01/01/2021',
            createdWay: 'Widget'
        },
    ]

    const timeData = [
        { time: '0h 15m' },
        { time: '0h 30m' },
        { time: '0h 45m' },
        { time: '1h 00m' },
        { time: '1h 15m' },
        { time: '1h 30m' },
        { time: '1h 45m' },
        { time: '2h 00m' },
        { time: '2h 15m' },
        { time: '2h 30m' },
        { time: '2h 45m' },
        { time: '3h 00m' },
        { time: '3h 15m' },
        { time: '3h 30m' },
        { time: '3h 45m' },
        { time: '4h 00m' },
        { time: '4h 15m' },
        { time: '4h 30m' },
        { time: '4h 45m' },
        { time: '5h 00m' },
        { time: '5h 15m' },
        { time: '5h 30m' },
        { time: '5h 45m' },
        { time: '6h 00m' },
        { time: '6h 15m' },
        { time: '6h 30m' },
        { time: '6h 45m' },
    ]

    const renderGuestTags = ({ item, index }) => {
        return (
            <View
                style={{
                    backgroundColor: 'blue',
                    borderRadius: 5,
                    marginBottom: 5,
                    width: windowWidth * 0.075,
                    height: windowHeight * 0.04,
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'center'
                }}>
                <Text style={[styles.table, { color: Colors.whiteColor }]}>
                    {`${item.tag}`}
                </Text>
            </View>
        )
    }

    const renderTimeData = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setTurnTime(item.time)
                    setSelectTime(index)
                }}
                style={selectTime === index ? { backgroundColor: 'rgb(30, 134, 151)' }
                    : { backgroundColor: Colors.whiteColor }}
            >
                <View style={{
                    height: 70,
                    width: windowWidth * 0.4,
                    borderBottomWidth: 1,
                    borderBottomColor: '#D3D3D3',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                    <Text
                        style={selectTime === index ? { color: Colors.whiteColor, textAlign: 'center' }
                            : { color: Colors.blackColor, textAlign: 'center' }}>
                        {`${item.time}`}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    const historyData = [
        {
            date: 'Fri 3 Dec 2021',
            time: '6.00PM',
            type: 'Reservation',
            guest: '',
            notes: 'test',
            amount: 100,
            venue: `Ryan's Steakhouse`,
        },
        {
            date: 'Fri 3 Dec 2021',
            time: '2.00PM',
            type: 'Reservation',
            guest: 'asd',
            notes: '',
            amount: 100,
            venue: `Ryan's Steakhouse`,
        },
        {
            date: 'Tue 4 Nov 2021',
            time: '3.00PM',
            type: 'Reservation',
            guest: 'qerqwr',
            notes: 'test',
            amount: 100,
            venue: `Ryan's Steakhouse`,
        },
        {
            date: 'Mon 5 Dec 2021',
            time: '6.00AM',
            type: 'Reservation',
            guest: '',
            notes: '',
            amount: 100,
            venue: `Ryan's Steakhouse`,
        }
    ]

    const renderHistoryData = ({ item, index }) => {
        return (
            <View style={{
                width: windowWidth * 0.63,
                height: windowHeight * 0.05,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#ffffff',
                borderWidth: 1,
                borderColor: 'gray'
            }}>
                <View style={{ flex: 2, justifyContent: 'flex-start' }}>
                    <Text style={{ marginLeft: 10 }}>
                        {`${item.date}`}, {`${item.time}`}
                    </Text>
                </View>
                <View style={{ flex: 1.5, justifyContent: 'flex-start' }}>
                    <Text>
                        {`${item.type}`}
                    </Text>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                    <Text>
                        {`${item.guest}`}
                    </Text>
                </View>
                {item.notes !== '' ?
                    <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                        <TouchableOpacity onPress={() => { setShowNotesModal(true) }}>
                            <MaterialCommunityIcons
                                name='note-text-outline'
                                size={25}
                            />
                        </TouchableOpacity>
                    </View>
                    : <View style={{ flex: 1 }}></View>}
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                    <Text>
                        RM {`${item.amount}`}
                    </Text>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                    <Text>
                        {`${item.venue}`}
                    </Text>
                </View>
            </View>
        )
    }

    const renderDummyData = ({ item, index }) => {
        // When Reservations is clicked
        if (showReservations) {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                    {`${item.guestTags.tag}`}
                                </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
        // When Finished/CXL is clicked
        else if (showFinished) {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                        {`${item.guestTags.tag}`}
                                    </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
        // When Waitlist is clicked
        else {
            return (
                <View style={{ backgroundColor: Colors.highlightColor }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View
                            style={styles.flatListHeader}>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.tableFirst}>
                                    {`${item.time}`}
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    <Icon
                                        name='md-person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{ marginLeft: 5 }}
                                    />
                                    2
                                </Text>
                            </View>
                            <View style={
                                { flex: 0.8, justifyContent: 'flex-start' }}>
                                <Text
                                    style={[styles.table,]}>
                                    <Icon
                                        name='person-outline'
                                        size={15}
                                        color={Colors.tabGrey}
                                        style={{
                                            opacity: 0.6,
                                            paddingLeft: 2,
                                            marginLeft: 5,
                                        }}
                                    />
                                    1
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Notes
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    RSV Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Guest Tags
                                </Text>
                            </View>
                            <View style={
                                { flex: 1, justifyContent: 'flex-start' }}>
                                <Text
                                    style={styles.table}>
                                    Created
                                </Text>
                            </View>
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('GuestDetailsScreen')
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                            <View
                                style={styles.flatListBody}>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.tableFirst}>
                                        {`${item.name}\n\n${item.phone}\n\n${item.place}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        <Icon
                                            name='md-person-outline'
                                            size={15}
                                            color={Colors.tabGrey}
                                            style={{ marginLeft: 5 }}
                                        />
                                        {`${item.two}`}
                                    </Text>
                                </View>
                                <View style={{
                                    flex: 0.8,
                                    justifyContent: 'flex-start',
                                }}>
                                    <View style={{
                                        flexDirection: 'row',
                                        borderRadius: 5,
                                        backgroundColor: 'rgb(0, 200, 0)',
                                    }}>
                                        <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                                            <EvilIcons
                                                name='check'
                                                size={35}
                                                color={Colors.whiteColor}
                                                style={{
                                                    marginLeft: 3,
                                                }}
                                            />
                                        </View>
                                        <View style={{ flex: 1.3 }}>
                                            <Text
                                                style={{
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {`${item.one}`}
                                            </Text>
                                            <Text style={{
                                                textAlign: 'center',
                                                color: Colors.whiteColor,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                Finished
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.guestNotes}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.rsvTags}`}
                                    </Text>
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <FlatList
                                        style={styles.table}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderGuestTags}
                                        data={item.guestTags.map((item) => {
                                            return item
                                        })}

                                    />
                                    {/* <Text style={styles.table}>
                                        {`${item.guestTags.tag}`}
                                    </Text> */}
                                </View>
                                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                                    <Text
                                        style={styles.table}>
                                        {`${item.created}\n\n${item.createdWay}`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            )
        }
    }

    // Navigation bar
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const userName = UserStore.useState((s) => s.name);

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Details
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });


    return (
        <View style={[
            styles.container,
            !isTablet()
                ? {
                    transform: [{ scaleX: 1 }, { scaleY: 1 }]
                }
                : {},
        ]}>

            {/* Sidebar */}
            {/* <View
                style={[
                    styles.sidebar,
                    !isTablet()
                        ? {
                            width: windowWidth * 0.08,
                        }
                        : {},
                    switchMerchant
                        ? {
                            width: '10%'
                        }
                        : {},
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={1}
                    expandOperation={true}
                />
            </View> */}

            <View>
                {/* Top bar */}
                <View style={[
                    styles.topBar,
                    switchMerchant
                        ? {}
                        : {
                            height: windowHeight * 0.07,
                            width: windowWidth * 0.92,
                        }
                ]}>
                    <TouchableOpacity
                        style={[
                            styles.topBarButton,
                            showReservations ? { backgroundColor: Colors.whiteColor } : {},
                        ]}
                        onPress={() => {
                            setShowReservations(true)
                            setShowFinished(false)
                            setShowWaitlist(false)
                        }}
                    >
                        <Text
                            style={{
                                textAlign: 'center',
                            }}>
                            Reservations
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.topBarButton,
                            showFinished ? { backgroundColor: Colors.whiteColor } : {},
                        ]}
                        onPress={() => {
                            setShowReservations(false)
                            setShowFinished(true)
                            setShowWaitlist(false)
                        }}
                    >
                        <Text
                            style={{
                                textAlign: 'center',
                            }}>
                            Finished/CXL
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[
                            styles.topBarButton,
                            showWaitlist ? { backgroundColor: Colors.whiteColor } : {},
                        ]}
                        onPress={() => {
                            setShowReservations(false)
                            setShowFinished(false)
                            setShowWaitlist(true)
                        }}
                    >
                        <Text
                            style={{
                                textAlign: 'center',
                            }}>
                            Waitlist
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.changeBackSeatButton}>
                        <Text style={{ color: 'rgb(30, 134, 151)' }}>
                            <AntDesign
                                name='arrowleft'
                                size={20}
                                color='rgb(30, 134, 151)'
                            />
                            Change back to Seated
                        </Text>
                    </TouchableOpacity>
                </View>
                {/* Left Side */}
                {/* Show or hide between the Page */}
                {/* Show Reservation page */}
                <View style={{ flexDirection: 'row' }}>
                    <View>
                        <FlatList
                            style={styles.clickedFlatList}
                            nestedScrollEnabled={true}
                            showsVerticalScrollIndicator={false}
                            data={dummyData}
                            renderItem={renderDummyData}
                        // data={suppliers.filter((item) => {
                        //     if (search !== '') {
                        //     return item.name
                        //         .toLowerCase()
                        //         .includes(search.toLowerCase());
                        //     } else {
                        //     return true;
                        //     }
                        // })}
                        // extraData={suppliers.filter((item) => {
                        //     if (search !== '') {
                        //     return item.name
                        //         .toLowerCase()
                        //         .includes(search.toLowerCase());
                        //     } else {
                        //     return true;
                        //     }
                        // })}
                        // renderItem={renderOrderItem}
                        // keyExtractor={(item, index) => String(index)}
                        />
                    </View>

                    {/* Right Side */}
                    <ScrollView style={styles.detailsContainer}>
                        {/* Modal party size */}
                        <ModalView supportedOrientations={['landscape', 'portrait']}
                            style={{ flex: 1 }}
                            visible={turnTimeModal}
                            transparent={true}>
                            <View style={[styles.modalContainer,]}>
                                <View
                                    style={[
                                        styles.modalView,
                                    ]}>
                                    <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderColor: '#D3D3D3' }}>
                                        <View style={{ flex: 1 }}>
                                        </View>
                                        <View style={{ flex: 5, marginBottom: 20 }}>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                textAlign: 'center',
                                                fontSize: switchMerchant ? 18 : 20,
                                            }}>
                                                Change Total Turn Time
                                            </Text>
                                        </View>
                                        <View style={{ flex: 1 }}>
                                            <TouchableOpacity
                                                style={{
                                                }}
                                                onPress={() => {
                                                    setTurnTimeModal(false);
                                                }}>
                                                <AIcon
                                                    name="closecircle"
                                                    size={switchMerchant ? 15 : 25}
                                                    color={Colors.fieldtTxtColor}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                    <View style={{
                                        flexDirection: 'row',
                                        backgroundColor: 'rgb(30, 134, 151)',
                                        height: windowHeight * 0.08,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: Colors.whiteColor,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold'
                                        }}>
                                            End of availability 17h 30m
                                        </Text>
                                    </View>
                                    <FlatList
                                        style={{
                                            height: windowHeight * 0.5,
                                            width: windowWidth * 0.4,
                                            // paddingVertical: 20,
                                            // paddingHorizontal: 15,
                                            //marginTop: 10,
                                            borderBottomLeftRadius: 5,
                                            borderBottomRightRadius: 5,
                                            backgroundColor: 'white',
                                            borderColor: 'gray'
                                        }}
                                        nestedScrollEnabled={true}
                                        showsVerticalScrollIndicator={false}
                                        data={timeData}
                                        renderItem={renderTimeData}
                                    // data={suppliers.filter((item) => {
                                    //     if (search !== '') {
                                    //     return item.name
                                    //         .toLowerCase()
                                    //         .includes(search.toLowerCase());
                                    //     } else {
                                    //     return true;
                                    //     }
                                    // })}
                                    // extraData={suppliers.filter((item) => {
                                    //     if (search !== '') {
                                    //     return item.name
                                    //         .toLowerCase()
                                    //         .includes(search.toLowerCase());
                                    //     } else {
                                    //     return true;
                                    //     }
                                    // })}
                                    // renderItem={renderOrderItem}
                                    // keyExtractor={(item, index) => String(index)}
                                    />
                                </View>
                            </View>
                        </ModalView>
                        {/* Two buttons */}
                        <View
                            style={{
                                flexDirection: 'row',
                            }}
                        >
                            <View style={{ flex: 1 }}>
                                <TouchableOpacity
                                    style={[
                                        styles.detailsButton,
                                        showDetailsReservations ? { backgroundColor: Colors.lightGrey }
                                            : { backgroundColor: Colors.whiteColor },
                                    ]}
                                    onPress={() => {
                                        setShowDetailsReservations(true)
                                        setShowDetailsGuest(false)
                                    }}
                                >
                                    <Text
                                        style={{
                                            textAlign: 'center',
                                        }}>
                                        Reservations Details
                                    </Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ flex: 1 }}>
                                <TouchableOpacity
                                    style={[
                                        styles.detailsButton,
                                        showDetailsGuest ? { backgroundColor: Colors.lightGrey }
                                            : { backgroundColor: Colors.whiteColor },
                                    ]}
                                    onPress={() => {
                                        setShowDetailsReservations(false)
                                        setShowDetailsGuest(true)
                                    }}
                                >
                                    <Text
                                        style={{
                                            textAlign: 'center',
                                        }}>
                                        Guest Details
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        {/* Guest Details */}
                        {showDetailsGuest ?
                            <View>
                                {/* First Row */}
                                <View style={{ flexDirection: 'row', marginTop: 10, marginBottom: 10 }}>
                                    <View style={{
                                        marginLeft: 20,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        borderRadius: 65,
                                        backgroundColor: '#808080',
                                        width: windowWidth * 0.1
                                    }}>
                                        <Fontisto
                                            name='male'
                                            size={90}
                                            color={Colors.blackColor}
                                            style={{
                                            }}
                                        />
                                        <Text style={{ borderWidth: 1, borderColor: 'orange', color: 'orange' }}>
                                            {tier} Member
                                        </Text>
                                        <Text style={{ color: Colors.whiteColor }}>
                                            Tier
                                        </Text>
                                    </View>
                                    <View style={{
                                        marginLeft: 10,
                                        marginBottom: 10
                                    }}>
                                        <Text>
                                            Reservation Name: { } Ryan Lee
                                        </Text>
                                        <Text style={{ marginBottom: 10 }}>
                                            Reservation Email: { } <EMAIL>
                                        </Text>
                                        <Text style={{ marginBottom: 10, color: 'gray' }}>
                                            Ryan
                                        </Text>
                                        <Text style={{ color: 'gray' }}>
                                            012-1238374
                                        </Text>
                                    </View>
                                    <TouchableOpacity style={[styles.changeGuestButton,
                                    { marginTop: 20, marginLeft: 300 }]}>
                                        <Text style={{ color: 'rgb(30, 134, 151)' }}>
                                            Change guest
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                {/* Second Row */}
                                <View style={{ flexDirection: 'row', marginBottom: 10 }}>
                                    <TouchableOpacity style={[styles.guestButtons, { marginLeft: 30 }]}>
                                        <View style={{ flexDirection: 'column', }}>
                                            <Text>
                                                Party Size
                                            </Text>
                                            <Text style={{ marginLeft: 27 }}>
                                                1
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={styles.guestButtons}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text>
                                                Time
                                            </Text>
                                            <Text style={{ marginLeft: 3 }}>
                                                1:00
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={styles.guestButtons}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text>
                                                Indoor
                                            </Text>
                                            <Text style={{ marginLeft: 20 }}>
                                                2
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.guestButtons}
                                        onPress={() => { setTurnTimeModal(true) }}
                                    >
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text>
                                                Turn Time
                                            </Text>
                                            <Text style={{ textAlign: 'center' }}>
                                                {turnTime}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                {/* Third Row */}
                                <View style={{ flexDirection: 'row' }}>
                                    {/* Left Side */}
                                    <View style={{ flexDirection: 'column' }}>
                                        <View style={[styles.reservationsContainer,
                                        { flexDirection: 'row' }]}>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                    Reservation Tags
                                                </Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <TouchableOpacity onPress={() => {
                                                    reservationTagsEdit ? setReservationTagsEdit(false)
                                                        : setReservationTagsEdit(true)
                                                }}>
                                                    <Text style={[styles.editButton]}>
                                                        {reservationTagsEdit ? 'Done' : 'Edit'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={styles.reservationsContainerBelow}>
                                            <TextInput
                                                editable={reservationTagsEdit ? true : false}
                                                onChangeText={(text) => { setReservationTags(text) }}
                                                value={reservationTags}
                                                color='black'
                                                placeholder='No reservation notes have been added'
                                            />
                                        </View>
                                        <View style={styles.reservationsContainer}>
                                            <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                Created
                                            </Text>
                                        </View>
                                        <View style={[styles.reservationsContainerBelow,
                                        { flexDirection: 'row', alignItems: 'center' }]}>
                                            <View style={{ flex: 1.5 }}>
                                                <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                    Date
                                                </Text>
                                            </View>
                                            <View style={{
                                                flex: 0.5,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderWidth: 1,
                                                borderColor: 'orange',
                                                borderRadius: 5,
                                                marginRight: 20,
                                            }}>
                                                <Text style={{
                                                    textAlign: 'center',
                                                    color: 'orange',
                                                    height: windowHeight * 0.04,
                                                    width: windowWidth * 0.06
                                                }}>
                                                    Widget
                                                </Text>
                                            </View>
                                        </View>
                                        <View style={[styles.reservationsContainer,
                                        { flexDirection: 'row' }]}>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                    Manual Entry
                                                </Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <TouchableOpacity onPress={() => setManualEntry(true)}>
                                                    <Text style={styles.editButton}>
                                                        Edit
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={[styles.reservationsContainerBelow,
                                        {
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }]}>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ marginLeft: 10, fontfamily: 'NunitoSans-Bold' }}>
                                                    Bill amount manual entry
                                                </Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{
                                                    fontfamily: 'NunitoSans-Bold',
                                                    textAlign: 'right',
                                                    marginRight: 10
                                                }}>
                                                    RM {`${bill}`}
                                                </Text>
                                            </View>
                                        </View>
                                    </View>
                                    {/* Right Side */}
                                    <View style={{ flexDirection: 'column' }}>
                                        <View style={[styles.reservationsContainer,
                                        { flexDirection: 'row' }]}>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                    Reservation Notes
                                                </Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <TouchableOpacity onPress={() => {
                                                    reservationNotesEdit ? setReservationNotesEdit(false)
                                                        : setReservationNotesEdit(true)
                                                }}>
                                                    <Text style={styles.editButton}>
                                                        {reservationNotesEdit ? 'Done' : 'Edit'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={styles.reservationsContainerBelow}>
                                            <TextInput
                                                editable={reservationNotesEdit ? true : false}
                                                onChangeText={(text) => { setReservationNotes(text) }}
                                                value={reservationNotes}
                                                color='black'
                                                placeholder='No reservation notes have been added'
                                            />
                                        </View>
                                        <View style={[styles.reservationsContainer,
                                        { flexDirection: 'row' }]}>
                                            <View style={{ flex: 1.5 }}>
                                                <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                    Reservation specific information
                                                </Text>
                                            </View>
                                            <View style={{ flex: 0.5 }}>
                                                <TouchableOpacity onPress={() => {
                                                    informationEdit ? setInformationEdit(false)
                                                        : setInformationEdit(true)
                                                }}>
                                                    <Text style={styles.editButton}>
                                                        {informationEdit ? 'Done' : 'Edit'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        <View style={[styles.reservationsContainerBelow,
                                        { height: windowHeight * 0.28, }]}>
                                            <Text style={styles.informationTitle}>
                                                First Name
                                            </Text>
                                            <TextInput
                                                style={styles.information}
                                                editable={informationEdit ? true : false}
                                                color='black'
                                                onChangeText={(text) => { setFirstName(text) }}
                                                value={firstName}
                                                underlineColorAndroid='#D3D3D3'
                                            >
                                                Ryan
                                            </TextInput>
                                            <Text style={styles.informationTitle}>
                                                Last Name
                                            </Text>
                                            <TextInput
                                                style={styles.information}
                                                editable={informationEdit ? true : false}
                                                color='black'
                                                onChangeText={(text) => { setLastName(text) }}
                                                value={lastName}
                                                underlineColorAndroid='#D3D3D3'
                                            >
                                                Lee
                                            </TextInput>
                                            <Text style={styles.informationTitle}>
                                                Email
                                            </Text>
                                            <TextInput
                                                style={styles.information}
                                                editable={informationEdit ? true : false}
                                                color='black'
                                                onChangeText={(text) => { setEmail(text) }}
                                                value={email}
                                                underlineColorAndroid='#D3D3D3'
                                            >
                                                <EMAIL>
                                            </TextInput>
                                        </View>
                                    </View>
                                </View>
                            </View>
                            :
                            // Reservation Details 
                            <View>
                                {showProfile ?
                                    <ScrollView>
                                        {/* Profile */}
                                        {/* First Row */}
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                marginBottom: 10,
                                                marginTop: 10,
                                                height: windowHeight * 0.15
                                            }}>
                                            <View style={{
                                                marginLeft: 20,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderRadius: 65,
                                                backgroundColor: '#808080',
                                                width: windowWidth * 0.1
                                            }}>
                                                <Fontisto
                                                    name='male'
                                                    size={90}
                                                    color={Colors.blackColor}
                                                    style={{
                                                    }}
                                                />
                                                <Text style={{ borderWidth: 1, borderColor: 'orange', color: 'orange' }}>
                                                    {tier} Member
                                                </Text>
                                                <Text style={{ color: Colors.whiteColor }}>
                                                    Tier
                                                </Text>
                                            </View>
                                            <View style={{
                                                marginLeft: 10,
                                                marginBottom: 10,
                                                justifyContent: 'center',
                                            }}>
                                                <Text style={{ marginBottom: 10 }}>
                                                    Ryan
                                                </Text>
                                                <Text>
                                                    012
                                                </Text>
                                            </View>
                                        </View>
                                        {/* Second Row */}
                                        <View style={{ flexDirection: 'row', marginBottom: 10 }}>
                                            <View style={{ flex: 1 }}>
                                                <View style={[styles.reservationDetailsSecondRow, { marginLeft: 10 }]}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                                        <Text style={[styles.secondRowTextHeader]}>
                                                            Total spend
                                                        </Text>
                                                        <MaterialIcons
                                                            name='info-outline'
                                                            size={15}
                                                            color='gray'
                                                            style={{ marginLeft: 10 }}
                                                        />
                                                    </View>
                                                    <Text style={styles.secondRowTextBody}>
                                                        RM 0.00
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsSecondRow}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                                        <Text style={[styles.secondRowTextHeader]}>
                                                            Visit
                                                        </Text>
                                                        <MaterialIcons
                                                            name='info-outline'
                                                            size={15}
                                                            color='gray'
                                                            style={{ marginLeft: 10 }}
                                                        />
                                                    </View>
                                                    <Text style={styles.secondRowTextBody}>
                                                        137
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsSecondRow}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                                        <Text style={[styles.secondRowTextHeader]}>
                                                            Avg/visit
                                                        </Text>
                                                        <MaterialIcons
                                                            name='info-outline'
                                                            size={15}
                                                            color='gray'
                                                            style={{ marginLeft: 10 }}
                                                        />
                                                    </View>
                                                    <Text style={styles.secondRowTextBody}>
                                                        RM 0.00
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsSecondRow}>
                                                    <Text style={styles.secondRowTextHeader}>
                                                        No-show
                                                    </Text>
                                                    <Text style={styles.secondRowTextBody}>
                                                        4
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsSecondRow}>
                                                    <Text style={styles.secondRowTextHeader}>
                                                        Cancellation
                                                    </Text>
                                                    <Text style={styles.secondRowTextBody}>
                                                        100
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        {/* Third Row */}
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                            }}
                                        >
                                            <TouchableOpacity
                                                style={[
                                                    styles.reservationProfileHistoryButton,
                                                    {
                                                        marginLeft: 30,
                                                        marginBottom: 10,
                                                        borderTopLeftRadius: 5,
                                                        borderBottomLeftRadius: 5,
                                                        borderWidth: 1,
                                                        borderColor: 'rgb(30, 134, 151)'
                                                    },
                                                    showProfile ? { backgroundColor: 'rgb(30, 134, 151)', }
                                                        : { backgroundColor: Colors.whiteColor },
                                                ]}
                                                onPress={() => {
                                                    setShowProfile(true)
                                                    setShowHistory(false)
                                                }}
                                            >
                                                <Text
                                                    style={[{
                                                        textAlign: 'center',
                                                    },
                                                    showProfile ? { color: Colors.whiteColor } : { color: 'rgb(30, 134, 151)' }
                                                    ]}>
                                                    Profile
                                                </Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={[
                                                    styles.reservationProfileHistoryButton,
                                                    {
                                                        marginBottom: 10,
                                                        borderTopRightRadius: 5,
                                                        borderBottomRightRadius: 5,
                                                        borderWidth: 1,
                                                        borderColor: 'rgb(30, 134, 151)'
                                                    },
                                                    showHistory ? { backgroundColor: 'rgb(30, 134, 151)' }
                                                        : { backgroundColor: Colors.whiteColor },
                                                ]}
                                                onPress={() => {
                                                    setShowProfile(false)
                                                    setShowHistory(true)
                                                }}
                                            >
                                                <Text
                                                    style={[{
                                                        textAlign: 'center',
                                                    },
                                                    showHistory ? { color: Colors.whiteColor } : { color: 'rgb(30, 134, 151)' }
                                                    ]}>
                                                    History
                                                </Text>
                                            </TouchableOpacity>
                                        </View>
                                        {/* Fourth Row */}
                                        <View style={
                                            {
                                                flexDirection: 'row',
                                                marginBottom: 10,
                                                justifyContent: 'center'
                                            }}>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsFourthRow}>
                                                    <Text style={styles.fourthRowTextHeader}>
                                                        Current Balance
                                                    </Text>
                                                    <Text style={styles.fourthRowTextBody}>
                                                        RM 0.00
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsFourthRow}>
                                                    <Text style={styles.fourthRowTextHeader}>
                                                        Credit Redeemed
                                                    </Text>
                                                    <Text style={styles.fourthRowTextBody}>
                                                        RM 0.00
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <View style={styles.reservationDetailsFourthRow}>
                                                    <Text style={styles.fourthRowTextHeader}>
                                                        Campaigns Available
                                                    </Text>
                                                    <Text style={styles.fourthRowTextBody}>
                                                        0
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        {/* Fifth Row */}
                                        <View style={{ flexDirection: 'row' }}>
                                            {/* Left Side */}
                                            <View style={{ flexDirection: 'column' }}>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                            Guest Tags
                                                        </Text>
                                                    </View>
                                                    <View style={{ flex: 1 }}>
                                                        <TouchableOpacity onPress={() => {
                                                            props.navigation.navigate('DetailsGuestTags')
                                                        }}>
                                                            <Text style={styles.editButton}>
                                                                Edit
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row', alignItems: 'center' }]}>
                                                    <Text style={{
                                                        color: Colors.whiteColor,
                                                        borderWidth: 1,
                                                        borderColor: 'blue',
                                                        borderRadius: 5,
                                                        backgroundColor: 'blue',
                                                        height: windowHeight * 0.04,
                                                        width: windowWidth * 0.06,
                                                        textAlign: 'center',
                                                        marginLeft: 10
                                                    }}>
                                                        Big Spender
                                                    </Text>
                                                    <Text style={{
                                                        color: Colors.whiteColor,
                                                        borderWidth: 1,
                                                        borderColor: 'blue',
                                                        borderRadius: 5,
                                                        backgroundColor: 'blue',
                                                        height: windowHeight * 0.04,
                                                        width: windowWidth * 0.06,
                                                        textAlign: 'center',
                                                        marginLeft: 10
                                                    }}>
                                                        Big Spender
                                                    </Text>
                                                </View>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row', }]}>
                                                    <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                        Automatic Tags
                                                    </Text>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row', alignItems: 'center' }]}>
                                                    <Text style={{
                                                        color: Colors.whiteColor,
                                                        borderWidth: 1,
                                                        borderColor: 'orange',
                                                        borderRadius: 5,
                                                        backgroundColor: 'orange',
                                                        height: windowHeight * 0.04,
                                                        width: windowWidth * 0.06,
                                                        textAlign: 'center',
                                                        marginTop: 10,
                                                        marginLeft: 10
                                                    }}>
                                                        Regular
                                                    </Text>
                                                </View>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                            Personal
                                                        </Text>
                                                    </View>
                                                    <View style={{ flex: 1 }}>
                                                        <TouchableOpacity onPress={() => {
                                                            personalExpand ?
                                                                setPersonalExpand(false) : setPersonalExpand(true)
                                                        }}>
                                                            <Text style={styles.editButton}>
                                                                {personalExpand ? "Collapse" : "Expand"}
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row' },
                                                personalExpand ? { height: windowHeight * 0.35 } :
                                                    { height: windowHeight * 0.13 }
                                                ]}>
                                                    {/* Expand button clicked */}
                                                    {personalExpand ?
                                                        <View style={{ flexDirection: 'column', marginLeft: 10, marginTop: 10 }}>
                                                            <RadioForm
                                                                formHorizontal={true}
                                                                animation={true}
                                                            >
                                                                {/* To create radio buttons, loop through your array of options */}
                                                                {
                                                                    genderRadio.map((obj, i) => (
                                                                        <RadioButton labelHorizontal={true} key={i} >
                                                                            <RadioButtonLabel
                                                                                obj={obj}
                                                                                index={i}
                                                                                labelHorizontal={true}
                                                                                onPress={() => {
                                                                                    setGender(obj.value)
                                                                                    setSelectGender(i)
                                                                                }}
                                                                                labelStyle={{ fontSize: 14, color: 'gray' }}
                                                                                labelWrapStyle={{}}
                                                                            />
                                                                            {/*  You can set RadioButtonLabel before RadioButtonInput */}
                                                                            <RadioButtonInput
                                                                                obj={obj}
                                                                                index={i}
                                                                                isSelected={selectGender === i}
                                                                                onPress={() => {
                                                                                    setGender(obj.value)
                                                                                    setSelectGender(i)
                                                                                    // console.log('i: ', selectGender)
                                                                                    // console.log('gender: ', gender)
                                                                                }}
                                                                                borderWidth={1}
                                                                                buttonInnerColor={'rgb(30, 134, 151)'}
                                                                                buttonOuterColor={selectGender === i ? '#2196f3' : '#000'}
                                                                                buttonSize={15}
                                                                                buttonOuterSize={30}
                                                                                buttonStyle={{}}
                                                                                buttonWrapStyle={{ marginLeft: 10 }}
                                                                            />
                                                                        </RadioButton>
                                                                    ))
                                                                }
                                                            </RadioForm>
                                                            <Text style={{ color: 'gray' }}>
                                                                First Name
                                                            </Text>
                                                            <TextInput
                                                                style={styles.information}
                                                                editable={personalExpand ? true : false}
                                                                color='black'
                                                                onChangeText={(text) => { setFirstName(text) }}
                                                                value={firstName}
                                                                underlineColorAndroid='#D3D3D3'
                                                            >
                                                                Ryan
                                                            </TextInput>
                                                            <Text style={{ color: 'gray' }}>
                                                                Last Name
                                                            </Text>
                                                            <TextInput
                                                                style={styles.information}
                                                                editable={personalExpand ? true : false}
                                                                color='black'
                                                                onChangeText={(text) => { setLastName(text) }}
                                                                value={lastName}
                                                                underlineColorAndroid='#D3D3D3'
                                                            >
                                                                Lee
                                                            </TextInput>
                                                            <View style={{ flexDirection: 'row', marginBottom: 10 }}>
                                                                <TextInput
                                                                    style={styles.information}
                                                                    editable={personalExpand ? true : false}
                                                                    color='black'
                                                                    placeholder='Birthday'
                                                                    onChangeText={(text) => { setBirthday(text) }}
                                                                    value={birthday}
                                                                    underlineColorAndroid='#D3D3D3'
                                                                />
                                                                <TextInput
                                                                    style={{ marginLeft: 150 }}
                                                                    editable={personalExpand ? true : false}
                                                                    color='black'
                                                                    placeholder='Age'
                                                                    onChangeText={(text) => { setAge(text) }}
                                                                    value={age}
                                                                    underlineColorAndroid='#D3D3D3'
                                                                />
                                                                <MaterialCommunityIcons
                                                                    name='lock'
                                                                    size={15}
                                                                    style={{ marginTop: 20 }}
                                                                />
                                                            </View>
                                                            <TextInput
                                                                style={styles.information}
                                                                editable={personalExpand ? true : false}
                                                                color='black'
                                                                placeholder='Anniversary'
                                                                onChangeText={(text) => { setAnniversary(text) }}
                                                                value={anniversary}
                                                                underlineColorAndroid='#D3D3D3'
                                                            />
                                                        </View>
                                                        :
                                                        <View style={{ flexDirection: 'column', marginLeft: 10, marginTop: 10 }}>
                                                            <RadioForm
                                                                formHorizontal={true}
                                                                animation={true}
                                                            >
                                                                {/* To create radio buttons, loop through your array of options */}
                                                                {
                                                                    genderRadio.map((obj, i) => (
                                                                        <RadioButton labelHorizontal={true} key={i} >
                                                                            <RadioButtonLabel
                                                                                obj={obj}
                                                                                index={i}
                                                                                labelHorizontal={true}
                                                                                onPress={() => {
                                                                                    setGender(obj.value)
                                                                                    setSelectGender(i)
                                                                                }}
                                                                                labelStyle={{ fontSize: 14, color: 'gray' }}
                                                                                labelWrapStyle={{}}
                                                                            />
                                                                            {/*  You can set RadioButtonLabel before RadioButtonInput */}
                                                                            <RadioButtonInput
                                                                                obj={obj}
                                                                                index={i}
                                                                                isSelected={selectGender === i}
                                                                                onPress={() => {
                                                                                    setGender(obj.value)
                                                                                    setSelectGender(i)
                                                                                    // console.log('i: ', selectGender)
                                                                                    // console.log('gender: ', gender)
                                                                                }}
                                                                                borderWidth={1}
                                                                                buttonInnerColor={'rgb(30, 134, 151)'}
                                                                                buttonOuterColor={selectGender === i ? '#2196f3' : '#000'}
                                                                                buttonSize={15}
                                                                                buttonOuterSize={30}
                                                                                buttonStyle={{}}
                                                                                buttonWrapStyle={{ marginLeft: 10 }}
                                                                            />
                                                                        </RadioButton>
                                                                    ))
                                                                }
                                                            </RadioForm>

                                                            <Text style={{ color: 'gray' }}>
                                                                First Name
                                                            </Text>
                                                            <TextInput
                                                                style={styles.information}
                                                                editable={informationEdit ? true : false}
                                                                color='black'
                                                                onChangeText={(text) => { setFirstName(text) }}
                                                                value={firstName}
                                                                underlineColorAndroid='#D3D3D3'
                                                            >
                                                                Ryan
                                                            </TextInput>

                                                        </View>
                                                    }
                                                </View>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                        Membership
                                                    </Text>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row', alignItems: 'center' }]}>
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                            Loyalty member
                                                        </Text>
                                                    </View>
                                                    <View style={{ flex: 1 }}>
                                                        <Switch
                                                            width={42}
                                                            style={{
                                                                marginRight: 10,
                                                                alignSelf: 'flex-end'
                                                            }}
                                                            value={loyaltyMember}
                                                            onSyncPress={(statusTemp) =>
                                                                // setState({ status: status })
                                                                setLoyaltyMember(statusTemp)
                                                            }
                                                            circleColorActive={Colors.whiteColor}
                                                            circleColorInactive={Colors.fieldtTxtColor}
                                                            backgroundActive={Colors.tabCyan}
                                                        />
                                                    </View>
                                                </View>

                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                        Tier
                                                    </Text>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row', alignItems: 'center' }]}>
                                                    <TouchableOpacity
                                                        style={{
                                                            borderWidth: 1,
                                                            borderColor: '#D3D3D3',
                                                            borderRadius: 5,
                                                            justifyContent: 'center',
                                                            marginLeft: 10,
                                                            height: windowHeight * 0.05,
                                                            width: windowWidth * 0.2
                                                        }}
                                                        onPress={() => {
                                                            setTierModal(true)
                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flex: 1 }}>
                                                                <Text style={{
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    marginLeft: 10
                                                                }}>
                                                                    {tier}
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 1 }}>
                                                                <MaterialIcons
                                                                    name='keyboard-arrow-down'
                                                                    size={25}
                                                                    color='#D3D3D3'
                                                                    style={{ alignSelf: 'flex-end', marginRight: 5 }}
                                                                />
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>

                                            </View>
                                            {/* Right Side */}
                                            <View style={{ flexDirection: 'column' }}>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                            Guest Notes
                                                        </Text>
                                                    </View>
                                                    <View style={{ flex: 1 }}>
                                                        <TouchableOpacity onPress={() => {
                                                            guestNotesEdit ? setGuestNotesEdit(false)
                                                                : setGuestNotesEdit(true)
                                                        }}>
                                                            <Text style={styles.editButton}>
                                                                {guestNotesEdit ? 'Done' : 'Edit'}
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { justifyContent: 'center' }]}>
                                                    <TextInput
                                                        editable={guestNotesEdit ? true : false}
                                                        onChangeText={(text) => { setGuestNotes(text) }}
                                                        value={guestNotes}
                                                        color='black'
                                                        placeholder='No guest notes have been added'
                                                    />
                                                </View>
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <View style={{ flex: 1 }}>
                                                        <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                            Communications
                                                        </Text>
                                                    </View>
                                                    <View style={{ flex: 1 }}>
                                                        <TouchableOpacity onPress={() => {
                                                            communicationExpand ? setCommunicationExpand(false)
                                                                : setCommunicationExpand(true)
                                                        }}>
                                                            <Text style={styles.editButton}>
                                                                {communicationExpand ? "Collapse" : "Expand"}
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                                {/* Expand button clicked */}
                                                {communicationExpand ?
                                                    <View style={[styles.reservationsContainerBelow,
                                                    { height: windowHeight * 0.38, }]}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text style={styles.informationTitle}>
                                                                Language
                                                            </Text>
                                                            <DropDownPicker
                                                                arrowColor={'#BDBDBD'}
                                                                arrowSize={23}
                                                                // arrowStyle={{paddingVertical:0}}
                                                                style={{
                                                                    width: windowWidth * 0.28,
                                                                    marginLeft: 10,
                                                                    marginTop: 5,
                                                                    paddingVertical: 15,
                                                                }}
                                                                dropDownStyle={{
                                                                    width: windowWidth * 0.28,
                                                                    marginLeft: 10,
                                                                }}
                                                                itemStyle={{ justifyContent: 'flex-start' }}
                                                                // placeholderStyle={{color: 'black'}}
                                                                // placeholder={'testing'}
                                                                items={[
                                                                    { label: 'English', value: 'English' },
                                                                    { label: 'Malay', value: 'Malay' },
                                                                    { label: 'Mandarin', value: 'Mandarin' },
                                                                ]}
                                                                value={language}
                                                                labelStyle={{ fontSize: 12.5 }}
                                                                onChangeItem={(item) => {
                                                                    setLanguage(item.value)
                                                                }}
                                                                globalTextStyle={{
                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                    color: Colors.fontDark,
                                                                    marginLeft: 5,
                                                                }}
                                                            />
                                                        </View>
                                                        <TextInput
                                                            style={{ marginLeft: 7, borderBottomWidth: 0.5, borderBottomColor: '#D3D3D3' }}
                                                            onChangeText={(text) => { setEmail(text) }}
                                                            value={email}
                                                            color='black'
                                                            placeholder='Email'

                                                        />
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text style={styles.informationTitle}>
                                                                    Code
                                                                </Text>
                                                                <TouchableOpacity
                                                                    onPress={() => { setShowCountryCode(true) }}
                                                                >
                                                                    <Text style={{
                                                                        marginLeft: 10,
                                                                        paddingTop: 10
                                                                    }}>
                                                                        {countryCode}
                                                                    </Text>
                                                                </TouchableOpacity>
                                                            </View>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text style={styles.informationTitle}>
                                                                    Phone
                                                                </Text>
                                                                <TextInput
                                                                    keyboardType='numeric'
                                                                    value={phoneNumber}
                                                                    onChangeText={(text) => { setPhoneNumber(text) }}
                                                                    underlineColorAndroid='#D3D3D3'
                                                                    style={{
                                                                        paddingBottom: 3,
                                                                        width: windowWidth * 0.2
                                                                    }}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginBottom: 20 }}>
                                                            <View style={{ flex: 1 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive E-mail marketing
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 1 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveEmail}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveEmail(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginBottom: 20 }}>
                                                            <View style={{ flex: 1 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive SMS marketing
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 1 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveSMS}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveSMS(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flex: 1.5 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive marketing automation
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 0.5 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveMarketing}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveMarketing(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                    </View>
                                                    :
                                                    <View style={[styles.reservationsContainerBelow,
                                                    { height: windowHeight * 0.30, }]}>
                                                        <Text style={styles.informationTitle}>
                                                            Language
                                                        </Text>
                                                        <DropDownPicker
                                                            arrowColor={'#BDBDBD'}
                                                            arrowSize={23}
                                                            // arrowStyle={{paddingVertical:0}}
                                                            style={{
                                                                width: windowWidth * 0.28,
                                                                marginLeft: 10,
                                                                marginTop: 5,
                                                                paddingVertical: 15,
                                                            }}
                                                            dropDownStyle={{
                                                                width: windowWidth * 0.28,
                                                                marginLeft: 10,
                                                                paddingVertical: 0,
                                                            }}
                                                            itemStyle={{ justifyContent: 'flex-start' }}
                                                            // placeholderStyle={{color: 'black'}}
                                                            // placeholder={'testing'}
                                                            items={[
                                                                { label: 'English', value: 'English' },
                                                                { label: 'Malay', value: 'Malay' },
                                                                { label: 'Mandarin', value: 'Mandarin' },
                                                            ]}
                                                            value={language}
                                                            labelStyle={{ fontSize: 12.5 }}
                                                            onChangeItem={(item) => {
                                                                setLanguage(item.value)
                                                            }}
                                                            globalTextStyle={{
                                                                fontFamily: 'NunitoSans-SemiBold',
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                color: Colors.fontDark,
                                                                marginLeft: 5,
                                                            }}
                                                        />
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text style={styles.informationTitle}>
                                                                    Code
                                                                </Text>
                                                                <Text style={{
                                                                    marginLeft: 10,
                                                                    paddingTop: 10
                                                                }}>
                                                                    {countryCode}
                                                                </Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text style={styles.informationTitle}>
                                                                    Phone
                                                                </Text>
                                                                <TextInput
                                                                    keyboardType='numeric'
                                                                    value={phoneNumber}
                                                                    onChangeText={(text) => { setPhoneNumber(text) }}
                                                                    underlineColorAndroid='#D3D3D3'
                                                                    style={{
                                                                        paddingBottom: 3,
                                                                        width: windowWidth * 0.2
                                                                    }}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginBottom: 20 }}>
                                                            <View style={{ flex: 1 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive E-mail marketing
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 1 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveEmail}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveEmail(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginBottom: 20 }}>
                                                            <View style={{ flex: 1 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive SMS marketing
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 1 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveSMS}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveSMS(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flex: 1.5 }}>
                                                                <Text style={{ color: 'gray', marginLeft: 10 }}>
                                                                    Receive marketing automation
                                                                </Text>
                                                            </View>
                                                            <View style={{ flex: 0.5 }}>
                                                                <Switch
                                                                    width={42}
                                                                    style={{
                                                                        marginRight: 10,
                                                                        alignSelf: 'flex-end'
                                                                    }}
                                                                    value={receiveMarketing}
                                                                    onSyncPress={(statusTemp) =>
                                                                        // setState({ status: status })
                                                                        setReceiveMarketing(statusTemp)
                                                                    }
                                                                    circleColorActive={Colors.whiteColor}
                                                                    circleColorInactive={Colors.fieldtTxtColor}
                                                                    backgroundActive={Colors.tabCyan}
                                                                />
                                                            </View>
                                                        </View>
                                                    </View>
                                                }
                                                <View style={[styles.reservationsContainer,
                                                { flexDirection: 'row' }]}>
                                                    <Text style={{ marginLeft: 10, color: 'gray' }}>
                                                        Professional
                                                    </Text>
                                                    <TouchableOpacity onPress={() => {
                                                        professionalExpand ? setProfessionalExpand(false)
                                                            : setProfessionalExpand(true)
                                                    }}>
                                                        <Text style={[styles.editButton, { marginLeft: 230 }]}>
                                                            {professionalExpand ? "Collapse" : "Expand"}
                                                        </Text>
                                                    </TouchableOpacity>
                                                </View>
                                                <View style={[styles.reservationsContainerBelow,
                                                { flexDirection: 'row' }]}>
                                                    <Text>

                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    </ScrollView>
                                    :
                                    // History
                                    <ScrollView>
                                        <View style={{ flexDirection: 'column' }}>
                                            <View style={{ flexDirection: 'row', marginTop: 10, marginBottom: 10 }}>
                                                <TouchableOpacity
                                                    style={[
                                                        styles.reservationProfileHistoryButton,
                                                        {
                                                            marginLeft: 30,
                                                            marginBottom: 10,
                                                            borderTopLeftRadius: 5,
                                                            borderBottomLeftRadius: 5,
                                                            borderWidth: 1,
                                                            borderColor: 'rgb(30, 134, 151)'
                                                        },
                                                        showProfile ? { backgroundColor: 'rgb(30, 134, 151)', }
                                                            : { backgroundColor: Colors.whiteColor },
                                                    ]}
                                                    onPress={() => {
                                                        setShowProfile(true)
                                                        setShowHistory(false)
                                                    }}
                                                >
                                                    <Text
                                                        style={[{
                                                            textAlign: 'center',
                                                        },
                                                        showProfile ? { color: Colors.whiteColor } : { color: 'rgb(30, 134, 151)' }
                                                        ]}>
                                                        Profile
                                                    </Text>
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    style={[
                                                        styles.reservationProfileHistoryButton,
                                                        {
                                                            marginBottom: 10,
                                                            borderTopRightRadius: 5,
                                                            borderBottomRightRadius: 5,
                                                            borderWidth: 1,
                                                            borderColor: 'rgb(30, 134, 151)'
                                                        },
                                                        showHistory ? { backgroundColor: 'rgb(30, 134, 151)' }
                                                            : { backgroundColor: Colors.whiteColor },
                                                    ]}
                                                    onPress={() => {
                                                        setShowProfile(false)
                                                        setShowHistory(true)
                                                    }}
                                                >
                                                    <Text
                                                        style={[{
                                                            textAlign: 'center',
                                                        },
                                                        showHistory ? { color: Colors.whiteColor } : { color: 'rgb(30, 134, 151)' }
                                                        ]}>
                                                        History
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderTopLeftRadius: 5,
                                                borderTopRightRadius: 5,
                                                marginLeft: 10,
                                                width: windowWidth * 0.63,
                                                height: windowHeight * 0.05,
                                                justifyContent: 'space-between',
                                                alignItems: 'center',
                                                borderColor: 'gray'
                                            }}>
                                                <View style={{ flex: 1 }}>
                                                    <Text style={{ marginLeft: 10 }}>
                                                        Past
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1, justifyContent: 'flex-end' }}>
                                                    <Text style={{ color: 'gray', }}>
                                                        Total:
                                                    </Text>
                                                </View>
                                            </View>
                                            <View style={{
                                                flexDirection: 'row',
                                                borderLeftWidth: 1,
                                                borderRightWidth: 1,
                                                marginLeft: 10,
                                                width: windowWidth * 0.63,
                                                height: windowHeight * 0.05,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                backgroundColor: 'light-gray',
                                            }}>
                                                <View style={{ flex: 2, justifyContent: 'flex-start' }}>
                                                    <Text style={{ color: 'gray', marginLeft: 10, marginRight: 20 }}>
                                                        {`Date & Time`}
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1.5, justifyContent: 'center' }}>
                                                    <Text style={{ color: 'gray', marginRight: 20 }}>
                                                        Type
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1, justifyContent: 'center' }}>
                                                    <Text style={{ color: 'gray', marginRight: 20 }}>
                                                        Guest
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1, justifyContent: 'center' }}>
                                                    <Text style={{ color: 'gray', marginRight: 20 }}>
                                                        Notes
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1, justifyContent: 'center' }}>
                                                    <Text style={{ color: 'gray', marginRight: 20 }}>
                                                        Amount
                                                    </Text>
                                                </View>
                                                <View style={{ flex: 1, justifyContent: 'center' }}>
                                                    <Text style={{ color: 'gray' }}>
                                                        Venue
                                                    </Text>
                                                </View>
                                            </View>
                                            <FlatList
                                                style={{
                                                    height: windowHeight * 0.5,
                                                    marginLeft: 10,
                                                    width: windowWidth * 0.63,
                                                    // paddingVertical: 20,
                                                    // paddingHorizontal: 15,
                                                    //marginTop: 10,
                                                    borderBottomLeftRadius: 5,
                                                    borderBottomRightRadius: 5,
                                                    backgroundColor: 'white',
                                                    borderColor: 'gray'
                                                }}
                                                nestedScrollEnabled={true}
                                                showsVerticalScrollIndicator={false}
                                                data={historyData}
                                                renderItem={renderHistoryData}
                                            // data={suppliers.filter((item) => {
                                            //     if (search !== '') {
                                            //     return item.name
                                            //         .toLowerCase()
                                            //         .includes(search.toLowerCase());
                                            //     } else {
                                            //     return true;
                                            //     }
                                            // })}
                                            // extraData={suppliers.filter((item) => {
                                            //     if (search !== '') {
                                            //     return item.name
                                            //         .toLowerCase()
                                            //         .includes(search.toLowerCase());
                                            //     } else {
                                            //     return true;
                                            //     }
                                            // })}
                                            // renderItem={renderOrderItem}
                                            // keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>

                                    </ScrollView>
                                }
                            </View>
                        }
                    </ScrollView>
                </View>
            </View>


            {/* Modal Tier */}
            <ModalView supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1, }}
                visible={tierModal}
                transparent={true}>
                <View style={[styles.modalContainer]}>
                    <View
                        style={[
                            styles.modalView, { height: windowHeight * 0.4 }
                        ]}>
                        <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderColor: '#D3D3D3' }}>
                            <View style={{ flex: 0.5 }}>
                                <TouchableOpacity
                                    style={{
                                        alignItems: 'center'
                                    }}
                                    onPress={() => {
                                        setTierModal(false);
                                    }}>
                                    <Text style={{
                                        fontFamily: 'NunitoSans',
                                        textAlign: 'center',
                                        fontSize: switchMerchant ? 16 : 18,
                                        color: 'gray'
                                    }}>
                                        Reset
                                    </Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ flex: 1.5, marginBottom: 10 }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'center',
                                    fontSize: switchMerchant ? 18 : 20,
                                }}>
                                    Loyalty Tiers
                                </Text>
                            </View>
                            <View style={{ flex: 0.5 }}>
                                <TouchableOpacity onPress={() => { setTierModal(false) }}>
                                    <AIcon
                                        name="closecircle"
                                        size={switchMerchant ? 15 : 25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ alignSelf: 'flex-end', marginRight: 20 }}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={{
                            flex: 1,
                            marginTop: 10,
                            borderBottomWidth: 1,
                            borderColor: '#D3D3D3',
                            justifyContent: 'center'
                        }}>
                            <View style={{ flexDirection: 'row' }}>
                                <View style={{ flex: 1 }}>
                                    <Text style={{ marginLeft: 10 }}>
                                        Silver Member
                                    </Text>
                                </View>
                                <View style={{ flex: 0.8 }}>
                                    <MaterialCommunityIcons
                                        name='lock'
                                        size={20}
                                        style={{ alignSelf: 'flex-end', paddingTop: 5 }}
                                    />
                                </View>
                                <View style={{ flex: 0.2 }}>
                                    <CheckBox
                                        width={42}
                                        style={{
                                            marginRight: 10,
                                            alignSelf: 'flex-end',
                                            borderRadius: 15
                                        }}
                                        value={checkSilverTier}
                                        onValueChange={() =>
                                        // setState({ status: status })
                                        {
                                            setTier('Silver Member')
                                            setCheckGoldenTier(false)
                                            setCheckSilverTier(true)
                                        }
                                        }
                                    />
                                </View>
                            </View>
                        </View>
                        <View style={{
                            flex: 1,
                            marginTop: 10,
                            borderBottomWidth: 1,
                            borderColor: '#D3D3D3',
                            justifyContent: 'center'
                        }}>
                            <View style={{ flexDirection: 'row' }}>
                                <View style={{ flex: 1 }}>
                                    <Text style={{ marginLeft: 10 }}>
                                        Golden Member
                                    </Text>
                                </View>
                                <View style={{ flex: 1 }}>
                                    <CheckBox
                                        width={42}
                                        style={{
                                            marginRight: 10,
                                            alignSelf: 'flex-end',
                                            borderRadius: 15
                                        }}
                                        value={checkGoldenTier}
                                        onValueChange={() => {
                                            // setState({ status: status })
                                            setCheckSilverTier(false)
                                            setCheckGoldenTier(true)
                                            setTier("Golden Member")
                                        }
                                        }
                                    />
                                </View>
                            </View>
                        </View>
                        <View style={{ flex: 1 }}></View>
                        <View style={{ flex: 1 }}></View>
                        <View style={{ flex: 1 }}></View>
                        <View style={{ flex: 1 }}></View>

                    </View>
                </View>
            </ModalView>

            {/* History Notes Modal */}
            <ModalView supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={showNotesModal}
                transparent={true}>
                <View style={styles.modalContainer}>
                    <View
                        style={[
                            styles.modalView,
                            { height: windowHeight * 0.6 },
                        ]}>
                        <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderColor: '#D3D3D3' }}>
                            <View style={{ flex: 0.5 }}>
                            </View>
                            <View style={{ flex: 1.5, marginBottom: 20 }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'center',
                                    fontSize: switchMerchant ? 18 : 20,
                                }}>
                                    Notes
                                </Text>
                            </View>
                            <View style={{ flex: 0.5 }}>
                                <TouchableOpacity onPress={() => { setShowNotesModal(false) }}>
                                    <AIcon
                                        name="closecircle"
                                        size={switchMerchant ? 15 : 25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ alignSelf: 'flex-end', marginRight: 20 }}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={{
                            marginLeft: 10, marginTop: 10
                        }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>
                                Reservation Notes (data)
                            </Text>
                        </View>
                    </View>
                </View>
            </ModalView>

            {/* Manual Entry Modal */}
            <ModalView supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={manualEntry}
                transparent={true}>
                <View style={styles.modalContainer}>
                    <View
                        style={[
                            styles.modalView,
                            { height: windowHeight * 0.5 },
                        ]}>
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                                setManualEntry(false);
                            }}>
                            <AIcon
                                name="closecircle"
                                size={switchMerchant ? 15 : 25}
                                color={Colors.fieldtTxtColor}
                            />
                        </TouchableOpacity>

                        <View style={{ alignContent: 'center', justifyContent: 'center' }}>
                            <Coins
                                style={{
                                    width: windowHeight * 0.035,
                                    height: windowHeight * 0.035,
                                    alignSelf: 'center',
                                }}
                            />
                            <Text style={{ marginTop: 10, alignSelf: 'center' }}>
                                Enter bill amount to finish reservation
                            </Text>
                            <Text style={{ marginTop: 10, color: 'gray', alignSelf: 'center' }}>
                                One last thing. Enter the final bill amount for the table
                            </Text>
                            <View style={{ flexDirection: 'row', marginTop: 10, alignSelf: 'center' }}>
                                <Text>
                                    RM
                                </Text>
                                <TextInput
                                    style={{
                                        height: 20,
                                        borderBottomWidth: 1,
                                        width: windowWidth * 0.05
                                    }}
                                    onChangeText={text => setBill(parseValidPriceText(text))}
                                    value={bill}
                                    placeholder='0.00'
                                    keyboardType='numeric'
                                />
                            </View>
                            <View style={{ flexDirection: 'row', marginTop: 10 }}>
                                <View style={{ flex: 1.3 }}>
                                    <Text style={{ alignSelf: 'flex-end' }}>
                                        Claim loyalty credits for this bill?
                                    </Text>
                                </View>
                                <View style={{ flex: 0.7 }}>
                                    <Switch
                                        width={42}
                                        style={{
                                            alignSelf: 'flex-start',
                                            marginLeft: 15
                                        }}
                                        value={loyaltyStatus}
                                        onSyncPress={(statusTemp) =>
                                            // setState({ status: status })
                                            setLoyaltyStatus(statusTemp)
                                        }
                                        circleColorActive={Colors.whiteColor}
                                        circleColorInactive={Colors.fieldtTxtColor}
                                        backgroundActive={Colors.tabCyan}
                                    />
                                </View>
                            </View>
                            <View style={{ flexDirection: 'row', alignSelf: 'center' }}>
                                <Text style={
                                    {
                                        marginTop: 10,
                                        color: 'green',
                                        textDecorationLine: 'underline'
                                    }}>
                                    <EvilIcons
                                        name='pencil'
                                        size={25}
                                        color={'green'}
                                    />
                                    Add Notes
                                </Text>
                            </View>
                            <TextInput
                                style={{
                                    height: 40,
                                    borderColor: 'gray',
                                    borderWidth: 1,
                                    marginTop: 10,
                                    width: windowWidth * 0.25,
                                    alignSelf: 'center'
                                }}
                                onChangeText={text => setAddNotes(text)}
                                value={addNotes}
                                placeholder=''
                            />
                            <TouchableOpacity style={styles.submitButton}
                                onPress={() => { setManualEntry(false) }}
                            >
                                <Text style={{ color: 'white' }}>
                                    Submit
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>

            </ModalView>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    topBar: {
        flexDirection: 'row',
        height: Dimensions.get('window').height * 0.11,
        width: Dimensions.get('window').width * 0.785,
        backgroundColor: Colors.lightGrey,
        justifyContent: 'flex-start',
    },
    topBarButton: {
        padding: 5,
        backgroundColor: Colors.lightGrey,
        width: Dimensions.get('window').width * 0.08,
        justifyContent: 'center',
    },
    search: {
        margin: 3,
        width: Dimensions.get('window').width * 0.30,
        backgroundColor: 'white',
        borderRadius: 7,
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1,
        borderColor: '#E5E5E5',
    },
    finished: {
        margin: 3,
        width: Dimensions.get('window').width * 0.06,
        height: Dimensions.get('window').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: '#90ee90',
    },
    noShow: {
        margin: 3,
        width: Dimensions.get('window').width * 0.06,
        height: Dimensions.get('window').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: 'black',
    },
    cancel: {
        margin: 3,
        width: Dimensions.get('window').width * 0.06,
        height: Dimensions.get('window').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 18,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
        borderWidth: 1.5,
        borderColor: '#808080',
    },
    export: {
        margin: 3,
        width: Dimensions.get('window').width * 0.06,
        height: Dimensions.get('window').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 10,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    filter: {
        margin: 3,
        width: Dimensions.get('window').width * 0.06,
        height: Dimensions.get('window').height * 0.04,
        backgroundColor: 'white',
        borderRadius: 10,
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    flatListHeader: {
        height: 70,
        marginTop: 10,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.91,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        //marginTop: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        backgroundColor: '#D3D3D3',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    flatListBody: {
        height: 70,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.91,
        height: Dimensions.get('window').height * 0.2,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        paddingHorizontal: 15,
        backgroundColor: '#ffffff',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    clickedFlatListHeader: {
        height: 70,
        marginTop: 10,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.25,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        //marginTop: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        backgroundColor: '#D3D3D3',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    table: {
        paddingTop: 5,
        textAlign: 'center',
        width: Dimensions.get('window').width * 0.11,
        fontFamily: 'NunitoSans-Bold',
    },
    tableFirst: {
        paddingTop: 5,
        textAlign: 'left',
        width: Dimensions.get('window').width * 0.11,
        fontFamily: 'NunitoSans-Bold',
    },
    flatList: {
        height: 100,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.91,
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    clickedFlatList: {
        height: 100,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.25,
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    detailsContainer: {
        height: Dimensions.get('window').width * 0.54,
        margin: 10,
        width: Dimensions.get('window').width * 0.63,
        // flexDirection: 'row',
        // paddingVertical: 20,
        // paddingHorizontal: 15,
        //marginTop: 10,
        borderRadius: 10,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    detailsButton: {
        padding: 5,
        backgroundColor: Colors.lightGrey,
        width: Dimensions.get('window').width * 0.35,
        height: 70,
        borderRadius: 10,
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    reservationProfileHistoryButton: {
        padding: 5,
        width: Dimensions.get('window').width * 0.30,
        height: 55,
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    changeBackSeatButton: {
        borderRadius: 5,
        borderWidth: 1.5,
        borderColor: 'rgb(30, 134, 151)',
        marginLeft: 20,
        justifyContent: 'center',
        alignItems: 'center',
        width: Dimensions.get('window').width * 0.16,
        height: Dimensions.get('window').height * 0.06,
    },
    guestButtons: {
        width: Dimensions.get('window').width * 0.14,
        height: 70,
        margin: 10,
        // borderWidth: 0,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    reservationsContainer: {
        borderWidth: 0.3,
        borderTopLeftRadius: 2,
        borderTopRightRadius: 2,
        // margin: 10,
        marginRight: 10,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.30,
    },
    reservationsContainerBelow: {
        borderWidth: 0.3,
        borderBottomLeftRadius: 2,
        borderBottomRightRadius: 2,
        marginBottom: 10,
        marginRight: 10,
        marginLeft: 10,
        width: Dimensions.get('window').width * 0.30,
        height: Dimensions.get('window').height * 0.1,
    },
    editButton: {
        marginRight: 10,
        textAlign: 'right',
        color: 'rgb(30, 134, 151)',
    },
    changeGuestButton: {
        borderWidth: 1.5,
        borderColor: 'rgb(30, 134, 151)',
        alignItems: 'center',
        justifyContent: 'center',
        width: Dimensions.get('window').width * 0.08,
        height: Dimensions.get('window').height * 0.04,
        marginLeft: 130,
        borderRadius: 7,
        marginBottom: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').height * 0.7,
        width: Dimensions.get('window').width * 0.4,
        backgroundColor: Colors.whiteColor,
        //borderRadius: windowWidth * 0.03,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.02,
        paddingHorizontal: Dimensions.get('window').width * 0,
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },
    informationTitle: {
        color: '#808080',
        marginLeft: 10,
        marginTop: 10
    },
    information: {
        marginLeft: 10,
        // borderBottomWidth: 1
    },
    submitButton: {
        backgroundColor: 'green',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        marginTop: 10,
        width: Dimensions.get('window').width * 0.25,
        height: Dimensions.get('window').width * 0.04
    },
    reservationDetailsSecondRow: {
        flexDirection: 'column',
        // marginRight: 100
    },
    reservationDetailsFourthRow: {
        flexDirection: 'column',
        // marginRight: 100
    },
    secondRowTextHeader: {
        color: 'gray',
        textAlign: 'center'
    },
    secondRowTextBody: {
        textAlign: 'center',
    },
    fourthRowTextHeader: {
        color: 'gray',
        textAlign: 'center'
    },
    fourthRowTextBody: {
        textAlign: 'center',
    },
    tierDropdown: {
        marginTop: 5,
        marginLeft: 5,
        // alignItems: 'center',
        borderRadius: 10,
        // justifyContent: 'center',
        backgroundColor: Colors.whiteColor,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: 1,
    },
});

export default GuestDetailsScreen;