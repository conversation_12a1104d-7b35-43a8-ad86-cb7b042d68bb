import React, { Component } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Dimensions,
} from "react-native";
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import Icon from "react-native-vector-icons/Ionicons";
import * as Cart from "../util/Cart";
import Styles from "../constant/Styles";
import Icon1 from "react-native-vector-icons/Feather";
import Back from 'react-native-vector-icons/EvilIcons';
var quantity2;
var total;
var options;
var refreshAction = null;

class MenuItemDetailsScreen extends Component {
  constructor({ navigation, props, route }) {
    const { outletData, menuItem, orderType, refresh, cartItem } = route.params;
    super(props);
    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginTop: 30 }}
          onPress={() => {
            this.props.navigation.goBack()
          }}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View style={{ marginLeft: 20 }}>
              <Back
                name="chevron-left"
                size={30}
                color={Colors.whiteColor}
              />
            </View>
            <Text
              style={{
                color: Colors.whiteColor,
                fontSize: 14,
                textAlign: 'center',
                fontFamily: "NunitoSans-Regular"
              }}
            >
              Back
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => <View style={{ marginRight: 15 }}></View>,
      headerTitle: () => (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            marginTop: 30,
          }}
        >
          <Text
            style={{
              fontWeight: "bold",
              fontSize: 20,
              textAlign: "center",
              color: Colors.whiteColor
            }}
          >
            {outletData.name}
          </Text>
        </View>
      ),
    });
    this.state = {
      menuItem: menuItem,
      total: menuItem.price,
      quantity: 1,
      remark: null,
      outletData: outletData,
      visible: false,
      optional: 0,
      optional1: 1,
      quantity1: 1,
      qty: null,
      detail: [],
      cartItem: [],
      choice: null,
      cartItem,
      size: "small",
      clicked: 1,
      clicked1: 0,
      clicked2: 0,
      cartIcon: false,
      expandChoice: false,
    };
    options = cartItem ? cartItem.options : [];
    quantity2 = cartItem ? cartItem.quantity : this.state.quantity;
    total = cartItem
      ? this.setState({ total: cartItem.price })
      : this.state.total;
    refreshAction = refresh;
  }

  componentDidMount() {
    console.log(" menu detail", Cart.getTableNumber())
    ApiClient.GET(API.getItemAddOnChoice + this.state.menuItem.id).then(
      (result) => {
        this.setState({ menuItemDetails: result });
      }
    );

    setInterval(() => {
      this.cartCount();
    }, 5000);
    this.cartCount();
  }

  expandChoice(param) {
    if (this.state.expandChoice == false) {
      return this.setState({ expandChoice: true }), param.expandChoice = true;
    } else {
      return this.setState({ expandChoice: false }), param.expandChoice = false;
    }
  }

  cartCount() {
    if (Cart.getCartItem().length > 0) {
      this.setState({ cartIcon: true })
    }
    else {
      this.setState({ cartIcon: false })
    }
  }

  changeClick() {
    if (this.state.clicked == 1) {
      this.setState({ clicked: 0 })
    }
    else {
      this.setState({ clicked: 1, clicked1: 0, clicked2: 0, size: "small" })
    }
  }

  changeClick1() {
    if (this.state.clicked1 == 1) {
      this.setState({ clicked1: 0 })
    }
    else {
      this.setState({ clicked1: 1, clicked: 0, clicked2: 0, size: "medium" })
    }
  }
  changeClick2() {
    if (this.state.clicked2 == 1) {
      this.setState({ clicked2: 0 })
    }
    else {
      this.setState({ clicked2: 1, clicked: 0, clicked1: 0, size: "big" })
    }
  }

  getCartItem() {
    this.setState({ cartItem: Cart.getCartItem() });
  }

  // function here
  addToCart() {
    Cart.setTax(5);
    if (Cart.getOutletId() != this.state.outletData.id) {
      Cart.setOutletId(this.state.outletData.id);
      //Cart.clearCart();
    }
    options.forEach((element, index) => {
      if (element.quantity == 0) options.splice(index, 1);
    });
    var data = {
      itemId: this.state.menuItem.id,
      quantity: this.state.quantity,
      remarks: this.state.remark,
      options: options,
      image: this.state.menuItem.image,
      name: this.state.menuItem.name,
      price: parseFloat(this.state.total),
      fireOrder: this.state.optional,
      menuItem: this.state.menuItem,
      size: this.state.size
    };
    Cart.setCartItem(data);
    Cart.setOutletId(this.state.outletData.id);
    this.setState({ refresh: true });
    refreshAction();
    Cart.getRefreshCartPage();
  }

  showPrice() {
    if (this.state.price == false) {
      this.setState({ price: true });
    } else {
      this.setState({ price: false });
    }
  }

  addToCart1() {
    Cart.setTax(5);
    if (Cart.getOutletId() != this.state.outletData.id) {
      Cart.setOutletId(this.state.outletData.id);
      Cart.clearCart();
    }
    options.forEach((element, index) => {
      if (element.quantity == 0) options.splice(index, 1);
    });
    var data = {
      itemId: this.state.menuItem.id,
      quantity: this.state.quantity,
      remark: this.state.remark,
      options: options,
      image: this.state.menuItem.image,
      name: this.state.menuItem.name,
      price: parseFloat(this.state.total),
      fireOrder: this.state.optional1,
      menuItem: this.state.menuItem,
    };
    Cart.setCartItem(data);
    Cart.setOutletId(this.state.outletData.id);
    this.setState({ refresh: true });
    refreshAction();
    Cart.getRefreshCartPage();
  }

  containsObject(id) {
    var i;
    for (i = 0; i < options.length; i++) {
      if (options[i].choice === id) {
        return true;
      }
    }
    return false;
  }

  addLeastItem(name, choiceId, price, cat) {
    var i;
    var total = 0;
    var prevChoice = 0;
    for (i = 0; i < options.length; i++) {
      if (options[i].least == true && options[i].cat == cat) {
        prevChoice = options[i].choice
        total =
          parseFloat(this.state.total) -
          options[i].price * this.state.quantity1;
        options.splice(i, 1);
      }
    }
    if (prevChoice != choiceId) {
      var choice = {
        choice: choiceId,
        quantity1: 1,
        itemName: name,
        least: true,
        cat: cat,
        price: price
      };
      this.setState({
        total:
          (total != 0 ? total : parseFloat(this.state.total)) +
          parseFloat(price) * this.state.quantity1,
      });
      options.push(choice);
    } else {
      this.setState({total: total})
    }
    this.setState({ refresh: true });
  }

  addOption(name, quantity, choiceId, price) {
    if (quantity > 0) {
      if (!this.containsObject(choiceId, options)) {
        var choice = {
          choice: choiceId,
          quantity: 1,
          itemName: "Add " + name,
          least: false,
          price: price,
        };
        this.setState({
          total: parseFloat(this.state.total) + parseFloat(price),
        });
        options.push(choice);
      } else {
        var i;
        for (i = 0; i < options.length; i++) {
          if (options[i].choice === choiceId) {
            options[i].quantity = options[i].quantity + 1;
            this.setState({
              total: parseFloat(this.state.total) + parseFloat(price),
            });
          }
        }
      }
    } else {
      var i;
      for (i = 0; i < options.length; i++) {
        if (options[i].choice === choiceId) {
          if (options[i].quantity > 0) {
            options[i].quantity = options[i].quantity + quantity;
            if (options[i] - 1 == 0) {
              options.splice(i, 1);
            }
            this.setState({
              total: parseFloat(this.state.total) - parseFloat(price),
            });
          } else {
            options.splice(i, 1);
          }
        }
      }
    }
  }

  getQuantity(itemId) {
    var i;
    for (i = 0; i < this.state.cartItem.length; i++) {
      if (this.state.cartItem[i].itemId === itemId) {
        quantity = options[i].quantity;
      }
    }
    return quantity;
  }

  getOptionQuantity(choiceId) {
    var quantity = 0;
    var i;
    for (i = 0; i < options.length; i++) {
      if (options[i].choice === choiceId) {
        quantity = options[i].quantity;
      }
    }
    return quantity;
  }

  quantity() {
    if (this.state.quantity == null) {
      this.setState({ quantity: 1 });
      return this.state.quantity
    } else if (this.state.cartItem != null) {
      return quantity2;
    } else {
      return this.state.quantity;
    }
  }
  // onChangeQty(e, id) {
  //   const cartItem = this.state.cartItem;
  //   console.log(cartItem);
  //   const item = cartItem.find((obj) => obj.itemId === id);
  //   item.quantity = e;
  //   this.setState({
  //     cartItem,
  //   });
  // }

  goToCart() {
    if (Cart.getCartItem().length > 0) {
      this.props.navigation.navigate('Cart', {
        test: this.state.test, outletData: this.state.outletData,
        menuItem: this.state.menuItem,
        clicked: this.state.clicked,
        clicked1: this.state.clicked1,
        clicked2: this.state.clicked2,
      })
    } else {
      Alert.alert("Info", "No item in your cart at the moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }
  }
  lessqty(id) {
    if (this.state.cartItem != null) {
      const cartItem = this.state.cartItem;
      cartItem.quantity = cartItem.quantity - 1;
      this.save();
    } else {
      return this.setState({ quantity: this.state.quantity - 1 });
    }
  }

  addqty(id) {
    if (this.state.cartItem != null) {
      const cartItem = this.state.cartItem;
      cartItem.quantity = cartItem.quantity + 1;
      this.save();
    } else {
      return this.setState({ quantity: this.state.quantity + 1 });
    }
  }

  totals() {
    if (this.state.cartItem != null) {
      return this.state.total;
    } else {
      return this.state.total;
    }
  }

  save() {
    if (this.state.cartItem != null) {
      return this.goToCart();
    } else {
      return this.setState({ visible: true });
    }
  }

  // function end

  render() {
    return (
      <View style={{ flex: 1 }}>
        <ScrollView style={styles.container}>
          <View
            style={{
              flexDirection: "row",
              flex: 1,
              alignContent: "center",
              alignItems: "center",
              marginBottom: 30,
            }}
          >
            <View
              style={{
                backgroundColor: Colors.secondaryColor,
                width: 60,
                height: 60,
                borderRadius: 10,
              }}
            >
              <Image
                source={{ uri: this.state.menuItem.image }}
                style={{ width: 60, height: 60, borderRadius: 10 }}
              />
            </View>
            <View style={{ marginLeft: 15, width: "80%" }}>
              <Text
                style={{
                  fontWeight: "600",
                  fontSize: 16,
                  textTransform: "uppercase",
                }}
              >
                {this.state.menuItem.name}
              </Text>
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontWeight: "700",
                  paddingTop: 5,
                  fontSize: 12,
                }}>
                RM{parseFloat(this.state.menuItem.price).toFixed(2)}
              </Text>
            </View>
          </View>
          <View style={{ marginBottom: 15 }}>
            <Text style={{ fontWeight: "700", fontSize: 16 }}>
              Choose size
                </Text>
            <View style={{ marginTop: 15, flexDirection: "row" }}>
              <TouchableOpacity style={{
                width: "30%", height: 40, backgroundColor: this.state.clicked == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
                borderColor: Colors.descriptionColor,
              }}
                onPress={() => { this.changeClick() }}>
                <Text style={{ alignSelf: "center", color: this.state.clicked == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Small</Text>
              </TouchableOpacity>

              <View style={{ width: '5%' }}></View>

              <TouchableOpacity style={{
                width: "30%", height: 40, backgroundColor: this.state.clicked1 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
                borderColor: Colors.descriptionColor,
              }}
                onPress={() => { this.changeClick1() }}>
                <Text style={{ alignSelf: "center", color: this.state.clicked1 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Medium</Text>
              </TouchableOpacity>

              <View style={{ width: '5%' }}></View>

              <TouchableOpacity style={{
                width: "30%", height: 40, backgroundColor: this.state.clicked2 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
                borderColor: Colors.descriptionColor,
              }}
                onPress={() => { this.changeClick2() }}>
                <Text style={{ alignSelf: "center", color: this.state.clicked2 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Big</Text>
              </TouchableOpacity>
            </View>
          </View>
          {this.state.menuItemDetails
            ? this.state.menuItemDetails.itemAddOns.map((item, index) => {
              return (
                (item.name !== "" ? <View style={{ marginBottom: 15 }}>
                  <Text
                    style={{
                      color: Colors.descriptionColor,
                      fontWeight: "500",
                      fontSize: 16,
                      marginBottom: 15,
                    }}
                  >
                    {item.name}
                  </Text>
                  {item.itemAddOnChoices.map((item2, index) => {
                    return (
                      (item2.name !== "" ? <View
                        style={{
                          marginBottom: 20,
                          flexDirection: "row",
                          justifyContent: "space-between",
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          {item.leastSelect == 1 ? (
                            <View
                              style={{
                                flexDirection: 'row',
                                marginRight: 10,
                              }}>

                              <TouchableOpacity
                                style={[
                                  styles.addBtnSmall,
                                  {
                                    backgroundColor:
                                      this.getOptionQuantity(item2.id) > 0
                                        ? Colors.primaryColor
                                        : "#e8e9eb",
                                  },
                                ]}
                                onPress={() => {
                                  console.log("this.getOptionQuantity(item2.id)",-this.getOptionQuantity(item2.id))
                                  if (this.getOptionQuantity(item2.id) >= 1) {
                                    this.addOption(
                                      item2.name,
                                      -this.getOptionQuantity(item2.id),
                                      item2.id,
                                      item2.price,
                                    );
                                  }
                                  else {
                                    this.addOption(
                                      item2.name,
                                      1,
                                      item2.id,
                                      item2.price,
                                    );
                                  }


                                }}
                              >
                                <Icon1
                                  name="check"
                                  size={25}
                                  color={
                                    this.getOptionQuantity(item2.id) > 0
                                      ? Colors.whiteColor
                                      : Colors.descriptionColor
                                  }
                                />
                              </TouchableOpacity>

                            </View>
                          ) : (
                              <View
                                style={{
                                  flexDirection: 'row',
                                  marginRight: 10,
                                }}>
                                <View>
                                  <TouchableOpacity
                                    style={[
                                      styles.addBtnSmall,
                                      {
                                        backgroundColor:
                                          this.containsObject(item2.id) == true
                                            ? Colors.primaryColor
                                            : '#e8e9eb',
                                      },
                                    ]}
                                    onPress={() => {
                                      this.addLeastItem(
                                        item2.name,
                                        item2.id,
                                        item2.price,
                                        1,
                                      );
                                    }}>
                                    <Icon1
                                      name="check"
                                      size={25}
                                      color={
                                        this.containsObject(item2.id) == true
                                          ? Colors.whiteColor
                                          : Colors.descriptionColor
                                      }
                                    />
                                  </TouchableOpacity>
                                </View>
                              </View>
                            )}
                          <View style={{ width: '55%' }}>
                            <Text style={{ fontSize: 14, fontWeight: '700' }}>
                              {item2.name}
                            </Text>
                          </View>
                          {item.leastSelect == 1 ? (
                            <View style={{ width: '14%', marginLeft: 3 }}>
                              <View style={{ flexDirection: 'row' }}>
                                <TouchableOpacity
                                  disabled={this.state.loading}
                                  onPress={() => {
                                    if (item2.quantity > 0) {
                                      item2.quantity - 1;
                                    }
                                    this.addOption(
                                      item2.name,
                                      -1,
                                      item2.id,
                                      item2.price,
                                    );
                                  }}>
                                  <View
                                    style={[
                                      styles.addBtnSmall,
                                      {
                                        backgroundColor:
                                          Colors.descriptionColor,
                                      },
                                    ]}>
                                    <Text
                                      style={{
                                        fontSize: 10,
                                        fontWeight: '500',
                                        color: Colors.whiteColor,
                                      }}>
                                      -
                                      </Text>
                                  </View>
                                </TouchableOpacity>
                                <View
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor: Colors.whiteColor,
                                      borderWidth: StyleSheet.hairlineWidth,
                                      borderColor: Colors.descriptionColor,
                                    },
                                  ]}>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      fontWeight: 'bold',
                                      color: Colors.primaryColor,
                                    }}>
                                    {this.getOptionQuantity(item2.id)}
                                  </Text>
                                </View>
                                <TouchableOpacity
                                  disabled={this.state.loading}
                                  onPress={() => {
                                    item2.id + 1;
                                    this.addOption(
                                      item2.name,
                                      1,
                                      item2.id,
                                      item2.price,
                                    );
                                  }}>
                                  <View
                                    style={[
                                      styles.addBtnSmall,
                                      {
                                        backgroundColor: Colors.primaryColor,
                                      },
                                    ]}>
                                    <Text
                                      style={{
                                        fontSize: 10,
                                        fontWeight: '500',
                                        color: Colors.whiteColor,
                                      }}>
                                      +
                                      </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>
                          ) : (
                              <View
                                style={{ width: '14%', marginLeft: 3 }}></View>
                            )}
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          {item.leastSelect == 1 ? (
                            <View style={{ width: '50%' }}>
                              <Text style={{ fontSize: 14, fontWeight: '300' }}>
                                {' '}
                                  + RM
                                  {(parseFloat(item2.price).toFixed(2) *
                                  this.getOptionQuantity(item2.id)).toFixed(2)}
                              </Text>
                            </View>
                          ) : (
                              <View style={{ width: '50%' }}>
                                <Text style={{ fontSize: 14, fontWeight: '300' }}>
                                  {' '}
                                  + RM
                                  {parseFloat(
                                    item2.price,
                                  ).toFixed(2) * 1}
                                </Text>
                              </View>
                            )}
                        </View>
                      </View> : null)
                    );
                  })}
                </View> : null)
              );
            })
            : null}
          <View style={{ marginBottom: 15 }}>
            <Text
              style={{
                color: Colors.descriptionColor,
                fontWeight: "500",
                fontSize: 16,
                marginBottom: 15,
              }}
            >
              Special remarks:
            </Text>
            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              style={styles.textInput}
              placeholder="eg: no onions"
              onChangeText={(text) => {
                this.setState({ remark: text });
              }}
              value={this.state.remark}
              multiline={true}
            />
          </View>
          <Text
            style={{
              fontSize: 18,
              fontWeight: "700",
              color: Colors.primaryColor,
              alignSelf: 'flex-end',
            }}>
            RM{(parseFloat(this.totals()) * this.quantity()).toFixed(2)}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                onPress={() => {
                  if (this.state.quantity -1 > 0){
                    this.lessqty(this.state.menuItem.id);
                  }
                }}>
                <View
                  style={[
                    styles.addBtn,
                    { backgroundColor: Colors.descriptionColor },
                  ]}
                >
                  <Text
                    style={{
                      fontSize: 30,
                      fontWeight: "500",
                      color: Colors.whiteColor,
                    }}
                  >
                    -
                  </Text>
                </View>
              </TouchableOpacity>
              <View
                style={[
                  styles.addBtn,
                  {
                    backgroundColor: Colors.whiteColor,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: Colors.descriptionColor,
                  },
                ]}
              >
                <Text
                  style={{
                    fontSize: 25,
                    fontWeight: "bold",
                    color: Colors.primaryColor,
                  }}>
                  {this.quantity()}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  this.addqty(this.state.menuItem.id);
                }}>
                <View
                  style={[
                    styles.addBtn,
                    { backgroundColor: Colors.primaryColor },
                  ]}
                >
                  <Text
                    style={{
                      fontSize: 30,
                      fontWeight: "500",
                      color: Colors.whiteColor,
                    }}
                  >
                    +
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <View style={{ flex: 1, paddingLeft: 30 }}>
              <TouchableOpacity
                onPress={() => {
                  this.save();
                  this.setState({ optional: 0 });
                  //console.log(this.state.optional);
                  this.addToCart(),
                    this.props.navigation.goBack();
                }}>
                <View style={[Styles.button, { paddingVertical: 13 }]}>
                  <Text
                    style={{
                      color: "#ffffff",
                      fontSize: 18,
                      fontWeight: "bold",
                    }}
                  >
                    Add to cart
                  </Text>
                </View>
              </TouchableOpacity>
              {/* <Modal
                style={{ flex: 1 }}
                visible={this.state.visible}
                transparent={true}
                animationType="slide"
              >
                <View
                  style={{
                    backgroundColor: "rgba(0,0,0,0.5)",
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <View style={styles.confirmBox}>
                    <TouchableOpacity
                      onPress={() => {
                        this.setState({ visible: false });
                      }}
                    >
                      <View
                        style={{
                          alignSelf: "flex-start",
                          padding: 16,
                        }}
                      >
                        <Close name="close" size={24} />
                      </View>
                    </TouchableOpacity>
                    <View>
                      <Text
                        style={{
                          textAlign: "center",
                          fontWeight: "bold",
                          fontSize: 14,
                          marginBottom: 5,
                        }}
                      >
                        Confirm order?
                      </Text>
                    </View>
                    <View
                      style={{
                        alignItems: "center",
                        width: "100%",
                        alignContent: "center",
                        marginBottom: "6%",
                        height: "20%",
                        marginTop: "5%",
                      }}
                    >
                      <TouchableOpacity
                        onPress={() => {
                          this.setState({ optional: 0 });
                          console.log(this.state.optional);
                          this.addToCart(),
                            this.props.navigation.navigate("OutletMenu");
                          this.setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.whiteColor,
                          borderColor: Colors.primaryColor,
                          borderWidth: 1,
                          width: "70%",
                          justifyContent: "center",
                          alignItems: "center",
                          alignContent: "center",
                          borderRadius: 5,
                          height: "80%",
                          marginBottom: "5%",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 15,
                            color: Colors.primaryColor,
                          }}
                        >
                          Confirm Order
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          // this.setState({ optional1: 1 });
                          // console.log(this.state.optional1);
                          // this.setState({ visible: false });
                          // this.addToCart1(),
                          //   this.props.navigation.navigate("OutletMenu");
                          this.setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: "70%",
                          justifyContent: "center",
                          alignItems: "center",
                          alignContent: "center",
                          borderRadius: 5,
                          height: "80%",
                          marginBottom: "2%",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 15,
                            color: Colors.whiteColor,
                          }}
                        >
                          CANCEL
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal> */}
            </View>
          </View>
          <View style={{ minHeight: 100 }} />
        </ScrollView>
        {/* {this.state.cartIcon ?
          <Draggable
            shouldReverse={this.state.reverse}
            renderSize={100}
            renderColor={Colors.secondaryColor}
            isCircle
            x={280}
            y={500}
            onShortPressRelease={() => {
              this.goToCart();
            }}
          >
            <View style={{ width: 60, height: 60, justifyContent: "center" }}>
              <View style={{ alignSelf: "center" }}>
                <Icon name="cart-outline" size={45} />
              </View>
              <View style={styles.cartCount}>
                <Text style={{ color: Colors.whiteColor, fontSize: 10 }}>
                  {Cart.getCartItem().length}
                </Text>
              </View>
            </View>
          </Draggable>
          : null} */}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    padding: 16,
  },
  floatCartBtn: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  textInput: {
    height: 100,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  addBtn: {
    backgroundColor: Colors.primaryColor,
    width: 45,
    height: 45,
    justifyContent: "center",
    alignItems: "center",
  },
  addBtnSmall: {
    backgroundColor: Colors.primaryColor,
    width: 25,
    height: 25,
    justifyContent: "center",
    alignItems: "center",
  },
  cartCount: {
    position: "absolute",
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  confirmBox: {
    width: "70%",
    height: "40%",
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: "100%",
    width: "100%",
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  addBtn1: {
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default MenuItemDetailsScreen;
