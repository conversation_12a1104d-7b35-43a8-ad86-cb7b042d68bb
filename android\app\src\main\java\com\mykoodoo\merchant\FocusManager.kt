package com.mykoodoo.merchant

import android.app.Activity
import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Build
import android.util.DisplayMetrics
import android.view.Display
import android.view.WindowManager
import androidx.annotation.RequiresApi
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class FocusManager(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val reactContext: ReactApplicationContext = reactContext

    override fun getName(): String {
        return "FocusManager"
    }

    @RequiresApi(Build.VERSION_CODES.R)
    @ReactMethod
    fun focusPrimaryDisplay() {
        val activity: Activity? = currentActivity
        if (activity == null) return

        // Get WindowManager and DisplayManager
        val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayManager = activity.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager

        // Get primary (default) display
        val defaultDisplay: Display? = displayManager.getDisplay(Display.DEFAULT_DISPLAY)

        // If primary display is available, force focus to it
        if (defaultDisplay != null) {
            val metrics = DisplayMetrics()
            windowManager.defaultDisplay.getMetrics(metrics)
            activity.window.decorView.requestFocus()
        }
    }
}
