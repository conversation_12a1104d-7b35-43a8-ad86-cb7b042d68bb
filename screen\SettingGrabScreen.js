import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { EXPAND_TAB_TYPE, WEEK } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { parseValidPriceText } from '../util/common';
import APILocal from '../util/apiLocalReplacers';
import DropDownPicker from "react-native-dropdown-picker";
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Clipboard from "@react-native-clipboard/clipboard";
import { grabSelfServeJourney } from '../util/grabFood';

const SettingGrabScreen = props => {
  const {
    navigation,
  } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [loading, setLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  //////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [grabActivationUrl, setGrabActivationUrl] = useState('');

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);

  const userName = UserStore.useState(s => s.name);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////

  const orderAcceptanceOption = [
    {
      label: 'Manual',
      value: 'MANUAL',
    },
    {
      label: 'Auto',
      value: 'AUTO'
    },];
  const [orderAcceptance, setOrderAcceptance] = useState('MANUAL');
  const [grabMerchantId, setGrabMerchantId] = useState('');
  const [grabCommissionGD, setGrabCommissionGD] = useState(0);
  const [grabCommissionOD, setGrabCommissionOD] = useState(0);
  const [grabCommissionT, setGrabCommissionT] = useState(0);
  const [grabCommissionDI, setGrabCommissionDI] = useState(0);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setGrabMerchantId(currOutlet.odGrabMID ? currOutlet.odGrabMID : '')
      setGrabCommissionGD(currOutlet.odGrabCommRate ? currOutlet.odGrabCommRate.toFixed(0) : 0)
      setGrabCommissionOD(currOutlet.odGrabDByRestaurantCommRate ? currOutlet.odGrabDByRestaurantCommRate.toFixed(0) : 0)
      setGrabCommissionT(currOutlet.odGrabTakeAwayCommRate ? currOutlet.odGrabTakeAwayCommRate.toFixed(0) : 0)
      setGrabCommissionDI(currOutlet.odGrabDineInCommRate ? currOutlet.odGrabDineInCommRate.toFixed(0) : 0)
      setOrderAcceptance(currOutlet.odGrabAccept ? currOutlet.odGrabAccept : 'MANUAL')
    }
  }, [currOutlet]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  //////////////////////////////////////////////////////////////////////////

  const setState = () => { };


  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            ...global.getHeaderTitleStyle(),
          },
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Grab Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const updateGrabSettingToOutlet = () => {
    var body = {
      outletId: currOutletId,
      odGrabAccept: orderAcceptance,
      odGrabMID: grabMerchantId,
      odGrabCommRate: grabCommissionGD,
      odGrabDByRestaurantCommRate: grabCommissionOD,
      odGrabTakeAwayCommRate: grabCommissionT,
      odGrabDineInCommRate: grabCommissionDI,
    };

    // console.log(body);

    // ApiClient.POST(API.updateOutletOrderDetails, body, false)
    APILocal.updateGrabSettingToOutlet({ body: body })
      .then((result) => {
        setLoading(true)
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Grab settings has been updated.',
            [
              {
                text: 'OK',
                onPress: () => {
                  setLoading(false)
                },
              },
            ],
            { cancelable: false },
          );
        }
      });
  }

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>    
    (<UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.container, !isTablet() ? {
        transform: [
          { scaleX: 1 },
          { scaleY: 1 },
        ],
      } : {}, {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        {/* <View style={[styles.sidebar, !isTablet() ? {
          width: windowWidth * 0.08,
        } : {}, switchMerchant ? {
          // width: '10%'
        } : {}, {
          width: windowWidth * 0.08,
        }]}>
          <SideBar navigation={props.navigation} selectedTab={10} expandSettings={true} />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal={true}>

            <View style={[styles.content, {
              width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
            }]}>
              <View
                style={{
                  backgroundColor: Colors.whiteColor,
                  // height: windowHeight - 120,
                  height: '100%',
                  width: windowWidth * 0.87,
                  alignSelf: 'center',

                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  // elevation: 1,
                  elevation: 3,
                  borderRadius: 5,

                  // borderRadius: 8,
                }}>
                <KeyboardAwareScrollView
                  contentContainerStyle={{
                    // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                  }}>
                  <View style={{}}>
                    {/* grab merchant id */}
                    <View style={{ flexDirection: 'row', padding: 30, zIndex: -3 }}>
                      <View style={{ flex: 3 }}>
                        {/* //////////////////////////////////////////////// */}

                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Grab Merchant ID `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                placeholder="Grab Merchant ID"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                defaultValue={grabMerchantId}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabMerchantId)
                                //   setGrabMerchantId('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabMerchantId == '') {
                                //     setGrabMerchantId(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabMerchantId(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* //////////////////////////////////////////////// */}
                        {/* koodoo merchant id */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`KooDoo Merchant ID `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                            alignItems: 'center',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <Text style={{}}>
                                {merchantId ? merchantId : 'N/A'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: 10, }}>
                              <TouchableOpacity onPress={() => {
                                Clipboard.setString(merchantId);
                                Alert.alert('Copied!', 'KooDoo Merchant ID copied to clipboard.');
                              }}>
                                <MaterialIcons name='content-copy' size={20} />
                              </TouchableOpacity>
                            </View>


                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>
                          </View>
                        </View>

                        {/* //////////////////////////////////////////////// */}
                        {/* self activation flow */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Self Activation `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                            alignItems: 'center',
                          }}>
                            {/* <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <Text style={{}}>
                                {merchantId ? merchantId : 'N/A'}
                              </Text>
                            </View> */}
                            <View style={{
                              // width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                              // right: 20,
                            }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  // width: 120,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}
                                onPress={async () => {
                                  // Clipboard.setString(merchantId);
                                  // Alert.alert('Copied!', 'KooDoo Merchant ID copied to clipboard.');

                                  if (grabActivationUrl) {
                                    Linking.openURL(grabActivationUrl);
                                  }
                                  else {
                                    const result = await grabSelfServeJourney(currOutlet.uniqueId);

                                    if (result && result.data && result.data.activationUrl) {
                                      setGrabActivationUrl(result.data.activationUrl);
                                    }
                                    else {
                                      Alert.alert('Info', 'Unable to initiate the self activation flow, please contact account manager on this.');
                                    }
                                  }
                                }}>
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  {grabActivationUrl ? 'OPEN IN BROWSER' : 'ACTIVATE'}
                                </Text>
                              </TouchableOpacity>
                            </View>


                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>
                          </View>
                        </View>

                        {/* commission grab delivery */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Grab Delivery) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                defaultValue={grabCommissionGD}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionGD)
                                //   setGrabCommissionGD('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionGD == '') {
                                //     setGrabCommissionGD(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionGD(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission outlet delivery*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Outlet Delivery) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                defaultValue={grabCommissionOD}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionOD)
                                //   setGrabCommissionOD('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionOD == '') {
                                //     setGrabCommissionOD(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionOD(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission Takeaway*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Takeaway) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                defaultValue={grabCommissionT}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionT)
                                //   setGrabCommissionT('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionT == '') {
                                //     setGrabCommissionT(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionT(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission Dine-in*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Dine-in) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                defaultValue={grabCommissionDI}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionDI)
                                //   setGrabCommissionDI('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionDI == '') {
                                //     setGrabCommissionDI(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionDI(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* order acceptance */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: 1 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Order Acceptance `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <DropDownPicker
                                globalTextStyle={{
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                containerStyle={{
                                  height: switchMerchant ? 35 : 40,
                                }}
                                arrowColor={'black'}
                                arrowSize={switchMerchant ? 17 : 20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                style={[{
                                  // width: 200,
                                  paddingVertical: 0,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  //zIndex: index ++ ? 1 : 0,
                                  //elevation: 1000
                                }, switchMerchant ? {
                                  marginRight: 5,
                                } : {}]}
                                placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                placeholder={'Select'}
                                items={orderAcceptanceOption}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  //marginLeft: 5,
                                  paddingHorizontal:
                                    windowWidth * 0.003,
                                  zIndex: 100,
                                }}
                                onChangeItem={(item) => {
                                  setOrderAcceptance(item.value);
                                }}
                                defaultValue={orderAcceptance}
                                dropDownStyle={{
                                  // width: windowWidth * 0.11,
                                  height: 80,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  textAlign: 'left',
                                  marginRight: 10,
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                      </View>

                    </View>
                  </View>
                  <View style={{ alignItems: 'center', zIndex: -4, }}>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: 120,
                        paddingHorizontal: 10,
                        height: 35,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      disabled={loading}
                      onPress={() => {
                        updateGrabSettingToOutlet();
                      }}>
                      <Text style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                        {loading ? 'LOADING...' : 'SAVE'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{ marginTop: 20 }}></View>
                </KeyboardAwareScrollView>
              </View>

            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );

}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center'
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold'
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default SettingGrabScreen;