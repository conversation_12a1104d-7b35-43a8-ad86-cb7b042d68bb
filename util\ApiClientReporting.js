import { Alert } from 'react-native';
import * as Token from './Token';
import * as User from './User';
import { apiReportingUrl, apiUrl } from '../constant/env';
import AsyncStorage from '@react-native-async-storage/async-storage'
import axios from 'axios';
import { logToFile } from './common';
import moment from 'moment';

const client = axios.create({
    baseURL: apiReportingUrl,
    timeout: 180000,
});

client.interceptors.request.use(async options => {
    const token = Token.getToken();

    if (token) {
        options.headers['Authorization'] = `Bearer ${token}`;
    }

    // options.headers['Content-Type'] = `application/x-www-form-urlencoded`;
    // options.headers['Accept'] = `application/json`;

    // options.headers['Content-Type'] = 'application/json';

    // options.headers['User-Agent'] = 'PostmanRuntime/7.37.3';
    // options.headers['Accept-Encoding'] = 'gzip, deflate, br';
    // options.headers['Connection'] = 'keep-alive';

    return options;
});

const logoutUser = async function () {
    // console.log('User');
    // console.log(User);

    // to support offline-mode
    // await AsyncStorage.clear();

    logToFile('network failed | sign out');

    /////////////////////////////////////////

    // 2024-07-15 - no need first

    await AsyncStorage.multiRemove([
        'accessToken',
        'userData',
        'refreshToken',
    ]);

    global.signInAlready = false;

    Token.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();

    global.funcSwitchShowApp(false);

    /////////////////////////////////////////
};

client.interceptors.response.use(
    response => {
        return response;
    },
    async function (error) {
        console.error(error);
        // console.log(error);

        const originalRequest = error.config;
        if (error.response && error.response.status === 401) {
            if (!originalRequest._retry) {
                originalRequest._retry = true;
                const refreshToken = Token.getRefreshToken();
                if (refreshToken) {
                    try {
                        const refreshRes = await client.get(
                            `/token?ctoken=REFRESH:${refreshToken}`,
                        );
                        if (refreshRes.data) {
                            Token.setToken(refreshRes.data.token);
                            Token.setRefreshToken(refreshRes.data.refreshToken);
                            return client(originalRequest);
                        }
                    } catch (err) {
                        if (
                            (global.currOutlet && global.currOutlet.noSignoutFN)
                            ||
                            global.noSignoutFN
                        ) {
                            // do nothing

                            global.udiData.fnns1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                            logToFile(`no signout when fn 1`);
                        }
                        else {
                            global.udiData.fns1 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                            logToFile(`auto signout when fn 1`);

                            logoutUser();
                        }
                    }
                } else {
                    if (
                        (global.currOutlet && global.currOutlet.noSignoutFN)
                        ||
                        global.noSignoutFN
                    ) {
                        // do nothing

                        global.udiData.fnns2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        logToFile(`no signout when fn 2`);
                    }
                    else {
                        global.udiData.fns2 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                        logToFile(`auto signout when fn 2`);

                        logoutUser();
                    }
                }
            } else {
                if (
                    (global.currOutlet && global.currOutlet.noSignoutFN)
                    ||
                    global.noSignoutFN
                ) {
                    // do nothing

                    global.udiData.fnns3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                    logToFile(`no signout when fn 3`);
                }
                else {
                    global.udiData.fns3 = `${moment().format('YYYY-MM-DD, HH:mm:ss.SSS')}`;

                    logToFile(`auto signout when fn 3`);

                    logoutUser();
                }
            }
        }
        // return Promise.reject(error);

        if (error.response && error.response.data && error.response.data.message) {
            // await AsyncStorage.setItem('api.error.msg', error.response.data.message);

            global['api.error.msg'] = error.response.data.message;
        }

        return error;
    },
);


////////////////////////////////////

const GET = async function (url) {
    try {
        // console.log(client.defaults.baseURL);
        // console.log(client.defaults.baseURL + url);

        // const { data } = await client.get(url, { data: {}, });
        const { data } = await client.get(url);
        if (data && data.error) {
            // Alert.alert("Error", url+ data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            //     // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", "Unknown error has occured", [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })

        console.error(err);
    }
}

const POST = async function (url, params) {
    try {
        const { data } = await client.post(url, params);
        if (data && data.error) {
            // Alert.alert("Error", url+ data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            //     // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", "Unknown error has occured", [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })

        console.error(err);
    }
}

const PATCH = async function (url, params) {
    try {
        const { data } = await client.patch(url, params);
        if (data && data.error) {
            // Alert.alert("Error", url+ data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            //     // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", "Unknown error has occured", [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })

        console.error(err);
    }
}

const ApiClient = {
    GET,
    POST,
    PATCH,
    logoutUser
}

export default ApiClient
