export const ANALYTICS = {
    MODULE_OPERATION: 'm_operation',
    MODULE_RESERVATION: 'm_reservation',
    MODULE_PRODUCT: 'm_product',
    MODULE_INVENTORY: 'm_inventory',
    MODULE_COMPOSITE: 'm_composite',
    MODULE_DOCKET: 'm_docket',
    MODULE_VOUCHER: 'm_voucher',
    MODULE_PROMOTION: 'm_promotion',
    MODULE_CRM: 'm_crm',
    MODULE_LOYALTY: 'm_loyalty',
    MODULE_TRANSACTION: 'm_transaction',
    MODULE_REPORT: 'm_report',
    MODULE_EMPLOYEE: 'm_employee',
    MODULE_SETTINGS: 'm_settings',
    MODULE_SUPPORT: 'm_support',
    MODULE_UPSELLING: 'm_upselling',

    MODULE_OPERATION_ORDERING: 'm_operation_ordering',
    MODULE_OPERATION_KITCHEN: 'm_operation_kitchen',
    MODULE_OPERATION_TABLE: 'm_operation_table',
    MOD<PERSON><PERSON>_OPERATION_DINEIN: 'm_operation_dinein',
    MODULE_OPERATION_TAKEAWAY: 'm_operation_takeaway',
    MODULE_OPERATION_OTHER_DELIVERY: 'm_operation_other_delivery',
    MODULE_OPERATION_QUEUE: 'm_operation_queue',
    MODULE_OPERATION_HISTORY: 'm_operation_history',
    MODULE_OPERATION_ORDER_DASHBOARD: 'm_operation_order_dashboard',

    MODULE_RESERVATION_MANAGE: 'm_reservation_manage',
    MODULE_RESERVATION_OVERVIEW: 'm_reservation_overview',
    MODULE_RESERVATION_DEPOSIT_SETTINGS: 'm_reservation_deposit_settings',
    MODULE_RESERVATION_INTERVAL_SETTINGS: 'm_reservation_interval_settings',
    MODULE_RESERVATION_ANALYTICS: 'm_reservation_analytics',

    MODULE_PRODUCT_P_CATEGORY: 'm_product_p_category',
    MODULE_PRODUCT_VARIANT_ADDON: 'm_product_variant_addon',
    MODULE_PRODUCT_MENU_DISPLAY: 'm_product_menu_display',
    MODULE_PRODUCT_MANAGE_PREORDER: 'm_product_manage_preorder',

    MODULE_INVENTORY_SUPPLIER: 'm_inventory_supplier',
    MODULE_INVENTORY_I_OVERVIEW: 'm_inventory_i_overview',
    MODULE_INVENTORY_PURCHASE_ORDER: 'm_inventory_purchase_order',
    MODULE_INVENTORY_STOCK_TRANSFER: 'm_inventory_stock_transfer',
    MODULE_INVENTORY_STOCK_TAKE: 'm_inventory_stock_take',
    MODULE_INVENTORY_STOCK_RETURN: 'm_inventory_stock_return',

    MODULE_COMPOSITE_SUPPLIER: 'm_composite_supplier',
    MODULE_COMPOSITE_I_OVERVIEW: 'm_composite_i_overview',
    MODULE_COMPOSITE_PURCHASE_ORDER: 'm_composite_purchase_order',
    MODULE_COMPOSITE_STOCK_TRANSFER: 'm_composite_stock_transfer',
    MODULE_COMPOSITE_STOCK_TAKE: 'm_composite_stock_take',

    MODULE_DOCKET_ACTIVE_D: 'm_docket_active_d',
    MODULE_DOCKET_EXPIRED_D: 'm_docket_expired_d',
    MODULE_DOCKET_REDEEMED_D: 'm_docket_redeemed_d',
    MODULE_DOCKET_MANAGE_D: 'm_docket_manage_d',

    MODULE_VOUCHER_V_LIST: 'm_voucher_v_list',
    MODULE_VOUCHER_ADD_V: 'm_voucher_add_v',
    MODULE_VOUCHER_V_REPORT: 'm_voucher_v_report',

    MODULE_PROMOTION_P_LIST: 'm_promotion_p_list',
    MODULE_PROMOTION_ADD_P: 'm_promotion_add_p',
    MODULE_PROMOTION_P_REPORT: 'm_promotion_p_report',

    MODULE_CRM_CUSTOMER: 'm_crm_customer',
    MODULE_CRM_SEGMENT: 'm_crm_segment',
    MODULE_CRM_CUSTOMER_REVIEW: 'm_crm_customer_review',

    MODULE_LOYALTY_CAMPAIGN: 'm_loyalty_campaign',
    MODULE_LOYALTY_SIGN_UP_REWARD: 'm_loyalty_sign_up_reward',
    MODULE_LOYALTY_STAMPS: 'm_loyalty_stamps',
    MODULE_LOYALTY_PAY_EARN: 'm_loyalty_pay_earn',
    MODULE_LOYALTY_REWARD_REDEMPTION: 'm_loyalty_reward_redemption',
    MODULE_LOYALTY_CREDIT_TYPE: 'm_loyalty_credit_type',
    MODULE_LOYALTY_CREDIT_TYPE_REPORT: 'm_loyalty_credit_type_report',
    MODULE_LOYALTY_L_REPORT: 'm_loyalty_l_report',

    // Transaction
    MODULE_TRANSACTION_AT_C_LOGO: 'm_transaction_at_c_logo',
    MODULE_TRANSACTION_AT_C_PROFILE: 'm_transaction_at_c_profile',
    MODULE_TRANSACTION_AT_C_DL: 'm_transaction_at_c_dl',
    MODULE_TRANSACTION_AT_DL_TB_EMAIL: 'm_transaction_at_dl_tb_email',
    MODULE_TRANSACTION_AT_DL_C_REP_EXCEL: 'm_transaction_at_dl_c_rep_excel',
    MODULE_TRANSACTION_AT_DL_C_REP_CSV: 'm_transaction_at_dl_c_rep_csv',
    MODULE_TRANSACTION_AT_C_CAL_START: 'm_transaction_at_c_cal_start',
    MODULE_TRANSACTION_AT_C_CAL_END: 'm_transaction_at_c_cal_end',
    MODULE_TRANSACTION_AT_TABLE_C_DATE: 'm_transaction_at_table_c_date',
    MODULE_TRANSACTION_AT_TABLE_C_OUT: 'm_transaction_at_table_c_out',
    MODULE_TRANSACTION_AT_TABLE_C_EMP: 'm_transaction_at_table_c_emp',
    MODULE_TRANSACTION_AT_TABLE_C_CUS: 'm_transaction_at_table_c_cus',
    MODULE_TRANSACTION_AT_TABLE_C_TYPE: 'm_transaction_at_table_c_type',
    MODULE_TRANSACTION_AT_TABLE_C_COUR: 'm_transaction_at_table_c_cour',
    MODULE_TRANSACTION_AT_TABLE_C_SALES: 'm_transaction_at_table_c_sales',
    MODULE_TRANSACTION_AT_TABLE_S_C_ICON_QM: 'm_transaction_at_table_s_c_icon_qm',
    MODULE_TRANSACTION_AT_TABLE_C_DISC: 'm_transaction_at_table_c_disc',
    MODULE_TRANSACTION_AT_TABLE_C_TAX: 'm_transaction_at_table_c_tax',
    MODULE_TRANSACTION_AT_TABLE_C_SERV_CHAR: 'm_transaction_at_table_c_serv_char',
    MODULE_TRANSACTION_AT_TABLE_C_NET_SALE: 'm_transaction_at_table_c_net_sale',
    MODULE_TRANSACTION_AT_TABLE_NS_C_ICON_QM: 'm_transaction_at_table_ns_c_icon_qm',
    MODULE_TRANSACTION_AT_C_PREV_PAGE_BUTTON: 'm_transaction_at_c_prev_page_button',
    MODULE_TRANSACTION_AT_C_NEXT_PAGE_BUTTON: 'm_transaction_at_c_next_page_button',

    MODULE_REPORT_DASHBOARD: 'm_report_dashboard',
    MODULE_REPORT_ANALYSIS: 'm_report_analysis',
    MODULE_REPORT_AOV: 'm_report_aov',
    MODULE_REPORT_ORDER_COUNT: 'm_report_order_count',
    MODULE_REPORT_REVISIT_COUNT: 'm_report_revisit_count',
    MODULE_REPORT_OVERVIEW: 'm_report_overview',
    MODULE_REPORT_REVISIT: 'm_report_revisit',
    MODULE_REPORT_UPSELLING: 'm_report_upselling',
    MODULE_REPORT_PRODUCT: 'm_report_product',
    MODULE_REPORT_CATEGORY: 'm_report_category',
    MODULE_REPORT_VARIANT: 'm_report_variant',
    MODULE_REPORT_ADDON: 'm_report_addon',
    MODULE_REPORT_CHANNEL: 'm_report_channel',
    MODULE_REPORT_PAYMENT: 'm_report_payment',
    MODULE_REPORT_SHIFT: 'm_report_shift',
    MODULE_REPORT_PAY_IN_OUT: 'm_report_pay_in_out',
    MODULE_REPORT_REFUND: 'm_report_refund',
    MODULE_REPORT_CATEGORY_PRODUCT: 'm_report_category_product',

    // Report - Dashboard Report
    MODULE_REPORT_DB_C_DL_REP_EMAIL_EXCEL: 'm_report_db_c_dl_rep_email_excel', // download report by email via excel sheet
    MODULE_REPORT_DB_C_DL_REP_EMAIL_CSV: 'm_report_db_c_dl_rep_email_csv',
    MODULE_REPORT_DB_C_REP_SALES_OT: 'm_report_db_c_rep_sales_ot',
    MODULE_REPORT_DB_C_ALL_TRANS: 'm_report_db_c_all_trans',
    MODULE_REPORT_DB_C_REP_SALES_PROD: 'm_report_db_c_rep_sales_prod',

    // Report - AOV Report
    MODULE_REPORT_AOV_C_LOGO: 'm_report_ocr_c_logo',
    MODULE_REPORT_AOV_C_PROFILE: 'm_report_ocr_c_profile',
    MODULE_REPORT_AOV_C_CHART_TODAY: 'm_report_aov_c_chart_today',
    MODULE_REPORT_AOV_C_CHART_TW: 'm_report_aov_c_chart_tw', // Sales AOV Chart - This Week
    MODULE_REPORT_AOV_C_CHART_MONTH: 'm_report_aov_c_chart_month', //Sales AOV Chart - This Month
    MODULE_REPORT_AOV_C_CHART_3_MONTHS: 'm_report_aov_c_chart_3_months',
    MODULE_REPORT_AOV_C_CHART_6_MONTHS: 'm_report_aov_c_chart_6_months',
    MODULE_REPORT_AOV_C_CHART_1_YEAR: 'm_report_aov_c_chart_1_year',
    MODULE_REPORT_AOV_C_CHART_YESTERDAY: 'm_report_aov_c_chart_yesterday',
    MODULE_REPORT_AOV_DD_APP_ORDER: 'm_report_aov_dd_app_order',
    MODULE_REPORT_AOV_C_CAL_START: 'm_report_aov_c_cal_start',
    MODULE_REPORT_AOV_C_CAL_END: 'm_report_aov_c_cal_end',

    // Report - Order Count Report
    MODULE_REPORT_OCR_C_LOGO: 'm_report_ocr_c_logo',
    MODULE_REPORT_OCR_C_PROFILE: 'm_report_ocr_c_profile',
    MODULE_REPORT_OCR_C_CHART_TODAY: 'm_report_ocr_c_chart_today',
    MODULE_REPORT_OCR_C_CHART_TW: 'm_report_ocr_c_chart_tw',
    MODULE_REPORT_OCR_C_CHART_MONTH: 'm_report_ocr_c_chart_month',
    MODULE_REPORT_OCR_C_CHART_3_MONTHS: 'm_report_ocr_c_chart_3_months',
    MODULE_REPORT_OCR_C_CHART_6_MONTHS: 'm_report_ocr_c_chart_6_months',
    MODULE_REPORT_OCR_C_CHART_1_YEAR: 'm_report_ocr_c_chart_1_year',
    MODULE_REPORT_OCR_C_CHART_YESTERDAY: 'm_report_ocr_c_chart_yesterday',
    MODULE_REPORT_OCR_DD_APP_ORDER: 'm_report_ocr_dd_app_order',
    MODULE_REPORT_OCR_C_CAL_START: 'm_report_ocr_c_cal_start',
    MODULE_REPORT_OCR_C_CAL_END: 'm_report_ocr_c_cal_end',

    // Report - Revisit Count Report
    MODULE_REPORT_RC_C_LOGO: 'm_report_rc_c_logo',
    MODULE_REPORT_RC_C_PROFILE: 'm_report_rc_c_profile',
    MODULE_REPORT_RC_C_CHART_TODAY: 'm_report_rc_c_chart_today',
    MODULE_REPORT_RC_C_CHART_TW: 'm_report_rc_c_chart_tw',
    MODULE_REPORT_RC_C_CHART_MONTH: 'm_report_rc_c_chart_month',
    MODULE_REPORT_RC_C_CHART_3_MONTHS: 'm_report_rc_c_chart_3_months',
    MODULE_REPORT_RC_C_CHART_6_MONTHS: 'm_report_rc_c_chart_6_months',
    MODULE_REPORT_RC_C_CHART_1_YEAR: 'm_report_rc_c_chart_1_year',
    MODULE_REPORT_RC_C_CHART_YESTERDAY: 'm_report_rc_c_chart_yesterday',
    MODULE_REPORT_RC_DD_APP_ORDER: 'm_report_rc_dd_app_order',

    // Report - Revisit Report
    MODULE_REPORT_RV_C_LOGO: 'm_report_rv_c_logo',
    MODULE_REPORT_RV_C_PROFILE: 'm_report_rv_c_profile',
    MODULE_REPORT_RV_C_DL_BUTTON: 'm_report_rv_c_dl_button',
    MODULE_REPORT_RV_DD_APP_ORDER: 'm_report_rv_dd_app_order',
    MODULE_REPORT_RV_DL_RP_C_EXCEL: 'm_report_rv_dl_rp_c_excel',
    MODULE_REPORT_RV_DL_RP_C_CSV: 'm_report_rv_dl_rp_c_csv',
    MODULE_REPORT_RV_TB_SEARCH: 'm_report_rv_tb_search',
    MODULE_REPORT_RV_C_CAL_START: 'm_report_rv_c_cal_start',
    MODULE_REPORT_RV_C_CAL_END: 'm_report_rv_c_cal_end',
    MODULE_REPORT_RV_C_CHART_TODAY: 'm_report_rv_c_chart_today',
    MODULE_REPORT_RV_C_CHART_TW: 'm_report_rv_c_chart_tw',
    MODULE_REPORT_RV_C_CHART_MONTH: 'm_report_rv_c_chart_month',
    MODULE_REPORT_RV_C_CHART_3_MONTHS: 'm_report_rv_c_chart_3_months',
    MODULE_REPORT_RV_C_CHART_6_MONTHS: 'm_report_rv_c_chart_6_months',
    MODULE_REPORT_RV_C_CHART_1_YEAR: 'm_report_rv_c_chart_1_year',
    MODULE_REPORT_RV_C_CHART_YESTERDAY: 'm_report_rv_c_chart_yesterday',
    MODULE_REPORT_RV_C_SORT_TABLE_DATE_TIME: 'm_report_rv_c_table_date_time',
    MODULE_REPORT_RV_C_SORT_TABLE_ORDER: 'm_report_rv_c_table_order',
    MODULE_REPORT_RV_C_SORT_TABLE_SALES: 'm_report_rv_c_sort_table_sales',
    MODULE_REPORT_RV_C_SORT_TABLE_DISC_RM: 'm_report_rv_c_sort_table_disc_rm',
    MODULE_REPORT_RV_C_SORT_TABLE_DISC_PERCENTAGE: 'm_report_rv_c_sort_table_disc_percentage',
    MODULE_REPORT_RV_C_SORT_TABLE_TAX: 'm_report_rv_c_sort_table_tax',
    MODULE_REPORT_RV_C_SORT_TABLE_SERV_CHARGE: 'm_report_rv_c_sort_table_serv_charge',
    MODULE_REPORT_RV_C_SORT_TABLE_NET_SALES: 'm_report_rv_c_sort_table_net_sales',
    MODULE_REPORT_RV_C_SORT_TABLE_GROSS_PROFIT: 'm_report_rv_C_sort_table_gross_profit',
    MODULE_REPORT_RV_C_SORT_TABLE_AVG_NET_SALES: 'm_report_rv_c_sort_table_avg_net_sales',
    MODULE_REPORT_RV_TABLE_SALES_C_ICON_QM: 'm_report_rv_table_sales_c_ion_qm',
    MODULE_REPORT_RV_TABLE_NET_SALES_C_ICON_QM: 'm_report_rv_table_net_sales_c_icon_qm',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_TIME: 'm_report_rv_sum_low_table_c_sort_table_time', // Summary low table for indiviual report table
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_SALES: 'm_report_rv_sum_low_table_c_Sort_table_sales',
    MODULE_REPORT_RV_SUM_LOW_TABLE_SALES_C_ICON_QM: 'm_report_rv_sum_low_table_sales_c_icon_qm',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_DISC_RM: 'm_report_rv_sum_low_table_c_s_t_disc_rm',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_DISC_PERC: 'm_report_rv_sum_low_table_c_s_t_dis_per',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_TAX: 'm_report_rv_sum_low_table_c_Sort_table_tax',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_SERV_CHARGE: 'm_report_rv_sum_low_table_c_s_t_se_char',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_NET_SALES: 'm_report_rv_sum_low_table_c_s_table_n_s',
    MODULE_REPORT_RV_SUM_LOW_TABLE_NET_SALES_C_ICON_QM: 'm_report_rv_sum_low_table_net_sales_c_icon_qm',
    MODULE_REPORT_RV_DD_ITEM_SHOWED: 'm_report_rv_dd_item_showed',
    MODULE_REPORT_RV_TB_PAGE: 'm_report_rv_tb_page',
    MODULE_REPORT_RV_C_PREV_PAGE_BUTTON: 'm_report_rv_c_prev_page_button',
    MODULE_REPORT_RV_C_NEXT_PAGE_BUTTON: 'm_report_rv_c_next_page_button',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_P_P_B: 'm_report_rv_sum_low_table_c_p_p_b',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_N_P_B: 'm_report_rv_sum_low_table_c_n_p_b',

    // report sales - upselling
    MODULE_REPORT_UP_C_LOGO: 'm_report_up_c_logo',
    MODULE_REPORT_UP_C_GEN_SET: 'm_report_up_c_gen_set',
    MODULE_REPORT_UP_DD_APP_ORDER: 'm_report_up_dd_app_order',
    MODULE_REPORT_UP_TB_SEARCH: 'm_report_up_tb_search',
    MODULE_REPORT_UP_DL_TB_EMAIL_ADDRESS: 'm_report_up_dd_tb_email_address',
    MODULE_REPORT_UP_DL_RP_C_REP_EXCEL: 'm_report_up_dl_rp_c_rep_excel',
    MODULE_REPORT_UP_DL_RP_C_REP_CSV: 'm_report_up_dl_rp_c_rep_csv',
    MODULE_REPORT_UP_C_CHART_TODAY: 'm_report_up_c_chart_today',
    MODULE_REPORT_UP_C_CHART_TW: 'm_report_up_c_chart_tw',
    MODULE_REPORT_UP_C_CHART_MONTH: 'm_report_up_c_chart_month',
    MODULE_REPORT_UP_C_CHART_3_MONTHS: 'm_report_up_c_chart_3_months',
    MODULE_REPORT_UP_C_CHART_6_MONTHS: 'm_report_up_c_chart_6_months',
    MODULE_REPORT_UP_C_CHART_1_YEAR: 'm_report_up_c_chart_1_year',
    MODULE_REPORT_UP_C_CHART_YESTERDAY: 'm_report_up_c_chart_yesterday',
    MODULE_REPORT_UP_TB_PAGE: 'm_report_up_tb_page',
    MODULE_REPORT_UP_C_DOWNLOAD: 'm_report_up_c_download',
    MODULE_REPORT_UP_SUM_TABLE_C_PROD: 'm_report_up_sum_table_c_prod',
    MODULE_REPORT_UP_SUM_TABLE_C_CAT: 'm_report_up_sum_table_c_cat',
    MODULE_REPORT_UP_SUM_TABLE_C_SKU: 'm_report_up_sum_table_c_sku',
    MODULE_REPORT_UP_SUM_TABLE_C_ITEMS: 'm_report_up_sum_table_c_items',
    MODULE_REPORT_UP_SUM_TABLE_ITEMS_C_ICON_QM: 'm_report_up_sum_table_items_c_icon_qm',
    MODULE_REPORT_UP_SUM_TABLE_C_DISC_RM: 'm_report_up_sum_table_c_disc_rm',
    MODULE_REPORT_UP_SUM_TABLE_C_DISC_PERC: 'm_report_up_sum_table_c_disc_perc',
    MODULE_REPORT_UP_SUM_TABLE_C_PAY_TYPE: 'm_report_up_sum_table_c_pay_type',
    MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES: 'm_report_up_sum_table_c_net_sales',
    MODULE_REPORT_UP_SUM_TABLE_NS_C_ICON_QM: 'm_report_up_sum_table_ns_c_icon_qm',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_O_ID: 'm_report_up_details_c_o_id',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_TD: 'm_report_up_details_c_td',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_S: 'm_report_up_details_c_s',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_S_C_I_QM: 'm_report_up_details_s_c_i_qm',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_DISC: 'm_report_up_details_disc',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_T: 'm_report_up_details_c_t',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SC: 'm_report_up_details_c_sc',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SR: 'm_report_up_details_c_sr',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_NS: 'm_report_up_details_c_ns',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_NS_C_I_QM: 'm_report_up_details_ns_c_i_qm',
    MODULE_REPORT_UP_DD_ITEMS_SHOWED: 'm_report_up_dd_items_showed',
    MODULE_REPORT_UP_C_NEXT_PAGE: 'm_report_up_c_next_page',
    MODULE_REPORT_UP_C_PREV_PAGE: 'm_report_up_c_prev_page',
    MODULE_REPORT_UP_SUM_IND_C_NEXT_PAGE: 'm_report_up_sum_ind_c_next_page',
    MODULE_REPORT_UP_SUM_IND_C_PREV_PAGE: 'm_report_up_sum_ind_c_prev_page',
    MODULE_REPORT_UP_C_CAL_START: 'm_report_up_c_cal_start',
    MODULE_REPORT_UP_C_CAL_END: 'm_report_up_c_cal_end',

    // report sales - product
    MODULE_REPORT_PROD_C_LOGO: 'm_report_prod_c_logo',
    MODULE_REPORT_PROD_C_PROFILE: 'm_report_prod_c_profile',
    MODULE_REPORT_PROD_DD_APP_ORDER: 'm_report_prod_dd_app_order',
    MODULE_REPORT_PROD_TB_SEARCH: 'm_report_prod_tb_search',
    MODULE_REPORT_PROD_DL_BUTTON: 'm_report_prod_dl_button',
    MODULE_REPORT_PROD_C_PRINT: 'm_report_prod_c_print',
    MODULE_REPORT_PROD_DL_RP_C_REP_EXCEL: 'm_report_prod_dl_rp_c_rep_excel',
    MODULE_REPORT_PROD_DL_RP_C_REP_CSV: 'm_report_prod_dl_rp_c_rep_csv',
    MODULE_REPORT_PROD_C_CHART_TODAY: 'm_report_prod_c_chart_today',
    MODULE_REPORT_PROD_C_CHART_TW: 'm_report_prod_c_chart_tw',
    MODULE_REPORT_PROD_C_CHART_MONTH: 'm_report_prod_c_chart_month',
    MODULE_REPORT_PROD_C_CHART_3_MONTHS: 'm_report_prod_c_chart_3_months',
    MODULE_REPORT_PROD_C_CHART_6_MONTHS: 'm_report_prod_c_chart_6_months',
    MODULE_REPORT_PROD_C_CHART_1_YEAR: 'm_report_prod_c_chart_1_year',
    MODULE_REPORT_PROD_C_CHART_YESTERDAY: 'm_report_prod_c_chart_yesterday',
    MODULE_REPORT_PROD_C_CALENDAR_START: 'm_report_prod_c_calendar_start',
    MODULE_REPORT_PROD_C_CALENDAR_END: 'm_report_prod_c_calendar_end',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_PROD_NAME: 'm_report_prod_sum_low_table_c_s_t_p_name',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_CAT: 'm_report_prod_sum_low_table_c_sort_table_cat',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_SKU: 'm_report_prod_sum_low_table_c_sort_table_sku',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_ITEMS: 'm_report_prod_sum_low_table_c_sort_table_items',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_SALES: 'm_report_prod_sum_low_table_c_s_t_sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_SALES_C_ICON_QM: 'm_report_prod_sum_low_table_sales_c_icon_qm',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_DISC_RM: 'm_report_prod_sum_low_table_c_s_t_d_rm',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_DISC_PERC: 'm_report_prod_sum_low_table_c_s_t_disc_p',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_PAY_TYPE: 'm_report_prod_sum_low_t_c_sort_t_pay_t',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_NET_SALES: 'm_report_prod_s_low_t_c_sort_t_net_sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_NET_SALES_C_ICON_QM: 'm_report_prod_s_low_t_net_sales_c_i_qm',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_ORDER_ID: 'm_report_prod_s_l_t_ind_sum_c_s_t_o_id',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TRAN_DATE: 'm_report_prod_sum_l_t_ind_sum_c_sort_tran_date',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_SALES: 'm_report_prod_sum_l_t_i_s_c_s_t_sale',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_SALES_C_ICON_QM: 'm_report_prod_sum_low_table_ind_sum_sales_c_icon_qm',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_DISC_RM: 'm_report_prod_sum_low_t_i_s_c_s_t_d_rm',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_DISC_PERC: 'm_report_prod_sum_low_t_i_s_c_sort_t_d_p',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_TAX: 'm_report_prod_sum_low_tab_i_s_c_s_t_tax',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_SERV_CHAR: 'm_report_prod_sum_low_table_i_s_c_s_s_c',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_SALES_RET: 'm_report_prod_sum_low_table_i_s_c_s_s_r',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_NET_SALES: 'm_report_prod_sum_low_t_i_s_c_s_t_net_s',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_NET_SALES_C_ICON_QM: 'm_report_prod_sum_l_t_i_s_n_s_c_i_qm',
    MODULE_REPORT_PROD_C_PREV_PAGE_BUTTON: 'm_report_prod_c_prev_page_button',
    MODULE_REPORT_PROD_C_NEXT_PAGE_BUTTON: 'm_report_prod_c_next_page_button',
    MODULE_REPORT_PROD_IND_SUM_C_PREV_PAGE_BUTTON: 'm_report_prod_ind_sum_c_prev_page_button',
    MODULE_REPORT_PROD_IND_SUM_C_NEXT_PAGE_BUTTON: 'm_report_prod_ind_sum_c_next_page_button',
    MODULE_REPORT_PROD_TB_PAGE: 'm_report_prod_tb_page',

    // Report Sales - Overtime
    MODULE_REPORT_OV_C_LOGO: 'm_report_ov_c_logo',
    MODULE_REPORT_OV_C_PROFILE: 'm_report_ov_c_profile',
    MODULE_REPORT_OV_DD_APP_ORDER: 'm_report_ov_dd_app_order',
    MODULE_REPORT_OV_TB_SEARCH: 'm_report_ov_tb_search',
    MODULE_REPORT_OV_DL_C_REP_DL_EXCEL: 'm_report_ov_dl_c_rep_dl_excel',
    MODULE_REPORT_OV_DL_C_REP_DL_CSV: 'm_report_ov_dl_c_rep_dl_csv',
    MODULE_REPORT_OV_CHART_C_CALENDAR_START: 'm_report_ov_chart_c_calendar_start',
    MODULE_REPORT_OV_CHART_C_CALENDAR_END: 'm_report_ov_chart_c_calendar_end',
    MODULE_REPORT_OV_C_CHART_TODAY: 'm_report_ov_c_chart_today',
    MODULE_REPORT_OV_C_CHART_TW: 'm_report_ov_c_chart_tw',
    MODULE_REPORT_OV_C_CHART_MONTH: 'm_report_ov_c_chart_month',
    MODULE_REPORT_OV_C_CHART_3_MONTHS: 'm_report_ov_c_chart_3_months',
    MODULE_REPORT_OV_C_CHART_6_MONTHS: 'm_report_ov_c_chart_6_months',
    MODULE_REPORT_OV_C_CHART_1_YEAR: 'm_report_ov_c_chart_1_year',
    MODULE_REPORT_OV_C_CHART_YESTERDAY: 'm_report_ov_c_chart_yesterday',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_ORDER: 'm_report_ov_sum_table_c_table_order',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SALES: 'm_report_ov_sum_table_c_table_sales',
    MODULE_REPORT_OV_SUM_TABLE_SALES_C_INFO_QM: 'm_report_ov_sum_table_sales_c_info_qm',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_ov_sum_table_c_table_disc_rm',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_ov_sum_table_c_table_d_perc',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_TAX: 'm_report_ov_sum_table_c_table_tax',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SC: 'm_report_ov_sum_table_c_table_sc',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SR: 'm_report_ov_sum_table_c_table_sr',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_NET_SALES: 'm_report_ov_sum_table_c_table_net_sales',
    MODULE_REPORT_OV_SUM_TABLE_NS_C_ICON_QM: 'm_report_ov_sum_table_ns_c_icon_qm',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_GP: 'm_report_ov_sum_table_c_table_gp',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_AVG_NS: 'm_report_ov_sum_table_c_table_avg_ns',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_TIME: 'm_report_ov_details_c_t_time',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SALES: 'm_report_ov_details_c_t_sales',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_ICON_QM: 'm_report_ov_details_c_icon_qm',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_D_RM: 'm_report_ov_details_c_table_d_rm',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_D_PERC: 'm_report_ov_details_c_table_d_perc',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_ov_details_c_table_tax',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SC: 'm_report_ov_details_c_table_sc',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SR: 'm_report_ov_details_c_table_sr',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_NS: 'm_report_ov_details_c_table_ns',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: 'm_report_ov_sum_tab_ind_sum_t_ns_c_i_qm',

    // Report - Sales Payment Method
    MODULE_REPORT_PMR_C_LOGO: 'm_report_pmr_c_logo',
    MODULE_REPORT_PMR_C_PROFILE_ICON: 'm_report_pmr_c_profile_icon',
    MODULE_REPORT_PMR_DL_RP_C_CSV: 'm_report_pmr_dl_rp_c_csv',
    MODULE_REPORT_PMR_DL_RP_C_EXCEL: 'm_report_pmr_dl_rp_c_excel',
    MODULE_REPORT_PMR_C_DOWNLOAD: 'm_report_pmr_c_download',
    MODULE_REPORT_PMR_C_CALENDAR_START: 'm_report_pmr_c_calendar_start',
    MODULE_REPORT_PMR_C_CALENDAR_END: 'm_report_pmr_c_calendar_end',
    MODULE_REPORT_PMR_TABLE_C_SORT_PAY_METHOD: 'm_report_pmr_table_c_sort_pay_method',
    MODULE_REPORT_PMR_TABLE_C_SORT_ORDER: 'm_report_pmr_table_c_sort_order',
    MODULE_REPORT_PMR_TABLE_C_SORT_SALES: 'm_report_pmr_table_c_sort_sales',
    MODULE_REPORT_PMR_TABLE_SALES_C_ICON_QM: 'm_report_pmr_table_sales_c_icon_qm',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_PM: 'm_report_pmr_table_ind_sum_c_table_pm',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_TRAN: 'm_report_pmr_table_ind_sum_c_table_tran',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_O: 'm_report_pmr_table_ind_sum_c_table_o',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_S: 'm_report_pmr_table_ind_sum_c_table_s',
    MODULE_REPORT_PMR_TABLE_IND_SUM_TABLE_S_C_ICON_QM: 'm_report_pmr_table_ind_sum_t_s_c_i_qm',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_DISC_RM: 'm_report_pmr_table_ind_sum_c_table_disc_rm',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_DISC_PERC: 'm_report_pmr_table_ind_sum_c_t_disc_perc',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_pmr_table_ind_sum_c_table_tax',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_SC: 'm_report_pmr_table_ind_sum_c_table_sc',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_SR: 'm_report_pmr_table_ind_sum_c_table_sr',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_NS: 'm_report_pmr_table_ind_sum_c_table_ns',
    MODULE_REPORT_PMR_TABLE_IND_SUM_TABLE_NS_C_ICON_QM: 'm_report_pmr_table_ind_sum_t_ns_c_i_qm',
    MODULE_REPORT_PMR_TABLE_C_PREV_PAGE_BUTTON: 'm_report_pmr_sum_table_c_p_page_button',
    MODULE_REPORT_PMR_TABLE_C_NEXT_PAGE_BUTTON: 'm_report_pmr_sum_table_c_n_page_button',
    MODULE_REPORT_PMR_TABLE_IND_TABLE_C_P_P_B: 'm_report_pmr_details_c_p_p_b',
    MODULE_REPORT_PMR_TABLE_IND_TABLE_C_P_N_B: 'm_report_pmr_details_c_p_n_b',

    // Report - Pay In Out 
    MODULE_REPORT_PIO_C_LOGO: 'm_report_pio_c_logo',
    MODULE_REPORT_PIO_C_PROFILE: 'm_report_pio_c_profile',
    MODULE_REPORT_PIO_DL_REP_C_REP_EXCEL: 'm_report_pio_dl_rep_c_rep_excel',
    MODULE_REPORT_PIO_DL_REP_C_REP_CSV: 'm_report_pio_dl_rep_c_rep_csv',
    MODULE_REPORT_PIO_C_CALENDAR_START: 'm_report_pio_c_calendar_start',
    MODULE_REPORT_PIO_C_CALENDAR_END: 'm_report_pio_c_calendar_end',
    MODULE_REPORT_PIO_TABLE_C_SORT_OP_TIME: 'm_report_pio_table_c_sort_op_time',
    MODULE_REPORT_PIO_TABLE_C_SORT_CASH: 'm_report_pio_table_c_sort_cash',
    MODULE_REPORT_PIO_TABLE_C_SORT_ON_OFF_TRAN: 'm_report_pio_table_c_sort_on_off_tran',
    MODULE_REPORT_PIO_TABLE_C_SORT_TRAN: 'm_report_pio_table_c_sort_tran',
    MODULE_REPORT_PIO_TABLE_C_SORT_ON_OFF_SALES: 'm_report_pio_table_c_sort_on_off_sales',
    MODULE_REPORT_PIO_TABLE_C_SORT_SALES: 'm_report_pio_table_c_sort_sales',
    MODULE_REPORT_PIO_TABLE_SALES_C_ICON_QM: 'm_report_pio_table_sales_c_icon_qm',
    MODULE_REPORT_PIO_TABLE_C_SORT_OP_CL_BAL: 'm_report_pio_table_c_sort_op_cl_bal',

    // Report - Refund Sales Report
    MODULE_REPORT_RF_C_LOGO: 'm_report_rf_c_logo',
    MODULE_REPORT_RF_C_PROFILE: 'm_report_rf_c_profile',
    MODULE_REPORT_RF_C_DOWNLOAD_BTN: 'm_report_rf_c_download_btn',
    MODULE_REPORT_RF_DL_BTN_TB_EMAIL: 'm_report_rf_dl_btn_tb_email',
    MODULE_REPORT_RF_DD_APP_ORDER: 'm_report_rf_dd_app_order',
    MODULE_REPORT_RF_DL_BTN_C_REP_EXCEL: 'm_report_rf_dl_btn_c_rep_excel',
    MODULE_REPORT_RF_DL_BTN_C_REP_CSV: 'm_report_rf_dl_btn_c_rep_csv',
    MODULE_REPORT_RF_DD_CHART_CAT: 'm_report_rf_dd_chart_cat',
    MODULE_REPORT_RF_C_CAL_START: 'm_report_rf_c_cal_start',
    MODULE_REPORT_RF_C_CAL_END: 'm_report_rf_c_cal_end',
    MODULE_REPORT_RF_C_CHART_TODAY: 'm_report_rf_c_chart_today',
    MODULE_REPORT_RF_C_CHART_TW: 'm_report_rf_c_chart_tw',
    MODULE_REPORT_RF_C_CHART_MONTH: 'm_report_rf_c_chart_month',
    MODULE_REPORT_RF_C_CHART_3_MONTHS: 'm_report_rf_c_chart_3_months',
    MODULE_REPORT_RF_C_CHART_6_MONTHS: 'm_report_rf_c_chart_6_months',
    MODULE_REPORT_RF_C_CHART_1_YEAR: 'm_report_rf_c_chart_1_year',
    MODULE_REPORT_RF_C_CHART_YESTERDAY: 'm_report_rf_c_chart_yesterday',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_PROD: 'm_report_rf_sum_table_c_table_prod',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_CAT: 'm_report_rf_sum_table_c_table_cat',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_SKU: 'm_report_rf_sum_table_c_table_sku',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_ITEMS: 'm_report_rf_sum_table_c_table_items',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_REF: 'm_report_rf_sum_table_c_table_ref',
    MODULE_REPORT_RF_SUM_TABLE_SORT_REF_C_ICON_QM: 'm_report_rf_sum_table_sort_ref_c_icon_qm',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_rf_sum_table_c_table_disc_rm',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_rf_sum_table_c_table_disc_perc',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_PM: 'm_report_rf_sum_table_c_table_pm',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_ID: 'm_report_rf_details_t_c_t_id',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_TD: 'm_report_rf_details_t_c_t_td',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_R: 'm_report_rf_details_t_c_t_r',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_R_C_I_QM: 'm_report_rf_details_t_r_c_i_qm',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_D_RM: 'm_report_rf_details_t_c_t_d_rm',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_D_P: 'm_report_rf_details_t_c_t_d_p',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_T: 'm_report_rf_details_t_c_t_t',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_SC: 'm_report_rf_details_t_c_t_sc',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_SR: 'm_report_rf_details_t_c_t_sr',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_NS: 'm_report_rf_sum_table_i_s_table_c_t_ns',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_NS_C_I_QM: 'm_report_rf_sum_table_i_s_table_ns_c_i_qm',
    MODULE_REPORT_RF_SUM_TABLE_C_PREV_PAGE_BUTTON: 'm_report_rf_sum_table_c_prev_page_button',
    MODULE_REPORT_RF_SUM_TABLE_C_NEXT_PAGE_BUTTON: 'm_report_rf_sum_table_c_next_page_button',
    MODULE_REPORT_RF_SUM_TABLE_IND_TABLE_C_P_P_B: 'm_report_rf_details_c_p_p_b',
    MODULE_REPORT_RF_SUM_TABLE_IND_TABLE_C_P_N_B: 'm_report_rf_details_c_p_n_b',

    // Report - Orders Channel Report
    MODULE_REPORT_CHA_C_LOGO: 'm_report_cha_c_logo',
    MODULE_REPORT_CHA_C_PROFILE: 'm_report_cha_c_profile',
    MODULE_REPORT_CHA_DD_APP_ORDER: 'm_report_cha_dd_app_order',
    MODULE_REPORT_CHA_C_DOWNLOAD_BTN: 'm_report_cha_c_download_btn',
    MODULE_REPORT_CHA_DL_BTN_TB_EMAIL: 'm_report_cha_dl_btn_tb_email',
    MODULE_REPORT_CHA_DL_BTN_C_REP_EXCEL: 'm_report_cha_dl_btn_c_rep_excel',
    MODULE_REPORT_CHA_DL_BTN_C_REP_CSV: 'm_report_cha_dl_btn_c_rep_csv',
    MODULE_REPORT_CHA_TB_SEARCH: 'm_report_cha_tb_search',
    MODULE_REPORT_CHA_C_CAL_START: 'm_report_cha_c_cal_start',
    MODULE_REPORT_CHA_C_CAL_END: 'm_report_cha_c_cal_end',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TRAN_CA: 'm_report_cha_sum_table_c_tran_ca',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_OR: 'm_report_cha_sum_table_c_tran_or',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SALES: 'm_report_cha_sum_table_c_tran_sales',
    MODULE_REPORT_CHA_SUM_TABLE_SALES_C_ICON_QM: 'm_report_cha_sum_table_sales_c_icon_qm',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_cha_sum_table_c_table_disc_perc',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_cha_sum_table_c_table_disc_rm',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TAX: 'm_report_cha_sum_table_c_table_tax',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_CHAR: 'm_report_cha_sum_table_c_table_serv_char',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_RET: 'm_report_cha_sum_table_c_table_serv_ret',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_NET_SALES: 'm_report_cha_sum_table_c_table_net_sales',
    MODULE_REPORT_CHA_SUM_TABLE_NS_C_ICON_QM: 'm_report_cha_sum_table_ns_c_icon_qm',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_CA: 'm_report_cha_details_c_tran_ca',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_DT: 'm_report_cha_details_c_tran_dt',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SALES: 'm_report_cha_details_c_tran_sales',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: 'm_report_cha_details_t_s_c_icon_qm',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: 'm_report_cha_details_c_table_disc_perc',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: 'm_report_cha_details_c_table_disc_rm',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_cha_details_c_table_tax',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_CHAR: 'm_report_cha_details_c_table_serv_char',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_RET: 'm_report_cha_details_c_table_serv_ret',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_NET_SALES: 'm_report_cha_details_c_table_net_sales',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: 'm_report_cha_details_t_ns_c_icon_qm',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_PREV_PAGE_BUTTON: 'm_report_cha_details_c_prev_page',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BUTTON: 'm_report_cha_details_c_next_page',
    MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_P_B: 'm_report_cha_details_c_p_p_b',
    MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_N_B: 'm_report_cha_details_c_p_n_b',

    // Report - Add-On Report
    // Report - Add-On Report
    MODULE_REPORT_ADN_C_LOGO: 'm_report_adn_c_logo',
    MODULE_REPORT_ADN_C_PROFILE: 'm_report_adn_c_profile',
    MODULE_REPORT_ADN_DD_APP_TYPE: 'm_report_adn_dd_app_type',
    MODULE_REPORT_ADN_C_DOWNLOAD_BTN: 'm_report_adn_c_download_btn',
    MODULE_REPORT_ADN_DL_BTN_TB_EMAIL: 'm_report_adn_dl_btn_tb_email',
    MODULE_REPORT_ADN_DL_BTN_C_REP_EXCEL: 'm_report_adn_dl_btn_c_rep_excel',
    MODULE_REPORT_ADN_DL_BTN_C_REP_CSV: 'm_report_adn_dl_btn_c_rep_csv',
    MODULE_REPORT_ADN_C_CAL_START: 'm_report_adn_c_cal_start',
    MODULE_REPORT_ADN_C_CAL_END: 'm_report_adn_c_cal_end',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_AO_GN: 'm_report_adn_sum_table_c_table_ao_gn', // Add-Ons Group Name
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_AO_O: 'm_report_adn_sum_table_c_table_ao_o',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_ITEM_S: 'm_report_adn_sum_table_c_table_item_s',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SALES: 'm_report_adn_sum_table_c_table_sales',
    MODULE_REPORT_ADN_SUM_TABLE_T_S_C_ICON_QM: 'm_report_adn_sum_table_t_s_c_icon_qm',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_adn_sum_table_c_table_disc_rm',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_adn_sum_table_c_table_disc_perc',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_TAX: 'm_report_adn_sum_table_c_table_tax',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SERV_CHAR: 'm_report_adn_sum_table_c_table_serv_char',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SALE_RET: 'm_report_adn_sum_table_c_table_sale_ret',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_NET_SALES: 'm_report_adn_sum_table_c_table_net_sales',
    MODULE_REPORT_ADN_SUM_TABLE_T_NS_C_ICON_QM: 'm_report_adn_sum_table_t_ns_c_icon_qm',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_O_ID: 'm_report_adn_details_c_table_o_id',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_TD: 'm_report_adn_details_c_table_td',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_S: 'm_report_adn_details_c_table_s', // Sales
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: 'm_report_adn_details_t_s_c_icon_qm',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: 'm_report_adn_details_c_disc_rm',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: 'm_report_adn_details_c_disc_perc',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_adn_details_c_table_tax',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_SC: 'm_report_adn_details_c_table_sc',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_SR: 'm_report_adn_details_c_table_sr',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_NS: 'm_report_adn_details_c_table_ns',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_T_S_NS_ICON_QM: 'm_report_adn_details_t_s_ns_icon_qm',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_PREV_PAGE_BTN: 'm_report_adn_sdetails_c_prev_page',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BTN: 'm_report_adn_details_c_next_page',
    MODULE_REPORT_ADN_SUM_TABLE_IND_TABLE_C_P_P_B: 'm_report_adn_sum_table_ind_table_c_p_p_b',
    MODULE_REPORT_ADN_SUM_TABLE_IND_TABLE_C_N_P_B: 'm_report_adn_sum_table_ind_table_c_n_p_b',

    // Report - Category Sales Report
    MODULE_REPORT_CAT_C_LOGO: 'm_report_cat_c_logo',
    MODULE_REPORT_CAT_C_PROFILE: 'm_report_cat_c_profile',
    MODULE_REPORT_CAT_DD_APP_TYPE: 'm_report_cat_dd_app_type',
    MODULE_REPORT_CAT_C_DOWNLOAD_BTN: 'm_report_cat_c_download_btn',
    MODULE_REPORT_CAT_DL_BTN_TB_EMAIL: 'm_report_cat_dl_btn_tb_email',
    MODULE_REPORT_CAT_DL_BTN_C_REP_EXCEL: 'm_report_cat_dl_btn_c_rep_excel',
    MODULE_REPORT_CAT_DL_BTN_C_REP_CSV: 'm_report_cat_dl_btn_c_rep_csv',
    MODULE_REPORT_CAT_C_CAL_START: 'm_report_cat_c_cal_start',
    MODULE_REPORT_CAT_C_CAL_END: 'm_report_cat_c_cal_end',
    MODULE_REPORT_CAT_TB_SEARCH: 'm_report_cat_tb_search',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_PROD_CAT: 'm_report_cat_sum_table_c_table_prod_cat',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_ITEM_SOLD: 'm_report_cat_sum_table_c_table_item_sold',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALES: 'm_report_cat_sum_table_c_table_sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_S_C_ICON_QM: 'm_report_cat_sum_table_t_s_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_cat_sum_table_c_table_disc_rm',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_cat_sum_table_c_table_disc_perc',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_TAX: 'm_report_cat_sum_table_c_table_tax',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SERV_CHAR: 'm_report_cat_sum_table_c_table_serv_char',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALE_RET: 'm_report_cat_sum_table_c_table_sale_ret',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_NET_SALES: 'm_report_cat_sum_table_c_table_net_sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_NS_C_ICON_QM: 'm_report_cat_sum_table_t_ns_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_O_ID: 'm_report_adn_details_c_table_o_id',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TD: 'm_report_cat_details_c_table_td',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_S: 'm_report_cat_details_c_table_s', // Sales
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: 'm_report_cat_details_t_s_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: 'm_report_cat_details_c_disc_rm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: 'm_report_cat_details_c_disc_perc',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_cat_details_c_table_tax',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SC: 'm_report_cat_details_c_table_sc',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SR: 'm_report_cat_details_c_table_sr',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_NS: 'm_report_cat_details_c_table_ns',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: 'm_report_cat_details_t_ns_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_PREV_PAGE_BTN: 'm_report_cat_details_c_prev_page',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BTN: 'm_report_cat_details_c_next_page',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_P_P_B: 'm_report_cat_details_c_p_p_b',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_N_P_B: 'm_report_cat_details_c_n_p_b',

    // Report - Category Sales Report
    MODULE_REPORT_CAT_C_LOGO: 'm_report_cat_c_logo',
    MODULE_REPORT_CAT_C_PROFILE: 'm_report_cat_c_profile',
    MODULE_REPORT_CAT_DD_APP_TYPE: 'm_report_cat_dd_app_type',
    MODULE_REPORT_CAT_C_DOWNLOAD_BTN: 'm_report_cat_c_download_btn',
    MODULE_REPORT_CAT_DL_BTN_TB_EMAIL: 'm_report_cat_dl_btn_tb_email',
    MODULE_REPORT_CAT_DL_BTN_C_REP_EXCEL: 'm_report_cat_dl_btn_c_rep_excel',
    MODULE_REPORT_CAT_DL_BTN_C_REP_CSV: 'm_report_cat_dl_btn_c_rep_csv',
    MODULE_REPORT_CAT_C_CAL_START: 'm_report_cat_c_cal_start',
    MODULE_REPORT_CAT_C_CAL_END: 'm_report_cat_c_cal_end',
    MODULE_REPORT_CAT_TB_SEARCH: 'm_report_cat_tb_search',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_PROD_CAT: 'm_report_cat_sum_table_c_table_prod_cat',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_ITEM_SOLD: 'm_report_cat_sum_table_c_table_item_sold',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALES: 'm_report_cat_sum_table_c_table_sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_S_C_ICON_QM: 'm_report_cat_sum_table_t_s_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_RM: 'm_report_cat_sum_table_c_table_disc_rm',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_PERC: 'm_report_cat_sum_table_c_table_disc_perc',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_TAX: 'm_report_cat_sum_table_c_table_tax',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SERV_CHAR: 'm_report_cat_sum_table_c_table_serv_char',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALE_RET: 'm_report_cat_sum_table_c_table_sale_ret',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_NET_SALES: 'm_report_cat_sum_table_c_table_net_sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_NS_C_ICON_QM: 'm_report_cat_sum_table_t_ns_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_O_ID: 'm_report_adn_details_c_table_o_id',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TD: 'm_report_cat_details_c_table_td',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_S: 'm_report_cat_details_c_table_s', // Sales
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: 'm_report_cat_details_t_s_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: 'm_report_cat_details_c_disc_rm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: 'm_report_cat_details_c_disc_perc',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TAX: 'm_report_cat_details_c_table_tax',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SC: 'm_report_cat_details_c_table_sc',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SR: 'm_report_cat_details_c_table_sr',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_NS: 'm_report_cat_details_c_table_ns',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: 'm_report_cat_details_t_ns_c_icon_qm',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_PREV_PAGE_BTN: 'm_report_cat_details_c_prev_page_btn',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BTN: 'm_report_cat_sum_t_ind_sum_c_next_page_btn',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_P_P_B: 'm_report_cat_details_c_p_p_b',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_N_P_B: 'm_report_cat_details_c_n_p_b',

    // Report - Product & Category
    MODULE_REPORT_PNC_C_LOGO: 'm_report_pnc_c_logo',
    MODULE_REPORT_PNC_C_PROFILE: 'm_report_pnc_c_profile',
    MODULE_REPORT_PNC_DD_APP_TYPE: 'm_report_pnc_dd_app_type',
    MODULE_REPORT_PNC_C_DOWNLOAD_BTN: 'm_report_pnc_c_download_btn',
    MODULE_REPORT_PNC_DL_BTN_TB_EMAIL: 'm_report_pnc_dl_btn_tb_email',
    MODULE_REPORT_PNC_DL_BTN_C_REP_EXCEL: 'm_report_pnc_dl_btn_c_rep_excel',
    MODULE_REPORT_PNC_DL_BTN_C_REP_CSV: 'm_report_pnc_dl_btn_c_rep_csv',
    MODULE_REPORT_PNC_C_CAL_START: 'm_report_pnc_c_cal_start',
    MODULE_REPORT_PNC_C_CAL_END: 'm_report_pnc_c_cal_end',
    MODULE_REPORT_PNC_TB_SEARCH: 'm_report_pnc_tb_search',
    // MODULE_REPORT_PNC_DD_DATE_LOG: 'm_report_pnc_dd_date_log',
    MODULE_REPORT_PNC_DD_PROD_FILTER: 'm_report_pnc_dd_prod_filter',
    MODULE_REPORT_PNC_DD_CHANNEL_FILTER: 'm_report_pnc_dd_channel_filter',
    MODULE_REPORT_PNC_C_PRINT: 'm_report_pnc_c_print',
    MODULE_REPORT_PNC_C_PRINT_WO_TAX_SC: 'm_report_pnc_c_print_wo_tax_sc',
    MODULE_REPORT_PNC_C_CAL_START: 'm_report_pnc_c_cal_start',
    MODULE_REPORT_PNC_C_CAL_END: 'm_report_pnc_c_cal_end',
    MODULE_REPORT_PNC_C_CHART_TODAY: 'm_report_pnc_c_chart_today',
    MODULE_REPORT_PNC_C_CHART_TW: 'm_report_pnc_c_chart_tw',
    MODULE_REPORT_PNC_C_CHART_MONTH: 'm_report_pnc_c_chart_month',
    MODULE_REPORT_PNC_C_CHART_3_MONTHS: 'm_report_pnc_c_chart_3_months',
    MODULE_REPORT_PNC_C_CHART_6_MONTHS: 'm_report_pnc_c_chart_6_months',
    MODULE_REPORT_PNC_C_CHART_1_YEAR: 'm_report_pnc_c_chart_1_year',
    MODULE_REPORT_PNC_C_CHART_YESTERDAY: 'm_report_pnc_c_chart_yesterday',
    MODULE_REPORT_PNC_SUM_TABLE_C_PROD_CAT: 'm_report_pnc_sum_table_c_prod_cat',
    MODULE_REPORT_PNC_SUM_TABLE_C_ITEM_SOLD: 'm_report_pnc_sum_table_c_item_sold',
    MODULE_REPORT_PNC_SUM_TABLE_C_SALES: 'm_report_pnc_sum_table_c_sales',
    MODULE_REPORT_PNC_SUM_TABLE_SALES_C_ICON_QM: 'm_report_pnc_sum_table_sales_c_icon_qm',
    MODULE_REPORT_PNC_SUM_TABLE_C_DISC_RM: 'm_report_pnc_sum_table_c_disc_rm',
    MODULE_REPORT_PNC_SUM_TABLE_C_DISC_PERC: 'm_report_pnc_sum_table_c_disc_perc',
    MODULE_REPORT_PNC_SUM_TABLE_C_TAX: 'm_report_pnc_sum_table_c_tax',
    MODULE_REPORT_PNC_SUM_TABLE_C_SERV_CHAR: 'm_report_pnc_sum_table_c_serv_char',
    MODULE_REPORT_PNC_SUM_TABLE_C_SALES_RET: 'm_report_pnc_sum_table_c_sales_ret',
    MODULE_REPORT_PNC_SUM_TABLE_C_NET_SALES: 'm_report_pnc_sum_table_c_net_sales',
    MODULE_REPORT_PNC_SUM_TABLE_NET_SALES_C_ICON_QM: 'm_report_pnc_sum_table_net_sales_c_icon_qm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_PROD: 'm_report_pnc_details_t_c_prod',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_CAT: 'm_report_pnc_details_t_c_cat',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SKU: 'm_report_pnc_details_t_c_sku',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_ITEMS: 'm_report_pnc_details_t_c_items',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SALES: 'm_report_pnc_details_t_c_sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_SALES_C_ICON_QM: 'm_report_pnc_details_t_sal_c_i_qm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_DISC_PERC: 'm_report_pnc_details_t_c_disc_p',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_DISC_RM: 'm_report_pnc_details_t_c_disc_rm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_PAY_TYPE: 'm_report_pnc_details_t_c_pay_type',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_NET_SALES: 'm_report_pnc_details_t_c_net_sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_NS_C_ICON_QM: 'm_report_pnc_details_t_ns_c_icon_qm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SUMMARY_BUTTON: 'm_report_pnc_details_t_c_s_b',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_O_ID: 'm_report_pnc_details_sec_sum_table_c_o_id',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_TD: 'm_report_pnc_details_sec_table_c_td',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SALES: 'm_report_pnc_details_sec_sum_table_c_sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_SALES_C_ICON_QM: 'm_report_pnc_details_sec_sum_t_s_c_i_qm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_DISC_PERC: 'm_report_pnc_details_sec_sum_t_c_dc_p',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_DISC_RM: 'm_report_pnc_details_sec_sum_t_c_d_rm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_TAX: 'm_report_pnc_details_sec_sum_t_c_tax',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SERV_CHAR: 'm_report_pnc_details_sec_sum_t_c_ser_cha',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SALES_RET: 'm_report_pnc_details_sec_sum_t_c_s_ret',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_NET_SALES: 'm_report_pnc_details_sec_sum_t_c_net_s',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_NS_C_ICON_QM: 'm_report_pnc_details_sec_sum_t_ns_c_i_qm',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SUMMARY_BUTTON: 'm_report_pnc_details_s_s_c_sum_but',
    MODULE_REPORT_PNC_SUM_TABLE_C_PREV_PAGE: 'm_report_pnc_sum_table_c_prev_page',
    MODULE_REPORT_PNC_SUM_TABLE_C_NEXT_PAGE: 'm_report_pnc_sum_table_c_next_page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_C_PREV_PAGE: 'm_report_pnc_details_c_prev_page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_C_NEXT_PAGE: 'm_report_pnc_details_c_next_page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_C_PREV_PAGE: 'm_report_pnc_details_s_s_c_prev_page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_C_NEXT_PAGE: 'm_report_pnc_details_s_s_c_next_page',

    // Report - Variant Sales
    MODULE_REPORT_VAR_C_LOGO: 'm_report_var_c_logo',
    MODULE_REPORT_VAR_C_PROFILE: 'm_report_var_c_profile',
    MODULE_REPORT_VAR_DD_APP_TYPE: 'm_report_var_dd_app_type',
    MODULE_REPORT_VAR_C_DOWNLOAD_BTN: 'm_report_var_c_download_btn',
    MODULE_REPORT_VAR_DL_BTN_TB_EMAIL: 'm_report_var_dl_btn_tb_email',
    MODULE_REPORT_VAR_DL_BTN_C_REP_EXCEL: 'm_report_var_dl_btn_c_rep_excel',
    MODULE_REPORT_VAR_DL_BTN_C_REP_CSV: 'm_report_var_dl_btn_c_rep_csv',
    MODULE_REPORT_VAR_C_CAL_START: 'm_report_var_c_cal_start',
    MODULE_REPORT_VAR_C_CAL_END: 'm_report_var_c_cal_end',
    MODULE_REPORT_VAR_TB_SEARCH: 'm_report_var_tb_search',
    MODULE_REPORT_VAR_SUM_TABLE_C_VAR_NAME: 'm_report_var_sum_table_c_var_name',
    MODULE_REPORT_VAR_SUM_TABLE_C_VAR_OPT: 'm_report_var_sum_table_c_var_opt',
    MODULE_REPORT_VAR_SUM_TABLE_C_ITEM_SOLD: 'm_report_var_sum_table_c_var_opt',
    MODULE_REPORT_VAR_SUM_TABLE_C_SALES: 'm_report_var_sum_table_c_sales',
    MODULE_REPORT_VAR_SUM_TABLE_SALES_C_ICON_QM: 'm_report_var_sum_table_sales_c_icon_qm',
    MODULE_REPORT_VAR_SUM_TABLE_C_DISC_PERC: 'm_report_var_sum_table_c_disc_perc',
    MODULE_REPORT_VAR_SUM_TABLE_C_DISC_RM: 'm_report_var_sum_table_c_disc_rm',
    MODULE_REPORT_VAR_SUM_TABLE_C_TAX: 'm_report_var_sum_table_c_tax',
    MODULE_REPORT_VAR_SUM_TABLE_C_SERV_CHAR: 'm_report_var_sum_table_c_serv_char',
    MODULE_REPORT_VAR_SUM_TABLE_C_SALE_RET: 'm_report_var_sum_table_c_sale_ret',
    MODULE_REPORT_VAR_SUM_TABLE_C_NET_SALES: 'm_report_var_sum_table_c_net_sales',
    MODULE_REPORT_VAR_SUM_TABLE_NS_C_ICON_QM: 'm_report_var_sum_table_ns_c_icon_qm',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_O_ID: 'm_report_var_details_table_c_o_id',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_TD: 'm_report_var_details_table_c_td',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SALES: 'm_report_var_details_table_c_sales',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_S_C_I_QM: 'm_report_var_details_table_s_c_i_qm',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_DISC_RM: 'm_report_var_details_table_c_disc_rm',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_DISC_PERC: 'm_report_var_details_table_c_disc_perc',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_TAX: 'm_report_var_details_table_c_tax',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SERV_C: 'm_report_var_details_table_c_serv_c',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SALE_R: 'm_report_var_details_table_c_sale_r',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_NET_S: 'm_report_var_details_table_c_net_s',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_NS_C_I_QM: 'm_report_var_details_table_ns_c_i_qm',
    MODULE_REPORT_VAR_SUM_TABLE_C_PREV_PAGE: 'm_report_var_sum_table_c_prev_page',
    MODULE_REPORT_VAR_SUM_TABLE_C_NEXT_PAGE: 'm_report_var_sum_table_c_next_page',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_C_PREV_PAGE: 'm_report_var_details_c_prev_page',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_C_NEXT_PAGE: 'm_report_var_details_c_next_page',
    MODULE_REPORT_VAR_SUM_TABLE_C_SUMMARY: 'm_report_var_sum_table_c_summary',

    // Report - Shift
    MODULE_REPORT_SHIFT_C_LOGO: 'm_report_shift_c_logo',
    MODULE_REPORT_SHIFT_C_PROFILE: 'm_report_shift_c_profile',
    MODULE_REPORT_SHIFT_DD_APP_TYPE: 'm_report_shift_dd_app_type',
    MODULE_REPORT_SHIFT_C_DOWNLOAD_BTN: 'm_report_shift_c_download_btn',
    MODULE_REPORT_SHIFT_DL_BTN_TB_EMAIL: 'm_report_shift_dl_btn_tb_email',
    MODULE_REPORT_SHIFT_DL_BTN_C_REP_EXCEL: 'm_report_shift_dl_btn_c_rep_excel',
    MODULE_REPORT_SHIFT_DL_BTN_C_REP_CSV: 'm_report_shift_dl_btn_c_rep_csv',
    MODULE_REPORT_SHIFT_C_CAL_START: 'm_report_shift_c_cal_start',
    MODULE_REPORT_SHIFT_C_CAL_END: 'm_report_shift_c_cal_end',
    MODULE_REPORT_SHIFT_TB_SEARCH: 'm_report_shift_tb_search',
    MODULE_REPORT_SHIFT_SUMMARY_C_OP_TABLE: 'm_report_shift_summary_c_op_table',
    MODULE_REPORT_SHIFT_SUMMARY_C_CASH: 'm_report_shift_summary_c_cash',
    MODULE_REPORT_SHIFT_SUMMARY_C_ONLINE_OFFLINE_TRANSACTION: 'm_report_shift_sum_c_on_off_tran',
    MODULE_REPORT_SHIFT_SUMMARY_C_TRANSACTION: 'm_report_shift_summary_c_transaction',
    MODULE_REPORT_SHIFT_SUMMARY_C_ONLINE_OFFLINE_SALES: 'm_report_shift_sum_c_on_off_sale',
    MODULE_REPORT_SHIFT_SUMMARY_C_SALES: 'm_report_shift_summary_c_sales',
    MODULE_REPORT_SHIFT_SUMMARY_SALES_C_ICON_QM: 'm_report_shift_summary_sales_c_icon_qm',
    MODULE_REPORT_SHIFT_SUMMARY_C_OPENING_CLOSING_BALANCE: 'm_report_shift_sum_c_open_clos_balance',
    MODULE_REPORT_SHIFT_SUMMARY_IND_LIST_C_PRINT: 'm_report_shift_sum_ind_list_c_print',
    MODULE_REPORT_SHIFT_SUMMARY_IND_LIST_C_PREVIEW: 'm_report_shift_sum_ind_list_c_preview',
    MODULE_REPORT_SHIFT_SUMMARY_C_IND_LIST: 'm_report_shift_sum_c_ind_list',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SUMMARY: 'm_report_shift_details_c_summary',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DATE_TIME: 'm_report_shift_details_c_date_time',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_CASHIER: 'm_report_shift_details_c_cashier',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SALES: 'm_report_shift_details_c_sales',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_SALES_C_ICON_QM: 'm_report_shift_details_sales_c_icon_qm',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DISC_RM: 'm_report_shift_details_c_disc_rm',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DISC_PERC: 'm_report_shift_details_c_disc_perc',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_TAX: 'm_report_shift_details_c_tax',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SERV_CHAR: 'm_report_shift_details_c_serv_char',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SALES_RET: 'm_report_shift_details_c_sales_ret',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_NET_SALES: 'm_report_shift_details_c_net_sales',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_NET_SALES_C_ICON_QM: 'm_report_shift_details_n_s_c_icon_qm',
    MODULE_REPORT_SHIFT_SUMMARY_DD_ITEMS_SHOWED: 'm_report_shift_summary_dd_items_showed',
    MODULE_REPORT_SHIFT_SUMMARY_TB_PAGE: 'm_report_shift_summary_tb_page',
    MODULE_REPORT_SHIFT_SUMMARY_C_PREV_BUTTON: 'm_report_shift_summary_c_prev_button',
    MODULE_REPORT_SHIFT_SUMMARY_C_NEXT_BUTTON: 'm_report_shift_summary_c_next_button',

    MODULE_CRM_CUSTOMERS_C_LOGO: 'm_crm_customers_c_logo',
    MODULE_CRM_CUSTOMERS_C_PROFILE: 'm_crm_customers_c_profile',
    MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN: 'm_crm_customers_c_download_btn',
    MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL: 'm_crm_customers_dl_btn_tb_email',
    MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL: 'm_crm_customers_dl_btn_c_rep_excel',
    MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV: 'm_crm_customers_dl_btn_c_rep_csv',
    MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD: 'm_crm_customers_c_batch_upload',
    MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP: 'm_crm_customers_batch_up_c_upload_temp',
    MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP: 'm_crm_customers_batch_up_c_export_temp',
    MODULE_CRM_CUSTOMERS_TB_SEARCH: 'm_crm_customers_tb_search',

    MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID: 'm_crm_customers_list_c_name_id',
    MODULE_CRM_CUSTOMERS_LIST_C_CONTACT_INFO: 'm_crm_customers_list_c_contact_info',
    MODULE_CRM_CUSTOMERS_LIST_C_GENDER: 'm_crm_customers_list_c_gender',
    MODULE_CRM_CUSTOMERS_LIST_C_DOB: 'm_crm_customers_list_c_dob',
    MODULE_CRM_CUSTOMERS_LIST_C_RACE: 'm_crm_customers_list_c_race',
    MODULE_CRM_CUSTOMERS_LIST_C_TIER: 'm_crm_customers_list_c_tier',
    MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING: 'm_crm_customers_list_c_avg_spending',

    MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER: 'm_crm_customers_c_new_customer',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_BACK: 'm_crm_customers_customer_c_back',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_SAVE: 'm_crm_customers_customer_c_save',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_EDIT: 'm_crm_customers_customer_c_edit',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_DELETE: 'm_crm_customers_customer_c_delete',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_NAME: 'm_crm_customers_customer_tb_name',
    MODULE_CRM_CUSTOMERS_CUSTOMER_DD_GENDER: 'm_crm_customers_customer_dd_gender',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_RACE: 'm_crm_customers_customer_tb_race',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_CONTACT_1: 'm_crm_customers_customer_tb_cont_1',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_CONTACT_2: 'm_crm_customers_customer_tb_cont_2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_DOB_CAL_ICON: 'm_crm_customers_customer_c_cal_icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_CUSTOMER_UPLOAD_IMAGE: 'm_crm_customers_customer_c_c_up_image',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_TAGS: 'm_crm_customers_customer_c_tags',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_C_ADD: 'm_crm_customers_customer_tags_c_add',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_ADD: 'm_crm_customers_customer_tags_tag_c_add',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_SEARCH: 'm_crm_customers_customer_tags_t_c_search',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_TAG_C_SELECT: 'm_crm_customers_customer_tags_t_t_c_sel',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_REMOVE: 'm_crm_customers_customer_tags_tag_c_r',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_NEXT_VISIT_DATE_CAL_ICON: 'm_crm_customers_customer_c_nvd_cal_ico',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_NEXT_VISIT_TIME_CAL_ICON: 'm_crm_customers_customer_c_nvt_cal_ico',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_FIRST_VISIT_CAL_ICON: 'm_crm_customers_customer_c_fv_cal_icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_LAST_VISIT_CAL_ICON: 'm_crm_customers_customer_c_lv_cal_icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_EMAIL1: 'm_crm_customers_customer_tb_email1',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_EMAIL2: 'm_crm_customers_customer_tb_email2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_ADDRESS1: 'm_crm_customers_customer_tb_add',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_ADDRESS2: 'm_crm_customer_customer_tb_add2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_PURCHASE_HISTORY: 'm_crm_customers_customer_sum_c_pur_hist',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_PURCHASE_HISTORY_C_DETAILS: 'm_crm_customers_customer_sum_p_h_c_det',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_CASHBACK: 'm_crm_customers_customer_sum_c_cbck',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_POINTS: 'm_crm_customers_customer_sum_c_points',
    MOUDLE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_VISITATION: 'm_crm_customers_customer_sum_c_visit',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_VISITATION_C_DETAILS: 'm_crm_customers_customer_sum_vis_c_det',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_VOUCHER: 'm_crm_customers_customer_sum_c_voucher',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_PROMOTION: 'm_crm_customers_customer_sum_c_promo',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_DOCKET: 'm_crm_customers_customer_sum_c_dock',
    MODULE_CRM_CUSTOMERS_LIST_C_CUSTOMER_DETAILS: 'm_crm_customers_list_c_customer_det',
    MODULE_CRM_CUSTOMERS_LIST_TB_PAGE: 'm_crm_customers_list_tb_page',
    MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON: 'm_crm_customers_list_c_prev_button',
    MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON: 'm_crm_customers_list_c_next_button',

    MODULE_CRM_SEGMENT_C_LOGO: 'm_crm_segment_c_logo',
    MODULE_CRM_SEGMENT_C_PROFILE: 'm_crm_segment_c_profile',
    MODULE_CRM_SEGMENT_C_NEW_SEGMENT: 'm_crm_segment_c_new_segment',
    MODULE_CRM_SEGMENT_TB_SEARCH: 'm_crm_segment_tb_search',
    MODULE_CRM_SEGMENT_LIST_C_SEGMENT_GROUP: 'm_crm_segment_list_c_segment_group',
    MODULE_CRM_SEGMENT_TB_PAGE: 'm_crm_segment_tb_page',
    MODULE_CRM_SEGMENT_C_PREV_BUTTON: 'm_crm_segment_c_prev_button',
    MODULE_CRM_SEGMENT_C_NEXT_BUTTON: 'm_crm_segment_c_next_button',

    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK : 'm_crm_segment_add_segment_c_back',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_UPDATE: 'm_crm_segment_add_segment_c_update',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_TB_SEGMENT_NAME: 'm_crm_segment_add_segment_tb_smt_name',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_TAGS: 'm_crm_segment_add_segment_c_tags',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_TAGS_C_TAG_SELECT: 'm_crm_segment_add_segment_tags_c_tag_sel',

    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_LOGO: 'm_employee_manage_emp_c_logo',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_PROFILE: 'm_employee_manage_emp_c_profile',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_DOWNLOAD_BTN: 'm_employee_manage_emp_c_download_btn',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_TB_EMAIL: 'm_employee_manage_emp_dl_btn_tb_email',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_EXCEL: 'm_employee_manage_emp_dl_btn_c_rep_xlsx',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_CSV: 'm_employee_manage_emp_dl_btn_c_rep_csv',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_NEW_EMPLOYEE: 'm_employee_manage_emp_c_new_emp',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_TB_SEARCH: 'm_employee_manage_emp_tb_search',

    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_BACK: 'm_employee_manage_emp_emp_c_back',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_SAVE: 'm_employee_manage_emp_emp_c_save',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_UPDATE: 'm_employee_manage_emp_emp_c_update',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_DELETE: 'm_employee_manage_emp_emp_c_delete',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_NAME: 'm_employee_manage_emp_emp_tb_name',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_USERNAME: 'm_employee_manage_emp_emp_tb_un',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_EMAIL: 'm_employee_manage_emp_emp_tb_email',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_PROFILE_PICTURE: 'm_employee_manage_emp_emp_c_pro_pic',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_ROLE: 'm_employee_manage_emp_emp_dd_role',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_OUTLET: 'm_employee_manage_emp_emp_dd_outlet',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_CONTACT_NUMBER: 'm_employee_manage_emp_emp_tb_c_num',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PASSWORD: 'm_employee_manage_emp_emp_tb_pass',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PIN: 'm_employee_manage_emp_emp_tb_pin',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_PRIVILEGES: 'm_employee_manage_emp_emp_dd_pr',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_SCREEN_BLOCK: 'm_employee_manage_emp_em_dd_sn_b',

    MODULE_EMPLOYEE_ACTIVITY_LOG_C_LOGO: 'm_employee_activity_log_c_logo',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_PROFILE: 'm_employee_activity_log_c_profile',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_DOWNLOAD_BTN: 'm_employee_activity_log_c_download_btn',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_TB_EMAIL: 'm_employee_activity_log_dl_btn_tb_email',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_C_REP_EXCEL: 'm_employee_activity_log_dl_btn_c_rep_xlsx',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_C_REP_CSV: 'm_employee_activity_log_dl_btn_c_rep_csv',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TB_SEARCH: 'm_employee_activity_log_tb_search',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_CAL_START: 'm_employee_activity_log_c_cal_start',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_CAL_END: 'm_employee_activity_log_c_cal_end',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DD_ITEMS_SHOWED: 'm_employee_activity_log_dd_items_showed',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TB_PAGE: 'm_employee_activity_log_tb_page',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_PREV_BUTTON: 'm_employee_activity_log_c_prev_button',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_NEXT_BUTTON: 'm_employee_activity_log_c_next_button',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TABLE_C_SUMMARY: 'm_employee_activity_log_table_c_sum',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TABLE_C_DATA: 'm_employee_activity_log_table_c_data',

    MODULE_EMPLOYEE_E_TIMESHEET_C_LOGO: 'm_employee_e_timesheet_c_logo',
    MODULE_EMPLOYEE_E_TIMESHEET_C_PROFILE: 'm_employee_e_timesheet_c_profile',
    MODULE_EMPLOYEE_E_TIMESHEET_C_DOWNLOAD_BTN: 'm_employee_e_timesheet_c_download_btn',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_TB_EMAIL: 'm_employee_e_timesheet_dl_btn_tb_email',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_EXCEL: 'm_employee_e_timesheet_dl_btn_c_rep_xlsx',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_CSV: 'm_employee_e_timesheet_dl_btn_c_rep_csv',
    MODULE_EMPLOYEE_E_TIMESHEET_TB_SEARCH: 'm_employee_e_timesheet_tb_search',
    MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_START: 'm_employee_e_timesheet_c_cal_start',
    MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_END: 'm_employee_e_timesheet_c_cal_end',
    MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED: 'm_employee_e_timesheet_dd_items_showed',
    MODULE_EMPLOYEE_E_TIMESHEET_TB_PAGE: 'm_employee_e_timesheet_tb_page',
    MODULE_EMPLOYEE_E_TIMESHEET_C_PREV_BUTTON: 'm_employee_e_timesheet_c_prev_button',
    MODULE_EMPLOYEE_E_TIMESHEET_C_NEXT_BUTTON: 'm_employee_e_timesheet_c_next_button',
    MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_SUMMARY: 'm_employee_e_timesheet_table_c_sum',
    MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_DATA: 'm_employee_e_timesheet_table_c_data',

    MODULE_EMPLOYEE_MANAGE_E: 'm_employee_manage_e',
    MODULE_EMPLOYEE_ACTIVITY_LOG: 'm_employee_activity_log',
    MODULE_EMPLOYEE_E_TIMESHEET: 'm_employee_e_timesheet',

    MODULE_SETTINGS_GENERAL: 'm_settings_general',
    MODULE_SETTINGS_SHIFT: 'm_settings_shift',
    MODULE_SETTINGS_RECEIPT: 'm_settings_receipt',
    MODULE_SETTINGS_ORDER: 'm_settings_order',
    MODULE_SETTINGS_PRINTER: 'm_settings_printer',
    MODULE_SETTINGS_PAYMENT: 'm_settings_payment',
    MODULE_SETTINGS_LOYALTY: 'm_settings_loyalty',

    MODULE_UPSELLING_LIST: 'm_upselling_list',
    MODULE_UPSELLING_CAMPAIGN: 'm_upselling_campaign',
    MODULE_UPSELLING_REPORT: 'm_upselling_report',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Inventory - Supplier
    MODULE_INVENTORY_SUPPLIER_C_LOGO: 'm_inv_supp_c_logo',
    MODULE_INVENTORY_SUPPLIER_C_PROFILE: 'm_inv_supp_c_profile',
    MODULE_INVENTORY_SUPPLIER_C_ITEM: 'm_inv_supp_c_item',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_C_IMAGE: 'm_inv_supp_info_ptl_c_img',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_C_DELETE: 'm_inv_supp_info_ptl_c_del',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_C_DELETE: 'm_inv_supp_info_pic_c_del',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_C_DELETE: 'm_inv_supp_info_pd_c_del',
    MODULE_INVENTORY_SUPPLIER_INFO_HISTORY_C_ITEM: 'm_inv_supp_info_history_c_item',
    MODULE_INVENTORY_SUPPLIER_INFO_C_SAVE_ALERT: 'm_inv_supp_info_c_save_a',
    MODULE_INVENTORY_SUPPLIER_INFO_C_UPDATE_ALERT: 'm_inv_supp_info_c_update_a',
    MODULE_INVENTORY_SUPPLIER_INFO_C_DELETE_ALERT: 'm_inv_supp_info_c_delete_a',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_inv_supp_dl_m_dl_as_c_excel_a',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_inv_supp_dl_m_dl_as_c_csv_a',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_CLOSE: 'm_inv_supp_batch_ul_m_c_close',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_UPLOAD_TEMPLATE: 'm_inv_supp_batch_ul_m_c_ul_template',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_EXPORT_TEMPLATE: 'm_inv_supp_batch_ul_m_c_exp_template',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_supp_dl_m_c_close',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_supp_dl_m_send_as_c_excel',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_supp_dl_m_send_as_c_csv',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_supp_dl_m_dl_as_c_excel',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_supp_dl_m_dl_as_c_csv',
    MODULE_INVENTORY_SUPPLIER_C_DOWNLOAD: 'm_inv_supp_c_dl',
    MODULE_INVENTORY_SUPPLIER_C_BATCH_UPLOAD: 'm_inv_supp_c_batch_ul',
    MODULE_INVENTORY_SUPPLIER_C_ADD_SUPPLY: 'm_inv_supp_c_add_supp',
    MODULE_INVENTORY_SUPPLIER_INFO_C_BACK: 'm_inv_supp_info_c_back',
    MODULE_INVENTORY_SUPPLIER_INFO_C_SAVE: 'm_inv_supp_info_c_save',
    MODULE_INVENTORY_SUPPLIER_INFO_C_DELETE: 'm_inv_supp_info_c_delete',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PERSON_IN_CHARGE: 'm_inv_supp_info_c_pic',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PRODUCTS_TO_LINKED: 'm_inv_supp_info_c_ptl',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PAYMENT_DETAILS: 'm_inv_supp_info_c_pd',
    MODULE_INVENTORY_SUPPLIER_INFO_C_HISTORY: 'm_inv_supp_info_c_history',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_C_ADD_PERSON_IN_CHARGE: 'm_inv_supp_info_pic_c_add_pic',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCTS_TO_LINKED_C_ADD_PRODUCT_SLOT: 'm_inv_supp_info_ptl_c_add_ps',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_C_ADD_PAYMENT_DETAIL: 'm_inv_supp_info_pd_c_add_pd',

    MODULE_INVENTORY_SUPPLIER_INFO_DD_TAX_RATE: 'm_inv_supp_info_dd_tax_rate',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_DD_ITEM_NAME: 'm_inv_supp_info_ptl_dd_item_name',

    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_SKU: 'm_inv_supp_info_ptl_tb_sku',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_PRICE: 'm_inv_supp_info_ptl_tb_price',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_COST: 'm_inv_supp_info_ptl_tb_cost',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_NAME: 'm_inv_supp_info_pic_tb_name',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_PHONE: 'm_inv_supp_info_pic_tb_phone',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_EMAIL: 'm_inv_supp_info_pic_tb_email',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_ACCOUNT_NAME: 'm_inv_supp_info_pd_tb_acc_name',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_ACCOUNT_NO: 'm_inv_supp_info_pd_tb_bank_acc_no',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_NAME: 'm_inv_supp_info_pd_tb_bank_name',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_BRANCH: 'm_inv_supp_info_pd_tb_bank_branch',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_supp_dl_modal_tb_email_address',
    MODULE_INVENTORY_SUPPLIER_TB_SEARCH: 'm_inv_supp_tb_search',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_NAME: 'm_inv_supp_info_tb_company_name',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NAME: 'm_inv_supp_info_tb_company_regis_name',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NO: 'm_inv_supp_info_tb_company_regis_no',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_ADDRESS: 'm_inv_supp_info_tb_company_address',

    // Inventory - Inventory Overview
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_LOGO: 'm_inv_inv_ov_c_logo',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_PROFILE: 'm_inv_inv_ov_c_profile',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_ITEM: 'm_inv_inv_ov_c_item',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_SUPPLIER: 'm_inv_inv_ov_c_supplier',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_ADD_INVENTORY_C_DELETE: 'm_inv_inv_ov_add_inv_c_delete',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT: 'm_inv_inv_ov_add_inv_c_add_a',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_inv_inv_ov_dl_modal_dl_as_c_excel_a',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_inv_inv_ov_dl_modal_dl_as_c_csv_a',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_inv_ov_dl_modal_c_close',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_inv_ov_dl_modal_send_as_c_excel',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_inv_ov_dl_modal_send_as_c_csv',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_inv_ov_dl_modal_dl_as_c_excel',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_inv_ov_dl_modal_dl_as_c_csv',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_DOWNLOAD: 'm_inv_inv_ov_c_dl',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_INVENTORY: 'm_inv_inv_ov_c_inv',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_SAVE: 'm_inv_inv_ov_c_save',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_BACK: 'm_inv_inv_ov_inv_c_back',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_ADD: 'm_inv_inv_ov_inv_c_add',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_ADD_PRODUCT_SLOT: 'm_inv_inv_ov_inv_c_add_prod_slot',

    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_DD_PRODUCT_NAME: 'm_inv_inv_ov_inv_dd_prod_name',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DD_STOCK_STATUS: 'm_inv_inv_ov_dd_stock_status',

    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_IDEAL_STOCK: 'm_inv_inv_ov_tb_ideal_stock',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_WARNING_STOCK: 'm_inv_inv_ov_tb_warning_stock',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_TB_INSERT_QUANTITY: 'm_inv_inv_ov_inv_tb_insert_quantity',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_inv_ov_dl_modal_tb_email_address',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_SEARCH: 'm_inv_inv_ov_tb_search',

    // Inventory - Purchase Order
    MODULE_INVENTORY_PURCHASE_ORDER_C_LOGO: 'm_inv_pchase_odr_c_logo',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PROFILE: 'm_inv_pchase_odr_c_profile',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_inv_pchase_odr_dl_modal_dl_as_c_exl_a',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_inv_pchase_odr_dl_modal_dl_as_c_csv_a',
    MODULE_INVENTORY_PURCHASE_ORDER_C_ITEM: 'm_inv_pchase_odr_c_item',
    MODULE_INVENTORY_PURCHASE_ORDER_ITEM_C_DUPLICATE: 'm_inv_pchase_odr_item_c_duplicate',
    MODULE_INVENTORY_PURCHASE_ORDER_ITEM_C_DOTS: 'm_inv_pchase_odr_item_c_dots',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE: 'm_inv_pchase_odr_info_items_odred_c_del',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT: 'm_inv_pchase_odr_info_c_save_e_a',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT: 'm_inv_pchase_odr_info_c_save_s_a',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT: 'm_inv_pchase_odr_info_c_save_e_qty_a',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT: 'm_inv_pchase_odr_info_c_update_s_a',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_pchase_odr_dl_modal_c_close',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_pchase_odr_dl_modal_send_as_c_exl',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_pchase_odr_dl_modal_send_as_c_csv',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_pchase_odr_dl_modal_dl_as_c_exl',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_pchase_odr_dl_modal_dl_as_c_csv',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_CLOSE: 'm_inv_pchase_odr_ul_modal_c_close',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_UPLOAD: 'm_inv_pchase_odr_ul_modal_c_ul',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_EXPORT: 'm_inv_pchase_odr_ul_modal_c_export',
    MODULE_INVENTORY_PURCHASE_ORDER_C_DOWNLOAD: 'm_inv_pchase_odr_c_dl',
    MODULE_INVENTORY_PURCHASE_ORDER_C_UPLOAD: 'm_inv_pchase_odr_c_ul',
    MODULE_INVENTORY_PURCHASE_ORDER_C_ADD_PO: 'm_inv_pchase_odr_c_add_po',
    MODULE_INVENTORY_PURCHASE_ORDER_C_START_DATE: 'm_inv_pchase_odr_c_start_date',
    MODULE_INVENTORY_PURCHASE_ORDER_C_END_DATE: 'm_inv_pchase_odr_c_end_date',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PO_TOADY: 'm_inv_pchase_odr_c_po_today',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PENDING_PO: 'm_inv_pchase_odr_c_pending_po',
    MODULE_INVENTORY_PURCHASE_ORDER_C_COMPLETED_PO: 'm_inv_pchase_odr_c_completed_po',
    MODULE_INVENTORY_PURCHASE_ORDER_C_CANCELLED_PO: 'm_inv_pchase_odr_c_cancelled_po',
    MODULE_INVENTORY_PURCHASE_ORDER_C_SUMMARY: 'm_inv_pchase_odr_c_summary',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_BACK: 'm_inv_pchase_odr_info_c_back',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_UPDATE: 'm_inv_pchase_odr_info_c_update',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE: 'm_inv_pchase_odr_info_c_save',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND: 'm_inv_pchase_odr_info_c_save_and_send',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON: 'm_inv_pchase_odr_info_c_po_id_icon',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON: 'm_inv_pchase_odr_info_c_est_arri_icon',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT: 'm_inv_pchase_odr_info_items_odred_c_add',

    MODULE_INVENTORY_PURCHASE_ORDER_INFO_DD_SUPPLIER: 'm_inv_pchase_odr_info_dd_supplier',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_DD_TARGET_STORE: 'm_inv_pchase_odr_info_dd_target_store',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME: 'm_inv_pchase_odr_info_items_odred_dd_pn',

    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY: 'm_inv_pchase_odr_info_items_odred_tb_oq',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY: 'm_inv_pchase_odr_info_items_odred_tb_rq',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_PRODUCT_PRICE: 'm_inv_pchase_odr_info_items_odred_tb_pp',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_COST_PRICE: 'm_inv_pchase_odr_info_items_odred_tb_cp',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_pchase_odr_dl_modal_tb_email_add',
    MODULE_INVENTORY_PURCHASE_ORDER_TB_SEARCH: 'm_inv_pchase_odr_tb_search',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID: 'm_inv_pchase_odr_info_tb_pchase_oid',

    // Inventory - Stock Transfer
    MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO: 'm_inv_stk_tran_c_logo',
    MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE: 'm_inv_stk_tran_c_profile',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: 'm_inv_stk_tran_dl_modal_dl_as_c_exl_s_a',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: 'm_inv_stk_tran_dl_modal_dl_as_c_csv_s_a',
    MODULE_INVENTORY_STOCK_TRANSFER_C_ITEM: 'm_inv_stk_tran_c_item',
    MODULE_INVENTORY_STOCK_TRANSFER_ITEM_C_EXPORT: 'm_inv_stk_tran_item_c_export',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE: 'm_inv_stk_tran_info_stk_to_tran_c_del',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT: 'm_inv_stk_tran_info_c_save_create_s_a',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: 'm_inv_stk_tran_info_c_save_update_s_a',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_stk_tran_dl_modal_c_close',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_stk_tran_dl_modal_send_as_c_exl',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_stk_tran_dl_modal_send_as_c_csv',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_stk_tran_dl_modal_dl_as_c_exl',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_stk_tran_dl_modal_dl_as_c_csv',
    MODULE_INVENTORY_STOCK_TRANSFER_C_DOWNLOAD: 'm_inv_stk_tran_c_dl',
    MODULE_INVENTORY_STOCK_TRANSFER_C_STOCK_TRANSFER: 'm_inv_stk_tran_c_stk_tran',
    MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE: 'm_inv_stk_tran_c_start_date',
    MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE: 'm_inv_stk_tran_c_end_date',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_BACK: 'm_inv_stk_tran_info_c_back',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED: 'm_inv_stk_tran_edit_c_mark_completed',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE: 'm_inv_stk_tran_info_c_save',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_ID_C_EDIT: 'm_inv_stk_tran_info_id_c_edit',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR: 'm_inv_stk_tran_info_shipped_date_c_cal',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: 'm_inv_stk_tran_info_stk_to_tran_c_aps',

    MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_SOURCE_STORE: 'm_inv_stk_tran_info_dd_src_store',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE: 'm_inv_stk_tran_info_dd_dest_store',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: 'm_inv_stk_tran_info_stk_to_tran_dd_pn',

    MODULE_INVENTORY_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_inv_stk_tran_new_stk_to_tran_tb_tq',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_inv_stk_tran_edit_stk_to_tran_tb_tq',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_stk_tran_dl_modal_tb_email_add',
    MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH: 'm_inv_stk_tran_tb_search',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_TB_ID: 'm_inv_stk_tran_edit_tb_id',

    // Inventory - Stock Take
    MODULE_INVENTORY_STOCK_TAKE_C_LOGO: 'm_inv_stk_take_c_logo',
    MODULE_INVENTORY_STOCK_TAKE_C_PROFILE: 'm_inv_stk_take_c_profile',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: 'm_inv_stk_take_dl_modal_dl_as_c_exl_s_a',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: 'm_inv_stk_take_dl_modal_dl_as_c_csv_s_a',
    MODULE_INVENTORY_STOCK_TAKE_C_ITEM: 'm_inv_stk_take_c_item',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_C_CLIPBOARD: 'm_inv_stk_take_info_item_c_clipboard',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE_CREATE_SUCCESS_ALERT: 'm_inv_stk_take_info_c_save_create_s_a',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: 'm_inv_stk_take_info_c_save_update_s_a',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_stk_take_dl_modal_c_close',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_stk_take_dl_modal_send_as_c_exl',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_stk_take_dl_modal_send_as_c_csv',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_stk_take_dl_modal_dl_as_c_exl',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_stk_take_dl_modal_dl_as_c_csv',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_CLOSE: 'm_inv_stk_take_info_item_clpb_r_m_c_cls',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_DONE: 'm_inv_stk_take_info_item_clpb_r_m_c_dne',
    MODULE_INVENTORY_STOCK_TAKE_C_DOWNLOAD: 'm_inv_stk_take_c_dl',
    MODULE_INVENTORY_STOCK_TAKE_C_STOCK_TAKE: 'm_inv_stk_take_c_stk_take',
    MODULE_INVENTORY_STOCK_TAKE_C_START_DATE: 'm_inv_stk_take_c_start_date',
    MODULE_INVENTORY_STOCK_TAKE_C_END_DATE: 'm_inv_stk_take_c_end_date',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_BACK: 'm_inv_stk_take_info_c_back',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE: 'm_inv_stk_take_info_c_save',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_COMPLETE: 'm_inv_stk_take_info_c_complete',
    MODULE_INVENTORY_STOCK_TAKE_EDIT_C_CANCEL: 'm_inv_stk_take_edit_c_cancel',
    MODULE_INVENTORY_STOCK_TAKE_EDIT_C_CANCEL_SUCCESS_ALERT: 'm_inv_stk_take_edit_c_cancel_s_a',
    MODULE_INVENTORY_STOCK_TAKE_NEW_ID_C_EDIT: 'm_inv_stk_take_new_id_c_edit',

    MODULE_INVENTORY_STOCK_TAKE_INFO_DD_TARGET_SUPPLIER: 'm_inv_stk_take_info_dd_target_supplier',

    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_TB_COUNTED: 'm_inv_stk_take_info_item_tb_counted',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_stk_take_dl_modal_tb_email_add',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_TB_REMARKS: 'm_inv_stk_take_info_item_clpb_r_m_tb_r',
    MODULE_INVENTORY_STOCK_TAKE_TB_SEARCH: 'm_inv_stk_take_tb_search',
    MODULE_INVENTORY_STOCK_TAKE_NEW_TB_ID: 'm_inv_stk_take_new_tb_id',
    MODULE_INVENTORY_STOCK_TAKE_INFO_TB_STOCK_TAKE_SUMMARY: 'm_inv_stk_take_info_tb_stk_take_summary',

    // Inventory - Stock Return
    MODULE_INVENTORY_STOCK_RETURN_C_LOGO: 'm_inv_stk_rtn_c_logo',
    MODULE_INVENTORY_STOCK_RETURN_C_PROFILE: 'm_inv_stk_rtn_c_profile',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: 'm_inv_stk_rtn_dl_modal_dl_as_c_exl_s_a',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: 'm_inv_stk_rtn_dl_modal_dl_as_c_csv_s_a',
    MODULE_INVENTORY_STOCK_RETURN_C_ITEM: 'm_inv_stk_rtn_c_item',
    MODULE_INVENTORY_STOCK_RETURN_INFO_STOCK_TO_TRANSFER_C_DELETE: 'm_inv_stk_rtn_info_stk_to_tran_c_del',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE_CREATE_SUCCESS_ALERT: 'm_inv_stk_rtn_info_c_save_create_s_a',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: 'm_inv_stk_rtn_info_c_save_update_s_a',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_C_CLOSE: 'm_inv_stk_rtn_dl_modal_c_close',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_inv_stk_rtn_dl_modal_send_as_c_exl',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_inv_stk_rtn_dl_modal_send_as_c_csv',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_inv_stk_rtn_dl_modal_dl_as_c_exl',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_inv_stk_rtn_dl_modal_dl_as_c_csv',
    MODULE_INVENTORY_STOCK_RETURN_C_DOWNLOAD: 'm_inv_stk_rtn_c_dl',
    MODULE_INVENTORY_STOCK_RETURN_C_STOCK_RETURN: 'm_inv_stk_rtn_c_stk_rtn',
    MODULE_INVENTORY_STOCK_RETURN_C_START_DATE: 'm_inv_stk_rtn_c_start_date',
    MODULE_INVENTORY_STOCK_RETURN_C_END_DATE: 'm_inv_stk_rtn_c_end_date',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_BACK: 'm_inv_stk_rtn_info_c_back',
    MODULE_INVENTORY_STOCK_RETURN_EDIT_C_MARK_COMPLETED: 'm_inv_stk_rtn_edit_c_mark_completed',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE: 'm_inv_stk_rtn_info_c_save',
    MODULE_INVENTORY_STOCK_RETURN_NEW_ID_C_EDIT: 'm_inv_stk_rtn_new_id_c_edit',
    MODULE_INVENTORY_STOCK_RETURN_INFO_SHIPPED_DATE_C_CALENDAR: 'm_inv_stk_rtn_info_shipped_date_c_cal',
    MODULE_INVENTORY_STOCK_RETURN_NEW_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: 'm_inv_stk_rtn_new_stk_to_tran_c_aps',

    MODULE_INVENTORY_STOCK_RETURN_INFO_DD_SOURCE_STORE: 'm_inv_stk_rtn_info_dd_src_store',
    MODULE_INVENTORY_STOCK_RETURN_INFO_DD_DESTINATION_SUPPLIER: 'm_inv_stk_rtn_info_dd_dest_supplier',
    MODULE_INVENTORY_STOCK_RETURN_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: 'm_inv_stk_rtn_info_stk_to_tran_dd_pn',

    MODULE_INVENTORY_STOCK_RETURN_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_inv_stk_rtn_new_stk_to_tran_dd_tq',
    MODULE_INVENTORY_STOCK_RETURN_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_inv_stk_rtn_edit_stk_to_tran_dd_tq',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_inv_stk_rtn_dl_modal_tb_email_add',
    MODULE_INVENTORY_STOCK_RETURN_TB_SEARCH: 'm_inv_stk_rtn_tb_search',
    MODULE_INVENTORY_STOCK_RETURN_NEW_TB_ID: 'm_inv_stk_rtn_new_tb_id',

    MODULE_RESERVATION_MANAGE_ADD_RESERVATION: 'm_reservation_manage_add_r',
    MODULE_RESERVATION_MANAGE_FILTER: 'm_reservation_manage_filter',

    MODULE_RESERVATION_ADD_GUEST: 'm_reservation_add_guest',
    MODULE_RESERVATION_ADD_PAX: 'm_reservation_add_pax',
    MOUDLE_RESERVATION_ADD_DATE: 'm_reservation_add_date',
    MOUDLE_RESERVATION_ADD_MONTH: 'm_reservation_add_month',
    MOUDLE_RESERVATION_ADD_YEAR: 'm_reservation_add_year',
    MOUDLE_RESERVATION_ADD_TIME: 'm_reservation_add_time',
    MOUDLE_RESERVATION_ADD_TABLE: 'm_reservation_add_table',
    MOUDLE_RESERVATION_ADD_REMARK: 'm_reservation_add_remark',
    MOUDLE_RESERVATION_ADD_DIETARY_RESTRICTIONS: 'm_reservation_add_dietary_restriction',
    MOUDLE_RESERVATION_ADD_SPECIAL_OCCASIONS: 'm_reservation_add_special_occasions',
    MOUDLE_RESERVATION_ADD_CONFIRM: 'm_reservation_add_confirm',
    MOUDLE_RESERVATION_ADD_CANCEL: 'm_reservation_add_cancel',

    MODULE_RESERVATION_OVERVIEW_TITLE_LEFT: 'm_reservation_overview_title_left',
    MODULE_RESERVATION_OVERVIEW_TITLE_RIGHT: 'm_reservation_overview_title_right',
    MODULE_RESERVATION_OVERVIEW_DETAIL_BOX: 'm_reservation_overview_detail_box',

    MODULE_RESERVATION_FOOTER_DATE: 'm_reservation_footer_date',
    MODULE_RESERVATION_FOOTER_MONTH: 'm_reservation_footer_month',
    MODULE_RESERVATION_FOOTER_YEAR: 'm_reservation_footer_year',
    MODULE_RESERVATION_FOOTER_LEFT: 'm_reservation_footer_left',
    MODULE_RESERVATION_FOOTER_RIGHT: 'm_reservation_footer_right',

    MODULE_RESERVATION_ANALYTICS_DATE_START: 'm_reservation_analytics_date_start',
    MODULE_RESERVATION_ANALYTICS_DATE_END: 'm_reservation_analytics_date_end',

    MODULE_RESERVATION_SETTINGS_ALLOW_RESERVATION: 'm_reservation_setting_allow_r',
    MODULE_RESERVATION_SETTINGS_MAX_PAX: 'm_reservation_setting_max_pax',
    MODULE_RESERVATION_SETTINGS_RESERVATION_LINK: 'm_reservation_setting_r_link',
    MODULE_RESERVATION_SETTINGS_SAVE: 'm_reservation_setting_save',

    MOUDULE_MENU_MENUDISPLAY_LIST: 'm_menu_menudisplay_dd',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Composite - Supplier
    MODULE_COMPOSITE_SUPPLIER_C_LOGO: 'm_com_supp_c_logo',
    MODULE_COMPOSITE_SUPPLIER_C_PROFILE: 'm_com_supp_c_profile',
    MODULE_COMPOSITE_SUPPLIER_C_ITEM: 'm_com_supp_c_item',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_IMAGE: 'm_com_supp_info_si_c_img',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_DELETE: 'm_com_supp_info_si_c_del',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_C_DELETE: 'm_com_supp_info_pic_c_del',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_C_DELETE: 'm_com_supp_info_pd_c_del',
    MODULE_COMPOSITE_SUPPLIER_INFO_HISTORY_C_ITEM: 'm_com_supp_info_history_c_item',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SAVE_ALERT: 'm_com_supp_info_c_save_a',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_UPDATE_ALERT: 'm_com_supp_info_c_update_a',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_DELETE_ALERT: 'm_com_supp_info_c_delete_a',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_com_supp_dl_m_dl_as_c_excel_a',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_com_supp_dl_m_dl_as_c_csv_a',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_C_CLOSE: 'm_com_supp_dl_m_c_close',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_com_supp_dl_m_send_as_c_excel',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_com_supp_dl_m_send_as_c_csv',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_com_supp_dl_m_dl_as_c_excel',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_com_supp_dl_m_dl_as_c_csv',
    MODULE_COMPOSITE_SUPPLIER_C_DOWNLOAD: 'm_com_supp_c_dl',
    MODULE_COMPOSITE_SUPPLIER_C_ADD_SUPPLY: 'm_com_supp_c_add_supp',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_BACK: 'm_com_supp_info_c_back',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SAVE: 'm_com_supp_info_c_save',
    MODULE_COMPOSITE_SUPPLIER_UPDATE_C_DELETE: 'm_com_supp_update_c_delete',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_PERSON_IN_CHARGE: 'm_com_supp_info_c_pic',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SUPPLY_ITEMS: 'm_com_supp_info_c_si',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_PAYMENT_DETAILS: 'm_com_supp_info_c_pd',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_HISTORY: 'm_com_supp_info_c_history',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_C_ADD_PERSON_IN_CHARGE: 'm_com_supp_info_pic_c_add_pic',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_ADD_SUPPLY_ITEMS: 'm_com_supp_info_si_c_add_si',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_C_ADD_PAYMENT_DETAIL: 'm_com_supp_info_pd_c_add_pd',

    MODULE_COMPOSITE_SUPPLIER_INFO_DD_TAX_RATE: 'm_com_supp_info_dd_tax_rate',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_DD_UNIT: 'm_com_supp_info_si_dd_unit',

    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_ITEM_NAME: 'm_com_supp_info_si_tb_item_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_SKU: 'm_com_supp_info_si_tb_sku',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_COST: 'm_com_supp_info_si_tb_cost',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_NAME: 'm_com_supp_info_pic_tb_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_PHONE: 'm_com_supp_info_pic_tb_phone',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_EMAIL: 'm_com_supp_info_pic_tb_email',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_ACCOUNT_NAME: 'm_com_supp_info_pd_tb_acc_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_ACCOUNT_NO: 'm_com_supp_info_pd_tb_bank_acc_no',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_NAME: 'm_com_supp_info_pd_tb_bank_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_BRANCH: 'm_com_supp_info_pd_tb_bank_branch',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_com_supp_dl_modal_tb_email_address',
    MODULE_COMPOSITE_SUPPLIER_TB_SEARCH: 'm_com_supp_tb_search',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_NAME: 'm_com_supp_info_tb_company_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NAME: 'm_com_supp_info_tb_company_regis_name',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NO: 'm_com_supp_info_tb_company_regis_no',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_ADDRESS: 'm_com_supp_info_tb_company_address',

    // Composite - Inventory Overview
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_LOGO: 'm_com_inv_ov_c_logo',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_PROFILE: 'm_com_inv_ov_c_profile',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_ITEM: 'm_com_inv_ov_c_item',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_SUPPLIER: 'm_com_inv_ov_c_supplier',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_DELETE: 'm_com_inv_ov_c_del',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_ARROW: 'm_com_inv_ov_c_arrow',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_DELETE: 'm_com_inv_ov_add_inv_c_delete',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT: 'm_com_inv_ov_add_inv_c_add_a',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_com_inv_ov_dl_modal_dl_as_c_excel_a',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_com_inv_ov_dl_modal_dl_as_c_csv_a',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_C_CLOSE: 'm_com_inv_ov_dl_modal_c_close',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_com_inv_ov_dl_modal_send_as_c_excel',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_com_inv_ov_dl_modal_send_as_c_csv',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_com_inv_ov_dl_modal_dl_as_c_excel',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_com_inv_ov_dl_modal_dl_as_c_csv',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_CLOSE: 'm_com_inv_ov_batch_ul_m_c_close',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_UPLOAD_TEMPLATE: 'm_com_inv_ov_batch_ul_m_c_ul_template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_EXPORT_TEMPLATE: 'm_com_inv_ov_batch_ul_m_c_exp_template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_CLOSE: 'm_com_inv_ov_del_m_c_close',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_DELETE: 'm_com_inv_ov_del_m_c_del',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_CANCEL: 'm_com_inv_ov_del_m_c_cancel',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_DOWNLOAD: 'm_com_inv_ov_c_dl',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_BATCH_UPLOAD: 'm_com_inv_ov_c_batch_ul',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_INVENTORY: 'm_com_inv_ov_c_inv',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_SAVE: 'm_com_inv_ov_c_save',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ITEM_C_BACK: 'm_com_inv_ov_item_c_back',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_BACK: 'm_com_inv_ov_inv_c_back',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_ADD: 'm_com_inv_ov_inv_c_add',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_ADD_PRODUCT_SLOT: 'm_com_inv_ov_inv_c_add_prod_slot',

    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_DD_PRODUCT_NAME: 'm_com_inv_ov_inv_dd_prod_name',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DD_STOCK_STATUS: 'm_com_inv_ov_dd_stock_status',

    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_IDEAL_STOCK: 'm_com_inv_ov_tb_ideal_stock',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_WARNING_STOCK: 'm_com_inv_ov_tb_warning_stock',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_TB_INSERT_QUANTITY: 'm_com_inv_ov_inv_tb_insert_quantity',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_com_inv_ov_dl_modal_tb_email_address',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_SEARCH: 'm_com_inv_ov_tb_search',

    // Composite - Purchase Order
    MODULE_COMPOSITE_PURCHASE_ORDER_C_LOGO: 'm_com_pchase_odr_c_logo',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PROFILE: 'm_com_pchase_odr_c_profile',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: 'm_com_pchase_odr_dl_modal_dl_as_c_exl_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: 'm_com_pchase_odr_dl_modal_dl_as_c_csv_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_ITEM: 'm_com_pchase_odr_c_item',
    MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DUPLICATE: 'm_com_pchase_odr_item_c_duplicate',
    MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DOTS: 'm_com_pchase_odr_item_c_dots',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE: 'm_com_pchase_odr_info_items_odred_c_del',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT: 'm_com_pchase_odr_info_c_save_e_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT: 'm_com_pchase_odr_info_c_save_s_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT: 'm_com_pchase_odr_info_c_save_e_qty_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT: 'm_com_pchase_odr_info_c_update_s_a',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE: 'm_com_pchase_odr_dl_modal_c_close',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_com_pchase_odr_dl_modal_send_as_c_exl',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_com_pchase_odr_dl_modal_send_as_c_csv',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_com_pchase_odr_dl_modal_dl_as_c_exl',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_com_pchase_odr_dl_modal_dl_as_c_csv',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_DOWNLOAD: 'm_com_pchase_odr_c_dl',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_ADD_PO: 'm_com_pchase_odr_c_add_po',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_START_DATE: 'm_com_pchase_odr_c_start_date',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_END_DATE: 'm_com_pchase_odr_c_end_date',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PO_TOADY: 'm_com_pchase_odr_c_po_today',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PENDING_PO: 'm_com_pchase_odr_c_pending_po',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_COMPLETED_PO: 'm_com_pchase_odr_c_completed_po',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_CANCELLED_PO: 'm_com_pchase_odr_c_cancelled_po',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_SUMMARY: 'm_com_pchase_odr_c_summary',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_BACK: 'm_com_pchase_odr_info_c_back',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE: 'm_com_pchase_odr_info_c_update',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE: 'm_com_pchase_odr_info_c_save',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND: 'm_com_pchase_odr_info_c_save_and_send',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON: 'm_com_pchase_odr_info_c_po_id_icon',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON: 'm_com_pchase_odr_info_c_est_arri_icon',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT: 'm_com_pchase_odr_info_items_odred_c_add',

    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER: 'm_com_pchase_odr_info_dd_supplier',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE: 'm_com_pchase_odr_info_dd_target_store',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME: 'm_com_pchase_odr_info_items_odred_dd_pn',

    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY: 'm_com_pchase_odr_info_items_odred_tb_oq',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY: 'm_com_pchase_odr_info_items_odred_tb_rq',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_com_pchase_odr_dl_modal_tb_email_add',
    MODULE_COMPOSITE_PURCHASE_ORDER_TB_SEARCH: 'm_com_pchase_odr_tb_search',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID: 'm_com_pchase_odr_info_tb_pchase_oid',

    // Composite - Stock Transfer
    MODULE_COMPOSITE_STOCK_TRANSFER_C_LOGO: 'm_com_stk_tran_c_logo',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_PROFILE: 'm_com_stk_tran_c_profile',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: 'm_com_stk_tran_dl_modal_dl_as_c_exl_s_a',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: 'm_com_stk_tran_dl_modal_dl_as_c_csv_s_a',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_ITEM: 'm_com_stk_tran_c_item',
    MODULE_COMPOSITE_STOCK_TRANSFER_ITEM_C_EXPORT: 'm_com_stk_tran_item_c_export',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE: 'm_com_stk_tran_info_stk_to_tran_c_del',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT: 'm_com_stk_tran_info_c_save_create_s_a',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: 'm_com_stk_tran_info_c_save_update_s_a',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE: 'm_com_stk_tran_dl_modal_c_close',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_com_stk_tran_dl_modal_send_as_c_exl',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_com_stk_tran_dl_modal_send_as_c_csv',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_com_stk_tran_dl_modal_dl_as_c_exl',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_com_stk_tran_dl_modal_dl_as_c_csv',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_DOWNLOAD: 'm_com_stk_tran_c_dl',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_STOCK_TRANSFER: 'm_com_stk_tran_c_stk_tran',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_START_DATE: 'm_com_stk_tran_c_start_date',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_END_DATE: 'm_com_stk_tran_c_end_date',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_BACK: 'm_com_stk_tran_info_c_back',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED: 'm_com_stk_tran_edit_c_mark_completed',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE: 'm_com_stk_tran_info_c_save',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_ID_C_EDIT: 'm_com_stk_tran_info_id_c_edit',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR: 'm_com_stk_tran_info_shipped_date_c_cal',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: 'm_com_stk_tran_info_stk_to_tran_c_aps',

    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_DD_SOURCE_STORE: 'm_com_stk_tran_info_dd_src_store',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE: 'm_com_stk_tran_info_dd_dest_store',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: 'm_com_stk_tran_info_stk_to_tran_dd_pn',

    MODULE_COMPOSITE_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_com_stk_tran_new_stk_to_tran_tb_tq',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: 'm_com_stk_tran_edit_stk_to_tran_tb_tq',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_com_stk_tran_dl_modal_tb_email_add',
    MODULE_COMPOSITE_STOCK_TRANSFER_TB_SEARCH: 'm_com_stk_tran_tb_search',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_TB_ID: 'm_com_stk_tran_edit_tb_id',

    // Composite - Stock Take
    MODULE_COMPOSITE_STOCK_TAKE_C_LOGO: 'm_com_stk_take_c_logo',
    MODULE_COMPOSITE_STOCK_TAKE_C_PROFILE: 'm_com_stk_take_c_profile',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: 'm_com_stk_take_dl_modal_dl_as_c_exl_s_a',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: 'm_com_stk_take_dl_modal_dl_as_c_csv_s_a',
    MODULE_COMPOSITE_STOCK_TAKE_C_ITEM: 'm_com_stk_take_c_item',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_C_CLIPBOARD: 'm_com_stk_take_info_item_c_clipboard',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE_CREATE_SUCCESS_ALERT: 'm_com_stk_take_info_c_save_create_s_a',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: 'm_com_stk_take_info_c_save_update_s_a',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_C_CLOSE: 'm_com_stk_take_dl_modal_c_close',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_com_stk_take_dl_modal_send_as_c_exl',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_com_stk_take_dl_modal_send_as_c_csv',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: 'm_com_stk_take_dl_modal_dl_as_c_exl',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: 'm_com_stk_take_dl_modal_dl_as_c_csv',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_CLOSE: 'm_com_stk_take_info_item_clpb_r_m_c_cls',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_DONE: 'm_com_stk_take_info_item_clpb_r_m_c_dne',
    MODULE_COMPOSITE_STOCK_TAKE_C_DOWNLOAD: 'm_com_stk_take_c_dl',
    MODULE_COMPOSITE_STOCK_TAKE_C_STOCK_TAKE: 'm_com_stk_take_c_stk_take',
    MODULE_COMPOSITE_STOCK_TAKE_C_START_DATE: 'm_com_stk_take_c_start_date',
    MODULE_COMPOSITE_STOCK_TAKE_C_END_DATE: 'm_com_stk_take_c_end_date',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_BACK: 'm_com_stk_take_info_c_back',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE: 'm_com_stk_take_info_c_save',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_COMPLETE: 'm_com_stk_take_info_c_complete',
    MODULE_COMPOSITE_STOCK_TAKE_EDIT_C_CANCEL: 'm_com_stk_take_edit_c_cancel',
    MODULE_COMPOSITE_STOCK_TAKE_EDIT_C_CANCEL_SUCCESS_ALERT: 'm_com_stk_take_edit_c_cancel_s_a',
    MODULE_COMPOSITE_STOCK_TAKE_NEW_ID_C_EDIT: 'm_com_stk_take_new_id_c_edit',

    MODULE_COMPOSITE_STOCK_TAKE_INFO_DD_TARGET_SUPPLIER: 'm_com_stk_take_info_dd_target_supplier',

    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_TB_COUNTED: 'm_com_stk_take_info_item_tb_counted',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_com_stk_take_dl_modal_tb_email_add',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_TB_REMARKS: 'm_com_stk_take_info_item_clpb_r_m_tb_r',
    MODULE_COMPOSITE_STOCK_TAKE_TB_SEARCH: 'm_com_stk_take_tb_search',
    MODULE_COMPOSITE_STOCK_TAKE_NEW_TB_ID: 'm_com_stk_take_new_tb_id',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_TB_STOCK_TAKE_SUMMARY: 'm_com_stk_take_info_tb_stk_take_summary',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Voucher - Voucher List
    MODULE_VOUCHER_VOUCHER_LIST_C_LOGO: 'm_vhr_vhr_list_c_logo',
    MODULE_VOUCHER_VOUCHER_LIST_C_PROFILE: 'm_vhr_vhr_list_c_profile',
    MODULE_VOUCHER_VOUCHER_LIST_C_ITEM: 'm_vhr_vhr_list_c_item',
    MODULE_VOUCHER_VOUCHER_LIST_ACTIVE_ITEM_C_TRASH: 'm_vhr_vhr_list_act_itm_c_trash',
    MODULE_VOUCHER_VOUCHER_LIST_ACTIVE_ITEM_TRASH_ALERT_C_YES: 'm_vhr_vhr_list_act_itm_trash_a_c_yes',
    MODULE_VOUCHER_VOUCHER_LIST_EXPIRED_ITEM_C_COPY: 'm_vhr_vhr_list_exp_itm_c_copy',
    MODULE_VOUCHER_VOUCHER_LIST_EXPIRED_ITEM_COPY_ALERT_C_YES: 'm_vhr_vhr_list_exp_itm_copy_a_c_yes',
    MODULE_VOUCHER_VOUCHER_LIST_C_VOUCHER: 'm_vhr_vhr_list_c_vhr',
    MODULE_VOUCHER_VOUCHER_LIST_C_ACTIVE: 'm_vhr_vhr_list_c_active',
    MODULE_VOUCHER_VOUCHER_LIST_C_EXPIRED: 'm_vhr_vhr_list_c_exp',
    MODULE_VOUCHER_VOUCHER_LIST_C_PREVIOUS_PAGE: 'm_vhr_vhr_list_c_pref_pg',
    MODULE_VOUCHER_VOUCHER_LIST_C_NEXT_PAGE: 'm_vhr_vhr_list_c_next_pg',

    MODULE_VOUCHER_VOUCHER_LIST_TB_SEARCH: 'm_vhr_vhr_list_tb_search',
    MODULE_VOUCHER_VOUCHER_LIST_TB_PAGE_NUMBER: 'm_vhr_vhr_list_tb_pg_no',

    // Voucher - Add Voucher
    MODULE_VOUCHER_ADD_VOUCHER_C_LOGO: 'm_vhr_add_vhr_c_logo',
    MODULE_VOUCHER_ADD_VOUCHER_C_PROFILE: 'm_vhr_add_vhr_c_profile',
    MODULE_VOUCHER_ADD_VOUCHER_SAVE_CREATE_ALERT_C_OK: 'm_vhr_add_vhr_save_create_a_c_ok',
    MODULE_VOUCHER_ADD_VOUCHER_SAVE_UPDATE_ALERT_C_OK: 'm_vhr_add_vhr_save_update_a_c_ok',
    MODULE_VOUCHER_ADD_VOUCHER_C_BACK: 'm_vhr_add_vhr_c_back',
    MODULE_VOUCHER_ADD_VOUCHER_C_SAVE: 'm_vhr_add_vhr_c_save',
    MODULE_VOUCHER_ADD_VOUCHER_C_UPLOAD_IMAGE: 'm_vhr_add_vhr_c_ul_img',
    MODULE_VOUCHER_ADD_VOUCHER_CLAIM_VOUCHER_LINK_C_COPY: 'm_vhr_add_vhr_clm_vhr_link_c_cpy',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_DATE_C_START_DATE: 'm_vhr_add_vhr_vhr_date_c_start_date',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_DATE_C_END_DATE: 'm_vhr_add_vhr_vhr_date_c_end_date',
    MODULE_VOUCHER_ADD_VOUCHER_APPLY_TO_CHANNEL_C_CHECKMARK: 'm_vhr_add_vhr_app_to_ch_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_APPLY_TO_OUTLETS_C_CHECKMARK: 'm_vhr_add_vhr_app_to_outlet_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_TIME_C_START_TIME: 'm_vhr_add_vhr_vhr_time_c_start_time',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_TIME_C_END_TIME: 'm_vhr_add_vhr_vhr_time_c_end_time',
    MODULE_VOUCHER_ADD_VOUCHER_EFFECTIVE_OPTIONS_C_CHECKMARK: 'm_vhr_add_vhr_eff_opt_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_TARGET_SEGMENTS_C_CHECKMARK: 'm_vhr_add_vhr_target_seg_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_IS_ONLINE_C_CHECKMARK: 'm_vhr_add_vhr_is_online_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_FREE_TO_CLAIM_C_CHECKMARK: 'm_vhr_add_vhr_free_to_clm_c_checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_PRODUCT_C_PLUS: 'm_vhr_add_vhr_criteria_product_c_plus',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_PRODUCT_C_MINUS: 'm_vhr_add_vhr_criteria_product_c_minus',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_FREE_TAKEAWAY_C_CHECKBOX: 'm_vhr_add_vhr_cri_free_ta_c_checkbox',

    MODULE_VOUCHER_ADD_VOUCHER_DD_VOUCHER_CODE_FORMAT: 'm_vhr_add_vhr_dd_vhr_code_format',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_TO_CHANNEL: 'm_vhr_add_vhr_dd_app_to_ch',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_DISCOUNT_PER: 'm_vhr_add_vhr_dd_app_disc_per',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_TO_OUTLETS: 'm_vhr_add_vhr_dd_app_to_outlet',
    MODULE_VOUCHER_ADD_VOUCHER_DD_EFFECTIVE_OPTIONS: 'm_vhr_add_vhr_dd_eff_opt',
    MODULE_VOUCHER_ADD_VOUCHER_DD_TARGET_SEGMENTS: 'm_vhr_add_vhr_dd_target_seg',
    MODULE_VOUCHER_ADD_VOUCHER_DD_VOUCHER_TYPE: 'm_vhr_add_vhr_dd_vhr_type',
    MODULE_VOUCHER_ADD_VOUCHER_DD_INFO_TO_COLLECTED: 'm_vhr_add_vhr_dd_info_to_col',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_BUY_DD_VARIATION: 'm_vhr_add_vhr_t_bdl_cri_buy_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_BUY_DD_PRODUCT: 'm_vhr_add_vhr_t_bdl_cri_buy_dd_pro',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_GET_DD_VARIATION: 'm_vhr_add_vhr_t_bdl_cri_get_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_GET_DD_PRODUCT: 'm_vhr_add_vhr_t_bdl_cri_get_dd_pro',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_VARIATION: 'm_vhr_add_vhr_t_ov_ex_p_cri_prod_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_PRODUCT: 'm_vhr_add_vhr_t_ov_ex_p_cri_prod_dd_pro',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_APPLY_TO_DD_VARIATION: 'm_vhr_add_vhr_t_fre_i_cri_app_to_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_APPLY_TO_DD_PRODUCT: 'm_vhr_add_vhr_t_fre_i_cri_app_to_dd_pro',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: 'm_vhr_add_vhr_t_tk_a_o_cri_app_to_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: 'm_vhr_add_vhr_t_tk_a_o_cri_app_to_dd_pr',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: 'm_vhr_add_vhr_t_p_a_o_cri_app_to_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: 'm_vhr_add_vhr_t_tk_p_o_cri_app_to_dd_pr',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_DD_VARIATION: 'm_vhr_add_vhr_t_delivery_cri_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_DD_PRODUCT: 'm_vhr_add_vhr_t_delivery_cri_dd_pro',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_DD_VARIATION: 'm_vhr_add_vhr_t_tkawy_cri_dd_va',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_DD_PRODUCT: 'm_vhr_add_vhr_t_tkawy_cri_dd_pro',

    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_DESCRIPTION: 'm_vhr_add_vhr_tb_vhr_desc',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_NAME: 'm_vhr_add_vhr_tb_vhr_name',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_GENERIC_CODE: 'm_vhr_add_vhr_tb_vhr_gen_code',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_QUANTITY: 'm_vhr_add_vhr_tb_vhr_quan',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_TERMS: 'm_vhr_add_vhr_tb_vhr_terms',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_MAX_CLAIM_PER_USER: 'm_vhr_add_vhr_tb_vhr_max_claim_per_usr',
    MODULE_VOUCHER_ADD_VOUCHER_TB_POINTS_REQUIRED: 'm_vhr_add_vhr_tb_points_req',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_BUY: 'm_vhr_add_vhr_t_bdl_cri_tb_buy',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_GET: 'm_vhr_add_vhr_t_bdl_cri_tb_get',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_ITEMS_FOR: 'm_vhr_add_vhr_t_bdl_cri_tb_items_for',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_vhr_add_vhr_t_bdl_cri_tb_min_spnd',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_vhr_add_vhr_t_ov_ex_p_cri_tb_min_spnd',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_TB_RM: 'm_vhr_add_vhr_t_ov_ex_p_cri_prod_tb_rm',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_vhr_add_vhr_t_fre_i_cri_tb_min_spend',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_TB_GET_QUANTITY: 'm_vhr_add_vhr_t_fre_i_cri_tb_get_quan',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_AMOUNT_OFF: 'm_vhr_add_vhr_t_tk_a_o_cri_tb_amnt_off',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_vhr_add_vhr_t_tk_a_o_cri_tb_spnd_amnt',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_QUANTITY: 'm_vhr_add_vhr_t_tk_a_o_cri_tb_min_quan',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_DISCOUNT: 'm_vhr_add_vhr_t_tk_p_o_cri_tb_disc',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_vhr_add_vhr_t_tk_p_o_cri_tb_min_spnd',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_QUANTITY: 'm_vhr_add_vhr_t_tk_p_o_cri_tb_min_quan',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_TB_DISCOUNT: 'm_vhr_add_vhr_t_delivery_cri_tb_disc',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_TB_ORDER_ABOVE: 'm_vhr_add_vhr_t_delivery_cri_tb_ord_abv',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_FREE_TAKEAWAY: 'm_vhr_add_vhr_t_tkawy_cri_tb_fre_tkawy',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_DISCOUNT: 'm_vhr_add_vhr_t_tkawy_cri_tb_disc',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_ORDER_ABOVE: 'm_vhr_add_vhr_t_tkawy_cri_tb_odr_abv',
    MODULE_VOUCHER_ADD_VOUCHER_NOTIFICAITON_TB_MESSAGE: 'm_vhr_add_vhr_noti_tb_msg',

    // Voucher - Voucher Report
    MODULE_VOUCHER_VOUCHER_REPORT_C_LOGO: 'm_vhr_vhr_rpt_c_logo',
    MODULE_VOUCHER_VOUCHER_REPORT_C_PROFILE: 'm_vhr_vhr_rpt_c_profile',
    MODULE_VOUCHER_VOUCHER_REPORT_C_ITEM: 'm_vhr_vhr_rpt_c_itm',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_ITEM: 'm_vhr_vhr_rpt_itm_dtls_c_itm',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_ITEM_C_PROFILE: 'm_vhr_vhr_rpt_itm_dtls_itm_c_prof',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_C_CLOSE: 'm_vhr_vhr_rpt_dl_m_c_close',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_vhr_vhr_rpt_dl_m_send_as_c_excel',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_vhr_vhr_rpt_dl_m_send_as_c_csv',
    MODULE_VOUCHER_VOUCHER_REPORT_C_DOWNLOAD: 'm_vhr_vhr_rpt_c_dl',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SUMMARY: 'm_vhr_vhr_rpt_itm_dtls_c_summary',
    MODULE_VOUCHER_VOUCHER_REPORT_C_START_DATE: 'm_vhr_vhr_rpt_c_start_date',
    MODULE_VOUCHER_VOUCHER_REPORT_C_END_DATE: 'm_vhr_vhr_rpt_c_end_date',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_TITLE: 'm_vhr_vhr_rpt_c_srt_title',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_TYPE: 'm_vhr_vhr_rpt_c_srt_type',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DURATION: 'm_vhr_vhr_rpt_c_srt_duration',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_CLAIMED: 'm_vhr_vhr_rpt_c_srt_claimed',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_USED: 'm_vhr_vhr_rpt_c_srt_used',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DISC_RM: 'm_vhr_vhr_rpt_c_srt_disc_rm',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DISC_PERCENT: 'm_vhr_vhr_rpt_c_srt_disc_percent',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_SALES: 'm_vhr_vhr_rpt_c_srt_sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DATETIME: 'm_vhr_vhr_rpt_itm_dtls_c_sort_date_time',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SALES: 'm_vhr_vhr_rpt_itm_dtls_c_sort_sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SALES_HELP: 'm_vhr_vhr_rpt_itm_dtls_c_sales_help',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DISC_RM: 'm_vhr_vhr_rpt_itm_dtls_c_sort_disc_rm',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DISC_PERCENT: 'm_vhr_vhr_rpt_itm_dtls_c_sort_disc_prct',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SERVICE_CHARGE: 'm_vhr_vhr_rpt_itm_dtls_c_sort_sc',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_TAX: 'm_vhr_vhr_rpt_itm_dtls_c_sort_tax',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SALES_RETURN: 'm_vhr_vhr_rpt_itm_dtls_c_sort_sales_rtn',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_NET_SALES: 'm_vhr_vhr_rpt_itm_dtls_c_sort_net_sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_NET_SALES_HELP: 'm_vhr_vhr_rpt_itm_dtls_c_net_sales_help',
    MODULE_VOUCHER_VOUCHER_REPORT_C_PREVIOUS_PAGE: 'm_vhr_vhr_rpt_c_pref_pg',
    MODULE_VOUCHER_VOUCHER_REPORT_C_NEXT_PAGE: 'm_vhr_vhr_rpt_c_next_pg',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_PREVIOUS_PAGE: 'm_vhr_vhr_rpt_itm_dtls_c_pref_pg',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_NEXT_PAGE: 'm_vhr_vhr_rpt_itm_dtls_c_next_pg',

    MODULE_VOUCHER_VOUCHER_REPORT_DD_ITEMS_SHOWED: 'm_vhr_vhr_rpt_dd_items_showed',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_DD_ITEMS_SHOWED: 'm_vhr_vhr_rpt_itm_dtls_dd_items_showed',

    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_vhr_vhr_rpt_dl_modal_tb_email_address',
    MODULE_VOUCHER_VOUCHER_REPORT_TB_PAGE_NUMBER: 'm_vhr_vhr_rpt_tb_pg_no',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_TB_PAGE_NUMBER: 'm_vhr_vhr_rpt_itm_dtls_tb_pg_no',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Promotion - Promotion List
    MODULE_PROMOTION_PROMOTION_LIST_C_LOGO: 'm_prm_prm_list_c_logo',
    MODULE_PROMOTION_PROMOTION_LIST_C_PROFILE: 'm_prm_prm_list_c_profile',
    MODULE_PROMOTION_PROMOTION_LIST_SWIPE_ITEM_C_BLAST: 'm_prm_prm_list_swipe_item_c_blast',
    MODULE_PROMOTION_PROMOTION_LIST_SWIPE_ITEM_C_DUPLICATE: 'm_prm_prm_list_swipe_item_c_duplicate',
    MODULE_PROMOTION_PROMOTION_LIST_C_ITEM: 'm_prm_prm_list_c_item',
    MODULE_PROMOTION_PROMOTION_LIST_C_PROMOTION: 'm_prm_prm_list_c_prm',
    MODULE_PROMOTION_PROMOTION_LIST_C_PREVIOUS_PAGE: 'm_prm_prm_list_c_pref_pg',
    MODULE_PROMOTION_PROMOTION_LIST_C_NEXT_PAGE: 'm_prm_prm_list_c_next_pg',

    MODULE_PROMOTION_PROMOTION_LIST_TB_SEARCH: 'm_prm_prm_list_tb_search',
    MODULE_PROMOTION_PROMOTION_LIST_TB_PAGE_NUMBER: 'm_prm_prm_list_tb_pg_no',

    // Promotion - Add Promotion
    MODULE_PROMOTION_ADD_PROMOTION_DELETE_ALERT_C_YES: 'm_prm_add_prm_del_a_c_yes',
    MODULE_PROMOTION_ADD_PROMOTION_DELETE_SUCCESS_ALERT_C_OK: 'm_prm_add_prm_del_s_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_BACK_ALERT_C_YES: 'm_prm_add_prm_back_a_c_yes',
    MODULE_PROMOTION_ADD_PROMOTION_C_LOGO: 'm_prm_add_prm_c_logo',
    MODULE_PROMOTION_ADD_PROMOTION_C_PROFILE: 'm_prm_add_prm_c_profile',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_ALERT_C_PUSH: 'm_prm_add_prm_pub_a_c_push',
    MODULE_PROMOTION_ADD_PROMOTION_SAVE_CREATE_ALERT_C_OK: 'm_prm_add_prm_save_create_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_CREATE_SCHEDULE_ALERT_C_OK: 'm_prm_add_prm_pub_create_sch_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_CREATE_PUSHED_ALERT_C_OK: 'm_prm_add_prm_pub_create_pushed_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_SAVE_UPDATE_ALERT_C_OK: 'm_prm_add_prm_save_update_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_UPDATE_SCHEDULE_ALERT_C_OK: 'm_prm_add_prm_pub_update_sch_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_UPDATE_PUSHED_ALERT_C_OK: 'm_prm_add_prm_pub_update_pushed_a_c_ok',
    MODULE_PROMOTION_ADD_PROMOTION_C_BACK: 'm_prm_add_prm_c_back',
    MODULE_PROMOTION_ADD_PROMOTION_C_SAVE: 'm_prm_add_prm_c_save',
    MODULE_PROMOTION_ADD_PROMOTION_C_DELETE: 'm_prm_add_prm_c_del',
    MODULE_PROMOTION_ADD_PROMOTION_C_PUBLISH: 'm_prm_add_prm_c_pub',
    MODULE_PROMOTION_ADD_PROMOTION_C_UPLOAD_IMAGE: 'm_prm_add_prm_c_ul_img',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_DATE_C_START_DATE: 'm_prm_add_prm_prm_date_c_start_date',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_DATE_C_END_DATE: 'm_prm_add_prm_prm_date_c_end_date',
    MODULE_PROMOTION_ADD_PROMOTION_APPLY_TO_CHANNEL_C_CHECKMARK: 'm_prm_add_prm_app_to_ch_c_checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_APPLY_TO_OUTLETS_C_CHECKMARK: 'm_prm_add_prm_app_to_outlet_c_checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_TIME_C_START_TIME: 'm_prm_add_prm_prm_time_c_start_time',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_TIME_C_END_TIME: 'm_prm_add_prm_prm_time_c_end_time',
    MODULE_PROMOTION_ADD_PROMOTION_EFFECTIVE_OPTIONS_C_CHECKMARK: 'm_prm_add_prm_eff_opt_c_checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_TARGET_SEGMENTS_C_CHECKMARK: 'm_prm_add_prm_target_seg_c_checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_USE_PROMO_CODE_C_CHECKMARK: 'm_prm_add_prm_use_prm_code_c_checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_C_PLUS: 'm_prm_add_prm_t_ov_ex_p_cri_prod_c_plus',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_C_MINUS: 'm_prm_add_prm_t_ov_ex_p_cri_prod_c_mins',

    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_TO_CHANNEL: 'm_prm_add_prm_dd_app_to_ch',
    MODULE_PROMOTION_ADD_PROMOTION_DD_PROMOTION_TYPE: 'm_prm_add_prm_dd_prm_type',
    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_DISCOUNT_PER: 'm_prm_add_prm_dd_app_disc_per',
    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_TO_OUTLETS: 'm_prm_add_prm_dd_app_to_outlet',
    MODULE_PROMOTION_ADD_PROMOTION_DD_EFFECTIVE_OPTIONS: 'm_prm_add_prm_dd_eff_opt',
    MODULE_PROMOTION_ADD_PROMOTION_DD_TARGET_SEGMENTS: 'm_prm_add_prm_dd_target_seg',
    MODULE_PROMOTION_ADD_PROMOTION_DD_INFO_TO_COLLECTED: 'm_prm_add_prm_dd_info_to_col',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_BUY_DD_VARIATION: 'm_prm_add_prm_t_bdl_cri_buy_dd_va',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_BUY_DD_PRODUCT: 'm_prm_add_prm_t_bdl_cri_buy_dd_pro',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_GET_DD_VARIATION: 'm_prm_add_prm_t_bdl_cri_get_dd_va',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_GET_DD_PRODUCT: 'm_prm_add_prm_t_bdl_cri_get_dd_pro',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_VARIATION: 'm_prm_add_prm_t_ov_ex_p_cri_prod_dd_va',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_PRODUCT: 'm_prm_add_prm_t_ov_ex_p_cri_prod_dd_pro',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: 'm_prm_add_prm_t_tk_a_o_cri_app_to_dd_va',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: 'm_prm_add_prm_t_tk_a_o_cri_app_to_dd_pr',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: 'm_prm_add_prm_t_p_a_o_cri_app_to_dd_va',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: 'm_prm_add_prm_t_tk_p_o_cri_app_to_dd_pr',

    MODULE_PROMOTION_ADD_PROMOTION_TB_CAMPAIGN_DESCRIPTION: 'm_prm_add_prm_tb_prm_desc',
    MODULE_PROMOTION_ADD_PROMOTION_TB_CAMPAIGN_NAME: 'm_prm_add_prm_tb_prm_name',
    MODULE_PROMOTION_ADD_PROMOTION_TB_CUSTOMER_LIMIT: 'm_prm_add_prm_tb_cust_limit',
    MODULE_PROMOTION_ADD_PROMOTION_TB_PROMO_CODE: 'm_prm_add_prm_tb_prm_code',
    MODULE_PROMOTION_ADD_PROMOTION_TB_USAGE_LIMIT: 'm_prm_add_prm_tb_usage_limit',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_BUY: 'm_prm_add_prm_t_bdl_cri_tb_buy',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_GET: 'm_prm_add_prm_t_bdl_cri_tb_get',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_ITEMS_FOR: 'm_prm_add_prm_t_bdl_cri_tb_items_for',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_prm_add_prm_t_bdl_cri_tb_min_spnd',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_TB_RM: 'm_prm_add_prm_t_ov_ex_p_cri_prod_tb_rm',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_AMOUNT_OFF: 'm_prm_add_prm_t_tk_a_o_cri_tb_amnt_off',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_prm_add_prm_t_tk_a_o_cri_tb_spnd_amnt',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_QUANTITY: 'm_prm_add_prm_t_tk_a_o_cri_tb_min_quan',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MAX_QUANTITY: 'm_prm_add_prm_t_tk_a_o_cri_tb_max_quan',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_DISCOUNT: 'm_prm_add_prm_t_tk_p_o_cri_tb_disc',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: 'm_prm_add_prm_t_tk_p_o_cri_tb_min_spnd',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_QUANTITY: 'm_prm_add_prm_t_tk_p_o_cri_tb_min_quan',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MAX_QUANTITY: 'm_prm_add_prm_t_tk_p_o_cri_tb_max_quan',

    // Promotion - Promotion Report
    MODULE_PROMOTION_PROMOTION_REPORT_C_LOGO: 'm_prm_prm_rpt_c_logo',
    MODULE_PROMOTION_PROMOTION_REPORT_C_PROFILE: 'm_prm_prm_rpt_c_profile',
    MODULE_PROMOTION_PROMOTION_REPORT_C_ITEM: 'm_prm_prm_rpt_c_itm',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_ITEM: 'm_prm_prm_rpt_itm_dtls_c_itm',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_ITEM_C_PROFILE: 'm_prm_prm_rpt_itm_dtls_itm_c_prof',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_C_CLOSE: 'm_prm_prm_rpt_dl_m_c_close',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: 'm_prm_prm_rpt_dl_m_send_as_c_excel',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_SEND_AS_C_CSV: 'm_prm_prm_rpt_dl_m_send_as_c_csv',
    MODULE_PROMOTION_PROMOTION_REPORT_C_DOWNLOAD: 'm_prm_prm_rpt_c_dl',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SUMMARY: 'm_prm_prm_rpt_itm_dtls_c_summary',
    MODULE_PROMOTION_PROMOTION_REPORT_C_START_DATE: 'm_prm_prm_rpt_c_start_date',
    MODULE_PROMOTION_PROMOTION_REPORT_C_END_DATE: 'm_prm_prm_rpt_c_end_date',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_TITLE: 'm_prm_prm_rpt_c_srt_title',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_TYPE: 'm_prm_prm_rpt_c_srt_type',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DURATION: 'm_prm_prm_rpt_c_srt_duration',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_ORDER: 'm_prm_prm_rpt_c_srt_odr',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DISC_RM: 'm_prm_prm_rpt_c_srt_disc_rm',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DISC_PERCENT: 'm_prm_prm_rpt_c_srt_disc_percent',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_SALES: 'm_prm_prm_rpt_c_srt_sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DATETIME: 'm_prm_prm_rpt_itm_dtls_c_sort_date_time',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SALES: 'm_prm_prm_rpt_itm_dtls_c_sort_sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SALES_HELP: 'm_prm_prm_rpt_itm_dtls_c_sales_help',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DISC_RM: 'm_prm_prm_rpt_itm_dtls_c_sort_disc_rm',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DISC_PERCENT: 'm_prm_prm_rpt_itm_dtls_c_sort_disc_prct',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SERVICE_CHARGE: 'm_prm_prm_rpt_itm_dtls_c_sort_sc',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_TAX: 'm_prm_prm_rpt_itm_dtls_c_sort_tax',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SALES_RETURN: 'm_prm_prm_rpt_itm_dtls_c_sort_sales_rtn',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_NET_SALES: 'm_prm_prm_rpt_itm_dtls_c_sort_net_sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_NET_SALES_HELP: 'm_prm_prm_rpt_itm_dtls_c_net_sales_help',
    MODULE_PROMOTION_PROMOTION_REPORT_C_PREVIOUS_PAGE: 'm_prm_prm_rpt_c_pref_pg',
    MODULE_PROMOTION_PROMOTION_REPORT_C_NEXT_PAGE: 'm_prm_prm_rpt_c_next_pg',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_PREVIOUS_PAGE: 'm_prm_prm_rpt_itm_dtls_c_pref_pg',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_NEXT_PAGE: 'm_prm_prm_rpt_itm_dtls_c_next_pg',

    MODULE_PROMOTION_PROMOTION_REPORT_DD_ITEMS_SHOWED: 'm_prm_prm_rpt_dd_items_showed',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_DD_ITEMS_SHOWED: 'm_prm_prm_rpt_itm_dtls_dd_items_showed',

    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: 'm_prm_prm_rpt_dl_modal_tb_email_address',
    MODULE_PROMOTION_PROMOTION_REPORT_TB_PAGE_NUMBER: 'm_prm_prm_rpt_tb_pg_no',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_TB_PAGE_NUMBER: 'm_prm_prm_rpt_itm_dtls_tb_pg_no',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Upselling - Upselling List
    MODULE_UPSELLING_UPSELLING_LIST_C_LOGO: 'm_ups_ups_list_c_logo',
    MODULE_UPSELLING_UPSELLING_LIST_C_PROFILE: 'm_ups_ups_list_c_profile',
    MODULE_UPSELLING_UPSELLING_LIST_SWIPE_ITEM_C_BLAST: 'm_ups_ups_list_swipe_item_c_blast',
    MODULE_UPSELLING_UPSELLING_LIST_SWIPE_ITEM_C_DUPLICATE: 'm_ups_ups_list_swipe_item_c_duplicate',
    MODULE_UPSELLING_UPSELLING_LIST_C_ITEM: 'm_ups_ups_list_c_item',
    MODULE_UPSELLING_UPSELLING_LIST_C_UPSELLING: 'm_ups_ups_list_c_ups',
    MODULE_UPSELLING_UPSELLING_LIST_C_PREVIOUS_PAGE: 'm_ups_ups_list_c_pref_pg',
    MODULE_UPSELLING_UPSELLING_LIST_C_NEXT_PAGE: 'm_ups_ups_list_c_next_pg',

    MODULE_UPSELLING_UPSELLING_LIST_TB_SEARCH: 'm_ups_ups_list_tb_search',
    MODULE_UPSELLING_UPSELLING_LIST_TB_PAGE_NUMBER: 'm_ups_ups_list_tb_pg_no',

    // Upselling - Upselling Campaign
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_LOGO: 'm_ups_ups_cam_c_logo',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_PROFILE: 'm_ups_ups_cam_c_profile',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_CREATE_ALERT_C_OK: 'm_ups_ups_cam_save_create_a_c_ok',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_UPDATE_ALERT_C_OK: 'm_ups_ups_cam_save_update_a_c_ok',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_C_MINUS: 'm_ups_ups_cam_prod_list_item_c_minus',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_BACK: 'm_ups_ups_cam_c_back',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_SAVE: 'm_ups_ups_cam_c_save',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_UPLOAD_IMAGE: 'm_ups_ups_cam_c_ul_img',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_START_DATE: 'm_ups_ups_cam_prm_date_c_start_date',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_END_DATE: 'm_ups_ups_cam_prm_date_c_end_date',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_START_TIME: 'm_ups_ups_cam_prm_time_c_start_time',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_END_TIME: 'm_ups_ups_cam_prm_time_c_end_time',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_EFFECTIVE_OPTIONS_C_CHECKMARK: 'm_ups_ups_cam_eff_opt_c_checkmark',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_C_PLUS: 'm_ups_ups_cam_prod_list_c_plus',

    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_NAME: 'm_ups_ups_cam_prod_list_itm_dd_name',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TAGS: 'm_ups_ups_cam_prod_list_itm_dd_tags',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TYPE: 'm_ups_ups_cam_prod_list_itm_dd_type',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_AVAILABILITY: 'm_ups_ups_cam_dd_avai',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_APPLY_TO_OUTLETS: 'm_ups_ups_cam_dd_app_to_outlet',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_EFFECTIVE_OPTIONS: 'm_ups_ups_cam_dd_eff_opt',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_TARGET_SEGMENTS: 'm_ups_ups_cam_dd_target_seg',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_SECTION_TO_SHOW: 'm_ups_ups_cam_dd_sec_to_show',

    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_TB_UPSELL_PRICE: 'm_ups_ups_cam_prod_list_itm_tb_upsell_p',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_DESCRIPTION: 'm_ups_ups_cam_tb_ups_desc',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_NAME: 'm_ups_ups_cam_tb_ups_name',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
};

export const ANALYTICS_PARSED = {
    MODULE_OPERATION: '[M] Operation',
    MODULE_RESERVATION: '[M] Reservation',
    MODULE_PRODUCT: '[M] Product',
    MODULE_INVENTORY: '[M] Inventory',
    MODULE_COMPOSITE: '[M] Composite',
    MODULE_DOCKET: '[M] Docket',
    MODULE_VOUCHER: '[M] Voucher',
    MODULE_PROMOTION: '[M] Promotion',
    MODULE_CRM: '[M] CRM',
    MODULE_LOYALTY: '[M] Loyalty',
    MODULE_TRANSACTION: '[M] Transaction',
    MODULE_REPORT: '[M] Report',
    MODULE_EMPLOYEE: '[M] Employee',
    MODULE_SETTINGS: '[M] Settings',
    MODULE_SUPPORT: '[M] Support',
    MODULE_UPSELLING: '[M] Upselling',

    MODULE_OPERATION_ORDERING: '[M] Operation > Ordering',
    MODULE_OPERATION_KITCHEN: '[M] Operation > Kitchen',
    MODULE_OPERATION_TABLE: '[M] Operation > Table',
    MODULE_OPERATION_DINEIN: '[M] Operation > Dine-In',
    MODULE_OPERATION_TAKEAWAY: '[M] Operation > Takeaway',
    MODULE_OPERATION_OTHER_DELIVERY: '[M] Operation > Other D.',
    MODULE_OPERATION_QUEUE: '[M] Operation > Queue',
    MODULE_OPERATION_HISTORY: '[M] Operation > History',
    MODULE_OPERATION_ORDER_DASHBOARD: '[M] Operation > Order Dashboard',

    MODULE_RESERVATION_MANAGE: '[M] Reservation > Manage',
    MODULE_RESERVATION_OVERVIEW: '[M] Reservation > Overview',
    MODULE_RESERVATION_DEPOSIT_SETTINGS: '[M] Reservation > Deposit Settings',
    MODULE_RESERVATION_INTERVAL_SETTINGS: '[M] Reservation > Interval Settings',
    MODULE_RESERVATION_ANALYTICS: '[M] Reservation > Analytics',

    MODULE_PRODUCT_P_CATEGORY: '[M] Product > P. Category',
    MODULE_PRODUCT_VARIANT_ADDON: '[M] Product > Variant Add-On',
    MODULE_PRODUCT_MENU_DISPLAY: '[M] Product > Menu Display',
    MODULE_PRODUCT_MANAGE_PREORDER: '[M] Product > Manage Preorder',

    MODULE_INVENTORY_SUPPLIER: '[M] Inventory > Supplier',
    MODULE_INVENTORY_I_OVERVIEW: '[M] Inventory > I. Overview',
    MODULE_INVENTORY_PURCHASE_ORDER: '[M] Inventory > Purchase Order',
    MODULE_INVENTORY_STOCK_TRANSFER: '[M] Inventory > Stock Transfer',
    MODULE_INVENTORY_STOCK_TAKE: '[M] Inventory > Stock Take',
    MODULE_INVENTORY_STOCK_RETURN: '[M] Inventory > Stock Return',

    MODULE_COMPOSITE_SUPPLIER: '[M] Composite > Supplier',
    MODULE_COMPOSITE_I_OVERVIEW: '[M] Composite > I. Overview',
    MODULE_COMPOSITE_PURCHASE_ORDER: '[M] Composite > Purchase Order',
    MODULE_COMPOSITE_STOCK_TRANSFER: '[M] Composite > Stock Transfer',
    MODULE_COMPOSITE_STOCK_TAKE: '[M] Composite > Stock Take',

    MODULE_DOCKET_ACTIVE_D: '[M] Docket > Active D.',
    MODULE_DOCKET_EXPIRED_D: '[M] Docket > Expired D.',
    MODULE_DOCKET_REDEEMED_D: '[M] Docket > Redeemed D.',
    MODULE_DOCKET_MANAGE_D: '[M] Docket > Manage D.',

    MODULE_VOUCHER_V_LIST: '[M] Voucher > V. List',
    MODULE_VOUCHER_ADD_V: '[M] Voucher > Add V.',
    MODULE_VOUCHER_V_REPORT: '[M] Voucher > V. Report',

    MODULE_PROMOTION_P_LIST: '[M] Promotion > P. List',
    MODULE_PROMOTION_ADD_P: '[M] Promotion > Add P.',
    MODULE_PROMOTION_P_REPORT: '[M] Promotion > P. Report',

    MODULE_CRM_CUSTOMER: '[M] CRM > Customer',
    MODULE_CRM_SEGMENT: '[M] CRM > Segment',
    MODULE_CRM_CUSTOMER_REVIEW: '[M] CRM > Customer Review',

    MODULE_LOYALTY_CAMPAIGN: '[M] Loyalty > Campaign',
    MODULE_LOYALTY_SIGN_UP_REWARD: '[M] Loyalty > Sign Up Reward',
    MODULE_LOYALTY_STAMPS: '[M] Loyalty > Stamps',
    MODULE_LOYALTY_PAY_EARN: '[M] Loyalty > Pay & Earn',
    MODULE_LOYALTY_REWARD_REDEMPTION: '[M] Loyalty > Reward & Redemption',
    MODULE_LOYALTY_CREDIT_TYPE: '[M] Loyalty > Credit Type',
    MODULE_LOYALTY_CREDIT_TYPE_REPORT: '[M] Loyalty > Credit Type Report',
    MODULE_LOYALTY_L_REPORT: '[M] Loyalty > L. Report',

    MODULE_TRANSACTION_AT_C_LOGO: '[M] Transaction > AT > C. Logo',
    MODULE_TRANSACTION_AT_C_PROFILE: '[M] Transaction > AT > C. Profile', // Profile icon leads to general settings after
    MODULE_TRANSACTION_AT_C_DL: '[M] Transaction > AT > C. DL',
    MODULE_TRANSACTION_AT_DL_TB_EMAIL: '[M] Transaction > AT > DL > TB. Email',
    MODULE_TRANSACTION_AT_DL_C_REP_EXCEL: '[M] Transaction > AT > DL > C. Rep Excel',
    MODULE_TRANSACTION_AT_DL_C_REP_CSV: '[M] Transaction > AT > DL > C. Rep CSV',
    MODULE_TRANSACTION_AT_C_CAL_START: '[M] Transaction > AT > C. Cal Start',
    MODULE_TRANSACTION_AT_C_CAL_END: '[M] Transaction > AT > C. Cal End',
    MODULE_TRANSACTION_AT_TABLE_C_DATE: '[M] Transaction > AT > Table > C. Date',
    MODULE_TRANSACTION_AT_TABLE_C_OUT: '[M] Transaction > AT > Table > C. Out',
    MODULE_TRANSACTION_AT_TABLE_C_EMP: '[M] Transaction > AT > Table > C. Emp',
    MODULE_TRANSACTION_AT_TABLE_C_CUS: '[M] Transaction > AT > Table > C. Cus',
    MODULE_TRANSACTION_AT_TABLE_C_TYPE: '[M] Transaction > AT > Table > C. Type',
    MODULE_TRANSACTION_AT_TABLE_C_COUR: '[M] Transaction > AT > Table > C. Cour',
    MODULE_TRANSACTION_AT_TABLE_C_SALES: '[M] Transaction > AT > Table > C. Sales',
    MODULE_TRANSACTION_AT_TABLE_S_C_ICON_QM: '[M] Transaction > AT > Table S > C. Icon QM',
    MODULE_TRANSACTION_AT_TABLE_C_DISC: '[M] Transaction > AT > Table > C. Disc',
    MODULE_TRANSACTION_AT_TABLE_C_TAX: '[M] Transaction > AT > Table > C. Tax',
    MODULE_TRANSACTION_AT_TABLE_C_SERV_CHAR: '[M] Transaction > AT > Table > C. Serv Char',
    MODULE_TRANSACTION_AT_TABLE_C_NET_SALE: '[M] Transaction > AT > Table > C. Net Sale',
    MODULE_TRANSACTION_AT_TABLE_NS_C_ICON_QM: '[M] Transaction > AT > Table NS > C. Icon QM',
    MODULE_TRANSACTION_AT_C_PREV_PAGE_BUTTON: '[M] Transaction > AT > C. Prev Page Button',
    MODULE_TRANSACTION_AT_C_NEXT_PAGE_BUTTON: '[M] Transaction > AT > C. Next Page Button',


    MODULE_REPORT_DASHBOARD: '[M] Report > Dashboard',
    MODULE_REPORT_ANALYSIS: '[M] Report > Analysis',
    MODULE_REPORT_AOV: '[M] Report > AOV',
    MODULE_REPORT_ORDER_COUNT: '[M] Report > Order\nCount',
    MODULE_REPORT_REVISIT_COUNT: '[M] Report > Revisit\nCount',
    MODULE_REPORT_OVERVIEW: '[M] Report > Overview',
    MODULE_REPORT_REVISIT: '[M] Report > Revisit',
    MODULE_REPORT_UPSELLING: '[M] Report > Upselling',
    MODULE_REPORT_PRODUCT: '[M] Report > Product',
    MODULE_REPORT_CATEGORY: '[M] Report > Category',
    MODULE_REPORT_VARIANT: '[M] Report > Variants',
    MODULE_REPORT_ADDON: '[M] Report > Add-Ons',
    MODULE_REPORT_CHANNEL: '[M] Report > Channel',
    MODULE_REPORT_PAYMENT: '[M] Report > Payment',
    MODULE_REPORT_SHIFT: '[M] Report > Shift',
    MODULE_REPORT_PAY_IN_OUT: '[M] Report > Pay In/Out',
    MODULE_REPORT_REFUND: '[M] Report > Refund',
    MODULE_REPORT_CATEGORY_PRODUCT: '[M] Report > Category\nProduct',

    // Report Dashboard
    MODULE_REPORT_DB_C_LOGO: '[M] Report > Dashboard > C. Logo',
    MODULE_REPORT_DB_C_GEN_SET: '[M] Report > Dashboard > C. Gen Set',
    MODULE_REPORT_DB_C_DL_REP_EMAIL_EXCEL: '[M] Report > Dashboard > Download > C. Email > Save As Excel',
    MODULE_REPORT_DB_C_DL_REP_EMAIL_CSV: '[M] Report > Dashboard > Download > C. Email > Save As CSV',
    MODULE_REPORT_DB_C_REP_SALES_OT: '[M] Report > Dashboard > C. Report Sales Overtime', // Also known as Today's Report
    MODULE_REPORT_DB_C_ALL_TRANS: '[M] Report > Dashboard > C. Today Transaction',
    MODULE_REPORT_DB_C_REP_SALES_PROD: '[M] Report > Dashboard > C. Rep Sales Prod',

    // report - AOV
    MODULE_REPORT_AOV_C_LOGO: '[M] Report > AOV > C. Logo ',
    MODULE_REPORT_AOV_C_PROFILE: '[M] Report > AOV > C. Profile',
    MODULE_REPORT_AOV_C_CHART_TODAY: '[M] Report > AOV > C. Chart Today',
    MODULE_REPORT_AOV_C_CHART_TW: '[M] Report > AOV > C. Chart This Week',
    MODULE_REPORT_AOV_C_CHART_MONTH: '[M] Report > AOV > C. Chart Month ',
    MODULE_REPORT_AOV_C_CHART_3_MONTHS: '[M] Report > AOV > C. Chart 3 Months',
    MODULE_REPORT_AOV_C_CHART_6_MONTHS: '[M] Report > AOV > C. Chart 6 Months',
    MODULE_REPORT_AOV_C_CHART_1_YEAR: '[M] Report > AOV > C. Chart 1 Year',
    MODULE_REPORT_AOV_C_CHART_YESTERDAY: '[M] Report > AOV > C. Chart Yesterday',
    MODULE_REPORT_AOV_DD_APP_ORDER: '[M] Report > AOV > DD App Order',
    MODULE_REPORT_AOV_C_CAL_START: '[M] Report > AOV > C. Cal Start',
    MODULE_REPORT_AOV_C_CAL_END: '[M] Report > AOV > C. Cal End',

    // Report Order Count
    MODULE_REPORT_OCR_C_LOGO: '[M] Report > OCR > C. Logo ',
    MODULE_REPORT_OCR_C_PROFILE: '[M] Report > OCR > C. Profile',
    MODULE_REPORT_OCR_C_CHART_TODAY: '[M] Report > OCR > C. Chart Today',
    MODULE_REPORT_OCR_C_CHART_TW: '[M] Report > OCR > C. Chart This Week',
    MODULE_REPORT_OCR_C_CHART_MONTH: '[M] Report > OCR > C. Chart This Month',
    MODULE_REPORT_OCR_C_CHART_3_MONTHS: '[M] Report > OCR > C. Chart 3 Months',
    MODULE_REPORT_OCR_C_CHART_6_MONTHS: '[M] Report > OCR > C. Chart 6 Months',
    MODULE_REPORT_OCR_C_CHART_1_YEAR: '[M] Report > OCR > C. Chart 1 Year',
    MODULE_REPORT_OCR_C_CHART_YESTERDAY: '[M] Report > OCR > C. Chart Yesterday',
    MODULE_REPORT_OCR_DD_APP_ORDER: '[M] Report > OCR > DD App Order',
    MODULE_REPORT_OCR_C_CAL_START: '[M] Report > OCR > C. Cal Start',
    MODULE_REPORT_OCR_C_CAL_END: '[M] Report > OCR > C. Cal Start',

    MODULE_REPORT_RC_C_LOGO: '[M] Report > RC > C. Logo ',
    MODULE_REPORT_RC_C_PROFILE: '[M] Report > RC > C. Profile',
    MODULE_REPORT_RC_C_CHART_TODAY: '[M] Report > RC > C. Chart Today',
    MODULE_REPORT_RC_C_CHART_TW: '[M] Report > RC > C. Chart This Week',
    MODULE_REPORT_RC_C_CHART_MONTH: '[M] Report > RC > C. Chart This Month',
    MODULE_REPORT_RC_C_CHART_3_MONTHS: '[M] Report > RC > C. Chart 3 Months',
    MODULE_REPORT_RC_C_CHART_6_MONTHS: '[M] Report > RC > C. Chart 6 Months',
    MODULE_REPORT_RC_C_CHART_1_YEAR: '[M] Report > RC > C. Chart 1 Year',
    MODULE_REPORT_RC_C_CHART_YESTERDAY: '[M] Report > RC > C. Chart Yesterday',
    MODULE_REPORT_RC_DD_APP_ORDER: '[M] Report > RC > DD App Order',

    MODULE_REPORT_RV_C_LOGO: '[M] Report > RV > C. Logo ',
    MODULE_REPORT_RV_C_PROFILE: '[M] Report > RV > C. Profile',
    MODULE_REPORT_RV_C_DL_BUTTON: '[M] Report > RV > C. DL Button',
    MODULE_REPORT_RV_DL_RP_C_CSV: '[M] Report > RV > DL > Report > C. CSV',
    MODULE_REPORT_RV_DL_RP_C_EXCEL: '[M] Report > RV > DL > Report > C. Excel',
    MODULE_REPORT_RV_TB_SEARCH: '[M] Report > RV > TB. Search',
    MODULE_REPORT_RV_C_CAL_START: '[M] Report > RV > C. Cal Start',
    MODULE_REPORT_RV_C_CAL_END: '[M] Report > RV > C. Cal End',
    MODULE_REPORT_RV_C_CHART_TODAY: '[M] Report > RV > C. Chart Today',
    MODULE_REPORT_RV_C_CHART_TW: '[M] Report > RV  > C. Chart This Week',
    MODULE_REPORT_RV_C_CHART_MONTH: '[M] Report > RV > C. Chart This Month',
    MODULE_REPORT_RV_C_CHART_3_MONTHS: '[M] Report > RV > C. Chart 3 Months',
    MODULE_REPORT_RV_C_CHART_6_MONTHS: '[M] Report > RV > C. Chart 6 Months',
    MODULE_REPORT_RV_C_CHART_1_YEAR: '[M] Report > RV > C. Chart 1 Year',
    MODULE_REPORT_RV_C_CHART_YESTERDAY: '[M] Report > RV > C. Chart Yesterday',
    MODULE_REPORT_RV_C_SORT_TABLE_DATE_TIME: '[M] Report > RV > C. Sort Table Date Time',
    MODULE_REPORT_RV_C_SORT_TABLE_ORDER: '[M] Report > RV > C. Sort Table Order',
    MODULE_REPORT_RV_C_SORT_TABLE_SALES: '[M] Report > RV > C. Sort Table Sales',
    MODULE_REPORT_RV_C_SORT_TABLE_DISC_RM: '[M] Report > RV > C. Sort Table Discount RM',
    MODULE_REPORT_RV_C_SORT_TABLE_DISC_PERCENTAGE: '[M] Report > RV > C. Sort Table Discount Percentage',
    MODULE_REPORT_RV_C_SORT_TABLE_TAX: '[M] Report > RV > C. Sort Table Tax',
    MODULE_REPORT_RV_C_SORT_TABLE_SERV_CHARGE: '[M] Report > RV > C. Sort Table Service Charge',
    MODULE_REPORT_RV_C_SORT_TABLE_NET_SALES: '[M] Report > RV > C. Sort Table Net Sales',
    MODULE_REPORT_RV_C_SORT_TABLE_GROSS_PROFIT: '[M] Report > RV > C. Sort Table Gross Profit',
    MODULE_REPORT_RV_C_SORT_TABLE_AVG_NET_SALES: '[M] Report > RV > C. Sort Table Avg Net Sales',
    MODULE_REPORT_RV_TABLE_SALES_C_ICON_QM: '[M] Report > RV > Table Sales > C. Icon QM', // QM = Question Mark Icon
    MODULE_REPORT_RV_TABLE_NET_SALES_C_ICON_QM: '[M] Report > RV > Table Net Sales > C. Icon QM',
    MODULE_REPORT_RV_DD_APP_ORDER: '[M] Report > RV > DD. App Order',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_TIME: '[M] Report > RV > Summary Low Table > C. Sort Table Time',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_SALES: '[M] Report > RV > Summary Low Table > C. Sort Table Sales',
    MODULE_REPORT_RV_SUM_LOW_TABLE_SALES_C_ICON_QM: '[M] Report > RV > Summary Low Table > Sales > C. Icon Question Mark',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_DISC_RM: '[M] Report > RV > Summary Low Table > C. Sort Table Disc RM',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_DISC_PERC: '[M] Report > RV > Summary Low Table > C. Sort Table Percentage',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_TAX: '[M] Report > RV > Summary Low Table > C. Sort Table Tax',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_SERV_CHARGE: '[M] Report > RV > Summary Low Table > C. Sort Table Service Charge',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_SORT_TABLE_NET_SALES: '[M] Report > RV > Summary Low Table > C. Sort Table Net Sales',
    MODULE_REPORT_RV_SUM_LOW_TABLE_NET_SALES_C_ICON_QM: '[M] Report > RV > Summary Low Table > Net Sales > C. Icon Question Mark',
    MODULE_REPORT_RV_DD_ITEM_SHOWED: '[M] Report > RV > DD. Item Showed',
    MODULE_REPORT_RV_TB_PAGE: '[M] Report > RV > TB. Page',
    MODULE_REPORT_RV_C_PREV_PAGE_BUTTON: '[M] Report > RV > C. Prev Page Button',
    MODULE_REPORT_RV_C_NEXT_PAGE_BUTTON: '[M] Report > RV > C. Next Page Button',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_P_P_B: '[M] Report > RV > Summary Low Table > C. Previous Page Button',
    MODULE_REPORT_RV_SUM_LOW_TABLE_C_N_P_B: '[M] Report > RV > Summary Low Table > C. Next Page Butto',

    // Report - Upselling
    MODULE_REPORT_UP_C_LOGO: '[M] Report > UP > C. Logo',
    MODULE_REPORT_UP_C_GEN_SET: '[M] Report > UP > C. Gen Set',
    MODULE_REPORT_UP_DD_APP_ORDER: '[M] Report > UP > DD. App Order',
    MODULE_REPORT_UP_TB_SEARCH: '[M] Report > UP > TB. Search',
    MODULE_REPORT_UP_DL_TB_EMAIL_ADDRESS: '[M] Report > UP > DL > TB. Email Address',
    MODULE_REPORT_UP_DL_RP_C_REP_EXCEL: '[M] Report > UP > DL > RP > C. Rep Excel',
    MODULE_REPORT_UP_DL_RP_C_REP_CSV: '[M] Report > UP > DL > RP > C. Rep CSV',
    MODULE_REPORT_UP_C_CHART_TODAY: '[M] Report > UP > C. Chart Today',
    MODULE_REPORT_UP_C_CHART_TW: '[M] Report > UP  > C. Chart This Week',
    MODULE_REPORT_UP_C_CHART_MONTH: '[M] Report > UP > C. Chart This Month',
    MODULE_REPORT_UP_C_CHART_3_MONTHS: '[M] Report > UP > C. Chart 3 Months',
    MODULE_REPORT_UP_C_CHART_6_MONTHS: '[M] Report > UP > C. Chart 6 Months',
    MODULE_REPORT_UP_C_CHART_1_YEAR: '[M] Report > UP > C. Chart 1 Year',
    MODULE_REPORT_UP_C_CHART_YESTERDAY: '[M] Report > UP > C. Chart Yesterday',
    MODULE_REPORT_UP_TB_PAGE: '[M] Report > UP > TB. Page',
    MODULE_REPORT_UP_C_DOWNLOAD: '[M] Report > UP > C. Download',
    MODULE_REPORT_UP_SUM_TABLE_C_PROD: '[M] Report > UP > Sum Table > C. Prod',
    MODULE_REPORT_UP_SUM_TABLE_C_CAT: '[M] Report > UP > Sum Table > C. Cat',
    MODULE_REPORT_UP_SUM_TABLE_C_SKU: '[M] Report > UP > Sum Table > C. SKU',
    MODULE_REPORT_UP_SUM_TABLE_C_ITEMS: '[M] Report > UP > Sum Table > C. Items',
    MODULE_REPORT_UP_SUM_TABLE_ITEMS_C_ICON_QM: '[M] Report > UP > Sum Table > Items > C. Icon QM',
    MODULE_REPORT_UP_SUM_TABLE_C_DISC_RM: '[M] Report > UP > Sum Table > C. Disc RM',
    MODULE_REPORT_UP_SUM_TABLE_C_DISC_PERC: '[M] Report > UP > Sum Table > C. Disc Perc',
    MODULE_REPORT_UP_SUM_TABLE_C_PAY_TYPE: '[M] Report > UP > Sum Table > C. Pay Type',
    MODULE_REPORT_UP_SUM_TABLE_C_NET_SALES: '[M] Report > UP > Sum Table > C. Net Sales',
    MODULE_REPORT_UP_SUM_TABLE_NS_C_ICON_QM: '[M] Report > UP > Sum Table > NS > C. Icon QM',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_O_ID: '[M] Report > UP > Sum Table > C. Prod',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_TD: '[M] Report > UP > Sum Table > Ind Sum > C. TD',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_S: '[M] Report > UP > Sum Table > Ind Sum > C. S',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_S_C_I_QM: '[M] Report > UP > Sum Table > Ind Sum  > S > C. I QM',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_DISC: '[M] Report > UP > Sum Table > Ind Sum > C. Disc',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_T: '[M] Report > UP > Sum Table > Ind Sum > C. T',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SC: '[M] Report > UP > Sum Table > Ind Sum > C. SC',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_SR: '[M] Report > UP > Sum Table > Ind Sum > C. SR',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_C_NS: '[M] Report > UP > Sum Table > Ind Sum > C. NS',
    MODULE_REPORT_UP_SUM_TABLE_IND_SUM_NS_C_I_QM: '[M] Report > UP > Sum Table > Ind Sum > NS > C. I QM',
    MODULE_REPORT_UP_DD_ITEMS_SHOWED: '[M] Report > UP > DD. Items Showed',
    MODULE_REPORT_UP_C_NEXT_PAGE: '[M] Report > UP > C. Next Page',
    MODULE_REPORT_UP_C_PREV_PAGE: '[M] Report > UP > C. Prev Page',
    MODULE_REPORT_UP_SUM_IND_C_NEXT_PAGE: '[M] Report > UP > Sum Ind > C. Next Page',
    MODULE_REPORT_UP_SUM_IND_C_PREV_PAGE: '[M] Report > UP > Sum Ind > C. Prev Page',
    MODULE_REPORT_UP_C_CAL_START: '[M] Report > UP > C. Cal Start',
    MODULE_REPORT_UP_C_CAL_END: '[M] Report > UP > C. Cal End',

    // Report - Productt
    MODULE_REPORT_PROD_C_LOGO: '[M] Report > Prod > C. Logo',
    MODULE_REPORT_PROD_C_PROFILE: '[M] Report > Prod > C. Profile',
    MODULE_REPORT_PROD_DD_APP_ORDER: '[M] Report > Prod > DD. App Order',
    MODULE_REPORT_PROD_TB_SEARCH: '[M] Report > Prod > TB. Search',
    MODULE_REPORT_PROD_C_PRINT: '[M] Report > Prod > C. Print',
    MODULE_REPORT_PROD_C_DL_BUTTON: '[M] Report > Prod > C. DL Button',
    MODULE_REPORT_PROD_DL_TB_EMAIL_ADDRESS: '[M] Report > Prod > DL > TB. Email Address',
    MODULE_REPORT_PROD_DL_RP_C_REP_EXCEL: '[M] Report > Prod > DL > RP > C. Rep Excel',
    MODULE_REPORT_PROD_DL_RP_C_REP_CSV: '[M] Report > Prod > DL > RP > C. Rep CSV',
    MODULE_REPORT_PROD_C_CHART_TODAY: '[M] Report > Prod > C. Chart Today',
    MODULE_REPORT_PROD_C_CHART_TW: '[M] Report > Prod  > C. Chart This Week',
    MODULE_REPORT_PROD_C_CHART_MONTH: '[M] Report > Prod > C. Chart This Month',
    MODULE_REPORT_PROD_C_CHART_3_MONTHS: '[M] Report > UP > C. Chart 3 Months',
    MODULE_REPORT_PROD_C_CHART_6_MONTHS: '[M] Report > UP > C. Chart 6 Months',
    MODULE_REPORT_PROD_C_CHART_1_YEAR: '[M] Report > Prod > C. Chart 1 Year',
    MODULE_REPORT_PROD_C_CHART_YESTERDAY: '[M] Report > Prod > C. Chart Yesterday',
    MODULE_REPORT_PROD_TB_PAGE: '[M] Report > Prod > TB. Page',
    MODULE_REPORT_PROD_C_CALENDAR_START: '[M] Report > Prod > C. Calendar Start',
    MODULE_REPORT_PROD_C_CALENDAR_END: '[M] Report > Prod > C. Calendar End',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_PROD_NAME: '[M] Report > Prod > Sum Low Table > C. Sort Table Product Name',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_CAT: '[M] Report > Prod > Sum Low Table > C. Sort Table Cat',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_SKU: '[M] Report > Prod > Sum Low Table > C. Sort Table SKU',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_ITEMS: '[M] Report > Prod > Sum Low Table > C. Sort Table Items',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_SALES: '[M] Report > Prod > Sum Low Table > C. Sort Table Sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_SALES_C_ICON_QM: '[M] Report > Prod > Sum Low Table > Sales > C. Icon QM',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_DISC_RM: '[M] Report > Prod > Sum Low Table > C. Sort Table Disc RM',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_DISC_PERC: '[M] Report > Prod > Sum Low Table > C. Sort Table Disc Perc',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_PAY_TYPE: '[M] Report > Prod > Sum Low Table > C. Sort Table Pay Type',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_C_SORT_TABLE_NET_SALES: '[M] Report > Prod > Sum Low Table > C. Sort Table Net Sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_NET_SALES_C_ICON_QM: '[M] Report > Prod > Sum Low Table > Net Sales > C. Icon QM',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_ORDER_ID: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Order ID',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TRAN_DATE: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Tran Date',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_SALES: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_SALES_C_ICON_QM: '[M] Report > Prod > Sum Low Table > Ind Sum > Sales > C. Icon QM',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_DISC_RM: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Disc RM',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_DISC_PERC: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Disc Perc',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_TAX: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Tax',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_SERV_CHAR: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Serv Char',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_SALES_RET: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Sales Ret',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_C_SORT_TABLE_NET_SALES: '[M] Report > Prod > Sum Low Table > Ind Sum > C. Sort Table Net Sales',
    MODULE_REPORT_PROD_SUM_LOW_TABLE_IND_SUM_NET_SALES_C_ICON_QM: '[M] Report > Prod > Sum Low Table > Ind Sum > Net Sales > C. Icon QM',
    MODULE_REPORT_PROD_C_PREV_PAGE_BUTTON: '[M] Report > Prod > C. Prev Page Button',
    MODULE_REPORT_PROD_C_NEXT_PAGE_BUTTON: '[M] Report > Prod > C. Next Page Button',
    MODULE_REPORT_PROD_IND_SUM_C_PREV_PAGE_BUTTON: '[M] Report > Prod > Ind Sum > C. Prev Page Button',
    MODULE_REPORT_PROD_IND_SUM_C_NEXT_PAGE_BUTTON: '[M] Report > Prod > ind Sum> C. Next Page Button',
    MODULE_REPORT_PROD_TB_PAGE: '[M] Report > Prod > TB. Page',

    // Report - Overview
    MODULE_REPORT_OV_C_LOGO: '[M] Report > OV > C. Logo',
    MODULE_REPORT_OV_C_PROFILE: '[M] Report > OV > C. Profile',
    MODULE_REPORT_OV_DD_APP_ORDER: '[M] Report > OV > DD. App Order',
    MODULE_REPORT_OV_TB_SEARCH: '[M] Report > OV > TB Search',
    MODULE_REPORT_OV_DL_C_REP_DL_EXCEL: '[M] Report > OV > DL > C. Rep DL Excel',
    MODULE_REPORT_OV_DL_C_REP_DL_CSV: '[M] Report > OV > DL > C. Rep DL CSV',
    MODULE_REPORT_OV_CHART_C_CALENDAR_START: '[M] Report > OV > Chart > C. Calendar Start',
    MODULE_REPORT_OV_CHART_C_CALENDAR_END: '[M] Report > OV > Chart > C. Calendar End',
    MODULE_REPORT_OV_C_CHART_TODAY: '[M] Report > OV > C. Chart Today',
    MODULE_REPORT_OV_C_CHART_TW: '[M] Report > OV > C. Chart TW',
    MODULE_REPORT_OV_C_CHART_MONTH: '[M] Report > OV > C. Chart Month',
    MODULE_REPORT_OV_C_CHART_3_MONTHS: '[M] Report > OV > C. Chart 3 Months',
    MODULE_REPORT_OV_C_CHART_6_MONTHS: '[M] Report > OV > C. Chart 6 Months',
    MODULE_REPORT_OV_C_CHART_1_YEAR: '[M] Report > OV > C. Chart 1 Year',
    MODULE_REPORT_OV_C_CHART_YESTERDAY: '[M] Report > OV > C. Chart Yesterday',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_ORDER: '[M] Report > OV > Sum Table > C. Table Order',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SALES: '[M] Report > OV > Sum Table > C. Table Sales',
    MODULE_REPORT_OV_SUM_TABLE_SALES_C_INFO_QM: '[M] Report > OV > Sum Table > Sales > C. Info QM',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_DISC_RM: '[M] Report > OV > Sum Table > C. Table Sales Disc RM',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_DISC_PERC: '[M] Report > OV > Sum Table > C. Table Disc Perc',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_TAX: '[M] Report > OV > Sum Table > C. Table Tax',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SC: '[M] Report > OV > Sum Table > C. Table SC',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_SR: '[M] Report > OV > Sum Table > C. Table SR',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_NET_SALES: '[M] Report > OV > Sum Table > C. Table Net Sales',
    MODULE_REPORT_OV_SUM_TABLE_NS_C_ICON_QM: '[M] Report > OV > Sum Table > NS > C. Icon QM',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_GP: '[M] Report > OV > Sum Table > C. Table GP',
    MODULE_REPORT_OV_SUM_TABLE_C_TABLE_AVG_NS: '[M] Report > OV > Sum Table > C. Table AVG NS',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_TIME: '[M] Report > OV > Sum Table > Ind Sum > C. Table Time',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SALES: '[M] Report > OV > Sum Table > Ind Sum > C. Table Sales',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_ICON_QM: '[M] Report > OV > Sum Table > Ind Sum > C. Icon QM',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_D_RM: '[M] Report > OV > Sum Table > Ind Sum > C. Table D RM',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_D_PERC: '[M] Report > OV > Sum Table > Ind Sum > C. Table D Perc',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_TAX: '[M] Report > OV > Sum Table > Ind Sum > C. Table Tax',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SC: '[M] Report > OV > Sum Table > Ind Sum > C. Table SC',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_SR: '[M] Report > OV > Sum Table > Ind Sum > C. Table SR',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_C_TABLE_NS: '[M] Report > OV > Sum Table > Ind Sum > C. Table NS',
    MODULE_REPORT_OV_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: '[M] Report > OV > Sum Table > Ind Sum > T NS > C. Icon QM',

    // Report - Payment
    MODULE_REPORT_PMR_C_LOGO: '[M] Report > PMR > C. Logo',
    MODULE_REPORT_PMR_C_PROFILE_ICON: '[M] Report > PMR > C. Profile Icon',
    MODULE_REPORT_PMR_DL_RP_C_CSV: '[M] Report > PMR > DL > RP > C. CSV',
    MODULE_REPORT_PMR_DL_RP_C_EXCEL: '[M] Report > PMR > DL > RP > C. Excel',
    MODULE_REPORT_PMR_C_DOWNLOAD: '[M] Report > PMR > C. Download',
    MODULE_REPORT_PMR_C_CALENDAR_START: '[M] Report > PMR > C. Calendar Start',
    MODULE_REPORT_PMR_C_CALENDAR_END: '[M] Report > PMR > C. Calendar End',
    MODULE_REPORT_PMR_TABLE_C_SORT_PAY_METHOD: '[M] Report > PMR > Table > C. Sort Pay Method',
    MODULE_REPORT_PMR_TABLE_C_SORT_ORDER: '[M] Report > PMR > Table >  C. Sort Order',
    MODULE_REPORT_PMR_TABLE_C_SORT_SALES: '[M] Report > PMR > Table > C. Sort Sales',
    MODULE_REPORT_PMR_TABLE_SALES_C_ICON_QM: '[M] Report > PMR > Table Sales > C. Icon QM',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_PM: '[M] Report > PMR > Table Sales > Ind Sum > C. Table PM',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_TRAN: '[M] Report > PMR > Table Sales > Ind Sum > C. Table Tran',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_O: '[M] Report > PMR > Table Sales > Ind Sum > C. Table O',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_S: '[M] Report > PMR > Table Sales > Ind Sum > C. Table S',
    MODULE_REPORT_PMR_TABLE_IND_SUM_TABLE_S_C_ICON_QM: '[M] Report > PMR > Table Sales > Ind Sum > Table S > C. Icon QM',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_DISC_RM: '[M] Report > PMR > Table Sales > Ind Sum > C. Disc RM',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_DISC_PERC: '[M] Report > PMR > Table Sales > Ind Sum > C. Disc Perc',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_TAX: '[M] Report > PMR > Table Sales > Ind Sum > C. Table Tax',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_SC: '[M] Report > PMR > Table Sales > Ind Sum > C. Table SC',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_SR: '[M] Report > PMR > Table Sales > Ind Sum > C. Table SR',
    MODULE_REPORT_PMR_TABLE_IND_SUM_C_TABLE_NS: '[M] Report > PMR > Table Sales > Ind Sum > C. Table NS',
    MODULE_REPORT_PMR_TABLE_IND_SUM_TABLE_NS_C_ICON_QM: '[M] Report > PMR > Table Sales > Ind Sum > Table NS > C. Icon QM',
    MODULE_REPORT_PMR_TABLE_C_PREV_PAGE_BUTTON: '[M] Report > PMR > Table Sales > C. Prev Page Button',
    MODULE_REPORT_PMR_TABLE_C_NEXT_PAGE_BUTTON: '[M] Report > PMR > Table Sales > C. Next Page Button',
    MODULE_REPORT_PMR_TABLE_IND_TABLE_C_P_P_B: '[M] Report > PMR > Table Sales > Ind Table > C. P P B',
    MODULE_REPORT_PMR_TABLE_IND_TABLE_C_P_N_B: '[M] Report > PMR > Table Sales > Ind Table > C. P N B',

    // Report - Pay In Out 
    MODULE_REPORT_PIO_C_LOGO: '[M] Report > PIO > C. Logo',
    MODULE_REPORT_PIO_C_PROFILE: '[M] Report > PIO > C. Profile',
    MODULE_REPORT_PIO_DL_REP_C_REP_EXCEL: '[M] Report > PIO > DL > REP > C. Rep Excel',
    MODULE_REPORT_PIO_DL_REP_C_REP_CSV: '[M] Report > PIO > DL > REP > C. Rep CSV',
    MODULE_REPORT_PIO_C_CALENDAR_START: '[M] Report > PIO > C. Calendar Start',
    MODULE_REPORT_PIO_C_CALENDAR_END: '[M] Report > PIO > C. Calendar End',
    MODULE_REPORT_PIO_TABLE_C_SORT_OP_TIME: '[M] Report > PIO > Table > C. Sort OP Time',
    MODULE_REPORT_PIO_TABLE_C_SORT_CASH: '[M] Report > PIO > Table > C. Sort Cash',
    MODULE_REPORT_PIO_TABLE_C_SORT_ON_OFF_TRAN: '[M] Report > PIO > Table > C. Sort On Off Tran',
    MODULE_REPORT_PIO_TABLE_C_SORT_TRAN: '[M] Report > PIO > Table > C. Sort Tran',
    MODULE_REPORT_PIO_TABLE_C_SORT_ON_OFF_SALES: '[M] Report > PIO > Table > C. Sort On Off Sales',
    MODULE_REPORT_PIO_TABLE_C_SORT_SALES: '[M] Report > PIO > Table > C. Sort Sales',
    MODULE_REPORT_PIO_TABLE_SALES_C_ICON_QM: '[M] Report > PIO > Table > Sales > C. Icon QM',
    MODULE_REPORT_PIO_TABLE_C_SORT_OP_CL_BAL: '[M] Report > PIO > Table > C. Sort OP Cl Bal',

    // Report - Refund 
    MODULE_REPORT_RF_C_LOGO: '[M] Report > RF > C. Logo',
    MODULE_REPORT_RF_C_PROFILE: '[M] Report > RF > C. Profile',
    MODULE_REPORT_RF_C_DOWNLOAD_BTN: '[M] Report > RF > C. Download Btn',
    MODULE_REPORT_RF_DL_BTN_TB_EMAIL: '[M] Report > RF > DL Btn > TB. Email',
    MODULE_REPORT_RF_DD_APP_ORDER: '[M] Report > RF > C. DD App Order',
    MODULE_REPORT_RF_DL_BTN_C_REP_EXCEL: '[M] Report > RF > DL Btn > C. Rep Excel',
    MODULE_REPORT_RF_DL_BTN_C_REP_CSV: '[M] Report > RF > DL Btn > C. Rep CSV',
    MODULE_REPORT_RF_DD_CHART_CAT: '[M] Report > RF > DD. Chart Cat',
    MODULE_REPORT_RF_C_CAL_START: '[M] Report > RF > C. Cal Start',
    MODULE_REPORT_RF_C_CAL_END: '[M] Report > RF > C. Cal End',
    MODULE_REPORT_RF_C_CHART_TODAY: '[M] Report > RF > C. Chart Today',
    MODULE_REPORT_RF_C_CHART_TW: '[M] Report > RF  > C. Chart This Week',
    MODULE_REPORT_RF_C_CHART_MONTH: '[M] Report > RF > C. Chart This Month',
    MODULE_REPORT_RF_C_CHART_3_MONTHS: '[M] Report > RF > C. Chart 3 Months',
    MODULE_REPORT_RF_C_CHART_6_MONTHS: '[M] Report > RF > C. Chart 6 Months',
    MODULE_REPORT_RF_C_CHART_1_YEAR: '[M] Report > RF > C. Chart 1 Year',
    MODULE_REPORT_RF_C_CHART_YESTERDAY: '[M] Report > RF > C. Chart Yesterday',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_PROD: '[M] Report > RF > Sum Table > C. Table Prod',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_CAT: '[M] Report > RF > Sum Table > C. Table Cat',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_SKU: '[M] Report > RF > Sum Table > C. Table SKU',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_ITEMS: '[M] Report > RF > Sum Table > C. Table Item',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_REF: '[M] Report > RF > Sum Table > C. Table Ref',
    MODULE_REPORT_RF_SUM_TABLE_SORT_REF_C_ICON_QM: '[M] Report > RF > Sum Table > Sort Ref > C. Icon QM',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_DISC_RM: '[M] Report > RF > Sum Table > C. Table RM',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_DISC_PERC: '[M] Report > RF > Sum Table > C. Table Disc Perc',
    MODULE_REPORT_RF_SUM_TABLE_C_TABLE_PM: '[M] Report > RF > Sum Table > C. Table PM',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_ID: '[M] Report > RF > Sum Table > Ind Sum > C. T ID',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_TD: '[M] Report > RF > Sum Table > Ind Sum > C. T TD',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_R: '[M] Report > RF > Sum Table > Ind Sum > C. T R',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_D_RM: '[M] Report > RF > Sum Table > Ind Sum > C. T D RM',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_D_P: '[M] Report > RF > Sum Table > Ind Sum > C. T D P',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_T: '[M] Report > RF > Sum Table > Ind Sum > C. T T',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_SC: '[M] Report > RF > Sum Table > Ind Sum > C. T SC',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_SR: '[M] Report > RF > Sum Table > Ind Sum > C. T SR',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_C_T_NS: '[M] Report > RF > Sum Table > Ind Sum > C. T NS',
    MODULE_REPORT_RF_SUM_TABLE_IND_SUM_TABLE_NS_C_I_QM: '[M] Report > RF > Sum Table > Ind Sum > NS > C I QM',
    MODULE_REPORT_RF_SUM_TABLE_C_PREV_PAGE_BUTTON: '[M] Report > RF > Sum Table > C. Prev Page Button',
    MODULE_REPORT_RF_SUM_TABLE_C_NEXT_PAGE_BUTTON: '[M] Report > RF > Sum Table > C. Next Page Button',
    MODULE_REPORT_RF_SUM_TABLE_IND_TABLE_C_P_P_B: '[M] Report > RF > Sum Table > Ind Table > C. P P B',
    MODULE_REPORT_RF_SUM_TABLE_IND_TABLE_C_P_N_B: '[M] Report > RF > Sum Table > Ind Table > C. P N B',

    // Report - Sales Transaction (Channel)
    MODULE_REPORT_CHA_C_LOGO: '[M] Report > CHA > C. Logo',
    MODULE_REPORT_CHA_C_PROFILE: '[M] Report > CHA > C. Profile',
    MODULE_REPORT_CHA_DD_APP_ORDER: '[M] Report > CHA > DD. App Order',
    MODULE_REPORT_CHA_C_DOWNLOAD_BTN: '[M] Report > CHA > C. Download Btn',
    MODULE_REPORT_CHA_DL_BTN_TB_EMAIL: '[M] Report > CHA > DL Btn > TB. Email',
    MODULE_REPORT_CHA_DL_BTN_C_REP_EXCEL: '[M] Report > CHA > DL Btn > C. Rep Excel',
    MODULE_REPORT_CHA_DL_BTN_C_REP_CSV: '[M] Report > CHA > DL Btn > C. Rep CSV',
    MODULE_REPORT_CHA_TB_SEARCH: '[M] Report > CHA > TB. Search',
    MODULE_REPORT_CHA_C_CAL_START: '[M] Report > CHA > C. Cal Start',
    MODULE_REPORT_CHA_C_CAL_END: '[M] Report > CHA > C. Cal End',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TRAN_CA: '[M] Report > CHA > Sum Table > C. Table Tran CA',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_OR: '[M] Report > CHA > Sum Table > C. Table OR',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SALES: '[M] Report > CHA > Sum Table > C. Table Sales',
    MODULE_REPORT_CHA_SUM_TABLE_SALES_C_ICON_QM: '[M] Report > CHA > Sum Table > Table Sales > C. Icon QM',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_PERC: '[M] Report > CHA > Sum Table > C. Table Disc Perc',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_RM: '[M] Report > CHA > Sum Table > C. Table Disc RM',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TAX: '[M] Report > CHA > Sum Table > C. Table Tax',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_CHAR: '[M] Report > CHA > Sum Table > C. Table Serv Char',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_RET: '[M] Report > CHA > Sum Table > C. Table Serv Net',
    MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_NET_SALES: '[M] Report > CHA > Sum Table > C. Table Net Sales',
    MODULE_REPORT_CHA_SUM_TABLE_NS_C_ICON_QM: '[M] Report > CHA > Sum Table > Table NS > C. Icon QM',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_CA: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Tran CA',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_DT: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Tran DT',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SALES: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Sales',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: '[M] Report > CHA > Sum Table > Ind Sum > T S > C. Icon QM',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Disc Perc',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Disc RM',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TAX: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Tax',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_CHAR: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Serv Char',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_RET: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Serv Ret',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_NET_SALES: '[M] Report > CHA > Sum Table > Ind Sum > C. Table Serv Sales',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: '[M] Report > CHA > Sum Table > Ind Sum > T NS > C. Table Icon QM',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_PREV_PAGE_BUTTON: '[M] Report > CHA > Sum Table > Ind Sum > C. Prev Page Button',
    MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BUTTON: '[M] Report > CHA > Sum Table > Ind Sum > C. Next Page Button',
    MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_P_B: '[M] Report > CHA > Sum Table > Ind Table > C. P P B',
    MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_N_B: '[M] Report > CHA > Sum Table > Ind Table > C. P N B',

    // Report - Add-On Sales Report
    MODULE_REPORT_ADN_C_LOGO: '[M] Report > ADN > C. Logo',
    MODULE_REPORT_ADN_C_PROFILE: '[M] Report > ADN > C. Profile',
    MODULE_REPORT_ADN_DD_APP_TYPE: '[M] Report > ADN > DD. App Type',
    MODULE_REPORT_ADN_C_DOWNLOAD_BTN: '[M] Report > ADN > C. Download Btn',
    MODULE_REPORT_ADN_DL_BTN_TB_EMAIL: '[M] Report > ADN > Dl Btn > TB. Email',
    MODULE_REPORT_ADN_DL_BTN_C_REP_EXCEL: '[M] Report > ADN > Dl Btn > C. Rep Excel',
    MODULE_REPORT_ADN_DL_BTN_C_REP_CSV: '[M] Report > ADN > Dl Btn > C. Rep CSV',
    MODULE_REPORT_ADN_C_CAL_START: '[M] Report > ADN > C. Cal Start',
    MODULE_REPORT_ADN_C_CAL_END: '[M] Report > ADN > C. Cal End',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_AO_GN: '[M] Report > ADN > Sum Table > C Table AO GN', // Add-Ons Group Name
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_AO_O: '[M] Report > ADN > Sum Table > C Table AO O',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_ITEM_S: '[M] Report > ADN > Sum Table > C Table Item S',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SALES: '[M] Report > ADN > Sum Table > C Table Sales',
    MODULE_REPORT_ADN_SUM_TABLE_T_S_C_ICON_QM: '[M] Report > ADN > Sum Table > T S > C Icon QM',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_DISC_RM: '[M] Report > ADN > Sum Table > C Table Disc RM',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_DISC_PERC: '[M] Report > ADN > Sum Table > C Table Disc Perc',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_TAX: '[M] Report > ADN > Sum Table > C Table Tax',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SERV_CHAR: '[M] Report > ADN > Sum Table > C Table Serv Char',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_SALE_RET: '[M] Report > ADN > Sum Table > C Table Sale Ret',
    MODULE_REPORT_ADN_SUM_TABLE_C_TABLE_NET_SALES: '[M] Report > ADN > Sum Table > C Table Net Sales',
    MODULE_REPORT_ADN_SUM_TABLE_T_NS_C_ICON_QM: '[M] Report > ADN > Sum Table > T NS > C. Icon QM',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_O_ID: '[M] Report > ADN > Sum Table > Ind Sum > C Table O ID',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_TD: '[M] Report > ADN > Sum Table > Ind Sum > C Table TD',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_S: '[M] Report > ADN > Sum Table > Ind Sum > C Table S', // Sales
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: '[M] Report > ADN > Sum Table > Ind Sum > TS C > C. Icon QM',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: '[M] Report > ADN > Sum Table > Ind Sum > C Table Disc RM',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: '[M] Report > ADN > Sum Table > Ind Sum > C Table Disc Perc',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_TAX: '[M] Report > ADN > Sum Table > Ind Sum > C Table Tax',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_SC: '[M] Report > ADN > Sum Table > Ind Sum > C Table SC',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_SR: '[M] Report > ADN > Sum Table > Ind Sum > C Table SR',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_TABLE_NS: '[M] Report > ADN > Sum Table > Ind Sum > C Table NS',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_T_S_NS_ICON_QM: '[M] Report > ADN > Sum Table > Ind Sum > TS NS > C Icon QM',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_PREV_PAGE_BTN: '[M] Report > ADN > Sum Table > Ind Sum > C. Prev Page Btn',
    MODULE_REPORT_ADN_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BTN: '[M] Report > ADN > Sum Table > Ind Sum > C. Next Page Btn',
    MODULE_REPORT_ADN_SUM_TABLE_IND_TABLE_C_P_P_B: '[M] Report > ADN > Sum Table > Ind Table > C. P P B',
    MODULE_REPORT_ADN_SUM_TABLE_IND_TABLE_C_N_P_B: '[M] Report > ADN > Sum Table > Ind Table > C. N P B',

    // Report - Category
    MODULE_REPORT_CAT_C_LOGO: '[M] Report > CAT > C. Logo',
    MODULE_REPORT_CAT_C_PROFILE: '[M] Report > CAT > C. Profile',
    MODULE_REPORT_CAT_DD_APP_TYPE: '[M] Report > CAT > DD. App Type',
    MODULE_REPORT_CAT_C_DOWNLOAD_BTN: '[M] Report > CAT > C. Download Btn',
    MODULE_REPORT_CAT_DL_BTN_TB_EMAIL: '[M] Report > CAT > Dl Btn > TB. Email',
    MODULE_REPORT_CAT_DL_BTN_C_REP_EXCEL: '[M] Report > CAT > Dl Btn > C. Rep Excel',
    MODULE_REPORT_CAT_DL_BTN_C_REP_CSV: '[M] Report > CAT > Dl Btn > C. Rep CSV',
    MODULE_REPORT_CAT_C_CAL_START: '[M] Report > CAT > C. Cal Start',
    MODULE_REPORT_CAT_C_CAL_END: '[M] Report > CAT > C. Cal End',
    MODULE_REPORT_CAT_TB_SEARCH: '[M] Report > CAT > TB. Search',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_PROD_CAT: '[M] Report > CAT > Sum Table > C. Table Prod Cat',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_ITEM_SOLD: '[M] Report > CAT > Sum Table > C. Table Item Sold',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALES: '[M] Report > CAT > Sum Table > C. Table Sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_S_C_ICON_QM: '[M] Report > CAT > Sum Table > T S > C. Icon QM',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_RM: '[M] Report > CAT > Sum Table > C. Table Disc RM',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_DISC_PERC: '[M] Report > CAT > Sum Table > C. Table Disc Perc',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_TAX: '[M] Report > CAT > Sum Table > C. Table Tax',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SERV_CHAR: '[M] Report > CAT > Sum Table > C. Table Serv Char',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_SALE_RET: '[M] Report > CAT > Sum Table > C. Table Serv Ret',
    MODULE_REPORT_CAT_SUM_TABLE_C_TABLE_NET_SALES: '[M] Report > CAT > Sum Table > C. Table Serv Sales',
    MODULE_REPORT_CAT_SUM_TABLE_T_NS_C_ICON_QM: '[M] Report > CAT > Sum Table > T NS > C. Icon QM',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_O_ID: '[M] Report > CAT > Sum Table > Ind Sum > C. Table O ID',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TD: '[M] Report > CAT > Sum Table > Ind Sum > C. Table TD',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_S: '[M] Report > CAT > Sum Table > Ind Sum > C. Table S', // Sales
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_S_C_ICON_QM: '[M] Report > CAT > Sum Table > Ind Sum > T S > C. Icon QM',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM: '[M] Report > CAT > Sum Table > Ind Sum > C. Table Disc RM',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC: '[M] Report > CAT > Sum Table > Ind Sum > C. Table Disc Perc',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_TAX: '[M] Report > CAT > Sum Table > Ind Sum > C. Table Tax',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SC: '[M] Report > CAT > Sum Table > Ind Sum > C. Table SC',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_SR: '[M] Report > CAT > Sum Table > Ind Sum > C. Table SR',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_TABLE_NS: '[M] Report > CAT > Sum Table > Ind Sum > C. Table NS',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM: '[M] Report > CAT > Sum Table > Ind Sum > T NS > C. Icon QM',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_PREV_PAGE_BTN: '[M] Report > CAT > Sum Table > Ind Sum > C. Prev Page Btn',
    MODULE_REPORT_CAT_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BTN: '[M] Report > CAT > Sum Table > Ind Sum > C. Next Page Btn',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_P_P_B: '[M] Report > CAT > Sum Table > Ind Table > C. P P B',
    MODULE_REPORT_CAT_SUM_TABLE_IND_TABLE_C_N_P_B: '[M] Report > CAT > Sum Table > Ind Table > C. N P B',

    // Report - Product & Category
    MODULE_REPORT_PNC_C_LOGO: '[M] Report > PNC > C. Logo',
    MODULE_REPORT_PNC_C_PROFILE: '[M] Report > PNC > C. Profile',
    MODULE_REPORT_PNC_DD_APP_TYPE: '[M] Report > PNC > DD. App Type',
    MODULE_REPORT_PNC_C_DOWNLOAD_BTN: '[M] Report > PNC > C. Download Btn',
    MODULE_REPORT_PNC_DL_BTN_TB_EMAIL: '[M] Report > PNC > DL Btn > TB Email',
    MODULE_REPORT_PNC_DL_BTN_C_REP_EXCEL: '[M] Report > PNC > DL Btn > C. Rep Excel',
    MODULE_REPORT_PNC_DL_BTN_C_REP_CSV: '[M] Report > PNC > DL Btn > C. Rep CSV',
    MODULE_REPORT_PNC_C_CAL_START: '[M] Report > PNC > C. Cal Start',
    MODULE_REPORT_PNC_C_CAL_END: '[M] Report > PNC > C. Cal End',
    MODULE_REPORT_PNC_TB_SEARCH: '[M] Report > PNC > TB. Search',
    // MODULE_REPORT_PNC_DD_DATE_LOG: '[M] Report > PNC > DD. Date Log',
    MODULE_REPORT_PNC_DD_PROD_FILTER: '[M] Report > PNC > DD. Prod Filter',
    MODULE_REPORT_PNC_DD_CHANNEL_FILTER: '[M] Report > PNC > DD. Channel Filter',
    MODULE_REPORT_PNC_C_PRINT: '[M] Report > PNC > C. Print',
    MODULE_REPORT_PNC_C_PRINT_WO_TAX_SC: '[M] Report > PNC > C. Print W/O Tax/SC',
    MODULE_REPORT_PNC_C_CAL_START: '[M] Report > PNC > C. Cal Start',
    MODULE_REPORT_PNC_C_CAL_END: '[M] Report > PNC > C. Cal End',
    MODULE_REPORT_PNC_C_CHART_TODAY: '[M] Report > PNC > C. Chart Today',
    MODULE_REPORT_PNC_C_CHART_TW: '[M] Report > PNC > C. Chart Tw',
    MODULE_REPORT_PNC_C_CHART_MONTH: '[M] Report > PNC > C. Chart Month',
    MODULE_REPORT_PNC_C_CHART_3_MONTHS: '[M] Report > PNC > C. Chart 3 Months',
    MODULE_REPORT_PNC_C_CHART_6_MONTHS: '[M] Report > PNC > C. Chart 6 Months',
    MODULE_REPORT_PNC_C_CHART_1_YEAR: '[M] Report > PNC > C. Chart 1 Year',
    MODULE_REPORT_PNC_C_CHART_YESTERDAY: '[M] Report > PNC > C. Chart Yesterday',
    MODULE_REPORT_PNC_SUM_TABLE_C_PROD_CAT: '[M] Report > PNC > Sum Table > C. Prod Cat',
    MODULE_REPORT_PNC_SUM_TABLE_C_ITEM_SOLD: '[M] Report > PNC > Sum Table > C. Item Sold',
    MODULE_REPORT_PNC_SUM_TABLE_C_SALES: '[M] Report > PNC > Sum Table > C. Sales',
    MODULE_REPORT_PNC_SUM_TABLE_SALES_C_ICON_QM: '[M] Report > PNC > Sum Table > Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_C_DISC_RM: '[M] Report > PNC > Sum Table > C. Disc RM',
    MODULE_REPORT_PNC_SUM_TABLE_C_DISC_PERC: '[M] Report > PNC > Sum Table > C. Disc Perc',
    MODULE_REPORT_PNC_SUM_TABLE_C_TAX: '[M] Report > PNC > Sum Table > C. Tax',
    MODULE_REPORT_PNC_SUM_TABLE_C_SERV_CHAR: '[M] Report > PNC > Sum Table > C. Serv Char', // Service Charge
    MODULE_REPORT_PNC_SUM_TABLE_C_SALES_RET: '[M] Report > PNC > Sum Table > C. Sales Ret', // Sales Return
    MODULE_REPORT_PNC_SUM_TABLE_C_NET_SALES: '[M] Report > PNC > Sum Table > C. Net Sales',
    MODULE_REPORT_PNC_SUM_TABLE_NET_SALES_C_ICON_QM: '[M] Report > PNC > Sum Table > Net Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SUMMARY_BUTTON: '[M] Report > PNC > Sum Table > First Sum Table > C. Summary',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_PROD: '[M] Report > PNC > Sum Table > First Sum Table > C. Prod',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_CAT: '[M] Report > PNC > Sum Table > First Sum Table > C. Cat',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SKU: '[M] Report > PNC > Sum Table > First Sum Table > C. SKU',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_ITEMS: '[M] Report > PNC > Sum Table > First Sum Table > C. Items',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_SALES: '[M] Report > PNC > Sum Table > First Sum Table > C. Sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_SALES_C_ICON_QM: '[M] Report > PNC > Sum Table > First Sum Table > Table Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_DISC_PERC: '[M] Report > PNC > Sum Table > First Sum Table > C. Disc Perc',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_DISC_RM: '[M] Report > PNC > Sum Table > First Sum Table > C. Disc RM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_PAY_TYPE: '[M] Report > PNC > Sum Table > First Sum Table > C. Pay Type',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_C_NET_SALES: '[M] Report > PNC > Sum Table > First Sum Table > C. Net Sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_TABLE_NS_C_ICON_QM: '[M] Report > PNC > Sum Table > First Sum Table > Table Net Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SUMMARY_BUTTON: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Summary',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_O_ID: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. O ID',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_TD: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. TD',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SALES: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_SALES_C_ICON_QM: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > Table Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_DISC_PERC: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Disc Perc',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_DISC_RM: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Disc RM',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_TAX: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Tax',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SERV_CHAR: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Serv Char',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_SALES_RET: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Sales Ret',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_C_NET_SALES: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > C. Net Sales',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_TABLE_NS_C_ICON_QM: '[M] Report > PNC > Sum Table > First Sum Table > Sec Sum Table > Table Net Sales > C. Icon QM',
    MODULE_REPORT_PNC_SUM_TABLE_C_PREV_PAGE: '[M] Report > PNC > Sum Table > C. Prev Page',
    MODULE_REPORT_PNC_SUM_TABLE_C_NEXT_PAGE: '[M] Report > PNC > Sum Table > C. Next Page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_C_PREV_PAGE: '[M] Report > PNC > Sum Table > First Sum Table > C. Prev Page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_C_NEXT_PAGE: '[M] Report > PNC > Sum Table > First Sum Table > C. Next Page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_C_PREV_PAGE: '[M] Report > PNC > Sum Table > First Sum Table > Second Sum > C. Prev Page',
    MODULE_REPORT_PNC_SUM_TABLE_FIRST_SUM_SEC_SUM_C_NEXT_PAGE: '[M] Report > PNC > Sum Table > First Sum Table > Second Sum > C. Next Page',

    // Report - Variant Sales
    MODULE_REPORT_VAR_C_LOGO: '[M] Report > VAR > C. Logo',
    MODULE_REPORT_VAR_C_PROFILE: '[M] Report > VAR > C. Profile',
    MODULE_REPORT_VAR_DD_APP_TYPE: '[M] Report > VAR > DD. App Type',
    MODULE_REPORT_VAR_C_DOWNLOAD_BTN: '[M] Report > VAR > C. Download Btn',
    MODULE_REPORT_VAR_DL_BTN_TB_EMAIL: '[M] Report > VAR > DL Btn > TB. Email',
    MODULE_REPORT_VAR_DL_BTN_C_REP_EXCEL: '[M] Report > VAR > DL Btn > C. Rep Excel',
    MODULE_REPORT_VAR_DL_BTN_C_REP_CSV: '[M] Report > VAR > DL Btn > C. Rep CSV',
    MODULE_REPORT_VAR_C_CAL_START: '[M] Report > VAR > C. Cal Start',
    MODULE_REPORT_VAR_C_CAL_END: '[M] Report > VAR > C. Cal End',
    MODULE_REPORT_VAR_TB_SEARCH: '[M] Report > VAR > TB Search',
    MODULE_REPORT_VAR_SUM_TABLE_C_VAR_NAME: '[M] Report > VAR > Sum Table > C. Var Name',
    MODULE_REPORT_VAR_SUM_TABLE_C_VAR_OPT: '[M] Report > VAR > Sum Table > C. Var Opt',
    MODULE_REPORT_VAR_SUM_TABLE_C_ITEM_SOLD: '[M] Report > VAR > Sum Table > C. Item Sold',
    MODULE_REPORT_VAR_SUM_TABLE_C_SALES: '[M] Report > VAR > Sum Table > C. Sales',
    MODULE_REPORT_VAR_SUM_TABLE_SALES_C_ICON_QM: '[M] Report > VAR > Sum Table > Sales > C. Icon QM',
    MODULE_REPORT_VAR_SUM_TABLE_C_DISC_PERC: '[M] Report > VAR > Sum Table > C. Disc Perc',
    MODULE_REPORT_VAR_SUM_TABLE_C_DISC_RM: '[M] Report > VAR > Sum Table > C. Disc RM',
    MODULE_REPORT_VAR_SUM_TABLE_C_TAX: '[M] Report > VAR > Sum Table > C. Tax',
    MODULE_REPORT_VAR_SUM_TABLE_C_SERV_CHAR: '[M] Report > VAR > Sum Table > C. Serv Char',
    MODULE_REPORT_VAR_SUM_TABLE_C_SALE_RET: '[M] Report > VAR > Sum Table > C. Sale Ret',
    MODULE_REPORT_VAR_SUM_TABLE_C_NET_SALES: '[M] Report > VAR > Sum Table > C. Net Sales',
    MODULE_REPORT_VAR_SUM_TABLE_NS_C_ICON_QM: '[M] Report > VAR > Sum Table > NS > C. Icon QM',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_O_ID: '[M] Report > Sum Table > Ind Sum Table > C. O ID',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_TD: '[M] Report > VAR > Sum Table > Ind Sum Table > C. TD',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SALES: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Sales',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_S_C_I_QM: '[M] Report > VAR > Sum Table > Ind Sum Table > S > C. I QM',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_DISC_RM: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Disc RM',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_DISC_PERC: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Disc Perc',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_TAX: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Tax',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SERV_C: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Serv C',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_SALE_R: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Sale R',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_C_NET_S: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Net S',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_TABLE_NS_C_I_QM: '[M] Report > VAR > Sum Table > Ind Sum Table > NS > C. I QM',
    MODULE_REPORT_VAR_SUM_TABLE_C_PREV_PAGE: '[M] Report > VAR > Sum Table > C. Prev Page',
    MODULE_REPORT_VAR_SUM_TABLE_C_NEXT_PAGE: '[M] Report > VAR > Sum Table > C. Next Page',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_C_PREV_PAGE: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Prev Page',
    MODULE_REPORT_VAR_SUM_TABLE_IND_SUM_C_NEXT_PAGE: '[M] Report > VAR > Sum Table > Ind Sum Table > C. Next Page',
    MODULE_REPORT_VAR_SUM_TABLE_C_SUMMARY: '[M] Report > VAR > Sum Table > C. Summary',

    // Report - Shift
    MODULE_REPORT_SHIFT_C_LOGO: '[M] Report > Shift > C. Logo',
    MODULE_REPORT_SHIFT_C_PROFILE: '[M] Report > Shift > C. Profile',
    MODULE_REPORT_SHIFT_DD_APP_TYPE: '[M] Report > Shift > DD. App Type',
    MODULE_REPORT_SHIFT_C_DOWNLOAD_BTN: '[M] Report > Shift > C. Download Button',
    MODULE_REPORT_SHIFT_DL_BTN_TB_EMAIL: '[M] Report > Shift > Download Button > TB. Email',
    MODULE_REPORT_SHIFT_DL_BTN_C_REP_EXCEL: '[M] Report > Shift > Download Button > C. Rep Excel',
    MODULE_REPORT_SHIFT_DL_BTN_C_REP_CSV: '[M] Report > Shift > Download Button > C. Rep CSV',
    MODULE_REPORT_SHIFT_C_CAL_START: '[M] Report > Shift > C. Calendar Start',
    MODULE_REPORT_SHIFT_C_CAL_END: '[M] Report > Shift > C. Calendar End',
    MODULE_REPORT_SHIFT_TB_SEARCH: '[M] Report > Shift > TB. Search',
    MODULE_REPORT_SHIFT_SUMMARY_C_OP_TABLE: '[M] Report > Shift > Summary > C. Operation Table',
    MODULE_REPORT_SHIFT_SUMMARY_C_CASH: '[M] Report > Shift > Summary > C. Cash',
    MODULE_REPORT_SHIFT_SUMMARY_C_ONLINE_OFFLINE_TRANSACTION: '[M] Report > Shift > Summary > C. Online/Offline Transaction',
    MODULE_REPORT_SHIFT_SUMMARY_C_TRANSACTION: '[M] Report > Shift > Summary > C. Transaction',
    MODULE_REPORT_SHIFT_SUMMARY_C_ONLINE_OFFLINE_SALES: '[M] Report > Shift > Summary > C. Online/Offline Sales',
    MODULE_REPORT_SHIFT_SUMMARY_C_SALES: '[M] Report > Shift > Summary > C. Sales',
    MODULE_REPORT_SHIFT_SUMMARY_SALES_C_ICON_QM: '[M] Report > Shift > Summary > Sales > C. Icon QM',
    MODULE_REPORT_SHIFT_SUMMARY_C_OPENING_CLOSING_BALANCE: '[M] Report > Shift > Summary > C. Opening/Closing Balance',
    MODULE_REPORT_SHIFT_SUMMARY_IND_LIST_C_PRINT: '[M] Report > Shift > Summary > Ind List > C. Print',
    MODULE_REPORT_SHIFT_SUMMARY_IND_LIST_C_PREVIEW: '[M] Report > Shift > Summary Ind List > C. Preview',
    MODULE_REPORT_SHIFT_SUMMARY_C_IND_LIST: '[M] Report > Shift > Shift > Summary > C. Ind List',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SUMMARY: '[M] Report > Shift > Summary > Ind Summary > C. Online/Offline Sales',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DATE_TIME: '[M] Report > Shift > Summary > Ind Summary > C. Date Time',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_CASHIER: '[M] Report > Shift > Summary > Ind Summary > C. Cashier',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SALES: '[M] Report > Shift > Summary > Ind Summary > C. Sales',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_SALES_C_ICON_QM: '[M] Report > Shift > Summary > Ind Summary > Sales > C. Icon QM',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DISC_RM: '[M] Report > Shift > Summary > Ind Summary > C. Discount RM',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_DISC_PERC: '[M] Report > Shift > Summary > Ind Summary > C. Discount Percentage',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_TAX: '[M] Report > Shift > Summary > Ind Summary > C. Tax',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SERV_CHAR: '[M] Report > Shift > Summary > Ind Summary > C. Service Charge',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_SALES_RET: '[M] Report > Shift > Summary > Ind Summary > C. Sales Return',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_C_NET_SALES: '[M] Report > Shift > Summary > Ind Summary > C. Net Sales',
    MODULE_REPORT_SHIFT_SUMMARY_IND_SUMMARY_NET_SALES_C_ICON_QM: '[M] Report > Shift > Summary > Ind Summary > Net Sales > C. Icon QM',
    MODULE_REPORT_SHIFT_SUMMARY_DD_ITEMS_SHOWED: '[M] Report > Shift > Summary > DD. Items Showed',
    MODULE_REPORT_SHIFT_SUMMARY_TB_PAGE: '[M] Report > Shift > Summary > TB. Page',
    MODULE_REPORT_SHIFT_SUMMARY_C_PREV_BUTTON: '[M] Report > Shift > Summary > C. Prev Button',
    MODULE_REPORT_SHIFT_SUMMARY_C_NEXT_BUTTON: '[M] Report > Shift > Summary > C. Next Button',

    // CRM - Customers
    MODULE_CRM_CUSTOMERS_C_LOGO: '[M] CRM > Customers > C. Logo',
    MODULE_CRM_CUSTOMERS_C_PROFILE: '[M] CRM > Customers > C. Profile',
    MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN: '[M] CRM > Customers > C. Download Button',
    MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL: '[M] CRM > Customers > Download Button > TB. Email',
    MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL: '[M] CRM > Customers > Download Button > C. Report Excel',
    MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV: '[M] CRM > Customers > Download Button > C. Report CSV',
    MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD: '[M] CRM > Customers > C. Batch Upload',
    MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP: '[M] CRM > Customers > Batch Upload > C. Upload Temp',
    MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP: '[M] CRM > Customers > Batch Upload > C. Export Temp',
    MODULE_CRM_CUSTOMERS_TB_SEARCH: '[M] CRM > Customers > TB. Search',

    MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID: '[M] CRM > Customers > List > C. Name & ID',
    MODULE_CRM_CUSTOMERS_LIST_C_CONTACT_INFO: '[M] CRM > Customers > List > C. Contact Info',
    MODULE_CRM_CUSTOMERS_LIST_C_GENDER: '[M] CRM > Customers > List > C. Contact Gender',
    MODULE_CRM_CUSTOMERS_LIST_C_DOB: '[M] CRM > Customers > List > C. DOB',
    MODULE_CRM_CUSTOMERS_LIST_C_RACE: '[M] CRM > Customers > List > C. Race',
    MODULE_CRM_CUSTOMERS_LIST_C_TIER: '[M] CRM > Customers > List > C. Tier',
    MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING: '[M] CRM > Customers > List > C. Average Spending',

    MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER: '[M] CRM > Customers > C. New Customer',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_BACK: '[M] CRM > Customers > Customer > C. Back',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_SAVE: '[M] CRM > Customers > Customer > C. Save',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_EDIT: '[M] CRM > Customers > Customer > C. Edit',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_DELETE: '[M] CRM > Customers > Customer > C. Delete',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_NAME: '[M] CRM > Customers > Customer > TB. Name',
    MODULE_CRM_CUSTOMERS_CUSTOMER_DD_GENDER: '[M] CRM > Customers > Customer > DD. Gender',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_RACE: '[M] CRM > Customers > Customer > TB. Race',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_CONTACT_1: '[M] CRM > Customers > Customer > TB. Contact 1',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_CONTACT_2: '[M] CRM > Customers > Customer > TB. Contact 2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_DOB_CAL_ICON: '[M] CRM > Customers > Customer > C. DOB Calendar Icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_CUSTOMER_UPLOAD_IMAGE: '[M] CRM > Customers > Customer > C. Customer Upload Image',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_TAGS: '[M] CRM > Customers > Customer > C. Tags',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_C_ADD: '[M] CRM > Customers > Customer > Tags > C. Add',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_TAG_C_SELECT: '[M] CRM > Customers > Customer > Tags > Tag > Tag > C. Select',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_REMOVE: '[M] CRM > Customers > Customer > Tags > Tag > C. Remove',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_ADD: '[M] CRM > Customers > Customer > Tags > Tag > C. Add',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TAGS_TAG_C_SEARCH: '[M] CRM > Customers > Customer > Tags > Tag > C. Search',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_NEXT_VISIT_DATE_CAL_ICON: '[M] CRM > Customers > Customer > C. Next Visit Date Calendar Icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_NEXT_VISIT_TIME_CAL_ICON: '[M] CRM > Customers > Customer > C. Next Visit Time Calendar Icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_FIRST_VISIT_CAL_ICON: '[M] CRM > Customers > Customer > C. First Visit Calendar Icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_C_LAST_VISIT_CAL_ICON: '[M] CRM > Customers > Customer > C. Last Visit Time Calendar Icon',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_EMAIL1: '[M] CRM > Customers > Customer > TB. Email1',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_EMAIL2: '[M] CRM > Customers > Customer > TB. Email2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_ADDRESS1: '[M] CRM > Customers > Customer > TB. Address1',
    MODULE_CRM_CUSTOMERS_CUSTOMER_TB_ADDRESS2: '[M] CRM > Customers > Customer > TB. Address2',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_PURCHASE_HISTORY: '[M] CRM > Customers > Customer > Summary > C. Purchase History',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_PURCHASE_HISTORY_C_DETAILS: '[M] CRM > Customers > Customer > Summary > Purchase History > C. Details',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_CASHBACK: '[M] CRM > Customers > Customer > Summary > C. Cashback',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_POINTS: '[M] CRM > Customers > Customer > Summary > C. Points',
    MOUDLE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_VISITATION: '[M] CRM > Customers > Customer > Summary > C. Visitation',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_VISITATION_C_DETAILS: '[M] CRM > Customers > Customer > Summary > Visitation > C. Details',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_VOUCHER: '[M] CRM > Customers > Customer > Summary > C. Voucher',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_PROMOTION: '[M] CRM > Customers > Customer > Summary > C. Promotion',
    MODULE_CRM_CUSTOMERS_CUSTOMER_SUMMARY_C_DOCKET: '[M] CRM > Customers > Customer > Summary > C. Docket',
    MODULE_CRM_CUSTOMERS_LIST_C_CUSTOMER_DETAILS: '[M] CRM > Customers > List > C. Customer Details',
    MODULE_CRM_CUSTOMERS_LIST_TB_PAGE: '[M] CRM > Customers > Customer > List > TB. Page',
    MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON: '[M] CRM > Customers > Customer > List > C. Prev Button',
    MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON: '[M] CRM > Customers > Customer > List > C. Next Button',

    MODULE_CRM_SEGMENT_C_LOGO: '[M] CRM > Segment > C. Logo',
    MODULE_CRM_SEGMENT_C_PROFILE: '[M] CRM > Segment > C. Profile',
    MODULE_CRM_SEGMENT_C_NEW_SEGMENT: '[M] CRM > Segment > C. New Segment',
    MODULE_CRM_SEGMENT_TB_SEARCH: '[M] CRM > Segment > TB. Search',
    MODULE_CRM_SEGMENT_LIST_C_SEGMENT_GROUP: '[M] CRM > Segment > List > C. Segment Group',
    MODULE_CRM_SEGMENT_TB_PAGE: '[M] CRM > Segment > TB. Page',
    MODULE_CRM_SEGMENT_C_PREV_BUTTON: '[M] CRM > Segment > C. Prev Button',
    MODULE_CRM_SEGMENT_C_NEXT_BUTTON: '[M] CRM > Segment > C. Next Button',

    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK : '[M] CRM > Segment > Add Segment > C. Back',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_UPDATE: '[M] CRM > Segment > Add Segment > C. Update',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_TB_SEGMENT_NAME: '[M] CRM > Segment > Add Segment > TB. Segment Name',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_C_TAGS: '[M] CRM > Segment > Add Segment > C. Tags',
    MODULE_CRM_SEGMENT_ADD_SEGMENT_TAGS_C_TAG_SELECT: '[M] CRM > Segment > Add Segment > Tags > C. Tag Select',

    MODULE_EMPLOYEE_MANAGE_E: '[M] Employee > Manage E.',
    MODULE_EMPLOYEE_ACTIVITY_LOG: '[M] Employee > Activity Log',
    MODULE_EMPLOYEE_E_TIMESHEET: '[M] Employee > E. Timesheet',

    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_LOGO: '[M] Employee > Manage Employee > C. Logo',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_PROFILE: '[M] Employee > Manage Employee > C. Profile',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_DOWNLOAD_BTN: '[M] Employee > Manage Employee > C. Download Button',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_TB_EMAIL: '[M] Employee > Manage Employee > Download Button > TB. Email',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_EXCEL: '[M] Employee > Manage Employee > Download Button > C. Report Excel',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_DL_BTN_C_REP_CSV: '[M] Employee > Manage Employee > Download Button > C. Report CSV',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_C_NEW_EMPLOYEE: '[M] Employee > Manage Employee > C. New Employee',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_TB_SEARCH: '[M] Employee > Manage Employee > TB. Search',

    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_BACK: '[M] Employee > Manage Employee > Employee > C. Back',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_SAVE: '[M] Employee > Manage Employee > Employee > C. Save',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_UPDATE: '[M] Employee > Manage Employee > Employee > C. Update',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_DELETE: '[M] Employee > Manage Employee > Employee > C. Delete',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_NAME: '[M] Employee > Manage Employee > Employee > TB. Name',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_USERNAME: '[M] Employee > Manage Employee > Employee > TB. Username',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_EMAIL: '[M] Employee > Manage Employee > Employee > TB. Email',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_C_PROFILE_PICTURE: '[M] Employee > Manage Employee > Employee > C. Profile Picture',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_ROLE: '[M] Employee > Manage Employee > Employee > DD. Role',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_OUTLET: '[M] Employee > Manage Employee > Employee > DD. Outlet',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_CONTACT_NUMBER: '[M] Employee > Manage Employee > Employee > TB. Contact Number',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PASSWORD: '[M] Employee > Manage Employee > Employee > TB. Password',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_TB_PIN: '[M] Employee > Manage Employee > Employee > TB. Pin',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_PRIVILEGES: '[M] Employee > Manage Employee > Employee > DD. Privileges',
    MODULE_EMPLOYEE_MANAGE_EMPLOYEE_EMPLOYEE_DD_SCREEN_BLOCK: '[M] Employee > Manage Employee > Employee > DD. Screen Block',

    MODULE_EMPLOYEE_ACTIVITY_LOG_C_LOGO: '[M] Employee > Activity Log > C. Logo',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_PROFILE: '[M] Employee > Activity Log > C. Profile',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_DOWNLOAD_BTN: '[M] Employee > Activity Log > C. Download Button',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_TB_EMAIL: '[M] Employee > Activity Log > Download Button > TB. Email',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_C_REP_EXCEL: '[M] Employee > Activity Log > Download Button > C. Report Excel',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DL_BTN_C_REP_CSV: '[M] Employee > Activity Log > Download Button > C. Report CSV',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TB_SEARCH: '[M] Employee > Activity Log > TB. Search',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_CAL_START: '[M] Employee > Activity Log > C. Calendar Start',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_CAL_END: '[M] Employee > Activity Log > C. Calendar End',
    MODULE_EMPLOYEE_ACTIVITY_LOG_DD_ITEMS_SHOWED: '[M] Employee > Activity Log > DD. Items Showed',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TB_PAGE: '[M] Employee > Activity Log > TB. Page',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_PREV_BUTTON: '[M] Employee > Activity Log > C. Previous Button',
    MODULE_EMPLOYEE_ACTIVITY_LOG_C_NEXT_BUTTON: '[M] Employee > Activity Log > C. Next Button',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TABLE_C_SUMMARY: '[M] Employee > Activity Log > Table > C. Summary',
    MODULE_EMPLOYEE_ACTIVITY_LOG_TABLE_C_DATA: '[M] Employee > Activity Log > Table > C. Data',

    MODULE_EMPLOYEE_E_TIMESHEET_C_LOGO: '[M] Employee > Employee Timesheet > C. Logo',
    MODULE_EMPLOYEE_E_TIMESHEET_C_PROFILE: '[M] Employee > Employee Timesheet > C. Profile',
    MODULE_EMPLOYEE_E_TIMESHEET_C_DOWNLOAD_BTN: '[M] Employee > Employee Timesheet > C. Download Button',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_TB_EMAIL: '[M] Employee > Employee Timesheet > Download Button > TB. Email',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_EXCEL: '[M] Employee > Employee Timesheet > Download Button > C. Report Excel',
    MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_CSV: '[M] Employee > Employee Timesheet > Download Button > C. Report CSV',
    MODULE_EMPLOYEE_E_TIMESHEET_TB_SEARCH: '[M] Employee > Employee Timesheet > TB. Search',
    MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_START: '[M] Employee > Employee Timesheet > C. Calendar Start',
    MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_END: '[M] Employee > Employee Timesheet > C. Calendar End',
    MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED: '[M] Employee > Employee Timesheet > DD. Items Showed',
    MODULE_EMPLOYEE_E_TIMESHEET_TB_PAGE: '[M] Employee > Employee Timesheet > TB. Page',
    MODULE_EMPLOYEE_E_TIMESHEET_C_PREV_BUTTON: '[M] Employee > Employee Timesheet > C. Previous Button',
    MODULE_EMPLOYEE_E_TIMESHEET_C_NEXT_BUTTON: '[M] Employee > Employee Timesheet > C. Next Button',
    MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_SUMMARY: '[M] Employee > Employee Timesheet > Table > C. Summary',
    MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_DATA: '[M] Employee > Employee Timesheet > Table > C. Data',


    MODULE_SETTINGS_GENERAL: '[M] Settings > General',
    MODULE_SETTINGS_SHIFT: '[M] Settings > Shift',
    MODULE_SETTINGS_RECEIPT: '[M] Settings > Receipt',
    MODULE_SETTINGS_ORDER: '[M] Settings > Order',
    MODULE_SETTINGS_PRINTER: '[M] Settings > Printer',
    MODULE_SETTINGS_PAYMENT: '[M] Settings > Payment',
    MODULE_SETTINGS_LOYALTY: '[M] Settings > Loyalty',

    MODULE_UPSELLING_LIST: '[M] Upselling > List',
    MODULE_UPSELLING_CAMPAIGN: '[M] Upselling > Campaign',
    MODULE_UPSELLING_REPORT: '[M] Upselling > Report',

    MODULE_RESERVATION_MANAGE_ADD_RESERVATION: '[M] Reservation > Manage > Add r.',
    MODULE_RESERVATION_MANAGE_FILTER: '[M] Reservation > Manage > Filter',

    MODULE_RESERVATION_ADD_GUEST: '[M] Reservation > Add > Guest',
    MODULE_RESERVATION_ADD_PAX: '[M] Reservation > Add > Pax',
    MOUDLE_RESERVATION_ADD_DATE: '[M] Reservation > Add > Date',
    MOUDLE_RESERVATION_ADD_MONTH: '[M] Reservation > Add > Month',
    MOUDLE_RESERVATION_ADD_YEAR: '[M] Reservation > Add > Year',
    MOUDLE_RESERVATION_ADD_TIME: '[M] Reservation > Add > Time',
    MOUDLE_RESERVATION_ADD_TABLE: '[M] Reservation > Add > Table',
    MOUDLE_RESERVATION_ADD_REMARK: '[M] Reservation > Add > Remark',
    MOUDLE_RESERVATION_ADD_DIETARY_RESTRICTIONS: '[M] Reservation > Add > Dietary restriction',
    MOUDLE_RESERVATION_ADD_SPECIAL_OCCASIONS: '[M] Reservation > Add > Special occasion',
    MOUDLE_RESERVATION_ADD_CONFIRM: '[M] Reservation > Add > Confirm',
    MOUDLE_RESERVATION_ADD_CANCEL: '[M] Reservation > Add > Cancel',

    MODULE_RESERVATION_OVERVIEW_TITLE_LEFT: '[M] Reservation > Overview > Title left',
    MODULE_RESERVATION_OVERVIEW_TITLE_RIGHT: '[M] Reservation > Overview > Title right',
    MODULE_RESERVATION_OVERVIEW_DETAIL_BOX: '[M] Reservation > Overview > Detail box',

    MODULE_RESERVATION_FOOTER_DATE: '[M] Reservation > Footer > Date',
    MODULE_RESERVATION_FOOTER_MONTH: '[M] Reservation > Footer > Month',
    MODULE_RESERVATION_FOOTER_YEAR: '[M] Reservation > Footer > Year',
    MODULE_RESERVATION_FOOTER_LEFT: '[M] Reservation > Footer > Left',
    MODULE_RESERVATION_FOOTER_RIGHT: '[M] Reservation > Footer > Right',

    MODULE_RESERVATION_ANALYTICS_DATE_START: '[M] Reservation > Analytics > Date S.',
    MODULE_RESERVATION_ANALYTICS_DATE_END: '[M] Reservation > Analytics > Date E.',

    MODULE_RESERVATION_SETTINGS_ALLOW_RESERVATION: '[M] Reservation > Setting > Allow R.',
    MODULE_RESERVATION_SETTINGS_MAX_PAX: '[M] Reservation > Setting > Max pax',
    MODULE_RESERVATION_SETTINGS_RESERVATION_LINK: '[M] Reservation > Setting > R. Link',
    MODULE_RESERVATION_SETTINGS_SAVE: '[M] Reservation > Setting > Save',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Inventory - Supplier
    MODULE_INVENTORY_SUPPLIER_C_LOGO: '[M] Inv > Supp > C Logo',
    MODULE_INVENTORY_SUPPLIER_C_PROFILE: '[M] Inv > Supp > C Profile',
    MODULE_INVENTORY_SUPPLIER_C_ITEM: '[M] Inv > Supp > C Item',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_C_IMAGE: '[M] Inv > Supp > Info > PTL > C Img',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_C_DELETE: '[M] Inv > Supp > Info > PTL > C Del',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_C_DELETE: '[M] Inv > Supp > Info > PIC > C Del',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_C_DELETE: '[M] Inv > Supp > Info > PD > C Del',
    MODULE_INVENTORY_SUPPLIER_INFO_HISTORY_C_ITEM: '[M] Inv > Supp > Info > History > C Item',
    MODULE_INVENTORY_SUPPLIER_INFO_C_SAVE_ALERT: '[M] Inv > Supp > Info > C Save > A',
    MODULE_INVENTORY_SUPPLIER_INFO_C_UPDATE_ALERT: '[M] Inv > Supp > Info > C Update > A',
    MODULE_INVENTORY_SUPPLIER_INFO_C_DELETE_ALERT: '[M] Inv > Supp > Info > C Delete > A',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Inv > Supp > Dl Modal > Dl As > C Excel > A',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Inv > Supp > Dl Modal > Dl As > C CSV > A',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_CLOSE: '[M] Inv > Supp > Batch Ul Modal > C Close',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_UPLOAD_TEMPLATE: '[M] Inv > Supp > Batch Ul Modal > C Ul Template',
    MODULE_INVENTORY_SUPPLIER_BATCH_UPLOAD_MODAL_C_EXPORT_TEMPLATE: '[M] Inv > Supp > Batch Ul Modal > C Export Template',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Supp > Dl Modal > C Close',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Supp > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Supp > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Supp > Dl Modal > Download As > C Excel',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Supp > Dl Modal > Download As > C CSV',
    MODULE_INVENTORY_SUPPLIER_C_DOWNLOAD: '[M] Inv > Supp > C Dl',
    MODULE_INVENTORY_SUPPLIER_C_BATCH_UPLOAD: '[M] Inv > Supp > C Batch Ul',
    MODULE_INVENTORY_SUPPLIER_C_ADD_SUPPLY: '[M] Inv > Supp > C Add Supp',
    MODULE_INVENTORY_SUPPLIER_INFO_C_BACK: '[M] Inv > Supp > Info > C Back',
    MODULE_INVENTORY_SUPPLIER_INFO_C_SAVE: '[M] Inv > Supp > Info > C Save',
    MODULE_INVENTORY_SUPPLIER_INFO_C_DELETE: '[M] Inv > Supp > Info > C Delete',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PERSON_IN_CHARGE: '[M] Inv > Supp > Info > C PIC',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PRODUCTS_TO_LINKED: '[M] Inv > Supp > Info > C PTL',
    MODULE_INVENTORY_SUPPLIER_INFO_C_PAYMENT_DETAILS: '[M] Inv > Supp > Info > C PD',
    MODULE_INVENTORY_SUPPLIER_INFO_C_HISTORY: '[M] Inv > Supp > Info > C History',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_C_ADD_PERSON_IN_CHARGE: '[M] Inv > Supp > Info > PIC > C Add PIC',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCTS_TO_LINKED_C_ADD_PRODUCT_SLOT: '[M] Inv > Supp > Info > PTL > C Add PS',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_C_ADD_PAYMENT_DETAIL: '[M] Inv > Supp > Info > PD > C PD',

    MODULE_INVENTORY_SUPPLIER_INFO_DD_TAX_RATE: '[M] Inv > Supp > Info > DD Tax Rate',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_DD_ITEM_NAME: '[M] Inv > Supp > Info > PTL > DD Item Name',

    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_SKU: '[M] Inv > Supp > Info > PTL > TB SKU',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_PRICE: '[M] Inv > Supp > Info > PTL > TB Price',
    MODULE_INVENTORY_SUPPLIER_INFO_PRODUCT_TO_LINK_TB_COST: '[M] Inv > Supp > Info > PTL > TB Cost',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_NAME: '[M] Inv > Supp > Info > PIC > TB Name',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_PHONE: '[M] Inv > Supp > Info > PIC > TB Phone',
    MODULE_INVENTORY_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_EMAIL: '[M] Inv > Supp > Info > PIC > TB Email',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_ACCOUNT_NAME: '[M] Inv > Supp > Info > PD > TB Acc Name',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_ACCOUNT_NO: '[M] Inv > Supp > Info > PD > TB Bank Acc No',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_NAME: '[M] Inv > Supp > Info > PD > TB Bank Name',
    MODULE_INVENTORY_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_BRANCH: '[M] Inv > Supp > Info > PD > TB Bank Branch',
    MODULE_INVENTORY_SUPPLIER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Supp > Dl Modal > TB Email Address',
    MODULE_INVENTORY_SUPPLIER_TB_SEARCH: '[M] Inv > Supp > TB Search',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_NAME: '[M] Inv > Supp > Info > TB Company Name',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NAME: '[M] Inv > Supp > Info > TB Company Registration Name',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NO: '[M] Inv > Supp > Info > TB Company Registration No',
    MODULE_INVENTORY_SUPPLIER_INFO_TB_COMPANY_ADDRESS: '[M] Inv > Supp > Info > TB Company Address',

    // Inventory - Inventory Overview
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_LOGO: '[M] Inv > Inv Ov > C Logo',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_PROFILE: '[M] Inv > Inv Ov > C Profile',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_ITEM: '[M] Inv > Inv Ov > C Item',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_SUPPLIER: '[M] Inv > Inv Ov > C Supplier',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_ADD_INVENTORY_C_DELETE: '[M] Inv > Inv Ov > Add Inv > C Delete',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT: '[M] Inv > Inv Ov > Add Inv > C Add > A',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Inv > Inv Ov > Dl Modal > Dl As > C Excel > A',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Inv > Inv Ov > Dl Modal > Dl As > C CSV > A',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Inv Ov > Dl Modal > C CSV > A',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Inv Ov > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Inv Ov > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Inv Ov > Dl Modal > Dl As > C Excel',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Inv Ov > Dl Modal > Dl As > C CSV',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_DOWNLOAD: '[M] Inv > Inv Ov > C Dl',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_INVENTORY: '[M] Inv > Inv Ov > C Inv',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_C_SAVE: '[M] Inv > Inv Ov > C Save',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_BACK: '[M] Inv > Inv Ov > Inv > C Back',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_ADD: '[M] Inv > Inv Ov > Inv > C Add',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_C_ADD_PRODUCT_SLOT: '[M] Inv > Inv Ov > Inv > C Add Prod Slot',

    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_DD_PRODUCT_NAME: '[M] Inv > Inv Ov > Inv > DD Prod Name',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DD_STOCK_STATUS: '[M] Inv > Inv Ov > DD Stock Status',

    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_IDEAL_STOCK: '[M] Inv > Inv Ov > TB Ideal Stock',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_WARNING_STOCK: '[M] Inv > Inv Ov > TB Warning Stock',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_INVENTORY_TB_INSERT_QUANTITY: '[M] Inv > Inv Ov > Inv > TB Insert Quantity',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Inv Ov > Dl Modal > TB Email Address',
    MODULE_INVENTORY_INVENTORY_OVERVIEW_TB_SEARCH: '[M] Inv > Inv Ov > TB Search',

    // Inventory - Purchase Order
    MODULE_INVENTORY_PURCHASE_ORDER_C_LOGO: '[M] Inv > Purchase Order > C Logo',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PROFILE: '[M] Inv > Purchase Order > C Profile',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Inv > Purchase Order > Dl Modal > Dl As > C Excel > A',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Inv > Purchase Order > Dl Modal > Dl As > C CSV > A',
    MODULE_INVENTORY_PURCHASE_ORDER_C_ITEM: '[M] Inv > Purchase Order > C Item',
    MODULE_INVENTORY_PURCHASE_ORDER_ITEM_C_DUPLICATE: '[M] Inv > Purchase Order > Item > C Duplicate',
    MODULE_INVENTORY_PURCHASE_ORDER_ITEM_C_DOTS: '[M] Inv > Purchase Order > Item > C Dots',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE: '[M] Inv > Purchase Order > Info > Items Ordered > C Delete',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT: '[M] Inv > Purchase Order > Info > C Save > E A',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT: '[M] Inv > Purchase Order > Info > C Save > S A',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT: '[M] Inv > Purchase Order > Info > C Save > E Q A',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT: '[M] Inv > Purchase Order > Info > C Update > S A',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Purchase Order > Dl Modal > C Close',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Purchase Order > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Purchase Order > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Purchase Order > Dl Modal > Dl As > C Excel',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Purchase Order > Dl Modal > Dl As > C CSV',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_CLOSE: '[M] Inv > Purchase Order > Ul Modal > C Close',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_UPLOAD: '[M] Inv > Purchase Order > Ul Modal > C Upload',
    MODULE_INVENTORY_PURCHASE_ORDER_UPLOAD_MODAL_C_EXPORT: '[M] Inv > Purchase Order > Ul Modal > C Export',
    MODULE_INVENTORY_PURCHASE_ORDER_C_DOWNLOAD: '[M] Inv > Purchase Order > C Dl',
    MODULE_INVENTORY_PURCHASE_ORDER_C_UPLOAD: '[M] Inv > Purchase Order > C Ul',
    MODULE_INVENTORY_PURCHASE_ORDER_C_ADD_PO: '[M] Inv > Purchase Order > C Add PO',
    MODULE_INVENTORY_PURCHASE_ORDER_C_START_DATE: '[M] Inv > Purchase Order > C Start Date',
    MODULE_INVENTORY_PURCHASE_ORDER_C_END_DATE: '[M] Inv > Purchase Order > C End Date',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PO_TOADY: '[M] Inv > Purchase Order > C PO Today',
    MODULE_INVENTORY_PURCHASE_ORDER_C_PENDING_PO: '[M] Inv > Purchase Order > C Pending PO',
    MODULE_INVENTORY_PURCHASE_ORDER_C_COMPLETED_PO: '[M] Inv > Purchase Order > C Completed PO',
    MODULE_INVENTORY_PURCHASE_ORDER_C_CANCELLED_PO: '[M] Inv > Purchase Order > C Cancelled PO',
    MODULE_INVENTORY_PURCHASE_ORDER_C_SUMMARY: '[M] Inv > Purchase Order > C Summary',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_BACK: '[M] Inv > Purchase Order > Info > C Back',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_UPDATE: '[M] Inv > Purchase Order > Info > C Update',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE: '[M] Inv > Purchase Order > Info > C Save',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND: '[M] Inv > Purchase Order > Info > C Save And Send',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON: '[M] Inv > Purchase Order > Info > C Purchase Order Id Icon',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON: '[M] Inv > Purchase Order > Info > C Est Arrival Icon',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT: '[M] Inv > Purchase Order > Info > Items Ordered > C Add Product Slot',

    MODULE_INVENTORY_PURCHASE_ORDER_INFO_DD_SUPPLIER: '[M] Inv > Purchase Order > Info > DD Supplier',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_DD_TARGET_STORE: '[M] Inv > Purchase Order > Info > DD Target Store',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME: '[M] Inv > Purchase Order > Info > Items Ordered > DD Product Name',

    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY: '[M] Inv > Purchase Order > Info > Items Ordered > TB Ordered Qty',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY: '[M] Inv > Purchase Order > Info > Items Ordered > TB Received Qty',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_PRODUCT_PRICE: '[M] Inv > Purchase Order > Info > Items Ordered > TB Product Price',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_COST_PRICE: '[M] Inv > Purchase Order > Info > Items Ordered > TB Cost Price',
    MODULE_INVENTORY_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Purchase Order > Dl Modal > TB Email Address',
    MODULE_INVENTORY_PURCHASE_ORDER_TB_SEARCH: '[M] Inv > Purchase Order > TB Search',
    MODULE_INVENTORY_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID: '[M] Inv > Purchase Order > Info > TB Purchase Order Id',

    // Inventory - Stock Transfer
    MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO: '[M] Inv > Stock Transfer > C Logo',
    MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE: '[M] Inv > Stock Transfer > C Profile',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: '[M] Inv > Stock Transfer > Dl Modal > Dl As > C Excel > S A',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: '[M] Inv > Stock Transfer > Dl Modal > Dl As > C CSV > S A',
    MODULE_INVENTORY_STOCK_TRANSFER_C_ITEM: '[M] Inv > Stock Transfer > C Item',
    MODULE_INVENTORY_STOCK_TRANSFER_ITEM_C_EXPORT: '[M] Inv > Stock Transfer > Item > C Export',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE: '[M] Inv > Stock Transfer > Info > Stock To Transfer > C Delete',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT: '[M] Inv > Stock Transfer > Info > C Save > Create S A',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: '[M] Inv > Stock Transfer > Info > C Save > Update S A',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Stock Transfer > Dl Modal > C Close',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Stock Transfer > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Stock Transfer > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Stock Transfer > Dl Modal > Dl As > C Excel',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Stock Transfer > Dl Modal > Dl As > C CSV',
    MODULE_INVENTORY_STOCK_TRANSFER_C_DOWNLOAD: '[M] Inv > Stock Transfer > C Dl',
    MODULE_INVENTORY_STOCK_TRANSFER_C_STOCK_TRANSFER: '[M] Inv > Stock Transfer > C Stock Transfer',
    MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE: '[M] Inv > Stock Transfer > C Start Date',
    MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE: '[M] Inv > Stock Transfer > C End Date',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_BACK: '[M] Inv > Stock Transfer > Info > C Back',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED: '[M] Inv > Stock Transfer > Edit > C Mark Completed',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE: '[M] Inv > Stock Transfer > Info > C Save',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_ID_C_EDIT: '[M] Inv > Stock Transfer > Info > ID > C Edit',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR: '[M] Inv > Stock Transfer > Info > Shipped Date > C Calendar',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: '[M] Inv > Stock Transfer > Info > Stock To Transfer > C Add Product Slot',

    MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_SOURCE_STORE: '[M] Inv > Stock Transfer > Info > DD Source Store',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE: '[M] Inv > Stock Transfer > Info > DD Destination Store',
    MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: '[M] Inv > Stock Transfer > Info > Stock To Transfer > DD Product Name',

    MODULE_INVENTORY_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Inv > Stock Transfer > New > Stock To Transfer > TB Transfer Qty',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Inv > Stock Transfer > Edit > Stock To Transfer > TB Transfer Qty',
    MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Stock Transfer > Dl Modal > TB Email Address',
    MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH: '[M] Inv > Stock Transfer > TB Search',
    MODULE_INVENTORY_STOCK_TRANSFER_EDIT_TB_ID: '[M] Inv > Stock Transfer > Edit > TB Search',

    // Inventory - Stock Take
    MODULE_INVENTORY_STOCK_TAKE_C_LOGO: '[M] Inv > Stock Take > C Logo',
    MODULE_INVENTORY_STOCK_TAKE_C_PROFILE: '[M] Inv > Stock Take > C Profile',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: '[M] Inv > Stock Take > Dl Modal > Dl As > C Excel > S A',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: '[M] Inv > Stock Take > Dl Modal > Dl As > C CSV > S A',
    MODULE_INVENTORY_STOCK_TAKE_C_ITEM: '[M] Inv > Stock Take > C Item',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_C_CLIPBOARD: '[M] Inv > Stock Take > Info > Item > C Clipbaord',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE_CREATE_SUCCESS_ALERT: '[M] Inv > Stock Take > Info > C Save > Create S A',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: '[M] Inv > Stock Take > Info > C Save > Update S A',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Stock Take > Dl Modal > C Close',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Stock Take > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Stock Take > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Stock Take > Dl Modal > Dl As > C Excel',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Stock Take > Dl Modal > Dl As > C CSV',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_CLOSE: '[M] Inv > Stock Take > Info > Item > Clipboard > Remarks Modal > C Close',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_DONE: '[M] Inv > Stock Take > Info > Item > Clipboard > Remarks Modal > C Done',
    MODULE_INVENTORY_STOCK_TAKE_C_DOWNLOAD: '[M] Inv > Stock Take > C Dl',
    MODULE_INVENTORY_STOCK_TAKE_C_STOCK_TAKE: '[M] Inv > Stock Take > C Stock Take',
    MODULE_INVENTORY_STOCK_TAKE_C_START_DATE: '[M] Inv > Stock Take > C Start Date',
    MODULE_INVENTORY_STOCK_TAKE_C_END_DATE: '[M] Inv > Stock Take > C End Date',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_BACK: '[M] Inv > Stock Take > Info > C Back',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_SAVE: '[M] Inv > Stock Take > Info > C Save',
    MODULE_INVENTORY_STOCK_TAKE_INFO_C_COMPLETE: '[M] Inv > Stock Take > Info > C Complete',
    MODULE_INVENTORY_STOCK_TAKE_EDIT_C_CANCEL: '[M] Inv > Stock Take > Edit > C Cancel',
    MODULE_INVENTORY_STOCK_TAKE_EDIT_C_CANCEL_SUCCESS_ALERT: '[M] Inv > Stock Take > Edit > C Cancel > S A',
    MODULE_INVENTORY_STOCK_TAKE_NEW_ID_C_EDIT: '[M] Inv > Stock Take > New > ID > C Edit',

    MODULE_INVENTORY_STOCK_TAKE_INFO_DD_TARGET_SUPPLIER: '[M] Inv > Stock Take > Info > DD Target Supplier',

    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_TB_COUNTED: '[M] Inv > Stock Take > Info > Item > TB Counted',
    MODULE_INVENTORY_STOCK_TAKE_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Stock Take > Dl Modal > TB Email Address',
    MODULE_INVENTORY_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_TB_REMARKS: '[M] Inv > Stock Take > Info > Item > Clipboard > Remarks Modal > TB Remarks',
    MODULE_INVENTORY_STOCK_TAKE_TB_SEARCH: '[M] Inv > Stock Take > TB Search',
    MODULE_INVENTORY_STOCK_TAKE_NEW_TB_ID: '[M] Inv > Stock Take > New > TB ID',
    MODULE_INVENTORY_STOCK_TAKE_INFO_TB_STOCK_TAKE_SUMMARY: '[M] Inv > Stock Take > Info > TB Stock Take Summary',

    // Inventory - Stock Return
    MODULE_INVENTORY_STOCK_RETURN_C_LOGO: '[M] Inv > Stock Return > C Logo',
    MODULE_INVENTORY_STOCK_RETURN_C_PROFILE: '[M] Inv > Stock Return > C Profile',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: '[M] Inv > Stock Return > Dl Modal > Dl As > C Excel > S A',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: '[M] Inv > Stock Return > Dl Modal > Dl As > C CSV > S A',
    MODULE_INVENTORY_STOCK_RETURN_C_ITEM: '[M] Inv > Stock Return > C Item',
    MODULE_INVENTORY_STOCK_RETURN_INFO_STOCK_TO_TRANSFER_C_DELETE: '[M] Inv > Stock Return > Info > Stock To Transfer > C Delete',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE_CREATE_SUCCESS_ALERT: '[M] Inv > Stock Return > Info > C Save > Create S A',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: '[M] Inv > Stock Return > Info > C Save > Update S A',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_C_CLOSE: '[M] Inv > Stock Return > Dl Modal > C Close',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Inv > Stock Return > Dl Modal > Send As > C Excel',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Inv > Stock Return > Dl Modal > Send As > C CSV',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Inv > Stock Return > Dl Modal > Dl As > C Excel',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Inv > Stock Return > Dl Modal > Dl As > C CSV',
    MODULE_INVENTORY_STOCK_RETURN_C_DOWNLOAD: '[M] Inv > Stock Return > C Dl',
    MODULE_INVENTORY_STOCK_RETURN_C_STOCK_RETURN: '[M] Inv > Stock Return > C Stock Return',
    MODULE_INVENTORY_STOCK_RETURN_C_START_DATE: '[M] Inv > Stock Return > C Start Date',
    MODULE_INVENTORY_STOCK_RETURN_C_END_DATE: '[M] Inv > Stock Return > C End Date',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_BACK: '[M] Inv > Stock Return > Info > C Back',
    MODULE_INVENTORY_STOCK_RETURN_EDIT_C_MARK_COMPLETED: '[M] Inv > Stock Return > Edit > C Mark Completed',
    MODULE_INVENTORY_STOCK_RETURN_INFO_C_SAVE: '[M] Inv > Stock Return > Info > C Save',
    MODULE_INVENTORY_STOCK_RETURN_NEW_ID_C_EDIT: '[M] Inv > Stock Return > New > ID > C Edit',
    MODULE_INVENTORY_STOCK_RETURN_INFO_SHIPPED_DATE_C_CALENDAR: '[M] Inv > Stock Return > Info > Shipped Date > C Calendar',
    MODULE_INVENTORY_STOCK_RETURN_NEW_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: '[M] Inv > Stock Return > New > Stock To Transfer > C Add Product Slot',

    MODULE_INVENTORY_STOCK_RETURN_INFO_DD_SOURCE_STORE: '[M] Inv > Stock Return > Info > DD Source Store',
    MODULE_INVENTORY_STOCK_RETURN_INFO_DD_DESTINATION_SUPPLIER: '[M] Inv > Stock Return > Info > DD Destination Supplier',
    MODULE_INVENTORY_STOCK_RETURN_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: '[M] Inv > Stock Return > Info > Stock To Transfer > DD Product Name',

    MODULE_INVENTORY_STOCK_RETURN_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Inv > Stock Return > New > Stock To Transfer > TB Transfer Qty',
    MODULE_INVENTORY_STOCK_RETURN_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Inv > Stock Return > Edit > Stock To Transfer > TB Transfer Qty',
    MODULE_INVENTORY_STOCK_RETURN_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Inv > Stock Take > Dl Modal > TB Email Address',
    MODULE_INVENTORY_STOCK_RETURN_TB_SEARCH: '[M] Inv > Stock Take > TB Search',
    MODULE_INVENTORY_STOCK_RETURN_NEW_TB_ID: '[M] Inv > Stock Return > New > TB ID',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Composite - Supplier
    MODULE_COMPOSITE_SUPPLIER_C_LOGO: '[M] Com > Supp > C Logo',
    MODULE_COMPOSITE_SUPPLIER_C_PROFILE: '[M] Com > Supp > C Profile',
    MODULE_COMPOSITE_SUPPLIER_C_ITEM: '[M] Com > Supp > C Item',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_IMAGE: '[M] Com > Supp > Info > SI > C Img',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_DELETE: '[M] Com > Supp > Info > SI > C Del',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_C_DELETE: '[M] Com > Supp > Info > PIC > C Del',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_C_DELETE: '[M] Com > Supp > Info > PD > C Del',
    MODULE_COMPOSITE_SUPPLIER_INFO_HISTORY_C_ITEM: '[M] Com > Supp > Info > History > C Item',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SAVE_ALERT: '[M] Com > Supp > Info > C Save > A',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_UPDATE_ALERT: '[M] Com > Supp > Info > C Update > A',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_DELETE_ALERT: '[M] Com > Supp > Info > C Delete > A',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Com > Supp > Dl Modal > Dl As > C Excel > A',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Com > Supp > Dl Modal > Dl As > C CSV > A',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_C_CLOSE: '[M] Com > Supp > Dl Modal > C Close',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Com > Supp > Dl Modal > Send As > C Excel',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Com > Supp > Dl Modal > Send As > C CSV',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Com > Supp > Dl Modal > Download As > C Excel',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Com > Supp > Dl Modal > Download As > C CSV',
    MODULE_COMPOSITE_SUPPLIER_C_DOWNLOAD: '[M] Com > Supp > C Dl',
    MODULE_COMPOSITE_SUPPLIER_C_BATCH_UPLOAD: '[M] Com > Supp > C Batch Ul',
    MODULE_COMPOSITE_SUPPLIER_C_ADD_SUPPLY: '[M] Com > Supp > C Add Supp',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_BACK: '[M] Com > Supp > Info > C Back',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SAVE: '[M] Com > Supp > Info > C Save',
    MODULE_COMPOSITE_SUPPLIER_UPDATE_C_DELETE: '[M] Com > Supp > Update > C Delete',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_PERSON_IN_CHARGE: '[M] Com > Supp > Info > C PIC',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_SUPPLY_ITEMS: '[M] Com > Supp > Info > C SI',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_PAYMENT_DETAILS: '[M] Com > Supp > Info > C PD',
    MODULE_COMPOSITE_SUPPLIER_INFO_C_HISTORY: '[M] Com > Supp > Info > C History',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_C_ADD_PERSON_IN_CHARGE: '[M] Com > Supp > Info > PIC > C Add PIC',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_C_ADD_SUPPLY_ITEMS: '[M] Com > Supp > Info > SI > C Add SI',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_C_ADD_PAYMENT_DETAIL: '[M] Com > Supp > Info > PD > C PD',

    MODULE_COMPOSITE_SUPPLIER_INFO_DD_TAX_RATE: '[M] Com > Supp > Info > DD Tax Rate',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_DD_UNIT: '[M] Com > Supp > Info > SI > DD Unit',

    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_ITEM_NAME: '[M] Com > Supp > Info > SI > TB Item Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_SKU: '[M] Com > Supp > Info > SI > TB SKU',
    MODULE_COMPOSITE_SUPPLIER_INFO_SUPPLY_ITEMS_TB_COST: '[M] Com > Supp > Info > SI > TB Cost',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_NAME: '[M] Com > Supp > Info > PIC > TB Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_PHONE: '[M] Com > Supp > Info > PIC > TB Phone',
    MODULE_COMPOSITE_SUPPLIER_INFO_PERSON_IN_CHARGE_TB_EMAIL: '[M] Com > Supp > Info > PIC > TB Email',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_ACCOUNT_NAME: '[M] Com > Supp > Info > PD > TB Acc Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_ACCOUNT_NO: '[M] Com > Supp > Info > PD > TB Bank Acc No',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_NAME: '[M] Com > Supp > Info > PD > TB Bank Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_PAYMENT_DETAILS_TB_BANK_BRANCH: '[M] Com > Supp > Info > PD > TB Bank Branch',
    MODULE_COMPOSITE_SUPPLIER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Com > Supp > Dl Modal > TB Email Address',
    MODULE_COMPOSITE_SUPPLIER_TB_SEARCH: '[M] Com > Supp > TB Search',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_NAME: '[M] Com > Supp > Info > TB Company Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NAME: '[M] Com > Supp > Info > TB Company Registration Name',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_REGISTRATION_NO: '[M] Com > Supp > Info > TB Company Registration No',
    MODULE_COMPOSITE_SUPPLIER_INFO_TB_COMPANY_ADDRESS: '[M] Com > Supp > Info > TB Company Address',

    // Composite - Inventory Overview
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_LOGO: '[M] Com > Inv Ov > C Logo',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_PROFILE: '[M] Com > Inv Ov > C Profile',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_ITEM: '[M] Com > Inv Ov > C Item',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_SUPPLIER: '[M] Com > Inv Ov > C Supplier',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_DELETE: '[M] Com > Inv Ov > C Delete',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_ARROW: '[M] Com > Inv Ov > C Arrow',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_DELETE: '[M] Com > Inv Ov > Add Inv > C Delete',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT: '[M] Com > Inv Ov > Add Inv > C Add > A',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Com > Inv Ov > Dl Modal > Dl As > C Excel > A',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Com > Inv Ov > Dl Modal > Dl As > C CSV > A',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_C_CLOSE: '[M] Com > Inv Ov > Dl Modal > C CSV > A',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Com > Inv Ov > Dl Modal > Send As > C Excel',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Com > Inv Ov > Dl Modal > Send As > C CSV',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Com > Inv Ov > Dl Modal > Dl As > C Excel',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Com > Inv Ov > Dl Modal > Dl As > C CSV',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_CLOSE: '[M] Com > Inv Ov > Batch Ul Modal > C Close',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_UPLOAD_TEMPLATE: '[M] Com > Inv Ov > Batch Ul Modal > C Ul Template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_BATCH_UPLOAD_MODAL_C_EXPORT_TEMPLATE: '[M] Com > Inv Ov > Batch Ul Modal > C Export Template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_CLOSE: '[M] Com > Inv Ov > Ul Modal > C Export Template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_DELETE: '[M] Com > Inv Ov > Ul Modal > C Export Template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DELETE_MODAL_C_CANCEL: '[M] Com > Inv Ov > Ul Modal > C Export Template',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_DOWNLOAD: '[M] Com > Inv Ov > C Dl',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_BATCH_UPLOAD: '[M] Com > Inv Ov > C Batch Ul',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_INVENTORY: '[M] Com > Inv Ov > C Inv',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_SAVE: '[M] Com > Inv Ov > C Save',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_ITEM_C_BACK: '[M] Com > Inv Ov > Item > C Back',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_BACK: '[M] Com > Inv Ov > Inv > C Back',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_ADD: '[M] Com > Inv Ov > Inv > C Add',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_C_ADD_PRODUCT_SLOT: '[M] Com > Inv Ov > Inv > C Add Prod Slot',

    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_DD_PRODUCT_NAME: '[M] Com > Inv Ov > Inv > DD Prod Name',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DD_STOCK_STATUS: '[M] Com > Inv Ov > DD Stock Status',

    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_IDEAL_STOCK: '[M] Com > Inv Ov > TB Ideal Stock',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_WARNING_STOCK: '[M] Com > Inv Ov > TB Warning Stock',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_INVENTORY_TB_INSERT_QUANTITY: '[M] Com > Inv Ov > Inv > TB Insert Quantity',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Com > Inv Ov > Dl Modal > TB Email Address',
    MODULE_COMPOSITE_INVENTORY_OVERVIEW_TB_SEARCH: '[M] Com > Inv Ov > TB Search',

    // Composite - Purchase Order
    MODULE_COMPOSITE_PURCHASE_ORDER_C_LOGO: '[M] Com > Purchase Order > C Logo',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PROFILE: '[M] Com > Purchase Order > C Profile',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT: '[M] Com > Purchase Order > Dl Modal > Dl As > C Excel > A',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT: '[M] Com > Purchase Order > Dl Modal > Dl As > C CSV > A',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_ITEM: '[M] Com > Purchase Order > C Item',
    MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DUPLICATE: '[M] Com > Purchase Order > Item > C Duplicate',
    MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DOTS: '[M] Com > Purchase Order > Item > C Dots',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE: '[M] Com > Purchase Order > Info > Items Ordered > C Delete',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT: '[M] Com > Purchase Order > Info > C Save > E A',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT: '[M] Com > Purchase Order > Info > C Save > S A',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT: '[M] Com > Purchase Order > Info > C Save > E Q A',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT: '[M] Com > Purchase Order > Info > C Update > S A',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE: '[M] Com > Purchase Order > Dl Modal > C Close',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Com > Purchase Order > Dl Modal > Send As > C Excel',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Com > Purchase Order > Dl Modal > Send As > C CSV',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Com > Purchase Order > Dl Modal > Dl As > C Excel',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Com > Purchase Order > Dl Modal > Dl As > C CSV',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_DOWNLOAD: '[M] Com > Purchase Order > C Dl',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_ADD_PO: '[M] Com > Purchase Order > C Add PO',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_START_DATE: '[M] Com > Purchase Order > C Start Date',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_END_DATE: '[M] Com > Purchase Order > C End Date',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PO_TOADY: '[M] Com > Purchase Order > C PO Today',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_PENDING_PO: '[M] Com > Purchase Order > C Pending PO',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_COMPLETED_PO: '[M] Com > Purchase Order > C Completed PO',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_CANCELLED_PO: '[M] Com > Purchase Order > C Cancelled PO',
    MODULE_COMPOSITE_PURCHASE_ORDER_C_SUMMARY: '[M] Com > Purchase Order > C Summary',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_BACK: '[M] Com > Purchase Order > Info > C Back',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE: '[M] Com > Purchase Order > Info > C Update',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE: '[M] Com > Purchase Order > Info > C Save',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND: '[M] Com > Purchase Order > Info > C Save And Send',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON: '[M] Com > Purchase Order > Info > C Purchase Order Id Icon',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON: '[M] Com > Purchase Order > Info > C Est Arrival Icon',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT: '[M] Com > Purchase Order > Info > Items Ordered > C Add Product Slot',

    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER: '[M] Com > Purchase Order > Info > DD Supplier',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE: '[M] Com > Purchase Order > Info > DD Target Store',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME: '[M] Com > Purchase Order > Info > Items Ordered > DD Product Name',

    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY: '[M] Com > Purchase Order > Info > Items Ordered > TB Ordered Qty',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY: '[M] Com > Purchase Order > Info > Items Ordered > TB Received Qty',
    MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Com > Purchase Order > Dl Modal > TB Email Address',
    MODULE_COMPOSITE_PURCHASE_ORDER_TB_SEARCH: '[M] Com > Purchase Order > TB Search',
    MODULE_COMPOSITE_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID: '[M] Com > Purchase Order > Info > TB Purchase Order Id',

    // Composite - Stock Transfer
    MODULE_COMPOSITE_STOCK_TRANSFER_C_LOGO: '[M] Com > Stock Transfer > C Logo',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_PROFILE: '[M] Com > Stock Transfer > C Profile',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: '[M] Com > Stock Transfer > Dl Modal > Dl As > C Excel > S A',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: '[M] Com > Stock Transfer > Dl Modal > Dl As > C CSV > S A',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_ITEM: '[M] Com > Stock Transfer > C Item',
    MODULE_COMPOSITE_STOCK_TRANSFER_ITEM_C_EXPORT: '[M] Com > Stock Transfer > Item > C Export',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE: '[M] Com > Stock Transfer > Info > Stock To Transfer > C Delete',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT: '[M] Com > Stock Transfer > Info > C Save > Create S A',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: '[M] Com > Stock Transfer > Info > C Save > Update S A',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE: '[M] Com > Stock Transfer > Dl Modal > C Close',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Com > Stock Transfer > Dl Modal > Send As > C Excel',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Com > Stock Transfer > Dl Modal > Send As > C CSV',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Com > Stock Transfer > Dl Modal > Dl As > C Excel',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Com > Stock Transfer > Dl Modal > Dl As > C CSV',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_DOWNLOAD: '[M] Com > Stock Transfer > C Dl',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_STOCK_TRANSFER: '[M] Com > Stock Transfer > C Stock Transfer',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_START_DATE: '[M] Com > Stock Transfer > C Start Date',
    MODULE_COMPOSITE_STOCK_TRANSFER_C_END_DATE: '[M] Com > Stock Transfer > C End Date',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_BACK: '[M] Com > Stock Transfer > Info > C Back',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED: '[M] Com > Stock Transfer > Edit > C Mark Completed',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_C_SAVE: '[M] Com > Stock Transfer > Info > C Save',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_ID_C_EDIT: '[M] Com > Stock Transfer > Info > ID > C Edit',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR: '[M] Com > Stock Transfer > Info > Shipped Date > C Calendar',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT: '[M] Com > Stock Transfer > Info > Stock To Transfer > C Add Product Slot',

    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_DD_SOURCE_STORE: '[M] Com > Stock Transfer > Info > DD Source Store',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE: '[M] Com > Stock Transfer > Info > DD Destination Store',
    MODULE_COMPOSITE_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME: '[M] Com > Stock Transfer > Info > Stock To Transfer > DD Product Name',

    MODULE_COMPOSITE_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Com > Stock Transfer > New > Stock To Transfer > TB Transfer Qty',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY: '[M] Com > Stock Transfer > Edit > Stock To Transfer > TB Transfer Qty',
    MODULE_COMPOSITE_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Com > Stock Transfer > Dl Modal > TB Email Address',
    MODULE_COMPOSITE_STOCK_TRANSFER_TB_SEARCH: '[M] Com > Stock Transfer > TB Search',
    MODULE_COMPOSITE_STOCK_TRANSFER_EDIT_TB_ID: '[M] Com > Stock Transfer > Edit > TB Search',

    // Composite - Stock Take
    MODULE_COMPOSITE_STOCK_TAKE_C_LOGO: '[M] Com > Stock Take > C Logo',
    MODULE_COMPOSITE_STOCK_TAKE_C_PROFILE: '[M] Com > Stock Take > C Profile',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT: '[M] Com > Stock Take > Dl Modal > Dl As > C Excel > S A',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT: '[M] Com > Stock Take > Dl Modal > Dl As > C CSV > S A',
    MODULE_COMPOSITE_STOCK_TAKE_C_ITEM: '[M] Com > Stock Take > C Item',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_C_CLIPBOARD: '[M] Com > Stock Take > Info > Item > C Clipbaord',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE_CREATE_SUCCESS_ALERT: '[M] Com > Stock Take > Info > C Save > Create S A',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE_UPDATE_SUCCESS_ALERT: '[M] Com > Stock Take > Info > C Save > Update S A',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_C_CLOSE: '[M] Com > Stock Take > Dl Modal > C Close',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Com > Stock Take > Dl Modal > Send As > C Excel',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Com > Stock Take > Dl Modal > Send As > C CSV',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL: '[M] Com > Stock Take > Dl Modal > Dl As > C Excel',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV: '[M] Com > Stock Take > Dl Modal > Dl As > C CSV',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_CLOSE: '[M] Com > Stock Take > Info > Item > Clipboard > Remarks Modal > C Close',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_C_DONE: '[M] Com > Stock Take > Info > Item > Clipboard > Remarks Modal > C Done',
    MODULE_COMPOSITE_STOCK_TAKE_C_DOWNLOAD: '[M] Com > Stock Take > C Dl',
    MODULE_COMPOSITE_STOCK_TAKE_C_STOCK_TAKE: '[M] Com > Stock Take > C Stock Take',
    MODULE_COMPOSITE_STOCK_TAKE_C_START_DATE: '[M] Com > Stock Take > C Start Date',
    MODULE_COMPOSITE_STOCK_TAKE_C_END_DATE: '[M] Com > Stock Take > C End Date',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_BACK: '[M] Com > Stock Take > Info > C Back',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_SAVE: '[M] Com > Stock Take > Info > C Save',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_C_COMPLETE: '[M] Com > Stock Take > Info > C Complete',
    MODULE_COMPOSITE_STOCK_TAKE_EDIT_C_CANCEL: '[M] Com > Stock Take > Edit > C Cancel',
    MODULE_COMPOSITE_STOCK_TAKE_EDIT_C_CANCEL_SUCCESS_ALERT: '[M] Com > Stock Take > Edit > C Cancel > S A',
    MODULE_COMPOSITE_STOCK_TAKE_NEW_ID_C_EDIT: '[M] Com > Stock Take > New > ID > C Edit',

    MODULE_COMPOSITE_STOCK_TAKE_INFO_DD_TARGET_SUPPLIER: '[M] Com > Stock Take > Info > DD Target Supplier',

    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_TB_COUNTED: '[M] Com > Stock Take > Info > Item > TB Counted',
    MODULE_COMPOSITE_STOCK_TAKE_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Com > Stock Take > Dl Modal > TB Email Address',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_ITEM_CLIPBOARD_REMARKS_MODAL_TB_REMARKS: '[M] Com > Stock Take > Info > Item > Clipboard > Remarks Modal > TB Remarks',
    MODULE_COMPOSITE_STOCK_TAKE_TB_SEARCH: '[M] Com > Stock Take > TB Search',
    MODULE_COMPOSITE_STOCK_TAKE_NEW_TB_ID: '[M] Com > Stock Take > New > TB ID',
    MODULE_COMPOSITE_STOCK_TAKE_INFO_TB_STOCK_TAKE_SUMMARY: '[M] Com > Stock Take > Info > TB Stock Take Summary',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Voucher - Voucher List
    MODULE_VOUCHER_VOUCHER_LIST_C_LOGO: '[M] VHR > VHR List > C Logo',
    MODULE_VOUCHER_VOUCHER_LIST_C_PROFILE: '[M] VHR > VHR List > C Profile',
    MODULE_VOUCHER_VOUCHER_LIST_C_ITEM: '[M] VHR > VHR List > C Item',
    MODULE_VOUCHER_VOUCHER_LIST_ACTIVE_ITEM_C_TRASH: '[M] VHR > VHR List > Active > Item > C Trash',
    MODULE_VOUCHER_VOUCHER_LIST_ACTIVE_ITEM_TRASH_ALERT_C_YES: '[M] VHR > VHR List > Active > Item > Trash > A > C Yes',
    MODULE_VOUCHER_VOUCHER_LIST_EXPIRED_ITEM_COPY: '[M] VHR > VHR List > Expired > Item > C Copy',
    MODULE_VOUCHER_VOUCHER_LIST_EXPIRED_ITEM_COPY_ALERT_C_YES: '[M] VHR > VHR List > Expired > Item > Copy > A > C Yes',
    MODULE_VOUCHER_VOUCHER_LIST_C_VOUCHER: '[M] VHR > VHR List > C Voucher',
    MODULE_VOUCHER_VOUCHER_LIST_C_ACTIVE: '[M] VHR > VHR List > C Active',
    MODULE_VOUCHER_VOUCHER_LIST_C_EXPIRED: '[M] VHR > VHR List > C Expired',
    MODULE_VOUCHER_VOUCHER_LIST_C_PREVIOUS_PAGE: '[M] VHR > VHR List > C Previous Page',
    MODULE_VOUCHER_VOUCHER_LIST_C_NEXT_PAGE: '[M] VHR > VHR List > C Next Page',

    MODULE_VOUCHER_VOUCHER_LIST_TB_SEARCH: '[M] VHR > VHR List > TB Search',
    MODULE_VOUCHER_VOUCHER_LIST_TB_PAGE_NUMBER: '[M] VHR > VHR List > TB Page Number',

    // Voucher - Add Voucher
    MODULE_VOUCHER_ADD_VOUCHER_C_LOGO: '[M] VHR > Add VHR > C Logo',
    MODULE_VOUCHER_ADD_VOUCHER_C_PROFILE: '[M] VHR > Add VHR > C Profile',
    MODULE_VOUCHER_ADD_VOUCHER_SAVE_CREATE_ALERT_C_OK: '[M] VHR > Add VHR > Save > Create A > C Ok',
    MODULE_VOUCHER_ADD_VOUCHER_SAVE_UPDATE_ALERT_C_OK: '[M] VHR > Add VHR > Save > Update A > C Ok',
    MODULE_VOUCHER_ADD_VOUCHER_C_BACK: '[M] VHR > Add VHR > C Back',
    MODULE_VOUCHER_ADD_VOUCHER_C_SAVE: '[M] VHR > Add VHR > C Save',
    MODULE_VOUCHER_ADD_VOUCHER_C_UPLOAD_IMAGE: '[M] VHR > Add VHR > C Upload Image',
    MODULE_VOUCHER_ADD_VOUCHER_CLAIM_VOUCHER_LINK_C_COPY: '[M] VHR > Add VHR > Claim Voucher Link > C Copy',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_DATE_C_START_DATE: '[M] VHR > Add VHR > Voucher Date > C Start Date',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_DATE_C_END_DATE: '[M] VHR > Add VHR > Voucher Date > C End Date',
    MODULE_VOUCHER_ADD_VOUCHER_APPLY_TO_CHANNEL_C_CHECKMARK: '[M] VHR > Add VHR > Apply to Channel > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_APPLY_TO_OUTLETS_C_CHECKMARK: '[M] VHR > Add VHR > Apply to Outlets > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_TIME_C_START_TIME: '[M] VHR > Add VHR > Voucher Time > C Start Time',
    MODULE_VOUCHER_ADD_VOUCHER_VOUCHER_TIME_C_END_TIME: '[M] VHR > Add VHR > Voucher Time > C End Time',
    MODULE_VOUCHER_ADD_VOUCHER_EFFECTIVE_OPTIONS_C_CHECKMARK: '[M] VHR > Add VHR > Effective Options > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_TARGET_SEGMENTS_C_CHECKMARK: '[M] VHR > Add VHR > Target Segments > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_IS_ONLINE_C_CHECKMARK: '[M] VHR > Add VHR > Is Online > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_FREE_TO_CLAIM_C_CHECKMARK: '[M] VHR > Add VHR > Free To Claim > C Checkmark',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_PRODUCT_C_PLUS: '[M] VHR > Add VHR > Criteria > Product > C Plus',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_PRODUCT_C_MINUS: '[M] VHR > Add VHR > Criteria > Product > C Minus',
    MODULE_VOUCHER_ADD_VOUCHER_CRITERIA_FREE_TAKEAWAY_C_CHECKBOX: '[M] VHR > Add VHR > Criteria > Free Takeaway > C Checkbox',

    MODULE_VOUCHER_ADD_VOUCHER_DD_VOUCHER_CODE_FORMAT: '[M] VHR > Add VHR > DD Voucher Code Format',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_TO_CHANNEL: '[M] VHR > Add VHR > DD Apply To Channel',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_DISCOUNT_PER: '[M] VHR > Add VHR > DD Apply Discount Per',
    MODULE_VOUCHER_ADD_VOUCHER_DD_APPLY_TO_OUTLETS: '[M] VHR > Add VHR > DD Apply To Outlets',
    MODULE_VOUCHER_ADD_VOUCHER_DD_EFFECTIVE_OPTIONS: '[M] VHR > Add VHR > DD Effective Options',
    MODULE_VOUCHER_ADD_VOUCHER_DD_TARGET_SEGMENTS: '[M] VHR > Add VHR > DD Target Segments',
    MODULE_VOUCHER_ADD_VOUCHER_DD_VOUCHER_TYPE: '[M] VHR > Add VHR > DD Voucher Type',
    MODULE_VOUCHER_ADD_VOUCHER_DD_INFO_TO_COLLECTED: '[M] VHR > Add VHR > DD Info To Collected',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_BUY_DD_VARIATION: '[M] VHR > Add VHR > Type Bundle > Criteria > Buy > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_BUY_DD_PRODUCT: '[M] VHR > Add VHR > Type Bundle > Criteria > Buy > DD Production',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_GET_DD_VARIATION: '[M] VHR > Add VHR > Type Bundle > Criteria > Get > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_GET_DD_PRODUCT: '[M] VHR > Add VHR > Type Bundle > Criteria > Get > DD Production',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_VARIATION: '[M] VHR > Add VHR > Type Override Existing Price > Criteria > Product > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_PRODUCT: '[M] VHR > Add VHR > Type Override Existing Price > Criteria > Product > DD Product',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_APPLY_TO_DD_VARIATION: '[M] VHR > Add VHR > Type Free Item > Criteria > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_APPLY_TO_DD_PRODUCT: '[M] VHR > Add VHR > Type Free Item > Criteria > DD Product',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: '[M] VHR > Add VHR > Type Take Amount Off > Criteria > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: '[M] VHR > Add VHR > Type Take Amount Off > Criteria > DD Product',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: '[M] VHR > Add VHR > Type Take Percent Off > Criteria > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: '[M] VHR > Add VHR > Type Take Percent Off > Criteria > DD Product',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_DD_VARIATION: '[M] VHR > Add VHR > Type Delivery > Criteria > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_DD_PRODUCT: '[M] VHR > Add VHR > Type Delivery > Criteria > DD Product',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_DD_VARIATION: '[M] VHR > Add VHR > Type Takeaway > Criteria > DD Variation',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_DD_PRODUCT: '[M] VHR > Add VHR > Type Takeaway > Criteria > DD Product',

    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_DESCRIPTION: '[M] VHR > Add VHR > TB VHR Description',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_NAME: '[M] VHR > Add VHR > TB VHR Name',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_GENERIC_CODE: '[M] VHR > Add VHR > TB VHR Generic Code',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_QUANTITY: '[M] VHR > Add VHR > TB VHR Quantity',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_TERMS: '[M] VHR > Add VHR > TB VHR Terms',
    MODULE_VOUCHER_ADD_VOUCHER_TB_VOUCHER_MAX_CLAIM_PER_USER: '[M] VHR > Add VHR > TB VHR Max Claim Per User',
    MODULE_VOUCHER_ADD_VOUCHER_TB_POINTS_REQUIRED: '[M] VHR > Add VHR > TB VHR Points Required',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_BUY: '[M] VHR > Add VHR > Type Bundle > Criteria > TB Buy',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_GET: '[M] VHR > Add VHR > Type Bundle > Criteria > TB Get',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_ITEMS_FOR: '[M] VHR > Add VHR > Type Bundle > Criteria > TB Items For',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_BUNDLE_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] VHR > Add VHR > Type Bundle > Criteria > TB Min Spend Amount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] VHR > Add VHR > Type Override Existing Price > Criteria > TB Min Spend Amount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_TB_RM: '[M] VHR > Add VHR > Type Override Existing Price > Criteria Product > TB RM',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] VHR > Add VHR > Type Free Item > Criteria > TB Min Spend Amount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_FREE_ITEM_CRITERIA_TB_GET_QUANTITY: '[M] VHR > Add VHR > Type Free Item > Criteria > TB Get Quantity',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_AMOUNT_OFF: '[M] VHR > Add VHR > Type Take Amount Off > Criteria > TB Amount Off',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] VHR > Add VHR > Type Take Amount Off > Criteria > TB Min Spend Amount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_QUANTITY: '[M] VHR > Add VHR > Type Take Amount Off > Criteria > TB Min Quantity',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_DISCOUNT: '[M] VHR > Add VHR > Type Take Percent Off > Criteria > TB Discount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] VHR > Add VHR > Type Take Percent Off > Criteria > TB Min Spend Amount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_QUANTITY: '[M] VHR > Add VHR > Type Take Percent Off > Criteria > TB Min Quantity',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_TB_DISCOUNT: '[M] VHR > Add VHR > Type Delivery > Criteria > TB Discount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_DELIVERY_CRITERIA_TB_ORDER_ABOVE: '[M] VHR > Add VHR > Type Delivery > Criteria > TB Order Above',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_FREE_TAKEAWAY: '[M] VHR > Add VHR > Type Takeaway > Criteria > TB Free Takeaway',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_DISCOUNT: '[M] VHR > Add VHR > Type Takeaway > Criteria > TB Discount',
    MODULE_VOUCHER_ADD_VOUCHER_TYPE_TAKEAWAY_CRITERIA_TB_ORDER_ABOVE: '[M] VHR > Add VHR > Type Takeaway > Criteria > TB Order Above',
    MODULE_VOUCHER_ADD_VOUCHER_NOTIFICAITON_TB_MESSAGE: '[M] VHR > Add VHR > Notification > TB Message',

    // Voucher - Voucher Report
    MODULE_VOUCHER_VOUCHER_REPORT_C_LOGO: '[M] VHR > VHR Report > C Logo',
    MODULE_VOUCHER_VOUCHER_REPORT_C_PROFILE: '[M] VHR > VHR Report > C Profile',
    MODULE_VOUCHER_VOUCHER_REPORT_C_ITEM: '[M] VHR > VHR Report > C Item',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_ITEM: '[M] VHR > VHR Report > Item Details > C Item',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_ITEM_C_PROFILE: '[M] VHR > VHR Report > Item Details > Item > C Profile',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_C_CLOSE: '[M] VHR > VHR Report > Dl Modal > C Close',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] VHR > VHR Report > Dl Modal > Send As > C Excel',
    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] VHR > VHR Report > Dl Modal > Send As > C CSV',
    MODULE_VOUCHER_VOUCHER_REPORT_C_DOWNLOAD: '[M] VHR > VHR Report > C Dl',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SUMMARY: '[M] VHR > VHR Report > Item Details > C Summary',
    MODULE_VOUCHER_VOUCHER_REPORT_C_START_DATE: '[M] VHR > VHR Report > Start Date',
    MODULE_VOUCHER_VOUCHER_REPORT_C_END_DATE: '[M] VHR > VHR Report > End Date',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_TITLE: '[M] VHR > VHR Report > Sort Title',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_TYPE: '[M] VHR > VHR Report > Sort Type',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DURATION: '[M] VHR > VHR Report > Sort Duration',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_CLAIMED: '[M] VHR > VHR Report > Sort Claimed',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_USED: '[M] VHR > VHR Report > Sort Used',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DISC_RM: '[M] VHR > VHR Report > Sort Disc RM',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_DISC_PERCENT: '[M] VHR > VHR Report > Sort Disc Percent',
    MODULE_VOUCHER_VOUCHER_REPORT_C_SORT_SALES: '[M] VHR > VHR Report > Sort Sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DATETIME: '[M] VHR > VHR Report > Item Details > C Sort Date Time',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SALES: '[M] VHR > VHR Report > Item Details > C Sort Sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SALES_HELP: '[M] VHR > VHR Report > Item Details > C Sales Help',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DISC_RM: '[M] VHR > VHR Report > Item Details > C Sort Disc RM',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_DISC_PERCENT: '[M] VHR > VHR Report > Item Details > C Sort Disc Percent',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SERVICE_CHARGE: '[M] VHR > VHR Report > Item Details > C Sort Service Charge',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_TAX: '[M] VHR > VHR Report > Item Details > C Sort Tax',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_SALES_RETURN: '[M] VHR > VHR Report > Item Details > C Sort Sales Return',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_SORT_NET_SALES: '[M] VHR > VHR Report > Item Details > C Sort Net Sales',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_NET_SALES_HELP: '[M] VHR > VHR Report > Item Details > C Net Sales Help',
    MODULE_VOUCHER_VOUCHER_REPORT_C_PREVIOUS_PAGE: '[M] VHR > VHR Report > C Previous Page',
    MODULE_VOUCHER_VOUCHER_REPORT_C_NEXT_PAGE: '[M] VHR > VHR Report > C Next Page',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_PREVIOUS_PAGE: '[[M] VHR > VHR Report > Item Details > C Previous Page',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_C_NEXT_PAGE: '[M] VHR > VHR Report > Item Details > C Next Page',

    MODULE_VOUCHER_VOUCHER_REPORT_DD_ITEMS_SHOWED: '[M] VHR > VHR Report > DD Items Showed',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_DD_ITEMS_SHOWED: '[M] VHR > VHR Report > Item Details > DD Items Showed',

    MODULE_VOUCHER_VOUCHER_REPORT_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] VHR > VHR Report > Dl Modal > TB Email Address',
    MODULE_VOUCHER_VOUCHER_REPORT_TB_PAGE_NUMBER: '[M] VHR > VHR Report > TB Page Number',
    MODULE_VOUCHER_VOUCHER_REPORT_ITEM_DETAILS_TB_PAGE_NUMBER: '[M] VHR > VHR Report > Item Details > TB Page Number',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Promotion - Promotion List
    MODULE_PROMOTION_PROMOTION_LIST_C_LOGO: '[M] Promo > Promo List > C Logo',
    MODULE_PROMOTION_PROMOTION_LIST_C_PROFILE: '[M] Promo > Promo List > C Profile',
    MODULE_PROMOTION_PROMOTION_LIST_SWIPE_ITEM_C_BLAST: '[M] Promo > Promo List > Swipe Item > C Blast',
    MODULE_PROMOTION_PROMOTION_LIST_SWIPE_ITEM_C_DUPLICATE: '[M] Promo > Promo List > Swipe Item > C Duplicate',
    MODULE_PROMOTION_PROMOTION_LIST_C_ITEM: '[M] Promo > Promo List > C Item',
    MODULE_PROMOTION_PROMOTION_LIST_C_PROMOTION: '[M] Promo > Promo List > C Promo',
    MODULE_PROMOTION_PROMOTION_LIST_C_PREVIOUS_PAGE: '[M] Promo > Promo List > C Previous Page',
    MODULE_PROMOTION_PROMOTION_LIST_C_NEXT_PAGE: '[M] Promo > Promo List > C Next Page',

    MODULE_PROMOTION_PROMOTION_LIST_TB_SEARCH: '[M] Promo > Promo List > TB Search',
    MODULE_PROMOTION_PROMOTION_LIST_TB_PAGE_NUMBER: '[M] Promo > Promo List > TB Page Number',

    // Promotion - Add Promotion
    MODULE_PROMOTION_ADD_PROMOTION_DELETE_ALERT_C_YES: '[M] Promo > Add Promo > Delete > A > C Yes',
    MODULE_PROMOTION_ADD_PROMOTION_DELETE_SUCCESS_ALERT_C_OK: '[M] Promo > Add Promo > Delete > S A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_BACK_ALERT_C_YES: '[M] Promo > Add Promo > Back > A > C Yes',
    MODULE_PROMOTION_ADD_PROMOTION_C_LOGO: '[M] Promo > Add Promo > C Logo',
    MODULE_PROMOTION_ADD_PROMOTION_C_PROFILE: '[M] Promo > Add Promo > C Profile',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_ALERT_C_PUSH: '[M] Promo > Add Promo > Publish > A > C Push',
    MODULE_PROMOTION_ADD_PROMOTION_SAVE_CREATE_ALERT_C_OK: '[M] Promo > Add Promo > Save > Create A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_CREATE_SCHEDULE_ALERT_C_OK: '[M] Promo > Add Promo > Pub > Create Schedule A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_CREATE_PUSHED_ALERT_C_OK: '[M] Promo > Add Promo > Pub > Create Pushed A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_SAVE_UPDATE_ALERT_C_OK: '[M] Promo > Add Promo > Save > Update A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_UPDATE_SCHEDULE_ALERT_C_OK: '[M] Promo > Add Promo > Pub > Update Sche A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_PUBLISH_UPDATE_PUSHED_ALERT_C_OK: '[M] Promo > Add Promo > Pub > Update Pushed A > C Ok',
    MODULE_PROMOTION_ADD_PROMOTION_C_BACK: '[M] Promo > Add Promo > C Back',
    MODULE_PROMOTION_ADD_PROMOTION_C_SAVE: '[M] Promo > Add Promo > C Save',
    MODULE_PROMOTION_ADD_PROMOTION_C_DELETE: '[M] Promo > Add Promo > C Delete',
    MODULE_PROMOTION_ADD_PROMOTION_C_PUBLISH: '[M] Promo > Add Promo > C Publish',
    MODULE_PROMOTION_ADD_PROMOTION_C_UPLOAD_IMAGE: '[M] Promo > Add Promo > C Upload Image',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_DATE_C_START_DATE: '[M] Promo > Add Promo > Promo Date > C Start Date',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_DATE_C_END_DATE: '[M] Promo > Add Promo > Promo Date > C End Date',
    MODULE_PROMOTION_ADD_PROMOTION_APPLY_TO_CHANNEL_C_CHECKMARK: '[M] Promo > Add Promo > Apply to Channel > C Checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_APPLY_TO_OUTLETS_C_CHECKMARK: '[M] Promo > Add Promo > Apply to Outlets > C Checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_TIME_C_START_TIME: '[M] Promo > Add Promo > Promo Time > C Start Time',
    MODULE_PROMOTION_ADD_PROMOTION_PROMOTION_TIME_C_END_TIME: '[M] Promo > Add Promo > Promo Time > C End Time',
    MODULE_PROMOTION_ADD_PROMOTION_EFFECTIVE_OPTIONS_C_CHECKMARK: '[M] Promo > Add Promo > Effective Options > C Checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_TARGET_SEGMENTS_C_CHECKMARK: '[M] Promo > Add Promo > Target Segments > C Checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_USE_PROMO_CODE_C_CHECKMARK: '[M] Promo > Add Promo > Use Promo Code > C Checkmark',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_C_PLUS: '[M] Promo > Add Promo > Type Override Existing Price > Criteria > Product > C Plus',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_C_MINUS: '[M] Promo > Add Promo > Type Override Existing Price > Criteria > Product > C Minus',

    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_TO_CHANNEL: '[M] Promo > Add Promo > DD Apply To Channel',
    MODULE_PROMOTION_ADD_PROMOTION_DD_PROMOTION_TYPE: '[M] Promo > Add Promo > DD Promo Type',
    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_DISCOUNT_PER: '[M] Promo > Add Promo > DD Apply Discount Per',
    MODULE_PROMOTION_ADD_PROMOTION_DD_APPLY_TO_OUTLETS: '[M] Promo > Add Promo > DD Apply To Outlets',
    MODULE_PROMOTION_ADD_PROMOTION_DD_EFFECTIVE_OPTIONS: '[M] Promo > Add Promo > DD Effective Options',
    MODULE_PROMOTION_ADD_PROMOTION_DD_TARGET_SEGMENTS: '[M] Promo > Add Promo > DD Target Segments',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_BUY_DD_VARIATION: '[M] Promo > Add Promo > Type Bundle > Criteria > Buy > DD Variation',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_BUY_DD_PRODUCT: '[M] Promo > Add Promo > Type Bundle > Criteria > Buy > DD Production',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_GET_DD_VARIATION: '[M] Promo > Add Promo > Type Bundle > Criteria > Get > DD Variation',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_GET_DD_PRODUCT: '[M] Promo > Add Promo > Type Bundle > Criteria > Get > DD Production',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_VARIATION: '[M] Promo > Add Promo > Type Override Existing Price > Criteria > Product > DD Variation',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_DD_PRODUCT: '[M] Promo > Add Promo > Type Override Existing Price > Criteria > Product > DD Product',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > DD Variation',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > DD Product',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_VARIATION: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > DD Variation',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_APPLY_TO_DD_PRODUCT: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > DD Product',

    MODULE_PROMOTION_ADD_PROMOTION_TB_CAMPAIGN_DESCRIPTION: '[M] Promo > Add Promo > TB Campaign Description',
    MODULE_PROMOTION_ADD_PROMOTION_TB_CAMPAIGN_NAME: '[M] Promo > Add Promo > TB Campaign Name',
    MODULE_PROMOTION_ADD_PROMOTION_TB_CUSTOMER_LIMIT: '[M] Promo > Add Promo > TB Customer Limit',
    MODULE_PROMOTION_ADD_PROMOTION_TB_PROMO_CODE: '[M] Promo > Add Promo > TB Promo Code',
    MODULE_PROMOTION_ADD_PROMOTION_TB_USAGE_LIMIT: '[M] Promo > Add Promo > TB Usage Limit',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_BUY: '[M] Promo > Add Promo > Type Bundle > Criteria > TB Buy',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_GET: '[M] Promo > Add Promo > Type Bundle > Criteria > TB Get',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_ITEMS_FOR: '[M] Promo > Add Promo > Type Bundle > Criteria > TB Items For',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_BUNDLE_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] Promo > Add Promo > Type Bundle > Criteria > TB Min Spend Amount',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_OVERRIDE_EXISTING_PRICE_CRITERIA_PRODUCT_TB_RM: '[M] Promo > Add Promo > Type Override Existing Price > Criteria Product > TB RM',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_AMOUNT_OFF: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > TB Amount Off',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > TB Min Spend Amount',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MIN_QUANTITY: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > TB Min Quantity',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_AMOUNT_OFF_CRITERIA_TB_MAX_QUANTITY: '[M] Promo > Add Promo > Type Take Amount Off > Criteria > TB Max Quantity',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_DISCOUNT: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > TB Discount',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_SPEND_AMOUNT: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > TB Min Spend Amount',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MIN_QUANTITY: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > TB Min Quantity',
    MODULE_PROMOTION_ADD_PROMOTION_TYPE_TAKE_PERCENT_OFF_CRITERIA_TB_MAX_QUANTITY: '[M] Promo > Add Promo > Type Take Percent Off > Criteria > TB Max Quantity',

    // Promotion - Promotion Report
    MODULE_PROMOTION_PROMOTION_REPORT_C_LOGO: '[M] Promo > Promo Report > C Logo',
    MODULE_PROMOTION_PROMOTION_REPORT_C_PROFILE: '[M] Promo > Promo Report > C Profile',
    MODULE_PROMOTION_PROMOTION_REPORT_C_ITEM: '[M] Promo > Promo Report > C Item',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_ITEM: '[M] Promo > Promo Report > Item Details > C Item',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_ITEM_C_PROFILE: '[M] Promo > Promo Report > Item Details > Item > C Profile',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_C_CLOSE: '[M] Promo > Promo Report > Dl Modal > C Close',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_SEND_AS_C_EXCEL: '[M] Promo > Promo Report > Dl Modal > Send As > C Excel',
    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_SEND_AS_C_CSV: '[M] Promo > Promo Report > Dl Modal > Send As > C CSV',
    MODULE_PROMOTION_PROMOTION_REPORT_C_DOWNLOAD: '[M] Promo > Promo Report > C Dl',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SUMMARY: '[M] Promo > Promo Report > Item Details > C Summary',
    MODULE_PROMOTION_PROMOTION_REPORT_C_START_DATE: '[M] Promo > Promo Report > Start Date',
    MODULE_PROMOTION_PROMOTION_REPORT_C_END_DATE: '[M] Promo > Promo Report > End Date',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_TITLE: '[M] Promo > Promo Report > Sort Title',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_TYPE: '[M] Promo > Promo Report > Sort Type',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DURATION: '[M] Promo > Promo Report > Sort Duration',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_ORDER: '[M] Promo > Promo Report > Sort Order',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DISC_RM: '[M] Promo > Promo Report > Sort Disc RM',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_DISC_PERCENT: '[M] Promo > Promo Report > Sort Disc Percent',
    MODULE_PROMOTION_PROMOTION_REPORT_C_SORT_SALES: '[M] Promo > Promo Report > Sort Sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DATETIME: '[M] Promo > Promo Report > Item Details > C Sort Date Time',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SALES: '[M] Promo > Promo Report > Item Details > C Sort Sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SALES_HELP: '[M] Promo > Promo Report > Item Details > C Sales Help',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DISC_RM: '[M] Promo > Promo Report > Item Details > C Sort Disc RM',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_DISC_PERCENT: '[M] Promo > Promo Report > Item Details > C Sort Disc Percent',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SERVICE_CHARGE: '[M] Promo > Promo Report > Item Details > C Sort Service Charge',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_TAX: '[M] Promo > Promo Report > Item Details > C Sort Tax',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_SALES_RETURN: '[M] Promo > Promo Report > Item Details > C Sort Sales Return',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_SORT_NET_SALES: '[M] Promo > Promo Report > Item Details > C Sort Net Sales',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_NET_SALES_HELP: '[M] Promo > Promo Report > Item Details > C Net Sales Help',
    MODULE_PROMOTION_PROMOTION_REPORT_C_PREVIOUS_PAGE: '[M] Promo > Promo Report > C Previous Page',
    MODULE_PROMOTION_PROMOTION_REPORT_C_NEXT_PAGE: '[M] Promo > Promo Report > C Next Page',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_PREVIOUS_PAGE: '[[M] Promo > Promo Report > Item Details > C Previous Page',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_C_NEXT_PAGE: '[M] Promo > Promo Report > Item Details > C Next Page',

    MODULE_PROMOTION_PROMOTION_REPORT_DD_ITEMS_SHOWED: '[M] Promo > Promo Report > DD Items Showed',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_DD_ITEMS_SHOWED: '[M] Promo > Promo Report > Item Details > DD Items Showed',

    MODULE_PROMOTION_PROMOTION_REPORT_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS: '[M] Promo > Promo Report > Dl Modal > TB Email Address',
    MODULE_PROMOTION_PROMOTION_REPORT_TB_PAGE_NUMBER: '[M] Promo > Promo Report > TB Page Number',
    MODULE_PROMOTION_PROMOTION_REPORT_ITEM_DETAILS_TB_PAGE_NUMBER: '[M] Promo > Promo Report > Item Details > TB Page Number',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // Upselling - Upselling List
    MODULE_UPSELLING_UPSELLING_LIST_C_LOGO: '[M] Upsell > Upsell List > C Logo',
    MODULE_UPSELLING_UPSELLING_LIST_C_PROFILE: '[M] Upsell > Upsell List > C Profile',
    MODULE_UPSELLING_UPSELLING_LIST_SWIPE_ITEM_C_BLAST: '[M] Upsell > Upsell List > Swipe Item > C Blast',
    MODULE_UPSELLING_UPSELLING_LIST_SWIPE_ITEM_C_DUPLICATE: '[M] Upsell > Upsell List > Swipe Item > C Duplicate',
    MODULE_UPSELLING_UPSELLING_LIST_C_ITEM: '[M] Upsell > Upsell List > C Item',
    MODULE_UPSELLING_UPSELLING_LIST_C_UPSELLING: '[M] Upsell > Upsell List > C Upsell',
    MODULE_UPSELLING_UPSELLING_LIST_C_PREVIOUS_PAGE: '[M] Upsell > Upsell List > C Previous Page',
    MODULE_UPSELLING_UPSELLING_LIST_C_NEXT_PAGE: '[M] Upsell > Upsell List > C Next Page',

    MODULE_UPSELLING_UPSELLING_LIST_TB_SEARCH: '[M] Upsell > Upsell List > TB Search',
    MODULE_UPSELLING_UPSELLING_LIST_TB_PAGE_NUMBER: '[M] Upsell > Upsell List > TB Page Number',

    // Upselling - Upselling Campaign
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_LOGO: '[M] Upsell > Upsell Campaign > C Logo',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_PROFILE: '[M] Upsell > Upsell Campaign > C Profile',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_CREATE_ALERT_C_OK: '[M] Upsell > Upsell Campaign > Save > Create A > C Ok',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_SAVE_UPDATE_ALERT_C_OK: '[M] Upsell > Upsell Campaign > Save > Update A > C Ok',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_C_MINUS: '[M] Upsell > Upsell Campaign > Product List > Item > C Minus',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_BACK: '[M] Upsell > Upsell Campaign > C Back',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_SAVE: '[M] Upsell > Upsell Campaign > C Save',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_C_UPLOAD_IMAGE: '[M] Upsell > Upsell Campaign > C Upload Image',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_START_DATE: '[M] Upsell > Upsell Campaign > Upsell Date > C Start Date',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_DATE_C_END_DATE: '[M] Upsell > Upsell Campaign > Upsell Date > C End Date',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_START_TIME: '[M] Upsell > Upsell Campaign > Upsell Time > C Start Time',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PROMO_TIME_C_END_TIME: '[M] Upsell > Upsell Campaign > Upsell Time > C End Time',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_EFFECTIVE_OPTIONS_C_CHECKMARK: '[M] Upsell > Upsell Campaign > Effective Options > C Checkmark',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_C_PLUS: '[M] Upsell > Upsell Campaign > Product List > C Plus',

    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_NAME: '[M] Upsell > Upsell Campaign > Product List > Item > DD Name',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TAGS: '[M] Upsell > Upsell Campaign > Product List > Item > DD Tags',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_DD_TYPE: '[M] Upsell > Upsell Campaign > Product List > Item > DD Type',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_AVAILABILITY: '[M] Upsell > Upsell Campaign > DD Availability',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_APPLY_TO_OUTLETS: '[M] Upsell > Upsell Campaign > DD Apply To Outlets',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_EFFECTIVE_OPTIONS: '[M] Upsell > Upsell Campaign > DD Effective Options',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_TARGET_SEGMENTS: '[M] Upsell > Upsell Campaign > DD Target Segments',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_DD_SECTION_TO_SHOW: '[M] Upsell > Upsell Campaign > DD Section To Show',

    MODULE_UPSELLING_UPSELLING_CAMPAIGN_PRODUCT_LIST_ITEM_TB_UPSELL_PRICE: '[M] Upsell > Upsell Campaign > Product List > Item > TB Upsell Price',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_DESCRIPTION: '[M] Upsell > Upsell Campaign > TB Campaign Description',
    MODULE_UPSELLING_UPSELLING_CAMPAIGN_TB_CAMPAIGN_NAME: '[M] Upsell > Upsell Campaign > TB Campaign Name',

    ////////////////////////////////////////////////////////////////////////////////////////////////////
};
