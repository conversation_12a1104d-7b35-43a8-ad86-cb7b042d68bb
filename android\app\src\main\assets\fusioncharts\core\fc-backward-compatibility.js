import _FusionCharts from"./";import{raiseError}from"@fusioncharts/core/src/event-api";import DataStore from"../datastore";import{between,equals,less,lessEquals,greater,greaterEquals,filter,select,groupBy,pipe,sort,pivot}from"../datastore/operators";import{aggregatorStore}from"../datastore/aggregators/index.js";import{before,after,parseDate,formatDate,duration,DatetimeUnits,Weekdays}from"../utils";_FusionCharts.DataStore=DataStore;_FusionCharts.DataStore.Operators={between:between,equals:equals,less:less,lessEquals:lessEquals,greater:greater,greaterEquals:greaterEquals,filter:filter,select:select,groupBy:groupBy,pipe:pipe,sort:sort,pivot:pivot};_FusionCharts.Utils={duration:duration,before:before,after:after,parseDate:parseDate,formatDate:formatDate,DatetimeUnits:DatetimeUnits,Weekdays:Weekdays};_FusionCharts.DataStore.Aggregators={aggregatorStore:aggregatorStore};const interpreters=["type","id","width","height","debugMode","registerWithJS","backgroundColor","scaleMode","lang","detectFlashVersion","autoInstallRedirect"],parseCommands=(obj,args)=>{var i,l;for(i=0,l=interpreters.length;i<l;i++){if(typeof args[i]==="object"){Object.assign(obj,args[i])}else{obj[interpreters[i]]=args[i]}}return obj};function FusionCharts(optionsArg){let options=optionsArg;if(this instanceof FusionCharts){if(!(arguments.length===1&&typeof options==="object")){options=parseCommands({},arguments)}return new(Function.prototype.bind.apply(_FusionCharts,[null].concat(options)))}if(arguments.length===1&&options instanceof Array&&options[0]==="private"){return _FusionCharts.register("module",options)}if(arguments.length===1&&typeof options==="string"){return _FusionCharts.getChartFromId(options)}raiseError(FusionCharts,"25081840","run","",new SyntaxError('Use the "new" keyword while creating a new FusionCharts object'))}Object.getOwnPropertyNames(_FusionCharts).filter(prop=>Object.getOwnPropertyDescriptor(_FusionCharts,prop).writable===true).forEach(prop=>{FusionCharts[prop]=_FusionCharts[prop]});export default FusionCharts;