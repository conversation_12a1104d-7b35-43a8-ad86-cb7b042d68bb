import { Text } from "react-native-fast-text";
import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
    createRef,
    useCallback,
} from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    Dimensions,
    TouchableOpacity,
    Switch,
    Modal,
    KeyboardAvoidingView,
    Platform,
    useWindowDimensions,
    InteractionManager,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { NativeViewGestureHandler, TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import {
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import {
    ORDER_TYPE,
    PAYMENT_CHANNEL_NAME_PARSED,
    DINE_IN_SORT_FIELD_TYPE,
    DINE_IN_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_COMPARE_OPERATOR,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE_SHORT,
    KD_PRINT_VARIATION,
    KD_ITEM_STATUS,
    PRIVILEGES_NAME,
    ROLE_TYPE,
    ORDER_TYPE_SUB,
    COURIER_INFO_DICT,
} from '../constant/common';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { FlashList } from "@shopify/flash-list";
import { openCashDrawer, printDocket, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder } from '../util/printer';
// import { storageMMKV } from '../util/storageMMKV';
import EvilIcons from 'react-native-vector-icons/EvilIcons';

const OdOrderList = React.memo((props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    const [controller1, setController1] = useState({});
    const [refArray, setRefArray] = useState([]);

    const [dineInOrders, setDineInOrders] = useState([]);

    const [search, setSearch] = useState('');

    const userOrders = OutletStore.useState((s) => s.userOrders);

    const privileges_state = UserStore.useState((s) => s.privileges);

    const [privileges, setPrivileges] = useState([]);
    const role = UserStore.useState((s) => s.role);
    const pinNo = UserStore.useState(s => s.pinNo);

    useEffect(() => {
        const useEffectCallback = async () => {
            // admin full access

            // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
            // const enteredPinNo = storageMMKV.getString('enteredPinNo');
            const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

            if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
                setPrivileges([
                    "EMPLOYEES",
                    "OPERATION",
                    "PRODUCT",
                    "INVENTORY",
                    "INVENTORY_COMPOSITE",
                    "DOCKET",
                    "VOUCHER",
                    "PROMOTION",
                    "CRM",
                    "LOYALTY",
                    "TRANSACTIONS",
                    "REPORT",
                    "RESERVATIONS",

                    // for action
                    'REFUND_ORDER',

                    'SETTINGS',

                    'QUEUE',

                    'OPEN_CASH_DRAWER',

                    'KDS',

                    'UPSELLING',

                    // for Kitchen

                    'REJECT_ITEM',
                    'CANCEL_ORDER',
                    //'REFUND_tORDER',

                    'MANUAL_DISCOUNT',
                ]);
            } else {
                setPrivileges(privileges_state || []);
            }
        };

        useEffectCallback();
    }, [role, privileges_state, pinNo]);

    useEffect(() => {
        if (isMounted) {
            InteractionManager.runAfterInteractions(() => {
                setDineInOrders(
                    userOrders.filter((order) => ((order.orderType === ORDER_TYPE.DINEIN) ||
                        (order.orderType !== ORDER_TYPE.DINEIN && order.orderTypeSub !== ORDER_TYPE_SUB.OTHER_DELIVERY) ||
                        (order.orderType === ORDER_TYPE.PICKUP && order.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY))
                        &&
                        (order.completedDate === null)
                        // (
                        //     order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                        //     ||
                        //     order.orderStatus === USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
                        //     ||
                        //     order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING
                        //     ||
                        //     order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                        //     ||
                        //     order.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED
                        // )
                    ),
                );

            });
        }
    }, [userOrders, isMounted]);

    useEffect(() => {
        setRefArray((ref) =>
            Array(dineInOrders.length)
                .fill()
                .map((_, i) => ref[i] || createRef()),
        );
    }, [dineInOrders.length]);


    const sortDineIn = (dataList, dineInSortFieldType) => {
        var dataListTemp = [...dataList];

        return dataListTemp;
    };

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false,
    // });

    const expandOrderFunc = (param) => {
        if (!expandViewDict[param.uniqueId]) {
            // return setState({ expandOrder: true }), param.expandOrder = true;
            // setExpandOrder(true);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: true,
            });
        } else {
            // return setState({ expandOrder: false }), param.expandOrder = false;
            // setExpandOrder(false);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: false,
            });
        }
    };

    const filterOrders = (param) => {
        if (param.value == 0) {
            // All orders
            setDineInOrders(
                userOrders.filter((order) => ((order.orderType === ORDER_TYPE.DINEIN) ||
                    (order.orderType !== ORDER_TYPE.DINEIN && order.orderTypeSub !== ORDER_TYPE_SUB.OTHER_DELIVERY) ||
                    (order.orderType === ORDER_TYPE.PICKUP && order.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY)) &&
                    (order.completedDate === null)),
            );
        }

        if (param.value == 1) {
            //Dine in
            setDineInOrders(
                userOrders.filter(
                    (order) => (order.orderType === ORDER_TYPE.DINEIN) &&
                        (order.completedDate === null)
                ),
            );
        }

        if (param.value == 2) {
            //Takeaway
            setDineInOrders(
                userOrders.filter(
                    (order) => (order.orderType !== ORDER_TYPE.DINEIN && order.orderTypeSub !== ORDER_TYPE_SUB.OTHER_DELIVERY) &&
                        (order.completedDate === null)
                ),
            );
        }

        if (param.value == 3) {
            //Other d
            setDineInOrders(
                userOrders.filter(
                    (order) => (order.orderType === ORDER_TYPE.PICKUP && order.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) &&
                        (order.completedDate === null)
                ),
            );
        }
    };

    const renderOrder = ({ item, index }) => {

        var diffMinute = moment().diff(item.createdAt, 'minute');
        var diffHour = moment().diff(item.createdAt, 'hour');
        var diffDay = moment().diff(item.createdAt, 'day')
        let diffTime;

        if (diffMinute < 60) {
            diffTime = diffMinute > 1 ? `${moment().diff(item.createdAt, 'minute')} minutes ago` : `${moment().diff(item.createdAt, 'minute')} minute ago`
        }
        else if (diffHour < 24 && diffMinute > 60) {
            diffTime = diffHour > 1 ? `${moment().diff(item.createdAt, 'hour')} hours ago` : `${moment().diff(item.createdAt, 'hour')} hour ago`
        }
        else if (diffHour > 24 && diffMinute > 60) {
            diffTime = diffDay > 1 ? `${moment().diff(item.createdAt, 'day')} days ago` : `${moment().diff(item.createdAt, 'day')} day ago`
        }
        // if (moment().diff(moment(item.createdAt), 'minute') < 60) {
        //     setHandleTime(`${moment().diff(moment(item.createdAt), 'minute')} minutes ago`)
        // }
        // else if ((moment().diff(moment(item.createdAt), 'minute') > 60 && moment().diff(moment(item.createdAt), 'hour') < 24)) {
        //     setHandleTime(`${moment().diff(moment(item.createdAt), 'hour')} hours ago`)
        // }
        // else {
        //     setHandleTime(`${moment().diff(moment(item.createdAt), 'day')} days ago`)
        // }


        ///////////////////////////

        return (
            <View
                style={{
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                        width: 0,
                        height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 1,
                    elevation: 3,

                    ...(item.isReservationOrder && {
                        elevation: 2,
                        borderRadius: 5,
                        backgroundColor: 'white',
                        borderWidth: 2,
                        borderColor: Colors.tabCyan,
                    }),
                }}>
                <TouchableOpacity
                    onPress={() => {
                        // expandOrderFunc(item);

                        CommonStore.update(
                            (s) => {
                                s.selectedOrder = item
                            },
                        );
                    }}>
                    <View style={{ elevation: 1, borderRadius: 5, backgroundColor: 'white' }}>
                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'column',
                                height:
                                    item.appType === 'WEB_ORDER' && windowWidth <= 1133 ? windowHeight * 0.26
                                        : windowWidth <= 1133 ? windowHeight * 0.26
                                            : windowHeight * 0.22,
                                alignItems: 'flex-start',
                                borderColor: Colors.fieldtTxtColor,
                                borderBottomWidth: StyleSheet.hairlineWidth,
                                paddingLeft: 20,
                                paddingTop: 20,
                                paddingBottom: 10,
                            }}>
                            <View style={{ flexDirection: 'row', height: '65%' }}>
                                <View
                                    style={{
                                        marginHorizontal: 1,
                                        width: (item.orderType !== ORDER_TYPE.DINEIN ? '86%' : (item.appType === 'WEB_ORDER' ? '60%' : '73%')),
                                        alignItems: 'flex-start',
                                        flexDirection: 'column',
                                    }}>
                                    <Text
                                        style={[
                                            {
                                                color: Colors.blackColor,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        #{item.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}
                                        {item.orderId}
                                    </Text>
                                    {item.isPrioritizedOrder ?
                                        <View style={{ marginTop: 5, borderWidth: 1, paddingHorizontal: 15, paddingVertical: 5, borderRadius: 5, backgroundColor: Colors.highlightColor, borderColor: Colors.primaryColor }}>
                                            <Text
                                                style={[
                                                    {
                                                        color: Colors.blackColor,
                                                        fontSize: 16,
                                                    },
                                                    switchMerchant
                                                        ? {
                                                            fontSize: 10,
                                                        }
                                                        : {},
                                                ]}>
                                                prioritized
                                            </Text>
                                        </View>
                                        : null}
                                </View>

                                <View
                                    style={{
                                        marginRight: '3%',
                                        width: '10%',
                                        alignItems: 'center',
                                        zIndex: 0,
                                    }}>
                                    <View
                                        style={{
                                            width: 60,
                                            height: 65,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            borderColor: Colors.highlightColor,
                                            backgroundColor: Colors.highlightColor,
                                            padding: 10,
                                        }}>
                                        {item.orderType === ORDER_TYPE.DINEIN ?
                                            <Image
                                                style={[
                                                    {
                                                        width: 30,
                                                        height: 30,
                                                        marginLeft: 0,
                                                    },
                                                    switchMerchant
                                                        ? {
                                                            width: windowWidth * 0.03,
                                                            height: windowHeight * 0.05,
                                                        }
                                                        : {},
                                                ]}
                                                resizeMode="contain"
                                                source={require('../assets/image/dineinGrey.png')}
                                            />
                                            :
                                            <>
                                                {item.courierId ? (
                                                    <>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                //left: 30,
                                                            }}>
                                                            <Image
                                                                style={[
                                                                    {
                                                                        width:
                                                                            windowWidth <= 1133
                                                                                ? 50
                                                                                : 60,
                                                                        height:
                                                                            windowWidth <= 1133
                                                                                ? 50
                                                                                : 60,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width:
                                                                                windowWidth * 0.03,
                                                                            height:
                                                                                windowHeight * 0.05,
                                                                        }
                                                                        : {},
                                                                ]}
                                                                source={COURIER_INFO_DICT[item.courierCode].img}
                                                            />
                                                        </View>
                                                    </>
                                                ) : (
                                                    <View
                                                        style={{
                                                            width: 60,
                                                            height: 60,
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                        }}>
                                                        <Image
                                                            style={[
                                                                {
                                                                    width: 30,
                                                                    height: 30,
                                                                    //marginLeft: 0,
                                                                    //left: 30,
                                                                },
                                                                switchMerchant
                                                                    ? {
                                                                        width: windowWidth * 0.03,
                                                                        height:
                                                                            windowHeight * 0.05,
                                                                        top: windowHeight * 0.009,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            resizeMode="contain"
                                                            source={require('../assets/image/TakeawayBlack.png')}
                                                        />
                                                    </View>
                                                )

                                                }
                                            </>
                                        }
                                    </View>
                                </View>
                                {item.appType === 'WEB_ORDER' ?
                                    <View
                                        style={{
                                            marginRight: '3%',
                                            width: '10%',
                                            alignItems: 'center',
                                            zIndex: 0,
                                        }}>

                                        <View
                                            style={{
                                                width: 60,
                                                height: 65,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderWidth: 1,
                                                borderRadius: 5,
                                                borderColor: Colors.highlightColor,
                                                backgroundColor: Colors.highlightColor,
                                                padding: 10,
                                            }}>
                                            <Ionicons name={'qr-code'} size={switchMerchant ? 25 : 30} />
                                        </View>


                                    </View>
                                    : null}
                                {item.orderType === ORDER_TYPE.DINEIN ?
                                    <View
                                        style={{
                                            marginRight: '3%',
                                            width: '10%',
                                            alignItems: 'center',
                                        }}>
                                        <View
                                            style={[
                                                {
                                                    width: 60,
                                                    height: 65,
                                                    borderWidth: 1,
                                                    borderRadius: 5,
                                                    borderColor: Colors.highlightColor,
                                                    backgroundColor: Colors.highlightColor,
                                                    padding: 10,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }
                                            ]}>
                                            <Text style={[{
                                                color: Colors.fontDark,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},]}
                                                numberOfLines={2}>
                                                {item.tableCode ? item.tableCode.slice(0, 6) : ''}
                                            </Text>
                                        </View>
                                    </View> : null}
                            </View>
                            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', height: '35%' }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', width: '40%' }}>
                                    <EvilIcons
                                        name="clock"
                                        size={30}
                                        color={Colors.primaryColor}
                                        style={{ marginRight: 5 }}
                                    />
                                    <Text
                                        style={[
                                            {
                                                color: Colors.blackColor,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        {diffTime}
                                    </Text>
                                </View>
                                <View style={{ flexDirection: 'row', alignItems: 'center', width: '20%' }}>
                                    <Ionicons
                                        name="fast-food-outline"
                                        size={24}
                                        color={Colors.primaryColor}
                                        style={{ marginRight: 5 }}

                                    />
                                    <Text
                                        style={[
                                            {
                                                color: Colors.blackColor,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        {item.cartItems.length > 1 ? `${item.cartItems.length} items` : `${item.cartItems.length} item`}
                                    </Text>
                                </View>
                                <View style={{ flexDirection: 'row', alignItems: 'center', width: '30%' }}>
                                    <MaterialCommunityIcons
                                        name="cash"
                                        size={30}
                                        style={{ color: item.paymentDetails && item.paymentDetails.channel || item.completedDate !== null ? Colors.primaryColor : Colors.tabRed, arginRight: 5 }}
                                    />
                                    <Text
                                        style={[
                                            {
                                                color: Colors.blackColor,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                }
                                                : {},
                                        ]}>
                                        {(item.paymentDetails && item.paymentDetails.channel || item.completedDate !== null) ? 'Paid' : 'Unpaid'}
                                    </Text>
                                </View>

                            </View>
                        </View>

                    </View>
                </TouchableOpacity>
            </View>
        );
    };


    return (
        // <View style={styles.container}>
        //   {renderModal(currToPrioritizeOrder)}

        (<UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                ]}>
                <View style={{ flex: 1, paddingVertical: 20 }}>

                    <View style={{ flexDirection: 'row', paddingHorizontal: 15, }}>
                        <View
                            style={[
                                {
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingLeft: 10,
                                    borderBottomLeftRadius: 5,
                                    borderTopLeftRadius: 5,
                                    height: 40,
                                    borderWidth: 1,
                                    borderRightWidth: 0,
                                    borderColor: '#E5E5E5',
                                    backgroundColor: 'white',
                                    // marginRight: 15,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                    // left: windowWidth * 0.01,
                                },
                                switchMerchant
                                    ? {
                                        height: 35,
                                        paddingLeft: windowWidth * 0.002,
                                        // top: windowHeight * -0.075,
                                        // right: windowHeight * 0.08,
                                    }
                                    : {},
                            ]}>
                            <Text
                                style={[
                                    {
                                        fontSize: 16,
                                        paddingRight: Platform.OS == 'ios' ? 20 : 20,
                                        borderColor: Colors.fieldtTxtColor,
                                        fontFamily: 'NunitoSans-Bold',
                                    },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                            // borderWidth: 1
                                            // paddingLeft: '10%',
                                            // left: windowWidth * 0.005,
                                            paddingLeft: '1%',
                                        }
                                        : {},
                                ]}>
                                Filter
                            </Text>
                        </View>
                        <DropDownPicker
                            // controller={instance => controller1 = instance}
                            controller={(instance) => setController1(instance)}
                            arrowColor={Colors.primaryColor}
                            arrowSize={switchMerchant ? 13 : 23}
                            arrowStyle={[
                                { fontWeight: 'bold' },
                                switchMerchant
                                    ? {
                                        top: windowHeight * -0.005,
                                        height: '180%',
                                        // borderWidth: 1
                                    }
                                    : {},
                            ]}
                            labelStyle={[
                                { fontFamily: 'NunitoSans-Regular' },
                                switchMerchant
                                    ? {
                                        fontSize: 10,
                                    }
                                    : {},
                            ]}
                            itemStyle={[
                                { justifyContent: 'flex-start', marginLeft: 5 },
                                switchMerchant
                                    ? {
                                        fontSize: 10,
                                    }
                                    : {},
                            ]}
                            placeholderStyle={[
                                { color: 'black' },
                                switchMerchant
                                    ? {
                                        fontSize: 10,
                                    }
                                    : {},
                            ]}
                            style={[
                                {
                                    width: 140,
                                    borderWidth: 0,
                                    height: 40,
                                    paddingHorizontal: 5,
                                    paddingVertical: 0,
                                    borderBottomRightRadius: 5,
                                    borderTopRightRadius: 5,
                                    borderTopLeftRadius: 0,
                                    borderBottomLeftRadius: 0,
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    borderLeftWidth: 0,
                                    paddingLeft: 2,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                    marginRight: 15,
                                },
                                switchMerchant
                                    ? {
                                        fontSize: 10,
                                        width: windowWidth * 0.152,
                                    }
                                    : {},
                            ]}
                            dropDownStyle={{
                                paddingLeft: 2,
                                right: 15,
                                width: switchMerchant
                                    ? windowWidth * 0.152
                                    : 140,
                            }}
                            items={[
                                { label: 'All Orders', value: 0 },
                                { label: 'Dine in', value: 1 },
                                { label: 'Takeaway', value: 2 },
                                { label: 'Other d', value: 3 },
                            ]}
                            placeholder={'All Orders'}
                            onChangeItem={(selectedFilter) => {
                                // setState({ filter: selectedFilter }),
                                filterOrders(selectedFilter);
                            }}
                        />

                        <View style={{ flex: 1, }}>
                            <View
                                style={[
                                    {
                                        width: '100%',
                                        height: 40,
                                        backgroundColor: 'white',
                                        borderRadius: 5,
                                        flexDirection: 'row',
                                        alignContent: 'center',
                                        alignItems: 'center',

                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                    },
                                    switchMerchant
                                        ? {
                                            height: 35,
                                            width: 200,
                                            // top: windowHeight * -0.075,
                                        }
                                        : {},
                                ]}>
                                {switchMerchant ? (
                                    <Icon
                                        name="search"
                                        size={10}
                                        color={Colors.primaryColor}
                                        style={{ marginLeft: 15 }}
                                    />
                                ) : (
                                    <Icon
                                        name="search"
                                        size={18}
                                        color={Colors.primaryColor}
                                        style={{ marginLeft: 15 }}
                                    />
                                )}
                                {switchMerchant ? (
                                    <TextInput
                                        // editable={!loading}
                                        style={[
                                            {
                                                width: 220,
                                                fontSize: 15,
                                                fontFamily: 'NunitoSans-Regular',
                                                paddingLeft: 5,
                                                height: 45,
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 10,
                                                    // borderWidth:1,
                                                    width: 180,
                                                    height: 40,
                                                }
                                                : {},
                                        ]}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        onChangeText={(text) => {
                                            setSearch(text);
                                        }}
                                        value={search}
                                    />
                                ) : (
                                    <TextInput
                                        // editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={[
                                            {
                                                width: 220,
                                                fontSize: 15,
                                                fontFamily: 'NunitoSans-Regular',
                                                paddingLeft: 5,
                                                height: 45,
                                            },
                                            switchMerchant
                                                ? {
                                                    fontSize: 8,
                                                    // borderWidth:1,
                                                    width: windowWidth * 0.17,
                                                    height: windowHeight * 0.18,
                                                }
                                                : {},
                                        ]}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        onChangeText={(text) => {
                                            setSearch(text);
                                        }}
                                        value={search}
                                    />
                                )}
                            </View>
                        </View>
                    </View>
                    <View style={{ borderTopWidth: StyleSheet.hairlineWidth, top: 20, opacity: 0.3 }} />
                    <View
                        style={[
                            { marginTop: 30, marginBottom: 100, zIndex: -1, },
                            switchMerchant
                                ? {
                                    marginTop: windowHeight * -0.05,
                                    marginBottom: windowHeight * 0.15,
                                }
                                : {},
                        ]}>
                        <FlatList
                            // data={order}
                            data={sortDineIn(dineInOrders)
                                .slice(0)
                                .sort((a, b) => {
                                    return b.orderDate - a.orderDate;
                                })
                                .sort((a, b) => {
                                    return b.isPrioritizedOrder - a.isPrioritizedOrder;
                                })
                                .filter((item) => {
                                    if (search !== '') {
                                        const searchLowerCase = search.toLowerCase();

                                        if ((item.tableCode ? item.tableCode : '').toLowerCase().includes(searchLowerCase)) {
                                            return true;
                                        }

                                        if (item.orderId.toLowerCase().includes(searchLowerCase)) {
                                            return true;
                                        }

                                        return false;
                                    } else {
                                        return true;
                                    }
                                })}
                            renderItem={renderOrder}
                            keyExtractor={(item, index) => String(index)}
                            showsVerticalScrollIndicator={false}
                        />
                    </View>
                </View>
            </View>
        </UserIdleWrapper>)
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.whiteColor,
        flexDirection: 'row',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        backgroundColor: Colors.whiteColor,
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#c4c4c4',
        borderRadius: 20,
        paddingVertical: 20,
        paddingHorizontal: 20,
        marginRight: 10,
        marginBottom: 10,
        width: (Dimensions.get('window').width - 150) / 2,
    },
    tablebox: {
        backgroundColor: Colors.whiteColor,
        shadowColor: '#c4c4c4',
        shadowOffset: {
            width: 8,
            height: 8,
        },
        shadowOpacity: 0.55,
        shadowRadius: 10.32,
        width: 100,
        height: 100,
        marginRight: 25,
        borderRadius: 10,
        marginBottom: 30,
        marginTop: 10,
        marginHorizontal: 20,
    },
});

export default OdOrderList;
