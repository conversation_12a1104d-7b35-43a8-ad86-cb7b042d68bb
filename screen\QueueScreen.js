import { Text } from "react-native-fast-text";
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Modal,
  Alert,
  Dimensions,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Entypo from 'react-native-vector-icons/Entypo';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import Switch from 'react-native-switch-pro';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { ScrollView, TextInput } from 'react-native-gesture-handler';
import QRCode from 'react-native-qrcode-svg';
import { qrUrl } from '../constant/env';
import AIcon from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import Feather from 'react-native-vector-icons/Feather';
import { USER_QUEUE_STATUS, USER_QUEUE_STATUS_PARSED, QUEUE_QR_SALT, APP_ENV } from '../constant/common';
import {
  QUEUE_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  QUEUE_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import APILocal from '../util/apiLocalReplacers';
import { useNetInfo } from "@react-native-community/netinfo";
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import Hashids from 'hashids';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { qrUrlUat } from "../constant/env";
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNPickerSelect from 'react-native-picker-select';

const hashids = new Hashids(QUEUE_QR_SALT);

const QueueScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [visible, setVisible] = useState(false);

  const [addQueueModal, setAddQueueModal] = useState(false);
  const [confirmQueueModal, setConfirmQueueModal] = useState(false);

  const [qrCodeTakeawayRef, setQRCodeTakeawayRef] = useState(null);
  const [qrTakeaway, setQrTakeaway] = useState(false);

  const [isSavingQRCode, setIsSavingQRCode] = useState(false);

  const [selectedQueue, setSelectedQueue] = useState(null);
  const [queueCustomerName, setQueueCustomerName] = useState('');
  const [queuePhone, setQueuePhone] = useState('');
  const [queuePax, setQueuePax] = useState('');
  const [queueCategory, setQueueCategory] = useState(null);
  const queueConfig = OutletStore.useState((s) => s.queueConfig);
  const [open, setOpen] = useState(false);

  const [displayChat, setDisplayChat] = useState(null);
  const [displayRemarkModal, setDisplayRemarkModal] = useState(null);

  const queueCategoryDropdownList = useMemo(() => {
    if (!queueConfig || !queueConfig.queueSizeOptions || !queueConfig.queueSizeOptions.length) {
      return [
        { label: '1 - 2 Pax', value: '1 - 2 Pax' },
        { label: '3 - 4 Pax', value: '3 - 4 Pax' },
        { label: '5 - 8 Pax', value: '5 - 8 Pax' },
        { label: '8 Pax & Above', value: '8 Pax & Above' }
      ];
    }
    return queueConfig.queueSizeOptions.map((item) => ({ label: item.name, value: item.name }));
  }, [queueConfig]);

  const [table, setTable] = useState([]);
  const [queue, setQueue] = useState([]);
  const [newReservationStatus, setNewReservationStatus] = useState(false);

  const [filterType, setFilterType] = useState(0);

  const [userQueues, setUserQueues] = useState([]);

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const userQueuesRaw = OutletStore.useState((s) => s.userQueues);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const userName = UserStore.useState((s) => s.name);
  const userId = UserStore.useState((s) => s.firebaseUid);
  const userEmail = UserStore.useState((s) => s.email);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const merchantId = UserStore.useState((s) => s.merchantId);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  // var cId = hashids.encodeHex(
  //   currOutlet.uniqueId.replaceAll('-', ''),
  // );

  const printQRTakeaway = async () => {
    // disconnectPrinter(printer); // no need anymore

    await printTableQR({
      tableQRUrl: `${global.appEnv === APP_ENV.LIVE ? qrUrl : qrUrlUat}outlet/${currOutlet ? currOutlet.subdomain : ''}/queue/`,
    });

    Alert.alert('Info', 'QR code has been added to printer queue');
  };

  const currTableQRUrl = OutletStore.useState((s) => s.currTableQRUrl);

  //New UX
  const [controller1, setController1] = useState({});

  ////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var userQueuesTemp = [];

      if (filterType == 0) {
        //Prioritize
        userQueuesTemp = userQueuesRaw;
      }
      if (filterType == 1) {
        //Prioritize
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.PENDING,
        );
      }
      if (filterType == 2) {
        //orderid
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.ACCEPTED,
        );
      }
      if (filterType == 3) {
        //date time
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.SEATED,
        );
      }
      if (filterType == 4) {
        //Name
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.SERVED,
        );
      }
      if (filterType == 5) {
        //Waiting Time
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.CANCELED,
        );
      }
      if (filterType == 6) {
        //Waiting Time
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.NO_SHOW,
        );
      }
      if (filterType == 7) {
        //Waiting Time
        userQueuesTemp = userQueuesRaw.filter(
          (order) => order.status === USER_QUEUE_STATUS.NOTIFIED,
        );
      }

      setUserQueues(userQueuesTemp);
    });
  }, [filterType, userQueuesRaw]);

  const getOutlet = () => {
    // ApiClient.GET(API.outlet + User.getOutletId())
    //   .then((result) => {
    //     setState({ switchState: result[0].queueStatus });
    //   })
    //   .catch((err) => {
    //     // console.log(err);
    //   });
  };

  const switchQueueStatus = (value) => {
    // self.onButtonClickHandler();

    var body = {
      // outletId: User.getOutletId()
      outletId: currOutlet.uniqueId,
      queueStatus: value,
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.switchQueueStatus, body)
    APILocal.switchQueueStatus({ body })
      .then((result) => {
        if (result.status) {
          setTimeout(() => {
            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }, 1000);
        }
        // if (result.queueStatus === true) {
        //     // Alert.alert("Queue is closed now")
        //     return self.setState({ switchState: false })

        // } else if (result.queueStatus === false) {
        //     //   Alert.alert("Queue is open now")
        //     return self.setState({ switchState: true })
        // }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  var closeAllSwipeable = () => { };

  ////////////////////////////////////////////////////////////////////////
  // Queue Operation
  const servedForQueue = (queueId) => {
    let body = {
      queueId,
      status: USER_QUEUE_STATUS.SERVED,
    };

    ApiClient.PATCH(API.updateQueueV2, body)
      .then((result) => {
        if (result.status === 'success') {
          Alert.alert('Success', 'Queue has been updated');
          closeAllSwipeable();
        }
        else {
          console.log('error update queue', result);
          Alert.alert('Error', 'Failed to update queue');
        }
      })
      .catch((err) => {
        console.error('error update queue', err);
      });
  };

  const seatedForQueue = (queueId) => {
    let body = {
      queueId,
      status: USER_QUEUE_STATUS.SEATED,
    };

    ApiClient.PATCH(API.updateQueueV2, body)
      .then((result) => {
        if (result.status === 'success') {
          Alert.alert('Success', 'Queue has been updated');
          closeAllSwipeable();
        }
        else {
          console.log('error update queue', result);
          Alert.alert('Error', 'Failed to update queue');
        }
      })
      .catch((err) => {
        console.error('error update queue', err);
      });
  };

  const noShowQueue = (queueId) => {
    var body = {
      queueId,
      status: USER_QUEUE_STATUS.NO_SHOW,
    };

    ApiClient.PATCH(API.updateQueueV2, body)
      .then((result) => {
        if (result.status === 'success') {
          Alert.alert('Success', 'Queue has been updated');
          closeAllSwipeable();
        }
        else {
          console.log('error update queue', result);
          Alert.alert('Error', 'Failed to update queue');
        }
      })
      .catch((err) => {
        console.error('error update queue', err);
      });
  };

  const cancelQueue = (queueId) => {
    var body = {
      queueId,
      status: USER_QUEUE_STATUS.CANCELED,
    };

    ApiClient.PATCH(API.updateQueueV2, body)
      .then((result) => {
        if (result.status === 'success') {
          Alert.alert('Success', 'Queue has been updated');
          closeAllSwipeable();
        }
        else {
          console.log('error update queue', result);
          Alert.alert('Error', 'Failed to update queue');
        }
      })
      .catch((err) => {
        console.error('error update queue', err);
      });
  };

  const notifyForQueue = (queueId) => {
    let body = {
      queueId,
    };

    ApiClient.POST(API.notifyUserQueueV2, body)
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Queue has been notified');
        }
        else {
          console.log('error notify user', result);
          Alert.alert('Error', 'Failed to notify user');
        }
      })
      .catch((err) => {
        console.error('error notify user', err);
        Alert.alert('Error', 'Failed to notify user');
      });
  };

  const createUserQueueByMerchant = () => {
    if (
      queueCustomerName.length <= 0 ||
      queuePhone.length <= 0 ||
      queuePax.length <= 0 ||
      queueCategory == null
    ) {
      Alert.alert('Info', 'Please fill in the following fields:\n\nCustomer Name\nPhone No\nCapacity\nCategory');
      return;
    }

    let parsePax = parseInt(queuePax, 10);

    if (queueCategory === '1 to 2 pax' && (parsePax < 1 || parsePax > 2)) {
      Alert.alert('Error', 'Pax must be between 1-2 for this category');
      return;
    } else if (queueCategory === '3 to 4 pax' && (parsePax < 3 || parsePax > 4)) {
      Alert.alert('Error', 'Pax must be between 3-4 for this category');
      return;
    } else if (queueCategory === '5 to 6 pax' && (parsePax < 5 || parsePax > 6)) {
      Alert.alert('Error', 'Pax must be between 5-6 for this category');
      return;
    } else if (queueCategory === '7 pax or above' && parsePax < 7) {
      Alert.alert('Error', 'Pax must be 7 or above for this category');
      return;
    }

    CommonStore.update((s) => { s.isLoading = true });

    let body = {
      pax: parseInt(queuePax, 10),
      category: queueCategory,
      outletId: currOutlet.uniqueId,

      merchantId: currOutlet.merchantId,
      outletCover: currOutlet.cover,
      merchantLogo: merchantLogo,
      outletName: currOutlet.name,
      merchantName: merchantName,

      userName: queueCustomerName,
      userPhone: queuePhone,

      userIdAnonymous: uuidv4(),
    };

    // ApiClient.POST(API.registerQueue, body)
    APILocal.createUserQueueByMerchant({ body })
      .then((result) => {
        if (result.status === 'success') {
          closeAllSwipeable();
          Alert.alert('Success', 'Queue has been created');
        }
      })
      .catch((err) => {
        console.error('error register queue', err);
        Alert.alert('Error', 'Failed to create queue');
      })
      .finally(() => {
        CommonStore.update((s) => { s.isLoading = false });
      });
  };

  const updateUserQueueByMerchant = () => {
    if (
      queueCustomerName.length <= 0 ||
      queuePhone.length <= 0 ||
      queuePax.length <= 0 ||
      queueCategory == null
    ) {
      Alert.alert(
        'Info',
        'Please fill in the following fields:\n\nCustomer Name\nPhone No\nCapacity\nCategory',
      );
      return;
    }

    let parsePax = parseInt(queuePax, 10);

    if (queueCategory === '1 to 2 pax' && (parsePax < 1 || parsePax > 2)) {
      Alert.alert('Error', 'Pax must be between 1-2 for this category');
      return;
    } else if (queueCategory === '3 to 4 pax' && (parsePax < 3 || parsePax > 4)) {
      Alert.alert('Error', 'Pax must be between 3-4 for this category');
      return;
    } else if (queueCategory === '5 to 6 pax' && (parsePax < 5 || parsePax > 6)) {
      Alert.alert('Error', 'Pax must be between 5-6 for this category');
      return;
    } else if (queueCategory === '7 pax or above' && parsePax < 7) {
      Alert.alert('Error', 'Pax must be 7 or above for this category');
      return;
    }

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    let body = {
      uniqueId: selectedQueue.uniqueId,
      pax: parseInt(queuePax, 10),
      userName: queueCustomerName,
      userPhone: queuePhone,
      category: queueCategory,
    };

    ApiClient.PATCH(API.editQueueV2, body)
      .then((result) => {
        if (result.status === 'success') {
          Alert.alert('Success', 'Queue has been updated');
        }
        else {
          console.log('error update queue', result);
          Alert.alert('Error', 'Failed to update queue');
        }
      })
      .catch((err) => {
        console.error('error update queue', err);
      })
      .finally(() => {
        CommonStore.update((s) => {
          s.isLoading = false;
        });
        setAddQueueModal(false);
      });
  };

  ////////////////////////////////////////////////////////////////////////

  const [queueCount, setQueueCount] = useState(0);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      // console.log('USER_QUEUE_STATUS_PARSED.SEATED');
      // console.log(userQueues);

      const pendingQueue = userQueues.filter((order) => {
        if (order.status === USER_QUEUE_STATUS.PENDING) {
          return true;
        }
      })
      setQueueCount(pendingQueue.length);
      // userQueues.filter(order => order.status === USER_QUEUE_STATUS_PARSED.SEATED);
    });
  }, [userQueues]);

  const filterOrders = (param) => {
    if (param.value == 0) {
      // Pending
      userQueues.filter((order) => order.status === USER_QUEUE_STATUS.PENDING);
    }
    // if (param.value == 1) { //Accepted
    //   setQueueOrders(userQueues.filter(order => order.status === STATUS.ACCEPTED));
    // }

    // if (param.value == 2) { //Seated
    //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.SEATED));
    // }

    // if (param.value == 3) { //Served
    //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.SERVED));
    // }

    // if (param.value == 4) { //Rejected
    //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.REJECTED));
    // }
    // {USER_QUEUE_STATUS_PARSED[item.status]}
    // if (param.value == 5) { //No Show
    //   setSelectedQueue(selectedQueue.filter(order => order.orderType === STATUS.REJECTED));
    // }
  };

  const [sort, setSort] = useState('');
  const [search, setSearch] = useState('');

  //Start Here Sorting

  const sortOperationQueue = (dataList, queueSortFieldType) => {
    var dataListTemp = [...dataList];
    // console.log('dataList');
    // console.log(dataList);

    // console.log('queueSortFieldType');
    // console.log(queueSortFieldType);

    const queueSortFieldTypeValue =
      QUEUE_SORT_FIELD_TYPE_VALUE[queueSortFieldType];

    const queueSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[queueSortFieldType];

    //QUEUE_ID
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //NAME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //DATE_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //CAPACITY
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //WAITING_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    }

    //STATUS
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    return dataListTemp;
  };

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? "27%" : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Queue
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const rightAction = (item, index) => {
    return (
      <View
        style={[
          {
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            //width: '32%',
          },
          switchMerchant
            ? {
              // borderWidth: 1,

              // left: windowWidth * 0.08,
            }
            : {},
        ]}>
        <TouchableOpacity
          onPress={() => {
            const queueExist = userQueuesRaw.find(q => q.uniqueId === item.uniqueId);
            if (queueExist && queueExist.chat && queueExist.chat.length > 0) {
              setDisplayChat(item.uniqueId);
            } else {
              Alert.alert(
                'No Chat History',
                'There is no chat history available for this queue.',
                [{ text: 'OK' }]
              );
            }
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: Colors.fieldtTxtColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            width: 75,
          }}>
          {switchMerchant ? (
            <Ionicons
              name={'chatbubble-ellipses-outline'}
              size={10}
              color={Colors.whiteColor}
            />
          ) : (
            <Ionicons
              name={'chatbubble-ellipses-outline'}
              size={40}
              color={Colors.whiteColor}
            />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            Chat
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            notifyForQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.secondaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {switchMerchant ? (
            <MIcon
              name={'bell-ring-outline'}
              size={10}
              color={Colors.whiteColor}
            />
          ) : (
            <MIcon
              name={'bell-ring-outline'}
              size={40}
              color={Colors.whiteColor}
            />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            Notify
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            seatedForQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.primaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {switchMerchant ? (
            <MIcon name="seat-outline" size={10} color={Colors.whiteColor} />
          ) : (
            <MIcon name="seat-outline" size={40} color={Colors.whiteColor} />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            Seated
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            servedForQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: '#00B1E1',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {switchMerchant ? (
            <MIcon
              name="room-service-outline"
              size={10}
              color={Colors.whiteColor}
            />
          ) : (
            <MIcon
              name="room-service-outline"
              size={40}
              color={Colors.whiteColor}
            />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            Served
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            noShowQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.tabGrey,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {switchMerchant ? (
            <Feather
              name="alert-triangle"
              size={10}
              color={Colors.whiteColor}
            />
          ) : (
            <Feather
              name="alert-triangle"
              size={40}
              color={Colors.whiteColor}
            />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            No-Show
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            cancelQueue(item.uniqueId, item.userId, item.pax, item.number, item.outletName);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.tabRed,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {/* <MaterialCommunityIcons
            name="message-alert-outline"
            size={40}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }}
          /> */}
          {switchMerchant ? (
            <FontAwesome name="trash-o" size={10} color={Colors.whiteColor} />
          ) : (
            <FontAwesome name="trash-o" size={40} color={Colors.whiteColor} />
          )}

          <Text
            style={[
              {
                color: Colors.whiteColor,
                fontSize: 12,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
                width: '80%',
              },
              switchMerchant
                ? {
                  fontSize: 10,
                }
                : {},
            ]}>
            Reject
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderQueue = ({ item, index }) => {
    var dys = Math.floor(moment().diff(item.createdAt, 'hours') / 24);
    var hrs = Math.floor((moment().diff(item.createdAt, 'minutes') / 60) % 24);
    var mins = Math.floor(moment().diff(item.createdAt, 'minutes') % 60);

    var backgroundColor = '#00B1E1';
    if (item.status === USER_QUEUE_STATUS.SERVED) {
      backgroundColor = '#00B1E1';
    } else if (item.status === USER_QUEUE_STATUS.PENDING) {
      backgroundColor = Colors.secondaryColor;
    } else if (item.status === USER_QUEUE_STATUS.NO_SHOW) {
      backgroundColor = Colors.tabGrey;
    }

    if (item.status === USER_QUEUE_STATUS.SEATED) {
      backgroundColor = Colors.primaryColor;
    }

    if (item.status === USER_QUEUE_STATUS.NOTIFIED) {
      backgroundColor = Colors.secondaryColor;
    }

    return (
      <View
        style={{
          paddingVertical: 5,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,

          // backgroundColor: 'yellow',
        }}>
        <Swipeable
          renderRightActions={() => rightAction(item)}
          // ref={refArray[index]}
          onSwipeableWillOpen={() => { }}>
          <View
            style={[
              { elevation: 1, borderRadius: 7, backgroundColor: 'white' },
              switchMerchant
                ? {
                  // width: windowWidth * 0.8,
                }
                : {},
            ]}>
            {/* <View style={{ 
                        // opacity: item.isPrioritizedOrder ? 100 : 0, 
                        position: 'absolute', alignItems: 'center', width: '28.5%',}}>
            <View style={{ width: 80, backgroundColor: Colors.secondaryColor, height: 20, position: "absolute", zIndex: 6000, top: 0, right: 8, borderRadius: 5 }}>
                            <View style={{ flexDirection: "row", justifyContent: 'center', alignItems:'center', width: "100%" }}>
                                <Text style={{ color: Colors.whiteColor, fontSize: 10, paddingVertical:3}}>012-777 777</Text>
                            </View>
            </View>
          </View> */}
            <View
              style={[
                {
                  width: '100%',
                  flexDirection: 'row',
                  height: windowHeight * 0.1,
                  alignItems: 'center',
                  borderBottomColor: Colors.fieldtT,
                  // borderBottomWidth: expandViewDict[item.uniqueId] == true ? StyleSheet.hairlineWidth : null
                },
                switchMerchant
                  ? {
                    width: windowWidth * 0.82,
                    // height: windowHeight * 0.1,
                  }
                  : {},
              ]}>
              <View
                style={[
                  {
                    width: '13%',
                    marginLeft: '2.5%',
                    marginHorizontal: 0.5,
                    alignItems: 'flex-start',
                  },
                  switchMerchant
                    ? {
                      width: '14%',
                      left: '10%',
                    }
                    : {},
                ]}>
                {/* <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId}</Text> */}
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  #{item.number}
                </Text>
              </View>

              <View
                style={[
                  {
                    width: '16%',
                    flexDirection: 'row',
                    marginHorizontal: 0.5,
                    alignItems: 'flex-start',
                  },
                  switchMerchant
                    ? {
                      width: '17.1%',
                      // left: '10%'
                    }
                    : {},
                ]}>
                <View
                  style={{
                    flexDirection: 'row',
                    marginHorizontal: 0.5,
                    width: Platform.OS == 'ios' ? '60%' : '60%',
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'left',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {item.userName ? item.userName : '-'}
                  </Text>
                </View>

                {/* <View style={{ width: Platform.OS == 'ios' ? '19%' : '16%', flexDirection: 'row', marginHorizontal: 0.5, }}> */}
                {/* <View style={{ width: '100%', flexDirection: 'row', marginHorizontal: 0.5, justifyContent: 'center', alignItems: 'center', right: Platform.OS == 'ios' ? 555 : 660 }}>
                <TouchableOpacity onPress={() => {
                  setQueueCustomerName(item.userName ? item.userName : '');
                  setQueuePhone(item.userPhone ? item.userPhone : '');
                  setQueuePax(item.pax ? item.pax : 0);

                  setSelectedQueue(item);
                  setAddQueueModal(true);
                }}>
                  <FontAwesome5 name='edit' size={23} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View> */}
                {/* <View style={{ 
                    // opacity: item.isPrioritizedOrder ? 100 : 0, 
                    position: 'absolute', alignItems: 'center', width: '425%',}}>
                    <View style={{ width: 80, backgroundColor: Colors.secondaryColor, height: 20,   zIndex: 6000, bottom: 25, right: 8, borderRadius: 5 }}>
                      <View style={{ flexDirection: "row", justifyContent: 'center', alignItems:'center', width: "100%" }}>
                        <Text style={{ color: Colors.whiteColor, fontSize: 10, paddingVertical:3}}>{item.userPhone ? item.userPhone : 'N/A'}</Text>
                      </View>
                    </View>
                  </View> */}
                {/* </View> */}
              </View>

              <View
                style={[
                  {
                    width: '16%',
                    marginHorizontal: 0.5,
                    textAlign: 'left',
                  },
                  switchMerchant
                    ? {
                      width: '17.1%',
                      // left: '10%'
                    }
                    : {}, windowWidth === 1280 && windowHeight === 800 ? {
                      right: 1,
                    } : {}
                ]}>
                {/* <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{moment(item.orderDate).format('DD MMM')}</Text> */}
                {/* <Text style={{ color: Colors.fontDark, fontSize: 13, fontFamily: 'NunitoSans-Bold', marginTop: 2 }}>{moment(item.orderDate).format('LT')}</Text> */}
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(item.createdAt).format('DD MMM YYYY')}
                </Text>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                      marginTop: 2,
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(item.createdAt).format('hh:mm A')}
                </Text>
              </View>

              <View
                style={[
                  {
                    width: '10%',
                    marginHorizontal: 0.5,
                    alignItems: 'flex-start',
                  },
                  switchMerchant
                    ? {
                      width: '10.8%',
                      // left: '10%'
                    }
                    : {}, windowWidth === 1280 && windowHeight === 800 ? {
                      right: 2,
                    } : {}
                ]}>
                {/* <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{moment(item.orderDate).format('DD MMM')}</Text> */}
                {/* <Text style={{ color: Colors.fontDark, fontSize: 13, fontFamily: 'NunitoSans-Bold', marginTop: 2 }}>{moment(item.orderDate).format('LT')}</Text> */}
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {item.pax} pax
                </Text>
              </View>

              <View
                style={[
                  {
                    width: '21%',
                    marginHorizontal: 0.5,
                    alignItems: 'flex-start',
                  },
                  switchMerchant
                    ? {
                      width: '22.4%',
                      // left: '10%'
                    }
                    : {},
                ]}>
                {/* {
                  item.preorderPackageId
                    ? */}
                {/* <View style={{
                      width: '100%',
                      alignItems: 'center',
                    }}> */}
                <View
                  style={[
                    {
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 3,
                      backgroundColor: '#ACACAC',
                      width: '70%',
                      height: switchMerchant
                        ? 25
                        : windowHeight * 0.065,
                    }, windowWidth === 1280 && windowHeight === 800 ? {
                      left: 1,
                    } : {}
                  ]}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        fontSize: Platform.OS == 'ios' ? 14 : 16,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {moment().diff(item.updatedAt, 'minutes') < 60
                      ? `${mins}mins`
                      : moment().diff(item.updatedAt, 'hours') < 24
                        ? `${hrs}hrs:${mins}mins`
                        : `${dys}days:${hrs}hrs:${mins}mins`}
                    {/* {moment().diff(item.updatedAt)} */}
                  </Text>
                </View>

                {/* <Text style={{ color: Colors.blackColor, fontSize: Platform.OS == 'ios' ? 11 : 13, fontFamily: 'NunitoSans-Regular' }}>{`${moment(item.preorderCollectionDate).format('DD/MM/YYYY')} ${moment(item.preorderCollectionTime).format('hh:mma')}`}</Text> */}

                {/* </View> */}
                {/* :
                    <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '75%' }}>
                      <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{moment().diff(waitingTime, 'minutes') < 60 ? `${mins} mins` : `${hrs}hrs:${mins}mins`}</Text>
                    <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '65%' }}>
                      <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{waitingTime > 60 ? "OverTime" : (waitingTime < 0 ? 0 : waitingTime.toFixed(0)) + ' mins'}</Text> 
                    </View>
                } */}
              </View>
              {/* <View style={{ width: '10%', marginHorizontal: 0.5, alignItems: 'center' }}>
                <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>
                  VIP Room
                </Text>
              </View> */}
              <View
                style={[
                  {
                    width: '12.5%',
                    marginHorizontal: 0.5,
                    alignItems: 'flex-start',
                  },
                  switchMerchant
                    ? {
                      width: '10%',
                      // left: '10%'
                    }
                    : {},
                ]}>
                {/* {
                  item.preorderPackageId
                    ? */}
                {/* <View style={{
                      width: '100%',
                      alignItems: 'center',
                    }}> */}
                {/* No show/Seated/Waiting */}
                <View
                  style={[
                    {
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 3,
                      // backgroundColor: '#ACACAC',
                      backgroundColor,
                      width: '60%',
                      height: switchMerchant
                        ? 25
                        : windowHeight * 0.065,
                    },
                    switchMerchant
                      ? {
                        width: '75%',
                        // left: '10%'
                      }
                      : {},
                  ]}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        fontSize: Platform.OS == 'ios' ? 14 : 16,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {USER_QUEUE_STATUS_PARSED[item.status]}
                  </Text>
                </View>

                {/* <Text style={{ color: Colors.blackColor, fontSize: Platform.OS == 'ios' ? 11 : 13, fontFamily: 'NunitoSans-Regular' }}>{`${moment(item.preorderCollectionDate).format('DD/MM/YYYY')} ${moment(item.preorderCollectionTime).format('hh:mma')}`}</Text> */}

                {/* </View> */}
                {/* :
                    <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '75%' }}>
                      <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{moment().diff(waitingTime, 'minutes') < 60 ? `${mins} mins` : `${hrs}hrs:${mins}mins`}</Text>
                    <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '65%' }}>
                      <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{waitingTime > 60 ? "OverTime" : (waitingTime < 0 ? 0 : waitingTime.toFixed(0)) + ' mins'}</Text> 
                    </View>
                } */}
              </View>

              <View
                style={{
                  width: '7.5%',
                  marginHorizontal: 0.5,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View
                  style={
                    switchMerchant
                      ? {
                        width: '100%',
                        flexDirection: 'row',
                        marginHorizontal: 0.5,
                        justifyContent: 'space-evenly',
                        alignItems: 'center',
                        right: windowWidth * 0.015,
                      }
                      : {
                        width: '100%',
                        flexDirection: 'row',
                        marginHorizontal: 0.5,
                        justifyContent: 'space-evenly',
                        alignItems: 'center',
                      }
                  }>

                  <TouchableOpacity
                    style={{ justifyContent: 'center', alignItems: 'center' }}
                    onPress={() => {
                      setDisplayRemarkModal(item.uniqueId);
                    }}>

                    {switchMerchant ? (
                      <FontAwesome
                        name="sticky-note-o"
                        size={13}
                        color={Colors.primaryColor}
                      />
                    ) : (
                      <FontAwesome
                        name="sticky-note-o"
                        size={24}
                        color={Colors.primaryColor}
                      />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{ justifyContent: 'center', alignItems: 'center' }}
                    onPress={() => {
                      setQueueCustomerName(item.userName ? item.userName : '');
                      setQueuePhone(item.userPhone ? item.userPhone : '');
                      setQueuePax(item.pax ? item.pax : '');
                      setQueueCategory(item.category ? item.category : null);

                      setSelectedQueue(item);
                      setAddQueueModal(true);
                    }}>
                    {switchMerchant ? (
                      <FontAwesome5
                        name="edit"
                        size={13}
                        color={Colors.primaryColor}
                      />
                    ) : (
                      <FontAwesome5
                        name="edit"
                        size={23}
                        color={Colors.primaryColor}
                      />
                    )}
                  </TouchableOpacity>


                </View>
              </View>

              {/* <TouchableOpacity style={{
                // position: 'absolute',
                // top: Platform.OS == 'ios' ? 3 : 23, right: Platform.OS == 'ios' ? 3 : 10,
                // alignItems: 'center',
                position: 'absolute',
                top: Platform.OS == 'ios' ? 3 : 30, right: Platform.OS == 'ios' ? 3 : 20,
                alignItems: 'center'
              }}>
                <Icon name="edit" size={25} color={Colors.primaryColor}/>
              </TouchableOpacity> */}
            </View>
          </View>
        </Swipeable>
      </View>
    );
  };

  const messageBubble = (message) => {
    const isFromOutlet = currOutlet?.uniqueId ? message.from === currOutlet.uniqueId : false;

    const messageParseDict = {
      'CONFIRM': 'Confirm Queue',
      'CANCEL': 'Cancel Queue',
      'REMARKS': 'Add Remark',
    }

    const parsedMessage = messageParseDict[message.message] || message.message;

    return (
      <View >
        {isFromOutlet ?
          // Outlet Message
          <View style={[styles.messageContainer, styles.outletMessageContainer]}>
            <View style={[styles.messageBubble, styles.outletMessageBubble]}>
              <Text style={styles.outletMessage}>{parsedMessage}</Text>
            </View>
          </View>
          :
          // Customer Message
          <View style={[styles.messageContainer, styles.customerMessageContainer]}>
            <View style={[styles.messageBubble, styles.customerMessageBubble]}>
              <Text style={styles.customerMessage}>{parsedMessage}</Text>
            </View>
          </View>
        }
      </View>
    );
  }

  return (
    <UserIdleWrapper disabled={!isMounted}>


      {displayRemarkModal !== null && (
        <Modal
          animationType="fade"
          transparent
          visible={displayRemarkModal !== null}
          onRequestClose={() => {
            setDisplayRemarkModal(null)
          }}>
          <View style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <View style={{
              backgroundColor: Colors.whiteColor,
              width: windowWidth * 0.4,
              height: windowHeight * 0.4,
              borderRadius: 20,
              paddingVertical: 20,
              paddingHorizontal: 30,
              alignItems: 'center'
            }}>

              <TouchableOpacity style={{ position: 'absolute', top: 10, right: 10 }} onPress={() => setDisplayRemarkModal(null)}>
                <Ionicons name="close-outline" size={26} color={Colors.descriptionColor} />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: 22,
                  color: Colors.mainTxtColor,
                  fontFamily: 'NunitoSans-Bold',
                  marginBottom: 10
                }}>
                Customer Remarks
              </Text>

              <ScrollView
                style={{
                  flex: 1,
                  width: '100%',
                  padding: 10,
                  flexDirection: 'column',
                  height: windowHeight * 0.7,
                  borderWidth: 1,
                  borderRadius: 10,
                  borderColor: Colors.fieldtTxtColor,
                  backgroundColor: Colors.fieldtBgColor,
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                }}
              >
                {userQueuesRaw.map(queueItem => {
                  if (queueItem.uniqueId === displayRemarkModal) {
                    return (
                      <Text
                        key={queueItem.uniqueId}
                        style={{
                          fontSize: 16,
                          color: Colors.mainTxtColor,
                          fontFamily: 'NunitoSans-Regular'
                        }}
                      >
                        {queueItem.remark || 'No Remark'}
                      </Text>
                    );
                  }
                  return null;
                })}
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}

      {displayChat !== null && (
        <Modal
          animationType="fade"
          transparent
          visible={displayChat !== null}
          onRequestClose={() => {
            setDisplayChat(null);
          }}>
          <View style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <View style={{
              backgroundColor: Colors.whiteColor,
              width: windowWidth * 0.4,
              height: windowHeight * 0.7,
              borderRadius: 20,
              paddingVertical: 20,
              paddingHorizontal: 30,
              alignItems: 'center'
            }}>

              <TouchableOpacity style={{ position: 'absolute', top: 10, right: 10 }} onPress={() => setDisplayChat(null)}>
                <Ionicons name="close-outline" size={26} color={Colors.descriptionColor} />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: 22,
                  color: Colors.mainTxtColor,
                  fontFamily: 'NunitoSans-Bold',
                  marginBottom: 20
                }}>
                Chat History
              </Text>

              <ScrollView
                style={{
                  flex: 1,
                  width: '100%',
                  paddingVertical: 20,
                  flexDirection: 'column',
                  height: windowHeight * 0.7,
                }}
              >
                {(() => {
                  const queueChat = userQueuesRaw.find(q => q.uniqueId === displayChat);
                  if (queueChat && queueChat.chat) {
                    return queueChat.chat.map((message, index) => (
                      messageBubble(message)
                    ));
                  }
                  return null;
                })()}
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}

      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        {/* Old UX */}
        {/* >
        <View style={{ backgroundColor: Colors.whiteColor }}>
          <View style={{ alignItems: 'center', flexDirection: 'row', alignSelf: "center", marginVertical: 20, marginBottom: 20 }}>
            <Text style={{ fontWeight: "bold", fontSize: 25, marginRight: 10 }}>Queue</Text>
            <Switch
              value={currOutlet.queueStatus}
              onSyncPress={(value) => {
                // setState({ newReservationStatus: item }, function  = () => {
                //   switchQueueStatus();
                // });
                switchQueueStatus(value)
              }}
              width={42}
              circleColorActive={Colors.primaryColor}
              circleColorInactive={Colors.fieldtTxtColor}
              backgroundActive='#dddddd'
            />
            <Text
              style={{ fontSize: 20, marginLeft: 10, color: currOutlet.queueStatus ? Colors.primaryColor : Colors.fieldtTxtColor, alignSelf: 'center' }}>
              {currOutlet.queueStatus ? 'ON' : 'OFF'}
            </Text>
          </View>

          <FlatList
            data={userQueues}
            extraData={userQueues}
            renderItem={renderRow}
            numColumns={2}
            contentContainerStyle={{
              paddingHorizontal: 4,
              paddingBottom: 100,
              paddingLeft: 6,
              paddingTop: 4,
              alignSelf: 'center',
              //backgroundColor: 'red',
              width: '90%',
            }}
            style={{
              paddingTop: 5,
              paddingRight: 0,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}
          />

        </View> */}

        {/* New UX */}
        {/* <ScrollView scrollEnabled={switchMerchant} horizontal={true}> */}
        <View style={[styles.content, switchMerchant ? {
          // width: '100%',
        } : {}]}>
          <View
            style={[
              {
                flexDirection: 'row',
                alignItems: 'center',
                padding: 2,
                width: switchMerchant ? '100%' : '100%',
                //justifyContent: 'space-between',
              },
              switchMerchant
                ? {
                  marginBottom: windowHeight * 0.03,
                  // borderWidth: 1,
                  marginTop: windowHeight * 0.03,
                  justifyContent: 'space-between',
                  // backgroundColor: 'red',
                }
                : {},
            ]}>
            <View style={[{ flexDirection: 'row', alignItems: 'center' },]}>
              <Text
                style={[
                  { fontSize: 26, fontFamily: 'NunitoSans-Bold' },
                  switchMerchant
                    ? {
                      // fontSize: 15,
                      //follow dashboard
                      fontSize: 20,
                      // borderWidth: 1,
                      // top: windowHeight * -0.05,
                    }
                    : {},
                ]}>
                {userQueues.length} Queue
              </Text>
              <View
                style={[
                  {
                    flexDirection: 'column',
                    top: Platform.OS == 'ios' ? 3 : 5,
                    marginLeft: switchMerchant ? '5%' : 25,
                    marginRight: 25
                  },
                  switchMerchant
                    ? {
                      // borderWidth: 1,
                      marginTop: '-8%',
                      // top: windowHeight * -0.042,
                      // marginLeft: windowWidth * 0.01,
                    }
                    : {},
                ]}>
                {isLoading ? (
                  <View
                    style={[{
                      // paddingTop: Platform.OS == 'ios' ? 10 : 0,
                      // position: 'absolute',
                      marginBottom: 10,
                      marginRight: 10,
                    }, switchMerchant ? {
                      marginBottom: 0,
                    } : {}]}>
                    {switchMerchant ? (
                      <ActivityIndicator
                        size={'small'}
                        color={Colors.secondaryColor}
                      />
                    ) : (
                      <ActivityIndicator
                        size={'large'}
                        color={Colors.secondaryColor}
                      />
                    )}
                  </View>
                ) : (
                  <>
                    {
                      currOutlet !== undefined
                        ?
                        <>
                          {switchMerchant ? (
                            <Switch
                              value={currOutlet.queueStatus}
                              onSyncPress={(value) => {
                                // setState({ newReservationStatus: item }, function  = () => {
                                //   switchQueueStatus();
                                // });
                                switchQueueStatus(value);
                              }}
                              width={20}
                              height={10}
                              circleColorActive={Colors.primaryColor}
                              circleColorInactive={Colors.fieldtTxtColor}
                              backgroundActive="#dddddd"
                            />
                          ) : (
                            <Switch
                              value={currOutlet.queueStatus}
                              onSyncPress={(value) => {
                                // setState({ newReservationStatus: item }, function  = () => {
                                //   switchQueueStatus();
                                // });
                                switchQueueStatus(value);
                              }}
                              width={42}
                              circleColorActive={Colors.primaryColor}
                              circleColorInactive={Colors.fieldtTxtColor}
                              backgroundActive="#dddddd"
                            />
                          )}
                        </>
                        :
                        <>
                        </>
                    }

                    <Text
                      style={[
                        {
                          fontSize: 18,
                          marginTop: 0,
                          color: currOutlet.reservationStatus
                            ? Colors.primaryColor
                            : Colors.fieldtTxtColor,
                          textAlign: 'center',
                          right: Platform.OS == 'ios' ? 1 : 0,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {currOutlet.queueStatus ? 'ON' : 'OFF'}
                    </Text>
                  </>
                )}
              </View>
            </View>

            <View
              style={[
                { flexDirection: 'row', alignItems: 'center' },
                switchMerchant
                  ? {
                    // left: windowWidth * -0.05,

                    // backgroundColor: 'red',
                  }
                  : {},
              ]}>

              {/* <TouchableOpacity
                style={[
                  styles.submitText,
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 10,
                    height: 40,
                    left: 0,
                    backgroundColor: '#4E9F7D',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                  },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.046,
                      height: 35,
                      width: windowWidth * 0.12,
                    }
                    : {},
                ]}
                onPress={() => {
                  // setQueueCustomerName('');
                  // setQueuePhone('');
                  // setQueuePax('');

                  // setSelectedQueue(null);
                  // setAddQueueModal(true);
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  {switchMerchant ? (
                    <AntDesign name="pluscircle" size={10} color="#FFFFFF" />
                  ) : (
                    <AntDesign name="pluscircle" size={20} color="#FFFFFF" />
                  )}

                  <Text
                    style={[
                      {
                        marginLeft: 5,
                        color: Colors.primaryColor,
                        fontSize: 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                          // paddingLeft: '10%',
                        }
                        : {},
                    ]}>
                    QR
                  </Text>
                </View>
                <QRCode
                  value={`${qrUrl}outlet/${currOutlet ? currOutlet.subdomain : ''}/takeaway`}
                  size={windowWidth * 0.17}
                  logoBackgroundColor="transparent"
                  getRef={(ref) => setQRCodeTakeawayRef(ref)}
                />
                {/* <View style={[styles.modalBody, { marginTop: 20 }]}>
                  <QRCode
                  //   value={`${qrUrl}outlet/${currOutlet ? currOutlet.subdomain : ''}/takeaway`}
                  //   size={windowWidth * 0.17}
                  //   logoBackgroundColor="transparent"
                  //   getRef={(ref) => setQRCodeTakeawayRef(ref)}
                  // />
                </View> */}
              {/* </TouchableOpacity> */}

              {/* {
                !switchMerchant
                  ?
                  <View style={{ alignSelf: 'flex-end', marginRight: 10, flexDirection: 'row' }}>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginHorizontal: 5,
                      }}
                      onPress={() => { 
                        setQrTakeaway(true)
                        console.log('outletData', currOutlet)
                        OutletStore.update((s) => {
                          s.currTableQRUrl = `${qrUrl}outlet/${currOutlet ? currOutlet.subdomain : ''}/queue/`;
                        });
                        }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',

                          paddingRight: '2%',
                        }}>
                        QR
                      </Text>
                    </TouchableOpacity>
                  </View>
                  :
                  <></>
              } */}

              {/* {
                switchMerchant
                  ?
                  <View style={{
                    // alignSelf: 'flex-end',
                    alignItems: 'flex-end',
                    justifyContent: 'flex-end',
                    marginRight: 10, flexDirection: 'row',
                    //  marginTop: 10, marginBottom: 20,
                    // backgroundColor: 'blue',

                    // width:windowWidth * (1 - Styles.sideBarWidth) * 0.85,
                  }}>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 10,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginHorizontal: 5,
                      }}
                      onPress={() => { setQrTakeaway(true) }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                          paddingHorizontal: 2,
                          // paddingRight: '2%',
                        }}>
                        QR
                      </Text>
                    </TouchableOpacity>
                  </View>
                  :
                  <></>
              } */}

              <Modal
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={qrTakeaway}
                transparent>
                <View style={styles.modalContainer}>
                  <View
                    style={[
                      styles.modalView,
                      {
                        width: windowWidth * 0.45,
                        height: windowHeight * 0.67,

                        padding: Dimensions.get('window').width * 0.03,
                        paddingHorizontal: Dimensions.get('window').width * 0.015,

                        ...getTransformForModalInsideNavigation(),
                      },
                    ]}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        switchMerchant
                          ? {
                            position: 'absolute',
                            top: windowWidth * 0.02,
                            right: windowWidth * 0.02,
                          }
                          : {},
                      ]}
                      onPress={() => {
                        setQrTakeaway(false);
                      }}>
                      {switchMerchant ? (
                        <AIcon
                          name="closecircle"
                          size={15}
                          color={Colors.fieldtTxtColor}
                        />
                      ) : (
                        <AIcon
                          name="closecircle"
                          size={25}
                          color={Colors.fieldtTxtColor}
                        />
                      )}
                    </TouchableOpacity>
                    <View style={styles.modalTitle}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 22,
                          },
                          switchMerchant
                            ? {
                              fontSize: 16,
                            }
                            : {},
                        ]}>
                        QR Queue
                      </Text>
                    </View>
                    <View style={[styles.modalBody, { marginTop: 20 }]}>
                      <QRCode
                        value={`${global.appEnv === APP_ENV.LIVE ? qrUrl : qrUrlUat}outlet/${currOutlet ? currOutlet.subdomain : ''}/queue/`}
                        size={windowWidth * 0.17}
                        logoBackgroundColor="transparent"
                        getRef={(ref) => {
                          setQRCodeTakeawayRef(ref)
                        }}
                      />
                    </View>

                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          marginLeft: 10,
                          marginTop: 20,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={async () => {
                          await printQRTakeaway();
                        }}>
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          PRINT QR
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        disabled={isSavingQRCode}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          marginLeft: 10,
                          marginTop: 20,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={async () => {
                          if (
                            qrCodeTakeawayRef
                          ) {
                            setIsSavingQRCode(true);

                            qrCodeTakeawayRef.toDataURL((data) => {
                              var imageFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/qr-takeaway-${Date.now().toString()}.png`;

                              RNFS.writeFile(imageFile, data, 'base64')
                                .then((success) => {
                                  Alert.alert('Info', `QR code saved successfully as ${imageFile}`);

                                  c(false);

                                  // return CameraRoll.saveToCameraRoll(RNFS.CachesDirectoryPath + "/some-name.png", 'photo')
                                })
                                .catch((ex) => {
                                  Alert.alert('Error', ex);

                                  setIsSavingQRCode(false);

                                  // this.setState({ busy: false, imageSaved: true })
                                  // ToastAndroid.show('Saved to gallery !!', ToastAndroid.SHORT)
                                });
                            })
                          }
                        }}>
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          EXPORT QR
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal>




              <TouchableOpacity
                style={[
                  styles.submitText,
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 10,
                    height: 40,
                    left: 0,
                    backgroundColor: '#4E9F7D',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                  },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.046,
                      height: 35,
                      width: windowWidth * 0.12,
                    }
                    : {},
                ]}
                onPress={() => {
                  setQueueCustomerName('');
                  setQueuePhone('');
                  setQueuePax('');
                  setQueueCategory(null);
                  setSelectedQueue(null);
                  setAddQueueModal(true);
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  {switchMerchant ? (
                    <AntDesign name="pluscircle" size={10} color="#FFFFFF" />
                  ) : (
                    <AntDesign name="pluscircle" size={20} color="#FFFFFF" />
                  )}

                  <Text
                    style={[
                      {
                        marginLeft: 5,
                        color: Colors.primaryColor,
                        fontSize: 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                          // paddingLeft: '10%',
                        }
                        : {},
                    ]}>
                    QUEUE
                  </Text>
                </View>
              </TouchableOpacity>

              {/* <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 10, height: 40, }}>
            
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingLeft: 10,
                  borderRadius: 5,
                  height: 40,
                  borderRadius: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  backgroundColor: 'white',
                  marginRight: 15,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,

                }}>
                  <Text style={{ fontSize: 16, paddingRight: Platform.OS == 'ios' ? 20 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>
                    Filter
                  </Text>
                  <DropDownPicker
                    controller={instance => setController1(instance)}
                    arrowColor={Colors.primaryColor}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    labelStyle={{ fontFamily: 'NunitoSans-Regular' }}
                    itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                    placeholderStyle={{ color: 'black' }}
                    style={{ width: 140, borderWidth: 0, paddingHorizontal: 5, paddingVertical: 0, borderRadius: 5, borderColor: '#E5E5E5', borderWidth: 0, borderLeftWidth: 0, }}
                    items={[{ label: 'Pending', value: 0 }, { label: 'Accepted', value: 1 }, { label: 'Seated', value: 2 }, { label: 'Served', value: 3 }, { label: 'Rejected', value: 4 }, { label: 'No Show', value: 5 }]} //Awaiting Authorization
                    placeholder={"Pending"}
                    onChangeItem={selectedFilter => {
                      filterOrders(selectedFilter);
                    }
                    }
                  />
                </View>
              </View> */}

              <View
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    borderBottomLeftRadius: 5,
                    borderTopLeftRadius: 5,
                    height: 40,
                    borderWidth: 1,
                    borderRightWidth: 0,
                    borderColor: '#E5E5E5',
                    backgroundColor: 'white',
                    // marginRight: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.046,
                      // width: windowWidth * 0.21,
                      height: 35,
                      // left: windowWidth * -0.12,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      paddingLeft: '1%',
                      paddingRight: Platform.OS == 'ios' ? 20 : 20,
                      borderColor: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                        // left: windowWidth * -0.015,
                        // left: '30%',
                        // backgroundColor: 'white',
                        // height: '100%',
                        // left: '-20%',
                        // borderRadius: 5,
                        // textAlignVertical: 'center'
                      }
                      : {},
                  ]}>
                  Filter
                </Text>
              </View>
              <DropDownPicker
                // controller={instance => setController1(instance)}
                arrowColor={Colors.primaryColor}
                arrowSize={switchMerchant ? 13 : 23}
                arrowStyle={[
                  { fontWeight: 'bold' },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.005,
                      height: '200%',
                      // borderWidth: 1
                    }
                    : {},
                ]}
                labelStyle={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                itemStyle={[
                  { justifyContent: 'flex-start', marginLeft: 6 },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                placeholderStyle={[
                  { color: 'black' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                style={[
                  {
                    width: 140,
                    borderWidth: 0,
                    height: 40,
                    paddingHorizontal: 5,
                    paddingVertical: 0,
                    borderBottomRightRadius: 5,
                    borderTopRightRadius: 5,
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                    borderColor: '#E5E5E5',
                    borderWidth: 1,
                    borderLeftWidth: 0,
                    paddingLeft: 2,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    marginRight: 15,
                    flex: 0,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      width: windowWidth * 0.12,
                      height: 35,
                    }
                    : {},
                ]}
                dropDownStyle={{
                  paddingLeft: 2,
                  right: 15,
                  width: switchMerchant
                    ? windowWidth * 0.12
                    : 140,
                }}
                items={[
                  { label: 'All', value: 0 },
                  { label: 'Pending', value: 1 },
                  { label: 'Seated', value: 3 },
                  { label: 'Served', value: 4 },
                  { label: 'No Show', value: 6 },
                  { label: 'Notified', value: 7 },
                ]} //Awaiting Authorization
                // placeholder={"Pending"}
                defaultValue={filterType}
                onChangeItem={(selectedFilter) => {
                  // filterOrders(selectedFilter);
                  setFilterType(selectedFilter.value);
                }}
              //onOpen={() => controller.close()}
              />
              {/* </View> */}

              <View
                style={[
                  {
                    height: switchMerchant ? 35 : 40,

                    marginRight: 15,
                  },
                  !isTablet()
                    ? {
                      marginLeft: 0,
                    }
                    : {},
                ]}>
                <View
                  style={[
                    {
                      width: 250,
                      height: 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    },
                    switchMerchant
                      ? {
                        height: 35,
                        width: windowWidth * 0.18,
                        // left: windowWidth * -0.05,
                        // top: windowHeight * -0.03,
                      }
                      : {},
                  ]}>
                  {switchMerchant ? (
                    <Icon
                      name="search"
                      size={13}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                  ) : (
                    <Icon
                      name="search"
                      size={18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                  )}
                  {switchMerchant ? (
                    <TextInput
                      // underlineColorAndroid={Colors.whiteColor}
                      style={[
                        {
                          width: 180,
                          fontSize: 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            // borderWidth:1,
                            // width: windowWidth * 0.17,
                            height: 35,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());
                      }}
                    // value={search}
                    />
                  ) : (
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: 220,
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());
                      }}
                    // value={search}
                    />
                  )}
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.submitText,
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 10,
                    height: 40,
                    left: 0,
                    backgroundColor: '#4E9F7D',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                  },
                  switchMerchant
                    ? {
                      height: 35,
                      width: windowWidth * 0.12,
                    }
                    : {},
                ]}
                onPress={() => {
                  navigation.navigate('QueueConfig');
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  {switchMerchant ? (
                    <Ionicons name="settings-sharp" size={10} color="#FFFFFF" />
                  ) : (
                    <Ionicons name="settings-sharp" size={20} color="#FFFFFF" />
                  )}

                  <Text style={{
                    marginLeft: 5,
                    color: '#FFFFFF',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 16
                  }}>
                    CONFIG
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={[
              { marginTop: 30, marginBottom: 100, zIndex: -1 },
              switchMerchant
                ? {
                  // borderWidth: 1,
                  marginTop: 5,
                  marginBottom: windowHeight * 0.23,
                }
                : {},
            ]}>
            <View
              style={{ width: '100%', flexDirection: 'row', alignItems: 'center' }}>
              <View
                style={{
                  marginLeft: '2.5%',
                  width: '13%',
                  alignItems: 'flex-start',
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ marginHorizontal: 0.5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Queue ID
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{}}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* ///////////////////////////Name/////////////////////// */}
              <View style={{ width: '16%', alignItems: 'flex-start' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ marginHorizontal: 0.5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Name
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{}}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.NAME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.NAME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* ///////////////////////////Date Time/////////////////////// */}
              <View style={{ width: '16%', alignItems: 'flex-start' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View
                    style={{
                      marginHorizontal: 0.5,
                      left: Platform.OS === 'android' ? '1%' : 0,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Date/Time
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{}}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* ///////////////////////////Capacity/////////////////////// */}
              <View style={{ width: '10%', alignItems: 'flex-start' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ marginHorizontal: 0.5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Capacity
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{}}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* ///////////////////////////Waiting Time/////////////////////// */}
              <View style={{ width: '21%', alignItems: 'flex-start' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ marginHorizontal: 0.5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            left: Platform.OS === 'android' ? '5%' : 0,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Waiting Time
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{ paddingLeft: 5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* <View style={{ width: '18%', marginHorizontal: 0.5, alignItems: 'center' }}>
                <View style={{ justifyContent: 'center', alignItems: 'center', borderRadius: 3, backgroundColor: '#ACACAC', width: '60%', height: 40, }}> */}
              {/* ///////////////////////////Status/////////////////////// */}
              <View style={{ width: '15%', alignItems: 'flex-start' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ marginHorizontal: 0.5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
                          setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC);
                        } else {
                          setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC);
                        }
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            left: Platform.OS === 'android' ? '10%' : 0,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Status
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{ paddingLeft: 5 }}>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-up"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-up"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC);
                      }}>
                      {switchMerchant ? (
                        <Entypo
                          name="triangle-down"
                          size={8}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.STATUS_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      ) : (
                        <Entypo
                          name="triangle-down"
                          size={14}
                          color={
                            sort === QUEUE_SORT_FIELD_TYPE.STATUS_DESC
                              ? Colors.secondaryColor
                              : Colors.descriptionColor
                          } />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <View
                style={{
                  width: '5%',
                  marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                }} />
            </View>

            <FlatList
              // data={takeAwayOrders.slice(0).sort((a, b) => {
              //   return b.orderDate - a.orderDate;

              // }).sort((a, b) => {
              //   return b.isPrioritizedOrder - a.isPrioritizedOrder;
              // }).filter(item => {
              //   if (search !== '') {
              //     const searchLowerCase = search.toLowerCase();

              //     for (var i = 0; i < item.cartItems.length; i++) {
              //       if (item.cartItems[i].name.toLowerCase().includes(searchLowerCase)) {
              //         return true;
              //       }

              //       for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
              //         if (item.cartItems[i].addOns[j].name.toLowerCase().includes(searchLowerCase)) {
              //           return true;
              //         }

              //         for (var k = 0; k < item.cartItems[i].addOns[j].choiceNames.length; k++) {
              //           if (item.cartItems[i].addOns[j].choiceNames[k].toLowerCase().includes(searchLowerCase)) {
              //             return true;
              //           }
              //         }
              //       }
              //     }

              //     return false;
              //   }
              //   else {
              //     return true;
              //   }
              // })}
              showsVerticalScrollIndicator={false}
              data={sortOperationQueue(userQueues, sort).filter((item) => {
                if (search !== '') {
                  const searchLowerCase = search.toLowerCase();
                  if (
                    item.number
                      .toString()
                      .toLowerCase()
                      .includes(searchLowerCase) ||
                    item.userName.toLowerCase().includes(searchLowerCase) ||
                    item.createdAt
                      .toString()
                      .toLowerCase()
                      .includes(searchLowerCase) ||
                    item.pax.toString().toLowerCase().includes(searchLowerCase) ||
                    item.updatedAt
                      .toString()
                      .toLowerCase()
                      .includes(searchLowerCase) ||
                    item.status.toLowerCase().includes(searchLowerCase)
                  ) {
                    return true;
                  } else {
                    return false;
                  }
                } else {
                  return true;
                }
              })}
              //   data={sortReportDataList(productSales.filter(item => {
              //     if (search !== '') {
              //         return item.productName.toLowerCase().includes(search.toLowerCase());
              //     }
              //     else {
              //         return true;
              //     }
              // }), currReportSummarySort).slice((currentPage - 1) * perPage, currentPage * perPage)}

              // extraData={userQueues}
              renderItem={renderQueue}
              keyExtractor={(item, index) => String(index)}
              contentContainerStyle={{
                paddingBottom: 80,
              }}
            />
          </View>
        </View>
        {/* </ScrollView> */}

        {/* add modal */}
        <Modal
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={addQueueModal}
          transparent>
          <View style={styles.modalContainer}>
            <View
              style={[
                {
                  width:
                    windowWidth <= 1133
                      ? windowWidth * 0.45
                      : windowWidth * 0.4,
                  height: windowHeight * 0.65,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: windowWidth * 0.04,
                  paddingBottom: 0,
                  alignItems: 'center',
                  justifyContent: 'space-between',

                  ...getTransformForModalInsideNavigation(),
                },
                switchMerchant
                  ? {
                    // padding: windowWidth * 0.005,
                    height: windowHeight * 0.7,
                    paddingBottom: 0,
                  }
                  : {},
              ]}>
              <View
                style={[
                  { justifyContent: 'space-between' },
                  switchMerchant
                    ? {
                      height: '100%',

                      // width: '100%',
                    }
                    : {},
                ]}>
                <View style={{}}>
                  <Text
                    style={[
                      {
                        fontSize: 24,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    {selectedQueue ? 'Queue Info' : 'Add Queue'}
                  </Text>
                  <Text
                    style={[
                      {
                        fontSize: 20,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        marginTop: 5,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>{`There are currently ${queueCount} parties in queue.`}</Text>
                </View>

                <View
                  style={[
                    {
                      justifyContent: 'center',
                      marginVertical: 20,
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // marginTop: '-30%'

                        // bottom: windowHeight * 0.16,
                      }
                      : {},
                  ]}>
                  {/* <ScrollView> */}
                  <View
                    style={[
                      {
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                      },
                      switchMerchant
                        ? {
                          marginTop: '3%',
                          paddingHorizontal: '5%',
                        }
                        : {},
                    ]}>
                    <View style={{ justifyContent: 'center', }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,

                              // left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Customer Name:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', }}>
                      <TextInput
                        placeholder="John Doe"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        textAlign='center'
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setQueueCustomerName(text);
                        }}
                        defaultValue={queueCustomerName}
                      />
                    </View>
                  </View>

                  <View
                    style={[{
                      justifyContent: 'space-between',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      //width: '100%',
                    }, switchMerchant ? {
                      paddingHorizontal: '5%',
                    } : {}]}>
                    <View style={{ justifyContent: 'center' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Mobile Number:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center' }}>
                      <TextInput
                        editable={selectedQueue === null}
                        placeholder="60123456879"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        textAlign='center'
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setQueuePhone(text);
                        }}
                        keyboardType={'number-pad'}
                        defaultValue={queuePhone}
                      />
                    </View>
                  </View>

                  <View
                    style={[{
                      justifyContent: 'space-between',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      //width: '100%',
                    }, switchMerchant ? {
                      paddingHorizontal: '5%',
                    } : {}]}>
                    <View style={{ justifyContent: 'center', }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Capacity:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', }}>
                      {/* <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingRight: 10,
                      }}
                      onPress={() => {
                        setQueuePax(queuePax - 1 >= 0 ? queuePax - 1 : 0);
                      }}>
                      <MIcon
                        name="minus-circle"
                        size={20}
                        color={Colors.primaryColor}
                      />
                    </TouchableOpacity> */}
                      {/* <View
                      style={{
                        paddingRight: 10,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}> */}
                      <TextInput
                        placeholder="0"
                        //placeholderTextColor={Colors.descriptionColor}
                        //placeholderStyle={{color: Colors.descriptionColor, justifyContent: 'center', alignItems: 'center'}}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 99,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.128,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                              paddingLeft: 10,
                              // textAlign: 'left'
                            }
                            : {},
                        ]}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          setQueuePax(text.length > 0 ? text : '0');
                        }}
                        keyboardType={'number-pad'}
                        defaultValue={`${queuePax}`}
                      />
                      {/* </View> */}
                      {/* <TouchableOpacity
                      style={{justifyContent: 'center', alignItems: 'center'}}
                      onPress={() => {
                        setQueuePax(queuePax + 1);
                      }}>
                      <MIcon
                        name="plus-circle"
                        size={20}
                        color={Colors.primaryColor}
                      />
                    </TouchableOpacity> */}
                    </View>
                  </View>
                  <View
                    style={[{
                      justifyContent: 'space-between',
                      marginTop: 20,
                      flexDirection: 'row',
                    }, switchMerchant ? {
                      paddingHorizontal: '5%',
                    } : {}]}>
                    <View style={{ justifyContent: 'center', zIndex: 2000 }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Category:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <RNPickerSelect
                        onValueChange={(value) => {
                          setQueueCategory(value);
                        }}
                        items={queueCategoryDropdownList}
                        placeholder={{ label: "Select an Item...", value: null }}
                        style={{
                          inputIOS: {
                            backgroundColor: Colors.fieldtBgColor,
                            height: 45,
                            borderRadius: 5,
                            paddingHorizontal: 10,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            color: 'black',
                            fontSize: 16,
                            textAlign: "center",
                          },
                          inputAndroid: {
                            backgroundColor: Colors.fieldtBgColor,
                            height: 45,
                            borderRadius: 5,
                            paddingHorizontal: 10,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            color: 'black',
                            fontSize: 16,
                            justifyContent: 'center',
                          },
                          placeholder: {
                            color: 'gray',
                            fontSize: 16,
                            textAlign: 'center',

                          },
                        }}
                        value={queueCategory}
                        useNativeAndroidPickerStyle={false}
                      />
                    </View>
                  </View>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-end' }}>
                  <View
                    style={[
                      {
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        // Optional: Adjust spacing

                        // position: 'absolute',

                        // backgroundColor: 'red',

                        // marginTop: 35,
                        // borderBottomLeftRadius: switchMerchant
                        //     ? 20
                        //     : 20,
                        // borderBottomRightRadius: switchMerchant
                        // ? 20
                        // : 20,
                        // backgroundColor: 'white',
                      },
                      switchMerchant
                        ? {
                          // marginTop: windowHeight * 0.067,
                          // borderWidth: 1,
                          // width: '100%',
                          // backgroundColor: 'red',
                          // position: 'absolute',
                          width: windowWidth * 0.45,
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      disabled={isLoading}
                      onPress={() => {
                        // setConfirmQueueModal(true)
                        // setAddQueueModal(false)

                        if (selectedQueue === null) {
                          createUserQueueByMerchant();
                        } else {
                          updateUserQueueByMerchant();
                        }

                        setAddQueueModal(false)
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width:
                            windowWidth <= 1133
                              ? '56%'
                              : '58%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 80,
                          borderBottomLeftRadius: switchMerchant
                            ? 20
                            : 20,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: '50%',
                            borderBottomLeftRadius: 12,
                          }
                          : {},
                      ]}>
                      {isLoading ? (
                        <>
                          {switchMerchant ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.primaryColor}
                            />
                          ) : (
                            <ActivityIndicator
                              size={'large'}
                              color={Colors.primaryColor}
                            />
                          )}
                        </>
                      ) : (
                        <Text
                          style={[
                            {
                              fontSize: 22,
                              color: Colors.primaryColor,
                              fontFamily: 'NunitoSans-SemiBold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 12,
                              }
                              : {},
                          ]}>
                          CONFIRM
                        </Text>
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      disabled={isLoading}
                      onPress={() => {
                        // setState({ visible: false });
                        setAddQueueModal(false);
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width: '57.8%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 80,
                          borderBottomRightRadius: switchMerchant
                            ? 20
                            : 20,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: '50%',
                            borderBottomRightRadius: 12,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontSize: 22,
                            color: Colors.descriptionColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 12,
                            }
                            : {},
                        ]}>
                        CANCEL
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </Modal>

        {/* confirm modal */}
        <Modal
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={confirmQueueModal}
          transparent>
          <View style={styles.modalContainer}>
            <View
              style={[
                {
                  width: windowWidth * 0.4,
                  height: windowHeight * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: windowWidth * 0.03,
                  padding: windowWidth * 0.04,
                  alignItems: 'center',
                  justifyContent: 'space-between',

                  ...getTransformForModalInsideNavigation(),
                },
                switchMerchant
                  ? {
                    padding: windowWidth * 0.03,
                    height: windowHeight * 0.7,
                  }
                  : {},
              ]}>
              <View style={{}}>
                <View style={{ height: windowHeight * 0.1 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 30,
                      },
                      switchMerchant
                        ? {
                          fontSize: 22,
                        }
                        : {},
                    ]}>
                    Done!
                  </Text>
                </View>

                <View
                  style={[
                    {
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      alignSelf: 'center',
                    },
                    switchMerchant
                      ? { marginTop: windowHeight * 0.02 }
                      : {},
                  ]}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: '80%',
                        alignSelf: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    You’ve added queue
                  </Text>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: '80%',
                        alignSelf: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 18,
                        }
                        : {},
                    ]}>
                    successfully with number:{' '}
                    {selectedQueue ? `#${selectedQueue.number}` : 'N/A'}
                    {/* {item.number} */}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: windowHeight * 0.25,
                    width: windowWidth * 0.4,
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setConfirmQueueModal(false);
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width: windowWidth * 0.4,
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomLeftRadius: 35,
                        borderBottomRightRadius: 35,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant
                        ? { marginTop: windowHeight * 0.23 }
                        : {},
                    ]}>
                    <Text
                      style={[
                        {
                          fontSize: 22,
                          color: Colors.primaryColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 16,
                          }
                          : {},
                      ]}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </UserIdleWrapper>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  content7: {
    backgroundColor: '#e4e8eb',
    width: 800,
    height: 120,
    marginLeft: 50,
    marginVertical: 30,
    borderRadius: 20,
  },

  content8: {
    flex: 3,
    backgroundColor: '#e4e8eb',

    height: 120,
  },
  content9: {
    flex: 1,
    backgroundColor: Colors.primaryColor,

    height: 120,
  },
  content10: {
    flex: 1,
    backgroundColor: Colors.secondaryColor,

    height: 120,
  },
  content11: {
    flex: 1,
    backgroundColor: '#848f96',

    height: 120,
  },
  content6: {
    backgroundColor: Colors.whiteColor,
    width: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 120,
    height: 120,
    marginLeft: 50,
    marginVertical: 15,
    borderRadius: 5,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    padding: 16,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.05,
    left: 295,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.4,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    //borderRadius: windowWidth * 0.03,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    paddingHorizontal: Dimensions.get('window').width * 0.015,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
  },
  modalBody: {
    flex: 1.1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  messageContainer: {
    width: '100%',
    paddingBottom: 10,
  },
  messageBubble: {
    maxWidth: '75%',
    padding: 10,
    borderRadius: 15,
  },
  customerMessageContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  customerMessageBubble: {
    backgroundColor: Colors.highlightColor,
  },
  customerMessage: {
    fontFamily: 'NunitoSans-Regular',
  },
  outletMessageContainer: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  outletMessage: {
    fontFamily: 'NunitoSans-Regular',
  },
  outletMessageContent: {
    color: Colors.fieldtTxtColor,
    fontSize: 14,
  },
  outletMessageBubble: {
    backgroundColor: Colors.primaryColor,
  },
});

export default QueueScreen;
