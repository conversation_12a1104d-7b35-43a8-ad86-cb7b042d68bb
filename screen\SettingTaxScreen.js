import { Text } from "react-native-fast-text";
import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useMemo,
    useCallback,
} from 'react';
import {
    StyleSheet,
    Image,
    View,
    Modal as ModalComponent,
    Alert,
    Dimensions,
    TouchableOpacity,
    Linking,
    ActivityIndicator,
    Platform,
    KeyboardAvoidingView,
    useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Entypo from 'react-native-vector-icons/Entypo';
import Icon from 'react-native-vector-icons/Feather';
import Icon3 from 'react-native-vector-icons/EvilIcons';
// import Swipeout from 'react-native-swipeout';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Icon1 from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Icon2 from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import moment from 'moment';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    isTablet,
    parseImagePickerResponse
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import RNPickerSelect from 'react-native-picker-select';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import { USER_QUEUE_STATUS, USER_QUEUE_STATUS_PARSED, OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST } from '../constant/common';
import {
    QUEUE_SORT_FIELD_TYPE,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    QUEUE_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_COMPARE_OPERATOR,
    PAYMENT_SORT_FIELD_TYPE,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import { uploadImageToFirebaseStorage, sortPaymentDataList } from '../util/common';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SettingTaxScreen = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [addCustomTaxModal, setAddCustomTaxModal] = useState(false);

    const [selectedCustomTax, setSelectedCustomTax] = useState({});

    const [customTaxName, setCustomTaxName] = useState('');
    const [customTaxCode, setCustomTaxCode] = useState('');
    const [customTaxRate, setCustomTaxRate] = useState('');

    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const outletCustomTax = OutletStore.useState((s) => s.outletCustomTax);

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const [temp, setTemp] = useState('');

    const userName = UserStore.useState((s) => s.name);
    const userId = UserStore.useState((s) => s.firebaseUid);
    const userEmail = UserStore.useState((s) => s.email);
    const merchantId = UserStore.useState((s) => s.merchantId);
    const merchantName = MerchantStore.useState((s) => s.name);
    const merchantLogo = MerchantStore.useState((s) => s.logo);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const createCustomTax = async () => {
        if (
            customTaxName.length < 0
        ) {
            Alert.alert('Info', 'Please fill in the information',);

        } else {
            var isExisted = false;

            for (var i = 0; i < outletCustomTax.length; i++) {
                if (outletCustomTax[i].name === customTaxName) {
                    isExisted = true;
                    break;
                }
            }

            if (isExisted) {
                Alert.alert('Info', 'Custom Tax with the same name exists');

            }
            else {
                CommonStore.update((s) => {
                    s.isLoading = true;
                });
                var body = {
                    outletId: currOutlet.uniqueId,
                    merchantId,
                    name: customTaxName,
                    code: customTaxCode,
                    rate: +parseFloat(customTaxRate) / 100, // Convert percentage to decimal
                };

                APILocal.createOutletCustomTax({ body, uid: userId })
                    .then((result) => {
                        if (result && result.status === 'success') {

                            Alert.alert('Success', 'Custom Tax has been created');

                            setAddCustomTaxModal(false);

                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                        }
                    })
                    .catch((err) => {
                        console.error(err);

                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });
                    });
            }
        }
    };

    const updateCustomTax = async () => {
        if (
            customTaxName.length < 0
        ) {
            Alert.alert('Info', 'Please fill in the information',);

        } else {
            var isExisted = false;

            for (var i = 0; i < outletCustomTax.length; i++) {
                if (outletCustomTax[i].name === customTaxName && outletCustomTax[i].uniqueId !== selectedCustomTax.uniqueId) {
                    isExisted = true;
                    break;
                }
            }

            if (isExisted) {
                Alert.alert('Info', 'Custom Tax with the same name exists');

            }
            else {
                CommonStore.update((s) => {
                    s.isLoading = true;
                });
                var body = {
                    outletId: currOutlet.uniqueId,
                    merchantId,
                    name: customTaxName,
                    code: customTaxCode,
                    rate: +parseFloat(customTaxRate) / 100, // Convert percentage to decimal

                    customTaxId: selectedCustomTax.uniqueId,
                };

                APILocal.updateOutletCustomTax({ body, uid: userId })
                    .then((result) => {
                        if (result && result.status === 'success') {

                            Alert.alert('Success', 'Custom Tax has been updated successfully');

                            setAddCustomTaxModal(false);

                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                        }
                    })
                    .catch((err) => {
                        console.error(err);

                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });
                    });
            }
        }
    };

    const deleteOutletCustomTax = async (customTaxId) => {
        // means existing item

        ///////////////////////////////////

        var body = {
            customTaxId,
        };

        // console.log(body);

        CommonStore.update(s => {
            s.isLoading = true;
        });

        APILocal.deleteOutletCustomTax({ body, uid: userId })
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert('Success', "Custom Tax has been removed", [
                        {
                            text: 'OK',
                            onPress: () => {

                            },
                        },
                    ],
                        { cancelable: false },
                    );
                }

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            });
    }

    const [sort, setSort] = useState('');
    const [search, setSearch] = useState('');

    //Start Here Sorting

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        ...global.getHeaderTitleStyle(),
                    },
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Tax Settings
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const renderCustomTax = ({ item, index }) => {

        return (
            <View
                style={{
                    paddingVertical: 5,
                    shadowOffset: {
                        width: 0,
                        height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                }}>
                <View
                    style={[
                        { elevation: 1, borderRadius: 7, backgroundColor: 'white' },
                    ]}>
                    <View
                        style={[
                            {
                                width: '100%',
                                flexDirection: 'row',
                                height: windowHeight * 0.1,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtT,
                                // borderBottomWidth: expandViewDict[item.uniqueId] == true ? StyleSheet.hairlineWidth : null
                            },
                        ]}>

                        <View
                            style={[
                                {
                                    width: '18%',
                                    paddingLeft: 10,
                                    flexDirection: 'row',
                                    alignItems: 'flex-start',
                                },
                            ]}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    width: Platform.OS == 'ios' ? '60%' : '60%',
                                }}>
                                <Text
                                    numberOfLines={3}
                                    style={[
                                        {
                                            color: Colors.fontDark,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            textAlign: 'left',
                                        },
                                    ]}>
                                    {item.name}
                                </Text>
                            </View>
                        </View>

                        <View
                            style={[
                                {
                                    width: '18%',
                                    textAlign: 'left',
                                },
                            ]}>

                            <Text
                                style={[
                                    {
                                        color: Colors.fontDark,
                                        fontSize: 16,
                                        fontFamily: 'NunitoSans-Bold',
                                    },
                                ]}>
                                {item.code}
                            </Text>
                        </View>

                        <View
                            style={[
                                {
                                    width: '18%',
                                    textAlign: 'left',
                                },
                            ]}>

                            <Text
                                style={[
                                    {
                                        color: Colors.fontDark,
                                        fontSize: 16,
                                        fontFamily: 'NunitoSans-Bold',
                                    },
                                ]}>
                                {(item.rate * 100).toFixed(0)}%
                            </Text>
                        </View>

                        <View
                            style={{
                                width: '5%',
                                marginHorizontal: 0.5,
                                alignItems: 'flex-start',
                            }}>
                            <View
                                style={switchMerchant ? {
                                    width: '100%',
                                    flexDirection: 'row',
                                    marginHorizontal: 0.5,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    right: windowWidth * 0.015
                                } : {
                                    width: '100%',
                                    flexDirection: 'row',
                                    marginHorizontal: 0.5,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        setSelectedCustomTax(item);

                                        setCustomTaxName(item.name);
                                        setCustomTaxCode(item.code);
                                        setCustomTaxRate((item.rate * 100).toFixed(0)); // Convert decimal to percentage for display

                                        setAddCustomTaxModal(true);
                                    }}>
                                    <FontAwesome5
                                        name="edit"
                                        size={23}
                                        color={Colors.primaryColor}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View
                            style={{
                                width: '5%',
                                marginHorizontal: 0.5,
                                alignItems: 'flex-start',
                            }}>
                            <View
                                style={{
                                    width: '100%',
                                    flexDirection: 'row',
                                    marginHorizontal: 0.5,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        Alert.alert('Info', 'Are you sure you want to remove this Custom Tax?', [
                                            {
                                                text: 'YES', onPress: () => {
                                                    deleteOutletCustomTax(item.uniqueId)
                                                }
                                            },
                                            { text: 'NO', onPress: () => { } },
                                        ],
                                            { cancelable: true },
                                        );

                                    }}>
                                    <Icon
                                        name="trash-2"
                                        size={23}
                                        color="#eb3446"
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        );
    };

    // function end

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                    {
                        ...getTransformForScreenInsideNavigation(),
                    }
                ]}>

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    style={{ backgroundColor: Colors.highlightColor }}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}>
                    <ScrollView horizontal>
                        <View style={[styles.content, {
                            width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                        }]}>
                            <View
                                style={[{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    padding: switchMerchant ? 0 : 2,
                                    width: '100%',
                                    justifyContent: 'space-between',
                                },]}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <Text
                                        style={[
                                            { fontSize: 26, fontFamily: 'NunitoSans-Bold' },
                                        ]}>
                                        {`${outletCustomTax.length} Custom Tax`}
                                    </Text>
                                </View>
                                <View
                                    style={[
                                        { flexDirection: 'row', alignItems: 'center' },
                                    ]}>
                                    <TouchableOpacity
                                        style={[
                                            styles.submitText,
                                            {
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                borderRadius: 10,
                                                height: 40,
                                                left: 0,
                                                backgroundColor: '#4E9F7D',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                            },
                                        ]}
                                        onPress={() => {
                                            setSelectedCustomTax({});

                                            setCustomTaxName('');
                                            setCustomTaxCode('');
                                            setCustomTaxRate('');

                                            setAddCustomTaxModal(true);
                                        }}>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <AntDesign name="pluscircle" size={20} color="#FFFFFF" />
                                            <Text
                                                style={[
                                                    {
                                                        marginLeft: 5,
                                                        color: Colors.primaryColor,
                                                        fontSize: 16,
                                                        color: '#FFFFFF',
                                                        fontFamily: 'NunitoSans-Bold',
                                                    },
                                                ]}>
                                                CUSTOM TAX
                                            </Text>
                                        </View>
                                    </TouchableOpacity>

                                    <View
                                        style={[
                                            {
                                                height: switchMerchant ? 35 : 40,
                                            },
                                        ]}>
                                        <View
                                            style={[
                                                {
                                                    width: 250,
                                                    height: 40,
                                                    backgroundColor: 'white',
                                                    borderRadius: 5,
                                                    flexDirection: 'row',
                                                    alignContent: 'center',
                                                    alignItems: 'center',
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                },
                                            ]}>
                                            {switchMerchant ? (
                                                <Icon
                                                    name="search"
                                                    size={13}
                                                    color={Colors.primaryColor}
                                                    style={{ marginLeft: 15 }}
                                                />
                                            ) : (
                                                <Icon
                                                    name="search"
                                                    size={18}
                                                    color={Colors.primaryColor}
                                                    style={{ marginLeft: 15 }}
                                                />
                                            )}
                                            <TextInput
                                                underlineColorAndroid={Colors.whiteColor}
                                                style={{
                                                    width: 220,
                                                    fontSize: 15,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    paddingLeft: 5,
                                                    height: 45
                                                }}
                                                clearButtonMode="while-editing"
                                                placeholder=" Search"
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                onChangeText={(text) => {
                                                    setSearch(text);
                                                }}
                                            // value={search}
                                            />
                                        </View>
                                    </View>
                                </View>
                            </View>

                            <View
                                style={[
                                    { marginTop: 15, marginBottom: 50, zIndex: -1 },
                                ]}>
                                <View
                                    style={{ width: '100%', flexDirection: 'row', alignItems: 'center' }}>

                                    {/* ///////////////////////////Name/////////////////////// */}
                                    <View style={{ width: '18%', alignItems: 'flex-start', paddingLeft: 10, }}>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ marginHorizontal: 0.5 }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC) {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                                                        } else {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC);
                                                        }
                                                    }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Regular',
                                                            },
                                                        ]}>
                                                        Name
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{}}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC);
                                                    }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={14}
                                                        color={
                                                            sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                                                    }}>
                                                    <Entypo
                                                        name="triangle-down"
                                                        size={14}
                                                        color={
                                                            sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>

                                    {/* ///////////////////////////Date Time/////////////////////// */}
                                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <View
                                                style={{}}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC) {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC);
                                                        } else {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                                                        }
                                                    }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Regular',
                                                            },
                                                        ]}>
                                                        Code
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{}}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                                                    }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={14}
                                                        color={
                                                            sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC);
                                                    }}>
                                                    <Entypo
                                                        name="triangle-down"
                                                        size={14}
                                                        color={
                                                            sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>

                                    <View style={{ width: '18%', alignItems: 'flex-start' }}>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <View
                                                style={{}}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC) {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC);
                                                        } else {
                                                            setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC);
                                                        }
                                                    }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Regular',
                                                            },
                                                        ]}>
                                                        {'Rate (%)'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{}}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC);
                                                    }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={14}
                                                        color={
                                                            sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setSort(PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC);
                                                    }}>
                                                    {switchMerchant ? (
                                                        <Entypo
                                                            name="triangle-down"
                                                            size={8}
                                                            color={
                                                                sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    ) : (
                                                        <Entypo
                                                            name="triangle-down"
                                                            size={14}
                                                            color={
                                                                sort === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    )}
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>


                                </View>
                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    data={sortPaymentDataList(outletCustomTax, sort).filter((item) => {
                                        if (search !== '') {
                                            const searchLowerCase = search.toLowerCase();
                                            if (
                                                item.name.toLowerCase().includes(searchLowerCase)
                                            ) {
                                                return true;
                                            } else {
                                                return false;
                                            }
                                        } else {
                                            return true;
                                        }
                                    })}
                                    renderItem={renderCustomTax}
                                    keyExtractor={(item, index) => String(index)}
                                />
                            </View>

                        </View>

                        {/* add modal */}
                        <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={addCustomTaxModal} transparent>
                            <KeyboardAvoidingView behavior={'padding'} style={styles.modalContainer}>
                                <View
                                    style={[
                                        {
                                            width: windowWidth * 0.4,
                                            height: windowHeight * 0.5,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 12,
                                            padding: windowWidth * 0.04,
                                            paddingBottom: 0,
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                        },
                                        {
                                            ...getTransformForModalInsideNavigation(),
                                        }
                                    ]}>
                                    <TouchableOpacity
                                        style={{
                                            position: 'absolute',
                                            right: windowWidth * 0.02,
                                            top: windowWidth * 0.02,

                                            elevation: 1000,
                                            zIndex: 1000,
                                        }}
                                        onPress={() => {
                                            setAddCustomTaxModal(false);
                                        }}>
                                        <AntDesign
                                            name="closecircle"
                                            size={switchMerchant ? 15 : 25}
                                            color={Colors.fieldtTxtColor}
                                        />
                                    </TouchableOpacity>
                                    <View style={[{ justifyContent: 'space-between', }]}>
                                        <View style={{}}>
                                            <Text
                                                style={[
                                                    {
                                                        fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 24,
                                                        justifyContent: 'center',
                                                        alignSelf: 'center',
                                                        fontFamily: 'NunitoSans-Bold',
                                                    },
                                                ]}>
                                                {selectedCustomTax ? 'Custom Tax Info' : 'Add Custom Tax'}
                                            </Text>
                                            <Text
                                                style={[
                                                    {
                                                        fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 20,
                                                        justifyContent: 'center',
                                                        alignSelf: 'center',
                                                        marginTop: 5,
                                                        color: Colors.descriptionColor,
                                                        fontFamily: 'NunitoSans-Regular',
                                                    },
                                                ]}>{`There are currently ${outletCustomTax.length} Custom Tax${outletCustomTax.length > 1 ? '(s)' : ''}.`}
                                            </Text>
                                        </View>

                                        <View style={[{ justifyContent: 'center', }]}>
                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignSelf: 'center',
                                                    alignContent: 'center',
                                                    marginTop: switchMerchant ? 10 : 20,
                                                    flexDirection: 'row',
                                                    width: windowWidth * 0.35,
                                                }}>
                                                <View style={{ justifyContent: 'center', width: '40%' }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Bold',
                                                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 20,
                                                            },
                                                        ]}>
                                                        Tax Name
                                                    </Text>
                                                </View>
                                                <View style={{ justifyContent: 'center', width: '60%' }}>
                                                    <TextInput
                                                        placeholder="Tax Name"
                                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                        style={[
                                                            {
                                                                backgroundColor: Colors.fieldtBgColor,
                                                                width: '100%',
                                                                height: 40,
                                                                borderRadius: 5,
                                                                padding: 5,
                                                                marginVertical: 5,
                                                                borderWidth: 1,
                                                                borderColor: '#E5E5E5',
                                                                paddingLeft: 10,
                                                            },
                                                        ]}
                                                        selectTextOnFocus
                                                        onChangeText={(text) => {
                                                            setCustomTaxName(text);
                                                        }}
                                                        keyboardType={'default'}
                                                        defaultValue={customTaxName}
                                                    />
                                                </View>
                                            </View>

                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignSelf: 'center',
                                                    alignContent: 'center',
                                                    marginTop: switchMerchant ? 10 : 20,
                                                    flexDirection: 'row',
                                                    width: windowWidth * 0.35,
                                                }}>
                                                <View style={{ justifyContent: 'center', width: '40%' }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Bold',
                                                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 20,
                                                            },
                                                        ]}>
                                                        Tax Code
                                                    </Text>
                                                </View>
                                                <View style={{ justifyContent: 'center', width: '60%' }}>
                                                    <TextInput
                                                        placeholder="Tax Code"
                                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                        style={[
                                                            {
                                                                backgroundColor: Colors.fieldtBgColor,
                                                                width: '100%',
                                                                height: 40,
                                                                borderRadius: 5,
                                                                padding: 5,
                                                                marginVertical: 5,
                                                                borderWidth: 1,
                                                                borderColor: '#E5E5E5',
                                                                paddingLeft: 10,
                                                            },
                                                        ]}
                                                        selectTextOnFocus
                                                        onChangeText={(text) => {
                                                            setCustomTaxCode(text);
                                                        }}
                                                        keyboardType={'default'}
                                                        defaultValue={customTaxCode}
                                                    />
                                                </View>
                                            </View>

                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignSelf: 'center',
                                                    alignContent: 'center',
                                                    marginTop: switchMerchant ? 10 : 20,
                                                    flexDirection: 'row',
                                                    width: windowWidth * 0.35,
                                                }}>
                                                <View style={{ justifyContent: 'center', width: '40%' }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                color: 'black',
                                                                fontFamily: 'NunitoSans-Bold',
                                                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 16 : 20,
                                                            },
                                                        ]}>
                                                        Tax Rate
                                                    </Text>
                                                </View>
                                                <View style={{ justifyContent: 'center', width: '60%' }}>
                                                    <TextInput
                                                        placeholder="Tax Rate"
                                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                        style={[
                                                            {
                                                                backgroundColor: Colors.fieldtBgColor,
                                                                width: '100%',
                                                                height: 40,
                                                                borderRadius: 5,
                                                                padding: 5,
                                                                marginVertical: 5,
                                                                borderWidth: 1,
                                                                borderColor: '#E5E5E5',
                                                                paddingLeft: 10,
                                                            },
                                                        ]}
                                                        selectTextOnFocus
                                                        onChangeText={(text) => {
                                                            setCustomTaxRate(text);
                                                        }}
                                                        keyboardType={'default'}
                                                        defaultValue={String(customTaxRate)}
                                                    />
                                                </View>
                                            </View>
                                        </View>

                                        <View
                                            style={[{
                                                flexDirection: 'row',
                                                // alignItems: 'center',
                                                justifyContent: 'center',
                                                marginTop: 35,
                                            }]}>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                onPress={() => {
                                                    if (selectedCustomTax.uniqueId === undefined) {
                                                        createCustomTax();
                                                    } else {
                                                        updateCustomTax();
                                                    }
                                                }}
                                                style={{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: windowWidth * 0.2,
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    alignContent: 'center',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60,
                                                    borderBottomLeftRadius: switchMerchant ? windowWidth * 0.25 : 25,
                                                    borderRightWidth: StyleSheet.hairlineWidth,
                                                    borderTopWidth: StyleSheet.hairlineWidth,
                                                }}>
                                                {isLoading ? (
                                                    <ActivityIndicator
                                                        size={'large'}
                                                        color={Colors.primaryColor}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={[
                                                            {
                                                                fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 22,
                                                                color: Colors.primaryColor,
                                                                fontFamily: 'NunitoSans-SemiBold',
                                                            },
                                                        ]}>
                                                        Confirm
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                onPress={() => {
                                                    // setState({ visible: false });
                                                    setAddCustomTaxModal(false);
                                                }}
                                                style={[{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: windowWidth * 0.2,
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    alignContent: 'center',
                                                    height: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60,
                                                    borderBottomRightRadius: switchMerchant ? windowWidth * 0.03 : 25,
                                                    borderTopWidth: StyleSheet.hairlineWidth,
                                                },]}>
                                                <Text
                                                    style={[
                                                        {
                                                            fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 22,
                                                            color: Colors.descriptionColor,
                                                            fontFamily: 'NunitoSans-SemiBold',
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}>
                                                    Cancel
                                                </Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </KeyboardAvoidingView>
                        </ModalView>
                    </ScrollView>
                </ScrollView>
            </View>
        </UserIdleWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    content: {
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        padding: 20,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    submitText: {
        height:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.06
                : Dimensions.get('window').height * 0.05,
        left: 295,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
export default SettingTaxScreen;
