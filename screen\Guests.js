import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Modal,
  PermissionsAndroid,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Switch from 'react-native-switch-pro';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import RNPickerSelect from 'react-native-picker-select';
// import { Picker } from '@react-native-picker/picker';
// import { ceil } from 'react-native-reanimated';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from 'react-native-modal-datetime-picker';
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'rn-fetch-blob';
import {
  isTablet
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { v4 as uuidv4 } from 'uuid';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import {
  PURCHASE_ORDER_STATUS,
  PURCHASE_ORDER_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  USER_ORDER_STATUS,
  ORDER_TYPE,
  USER_RESERVATION_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import {
  convertArrayToCSV,
  uploadImageToFirebaseStorage,
  generateEmailReport,
  listenToSelectedCustomerChangesMerchant,
} from '../util/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import {
//   Table,
//   TableWrapper,
//   Row,
//   Rows,
//   Col,
//   Cols,
//   Cell,
// } from 'react-native-table-component';
import { Platform } from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);
import XLSX from 'xlsx';
const RNFS = require('@dr.pogodin/react-native-fs');
import { writeFile, readFile, DocumentDirectoryPath } from '@dr.pogodin/react-native-fs';
import AsyncImage from '../components/asyncImage';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import Footer from './footer';

const alphabets = [
  { alphabet: 'A' },
  { alphabet: 'B' },
  { alphabet: 'C' },
  { alphabet: 'D' },
  { alphabet: 'E' },
  { alphabet: 'F' },
  { alphabet: 'G' },
  { alphabet: 'H' },
  { alphabet: 'I' },
  { alphabet: 'J' },
  { alphabet: 'K' },
  { alphabet: 'L' },
  { alphabet: 'M' },
  { alphabet: 'N' },
  { alphabet: 'O' },
  { alphabet: 'P' },
  { alphabet: 'Q' },
  { alphabet: 'R' },
  { alphabet: 'S' },
  { alphabet: 'T' },
  { alphabet: 'U' },
  { alphabet: 'V' },
  { alphabet: 'W' },
  { alphabet: 'X' },
  { alphabet: 'Y' },
  { alphabet: 'Z' },
];

const Guests = (props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [userTagList, setUserTagList] = useState([]);

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [search, setSearch] = useState('');

  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const selectedCustomerDineInOrders = OutletStore.useState(
    (s) => s.selectedCustomerDineInOrders,
  );
  // const userOrders = OutletStore.useState((s) => s.userOrders);
  const allOutletsUserOrdersDone = OutletStore.useState(
    (s) => s.allOutletsUserOrdersDone,
  );
  const userReservations = OutletStore.useState((s) => s.userReservations);

  const selectUserDetails = CommonStore.useState((s) => s.selectUserDetails);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const selectedGuest = CommonStore.useState((s) => s.selectedGuestEdit);

  const userName = UserStore.useState((s) => s.name);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Guests
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const renderCrmData = ({ item, index }) => {
    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(item.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(item.email) ||
          crmUserTags[i].phoneList.includes(item.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }

    var tempOrderDate = null;
    var tempOrder = allOutletsUserOrdersDone.filter((order) => {
      if (
        order.userId === item.userId &&
        order.orderType === ORDER_TYPE.DINEIN
      ) {
        // console.log('asd');
        return true;
      }
    });
    // listenToSelectedCustomerChangesMerchant(item, currOutletId);
    if (tempOrder[0]) {
      tempOrderDate = moment(tempOrder[0].orderDate).format('DD MMM YYYY');
    }

    var tempUserLastReservations = null;
    var tempUserNextReservations = null;
    var tempReservations = userReservations.filter(
      (reservation) =>
        reservation.userId === item.userId &&
        reservation.status !== USER_RESERVATION_STATUS.CANCELED,
    );
    if (tempReservations.length > 0) {
      if (moment().isSameOrAfter(tempReservations[0].reservationTime)) {
        tempUserLastReservations = tempReservations[0].reservationTime;
      } else if (moment().isBefore(tempReservations[0].reservationTime)) {
        tempUserNextReservations = tempReservations[0].reservationTime;
      }
    }

    var tempNoShow = false;
    var tempReservationNoShow = userReservations.filter(
      (reservation) =>
        reservation.userId === item.userId &&
        reservation.status === USER_RESERVATION_STATUS.NO_SHOW,
    );
    if (tempReservationNoShow.length > 0) {
      tempNoShow = true;
    }
    return (
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('CreateGuests')
          // // console.log('here', item);
          // // console.log('here', tempOrder);
          // // console.log('here1', selectedCustomerDineInOrders);

          CommonStore.update((s) => {
            s.selectedGuestEdit = item;
          });

          props.navigation.navigate('CreateGuests')
        }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            borderBottomLeftRadius: 5,
            borderBottomRightRadius: 5,
            marginLeft: 5,
            minHeight: windowHeight * 0.1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 5,
          }}>
          <View
            style={{
              flex: 1.5,
              flexDirection: 'row',
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            {item.avatar ? (
              <AsyncImage
                style={{
                  width: switchMerchant ? 30 : 40,
                  height: switchMerchant ? 30 : 40,
                  alignSelf: 'center',
                  borderRadius: 100,
                  marginHorizontal: 5,
                }}
                source={{
                  uri: item.avatar,
                }}
                item={item}
                hideLoading={true}
              />
            ) : (
              <Image
                style={{
                  width: switchMerchant ? 30 : 40,
                  height: switchMerchant ? 30 : 40,
                  alignSelf: 'center',
                  borderRadius: 100,
                  marginHorizontal: 5,
                }}
                source={require('../assets/image/profile-pic.jpg')}
                hideLoading={true}
              />
            )}
            <Text
              style={{
                color: Colors.primaryColor,
                // fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {`${item.name}\n${item.number}`}
            </Text>
          </View>
          <View
            style={{
              flex: 1,
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
              // overflow: 'hidden',
              alignItems: 'center',
              justifyContent: 'center',
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            <Text>{userTagListTemp.length > 0 ? '' : '-'}</Text>
            {userTagListTemp.map((item, index) => {
              return (
                <Text
                  style={{
                    // fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    color: Colors.primaryColor,
                    borderRadius: 5,
                    maxWidth: '80%',
                    margin: 3,
                  }}>
                  {`${item.name}`}
                </Text>
              );
            })}
          </View>
          <View
            style={{
              flex: 1,
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            <Text
              style={{
                //   fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {`${item.guestNotes || '-'}`}
            </Text>
          </View>
          <View
            style={{
              flex: 0.7,
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            <Text
              style={{
                //   fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {`Past: ${tempUserLastReservations
                ? moment(tempUserLastReservations).format('DD MMM YYYY')
                : 'None'
                }\nNext: ${tempUserNextReservations
                  ? moment(tempUserNextReservations).format('DD MMM YYYY')
                  : 'None'
                }`}
            </Text>
          </View>
          <View
            style={{
              flex: 0.7,
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            <Text
              style={{
                //   fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {tempOrderDate
                ? moment(tempOrderDate).format('DD MMM YYYY')
                : 'N/A'}
            </Text>
          </View>
          <View
            style={{
              flex: 0.5,
              // borderRightColor: Colors.lightGrey,
              // borderRightWidth: 1,
            }}>
            <Text
              style={{
                //   fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              {tempNoShow ? 'No Show' : '-'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = ({ item, index }) => {
    var alphabet = item.alphabet.toLowerCase();
    return (
      <View>
        {/* Header */}
        <View
          style={{
            marginTop: 10,
            marginLeft: 5,
            //backgroundColor: Colors.lightGrey,
            height: 60,
            flexDirection: 'row',
            borderTopLeftRadius: 10,
            borderTopRightRadius: 10,
            borderBottomWidth: StyleSheet.hairlineWidth,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View style={{ flex: 1.5, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'flex-start',
                fontFamily: 'NunitoSans-Bold',
                marginLeft: 10,
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              {`${item.alphabet}`}
            </Text>
          </View>
          <View style={{ flex: 1, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              Guest Tags
            </Text>
          </View>
          <View style={{ flex: 1, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              Guest Notes
            </Text>
          </View>
          <View style={{ flex: 0.7, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              Reservations
            </Text>
          </View>
          <View style={{ flex: 0.7, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              Last Visit
            </Text>
          </View>
          <View style={{ flex: 0.5, justifyContent: 'flex-start' }}>
            <Text
              style={{
                alignSelf: 'center',
                fontFamily: 'NunitoSans-Bold',
                // fontSize: switchMerchant ? 10 : 14,
              }}>
              No-Show
            </Text>
          </View>
        </View>
        {/* Body FlatList        */}
        <FlatList
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
          renderItem={renderCrmData}
          data={crmUsers.filter((item) => {
            var tempFirst = item.name.split('');
            if (
              tempFirst[0] &&
              tempFirst[0].toString().toLowerCase() === alphabet
            ) {
              if (search !== '') {
                return item.name.toLowerCase().includes(search.toLowerCase());
              } else {
                return true;
              }
            }
          })}
        />
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      {/* Sidebar */}
      <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: windowWidth * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        {/* <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        /> */}
      </View>
      <View
        style={{
          flex: 1,
          position: 'absolute',
          bottom: Platform.OS == 'ios' ? windowHeight * 0.075 : windowHeight * 0.1,
        }}>
        <Footer />
      </View>

      <View>
        {/* Search bar and Create guest button */}
        <View style={switchMerchant ?
          { flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, justifyContent: 'flex-end', width: '100%' }
          :
          { flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, justifyContent: 'flex-end', width: windowWidth * 0.87, alignSelf: 'center', marginHorizontal: 30, marginTop: 16 }
        }>
          <View
            style={{
              //flex: 1,
            }}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.primaryColor,
                backgroundColor: '#4E9F7D',
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
                marginHorizontal: 10,
              }}
              onPress={() => {
                CommonStore.update((s) => {
                  s.selectedGuestEdit = null;
                });
                props.navigation.navigate('CreateGuests');

              }}>
              <Text
                style={{
                  color: Colors.whiteColor,
                  marginLeft: 5,
                  fontSize: switchMerchant ? 10 : 16,
                  textAlign: 'center',
                  fontFamily: 'NunitoSans-Bold',
                }}>
                CREATE GUEST
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: switchMerchant ? 200 : 250,
              height: switchMerchant ? 35 : 40,
              backgroundColor: 'white',
              borderRadius: 5,
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
              borderWidth: 1,
              borderColor: '#E5E5E5',
            }}>
            <Icon
              name="search"
              size={switchMerchant ? 13 : 18}
              color={Colors.primaryColor}
              style={{ marginLeft: 15 }}
            />
            <TextInput
              // editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              style={{
                fontFamily: 'NunitoSans-Regular',
                paddingLeft: 5,
                height: 45,
              }}
              clearButtonMode="while-editing"
              placeholder=" Search"
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              onChangeText={(text) => {
                // setSearch(text.trim());
                setSearch(text);
              }}
              value={search}
            />
          </View>
        </View>

        <ScrollView>
          {/* Guest FlatList */}
          <View style={{ flexDirection: 'row' }}>
            {/* <View
                    style={{
                        // backgroundColor: '#ffffff',
                        height: 60,
                        width: 800,
                        flexDirection: 'row',
                        paddingVertical: 20,
                        paddingHorizontal: 15,
                        borderRadius:10,
                        //marginTop: 10,
                        borderTopLeftRadius: 5,
                        borderTopRightRadius: 5,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                        style={{
                        width: '24%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        A
                    </Text>
                    <Text
                        style={{
                        width: '22%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Guest Tags
                    </Text>
                    <Text
                        style={{
                        width: '25%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Guest Notes
                    </Text>
                    <Text
                        style={{
                        width: '17%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Reservations
                    </Text>
                    <Text
                        style={{
                        width: '17%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        Last Visit
                    </Text>
                    <Text
                        style={{
                        width: '17%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 3,
                        // fontSize: switchMerchant ? 10 : 14,
                        }}>
                        No-Show
                    </Text>
                </View> */}
          </View>
          <FlatList
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
            renderItem={renderHeader}
            style={{
              backgroundColor: Colors.whiteColor,
              width: switchMerchant
                ? windowWidth * 0.8
                : windowWidth * 0.87,
              height: Platform.OS == 'ios' ? windowHeight * 0.74 : windowHeight * 0.65,
              minHeight: windowHeight * 0.01,
              marginTop: 15,
              marginHorizontal: switchMerchant ? 0 : 30,
              marginBottom: 0,
              alignSelf: 'center',
              borderRadius: 5,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}
            data={alphabets.filter((item) => {
              var alphabet = item.alphabet.toLowerCase();
              var ret = crmUsers.filter((item) => {
                var tempFirst = item.name.split('');
                if (
                  tempFirst[0] &&
                  tempFirst[0].toString().toLowerCase() === alphabet
                ) {
                  return true;
                }
              });
              if (ret.length > 0) {
                if (search !== '') {
                  return item.alphabet
                    .toLowerCase()
                    .includes(search[0].toLowerCase());
                } else {
                  return true;
                }
              }
            })}
            keyExtractor={(item, index) => String(index)}
          />
        </ScrollView>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    // flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    height: Dimensions.get('window').height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  topBar: {
    flexDirection: 'row',
    height: Dimensions.get('window').height * 0.05,
    width: Dimensions.get('window').width * 0.91,
    backgroundColor: Colors.lightGrey,
    justifyContent: 'flex-start',
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },

  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 350,
    height: 50,
    //flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },

  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'flex-end',
    //   justifyContent: 'flex-end',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.3,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'flex-start',
    //   justifyContent: 'flex-start',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 24,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: 130,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  ManageFilterBox: {
    //width: windowWidth * 0.27,
    //height: Platform.OS === 'ios' ?windowHeight * 0.23: windowHeight * 0.24,
    //width: Platform.OS === 'ios' ? windowWidth * 0.4 : windowWidth * 0.33,
    height:
      Platform.OS === 'ios' ? 180 : Dimensions.get('window').height * 0.24,
    width: Platform.OS === 'ios' ? 400 : Dimensions.get('window').width * 0.33,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    position: 'absolute',
    marginTop: Platform.OS === 'ios' ? '13%' : '13%',
    marginLeft: Platform.OS === 'ios' ? '12%' : '12%',
    //left: Platform.OS === 'ios' ? '38%' : '0%',
    //top: Platform.OS === 'ios' ? '46%' : '0%',
    shadowColor: '#000',
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.32,
    shadowRadius: 3.22,
    elevation: 10,
    zIndex: 1000,
    borderRadius: 10,
    //borderTopLeftRadius: 0,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  tierDropdown: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 10,
    // alignItems: 'center',
    borderRadius: 10,
    // justifyContent: 'center',
    backgroundColor: Colors.whiteColor,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: 1,
  },
});

export default Guests;
