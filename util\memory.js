import { AppState } from 'react-native';
import { logToFile } from './common';
import FastImage from 'react-native-fast-image';

export class MemoryMonitor {
    constructor() {
        this.memoryWarningThreshold = 150 * 1024 * 1024; // 150MB
        this.checkInterval = 60000 * 5; // Check every 60 seconds
        this.intervalId = null;
    }

    start() {
        this.intervalId = setInterval(() => this.checkMemoryUsage(), this.checkInterval);
        AppState.addEventListener('change', this.handleAppStateChange);
    }

    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        AppState.removeEventListener('change', this.handleAppStateChange);
    }

    handleAppStateChange = (nextAppState) => {
        if (nextAppState === 'active') {
            // App came to foreground, force garbage collection
            // global.gc && global.gc();

            if (!global.isPrintingNow &&
                !global.isReconnectingToTimeoutPrinter &&
                global.currOutlet &&
                global.currOutlet.gcAuto
            ) {
                if (typeof global !== 'undefined' && typeof global.gc === 'function') {
                    global.gc();
                    // console.log('Garbage collection suggested');
                    logToFile('gc');
                    return true;
                }
            }
        }
    }

    checkMemoryUsage() {
        // const memoryUsage = performance.memory.usedJSHeapSize;

        // if (memoryUsage > this.memoryWarningThreshold) {
        //     // console.warn('Memory usage high:', memoryUsage);
        //     logToFile(`memory usage high: ${memoryUsage}`);
        //     // Take action - clear caches, reduce rendered items, etc.
        //     this.reduceCacheSize();
        //     // Request garbage collection if exposed
        //     // global.gc && global.gc();

        //     if (typeof global !== 'undefined' && typeof global.gc === 'function') {
        //         global.gc();
        //         // console.log('Garbage collection suggested');
        //         logToFile('gc');
        //         return true;
        //     }
        // }

        if (!global.isPrintingNow &&
            !global.isReconnectingToTimeoutPrinter &&
            global.currOutlet &&
            global.currOutlet.gcAuto
        ) {
            this.reduceCacheSize();
            if (typeof global !== 'undefined' && typeof global.gc === 'function') {
                global.gc();
                // console.log('Garbage collection suggested');
                logToFile('gc');
                return true;
            }
        }
    }

    async reduceCacheSize() {
        // Implement cache reduction logic

        // 1. Clear FastImage cache - often a major contributor to memory issues
        try {
            await FastImage.clearDiskCache();
            await FastImage.clearMemoryCache();
            logToFile('✓ cleared FastImage cache');
        } catch (error) {
            logToFile('failed to clear FastImage cache:', error.message);
        }
    }
}
