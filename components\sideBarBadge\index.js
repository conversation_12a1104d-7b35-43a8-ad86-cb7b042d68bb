import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import { ORDER_TYPE, ORDER_TYPE_SUB, USER_ORDER_STATUS } from '../../constant/common';
import { OutletStore } from '../../store/outletStore';
import { MerchantStore } from '../../store/merchantStore';

const SideBarBadge = ({ orderType }) => {
    const [takeawayCount, setTakeawayCount] = useState(0);
    const [dineInCount, setDineInCount] = useState(0);

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const userOrders = OutletStore.useState((s) => s.userOrders);

    useEffect(() => {
        if (!userOrders) return;

        // Debugging: Log user orders to check updates
        console.log("Updated userOrders:", userOrders);

        // Count active takeaway orders (remain until completed or canceled)
        const activeTakeawayOrders = userOrders.filter((order) =>
            (order.orderType === ORDER_TYPE.PICKUP && order.orderTypeSub === ORDER_TYPE_SUB.NORMAL) &&
            // ![
            //     USER_ORDER_STATUS.ORDER_DELIVERED, // Completed
            //     USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,  // Canceled
            // ].includes(order?.orderStatus)
            // (order.orderStatus !== USER_ORDER_STATUS.ORDER_AUTHORIZED &&
            //     order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARING &&
            //     order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
            //     order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED)
            (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED)
        ).length;

        // Count active dine-in orders (remain until completed or canceled)
        const activeDineInOrders = userOrders.filter((order) =>
            (currOutlet?.dineInRequiredAuthorization &&
                order.orderType === ORDER_TYPE.DINEIN && order.orderTypeSub === ORDER_TYPE_SUB.NORMAL) &&
            // ![
            //     USER_ORDER_STATUS.ORDER_DELIVERED, // Completed
            //     USER_ORDER_STATUS.ORDER_CANCELED,  // Canceled
            // ].includes(order?.orderStatus)
            (
                // order.orderType === ORDER_TYPE.DINEIN &&
                order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
            )
        ).length;

        console.log("Active Takeaway Orders:", activeTakeawayOrders);
        console.log("Active Dine-In Orders:", activeDineInOrders);

        setTakeawayCount(activeTakeawayOrders);
        setDineInCount(activeDineInOrders);

    }, [userOrders]); // Update badge when orders change

    return (
        <>
            {orderType === ORDER_TYPE.PICKUP && takeawayCount > 0 && (
                <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>
                        {takeawayCount > 99 ? '99+' : takeawayCount}
                    </Text>
                </View>
            )}

            {orderType === ORDER_TYPE.DINEIN && dineInCount > 0 && (
                <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>
                        {dineInCount > 99 ? '99+' : dineInCount}
                    </Text>
                </View>
            )}
        </>
    );
};

const styles = {
    badgeContainer: {
        position: 'absolute',
        top: -10,
        right: -18,
        backgroundColor: 'red',
        borderRadius: 15,
        height: 18,
        width: 18,
        justifyContent: 'center',
        alignItems: 'center',
    },
    badgeText: {
        color: 'white',
        fontSize: 8,
        fontWeight: 'bold',
    },
};

export default SideBarBadge;
