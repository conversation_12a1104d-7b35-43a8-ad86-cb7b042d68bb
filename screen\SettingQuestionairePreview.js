import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Ionicons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import {
  isTablet
} from '../util/common';
import { MERCHANT_VOUCHER_CODE_FORMAT, MERCHANT_VOUCHER_TYPE, SEGMENT_TYPE, EXPAND_TAB_TYPE, } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { get } from 'react-native/Libraries/Utilities/PixelRatio';

/////////////////////////////////////////////////////////////////////////////////////
const QUESTIONAIRE_OPTIONS = {
  SINGLE_CHOICE: 'SINGLE_CHOICE',
  MULTIPLE_CHOICE: 'MULTIPLE_CHOICE',
  REMARK: 'REMARK',
};

// const QUESTIONAIRE_OPTIONS_DROPDOWN_LIST = [
//   {
//     label: 'Single Choice',
//     value: QUESTIONAIRE_OPTIONS.SINGLE_CHOICE,
//   },
//   {
//     label: 'Multiple Choice',
//     value: QUESTIONAIRE_OPTIONS.MULTIPLE_CHOICE,
//   },
//   {
//     label: 'Remark',
//     value: QUESTIONAIRE_OPTIONS.REMARK,
//   }
// ];

/////////////////////////////////////////////////////////////////////////////////////

const SettingQuestionairePreview = props => {
  const {
    navigation,
  } = props;

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [questionaireOption, setQuestionaireOption] = useState(QUESTIONAIRE_OPTIONS.SINGLE_CHOICE); //questionaire choices

  const [questionaireSpecify, setQuestionaireSpecify] = useState([]); //Specify  (for customer to fill in only)
  const [questionaireRemark, setQuestionaireRemark] = useState(''); //Remark (for customer to fill in)

  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  //////////////////////////////////////////////////////////////////////////////////////////////////////////


  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });


  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            textAlign: 'left',
            fontSize: switchMerchant ? 20 : 24,
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Credit
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });
  ///////////////////////////////////////////////////////////////////////////////////////
  ///////Publish Questionaire//////////
  const publichQuestionaireFunc = () => {

  }

  /////////Render Questionaire Create by SettingQuestionaireScreen///////
  const renderQuestion = ({ item }) => {

    return (
      <View>
        <View style={{ flexDirection: 'row', marginBottom: 5 }}>
          <Text style={{ fontSize: 17, fontWeight: '600', color: Colors.fieldtTxtColor }}>
            {/* {index + 1} */}
            1.{' '}
          </Text>
          <Text style={{ fontSize: 17, fontWeight: '600', color: Colors.fieldtTxtColor }}>
            {/* {questionaireQuestion} */}
            Is the portion of food big enough?
          </Text>
        </View>

        {questionaireOption === QUESTIONAIRE_OPTIONS.SINGLE_CHOICE ?
          // Single Choice 
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <DropDownPicker
              style={{ width: 200, height: 40, paddingVertical: 0 }}
              dropDownStyle={{}}
              placeholder={'Choose an answer'}
              arrowSize={20}
              arrowStyle={{ paddingVertical: 0 }}
              itemStyle={{}}
              items={[{ label: 'Yes', value: 'Yes', }, { label: 'No', value: 'No', }]}
            />
            <Text style={{ marginHorizontal: 10, marginLeft: 15, fontWeight: '600', color: Colors.fieldtTxtColor, fontSize: 16 }}>
              Specify:
            </Text>
            <TextInput
              style={{ borderColor: '#E5E5E5', borderWidth: 1, borderRadius: 5, width: 180, height: 35, paddingLeft: 5, }}
              placeholder={'Specify'}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                //setQuestionaireSpecify([text]) 
              }}
            />
          </View>
          : <></>}

        {questionaireOption === QUESTIONAIRE_OPTIONS.MULTIPLE_CHOICE ?
          // Multiple Choice
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <DropDownPicker
              style={{ width: 200, height: 40, paddingVertical: 0 }}
              dropDownStyle={{}}
              placeholder={'Choose an answer'}
              arrowSize={20}
              arrowStyle={{ paddingVertical: 0 }}
              itemStyle={{}}
              items={[{ label: 'Yes', value: 'Yes', }, { label: 'No', value: 'No', }]}
              customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
            //multiple={true}
            />
            <Text style={{ marginHorizontal: 10, marginLeft: 15, fontWeight: '600', color: Colors.fieldtTxtColor, fontSize: 16 }}>
              Specify:
            </Text>
            <TextInput
              style={{ borderColor: '#E5E5E5', borderWidth: 1, borderRadius: 5, width: 180, height: 35, paddingLeft: 5, }}
              placeholder={'Specify'}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                //setQuestionaireSpecify([text]) 
              }}
            //value={questionaireSpecify}
            />
          </View>
          : null}

        {questionaireOption === QUESTIONAIRE_OPTIONS.REMARK ?
          //Remark
          <View>
            <TextInput
              style={{ borderRadius: 5, borderWidth: 1, borderColor: '#E5E5E5', width: 280, height: 40, paddingLeft: 5 }}
              placeholder={'Remark'}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            //onChangeText={(text) => { setQuestionaireRemark(text) }}
            //value={questionaireRemark}
            />
          </View>
          : null}

      </View>
    )
  }

  ///////////////////////////////////////////////////////////////////////////////////////

  //Render Start Here
  return (
    <View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {
    }]}>
      {/* <View style={[styles.sidebar, !isTablet() ? {
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={10} expandSettings={true} />
      </View> */}

      <View style={styles.list}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 20,
          marginBottom: 5,
          marginTop: 5,
          //backgroundColor: 'red',
          height: '10%',
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          paddingHorizontal: 10,
        }}>
          <View style={{ width: '55%', justifyContent: 'center' }}>
            <Text style={{ fontFamily: 'Nunitosans-bold', fontSize: 18, }}>
              Questionaire (Preview)
            </Text>
          </View>
          <TouchableOpacity style={{ width: 100, height: 35, borderRadius: 7, backgroundColor: Colors.primaryColor, justifyContent: 'center' }}
            onPress={() => { publichQuestionaireFunc() }}
          >
            <View style={{ flexDirection: 'row', paddingHorizontal: 10, justifyContent: 'center', }}>
              <Text style={{ textAlign: 'center', fontFamily: 'Nunitosans-Bold', color: Colors.whiteColor, fontSize: 16 }}>
                Publish
              </Text>
            </View>
          </TouchableOpacity>

        </View>
        <View style={{ flexDirection: 'row', marginHorizontal: 10, }}>
          <TouchableOpacity style={{
            flexDirection: 'row',
            justifyContent: 'center',
            width: '10%',
            height: 35,
            alignItems: 'center',
            alignSelf: 'center',
            marginLeft: 15,
          }}
            onPress={() => { props.navigation.goBack() }}
          >
            <View style={{ height: '100%', justifyContent: 'center', alignItems: 'center', color: Colors.primaryColor }}>
              <Feather name='chevron-left' size={25} color={Colors.primaryColor} />
            </View>
            <View style={{}}>
              <Text style={{ color: Colors.primaryColor, fontSize: 15 }}>
                Back to edit
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <View style={{
          flexDirection: 'column',
          borderWidth: 1,
          borderColor: '#c4c4c4',
          borderRadius: 3,
          alignSelf: 'center',
          width: Dimensions.get('screen').width * 0.5,
          padding: 20,
          minHeight: Dimensions.get('screen').height * 0.5,
        }}>

          <FlatList
            data={renderQuestion}
            renderItem={renderQuestion}
            keyExtractor={(item, index) => String(index)}
            style={{ marginVertical: 5 }}
          />

        </View>
      </View>








    </View>
  )

};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('screen').width * 0.85,
    height: Dimensions.get('screen').height,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold'
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default SettingQuestionairePreview;
