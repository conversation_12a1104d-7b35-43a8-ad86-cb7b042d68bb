/**
 * If connection is successful but no output is printed without any error, try restarting the printer.
 */
import TcpSocket from 'react-native-tcp-socket';

class TscPrinterService{
  constructor(host, port = 9100) {
    this.host = host;
    this.port = port;
    this.client = null;
    this.connectionTimeout = 10000; // 10 Seconds Connection Timeout
  }

  //=======================
  // Connection Management
  //=======================

  connect() {
    return new Promise((resolve, reject) => {
      if (this.client) {
        resolve();
        return;
      }

      if (!this.host || !this.port) {
        reject(new Error('Invalid host or port'));
        return;
      }

      console.log(`Connecting to printer... (Timeout: ${this.connectionTimeout / 1000} seconds)`);

      // Create Connection
      this.client = TcpSocket.createConnection({ 
        host: this.host, 
        port: this.port 
      });

      this.client.setTimeout(this.connectionTimeout);

      this.client.on('connect', () => {
        console.log('Connected to server');
        resolve();
      });

      this.client.on('error', (error) => {
        console.error('Connection error', error);
        this.disconnect();
        reject(error);
      });

      this.client.on('timeout', () => {
        console.error('Connection timed out');
        this.disconnect();
        reject(new Error('Connection timed out'));
      });

      this.client.on('close', (hadError) => {
        if (hadError) {
          console.error('Connection closed unexpectedly');
        } else {
          console.log('Connection closed');
        }
        this.disconnect();
      });

      this.client.on('data', (data) => {
        console.log('Received data from printer:', data.toString());
      });
    });
  }

  disconnect() {
    return new Promise((resolve) => {
      if (!this.client) {
        resolve();
      }

      this.client.destroy();
      this.client = null;
      resolve();
    });
  }

  //=======================
  // Command Execution
  //=======================

  sendCommand(command) {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Not connected to printer'));
      }
      
      console.log('Sending command to printer:');
      console.log(command);

      this.client.write(command, 'utf-8', (err) => {
        if (err) {
          reject(err);
        }
        else {
          console.log('Command sent to printer');
          resolve();
        }
      });
    });
  }

  //=======================
  // Utility Methods
  //=======================

  isConnected() {
    return this.client !== null && !this.client.destroyed;
  }
}

export default TscPrinterService;
