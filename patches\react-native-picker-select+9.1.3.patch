diff --git a/node_modules/react-native-picker-select/src/index.js b/node_modules/react-native-picker-select/src/index.js
index 7ae6730..8199acf 100644
--- a/node_modules/react-native-picker-select/src/index.js
+++ b/node_modules/react-native-picker-select/src/index.js
@@ -3,9 +3,12 @@ import isEqual from 'lodash.isequal';
 import isObject from 'lodash.isobject';
 import PropTypes from 'prop-types';
 import React, { PureComponent } from 'react';
-import { Keyboard, Modal, Platform, Text, TextInput, TouchableOpacity, View } from 'react-native';
+import { Keyboard, Modal as ModalComponent, Platform, Text, TextInput, TouchableOpacity, View } from 'react-native';
+import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
 import { defaultStyles } from './styles';
 
+const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;
+
 export default class RNPickerSelect extends PureComponent {
   static propTypes = {
     onValueChange: PropTypes.func.isRequired,
@@ -449,7 +452,7 @@ export default class RNPickerSelect extends PureComponent {
         >
           {this.renderTextInputOrChildren()}
         </TouchableOpacity>
-        <Modal
+        <ModalView
           testID="ios_modal"
           visible={showPicker}
           transparent
@@ -483,7 +486,7 @@ export default class RNPickerSelect extends PureComponent {
               {this.renderPickerItems()}
             </Picker>
           </View>
-        </Modal>
+        </ModalView>
       </View>
     );
   }
