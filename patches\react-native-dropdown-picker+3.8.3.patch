diff --git a/node_modules/react-native-dropdown-picker/src/index.js b/node_modules/react-native-dropdown-picker/src/index.js
index 5650618..fe0c855 100644
--- a/node_modules/react-native-dropdown-picker/src/index.js
+++ b/node_modules/react-native-dropdown-picker/src/index.js
@@ -4,10 +4,11 @@ import {
     Text,
     View,
     TouchableOpacity,
-    ScrollView,
+    // ScrollView,
     Platform,
     TextInput
 } from 'react-native';
+import { ScrollView } from 'react-native-gesture-handler';
 
 // Icon
 import Feather from 'react-native-vector-icons/Feather';
@@ -20,7 +21,7 @@ class DropDownPicker extends React.Component {
         let choice;
         let items = [];
         let defaultValueIndex; // captures index of first defaultValue for initial scrolling
-        if (! props.multiple) {
+        if (!props.multiple) {
             if (props.defaultValue || props.defaultValue === 0) {
                 choice = props.items.find(item => item.value === props.defaultValue);
             } else if (props.items.filter(item => item.hasOwnProperty('selected') && item.selected === true).length > 0) {
@@ -31,6 +32,9 @@ class DropDownPicker extends React.Component {
             defaultValueIndex = props.items.findIndex(item => item.value === props.defaultValue);
         } else {
             if (props.defaultValue && Array.isArray(props.defaultValue) && props.defaultValue.length > 0) {
+                // console.log('set choice 2 [a]');
+                // console.log(props.items);
+                // console.log(props.defaultValue);
                 props.defaultValue.forEach((value, index) => {
                     items.push(
                         props.items.find(item => item.value === value)
@@ -42,6 +46,23 @@ class DropDownPicker extends React.Component {
             defaultValueIndex = props.items.findIndex(item => item.value === props.defaultValue[0]);
         }
 
+        // console.log('set choice 2 [b] [checking]');
+        // console.log(items);
+
+        if (items.includes(undefined) ||
+            items.find(value => value === undefined)) {
+            // console.log('set choice 2 [b.i]');
+
+            items = [];
+        }
+
+        // console.log('set choice 2');
+        // console.log(props.multiple ? items : {
+        //     label: choice.label,
+        //     value: choice.value,
+        //     icon: choice.icon
+        // });
+
         this.state = {
             choice: props.multiple ? items : {
                 label: choice.label,
@@ -63,12 +84,18 @@ class DropDownPicker extends React.Component {
 
     static getDerivedStateFromProps(props, state) {
         // Change default value (! multiple)
-        if (! state.props.multiple && props.defaultValue !== state.props.defaultValue) {
+        if (!state.props.multiple && props.defaultValue !== state.props.defaultValue) {
             const { label, value, icon } = props.defaultValue === null ? {
                 label: null,
                 value: null,
-                icon: () => {}
+                icon: () => { }
             } : props.items.find(item => item.value === props.defaultValue);
+
+            // console.log('set choice 4 [return]');
+            // console.log({
+            //     label, value, icon
+            // });
+
             return {
                 choice: {
                     label, value, icon
@@ -91,12 +118,19 @@ class DropDownPicker extends React.Component {
                 });
             }
 
+            if (items.includes(undefined) ||
+                items.find(value => value === undefined)) {
+                // console.log('set choice 2 [b.i]');
+
+                items = [];
+            }
+
             return {
-               choice: items,
-               props: {
-                   ...state.props,
-                   defaultValue: props.defaultValue
-               } 
+                choice: items,
+                props: {
+                    ...state.props,
+                    defaultValue: props.defaultValue
+                }
             }
         }
 
@@ -140,7 +174,7 @@ class DropDownPicker extends React.Component {
                     y: this.dropdownCoordinates[this.state.defaultValueIndex],
                     animated: true,
                 });
-                this.setState({initialScroll: false});
+                this.setState({ initialScroll: false });
             }, 200);
         }
     }
@@ -154,20 +188,20 @@ class DropDownPicker extends React.Component {
         return {
             label: null,
             value: null,
-            icon: () => {}
+            icon: () => { }
         }
     }
 
     toggle() {
         this.setState({
-            isVisible: ! this.state.isVisible,
+            isVisible: !this.state.isVisible,
         }, () => {
             const isVisible = this.state.isVisible;
             if (isVisible) {
-        		this.open(false);
-        	} else {
-        		this.close(false);
-        	}
+                this.open(false);
+            } else {
+                this.close(false);
+            }
         });
     }
 
@@ -213,7 +247,7 @@ class DropDownPicker extends React.Component {
         });
     }
 
-    removeItem(value, {changeDefaultValue = true} = {}) {
+    removeItem(value, { changeDefaultValue = true } = {}) {
         const items = [...this.props.items].filter(item => item.value !== value);
         this.setPropState({
             items
@@ -234,7 +268,7 @@ class DropDownPicker extends React.Component {
         });
     }
 
-    setPropState(data, callback = () => {}) {
+    setPropState(data, callback = () => { }) {
         this.props.onChangeList(data.items, callback);
     }
 
@@ -244,13 +278,13 @@ class DropDownPicker extends React.Component {
 
     open(setState = true) {
         this.setState({
-            ...(setState && {isVisible: true})
+            ...(setState && { isVisible: true })
         }, () => this.props.onOpen());
     }
 
     close(setState = true) {
         this.setState({
-            ...(setState && {isVisible: false}),
+            ...(setState && { isVisible: false }),
             searchableText: null
         }, () => this.props.onClose());
     }
@@ -278,7 +312,14 @@ class DropDownPicker extends React.Component {
 
     select(item) {
         const { multiple } = this.state.props;
-        if (! multiple) {
+        if (!multiple) {
+            // console.log('set choice 3');
+            // console.log({
+            //     label: item.label,
+            //     value: item.value,
+            //     icon: item.icon
+            // });
+
             this.setState({
                 choice: {
                     label: item.label,
@@ -307,6 +348,9 @@ class DropDownPicker extends React.Component {
                 choice.push(item);
             }
 
+            // console.log('set choice 1');
+            // console.log(choice);
+
             this.setState({
                 choice
             });
@@ -316,7 +360,7 @@ class DropDownPicker extends React.Component {
         }
 
         // onClose callback (! multiple)
-        if (! multiple)
+        if (!multiple)
             this.props.onClose();
     }
 
@@ -343,7 +387,12 @@ class DropDownPicker extends React.Component {
     }
 
     isSelected(item) {
-      return this.state.choice.findIndex(a => a.value === item.value) > -1;
+        // console.log('dropdownpicker');
+        // console.log(item);
+        // console.log(this.state.choice);
+        // console.log(this.props);
+        // console.log('==================');
+        return this.state.choice.findIndex(a => a.value === item.value) > -1;
     }
 
     getLabel(item, selected = false) {
@@ -367,7 +416,7 @@ class DropDownPicker extends React.Component {
     render() {
         this.props.controller(this);
         const { multiple, disabled } = this.state.props;
-        const { placeholder, scrollViewProps, searchTextInputProps, renderSeperator} = this.props;
+        const { placeholder, scrollViewProps, searchTextInputProps, renderSeperator } = this.props;
         const isPlaceholderActive = this.state.choice.label === null || (Array.isArray(this.state.choice) && this.state.choice.length === 0);
         const label = isPlaceholderActive ? (placeholder) : this.getLabel(this.state.choice?.label, true);
         const placeholderStyle = isPlaceholderActive && this.props.placeholderStyle;
@@ -378,9 +427,9 @@ class DropDownPicker extends React.Component {
         return (
             <View style={[this.props.containerStyle, {
 
-              ...(Platform.OS !== 'android' && {
-                  zIndex: this.props.zIndex
-              })
+                ...(Platform.OS !== 'android' && {
+                    zIndex: this.props.zIndex
+                })
 
             }]} {...this.props.containerProps}>
                 <TouchableOpacity
@@ -398,34 +447,34 @@ class DropDownPicker extends React.Component {
                     ]}
                 >
 
-                        {this.state.choice.icon && ! multiple && this.state.choice.icon()}
-                        <Text style={[
-                            { color: '#000' }, // default label color
-                            this.props.globalTextStyle,
-                            {opacity, flex: 1}, {
-                                marginLeft: (this.props.labelStyle.hasOwnProperty('textAlign') && this.props.labelStyle.textAlign === 'left') || ! this.props.labelStyle.hasOwnProperty('textAlign') ? 5 : 0,
-                                marginRight: (this.props.labelStyle.hasOwnProperty('textAlign') && this.props.labelStyle.textAlign === 'right') ? 5 : 0,
-                            },
-                            this.state.choice.icon ?? {marginLeft: 5},
-                            this.props.labelStyle,
-                            this.state.choice.label !== null && this.props.selectedLabelStyle,
-                            placeholderStyle
-                        ]} {...this.props.labelProps}>
-                            {multiple ? (
-                                this.state.choice.length > 0 ? this.getNumberOfItems() : placeholder
-                            ) : label}
-                        </Text>
+                    {this.state.choice.icon && !multiple && this.state.choice.icon()}
+                    <Text style={[
+                        { color: '#000' }, // default label color
+                        this.props.globalTextStyle,
+                        { opacity, flex: 1 }, {
+                            marginLeft: (this.props.labelStyle.hasOwnProperty('textAlign') && this.props.labelStyle.textAlign === 'left') || !this.props.labelStyle.hasOwnProperty('textAlign') ? 5 : 0,
+                            marginRight: (this.props.labelStyle.hasOwnProperty('textAlign') && this.props.labelStyle.textAlign === 'right') ? 5 : 0,
+                        },
+                        this.state.choice.icon ?? { marginLeft: 5 },
+                        this.props.labelStyle,
+                        this.state.choice.label !== null && this.props.selectedLabelStyle,
+                        placeholderStyle
+                    ]} {...this.props.labelProps}>
+                        {multiple ? (
+                            this.state.choice.length > 0 ? this.getNumberOfItems() : placeholder
+                        ) : label}
+                    </Text>
 
                     {this.props.showArrow && (
                         <View style={[styles.arrow]}>
-                            <View style={[this.props.arrowStyle, {opacity}]}>
-                            {
-                                ! this.state.isVisible ? (
-                                    this.props.customArrowDown(this.props.arrowSize, this.props.arrowColor)
-                                ) : (
-                                    this.props.customArrowUp(this.props.arrowSize, this.props.arrowColor)
-                                )
-                            }
+                            <View style={[this.props.arrowStyle, { opacity }]}>
+                                {
+                                    !this.state.isVisible ? (
+                                        this.props.customArrowDown(this.props.arrowSize, this.props.arrowColor)
+                                    ) : (
+                                        this.props.customArrowUp(this.props.arrowSize, this.props.arrowColor)
+                                    )
+                                }
                             </View>
                         </View>
                     )}
@@ -434,35 +483,35 @@ class DropDownPicker extends React.Component {
                     styles.dropDown,
                     styles.dropDownBox,
                     this.props.dropDownStyle,
-                    ! this.state.isVisible && styles.hidden, {
+                    !this.state.isVisible && styles.hidden, {
                         top: this.state.top,
                         maxHeight: this.props.dropDownMaxHeight,
                         zIndex: this.props.zIndex
                     }
                 ]}>
                     {
-                      this.props.searchable && (
-                        <View style={{width: '100%', flexDirection: 'row'}}>
-                            <TextInput
-                                style={[styles.input, this.props.globalTextStyle, this.props.searchableStyle]}
-                                defaultValue={this.state.searchableText}
-                                placeholder={this.props.searchablePlaceholder}
-                                placeholderTextColor={this.props.searchablePlaceholderTextColor}
-                                {...searchTextInputProps}
-                                onChangeText={ (text) => {
-                                    this.setState({
-                                        searchableText: text
-                                    })
-                                    this.props.onSearch(text);
-                                    if(searchTextInputProps.onChangeText) searchTextInputProps.onChangeText(text);
-                                }}
-                            />
-                        </View>
-                      )
+                        this.props.searchable && (
+                            <View style={{ width: '100%', flexDirection: 'row' }}>
+                                <TextInput
+                                    style={[styles.input, this.props.globalTextStyle, this.props.searchableStyle]}
+                                    defaultValue={this.state.searchableText}
+                                    placeholder={this.props.searchablePlaceholder}
+                                    placeholderTextColor={this.props.searchablePlaceholderTextColor}
+                                    {...searchTextInputProps}
+                                    onChangeText={(text) => {
+                                        this.setState({
+                                            searchableText: text
+                                        })
+                                        this.props.onSearch(text);
+                                        if (searchTextInputProps.onChangeText) searchTextInputProps.onChangeText(text);
+                                    }}
+                                />
+                            </View>
+                        )
                     }
 
                     <ScrollView
-                        style={{width: '100%'}}
+                        style={{ width: '100%' }}
                         nestedScrollEnabled={true}
                         ref={ref => {
                             this.scrollViewRef = ref;
@@ -506,13 +555,13 @@ class DropDownPicker extends React.Component {
                                         <Text style={[
                                             this.props.globalTextStyle,
                                             this.props.labelStyle,
-                                                multiple ?
+                                            multiple ?
                                                 (this.isSelected(item) && this.props.activeLabelStyle) : (this.state.choice.value === item.value && this.props.activeLabelStyle)
                                             , {
-                                            ...(item.icon && {
-                                                marginHorizontal: 5
-                                            })
-                                        }]} {...this.props.labelProps}>
+                                                ...(item.icon && {
+                                                    marginHorizontal: 5
+                                                })
+                                            }]} {...this.props.labelProps}>
                                             {this.getLabel(item)}
                                         </Text>
                                     </View>
@@ -563,7 +612,7 @@ DropDownPicker.defaultProps = {
     searchableError: () => <Text>Not Found</Text>,
     searchableStyle: {},
     searchablePlaceholderTextColor: 'gray',
-    onSearch: () => {},
+    onSearch: () => { },
     isVisible: false,
     autoScrollToDefaultValue: false,
     multiple: false,
@@ -577,11 +626,11 @@ DropDownPicker.defaultProps = {
     searchTextInputProps: {},
     containerProps: {},
     globalTextStyle: {},
-    controller: () => {},
-    onOpen: () => {},
-    onClose: () => {},
-    onChangeItem: () => {},
-    onChangeList: () => {},
+    controller: () => { },
+    onOpen: () => { },
+    onClose: () => { },
+    onChangeItem: () => { },
+    onChangeList: () => { },
 };
 
 const styles = StyleSheet.create({
@@ -620,12 +669,12 @@ const styles = StyleSheet.create({
         justifyContent: 'center'
     },
     input: {
-      flex: 1,
-      borderColor: '#dfdfdf',
-      borderBottomWidth: 1,
-      paddingHorizontal: 0,
-      paddingVertical: 8,
-      marginBottom: 10,
+        flex: 1,
+        borderColor: '#dfdfdf',
+        borderBottomWidth: 1,
+        paddingHorizontal: 0,
+        paddingVertical: 8,
+        marginBottom: 10,
     },
     hidden: {
         position: 'relative',
