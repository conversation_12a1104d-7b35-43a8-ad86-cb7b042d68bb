import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal as ModalComponent,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  TouchableWithoutFeedback,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import moment from 'moment';
import Styles from '../constant/Styles';
import GCalendar from '../assets/svg/GCalendar';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import { isTablet } from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import RNPickerSelect from 'react-native-picker-select';
import 'react-native-get-random-values';
import { Calendar } from 'react-native-big-calendar';
import { v4 as uuidv4 } from 'uuid';
import { firebase } from '@react-native-firebase/storage';
import { Collections } from '../constant/firebase';
import {
  RESERVATIONS_SHIFT_TYPE,
  RESERVATION_PRIORITY,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SettingIntervalScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // get the page from asyncStorage
  const openPage = CommonStore.useState((s) => s.venueSettingPage);

  // reservation
  const [selectedReservationUniqueId, setSelectedReservationUniqueId] =
    useState('');
  const [allReservationAvailability, setAllReservationAvailability] = useState(
    [],
  );
  const [reservationThisWeek, setReservationThisWeek] = useState([]);

  // check if it is adding availability or modifying availability event
  const [isAddAvailability, setIsAddAvailability] = useState(true);

  // all floor room
  const [floorRoomList, setFloorRoomList] = useState([]);

  //reservation page useState
  const [showAddAvModal, setShowAddAvModal] = useState(false);
  const [showUpdAvModal, setShowUpdAvModal] = useState(false);
  const [isBasic, setIsBasic] = useState(false);
  const [isTimeDays, setIsTimeDays] = useState(false);
  const [isPacing, setIsPacing] = useState(false);
  const [isRoom, setIsRoom] = useState(false);
  const [isColor, setIsColor] = useState(false);
  const [showPacingModal, setShowPacingModal] = useState(false);
  const [calendarStartDate, setCalendarStartDate] = useState(
    moment().startOf('isoWeek').format('DD MMM YYYY'),
  );

  //modal basic tab
  const [basicReserveName, setBasicReserveName] = useState('');
  const [switchActive, setSwitchActive] = useState(true);
  const [basicColor, setBasicColor] = useState('');

  //modal time & days tab
  const [timeDaysDateTimePicker, setTimeDaysDateTimePicker] = useState(false);
  const [timeDaysDateTimePicker2, setTimeDaysDateTimePicker2] = useState(false);
  const [rev_date, setRev_date] = useState(moment().endOf(Date.now()).toDate());
  const [rev_date2, setRev_date2] = useState('');
  const [repeatShift, setRepeatShift] = useState('');
  const [switchDaily, setSwitchDaily] = useState(true);
  const [dailyHours1, setDailyHours1] = useState('');
  const [dailyHours2, setDailyHours2] = useState('');
  const [dailyHours3, setDailyHours3] = useState('');
  const [dailyHours4, setDailyHours4] = useState('');
  const [dailyHours5, setDailyHours5] = useState('');
  const [dailyHours6, setDailyHours6] = useState('');
  const [dailyHours7, setDailyHours7] = useState('');
  const [dailyHours8, setDailyHours8] = useState('');
  const [dailyHours9, setDailyHours9] = useState('');
  const [dailyHours10, setDailyHours10] = useState('');
  const [dailyHours11, setDailyHours11] = useState('');
  const [dailyHours12, setDailyHours12] = useState('');
  const [dailyHours13, setDailyHours13] = useState('');
  const [dailyHours14, setDailyHours14] = useState('');
  const [dailyHours15, setDailyHours15] = useState('');
  const [dailyHours16, setDailyHours16] = useState('');
  const [isMon, setIsMon] = useState(true);
  const [isTue, setIsTue] = useState(true);
  const [isWed, setIsWed] = useState(true);
  const [isThur, setIsThur] = useState(true);
  const [isFri, setIsFri] = useState(true);
  const [isSat, setIsSat] = useState(true);
  const [isSun, setIsSun] = useState(true);
  const [weeklyRepeat, setWeeklyRepeat] = useState('');
  const [monthlyRepeat, setMonthlyRepeat] = useState('');
  const [yearlyRepeat, setYearlyRepeat] = useState('');

  //modal pacing tab
  const [pacingRes, setPacingRes] = useState('');
  const [pacingList, setPacingList] = useState({});
  const [guestLimit, setGuestLimit] = useState(30);

  //modal room tab
  const [isAllRoom, setIsAllRoom] = useState(true);
  const [roomAreaOption, setRoomAreaOption] = useState(
    floorRoomList.map((clone) => ({ ...clone })),
  );

  // store states
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const outletSections = OutletStore.useState((s) => s.outletSections);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  //////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');
  // label value option

  // interval
  const [turnTimeOption, setTurnTimeOption] = useState([
    {
      label: '15 min',
      value: 15,
    },
    {
      label: '30 min',
      value: 30,
    },
    {
      label: '45 min',
      value: 45,
    },
    {
      label: '60 min',
      value: 60,
    },
    {
      label: '75 min',
      value: 75,
    },
    {
      label: '90 min',
      value: 90,
    },
    {
      label: '105 min',
      value: 105,
    },
    {
      label: '120 min',
      value: 120,
    },
  ]);

  // reservation time option from 8 am to 8 pm
  const [timeOption, setTimeOption] = useState([
    { label: '8:00 AM', value: '0800' },
    { label: '8:15 AM', value: '0815' },
    { label: '8:30 AM', value: '0830' },
    { label: '8:45 AM', value: '0845' },
    { label: '9:00 AM', value: '0900' },
    { label: '9:15 AM', value: '0915' },
    { label: '9:30 AM', value: '0930' },
    { label: '9:45 AM', value: '0945' },
    { label: '10:00 AM', value: '1000' },
    { label: '10:15 AM', value: '1015' },
    { label: '10:30 AM', value: '1030' },
    { label: '10:45 AM', value: '1045' },
    { label: '11:00 AM', value: '1100' },
    { label: '11:15 AM', value: '1115' },
    { label: '11:30 AM', value: '1130' },
    { label: '11:45 AM', value: '1145' },
    { label: '12:00 PM', value: '1200' },
    { label: '12:15 PM', value: '1215' },
    { label: '12:30 PM', value: '1230' },
    { label: '12:45 PM', value: '1245' },
    { label: '1:00 PM', value: '1300' },
    { label: '1:15 PM', value: '1315' },
    { label: '1:30 PM', value: '1330' },
    { label: '1:45 PM', value: '1345' },
    { label: '2:00 PM', value: '1400' },
    { label: '2:15 PM', value: '1415' },
    { label: '2:30 PM', value: '1430' },
    { label: '2:45 PM', value: '1445' },
    { label: '3:00 PM', value: '1500' },
    { label: '3:15 PM', value: '1515' },
    { label: '3:30 PM', value: '1530' },
    { label: '3:45 PM', value: '1545' },
    { label: '4:00 PM', value: '1600' },
    { label: '4:15 PM', value: '1615' },
    { label: '4:30 PM', value: '1630' },
    { label: '4:45 PM', value: '1645' },
    { label: '5:00 PM', value: '1700' },
    { label: '5:15 PM', value: '1715' },
    { label: '5:30 PM', value: '1730' },
    { label: '5:45 PM', value: '1745' },
    { label: '6:00 PM', value: '1800' },
    { label: '6:15 PM', value: '1815' },
    { label: '6:30 PM', value: '1830' },
    { label: '6:45 PM', value: '1845' },
    { label: '7:00 PM', value: '1900' },
    { label: '7:15 PM', value: '1915' },
    { label: '7:30 PM', value: '1930' },
    { label: '7:45 PM', value: '1945' },
    { label: '8:00 PM', value: '2000' },
    { label: '8:15 PM', value: '2015' },
    { label: '8:30 PM', value: '2030' },
    { label: '8:45 PM', value: '2045' },
    { label: '9:00 PM', value: '2100' },
    { label: '9:15 PM', value: '2115' },
    { label: '9:30 PM', value: '2130' },
    { label: '9:45 PM', value: '2145' },
    { label: '10:00 PM', value: '2200' },
    { label: '10:15 PM', value: '2215' },
    { label: '10:30 PM', value: '2230' },
    { label: '10:45 PM', value: '2245' },
    { label: '11:00 PM', value: '2300' },
    { label: '11:15 PM', value: '2315' },
    { label: '11:30 PM', value: '2330' },
  ]);

  const events = [
    {
      title: 'Spa + Massage',
      uniqueId: '1',
      start: moment('2021-12-23' + ' ' + '00:30'),
      end: moment('2021-12-23' + ' ' + '05:30'),
    },
    {
      title: 'Sleeping Mask',
      uniqueId: '2',
      start: moment('2021-12-24' + ' ' + '22:00'),
      end: moment('2021-12-25' + ' ' + '02:00'),
    },
    {
      title: 'Rob Bank',
      uniqueId: '3',
      start: moment('2021-12-25' + ' ' + '01:30'),
      end: moment('2021-12-25' + ' ' + '03:30'),
    },
  ];

  const minMaxOptions = [];
  const [oneMonth, setOneMonth] = useState([]);
  for (let i = 1; i <= 10; i++) {
    minMaxOptions.push({
      label: `${i}`,
      value: `${i}`,
    });
  }

  ////////////////////////////////////////////////////////

  //use effect start
  useEffect(() => {
    // console.log('refreshed');
  }, []);

  // change the value to default when it is isAddAvailability
  useEffect(() => {
    if (isAddAvailability) {
      cleanUpModalState();
    }
  }, [isAddAvailability]);

  // use effect to get the data from the firestore
  useEffect(() => {
    try {
      const unsubscribe = firebase
        .firestore()
        .collection(Collections.ReservationAvailability)
        .where('merchantId', '==', merchantId)
        .where('outletId', '==', currOutletId)
        .onSnapshot(async (snapshot) => {
          const eachReservationAvailability = [];
          if (snapshot && !snapshot.empty) {
            snapshot.forEach((doc) => {
              const docData = doc.data();

              eachReservationAvailability.push({
                ...docData,
              });
            });
            setAllReservationAvailability(eachReservationAvailability);
          }
          // console.log('ReservationList', eachReservationAvailability)

          // need move whole logic to start of app (dashboard ?) or add to store ???
        });
      return () => {
        // unsubscribe the listener
        unsubscribe();
      };
    } catch (e) {
      // console.log(e);
    }
  }, []);

  // update the reservation to show each week
  useEffect(() => {
    const eventData = [];
    allReservationAvailability.forEach((availability) => {
      let dailyHours = []; // store start time and end time from weekdaysTimeRepeat
      let start;
      let end;
      // make sure the weekdaysTimeRepeat time is length 4 (0830)

      // check if there is stop date and already pass the date
      if (
        availability.dateStopRepeat &&
        moment(calendarStartDate)
          .startOf('day')
          .isAfter(moment(availability.dateStopRepeat).startOf('day'))
      ) {
        // console.log('return past date')
        return;
      }

      if (
        availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT
      ) {
        // DOES NOT REPEAT

        // check if dateStartSingle is within this calendar week
        if (
          moment(availability.dateStartSingle).isBetween(
            moment(calendarStartDate).startOf('isoweek'),
            moment(calendarStartDate).endOf('isoweek'),
          )
        ) {
          dailyHours = availability.weekdaysTimeRepeat.split('-');
          start = moment(availability.dateStartSingle)
            .startOf('day')
            .add(dailyHours[0].slice(0, 2), 'hours')
            .add(dailyHours[0].slice(2, 4), 'minutes');
          end = moment(availability.dateStartSingle)
            .startOf('day')
            .add(dailyHours[1].slice(0, 2), 'hours')
            .add(dailyHours[1].slice(2, 4), 'minutes');

          eventData.push({
            ...availability,
            title: availability.reservationName,
            start: start.valueOf(),
            end: end.valueOf(),
          });
        }
      } else if (
        availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DAILY
      ) {
        // DAILY

        // for each day in the calendar week add this event
        for (let i = 0; i < 7; i++) {
          dailyHours = availability.weekdaysTimeRepeat.split('-');
          start = moment(calendarStartDate)
            .startOf('day')
            .add(dailyHours[0].slice(0, 2), 'hours')
            .add(dailyHours[0].slice(2, 4), 'minutes')
            .add(i, 'days');
          end = moment(calendarStartDate)
            .startOf('day')
            .add(dailyHours[1].slice(0, 2), 'hours')
            .add(dailyHours[1].slice(2, 4), 'minutes')
            .add(i, 'days');

          // check if it reached stop date
          if (
            availability.dateStopRepeat &&
            moment(start).isAfter(moment(availability.dateStopRepeat))
          ) {
            break;
          }

          eventData.push({
            ...availability,
            title: availability.reservationName,
            start: start.valueOf(),
            end: end.valueOf(),
          });
        }
      } else if (
        availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.WEEKLY
      ) {
        // WEEKLY

        let timeLapse = 0;
        // repeatEveryShiftPeriod to check if it intersects the calendar week
        while (true) {
          const repeatedWeekDate = moment(availability.dateStartSingle).add(
            availability.repeatEveryShiftPeriod * timeLapse,
            'weeks',
          );

          // if past calendar week range then break
          if (
            moment(repeatedWeekDate).isAfter(
              moment(calendarStartDate).endOf('isoweek'),
            )
          ) {
            break;
          }

          // // console.log('enter ?', moment(repeatedWeekDate).isBetween(
          //   moment(calendarStartDate).startOf('isoweek'),
          //   moment(calendarStartDate).endOf('isoweek')))
          // // console.log(
          //   moment(repeatedWeekDate).format('YYYY-MM-DD'),
          //   moment(calendarStartDate).startOf('isoweek').format('YYYY-MM-DD'),
          //   moment(calendarStartDate).endOf('isoweek').format('YYYY-MM-DD')
          // )
          // check if it is in the calendar week range
          if (
            moment(repeatedWeekDate).isBetween(
              moment(calendarStartDate).startOf('isoweek'),
              moment(calendarStartDate).endOf('isoweek'),
            )
          ) {
            // repeatOnWeekdays repeat on which day of the week
            for (
              let day = 0;
              day < availability.repeatOnWeekdays.length;
              day++
            ) {
              // check which day is true to repeat [mon,tue,wed,thu,fri,sat,sun]
              if (availability.repeatOnWeekdays[day] === true) {
                if (availability.isWeekdaysTimeSame) {
                  // all repeating day is same time (weekdaysTimeRepeat)
                  dailyHours = availability.weekdaysTimeRepeat.split('-');
                  start = moment(calendarStartDate)
                    .startOf('isoweek')
                    .add(day, 'days')
                    .add(dailyHours[0].slice(0, 2), 'hours')
                    .add(dailyHours[0].slice(2, 4), 'minutes');
                  end = moment(calendarStartDate)
                    .startOf('isoweek')
                    .add(day, 'days')
                    .add(dailyHours[1].slice(0, 2), 'hours')
                    .add(dailyHours[1].slice(2, 4), 'minutes');

                  // check if it reached stop date
                  if (
                    availability.dateStopRepeat &&
                    moment(start).isAfter(moment(availability.dateStopRepeat))
                  ) {
                    return;
                  }

                  eventData.push({
                    ...availability,
                    title: availability.reservationName,
                    start: start.valueOf(),
                    end: end.valueOf(),
                  });
                } else {
                  // each repeating day is different time
                  // use weekdaysTime and find the time for each day
                  dailyHours = availability.weekdaysTime[day].split('-');
                  start = moment(calendarStartDate)
                    .startOf('isoweek')
                    .add(day, 'days')
                    .add(dailyHours[0].slice(0, 2), 'hours')
                    .add(dailyHours[0].slice(2, 4), 'minutes');
                  end = moment(calendarStartDate)
                    .startOf('isoweek')
                    .add(day, 'days')
                    .add(dailyHours[1].slice(0, 2), 'hours')
                    .add(dailyHours[1].slice(2, 4), 'minutes');

                  // check if it reached stop date
                  if (
                    availability.dateStopRepeat &&
                    moment(start).isAfter(moment(availability.dateStopRepeat))
                  ) {
                    return;
                  }

                  eventData.push({
                    ...availability,
                    title: availability.reservationName,
                    start: start.valueOf(),
                    end: end.valueOf(),
                  });
                }
              }
            }
            return;
          }

          timeLapse++;
          if (timeLapse > 500) {
            // console.log('%cdangerous INFINITE LOOP return ########################',
            // "color: blue; font-family:monospace; font-size: 20px")
            break;
          }
          if (availability.repeatEveryShiftPeriod === 0) {
            break;
          }
        }
      }
    });
    // console.log('eventData', eventData);
    setReservationThisWeek(eventData);
  }, [allReservationAvailability, calendarStartDate]);

  // get all room/floor/section data
  useEffect(() => {
    var roomAreaOptionTemp = [];

    for (var i = 0; i < outletSections.length; i++) {
      var outletSection = outletSections[i];

      roomAreaOptionTemp.push({
        label: outletSection.sectionName,
        value: outletSection.uniqueId,
        isSelect: false,
      });
    }

    setFloorRoomList(roomAreaOptionTemp);
  }, [outletSections]);

  const renderEvents = (event, touchableOpacityProps) => {
    const [checkEvent, setCheckEvent] = useState(true);

    return (
      <TouchableOpacity
        {...touchableOpacityProps}
        {...(touchableOpacityProps.style[2].backgroundColor = event.isActive
          ? 'rgb(66, 133, 244)'
          : '#D3D3D3')}>
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{
              color: Colors.whiteColor,
              fontFamily: 'NunitoSans-Bold',
              marginBottom: 10,
            }}>{`${event.title}`}</Text>
          {event.isActive ? (
            <MaterialIcons
              name="check-circle"
              size={20}
              color={Colors.whiteColor}
            />
          ) : (
            <MaterialIcons name="circle" size={20} color={Colors.whiteColor} />
          )}
          <Text
            style={{
              color: Colors.whiteColor,
              marginTop: 10,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {`${moment(event.start).format('HH:mm') +
              ' - ' +
              moment(event.end).format('HH:mm')
              }`}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderPacing = (item, index) => {
    const currItem = item.item;
    const currIndex = item.index;
    // // console.log('item', outerIndex, currItem)
    return (
      <View
        key={index}
        style={{
          paddingHorizontal: 10,
          borderWidth: 1,
          width: '100%',
          borderRadius: 10,
          height: 45,
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: windowHeight * 0.01,
        }}>
        <View style={{ width: '15%' }}>
          <Text>{currItem.time.label}</Text>
        </View>
        <View style={{ width: '22.5%' }}>
          <Switch
            value={currItem.switchActive}
            onSyncPress={(value) => {
              let tempPacingList = pacingList;
              tempPacingList.map((item, index) => {
                if (currIndex == index) {
                  item.switchActive = !item.switchActive;
                }
              });
              setPacingList(tempPacingList);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
          />
        </View>
        <View
          style={[
            {
              width: '27.5%',
              flexDirection: 'row',
              alignItems: 'center',
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              let tempPacingList = pacingList;
              tempPacingList.map((item, index) => {
                if (currIndex == index) {
                  item.isAuto = true;
                }
              });
              setPacingList(tempPacingList);
            }}
            style={{
              backgroundColor: currItem.isAuto
                ? Colors.primaryColor
                : Colors.lightPrimary,
              borderLeftTopRadius: 5,
              borderLeftBottomRadius: 5,
              paddingHorizontal: 15,
              height: switchMerchant ? 30 : 35,
              justifyContent: 'center',
            }}>
            <Text
              style={{
                color: currItem.isAuto
                  ? Colors.whiteColor
                  : Colors.primaryColor,
              }}>
              Auto
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              let tempPacingList = pacingList;
              tempPacingList.map((item, index) => {
                if (currIndex == index) {
                  item.isAuto = false;
                  item.isModifiedPacing = true;
                }
              });
              setPacingList(tempPacingList);
            }}
            style={{
              backgroundColor: !currItem.isAuto
                ? Colors.primaryColor
                : Colors.lightPrimary,
              paddingHorizontal: 15,
              borderRightTopRadius: 5,
              borderRightBottomRadius: 5,
              height: switchMerchant ? 30 : 35,
              justifyContent: 'center',
            }}>
            <Text
              style={{
                color: !currItem.isAuto
                  ? Colors.whiteColor
                  : Colors.primaryColor,
              }}>
              Capped
            </Text>
          </TouchableOpacity>
        </View>
        <View style={{ width: '17.5%' }}>
          <TextInput
            defaultValue={currItem.guestLimit}
            onChangeText={(text) => {
              let tempPacingList = pacingList;
              tempPacingList.map((item, index) => {
                if (currIndex == index) {
                  item.guestLimit = parseInt(text);
                  item.isModifiedPacing = true;
                }
              });
              setPacingList(tempPacingList);
            }}
            placeholderTextColor={Colors.descriptionColor}
            placeholder={'30'}
            style={{
              backgroundColor: Colors.fieldtBgColor,
              width: '50%',
              height: switchMerchant ? 30 : 35,
              borderRadius: 5,
              padding: 5,
              marginVertical: 5,
              // borderWidth: 1,
              borderColor: '#E5E5E5',
              paddingLeft: 10,
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-Regular',
            }}
          />
        </View>
        <View style={{ width: '17.5%' }}>
          <TextInput
            defaultValue={currItem.reservationLimit}
            onChangeText={(text) => {
              let tempPacingList = pacingList;
              tempPacingList.map((item, index) => {
                if (currIndex == index) {
                  item.reservationLimit = parseInt(text);
                  item.isModifiedPacing = true;
                }
              });
              setPacingList(tempPacingList);
            }}
            placeholderTextColor={Colors.descriptionColor}
            placeholder={'10'}
            style={{
              backgroundColor: Colors.fieldtBgColor,
              width: '50%',
              height: switchMerchant ? 30 : 35,
              borderRadius: 5,
              padding: 5,
              marginVertical: 5,
              // borderWidth: 1,
              borderColor: '#E5E5E5',
              paddingLeft: 10,
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-Regular',
            }}
          />
        </View>
      </View>
    );
  };

  const getDefaultPacingList = () => {
    let pacingList = [];
    // use timeOption to push data and add additional field
    for (let i = 0; i < timeOption.length; i++) {
      pacingList.push({
        time: timeOption[i],
        switchActive: true,
        isAuto: true,
        guestLimit: 30,
        reservationLimit: 10,
        isModifiedPacing: false,
      });
    }
    return pacingList;
  };

  // this function handle add AND update reservation availability (using uniqueId)
  const addUpdateReservationAvailability = async () => {
    /**
    [New] ReservationAvailability
    {
    uniqueId: "string", // Unique ID that same with document ID in firestore 
    outletId: "string",
    merchantId: "string",
    createdAt: "string", // unix timestamp
    updatedAt: "string", // unix timestamp
  
    // Basic
    reservationName: "string",
    isActive: "boolean", // to check if this reservation is active or not
  
    
    // Time & days
    dateStartSingle: "string", // unix timestamp, reservation on that day only, ignore when repeating
    repeatShiftType: "string", // RESERVATIONS_SHIFT_TYPE,
    repeatOnWeekdays: "Object", // for WEEKLY.. [0(Mon): true, 1: true, 2: true, 3: true, 4: true, 5: true, 6: true] 
    isWeekdaysTimeSame: "boolean", // default true
    weekdaysTimeRepeat: "string", // if isWeekdaysTimeSame is true then specify repeat time '1100-1945'
    weekdaysTime: {  // if isWeekdaysTimeSame is false then this will be specify each day time
      monday: '1100-1945',
      tuesday: '1100-1945'...
    },
    repeatEveryShiftPeriod: "number", 
      // - if "repeatShiftType"=="WEEKLY" then repeat on 1/2/3/4 week,
      // for repeat monthly is repeat every 4 WEEKLY
    dateStopRepeat: "string | null", // unix timestamp, if null then keep on repeating
  
  
    // Pacing
    intervalMin: "number: 15 default", // 15/30/90 support on custom turn time ? time for each time slot
    pacingList: {   // if user pressed capped, typed guestLimit or reservationLimit then store in here
        '0800': {
            guestLimitNum: 30,
            reservationLimitNum: 10,
        }, ...
      },
    defaultGuestLimit: 30,
    defaultReservationLimit: 10,
  
    // Room & areas
    sectionIdList: [ sectionId, ... ], // sectionId: outletSection.uniqueId, if empty means apply to all sections/rooms 
  
    } */

    // validation
    if (basicReserveName.length === 0) {
      Alert.alert('Error', 'Please enter reservation name');
      return;
    }
    if (pacingRes === '') {
      Alert.alert('Error', 'Please select a reservation interval');
      return;
    }
    if (repeatShift == RESERVATIONS_SHIFT_TYPE.WEEKLY) {
      if (!switchDaily) {
        // switchDaily is false
        if (
          (isMon && (!dailyHours3 || !dailyHours4)) ||
          (isTue && (!dailyHours5 || !dailyHours6)) ||
          (isWed && (!dailyHours7 || !dailyHours8)) ||
          (isThur && (!dailyHours9 || !dailyHours10)) ||
          (isFri && (!dailyHours11 || !dailyHours12)) ||
          (isSat && (!dailyHours13 || !dailyHours14)) ||
          (isSun && (!dailyHours15 || !dailyHours16))
        ) {
          Alert.alert(
            'Error',
            'Please fill in reservation time for repeating days',
          );
          return;
        } else if (
          (isMon && +dailyHours3 >= +dailyHours4) ||
          (isTue && +dailyHours5 >= +dailyHours6) ||
          (isWed && +dailyHours7 >= +dailyHours8) ||
          (isThur && +dailyHours9 >= +dailyHours10) ||
          (isFri && +dailyHours11 >= +dailyHours12) ||
          (isSat && +dailyHours13 >= +dailyHours14) ||
          (isSun && +dailyHours15 >= +dailyHours16)
        ) {
          Alert.alert(
            'Error',
            'Reservation start time must be ealier than end time',
          );
          return;
        }
      } else {
        // switchDaily == true
        if (!dailyHours1 || !dailyHours2) {
          Alert.alert('Error', 'Please select a reservation time');
          return;
        }
        if (+dailyHours1 >= +dailyHours2) {
          Alert.alert(
            'Error',
            'Reservation start time must be ealier than end time',
          );
          return;
        }
      }
    } else {
      if (!dailyHours1 || !dailyHours2) {
        Alert.alert('Error', 'Please select a reservation time');
        return;
      }
      if (+dailyHours1 >= +dailyHours2) {
        Alert.alert(
          'Error',
          'Reservation start time must be ealier than end time',
        );
        return;
      }
    }

    // generate data for server
    const repeatOnWeekdays = [isMon, isTue, isWed, isThur, isFri, isSat, isSun];

    const weekdaysTime = [];
    if (!switchDaily) {
      weekdaysTime.push(
        dailyHours3 + '-' + dailyHours4,
        dailyHours5 + '-' + dailyHours6,
        dailyHours7 + '-' + dailyHours8,
        dailyHours9 + '-' + dailyHours10,
        dailyHours11 + '-' + dailyHours12,
        dailyHours13 + '-' + dailyHours14,
        dailyHours15 + '-' + dailyHours16,
      );
    }
    const repeatEveryShiftPeriod = weeklyRepeat ? +weeklyRepeat : 0;

    const modifiedPacing = pacingList.reduce((acc, item) => {
      if (item.isModifiedPacing) {
        acc[item.time.value] = {
          switchActive: item.switchActive,
          isAuto: item.isAuto,
          guestLimit: item.guestLimit,
          reservationLimit: item.reservationLimit,
          isModifiedPacing: item.isModifiedPacing,
        };
      }
      return acc;
    }, {});

    const sectionList = [];
    // push selected room floor section
    if (!isAllRoom) {
      roomAreaOption.map((item) => {
        if (item.isSelect) {
          sectionList.push(item.value);
        }
      });
    }

    // add or update reservation to backend
    const body = {
      uniqueId: isAddAvailability ? uuidv4() : selectedReservationUniqueId,
      outletId: currOutletId,
      merchantId: merchantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),

      // Basic
      reservationName: basicReserveName,
      isActive: switchActive,

      // Time & days
      dateStartSingle: moment(rev_date).valueOf(),
      repeatShiftType: repeatShift,
      repeatOnWeekdays: repeatOnWeekdays,
      isWeekdaysTimeSame: switchDaily,
      weekdaysTimeRepeat: dailyHours1 + '-' + dailyHours2,
      weekdaysTime: weekdaysTime,
      repeatEveryShiftPeriod: repeatEveryShiftPeriod,
      dateStopRepeat: rev_date2 === '' ? '' : moment(rev_date2).valueOf(),

      // Pacing
      intervalMin: pacingRes,
      pacingList: modifiedPacing,
      defaultGuestLimit: guestLimit ? +guestLimit : 0,
      defaultReservationLimit: 10,

      // Room & areas
      sectionIdList: sectionList,
    };

    // console.log('body', body);
    const res = await ApiClient.POST(API.createReservationAvailability, body);

    if (res.status === 'success') {
      Alert.alert(
        'Success',
        `Reservation availability has been ${isAddAvailability ? 'added' : 'updated'
        }`,
      );
      Alert.alert('Info', 'Do you want Update for All day or Current Day?', [
        {
          text: 'Cancel',
          onPress: () => {
            //Cancel
          },
        },
        {
          text: 'All Day',
          onPress: () => {
            //All day
          },
        },
        {
          text: 'Current Day',
          onPress: () => {
            //Current Day
          },
        },
      ]);
    } else {
      Alert.alert('Error', 'Something went wrong');
    }

    // todo 3) pacing list doesnt auto refresh ui

    // TODO calendar toggle

    setShowAddAvModal(false);
    cleanUpModalState();
  };

  // delete reservation availability
  const deleteReservationAvailability = async () => {
    const body = {
      uniqueId: selectedReservationUniqueId,
    };
    const res = await ApiClient.POST(API.deleteReservationAvailability, body);

    if (res.status === 'success') {
      Alert.alert('Success', 'Reservation availability has been removed');
    } else {
      Alert.alert('Error', 'Something went wrong');
    }

    setShowDeleteAvModal(false);
    cleanUpModalState();
  };

  // restore modal state
  const restoreModalState = (event) => {
    // update the state of a selected reservation
    setSelectedReservationUniqueId(event.uniqueId);
    setBasicReserveName(event.reservationName);
    setSwitchActive(event.isActive);
    setBasicColor('');

    setTimeDaysDateTimePicker(false);
    setTimeDaysDateTimePicker2(false);
    setRev_date(moment(event.dateStartSingle).toDate());
    if (event.dateStopRepeat === '') {
      setRev_date2('');
    } else {
      setRev_date2(moment(event.dateStopRepeat).toDate());
    }

    setRepeatShift(event.repeatShiftType);
    setSwitchDaily(event.isWeekdaysTimeSame);

    const dailyHour = event.weekdaysTimeRepeat.split('-');
    setDailyHours1(dailyHour[0]);
    setDailyHours2(dailyHour[1]);

    if (event.isWeekdaysTimeSame) {
      setDailyHours3('');
      setDailyHours4('');
      setDailyHours5('');
      setDailyHours6('');
      setDailyHours7('');
      setDailyHours8('');
      setDailyHours9('');
      setDailyHours10('');
      setDailyHours11('');
      setDailyHours12('');
      setDailyHours13('');
      setDailyHours14('');
      setDailyHours15('');
      setDailyHours16('');
    } else {
      setDailyHours3(event.weekdaysTime[0].split('-')[0]);
      setDailyHours4(event.weekdaysTime[0].split('-')[1]);
      setDailyHours5(event.weekdaysTime[1].split('-')[0]);
      setDailyHours6(event.weekdaysTime[1].split('-')[1]);
      setDailyHours7(event.weekdaysTime[2].split('-')[0]);
      setDailyHours8(event.weekdaysTime[2].split('-')[1]);
      setDailyHours9(event.weekdaysTime[3].split('-')[0]);
      setDailyHours10(event.weekdaysTime[3].split('-')[1]);
      setDailyHours11(event.weekdaysTime[4].split('-')[0]);
      setDailyHours12(event.weekdaysTime[4].split('-')[1]);
      setDailyHours13(event.weekdaysTime[5].split('-')[0]);
      setDailyHours14(event.weekdaysTime[5].split('-')[1]);
      setDailyHours15(event.weekdaysTime[6].split('-')[0]);
      setDailyHours16(event.weekdaysTime[6].split('-')[1]);
    }

    setIsMon(event.repeatOnWeekdays[0]);
    setIsTue(event.repeatOnWeekdays[1]);
    setIsWed(event.repeatOnWeekdays[2]);
    setIsThur(event.repeatOnWeekdays[3]);
    setIsFri(event.repeatOnWeekdays[4]);
    setIsSat(event.repeatOnWeekdays[5]);
    setIsSun(event.repeatOnWeekdays[6]);

    if (event.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT) {
      setWeeklyRepeat('');
      setMonthlyRepeat('');
      setYearlyRepeat('');
    } else if (event.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DAILY) {
      setWeeklyRepeat('');
      setMonthlyRepeat('');
      setYearlyRepeat('');
    } else if (event.repeatShiftType === RESERVATIONS_SHIFT_TYPE.WEEKLY) {
      setWeeklyRepeat(
        event.repeatEveryShiftPeriod
          ? event.repeatEveryShiftPeriod.toString()
          : '',
      );
      setMonthlyRepeat('');
      setYearlyRepeat('');
    }

    setPacingRes(event.intervalMin);

    // get pacingList
    let tempPacingList = getDefaultPacingList();
    // for each key in event.pacingList find it in tempPacingList.time.value and replace it
    for (let key in event.pacingList) {
      for (let i = 0; i < tempPacingList.length; i++) {
        if (tempPacingList[i].time.value === key) {
          tempPacingList[i] = {
            ...tempPacingList[i],
            ...event.pacingList[key],
          };
        }
      }
    }
    setPacingList(tempPacingList);
    setGuestLimit(
      isNaN(+event.defaultGuestLimit) ? 30 : +event.defaultGuestLimit,
    );

    if (event.sectionIdList.length === 0) {
      setIsAllRoom(true);
      // deep copy of an array of object
      setRoomAreaOption(floorRoomList.map((clone) => ({ ...clone })));
    } else {
      setIsAllRoom(false);
      let tempRoomArea = floorRoomList.map((clone) => ({ ...clone }));
      for (let i = 0; i < event.sectionIdList.length; i++) {
        for (let j = 0; j < tempRoomArea.length; j++) {
          if (tempRoomArea[j].value === event.sectionIdList[i]) {
            tempRoomArea[j].isSelect = true;
          }
        }
      }
      setRoomAreaOption(tempRoomArea);
    }
  };

  // reset all the state inside the reservation modal
  const cleanUpModalState = () => {
    // setIsAddAvailability(true);

    setSelectedReservationUniqueId('');
    setBasicReserveName('');
    setSwitchActive(true);
    setBasicColor('');

    setTimeDaysDateTimePicker(false);
    setTimeDaysDateTimePicker2(false);
    setRev_date(moment().endOf(Date.now()).toDate());
    setRev_date2('');

    setRepeatShift(RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT);
    setSwitchDaily(true);

    setDailyHours1('');
    setDailyHours2('');

    setDailyHours3('');
    setDailyHours4('');
    setDailyHours5('');
    setDailyHours6('');
    setDailyHours7('');
    setDailyHours8('');
    setDailyHours9('');
    setDailyHours10('');
    setDailyHours11('');
    setDailyHours12('');
    setDailyHours13('');
    setDailyHours14('');
    setDailyHours15('');
    setDailyHours16('');
    setIsMon(true);
    setIsTue(true);
    setIsWed(true);
    setIsThur(true);
    setIsFri(true);
    setIsSat(true);
    setIsSun(true);
    setWeeklyRepeat('');
    setMonthlyRepeat('');
    setYearlyRepeat('');

    setPacingRes('');
    setPacingList(getDefaultPacingList());
    setGuestLimit(30);

    setIsAllRoom(true);
    setRoomAreaOption(floorRoomList.map((clone) => ({ ...clone })));
  };

  // navigation
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          } else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),

    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? '27%' : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? '1%' : 0,
          }}>
          Interval Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* Sidebar */}
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                //width: '10%',
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation={true}
          />
        </View> */}
        <View
          style={{
            //height: windowHeight * 0.07,
            //width: switchMerchant ? windowWidth * 0.92 : null,
            //width: '100%',
            //height: '100%',
            width: switchMerchant ? windowWidth * 0.87 : windowWidth * 0.92,
            height: windowHeight * 0.89,
            backgroundColor: Colors.whiteColor,
            flexDirection: 'column',
            marginHorizontal: switchMerchant ? 16 : 0,
          }}>
          {/* Modal for Update
        <ModalView
          animationType="slide"
          transparent={true}
          visible={showUpdAvModal}
          onRequestClose={() => {
            setShowUpdAvModal(false);
            cleanUpModalState();
          }}>
          <View style={styles.modalContainer}>
            <View style={styles.modalView}>
              <View style={styles.closeButton}>
                <TouchableOpacity
                  onPress={() => {
                    setShowUpdAvModal(false);
                    cleanUpModalState();
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
              </View>
              <Text
                style={{
                  color: Colors.blackColor,
                  paddingTop: 10,
                  fontSize: 18,
                }}>Update for all day or Current Day?</Text>
              <View
                style={{
                  width: windowWidth * 0.1,
                  height: windowHeight * 0.08,
                  flexDirection: 'row',
                }}>
                <View style={{
                  flexDirection: 'row',
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  backgroundColor: isBasic
                    ? Colors.fieldtBgColor2
                    : Colors.whiteColor
                }}>
                  <TouchableOpacity
                    onPress={() => {
                      // For all day
                    }}>
                    <View
                      style={{
                        paddingHorizontal: 18,
                        paddingVertical: 8,
                        marginRight: 5,
                        borderRadius: 5,
                        backgroundColor: Colors.primaryColor,
                      }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          paddingLeft: 5,
                          bottom: 1,
                        }}>
                        All Day
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      // For Current day
                    }}>
                    <View
                      style={{
                        paddingHorizontal: 8,
                        paddingVertical: 8,
                        paddingLeft: 5,
                        borderRadius: 5,
                        backgroundColor: Colors.primaryColor,
                      }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          paddingLeft: 5,
                          bottom: 1,
                        }}>
                        Current Day
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
                      </ModalView> */}

          {/* Modal start */}
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            animationType="fade"
            transparent={true}
            visible={showAddAvModal}
            onRequestClose={() => {
              setShowAddAvModal(false);
              setIsBasic(false);
              setIsTimeDays(false);
              setIsPacing(false);
              setIsRoom(false);
              cleanUpModalState();
            }}>
            <View style={styles.modalContainer}>
              <View style={styles.modalView}>
                <View style={styles.closeButton}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowAddAvModal(false);
                      cleanUpModalState();
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                </View>
                <View style={{ flex: 9 }}>
                  <View
                    style={{
                      width: windowWidth * 0.7,
                      height: windowHeight * 0.8,
                      flexDirection: 'row',
                    }}>
                    {/* left component */}
                    <View style={{ flex: 3 }}>
                      <View style={{ paddingTop: '10%', height: '50%' }}>
                        <TouchableOpacity
                          onPress={() => {
                            setIsBasic(true);
                            setIsTimeDays(false);
                            setIsPacing(false);
                            setIsColor(false);
                            setIsRoom(false);
                          }}
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            paddingVertical: 10,
                            paddingLeft: 10,
                            backgroundColor: isBasic
                              ? Colors.fieldtBgColor2
                              : Colors.whiteColor,
                          }}>
                          {/* icon here */}
                          <View style={{ width: 40 }}>
                            <Icon
                              name="calendar-outline"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                          </View>
                          <View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Basic
                            </Text>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {basicReserveName
                                ? basicReserveName
                                : 'Enter a name'}
                            </Text>
                          </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            setIsBasic(false);
                            setIsTimeDays(true);
                            setIsPacing(false);
                            setIsColor(false);
                            setIsRoom(false);
                          }}
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            paddingVertical: 10,
                            paddingLeft: 10,
                            backgroundColor: isTimeDays
                              ? Colors.fieldtBgColor2
                              : Colors.whiteColor,
                          }}>
                          {/* icon here */}
                          <View style={{ width: 40 }}>
                            <Icon
                              name="time-outline"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                          </View>
                          <View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Time & Days
                            </Text>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              8:15 AM - 12:00 pm
                            </Text>
                          </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            setIsBasic(false);
                            setIsTimeDays(false);
                            setIsPacing(true);
                            setIsColor(false);
                            setIsRoom(false);
                          }}
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            paddingVertical: 10,
                            paddingLeft: 10,
                            backgroundColor: isPacing
                              ? Colors.fieldtBgColor2
                              : Colors.whiteColor,
                          }}>
                          {/* icon here */}
                          <View style={{ width: 40 }}>
                            <Icon
                              name="timer-outline"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                          </View>
                          <View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Interval & Pacing
                            </Text>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {turnTimeOption
                                ? `${pacingRes ? pacingRes : '0'} min`
                                : ''}
                              , Auto Pacing
                            </Text>
                          </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            setIsBasic(false);
                            setIsTimeDays(false);
                            setIsPacing(false);
                            setIsRoom(true);
                            setIsColor(false);
                          }}
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            paddingVertical: 10,
                            paddingLeft: 10,
                            backgroundColor: isRoom
                              ? Colors.fieldtBgColor2
                              : Colors.whiteColor,
                          }}>
                          {/* icon here */}
                          <View style={{ width: 40 }}>
                            <Icon
                              name="square-outline"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                          </View>
                          <View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Room & Areas
                            </Text>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              All Rooms
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                    {/* Right component */}
                    <View
                      style={{
                        flex: 7,
                        paddingTop: windowHeight * 0.05,
                        paddingHorizontal: windowWidth * 0.02,
                      }}>
                      {/* <ScrollView> */}
                      {isBasic ? (
                        <View style={styles.flexOne}>
                          <Text
                            style={{
                              marginBottom: windowHeight * 0.01,
                              color: 'black',
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                              alignSelf: 'center'
                            }}>
                            Basic Information
                          </Text>
                          <View style={styles.spaceBetweenFullWidth}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Reservation availability name
                            </Text>
                            <TextInput
                              defaultValue={basicReserveName}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholder={'e.g Dinner, Lunch, etc'}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                // borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              //iOS
                              // clearTextOnFocus={true}
                              selectTextOnFocus
                              //Android
                              // onFocus={() => {
                              //   setTemp(basicReserveName);
                              //   setBasicReserveName('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (basicReserveName == '') {
                              //     setBasicReserveName(temp);
                              //   }
                              // }}
                              ///////////////////////////////////////////////
                              onChangeText={(text) => {
                                setBasicReserveName(text);
                              }}
                            />
                          </View>
                          <View style={styles.spaceBetweenFullWidth}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Active
                            </Text>
                            <Switch
                              value={switchActive}
                              width={42}
                              height={20}
                              onSyncPress={(value) => {
                                setSwitchActive(!switchActive);
                              }}
                              circleColorActive={Colors.whiteColor}
                              circleColorInactive={Colors.fieldtTxtColor}
                              backgroundActive={Colors.primaryColor}
                            />
                          </View>
                          <View
                            style={{
                              marginBottom: 0,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              width: '100%',
                              alignItems: 'center',
                              marginTop: windowHeight * 0.01,
                              height: isTablet() ? 40 : 35,
                            }}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Color
                            </Text>
                            <TouchableOpacity
                              onPress={() => {
                                setIsColor(!isColor);
                              }}>
                              {/* rectangle */}
                              <View
                                style={{
                                  height: 20,
                                  width: 20,
                                  borderRadius: 3,
                                  backgroundColor:
                                    basicColor === ''
                                      ? Colors.lightPrimary
                                      : basicColor,
                                }}
                              />
                            </TouchableOpacity>
                          </View>
                          {isColor ? (
                            <View>
                              <View
                                style={{
                                  width: 0,
                                  height: 0,
                                  borderLeftWidth: 8,
                                  borderRightWidth: 8,
                                  borderBottomWidth: 16,
                                  borderStyle: 'solid',
                                  backgroundColor: 'transparent',
                                  borderLeftColor: 'transparent',
                                  borderRightColor: 'transparent',
                                  borderBottomColor: '#B7B2B6',
                                  left: '97.5%',
                                }}
                              />
                              <View
                                style={{
                                  height: windowHeight * 0.25,
                                  width: windowWidth * 0.2,
                                  shadowOpacity: 0,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  borderRadius: 5,
                                  left: '67%',
                                }}>
                                {/* <ColorPanel
                              style={{flex: 1}}
                              fullColor={true}
                              color={basicColor}
                              brightnessLowerLimit={0}
                              onColorChange={(color) => setBasicColor(color)}
                            /> */}
                              </View>
                            </View>
                          ) : null}
                          {!isAddAvailability ? (
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '100%',
                                alignItems: 'center',
                                marginTop: windowHeight * 0.04,
                                height: switchMerchant ? 35 : 40,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Delete availability
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  deleteReservationAvailability();
                                  setShowAddAvModal(false);
                                }}
                                style={{
                                  backgroundColor: 'red',
                                  height: switchMerchant ? 35 : 40,
                                  width: switchMerchant ? 140 : 200,
                                  borderRadius: 5,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                  }}>
                                  Delete availability
                                </Text>
                              </TouchableOpacity>
                            </View>
                          ) : null}
                        </View>
                      ) : null}
                      {isTimeDays ? (
                        <View style={styles.flexOne}>
                          <View style={styles.spaceBetweenFullWidth}>
                            <DateTimePickerModal
                              //supportedOrientations={['landscape', 'portrait']}
                              isVisible={timeDaysDateTimePicker}
                              mode={'date'}
                              onConfirm={(text) => {
                                setRev_date(moment(text));
                                setTimeDaysDateTimePicker(false);
                              }}
                              onCancel={() => {
                                setTimeDaysDateTimePicker(false);
                              }}
                              style={{ zIndex: 1000 }}
                              minimumDate={moment().endOf(Date.now()).toDate()}
                            />
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Date
                            </Text>
                            <TouchableOpacity
                              onPress={() => {
                                setTimeDaysDateTimePicker(true);
                              }}
                              style={[
                                {
                                  width: windowWidth * 0.12,
                                  height: 40,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  borderRadius: 10,
                                  paddingVertical: 10,
                                  justifyContent: 'center',
                                  backgroundColor: Colors.fieldtBgColor,
                                  shadowOpacity: 0,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: 1,
                                },
                                switchMerchant
                                  ? {
                                    height: 35,
                                    // width: windowWidth * 0.24,
                                    // top: windowHeight * -0.075,
                                  }
                                  : {},
                              ]}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                {moment(rev_date).format('ddd DD MMM yyyy')}
                              </Text>
                            </TouchableOpacity>
                          </View>

                          <View style={styles.spaceBetweenFullWidth}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Repeat this shift
                            </Text>
                            {switchMerchant ? (
                              <View style={styles.phoneRnPickerViewStyle}>
                                <RNPickerSelect
                                  placeholder={{}}
                                  useNativeAndroidPickerStyle={false}
                                  style={{
                                    inputAndroid: {
                                      fontSize: 10,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: 10,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={[
                                    {
                                      label: 'Does not repeat',
                                      value:
                                        RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT,
                                    },
                                    {
                                      label: 'Daily',
                                      value: RESERVATIONS_SHIFT_TYPE.DAILY,
                                    },
                                    {
                                      label: 'Weekly',
                                      value: RESERVATIONS_SHIFT_TYPE.WEEKLY,
                                    },
                                  ]}
                                  value={repeatShift}
                                  onValueChange={(value) => {
                                    setRepeatShift(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View style={styles.tabletRnPickerViewStyle}>
                                <RNPickerSelect
                                  placeholder={{}}
                                  style={{
                                    inputAndroidContainer: {
                                      width: 250,
                                      height: 40,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      width: 250,
                                      fontSize: 14,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: '100%',
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      width: 250,
                                      fontSize: 14,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: '100%',
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      width: 250,
                                      height: 35,
                                      width: windowWidth * 0.15,
                                      justifyContent: 'center',
                                      fontSize: 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={[
                                    {
                                      label: 'Does not repeat',
                                      value:
                                        RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT,
                                    },
                                    {
                                      label: 'Daily',
                                      value: RESERVATIONS_SHIFT_TYPE.DAILY,
                                    },
                                    {
                                      label: 'Weekly',
                                      value: RESERVATIONS_SHIFT_TYPE.WEEKLY,
                                    },
                                  ]}
                                  value={repeatShift}
                                  onValueChange={(value) => {
                                    setRepeatShift(value);
                                  }}
                                />
                              </View>
                            )}
                          </View>

                          {repeatShift ===
                            RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT ? (
                            <View style={styles.spaceBetweenFullWidth}>
                              <Text
                                style={{
                                  color: 'black',
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                Reservation hours
                              </Text>
                              <View style={{ flexDirection: 'row' }}>
                                {switchMerchant ? (
                                  <View style={styles.phoneRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      useNativeAndroidPickerStyle={false}
                                      style={{
                                        inputAndroid: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                      }}
                                      items={timeOption}
                                      value={dailyHours1}
                                      onValueChange={(value) => {
                                        setDailyHours1(value);
                                      }}
                                    />
                                  </View>
                                ) : (
                                  <View style={styles.tabletRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      style={{
                                        inputAndroidContainer: {
                                          height: 35,
                                          justifyContent: 'center',
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                        inputAndroid: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        viewContainer: {
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          height: 35,
                                          width: windowWidth * 0.12,
                                          justifyContent: 'center',
                                          fontSize: 16,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                      }}
                                      items={timeOption}
                                      value={dailyHours1}
                                      onValueChange={(value) => {
                                        setDailyHours1(value);
                                      }}
                                    />
                                  </View>
                                )}
                                <Text
                                  style={{
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    textAlignVertical: 'center',
                                  }}>
                                  -
                                </Text>
                                {switchMerchant ? (
                                  <View style={styles.phoneRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      useNativeAndroidPickerStyle={false}
                                      style={{
                                        inputAndroid: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                      }}
                                      items={timeOption}
                                      value={dailyHours2}
                                      onValueChange={(value) => {
                                        setDailyHours2(value);
                                      }}
                                    />
                                  </View>
                                ) : (
                                  <View style={styles.tabletRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      style={{
                                        inputAndroidContainer: {
                                          height: 35,
                                          justifyContent: 'center',
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                        inputAndroid: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        viewContainer: {
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          height: 35,
                                          width: windowWidth * 0.12,
                                          justifyContent: 'center',
                                          fontSize: 16,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                      }}
                                      items={timeOption}
                                      value={dailyHours2}
                                      onValueChange={(value) => {
                                        setDailyHours2(value);
                                      }}
                                    />
                                  </View>
                                )}
                              </View>
                            </View>
                          ) : repeatShift === RESERVATIONS_SHIFT_TYPE.DAILY ? (
                            <View>
                              <View style={styles.spaceBetweenFullWidth}>
                                <Text>Reservation hours</Text>
                                <View style={{ flexDirection: 'row' }}>
                                  {switchMerchant ? (
                                    <View style={styles.phoneRnPickerViewStyle}>
                                      <RNPickerSelect
                                        placeholder={{}}
                                        useNativeAndroidPickerStyle={false}
                                        style={{
                                          inputAndroid: {
                                            fontSize: 10,
                                            paddingVertical: 5,
                                            color: 'black',
                                            textAlign: 'center',
                                          },
                                          inputIOS: {
                                            fontSize: 10,
                                            paddingVertical: 5,
                                            color: 'black',
                                            textAlign: 'center',
                                          },
                                        }}
                                        items={timeOption}
                                        value={dailyHours1}
                                        onValueChange={(value) => {
                                          setDailyHours1(value);
                                        }}
                                      />
                                    </View>
                                  ) : (
                                    <View
                                      style={styles.tabletRnPickerViewStyle}>
                                      <RNPickerSelect
                                        placeholder={{}}
                                        style={{
                                          inputAndroidContainer: {
                                            height: 35,
                                            justifyContent: 'center',
                                            backgroundColor: '#fafafa',
                                            borderRadius: 4,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                              width: 0,
                                              height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                          },
                                          inputAndroid: {
                                            //backgroundColor: '#fafafa',
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: 16,
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 5,
                                            width: '100%',
                                            paddingHorizontal: 10,
                                            height: 35,
                                            paddingLeft: 12,
                                            textAlign: 'center',
                                          },
                                          inputIOS: {
                                            //backgroundColor: '#fafafa',
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: 16,
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 5,
                                            width: '100%',
                                            paddingHorizontal: 10,
                                            height: 35,
                                            paddingLeft: 12,
                                            textAlign: 'center',
                                          },
                                          viewContainer: {
                                            backgroundColor: '#fafafa',
                                            borderRadius: 4,
                                            height: 35,
                                            width: windowWidth * 0.12,
                                            justifyContent: 'center',
                                            fontSize: 16,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                              width: 0,
                                              height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                          },
                                        }}
                                        items={timeOption}
                                        value={dailyHours1}
                                        onValueChange={(value) => {
                                          setDailyHours1(value);
                                        }}
                                      />
                                    </View>
                                  )}
                                  <Text
                                    style={{
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      textAlignVertical: 'center',
                                    }}>
                                    -
                                  </Text>
                                  {switchMerchant ? (
                                    <View style={styles.phoneRnPickerViewStyle}>
                                      <RNPickerSelect
                                        placeholder={{}}
                                        useNativeAndroidPickerStyle={false}
                                        style={{
                                          inputAndroid: {
                                            fontSize: 10,
                                            paddingVertical: 5,
                                            color: 'black',
                                            textAlign: 'center',
                                          },
                                          inputIOS: {
                                            fontSize: 10,
                                            paddingVertical: 5,
                                            color: 'black',
                                            textAlign: 'center',
                                          },
                                        }}
                                        items={timeOption}
                                        value={dailyHours2}
                                        onValueChange={(value) => {
                                          setDailyHours2(value);
                                        }}
                                      />
                                    </View>
                                  ) : (
                                    <View
                                      style={styles.tabletRnPickerViewStyle}>
                                      <RNPickerSelect
                                        placeholder={{}}
                                        style={{
                                          inputAndroidContainer: {
                                            height: 35,
                                            justifyContent: 'center',
                                            backgroundColor: '#fafafa',
                                            borderRadius: 4,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                              width: 0,
                                              height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                          },
                                          inputAndroid: {
                                            //backgroundColor: '#fafafa',
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: 16,
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 5,
                                            width: '100%',
                                            paddingHorizontal: 10,
                                            height: 35,
                                            paddingLeft: 12,
                                            textAlign: 'center',
                                          },
                                          inputIOS: {
                                            //backgroundColor: '#fafafa',
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: 16,
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 5,
                                            width: '100%',
                                            paddingHorizontal: 10,
                                            height: 35,
                                            paddingLeft: 12,
                                            textAlign: 'center',
                                          },
                                          viewContainer: {
                                            backgroundColor: '#fafafa',
                                            borderRadius: 4,
                                            height: 35,
                                            width: windowWidth * 0.12,
                                            justifyContent: 'center',
                                            fontSize: 16,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                              width: 0,
                                              height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                          },
                                        }}
                                        items={timeOption}
                                        value={dailyHours2}
                                        onValueChange={(value) => {
                                          setDailyHours2(value);
                                        }}
                                      />
                                    </View>
                                  )}
                                </View>
                              </View>

                              <View style={styles.spaceBetweenFullWidth}>
                                <DateTimePickerModal
                                  //supportedOrientations={['landscape', 'portrait']}
                                  isVisible={timeDaysDateTimePicker2}
                                  mode={'date'}
                                  onConfirm={(text) => {
                                    setRev_date2(moment(text));
                                    setTimeDaysDateTimePicker2(false);
                                  }}
                                  onCancel={() => {
                                    setTimeDaysDateTimePicker2(false);
                                  }}
                                  style={{ zIndex: 1000 }}
                                  minimumDate={moment().add(1, 'days').toDate()}
                                />
                                <Text>Stop repeating</Text>
                                <TouchableOpacity
                                  onPress={() => {
                                    setTimeDaysDateTimePicker2(true);
                                  }}
                                  style={[
                                    {
                                      width: windowWidth * 0.13,
                                      height: 40,
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      flexDirection: 'row',
                                      borderRadius: 10,
                                      paddingVertical: 10,
                                      paddingHorizontal: 20,
                                      justifyContent: 'center',
                                      backgroundColor: Colors.fieldtBgColor,
                                      shadowOpacity: 0,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: 1,
                                    },
                                    switchMerchant
                                      ? {
                                        height: 35,
                                      }
                                      : {},
                                  ]}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Regular',
                                        flex: 9,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {rev_date2 === ''
                                      ? 'Never'
                                      : moment(rev_date2).format(
                                        'ddd DD/MMM/yyyy',
                                      )}
                                  </Text>
                                  {rev_date2 !== '' ? (
                                    <TouchableOpacity
                                      style={{ flex: 1, alignItems: 'flex-end' }}
                                      onPress={() => {
                                        setRev_date2('');
                                      }}>
                                      <Text>X</Text>
                                    </TouchableOpacity>
                                  ) : (
                                    <Text style={{ flex: 1, textAlign: 'right' }}>
                                      V
                                    </Text>
                                  )}
                                </TouchableOpacity>
                              </View>
                            </View>
                          ) : repeatShift === RESERVATIONS_SHIFT_TYPE.WEEKLY ? (
                            <View>
                              <View style={{ width: '100%' }}>
                                <Text>Repeat on</Text>
                                <View
                                  style={{
                                    paddingTop: 10,
                                    width: '36%',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                  }}>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsMon(!isMon);
                                    }}
                                    style={{
                                      backgroundColor: isMon
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isMon
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      M
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsTue(!isTue);
                                    }}
                                    style={{
                                      backgroundColor: isTue
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isTue
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      T
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsWed(!isWed);
                                    }}
                                    style={{
                                      backgroundColor: isWed
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isWed
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      W
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsThur(!isThur);
                                    }}
                                    style={{
                                      backgroundColor: isThur
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isThur
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      T
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsFri(!isFri);
                                    }}
                                    style={{
                                      backgroundColor: isFri
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isFri
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      F
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsSat(!isSat);
                                    }}
                                    style={{
                                      backgroundColor: isSat
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isSat
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      S
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setIsSun(!isSun);
                                      // console.log(oneMonth);
                                    }}
                                    style={{
                                      backgroundColor: isSun
                                        ? Colors.primaryColor
                                        : Colors.whiteColor,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      height: 24,
                                      width: 24,
                                      borderRadius: 9999,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}>
                                    <Text
                                      style={{
                                        color: isSun
                                          ? Colors.whiteColor
                                          : Colors.primaryColor,
                                      }}>
                                      S
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </View>

                              <View style={styles.spaceBetweenFullWidth}>
                                <View style={{ flexDirection: 'row' }}>
                                  <Switch
                                    value={switchDaily}
                                    onSyncPress={(value) => {
                                      setSwitchDaily(value);
                                    }}
                                    circleColorActive={Colors.whiteColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive={Colors.primaryColor}
                                  />
                                  <Text
                                    style={{
                                      marginLeft: 10,
                                    }}>
                                    Same reservation hours for all days
                                  </Text>
                                </View>

                                <View style={{ flexDirection: 'row' }}>
                                  {switchDaily ? (
                                    <View style={{ flexDirection: 'row' }}>
                                      {switchMerchant ? (
                                        <View
                                          style={styles.phoneRnPickerViewStyle}>
                                          <RNPickerSelect
                                            placeholder={{}}
                                            useNativeAndroidPickerStyle={false}
                                            style={{
                                              inputAndroid: {
                                                fontSize: 10,
                                                paddingVertical: 5,
                                                color: 'black',
                                                textAlign: 'center',
                                              },
                                              inputIOS: {
                                                fontSize: 10,
                                                paddingVertical: 5,
                                                color: 'black',
                                                textAlign: 'center',
                                              },
                                            }}
                                            items={timeOption}
                                            value={dailyHours1}
                                            onValueChange={(value) => {
                                              setDailyHours1(value);
                                            }}
                                          />
                                        </View>
                                      ) : (
                                        <View
                                          style={
                                            styles.tabletRnPickerViewStyle
                                          }>
                                          <RNPickerSelect
                                            placeholder={{}}
                                            style={{
                                              inputAndroidContainer: {
                                                height: 35,
                                                justifyContent: 'center',
                                                backgroundColor: '#fafafa',
                                                borderRadius: 4,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                              },
                                              inputAndroid: {
                                                //backgroundColor: '#fafafa',
                                                color: 'black',
                                                fontFamily:
                                                  'NunitoSans-Regular',
                                                fontSize: 16,
                                                borderWidth: 1,
                                                borderColor:
                                                  Colors.primaryColor,
                                                borderRadius: 5,
                                                width: '100%',
                                                paddingHorizontal: 10,
                                                height: 35,
                                                paddingLeft: 12,
                                                textAlign: 'center',
                                              },
                                              inputIOS: {
                                                //backgroundColor: '#fafafa',
                                                color: 'black',
                                                fontFamily:
                                                  'NunitoSans-Regular',
                                                fontSize: 16,
                                                borderWidth: 1,
                                                borderColor:
                                                  Colors.primaryColor,
                                                borderRadius: 5,
                                                width: '100%',
                                                paddingHorizontal: 10,
                                                height: 35,
                                                paddingLeft: 12,
                                                textAlign: 'center',
                                              },
                                              viewContainer: {
                                                backgroundColor: '#fafafa',
                                                borderRadius: 4,
                                                height: 35,
                                                width: windowWidth * 0.12,
                                                justifyContent: 'center',
                                                fontSize: 16,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                              },
                                            }}
                                            items={timeOption}
                                            value={dailyHours1}
                                            onValueChange={(value) => {
                                              setDailyHours1(value);
                                            }}
                                          />
                                        </View>
                                      )}
                                      <Text
                                        style={{
                                          paddingHorizontal: 10,
                                          height: switchMerchant ? 35 : 40,
                                          textAlignVertical: 'center',
                                        }}>
                                        -
                                      </Text>
                                      {switchMerchant ? (
                                        <View
                                          style={styles.phoneRnPickerViewStyle}>
                                          <RNPickerSelect
                                            placeholder={{}}
                                            useNativeAndroidPickerStyle={false}
                                            style={{
                                              inputAndroid: {
                                                fontSize: 10,
                                                paddingVertical: 5,
                                                color: 'black',
                                                textAlign: 'center',
                                              },
                                              inputIOS: {
                                                fontSize: 10,
                                                paddingVertical: 5,
                                                color: 'black',
                                                textAlign: 'center',
                                              },
                                            }}
                                            items={timeOption}
                                            value={dailyHours2}
                                            onValueChange={(value) => {
                                              setDailyHours2(value);
                                            }}
                                          />
                                        </View>
                                      ) : (
                                        <View
                                          style={
                                            styles.tabletRnPickerViewStyle
                                          }>
                                          <RNPickerSelect
                                            placeholder={{}}
                                            style={{
                                              inputAndroidContainer: {
                                                height: 35,
                                                justifyContent: 'center',
                                                backgroundColor: '#fafafa',
                                                borderRadius: 4,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                              },
                                              inputAndroid: {
                                                //backgroundColor: '#fafafa',
                                                color: 'black',
                                                fontFamily:
                                                  'NunitoSans-Regular',
                                                fontSize: 16,
                                                borderWidth: 1,
                                                borderColor:
                                                  Colors.primaryColor,
                                                borderRadius: 5,
                                                width: '100%',
                                                paddingHorizontal: 10,
                                                height: 35,
                                                paddingLeft: 12,
                                                textAlign: 'center',
                                              },
                                              inputIOS: {
                                                //backgroundColor: '#fafafa',
                                                color: 'black',
                                                fontFamily:
                                                  'NunitoSans-Regular',
                                                fontSize: 16,
                                                borderWidth: 1,
                                                borderColor:
                                                  Colors.primaryColor,
                                                borderRadius: 5,
                                                width: '100%',
                                                paddingHorizontal: 10,
                                                height: 35,
                                                paddingLeft: 12,
                                                textAlign: 'center',
                                              },
                                              viewContainer: {
                                                backgroundColor: '#fafafa',
                                                borderRadius: 4,
                                                height: 35,
                                                width: windowWidth * 0.12,
                                                justifyContent: 'center',
                                                fontSize: 16,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                              },
                                            }}
                                            items={timeOption}
                                            value={dailyHours2}
                                            onValueChange={(value) => {
                                              setDailyHours2(value);
                                            }}
                                          />
                                        </View>
                                      )}
                                    </View>
                                  ) : null}
                                </View>
                              </View>

                              {!switchDaily ? (
                                <ScrollView
                                  showsVerticalScrollIndicator={true}
                                  style={{
                                    height: windowHeight * 0.26,
                                    width: '100%',
                                  }}>
                                  {isMon ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Mondays</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours3}
                                                onValueChange={(value) => {
                                                  setDailyHours3(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours3}
                                                onValueChange={(value) => {
                                                  setDailyHours3(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours4}
                                                onValueChange={(value) => {
                                                  setDailyHours4(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours4}
                                                onValueChange={(value) => {
                                                  setDailyHours4(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isTue ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Tuesdays</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours5}
                                                onValueChange={(value) => {
                                                  setDailyHours5(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours5}
                                                onValueChange={(value) => {
                                                  setDailyHours5(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours6}
                                                onValueChange={(value) => {
                                                  setDailyHours6(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours6}
                                                onValueChange={(value) => {
                                                  setDailyHours6(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isWed ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Wednesdays</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours7}
                                                onValueChange={(value) => {
                                                  setDailyHours7(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours7}
                                                onValueChange={(value) => {
                                                  setDailyHours7(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours8}
                                                onValueChange={(value) => {
                                                  setDailyHours8(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours8}
                                                onValueChange={(value) => {
                                                  setDailyHours8(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isThur ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Thursday</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours9}
                                                onValueChange={(value) => {
                                                  setDailyHours9(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours9}
                                                onValueChange={(value) => {
                                                  setDailyHours9(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours10}
                                                onValueChange={(value) => {
                                                  setDailyHours10(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours10}
                                                onValueChange={(value) => {
                                                  setDailyHours10(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isFri ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Friday</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours11}
                                                onValueChange={(value) => {
                                                  setDailyHours11(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours11}
                                                onValueChange={(value) => {
                                                  setDailyHours11(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours12}
                                                onValueChange={(value) => {
                                                  setDailyHours12(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours12}
                                                onValueChange={(value) => {
                                                  setDailyHours12(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isSat ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Saturdays</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours3}
                                                onValueChange={(value) => {
                                                  setDailyHours13(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours3}
                                                onValueChange={(value) => {
                                                  setDailyHours13(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours14}
                                                onValueChange={(value) => {
                                                  setDailyHours14(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours14}
                                                onValueChange={(value) => {
                                                  setDailyHours14(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                  {isSun ? (
                                    <View style={styles.spaceBetweenFullWidth}>
                                      <Text>Sundays</Text>
                                      <View style={{ flexDirection: 'row' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours15}
                                                onValueChange={(value) => {
                                                  setDailyHours15(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours15}
                                                onValueChange={(value) => {
                                                  setDailyHours15(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                          <Text
                                            style={{
                                              paddingHorizontal: 10,
                                              height: switchMerchant ? 35 : 40,
                                              textAlignVertical: 'center',
                                            }}>
                                            -
                                          </Text>
                                          {switchMerchant ? (
                                            <View
                                              style={
                                                styles.phoneRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                useNativeAndroidPickerStyle={
                                                  false
                                                }
                                                style={{
                                                  inputAndroid: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    fontSize: 10,
                                                    paddingVertical: 5,
                                                    color: 'black',
                                                    textAlign: 'center',
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours16}
                                                onValueChange={(value) => {
                                                  setDailyHours16(value);
                                                }}
                                              />
                                            </View>
                                          ) : (
                                            <View
                                              style={
                                                styles.tabletRnPickerViewStyle
                                              }>
                                              <RNPickerSelect
                                                placeholder={{}}
                                                style={{
                                                  inputAndroidContainer: {
                                                    height: 35,
                                                    justifyContent: 'center',
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                  inputAndroid: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  inputIOS: {
                                                    //backgroundColor: '#fafafa',
                                                    color: 'black',
                                                    fontFamily:
                                                      'NunitoSans-Regular',
                                                    fontSize: 16,
                                                    borderWidth: 1,
                                                    borderColor:
                                                      Colors.primaryColor,
                                                    borderRadius: 5,
                                                    width: '100%',
                                                    paddingHorizontal: 10,
                                                    height: 35,
                                                    paddingLeft: 12,
                                                    textAlign: 'center',
                                                  },
                                                  viewContainer: {
                                                    backgroundColor: '#fafafa',
                                                    borderRadius: 4,
                                                    height: 35,
                                                    width: windowWidth * 0.12,
                                                    justifyContent: 'center',
                                                    fontSize: 16,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                  },
                                                }}
                                                items={timeOption}
                                                value={dailyHours16}
                                                onValueChange={(value) => {
                                                  setDailyHours16(value);
                                                }}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  ) : null}
                                </ScrollView>
                              ) : null}

                              <View style={styles.spaceBetweenFullWidth}>
                                <Text>Repeat every</Text>
                                {switchMerchant ? (
                                  <View style={styles.phoneRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      useNativeAndroidPickerStyle={false}
                                      style={{
                                        inputAndroid: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          fontSize: 10,
                                          paddingVertical: 5,
                                          color: 'black',
                                          textAlign: 'center',
                                        },
                                      }}
                                      items={[
                                        {
                                          label: '1 Week',
                                          value: '1',
                                        },
                                        {
                                          label: '2 Weeks',
                                          value: '2',
                                        },
                                        {
                                          label: '3 Weeks',
                                          value: '3',
                                        },
                                        {
                                          label: '4 Weeks',
                                          value: '4',
                                        },
                                      ]}
                                      value={weeklyRepeat}
                                      onValueChange={(value) => {
                                        setWeeklyRepeat(value);
                                      }}
                                    />
                                  </View>
                                ) : (
                                  <View style={styles.tabletRnPickerViewStyle}>
                                    <RNPickerSelect
                                      placeholder={{}}
                                      style={{
                                        inputAndroidContainer: {
                                          height: 35,
                                          justifyContent: 'center',
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                        inputAndroid: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        inputIOS: {
                                          //backgroundColor: '#fafafa',
                                          color: 'black',
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: 16,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          borderRadius: 5,
                                          width: '100%',
                                          paddingHorizontal: 10,
                                          height: 35,
                                          paddingLeft: 12,
                                          textAlign: 'center',
                                        },
                                        viewContainer: {
                                          backgroundColor: '#fafafa',
                                          borderRadius: 4,
                                          height: 35,
                                          width: windowWidth * 0.12,
                                          justifyContent: 'center',
                                          fontSize: 16,
                                          shadowColor: '#000',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                        },
                                      }}
                                      items={[
                                        {
                                          label: '1 Week',
                                          value: '1',
                                        },
                                        {
                                          label: '2 Weeks',
                                          value: '2',
                                        },
                                        {
                                          label: '3 Weeks',
                                          value: '3',
                                        },
                                        {
                                          label: '4 Weeks',
                                          value: '4',
                                        },
                                      ]}
                                      value={weeklyRepeat}
                                      onValueChange={(value) => {
                                        setWeeklyRepeat(value);
                                      }}
                                    />
                                  </View>
                                )}
                              </View>

                              <View style={styles.spaceBetweenFullWidth}>
                                <DateTimePickerModal
                                  //supportedOrientations={['landscape', 'portrait']}
                                  isVisible={timeDaysDateTimePicker2}
                                  mode={'date'}
                                  onConfirm={(text) => {
                                    setRev_date2(moment(text));
                                    setTimeDaysDateTimePicker2(false);
                                  }}
                                  onCancel={() => {
                                    setTimeDaysDateTimePicker2(false);
                                  }}
                                  style={{ zIndex: 1000 }}
                                  minimumDate={moment().add(1, 'days').toDate()}
                                />
                                <Text>Stop repeating</Text>
                                <TouchableOpacity
                                  onPress={() => {
                                    setTimeDaysDateTimePicker2(true);
                                  }}
                                  style={[
                                    {
                                      width: windowWidth * 0.13,
                                      height: 40,
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      flexDirection: 'row',
                                      borderRadius: 10,
                                      paddingVertical: 10,
                                      paddingHorizontal: 20,
                                      justifyContent: 'center',
                                      backgroundColor: Colors.fieldtBgColor,
                                      shadowOpacity: 0,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: 1,
                                    },
                                    switchMerchant
                                      ? {
                                        height: 35,
                                      }
                                      : {},
                                  ]}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Regular',
                                        flex: 9,
                                        fontSize: 14,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {rev_date2 === ''
                                      ? 'Never'
                                      : moment(rev_date2).format(
                                        'ddd DD/MMM/yyyy',
                                      )}
                                  </Text>
                                  {rev_date2 !== '' ? (
                                    <TouchableOpacity
                                      style={{ flex: 1, alignItems: 'flex-end' }}
                                      onPress={() => {
                                        setRev_date2('');
                                      }}>
                                      <AntDesign
                                        name="closecircle"
                                        size={15}
                                        color={Colors.fieldtTxtColor}
                                      />
                                    </TouchableOpacity>
                                  ) : (
                                    <></>
                                  )}
                                </TouchableOpacity>
                              </View>
                            </View>
                          ) : null}
                        </View>
                      ) : null}
                      {isPacing ? (
                        <View style={styles.flexOne}>
                          <Text
                            style={{
                              marginBottom: windowHeight * 0.01,
                              color: 'black',
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                              alignSelf: 'center'
                            }}>
                            Interval & Gaps
                          </Text>
                          <View style={styles.spaceBetweenFullWidth}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Reservation Interval
                            </Text>
                            {switchMerchant ? (
                              <View style={styles.phoneRnPickerViewStyle}>
                                <RNPickerSelect
                                  placeholder={{}}
                                  useNativeAndroidPickerStyle={false}
                                  style={{
                                    inputAndroid: {
                                      fontSize: 10,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: 10,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={turnTimeOption}
                                  value={pacingRes}
                                  onValueChange={(value) => {
                                    setPacingRes(+value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View style={styles.tabletRnPickerViewStyle}>
                                <RNPickerSelect
                                  placeholder={{}}
                                  style={{
                                    inputAndroidContainer: {
                                      height: 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      // width: windowWidth * 0.15,
                                      fontSize: 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: '100%',
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      // width: windowWidth * 0.15,
                                      fontSize: 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: '100%',
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: 35,
                                      width: windowWidth * 0.15,
                                      justifyContent: 'center',
                                      fontSize: 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={turnTimeOption}
                                  value={pacingRes}
                                  onValueChange={(value) => {
                                    setPacingRes(+value);
                                  }}
                                />
                              </View>
                            )}
                          </View>
                          <View style={styles.spaceBetweenFullWidth}>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Guest Limit
                            </Text>
                            <TextInput
                              value={guestLimit.toString()}
                              keyboardType="numeric"
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              placeholderStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              placeholder={'Guest limit'}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                //borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              //iOS
                              // clearTextOnFocus={true}
                              selectTextOnFocus
                              //Android
                              // onFocus={() => {
                              //   setTemp(guestLimit);
                              //   setGuestLimit('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (guestLimit == '') {
                              //     setGuestLimit(isNaN(+temp) ? 30 : +temp);
                              //   }
                              // }}
                              ///////////////////////////////////////////////
                              onChangeText={(text) => {
                                setGuestLimit(isNaN(+text) ? 30 : +text);
                              }}
                            />
                          </View>
                          <View>
                            {/* <Text>Pacing</Text>
                        <Text>
                          Pacing helps you control the flow guests &
                          reservations to suit your business needs
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '100%',
                            paddingHorizontal: 10,
                          }}>
                          <Text style={{ width: '15%' }}>Time</Text>
                          <Text style={{ width: '22.5%' }}>Display end time</Text>
                          <Text style={{ width: '27.5%' }}>Pacing</Text>
                          <Text style={{ width: '17.5%' }}>Guest Limit</Text>
                          <Text style={{ width: '17.5%' }}>Rsv Limit</Text>
                        </View>
                        <FlatList
                          data={pacingList}
                          renderItem={renderPacing}
                          keyExtractor={(item, index) => String(index)}
                          style={{ marginTop: 10 }}
                          nestedScrollEnabled={true}
                          contentContainerStyle={{}}
                        /> */}
                          </View>
                        </View>
                      ) : null}
                      {isRoom ? (
                        <View style={{ height: windowHeight * 0.7 }}>
                          <Text
                            style={{
                              marginBottom: windowHeight * 0.01,
                              color: 'black',
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                              alignSelf: 'center',
                            }}>
                            Rooms
                          </Text>
                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: windowHeight * 0.01,
                            }}
                            onPress={() => {
                              setIsAllRoom(true);
                            }}>
                            <View
                              style={{
                                backgroundColor: isAllRoom
                                  ? Colors.primaryColor
                                  : Colors.whiteColor,
                                borderRadius: 9999,
                                height: 25,
                                width: 25,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                marginRight: 15,
                              }}></View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              All Rooms
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}
                            onPress={() => {
                              setIsAllRoom(false);
                            }}>
                            <View
                              style={{
                                backgroundColor: !isAllRoom
                                  ? Colors.primaryColor
                                  : Colors.whiteColor,
                                borderRadius: 9999,
                                height: 25,
                                width: 25,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                marginRight: 15,
                              }}></View>
                            <Text
                              style={{
                                color: 'black',
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              Select the rooms this availability applies to
                            </Text>
                          </TouchableOpacity>
                          {!isAllRoom ? (
                            <ScrollView style={{}}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  flexWrap: 'wrap',
                                }}>
                                {roomAreaOption.map((item, index) => {
                                  return (
                                    <TouchableOpacity
                                      onPress={() => {
                                        for (
                                          let i = 0;
                                          i < roomAreaOption.length;
                                          i++
                                        ) {
                                          if (
                                            item.value ===
                                            roomAreaOption[i].value
                                          ) {
                                            let tempData = [...roomAreaOption];
                                            tempData[i].isSelect =
                                              !item.isSelect;
                                            setRoomAreaOption(tempData);
                                            break;
                                          }
                                        }
                                      }}
                                      style={{
                                        marginRight: windowWidth * 0.01,
                                        marginTop: windowWidth * 0.01,
                                        paddingHorizontal: 10,
                                        paddingVertical: 5,
                                        borderRadius: 5,
                                        backgroundColor: Colors.lightPrimary,
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        shadowOpacity: 0,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      }}>
                                      <View
                                        style={{
                                          backgroundColor: item.isSelect
                                            ? Colors.primaryColor
                                            : Colors.whiteColor,
                                          borderRadius: 9999,
                                          height: 25,
                                          width: 25,
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          marginRight: 10,
                                        }}></View>
                                      <Text>{item.label}</Text>
                                    </TouchableOpacity>
                                  );
                                })}
                              </View>
                            </ScrollView>
                          ) : null}
                        </View>
                      ) : null}
                      {/* </ScrollView> */}
                    </View>
                  </View>
                </View>
                <View
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      addUpdateReservationAvailability();
                    }}
                    style={{
                      justifyContent: 'center',
                      //flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      width: Dimensions.get('window').width * 0.13,
                      height: Dimensions.get('window').height * 0.05,
                      paddingHorizontal: 10,
                      //height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: 7,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      {isAddAvailability ? (
                        <AntDesign
                          name="pluscircle"
                          size={switchMerchant ? 10 : windowWidth < 1000 ? 15 : 20}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <MaterialCommunityIcons
                          name="update"
                          size={switchMerchant ? 10 : windowWidth < 1000 ? 15 : 20}
                          color={Colors.whiteColor}
                        />
                      )}
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : windowWidth < 1000 ? 14 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        AVAILABILITY
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>

          {/* Modal end */}

          {/* Top bar */}
          {/* <View
          style={{
            flexDirection: 'row',
            backgroundColor: Colors.darkBgColor,
            justifyContent: 'space-between',
            alignItems: 'center',
            flex: 0.8,
          }}> */}
          {/* <View style={{ flex: 0.25 }}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
              onPress={() => {
                props.navigation.navigate('NewSettingsScreen');
              }}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <Plus name="x" size={30} color={Colors.whiteColor} style={{}} />
              </View>
            </TouchableOpacity>
          </View>

          <View
            style={[
              styles.flexOne,
              {
                backgroundColor:
                  openPage == 'table' ? Colors.modalBgColor : Colors.darkBgColor,
              },
            ]}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                height: '100%',
              }}
              onPress={() => {
                // CommonStore.update((s) => {
                //   s.venueSettingPage = 'table';
                // });
                navigation.navigate('VenueSettingsTableSetupScreen');
              }}>
              <Text
                style={{
                  textAlign: 'center',
                  color: Colors.whiteColor,
                  textDecorationLine: openPage == 'table' ? 'underline' : 'none',
                }}>
                Table setup
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={[
              styles.flexOne,
              {
                backgroundColor:
                  openPage == 'tableCombinations'
                    ? Colors.modalBgColor
                    : Colors.darkBgColor,
              },
            ]}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                height: '100%',
              }}
              onPress={() => {
                // CommonStore.update((s) => {
                //   s.venueSettingPage = 'tableCombinations';
                // });
                navigation.navigate('VenueSettingsCombinationScreen');
              }}>
              <Text
                style={{
                  textAlign: 'center',
                  color: Colors.whiteColor,
                  textDecorationLine:
                    openPage == 'tableCombinations' ? 'underline' : 'none',
                }}>
                Table Combinations
              </Text>
            </TouchableOpacity>
          </View> */}
          {/* <View
            style={[
              styles.flexOne,
              {
                backgroundColor: Colors.primaryColor,
              },
            ]}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                height: '100%',
              }}
              onPress={() => {
                //   CommonStore.update((s) => {
                //     s.venueSettingPage = 'reservation';
                //   });
                setShowUpdAvModal(true)
              }}>
              <Text
                style={{
                  textAlign: 'center',
                  color: Colors.whiteColor,
                  fontSize: 20,
                  //textDecorationLine: 'underline',
                }}>
                Reservation Availability
              </Text>
            </TouchableOpacity>
          </View>
        </View> */}

          {/* Page Content */}
          <View style={{ flex: 9 }}>
            <View>
              <View
                style={{
                  flexDirection: 'row',
                  paddingHorizontal: windowWidth * 0.01,
                  paddingVertical: windowHeight * 0.01,
                  justifyContent: 'space-between',
                }}>
                <View>
                  <TouchableOpacity
                    onPress={() => {
                      setShowAddAvModal(true);

                      setIsBasic(true);
                      setIsTimeDays(false);
                      setIsPacing(false);
                      setIsRoom(false);
                      setIsColor(false);
                      setBasicColor('#DCECE5');

                      setIsAddAvailability(true);
                    }}>
                    <View
                      style={{
                        paddingHorizontal: 5,
                        paddingVertical: 4,
                        borderRadius: 5,
                        backgroundColor: Colors.primaryColor,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <AntDesign
                        name="pluscircle"
                        size={switchMerchant ? 10 : windowWidth < 1000 ? 15 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : windowWidth < 1000 ? 14 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        AVAILABILITY
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    alignContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      color: Colors.blackColor,
                      fontSize: 18,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    {calendarStartDate}
                  </Text>
                  <Text
                    style={{
                      color: Colors.descriptionColor,
                      fontSize: 12,
                      fontFamily: 'NunitoSans-Regular',
                    }}>
                    {'< Swipe to navigate >'}
                  </Text>
                </View>

                <View
                  style={{
                    width: '15%',
                  }}></View>
              </View>

              <Calendar
                ampm={true}
                //open={'openButton'}

                onChangeDate={(date) => {
                  setCalendarStartDate(moment(date[0]).format('DD MMM YYYY'));
                }}
                onPressDateHeader={(day) => {
                  // console.log('pacing', day);
                  setShowPacingModal(true);
                }}
                onPressStatusOpen={(day) => {
                  // console.log('open', day);
                  // day in format (2021-12-20T05:38:23.853Z) => need update database open close status using date as key
                }}
                onPressStatusClose={(day) => {
                  // console.log('close', day);
                  // day in format (2021-12-20T05:38:23.853Z) => need update database open close status using date as key
                }}
                onPressEvent={(event) => {
                  // console.log('Pressed Event!', event);

                  setIsAddAvailability(false);

                  // store the event data into modal state
                  restoreModalState(event);

                  // call the modal to edit the event
                  setShowAddAvModal(true);
                }}
                // swipeEnabled={false}
                weekStartsOn={1}
                weekEndsOn={0}
                renderEvent={renderEvents}
                events={reservationThisWeek}
                headerContainerStyle={{ borderWidth: 1 }}
                headerComponentStyle={{ borderWidth: 1, width: 100, height: 150 }}
                dayHeaderStyle={{ marginBottom: 5 }}
                dayHeaderHighlightColor={Colors.primaryColor}
                weekDayHeaderHighlightColor={Colors.primaryColor}
                height={windowHeight * 0.86}
              />
            </View>
          </View>
        </View>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.5,
    height: Dimensions.get('window').height * 0.2,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
  },
  flexOne: {
    flex: 1,
    // borderWidth: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').height * 0.6,
    width: Dimensions.get('window').width * 0.75,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.02,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.01,
    top: Dimensions.get('window').height * 0.02,
    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    // flex: 1,
    alignItems: 'center',
    // borderWidth: 1,
  },
  modalBody: {
    flex: 8,
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexDirection: 'column',
  },
  modalBodyRow: {
    flex: 1,
    justifyContent: 'space-around',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 28,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  addModalBodySize: {
    width: Dimensions.get('window').width * 0.3,
  },
  addModalBodyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: Dimensions.get('window').width * 0.01,
    paddingVertical: Dimensions.get('window').height * 0.05,
  },
  addModalButton: {
    backgroundColor: Colors.primaryColor,
    paddingVertical: Dimensions.get('window').height * 0.01,
    paddingHorizontal: Dimensions.get('window').width * 0.02,
    borderRadius: 5,
  },
  tabletRnPickerViewStyle: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  phoneRnPickerViewStyle: {
    backgroundColor: '#fafafa',
    // backgroundColor: 'green',
    borderRadius: 4,
    height: Dimensions.get('window').height * 0.08,
    width: Dimensions.get('window').width * 0.13,
    justifyContent: 'center',
    fontSize: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    // left: windowWidth * -0.002,
  },
  spaceBetweenFullWidth: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    marginVertical: Dimensions.get('window').height * 0.01,
    height: isTablet() ? 40 : 35,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
});

export default SettingIntervalScreen;
