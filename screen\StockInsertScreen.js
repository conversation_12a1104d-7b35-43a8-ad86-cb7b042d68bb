import { Text } from "react-native-fast-text";
import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Modal as ModalComponent,
  PermissionsAndroid,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import GCalendar from '../assets/svg/GCalendar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePicker from "react-native-modal-datetime-picker";
import AntDesign from 'react-native-vector-icons/AntDesign';
import moment from 'moment';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Styles from '../constant/Styles'
import DocumentPicker from 'react-native-document-picker';
import RNFetchBlob from 'rn-fetch-blob';
import {
  isTablet
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { PURCHASE_ORDER_STATUS, PURCHASE_ORDER_STATUS_PARSED, EXPAND_TAB_TYPE, } from '../constant/common';
import { UserStore } from '../store/userStore';
import { convertArrayToCSV, parseValidPriceText } from '../util/common';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const StockInsertScreen = props => {
  const {
    navigation,
  } = props;

  const [purchaseOrder, setPurchaseOrder] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}]);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [search, setSearch] = useState('');

  const [loading, setLoading] = useState(false);
  const [choice2, setChoice2] = useState('Print P.O');

  ///////////////////////////////////////////////////////////////////
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [poId, setPoId] = useState('');

  const [supplierDropdownList, setSupplierDropdownList] = useState([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState('');

  const [poStatus, setPoStatus] = useState(PURCHASE_ORDER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [supplyItemDropdownList, setSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      supplyItemId: '',
      name: '',
      sku: '',
      quantity: 0,
      orderQuantity: 0,
      receivedQuantity: 0,
      price: 0,
      totalPrice: 0,
    }
  ]);

  const [selectedSupplier, setSelectedSupplier] = useState({
    taxRate: 0,
  });

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [expandViewDict, setExpandViewDict] = useState({});

  const outletSupplyItemsSkuDict = CommonStore.useState(s => s.outletSupplyItemsSkuDict);

  // const supplyItems = CommonStore.useState(s => s.supplyItems);
  const [supplyItems, setSupplyItems] = useState([]);
  const supplyItemsAll = CommonStore.useState(s => s.supplyItems);

  const supplyItemsDict = CommonStore.useState(s => s.supplyItemsDict);
  const suppliers = CommonStore.useState(s => s.suppliers);
  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const purchaseOrders = CommonStore.useState(s => s.purchaseOrders);

  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);

  const selectedPurchaseOrderEdit = CommonStore.useState(s => s.selectedPurchaseOrderEdit);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    if (selectedPurchaseOrderEdit) {
      // insert info

      setPoId(selectedPurchaseOrderEdit.poId);
      setSelectedSupplierId(selectedPurchaseOrderEdit.supplierId);
      setPoStatus(selectedPurchaseOrderEdit.status);
      setSelectedTargetOutletId(selectedPurchaseOrderEdit.outletId);
      setDate(selectedPurchaseOrderEdit.estimatedArrivalDate);

      if (selectedPurchaseOrderEdit.poItems) {
        setPoItems(selectedPurchaseOrderEdit.poItems);
      }
    }
    else {
      // designed to always mounted, thus need clear manually...

      setPoId('');
      setSelectedSupplierId('');
      setPoStatus('');
      setSelectedTargetOutletId('');
      setDate(Date.now());

      if (supplyItems.length > 0 && Object.keys(outletSupplyItemsSkuDict).length > 0) {
        setPoItems([
          {
            supplyItemId: supplyItems[0].uniqueId,
            name: supplyItems[0].name,
            sku: supplyItems[0].sku,
            quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
            orderQuantity: 0,
            receivedQuantity: 0,
            price: supplyItems[0].price,
            totalPrice: 0,
          }
        ]);

      }
      else {
        setPoItems([
          {
            supplyItemId: '',
            name: '',
            sku: '',
            quantity: 0,
            orderQuantity: 0,
            receivedQuantity: 0,
            price: 0,
            totalPrice: 0,
          }
        ]);
      }
    }
  }, [selectedPurchaseOrderEdit]);

  useEffect(() => {
    setSupplyItems(supplyItemsAll.filter(supplyItem => {
      if (supplyItem.supplierId === selectedSupplierId) {
        return true;
      }
    }));
  }, [supplyItemsAll, selectedSupplierId]);

  useEffect(() => {
    setSupplierDropdownList(suppliers.map(supplier => ({ label: supplier.name, value: supplier.uniqueId })));

    if (selectedSupplierId === '' && suppliers.length > 0) {
      setSelectedSupplierId(suppliers[0].uniqueId);
    }
  }, [suppliers]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setSupplyItemDropdownList(supplyItems.map(supplyItem => {
      // if (selectedSupplierId === supplyItem.supplierId) {
      //   return { label: supplyItem.name, value: supplyItem.uniqueId };
      // }      

      return { label: supplyItem.name, value: supplyItem.uniqueId };
    }));

    if (supplyItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].supplyItemId === '') {
      setPoItems([
        {
          supplyItemId: supplyItems[0].uniqueId,
          name: supplyItems[0].name,
          sku: supplyItems[0].sku,
          quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
          orderQuantity: 0,
          receivedQuantity: 0,
          price: supplyItems[0].price,
          totalPrice: 0,
        }
      ]);
    }
    else if (poItems[0].supplyItemId !== '' &&
      Object.keys(supplyItemsDict).length > 0) {
      var poItemsTemp = [
        ...poItems,
      ];

      for (var i = 0; i < poItemsTemp.length; i++) {
        const supplyItem = supplyItemsDict[poItemsTemp[i].supplyItemId];

        poItemsTemp[i] = {
          ...poItemsTemp[i],
          quantity: outletSupplyItemsSkuDict[supplyItem.sku] ? outletSupplyItemsSkuDict[supplyItem.sku].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
          price: supplyItem.price, // might changed in real time
        };
      }

      setPoItems(poItemsTemp);
    }
  }, [supplyItems, supplyItemsDict, outletSupplyItemsSkuDict]);

  useEffect(() => {
    if (suppliers.length > 0 && selectedSupplierId !== '') {
      setSelectedSupplier(suppliers.find(supplier => supplier.uniqueId === selectedSupplierId));
    }
  }, [suppliers, selectedSupplierId]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
  }, [poItems]);

  useEffect(() => {
    // console.log('taxTotal');
    // console.log(subtotal * selectedSupplier.taxRate);
    setTaxTotal(subtotal * selectedSupplier.taxRate);
  }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log((subtotal - discountTotal) + taxTotal);
    setFinalTotal((subtotal - discountTotal) + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: Dimensions.get('screen').width * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={[{
        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
        // bottom: switchMerchant ? '2%' : 0,
        ...global.getHeaderTitleStyle(),
      },
      // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
      ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Purchase Order
        </Text>
      </View>
    ),
    headerRight: () => (
      <TouchableOpacity onPress={() => {
        if (global.currUserRole === 'admin') {
          navigation.navigate('Setting');
        }
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          // backgroundColor: 'red',
        }}>
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.whiteColor,
            }}>
            {merchantName}
          </Text>

          <View style={{
            backgroundColor: 'white',
            width: 0.5,
            height: Dimensions.get('screen').height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}>
          </View>

          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}>
            {userName}
          </Text>

          {/* <Text
                  style={{
                    fontWeight: 'bold',
                    marginRight: 10,
                    color: Colors.secondaryColor,
                  }}>
                  Logout
                </Text> */}

          <View style={{
            marginRight: 30,
            width: Dimensions.get('screen').height * 0.05,
            height: Dimensions.get('screen').height * 0.05,
            borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
          }}>
            <Image style={{
              width: Dimensions.get('screen').height * 0.035,
              height: Dimensions.get('screen').height * 0.035,
              alignSelf: 'center',
            }} source={require('../assets/image/profile-pic.jpg')} />
          </View>
        </View>

      </TouchableOpacity>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder();

  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "KooDoo Merchant Storage Permission",
          message:
            "KooDoo Merchant App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log("Storage permission granted");
      } else {
        // console.log("Storage permission denied");
      }
    } catch (err) {
      console.warn(err);
    }
  }

  const importCSV = () => {
    try {
      const res = DocumentPicker.pickSingle({
        type: [DocumentPicker.types.csv],
      });
      // console.log(
      //   res.uri,
      //   res.type,
      //   res.name,
      //   res.size
      // );
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        throw err;
      }
    }
  }

  const exportFunc = () => {
    if (purchaseOrders) {
      const csvData = convertArrayToCSV(purchaseOrders);

      const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-purchase-order-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
      // console.log("PATH", pathToWrite)
      RNFetchBlob.fs
        .writeFile(pathToWrite, csvData, 'utf8')
        .then(() => {
          // console.log(`wrote file ${pathToWrite}`);
          // wrote file /storage/emulated/0/Download/data.csv
          Alert.alert(
            'Success',
            `Send to ${pathToWrite}`,
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        })
        .catch(error => console.error(error));
    }

    // var body = {
    //   data: orderList
    // }
    // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
    //   // console.log("RESULT", result)
    //   if (result !== null) {
    //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/POData.csv`;
    //     // console.log("PATH", pathToWrite)
    //     RNFetchBlob.fs
    //       .writeFile(pathToWrite, result, 'utf8')
    //       .then(() => {
    //         // console.log(`wrote file ${pathToWrite}`);
    //         // wrote file /storage/emulated/0/Download/data.csv
    //         Alert.alert(
    //           'Success',
    //           'The data had exported',
    //           [{ text: 'OK', onPress: () => { } }],
    //           { cancelable: false },
    //         );
    //       })
    //       .catch(error => console.error(error));
    //   }
    // });
  }

  // function here
  const showDateTimePicker = () => {
    // setState({ isDateTimePickerVisible: true });
    setIsDateTimePickerVisible(true);
  };

  const hideDateTimePicker = () => {
    // setState({ isDateTimePickerVisible: false });
    setIsDateTimePickerVisible(false);
  };

  const handleDatePicked = date => {

    // setState({ date: date.toString() });
    setDate(date);
  };

  const renderOrderItem = ({ item }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    return (
      item.poItems.map((element, index) => {
        return (
          <TouchableOpacity onPress={() => {
            // setState({
            //   lowStockAlert: false,
            //   purchaseOrder: false,
            //   stockTransfer: false,
            //   stockTake: false,
            //   addPurchase: false,
            //   editPurchase: true,
            //   addStockTransfer: false,
            //   addStockTake: false,
            // });

            // disable first
            // setEditPurchase(true);
            // setPurchaseOrder(false);

            CommonStore.update(s => {
              s.selectedPurchaseOrderEdit = item;
            });

            setPurchaseOrder(false);
            setAddPurchase(true);
          }}>
            <View
              style={{
                backgroundColor: '#ffffff',
                flexDirection: 'row',
                paddingVertical: 20,
                paddingHorizontal: 20,
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderBottomColor: '#c4c4c4',
              }}>


              <Text style={{ width: '11%', color: Colors.primaryColor }}>{`PO${item.poId}`}</Text>
              <Text style={{ width: '13%' }}>{moment(item.orderDate).format('DD/MM/YYYY')}</Text>
              <Text style={{ width: '15%' }}>
                {moment(item.estimatedArrivalDate).format('DD/MM/YYYY')}
              </Text>
              <Text style={{ width: '15%' }}>{item.outletName}</Text>
              <Text style={{ width: '17%' }}>{item.supplierName}</Text>
              <Text style={{ width: '15%' }}>RM{element.orderQuantity}</Text>
              <View style={{
                width: '12%',
                height: 30,
                alignItems: "center",
                backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? '#969696' : item.status == 2 ? Colors.secondaryColor : Colors.primaryColor,
                borderRadius: 10,
                justifyContent: 'center',
                alignContent: 'center'
              }}>
                <Text style={{
                  color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.whiteColor : item.status == 2 ? Colors.blackColor : Colors.whiteColor,
                  borderRadius: 10
                }}>
                  {/* {item.status == 0 ? "Fail" : item.status == 1 ? "In Progress" : item.status == 2 ? "Arrived" : "Completed"} */}
                  {PURCHASE_ORDER_STATUS_PARSED[item.status]}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        )
      })
    );
  }

  const renderAddPO = ({ item, index }) => {
    // // console.log('poItems[index]');
    // // console.log(poItems[index]);
    // // console.log('supplyItemDropdownList');
    // // console.log(supplyItemDropdownList);

    // var isValidToRender = false;

    // for (var i = 0; i < supplyItems.length; i++) {

    // }

    // becoz poitems don't store supplier id
    if (
      poItems[index].supplierId === selectedSupplierId
      // true
    ) {
      return (
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            alignItems: 'center',
            // height: (Dimensions.get('window').width * 0.1) * 3,
          }}>


          <View style={{ width: 120, color: '#969696' }}>
            <DropDownPicker
              // items={[
              //   {
              //     label: 'Chicken patty',
              //     value: 'Chicken patty',
              //   },
              //   {
              //     label: 'Beef patty',
              //     value: 'Beef patty',
              //   },
              //   {
              //     label: 'lamb chop',
              //     value: 'lamb chop',
              //   },
              // ]}
              items={supplyItemDropdownList}
              defaultValue={poItems[index].supplyItemId ? poItems[index].supplyItemId : ''}
              placeholder="Supply Items"
              containerStyle={{ height: 30 }}
              style={{
                backgroundColor: '#FAFAFA',
                // elevation: 10,
              }}
              itemStyle={{
                justifyContent: 'flex-start',
              }}
              dropDownStyle={{
                backgroundColor: '#FAFAFA',
                elevation: 30,
              }}
              onChangeItem={(item) =>
                // setState({
                //   choice4: item.value,
                // })
                setPoItems(poItems.map((poItem, i) => (i === index ? {
                  ...poItem,
                  supplyItemId: item.value,
                  sku: supplyItemsDict[item.value].sku,
                  quantity: outletSupplyItemsSkuDict[supplyItemsDict[item.value].sku] ? outletSupplyItemsSkuDict[supplyItemsDict[item.value].sku].quantity : 0,
                  orderQuantity: 0,
                  receivedQuantity: 0,
                  price: supplyItemsDict[item.value].price,
                  totalPrice: 0,
                } : poItem)))
              }
            />
          </View>

          <Text style={{ width: '2%' }}></Text>
          <Text style={{ width: '16%', color: '#8f8f8f' }}>
            {poItems[index].sku}
          </Text>
          <Text style={{ width: '6%', color: '#8f8f8f' }}>
            {poItems[index].quantity}
          </Text>

          {/* <View style={{
          width: '12%',
          alignItems: "center",
          backgroundColor: '#f5f5f5',
          borderRadius: 10,
          padding: 10,
        }}>
          <Text style={{ color: '#8f8f8f' }}>50</Text>
        </View> */}

          <View style={{
            width: '12%',
            // marginLeft: 50,
            // backgroundColor: 'blue',
          }}>
            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: 100,
                height: 40,
                borderRadius: 5,
                padding: 5,
                marginVertical: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                paddingLeft: 10,
              }}
              placeholder={'50'}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              keyboardType={'decimal-pad'}
              // placeholder={itemName}
              onChangeText={(text) => {
                // setState({ itemName: text });
                setPoItems(poItems.map((poItem, i) => (i === index ? {
                  ...poItem,
                  orderQuantity: parseInt(text),
                  totalPrice: parseInt(text) * poItem.price,
                } : poItem)))
              }}
              value={poItems[index].orderQuantity}
            // ref={myTextInput}
            />
          </View>

          {/* <Text style={{ width: '14%', marginLeft: 50, color: '#8f8f8f' }}>50</Text> */}

          {/* <View style={{
          width: '14%',
          marginLeft: 30,
          marginRight: 30,
          // backgroundColor: 'red',
        }}>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'50'}
            keyboardType={'decimal-pad'}
            // placeholder={itemName}
            // onChangeText={(text) => {
            //   // setState({ itemName: text });
            //   setPoItems(poItems.map((poItem, i) => (i === index ? {
            //     ...poItem,
            //     orderQuantity: parseInt(text),
            //     totalPrice: parseInt(text) * poItem.price,
            //   } : poItem)))
            // }}
            value={poItems[index].receivedQuantity}
          // ref={myTextInput}
          />
        </View> */}

          <Text style={{
            width: '14%',
            marginLeft: 30,
            marginRight: 30,
            color: '#8f8f8f'
          }}>{poItems[index].receivedQuantity}</Text>

          <Text style={{ width: '18%', color: '#8f8f8f' }}>{`RM${(poItems[index].price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
          <Text style={{ width: '10%', color: '#8f8f8f' }}>{`RM${poItems[index].totalPrice.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
          <TouchableOpacity style={{ marginLeft: 10 }}
            onPress={() => {
              setPoItems([
                ...poItems.slice(0, index),
                ...poItems.slice(index + 1),
              ]);
            }}>
            <Icon name="trash-2" size={20} color="#eb3446" />
          </TouchableOpacity>
        </View>
      );
    }
  };

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email
    }
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     Alert.alert(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }

  // const createStockOrder = () => {
  //   var body = {
  //     poId: poId,
  //     poItems: poItems,
  //     supplierId: selectedSupplierId,
  //     status: poStatus,
  //     outletId: selectedTargetOutletId,
  //     tax: taxTotal,
  //     discount: discountTotal,
  //     totalPrice: subtotal,
  //     finalTotal: finalTotal,
  //     estimatedArrivalDate: date,

  //     merchantId: merchantId,
  //     remarks: '',
  //   };

  //   ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
  //     if (result && result.uniqueId) {
  //       Alert.alert(
  //         'Success',
  //         'Purchase order created successfully',
  //         [
  //           { text: "OK", onPress: () => { props.navigation.goBack() } }
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   });
  // }

  const createPurchaseOrder = () => {
    // console.log('on createPurchaseOrder');

    if (selectedPurchaseOrderEdit === null) {
      var body = {
        poId: poId,
        poItems: poItems,
        supplierId: selectedSupplierId,
        status: poStatus,
        outletId: selectedTargetOutletId,
        tax: +(parseFloat(taxTotal).toFixed(2)),
        discount: +(parseFloat(discountTotal).toFixed(2)),
        totalPrice: +(parseFloat(subtotal).toFixed(2)),
        finalTotal: +(parseFloat(finalTotal).toFixed(2)),
        estimatedArrivalDate: moment(date).valueOf(),

        outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,
        supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

        merchantId: merchantId,
        remarks: '',
      };

      // console.log(body);

      ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Purchase order has been created',
            [
              { text: "OK", onPress: () => { props.navigation.goBack() } }
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert(
            'Error',
            'Failed to create Purchase order',
          );
        }
      });
    }
    else {
      var body = {
        poId: poId,
        poItems: poItems,
        supplierId: selectedSupplierId,
        status: poStatus,
        outletId: selectedTargetOutletId,
        tax: +(parseFloat(taxTotal).toFixed(2)),
        discount: +(parseFloat(discountTotal).toFixed(2)),
        totalPrice: +(parseFloat(subtotal).toFixed(2)),
        finalTotal: +(parseFloat(finalTotal).toFixed(2)),
        estimatedArrivalDate: moment(date).valueOf(),

        outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,
        supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

        merchantId: merchantId,
        remarks: '',

        uniqueId: selectedPurchaseOrderEdit.uniqueId,
      };

      // console.log(body);

      ApiClient.POST(API.updatePurchaseOrder, body).then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Purchase order has been updated',
            [
              { text: "OK", onPress: () => { props.navigation.goBack() } }
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert(
            'Error',
            'Failed to update Purchase order',
          );
        }
      });
    }
  }

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    (<View style={[styles.container, !isTablet() ? {
      transform: [
        { scaleX: 1 },
        { scaleY: 1 },
      ],
    } : {}]}>
      {/* <View style={[styles.sidebar, !isTablet() ? {
        width: Dimensions.get('screen').width * 0.08,
      } : {}, switchMerchant ? {
        // width: '10%'
      } : {}]}>
        <SideBar navigation={props.navigation} selectedTab={3} expandInventory={true} />
      </View> */}
      <View style={styles.content}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: Platform.OS == 'ios' ? 0 : 10 }} >
          <View style={{ marginRight: 248, }}>
            <View style={{ flexDirection: 'row', flex: 1 }}>
              {/* <TouchableOpacity style={[styles.submitText, {
                height: Dimensions.get('screen').height * 0.05,
              }]} onPress={() => { importCSV() }}>
                <View style={{ flexDirection: 'row' }}>
                  <Icon name="download" size={20} color={Colors.primaryColor} />
                  <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                    Import
                  </Text>
                </View>
              </TouchableOpacity> */}
              <TouchableOpacity style={[styles.submitText, {
                height: Dimensions.get('screen').height * 0.05,
              }]} onPress={() => { exportFunc() }}>
                <View style={{ flexDirection: 'row' }}>
                  <Icon name="upload" size={20} color={Colors.primaryColor} />
                  <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                    Export
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          {/* <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="🔍  Search"
              onChangeText={(text) => {
                setState({ search: text.trim() });
              }}
              value={email}
            /> */}

          {/* <Ionicons
            name="search-outline"
            size={20}
            style={styles.searchIcon}
          />

          <TextInput
            editable={loading}
            clearButtonMode="while-editing"
            style={[styles.textInput, { fontFamily: "NunitoSans-Bold" }]}
            placeholder="Search"
            onChangeText={(text) => {
              setState({
                search: text.trim(),
              });
            }}
            value={email}
          /> */}

          <View
            style={[{
              // flex: 1,
              // alignContent: 'flex-end',
              // marginBottom: 10,
              // flexDirection: 'row',
              // marginRight: '-40%',
              // marginLeft: 310,
              // backgroundColor: 'red',
              // alignItems: 'flex-end',
              // right: '-50%',
              width: '50%',
              height: 40,

            }, !isTablet() ? {
              marginLeft: 0,
            } : {}]}>
            <View style={{
              width: 250,
              height: 40,
              backgroundColor: 'white',
              borderRadius: 10,
              // marginLeft: '53%',
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',

              marginRight: Dimensions.get('screen').width * Styles.sideBarWidth,

              position: 'absolute',
              right: '17%',

              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}>
              <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
              <TextInput
                editable={!loading}
                underlineColorAndroid={Colors.whiteColor}
                style={{
                  width: 250,
                  fontSize: 15,
                  fontFamily: 'NunitoSans-Regular',
                }}
                clearButtonMode="while-editing"
                placeholder=" Search"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                onChangeText={(text) => {
                  // setSearch(text.trim());
                  setSearch(text);
                  // setList1(false);
                  // setSearchList(true);
                }}
                value={search}
              />
            </View>
          </View>

        </View>

        {purchaseOrder ? (
          <View style={{ height: Dimensions.get('window').height }}>
            {/* <View> */}
            <ScrollView>
              <TouchableOpacity style={[styles.submitText1, {
                right: '0%',
              }]} onPress={() => {
                // setState({
                //   lowStockAlert: false,
                //   purchaseOrder: false,
                //   stockTransfer: false,
                //   stockTake: false,
                //   addPurchase: true,
                //   editPurchase: false,
                //   addStockTransfer: false,
                //   addStockTake: false,
                // });
                setPurchaseOrder(false);
                setAddPurchase(true);
              }}>
                <View style={{ flexDirection: 'row' }}>
                  <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                  <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                    New Purchase Order
                  </Text>
                </View>
              </TouchableOpacity>

              <View
                style={{
                  backgroundColor: '#ffffff',
                  flexDirection: 'row',
                  paddingVertical: 20,
                  paddingHorizontal: 20,
                  marginTop: 10,
                  borderBottomWidth: StyleSheet.hairlineWidth,
                }}>
                <Text style={{ width: '11%', alignSelf: 'center', color: '#969696' }}>
                  P.O.ID
                </Text>
                <Text style={{ width: '13%', alignSelf: 'center', color: '#969696' }}>
                  Date
                </Text>
                <Text style={{ width: '13%', alignSelf: 'center', marginRight: 20, color: '#969696' }}>
                  Estimated Date of Arrival
                </Text>
                <Text style={{ width: '15%', alignSelf: 'center', color: '#969696' }}>Target Store</Text>
                <Text style={{ width: '17%', alignSelf: 'center', color: '#969696' }}>Supplier </Text>
                <Text style={{ width: '15%', alignSelf: 'center', color: '#969696' }}>Amount (RM)</Text>
                <Text style={{ width: '15%', alignSelf: 'center', color: '#969696' }}>Status </Text>
              </View>
              <FlatList
                data={purchaseOrders.filter(item => {
                  if (search !== '') {
                    const searchLowerCase = search.toLowerCase();

                    return item.supplierName.toLowerCase().includes(searchLowerCase) || item.outletName.toLowerCase().includes(searchLowerCase);
                  }
                  else {
                    return true;
                  }
                })}
                extraData={purchaseOrders.filter(item => {
                  if (search !== '') {
                    const searchLowerCase = search.toLowerCase();

                    return item.supplierName.toLowerCase().includes(searchLowerCase) || item.outletName.toLowerCase().includes(searchLowerCase);
                  }
                  else {
                    return true;
                  }
                })}
                renderItem={renderOrderItem}
                keyExtractor={(item, index) => String(index)}
              />
              <View
                style={{
                  flexDirection: 'row',
                  backgroundColor: '#ffffff',
                  justifyContent: 'center',
                  padding: 18,
                }}>
                <View style={{ alignItems: 'center', }}>
                  <Text style={{ fontSize: 30, fontWeight: 'bold' }}>
                    {/* {orderList.length} */}
                    {purchaseOrders.length}
                  </Text>
                  <Text>PURCHASE ORDERS</Text>
                </View>
              </View>
            </ScrollView>
            {/* </View> */}
          </View>
        ) : null}

        {addPurchase ? (
          <View style={{ height: Dimensions.get('window').height - 200 }}>
            <View>
              <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide">
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: Dimensions.get('window').height,
                  }}>
                  <View style={styles.confirmBox}>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: false });
                      }}>
                      <View
                        style={{
                          alignSelf: 'flex-end',
                          padding: 16,
                        }}>
                        {/* <Close name="closecircle" size={25} /> */}
                        <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                      </View>
                    </TouchableOpacity>
                    <View>
                      <Text
                        style={{
                          textAlign: 'center',
                          fontWeight: '700',
                          fontSize: 28,
                        }}>
                        Purchase Order
                      </Text>
                    </View>
                    <View style={{ marginTop: 20 }}>
                      <Text
                        style={{
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 25,
                        }}>
                        Fill in the email information
                      </Text>
                    </View>
                    <View style={{ backgroundColor: 'white', alignSelf: 'center', flexDirection: "row" }}>
                      <Text style={{ fontSize: 20, marginTop: 70 }}>
                        Email:
                      </Text>
                      <View style={{ marginTop: 60, backgroundColor: '#f7f5f5', marginLeft: 10 }}>
                        <TextInput
                          editable={!loading}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          clearButtonMode="while-editing"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          style={styles.textCapacity}
                          placeholder="<EMAIL>"
                          onChangeText={(text) => {
                            setState({ Email: text });
                          }}
                          value={Email}
                        />
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '50%',
                        alignContent: 'center',
                        zIndex: 6000,
                      }}>

                    </View>
                    <View
                      style={{
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: 250,
                        height: 40,
                        alignContent: 'center',
                        flexDirection: "row",
                        marginTop: 40
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          email(),
                            setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '60%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          borderRadius: 10,
                          height: 60,
                        }}>
                        <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                          Send
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          setState({ visible: false });
                        }}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '60%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          borderRadius: 10,
                          height: 60,
                          marginLeft: 30
                        }}>
                        <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                          No
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>

              <TouchableOpacity style={{ marginBottom: 20, flexDirection: 'row', alignContent: 'center', alignItems: 'center', marginTop: 20 }}
                onPress={() => {
                  // setState({
                  //   lowStockAlert: false,
                  //   purchaseOrder: true,
                  //   stockTransfer: false,
                  //   stockTake: false,
                  //   addPurchase: false,
                  //   editPurchase: false,
                  //   addStockTransfer: false,
                  //   addStockTake: false,
                  // })
                  setPurchaseOrder(true);
                  setAddPurchase(false);
                }}>
                <Icon name="chevron-left" size={30} color={Colors.primaryColor} />
                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 17, color: Colors.primaryColor }}> Back </Text>
              </TouchableOpacity>

              <ScrollView>
                <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end" }}>
                  <DropDownPicker
                    items={[
                      {
                        label: '🖨️  Print P.O',
                        value: 'Print P.O',
                      },
                      {
                        label: '📧  Email P.O',
                        value: 'Chicken',
                      },
                      {
                        label: '📤  Export Labels',
                        value: 'Export Labels',
                      },
                      {
                        label: '❌  Cancel P.O',
                        value: 'Cancel P.O',
                      },
                      {
                        label: '🗑️  Delete P.O',
                        value: 'Delete P.O',
                      },
                    ]}
                    defaultValue={choice2}
                    placeholder=""
                    containerStyle={{ height: 30 }}
                    style={{ backgroundColor: '#FAFAFA' }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                    onChangeItem={(item) =>
                      setState({
                        choice2: item.value,
                      })
                    }
                  />
                </View>
                <View style={{ borderBottomWidth: StyleSheet.hairlineWidth }}>
                  <View>
                    <Text style={{ alignSelf: "center", marginTop: 30, fontSize: 40, fontWeight: 'bold' }}>Create Purchase Order</Text>
                    <Text style={{ alignSelf: "center", fontSize: 16, color: '#adadad' }}>Fill in the purchase order information</Text>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5 }}>
                    <View style={{
                      flexDirection: "row",
                      flex: 1,
                      alignItems: 'center',
                      // justifyContent: 'space-around',
                      width: '50%',
                    }}>
                      <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>P.O.ID</Text>

                      {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}
                      <TextInput
                        editable={false}
                        underlineColorAndroid={Colors.fieldtBgColor}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 100,
                          height: 40,
                          width: '40%',
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                        }}
                        // placeholder={'50'}
                        placeholder={poId}
                        keyboardType={'default'}
                      />
                    </View>
                    <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                      <Text style={{
                        fontSize: 16,
                        // marginLeft: 60,                        
                        width: '40%',
                        textAlign: 'left'
                      }}>Supplier</Text>

                      <View style={{ width: '40%', }}>
                        <DropDownPicker
                          // items={[
                          //   {
                          //     label: 'My Burger Enterprise',
                          //     value: '1',
                          //   },
                          //   {
                          //     label: 'Supplier B',
                          //     value: '2',
                          //   },
                          //   {
                          //     label: 'Supplier c',
                          //     value: '3',
                          //   },

                          // ]}
                          items={supplierDropdownList}
                          defaultValue={selectedSupplierId}
                          placeholder="Supplier"
                          containerStyle={{ height: 30 }}
                          style={{ backgroundColor: '#FAFAFA' }}
                          itemStyle={{
                            justifyContent: 'flex-start',
                          }}
                          dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                          onChangeItem={(item) =>
                            // setState({
                            //   choice7: item.value,
                            // })
                            setSelectedSupplierId(item.value)
                          }
                        />
                      </View>
                    </View>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5 }}>
                    <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                      <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>Current status</Text>
                      <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: '#838387', borderRadius: 10, width: '40%' }}>
                        <Text style={{ color: Colors.whiteColor }}>{PURCHASE_ORDER_STATUS_PARSED[poStatus]}</Text>
                      </View>
                    </View>

                    <View style={{ flexDirection: "row", flex: 1, width: '50%', alignItems: 'center' }}>
                      <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>Target Store</Text>
                      <View style={{ width: '40%', }}>
                        <DropDownPicker
                          // items={[
                          //   {
                          //     label: 'MyBurgerlab （Seapark)',
                          //     value: '1',
                          //   },
                          //   {
                          //     label: 'Store B',
                          //     value: '2',
                          //   },
                          //   {
                          //     label: 'Store c',
                          //     value: '3',
                          //   },
                          // ]}
                          items={targetOutletDropdownList}
                          defaultValue={selectedTargetOutletId}
                          placeholder=""
                          containerStyle={{ height: 30 }}
                          style={{ backgroundColor: '#FAFAFA' }}
                          itemStyle={{
                            justifyContent: 'flex-start',
                          }}
                          dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                          onChangeItem={(item) =>
                            // setState({
                            //   choice8: item.value,
                            // })
                            setSelectedTargetOutletId(item.value)
                          }
                        />
                      </View>
                    </View>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: "space-evenly", marginTop: 50, marginBottom: 40, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5 }}>
                    <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                      <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>Estimated Arrival Time</Text>
                      <View style={{
                        width: 140,
                        backgroundColor: '#f2f2f2',
                        padding: 10,
                        paddingHorizontal: 0,
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '40%',
                      }}>
                        <Text style={{
                          textAlign: 'left',
                        }}>{moment(date).format('DD/MM/YYYY')} <Text>        </Text>
                          <TouchableOpacity style={{ alignSelf: "flex-end", }} onPress={showDateTimePicker}>
                            {/* <TouchableOpacity style={{ alignSelf: "flex-end" }} onPress={showDateTimePicker}> */}
                            <GCalendar width={22} height={22} />
                          </TouchableOpacity>
                          <DateTimePicker
                            isVisible={isDateTimePickerVisible}
                            onConfirm={handleDatePicked}
                            onCancel={hideDateTimePicker}
                          /></Text>
                      </View>
                    </View>

                    <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                      <Text style={{
                        fontSize: 16,
                        width: '40%',
                        textAlign: 'left'
                      }}>Order Date</Text>
                      {/* <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>{moment().format('DD/MM/YYYY')}</Text> */}
                      <TextInput
                        editable={false}
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '40%',
                          height: 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                        }}
                        // placeholder={'50'}
                        placeholder={moment().format('DD/MM/YYYY')}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        keyboardType={'default'}
                      />
                    </View>
                  </View>
                </View>

                <View>
                  <Text style={{ alignSelf: "center", marginTop: 30, fontSize: 25, fontWeight: 'bold' }}>Items to order</Text>
                </View>

                <View
                  style={{
                    backgroundColor: '#ffffff',
                    flexDirection: 'row',
                    paddingVertical: 20,
                    paddingHorizontal: 20,
                    marginTop: 10,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                  }}>
                  <Text style={{ width: '14%', alignSelf: 'center' }}>
                    Product name
                  </Text>
                  <Text style={{ width: '2%' }}></Text>
                  <Text style={{ width: '14%', alignSelf: 'center' }}>
                    SKU
                  </Text>

                  <Text style={{ width: '8%', alignSelf: 'center' }}>
                    On hand
                  </Text>
                  <Text style={{ width: '14%', alignSelf: 'center' }}>
                    Ordered qty
                  </Text>
                  <Text style={{ width: '16%', alignSelf: 'center' }}>Received qty</Text>
                  <Text style={{ width: '20%', alignSelf: 'center' }}>Supplier Price </Text>
                  <Text style={{ width: '16%', alignSelf: 'center' }}>Total (RM)</Text>
                </View>

                {console.log('poItems')}
                {console.log(poItems)}
                {console.log('supplyItems')}
                {console.log(supplyItems)}
                {console.log('supplyItemDropdownList')}
                {console.log(supplyItemDropdownList)}

                {((selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.supplierId === selectedSupplierId && supplyItemsDict[poItems[0].supplyItemId]) || selectedPurchaseOrderEdit === null) &&
                  supplyItemDropdownList.length > 0 &&
                  Object.keys(supplyItemsDict).length > 0 &&
                  Object.keys(outletSupplyItemsSkuDict).length > 0 &&
                  <FlatList
                    data={poItems}
                    extraData={poItems}
                    renderItem={renderAddPO}
                    keyExtractor={(item, index) => String(index)}
                  />
                }

                {/* <ScrollView>
                  {
                    // itemsToOrder2 && itemsToOrder2.map((item, i) => {
                    //   return renderAddPO(item);
                    // })
                    poItems && poItems.map((item, i) => {
                      return renderAddPO(item, i);
                    })
                  }
                </ScrollView> */}

                <View style={{ flexDirection: 'row' }}>
                  <View >
                    <TouchableOpacity style={styles.submitText2} onPress={() => {
                    }}>
                      <View style={{ flexDirection: 'row' }}>
                        <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                        <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                          Add product slot
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                  <View style={{ marginLeft: 350, marginTop: 10 }}>
                    <View style={{ flexDirection: "row" }}>
                      <Text style={{ color: '#adadad' }}>Subtotal</Text>
                      <Text style={{ color: '#adadad', marginLeft: 50 }}>{`RM${subtotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 10 }}>
                      <Text style={{ color: '#adadad' }}>Tax</Text>
                      <Text style={{ color: '#adadad', marginLeft: 80 }}>{`RM${taxTotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 10 }}>
                      <Text style={{ color: '#adadad' }}>Discount</Text>
                      <Text style={{ color: '#adadad', marginLeft: 50 }}>{`RM${discountTotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 10 }}>
                      <Text style={{ fontWeight: 'bold' }}>Total (RM)</Text>
                      <Text style={{ fontWeight: 'bold', marginLeft: 40 }}>{`RM${finalTotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
                    </View>
                  </View>

                </View>

                <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 200,
                      height: 40,
                      marginVertical: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity onPress={createPurchaseOrder}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                        }}>
                        SAVE
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 200,
                      height: 40,
                      marginVertical: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                      marginLeft: 40
                    }}>
                    <TouchableOpacity onPress={() => { setVisible(true) }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                        }}>
                        SAVE & SEND
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>


                <View
                  style={{
                    flexDirection: 'row',
                    backgroundColor: '#ffffff',
                    justifyContent: 'center',
                    padding: 18,
                  }}>
                  <View style={{ alignItems: 'center' }}>
                    <Text style={{ fontSize: 30, fontWeight: 'bold' }}>
                      {/* {stockList.length} */}
                      {purchaseOrders.length}
                    </Text>
                    <Text>PURCHASE ORDERS</Text>
                  </View>
                </View>
              </ScrollView>
            </View>


          </View>
        ) : null}

        {editPurchase ? (
          <View style={{ height: Dimensions.get('window').height - 200 }}>

            <View>
              <ScrollView>
                <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end" }}>
                  <DropDownPicker
                    items={[
                      {
                        icon: () => { },
                        label: '🖨️  Print P.O',
                        value: 'Print P.O',
                      },
                      {
                        label: '📧  Email P.O',
                        value: 'Chicken',
                      },
                      {
                        label: '📤  Export Labels',
                        value: 'Export Labels',
                      },
                      {
                        label: '❌  Cancel P.O',
                        value: 'Cancel P.O',
                      },
                      {
                        label: '🗑️  Delete P.O',
                        value: 'Delete P.O',
                      },
                    ]}
                    defaultValue={choice}
                    placeholder=""
                    placeholderStyle={{ color: 'black' }}
                    containerStyle={{ height: 30 }}
                    style={{ backgroundColor: '#FAFAFA' }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                    onChangeItem={(item) =>
                      setState({
                        choice: item.value,
                      })
                    }
                  />
                </View>
                <View style={{ borderBottomWidth: StyleSheet.hairlineWidth }}>
                  <View>
                    <Text style={{ alignSelf: "center", marginTop: 30, fontSize: 40, fontWeight: 'bold' }}>Edit Purchase Order</Text>
                    <Text style={{ alignSelf: "center", fontSize: 16, color: '#adadad' }}>Edit your purchase order information</Text>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50 }}>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>P.O.ID</Text>
                      <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text>
                    </View>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>Supplier</Text>
                      <Text style={{ color: '#adadad', marginLeft: 100, fontSize: 16, }}>My Burgers Enterprise</Text>
                    </View>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50 }}>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>Current status</Text>
                      <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: '#838387', borderRadius: 10, marginLeft: 100 }}>
                        <Text style={{ color: Colors.whiteColor }}>Partially received</Text>
                      </View>

                    </View>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>Target Store</Text>
                      <Text style={{ color: '#adadad', marginLeft: 70, fontSize: 16, }}>MyBurgerlab (Seapark)</Text>
                    </View>
                  </View>

                  <View style={{ flexDirection: "row", marginTop: 10, justifyContent: "space-evenly", marginTop: 50, marginBottom: 40 }}>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>Estimated Arrival Time</Text>
                      <Text style={{ color: '#adadad', marginLeft: 50, fontSize: 16, }}>1/10/2020</Text>
                    </View>
                    <View style={{ flexDirection: "row", flex: 1 }}>
                      <Text style={{ fontSize: 16, marginLeft: 80 }}>Order Date</Text>
                      <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>5/10/2020</Text>
                    </View>
                  </View>
                </View>

                <View>
                  <Text style={{ alignSelf: "center", marginTop: 30, fontSize: 25, fontWeight: 'bold' }}>Items to order</Text>
                </View>

                <View
                  style={{
                    backgroundColor: '#ffffff',
                    flexDirection: 'row',
                    paddingVertical: 20,
                    paddingHorizontal: 20,
                    marginTop: 10,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                  }}>
                  <Text style={{ width: '8%' }}></Text>
                  <Text style={{ width: '14%', alignSelf: 'center' }}>
                    Product name
                  </Text>
                  <Text style={{ width: '16%', alignSelf: 'center' }}>
                    SKU
                  </Text>
                  <Text style={{ width: '14%', alignSelf: 'center' }}>
                    Ordered qty
                  </Text>
                  <Text style={{ width: '16%', alignSelf: 'center' }}>Received qty</Text>
                  <Text style={{ width: '18%', alignSelf: 'center' }}>Supplier Price </Text>
                  <Text style={{ width: '16%', alignSelf: 'center' }}>Total (RM)</Text>
                </View>
                <FlatList
                  data={itemsToOrder}
                  extraData={itemsToOrder}
                  renderItem={renderItemsToOrder}
                  keyExtractor={(item, index) => String(index)}
                />

                <View style={{ flexDirection: 'row' }}>
                  <View >
                    <TouchableOpacity style={styles.submitText2} onPress={() => {
                    }}>
                      <View style={{ flexDirection: 'row' }}>
                        <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                        <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                          Add product slot
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                  <View style={{ marginLeft: 480, marginTop: 10 }}>
                    <View style={{ flexDirection: "row" }}>
                      <Text style={{ color: '#adadad' }}>Subtotal</Text>
                      <Text style={{ color: '#adadad', marginLeft: 50 }}>RM360.00</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 10 }}>
                      <Text style={{ color: '#adadad' }}>Tax</Text>
                      <Text style={{ color: '#adadad', marginLeft: 80 }}>RM0.00</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 10 }}>
                      <Text style={{ color: '#adadad' }}>Discount</Text>
                      <Text style={{ color: '#adadad', marginLeft: 50 }}>RM0.00</Text>
                    </View>
                    <View style={{ flexDirection: "row", marginTop: 5 }}>
                      <Text style={{ fontWeight: 'bold' }}>Total (RM)</Text>
                      <Text style={{ fontWeight: 'bold', marginLeft: 40 }}>RM360.00</Text>
                    </View>
                  </View>

                </View>

                <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 200,
                      height: 40,
                      marginVertical: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                    }}>
                    <TouchableOpacity onPress={() => { editStockOrder() }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                        }}>
                        SAVE
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: 200,
                      height: 40,
                      marginVertical: 15,
                      borderRadius: 5,
                      alignSelf: 'center',
                      marginLeft: 40
                    }}>
                    <TouchableOpacity onPress={() => { }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          alignSelf: 'center',
                          marginVertical: 10,
                        }}>
                        SAVE & SEND
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>


                <View
                  style={{
                    flexDirection: 'row',
                    backgroundColor: '#ffffff',
                    justifyContent: 'center',
                    padding: 18,
                  }}>
                  <View style={{ alignItems: 'center' }}>
                    <Text style={{ fontSize: 30, fontWeight: 'bold' }}>
                      {/* {stockList.length} */}
                      {purchaseOrders.length}
                    </Text>
                    <Text>PURCHASE ORDERS</Text>
                  </View>
                </View>
              </ScrollView>
            </View>

          </View>
        ) : null}
      </View>
    </View>)
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row'
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center'
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
});
export default StockInsertScreen;
