
import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Text,
    TouchableOpacity,
    Dimensions,
    Alert,
    Button,
    Modal as ModalComponent,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    KeyboardAvoidingViewBase,
    useWindowDimensions,
    TouchableWithoutFeedback,
    InteractionManager,
    Keyboard,
    AppState,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AIcon from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import Icon from 'react-native-vector-icons/Ionicons';
import Colors from '../../constant/Colors';
import { PRIVILEGES_NAME, ROLE_TYPE } from '../../constant/common';
import Styles from '../../constant/Styles';
import { CommonStore } from '../../store/commonStore';
import { MerchantStore } from '../../store/merchantStore';
import { OutletStore } from '../../store/outletStore';
import { TableStore } from '../../store/tableStore';
import { getCachedUrlContent, getImageFromFirebaseStorage, getTransformForModalInsideNavigation, isTablet } from '../../util/common';
import { openCashDrawer } from '../../util/printer';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;


const PinUnlockModal = props => {
    const {
    } = props;

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const showPinUnlockModal = CommonStore.useState(s => s.showPinUnlockModal);
    const pinUnlockType = CommonStore.useState(s => s.pinUnlockType);

    const isAuthenticating = CommonStore.useState(s => s.isAuthenticating);

    const allOutletsEmployees = OutletStore.useState(
        (s) => s.allOutletsEmployees,
    );

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const [pinNo, setPinNo] = useState('');

    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    const checkForPinAccess = async () => {
        CommonStore.update(s => {
            s.isAuthenticating = true;
        });

        console.log("🔍 Entered PIN:", pinNo);
        console.log("🔍 Unlock Type:", pinUnlockType);
        console.log("🔍 Current Outlet ID:", currOutletId);
        console.log("🔍 All Employees Data:", allOutletsEmployees);

        const isPlacingOrder = pinUnlockType === PRIVILEGES_NAME.ORDER;

        const employeeMatched = allOutletsEmployees.find(employee => {
            console.log("👤 Checking Employee:", employee.pinNo, "Outlet ID:", employee.outletId, "Role:", employee.role);

            if (isPlacingOrder) {
                // ✅ Allow ALL employees to place an order with their PIN (no role or privilege restriction)
                return employee.pinNo === pinNo;
            }

            // ❌ Restrict all other actions (Refund, Cancel, Reject) to Admins or users with specific privileges
            return employee.pinNo === pinNo && (
                (employee.outletId === currOutletId &&
                    employee.privileges && employee.privileges.includes(pinUnlockType)
                ) ||
                employee.role === ROLE_TYPE.ADMIN
            );
        });

        if (employeeMatched) {
            console.log("✅ PIN Matched: Proceeding with action");

            global.currPinId = employeeMatched.firebaseUid || '';
            global.currPinName = employeeMatched.name || '';

            CommonStore.update(s => {
                s.pinUnlockType = '';
                s.showPinUnlockModal = false;
            });

            setTimeout(async () => {
                if (typeof global.pinUnlockCallback === 'function') {
                    await global.pinUnlockCallback();
                }
            }, (currOutlet && currOutlet.puct) ? currOutlet.puct : 500);

        } else {
            console.log("❌ No Matching PIN Found for action:", pinUnlockType);
            Alert.alert('Info', 'Incorrect PIN. Please try again.');
            setPinNo('');
        }

        CommonStore.update(s => {
            s.isAuthenticating = false;
        });
    };


    const onButtonPress = (key) => {
        if (key >= 0) {
            if (pinNo.length < 4) {
                var currPinNo = pinNo + key;

                setPinNo(pinNo + key);

                // await global.watermelonDBDatabase.localStorage.set('enteredPinNo', currPinNo);
            }
        } else {
            if (pinNo.length > 0)
                var currPinNo = pinNo.slice(0, key);

            setPinNo(pinNo.slice(0, key));

            // await global.watermelonDBDatabase.localStorage.set('enteredPinNo', currPinNo);
        }

    };

    return (
        <>
            {
                // showPinUnlockModal
                true
                    ?
                    <ModalView
                        supportedOrientations={['landscape', 'portrait']}
                        style={{ flex: 1 }}
                        visible={showPinUnlockModal}
                        // style={{ flex: 1, display: seatingModal ? 'flex' : 'none' }}
                        // visible={true}
                        transparent>
                        <View style={styles.modalContainer}>
                            <View
                                style={[
                                    {
                                        width: windowWidth * 0.55,
                                        height: windowHeight * 0.8,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 12,
                                        padding: windowWidth * 0.04,
                                        alignItems: 'center',
                                        justifyContent: 'space-between',

                                        // backgroundColor: 'red',

                                        ...getTransformForModalInsideNavigation(),
                                    },
                                    switchMerchant
                                        ? {
                                            height: windowHeight * 0.85,
                                        }
                                        : {},
                                ]}>
                                <TouchableOpacity
                                    disabled={isAuthenticating}
                                    style={[
                                        styles.closeButton,
                                        switchMerchant
                                            ? {
                                                position: 'absolute',
                                                top: windowWidth * 0.02,
                                                right: windowWidth * 0.02,
                                            }
                                            : {},
                                    ]}
                                    onPress={() => {
                                        // setSeatingModal(false);

                                        requestAnimationFrame(() => {
                                            CommonStore.update(s => {
                                                s.showPinUnlockModal = false;

                                                s.isModalOpened = false;

                                                s.isLoading = false;
                                            });

                                            if (pinUnlockType === PRIVILEGES_NAME.REJECT_ITEM) {
                                                setTimeout(async () => {
                                                    global.viewTableOrderModalPrev = true;

                                                    global.pinUnlockModalShowing = false;

                                                    TableStore.update(s => {
                                                        s.viewTableOrderModal = true;
                                                    });
                                                }, (currOutlet && currOutlet.puct) ? currOutlet.puct : 500);
                                            }
                                        });
                                    }}>
                                    {switchMerchant ? (
                                        <AIcon
                                            name="closecircle"
                                            size={20}
                                            color={Colors.fieldtTxtColor}
                                        />
                                    ) : (
                                        <AIcon
                                            name="closecircle"
                                            size={40}
                                            color={Colors.fieldtTxtColor}
                                        />
                                    )}
                                </TouchableOpacity>

                                <View style={[styles.modalTitle, {
                                    // backgroundColor: 'red',
                                }]}>
                                    <Text
                                        style={[
                                            styles.modalTitleText,
                                            switchMerchant
                                                ? {
                                                    fontSize: 16,
                                                    top: windowWidth * -0.03,
                                                }
                                                : {},
                                            {
                                                marginBottom: 10,
                                            }
                                        ]}>
                                        Pin Access
                                    </Text>
                                </View>

                                <View
                                    style={[
                                        styles.modalBody,
                                        { width: '100%', alignItems: 'center' },
                                        switchMerchant
                                            ? {
                                                top: windowHeight * -0.06,
                                                right: windowWidth * 0.016,
                                            }
                                            : {},
                                    ]}>
                                    <View style={{
                                        alignItems: 'center',
                                        width: '100%',

                                        // backgroundColor: 'red'
                                    }}>
                                        <View style={[{ width: '65%' }, !isTablet() ? {
                                            // flex: 5, // remove flex
                                        } : {}]}>
                                            {/* <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 40 : 40, fontWeight: 'bold' }, !isTablet() ? {
                                                fontSize: Dimensions.get('screen').width / 50,
                                            } : {}]}>Login</Text> */}

                                            {/* {isTablet() ? <Text style={[{ fontSize: 16, paddingTop: 10, color: Colors.fieldtTxtColor }, !isTablet() ? {
                                                fontSize: Dimensions.get('screen').width / 70,
                                                paddingTop: 5,
                                            } : {}]}>Enter the PIN code to continue</Text> : <></>} */}

                                            <View style={[styles.pinContainer, !isTablet() ? {
                                                // paddingTop: Dimensions.get('screen').width / 50,
                                            } : {}]}>
                                                {/* <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{pinNo.length > 0 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{pinNo.length > 1 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{pinNo.length > 2 ? "•" : null}</Text></View>
                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}><Text style={[{ fontSize: 40, bottom: Platform.OS === 'ios' ? 5 : 0, }, !isTablet() ? { fontSize: 20, bottom: Platform.OS === 'ios' ? 5 : 5, } : {}]}>{pinNo.length > 3 ? "•" : null}</Text></View> */}
                                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{pinNo.length > 0 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{pinNo.length > 1 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{pinNo.length > 2 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                                <View style={[styles.pinBox, { width: Dimensions.get('window').width * 0.045, height: Dimensions.get('window').width * 0.045 }]}>{pinNo.length > 3 ? <Entypo name='dot-single' size={35} /> : null}</View>
                                            </View>
                                        </View>
                                        <View style={[{ width: '40%' }, !isTablet() ? {
                                            width: '50%',
                                            // flex: 12, // remove flex
                                            // marginTop: 20,
                                        } : {
                                            marginTop: 20,
                                        }]}>
                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                                !isTablet() ? { height: '30%' } : {}]}>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin1'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(1);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, {}, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>1</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin2'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(2);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>2</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin3'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(3);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>3</Text></View>
                                                    </TouchableOpacity>
                                                </View>
                                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                                !isTablet() ? { height: '30%' } : {}]}>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin4'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(4);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>4</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin5'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(5);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>5</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin6'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(6);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>6</Text></View>
                                                    </TouchableOpacity>
                                                </View>
                                                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                                !isTablet() ? { height: '30%' } : {}]}>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin7'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(7);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>7</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin8'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(8);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>8</Text></View>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPin9'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                onButtonPress(9);
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>9</Text></View>
                                                    </TouchableOpacity>
                                                </View>
                                                <View style={{ flexDirection: 'row', width: '100%' }}>
                                                    <View style={[{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
                                                    !isTablet() ? { height: '30%' } : {}]}>
                                                        <TouchableOpacity
                                                            testID='PinLogin.buttonPinClockIn'
                                                            // disabled={isAuthenticating}
                                                            disabled
                                                            onPress={() => {
                                                                requestAnimationFrame(() => {
                                                                    if (pinNo.length === 4) {
                                                                        // this._clockInUser();
                                                                    }
                                                                    else {
                                                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                                                    }
                                                                });
                                                            }}
                                                            style={{
                                                                opacity: 0,
                                                            }}
                                                        >
                                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}
                                                            ><Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                                fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                            } : {}]}>Clock</Text>
                                                                <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                                    fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                                } : {}]}>in</Text>
                                                            </View>
                                                        </TouchableOpacity>
                                                        <TouchableOpacity
                                                            testID='PinLogin.buttonPin0'
                                                            onPress={() => {
                                                                requestAnimationFrame(() => {
                                                                    onButtonPress(0);
                                                                });
                                                            }}>
                                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}><Text style={[styles.pinNo, { fontSize: 0.35 * (Dimensions.get('window').width * 0.04) }, !isTablet() ? { fontSize: Dimensions.get('screen').width / 70 } : {}]}>0</Text></View>
                                                        </TouchableOpacity>
                                                        <TouchableOpacity
                                                            testID='PinLogin.buttonPinClockOut'
                                                            // disabled={isAuthenticating}
                                                            disabled
                                                            onPress={() => {
                                                                requestAnimationFrame(() => {
                                                                    if (pinNo.length === 4) {
                                                                        // this._clockOutUser();
                                                                    }
                                                                    else {
                                                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                                                    }
                                                                });

                                                                // this.setState({ clockoutModal: true })
                                                            }}
                                                            style={{
                                                                opacity: 0,
                                                            }}
                                                        >
                                                            <View style={[styles.pinBtn, { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 }]}
                                                            ><Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                                fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                            } : {}]}>Clock</Text>
                                                                <Text style={[{ fontSize: isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? Dimensions.get('screen').width / 100 : 0.25 * (Dimensions.get('window').width * 0.05), fontWeight: 'bold' }, !isTablet() ? {
                                                                    fontSize: 0.25 * (Dimensions.get('window').width * 0.04),
                                                                } : {}]}>out</Text>
                                                            </View>
                                                        </TouchableOpacity>
                                                    </View>
                                                    <TouchableOpacity
                                                        testID='PinLogin.buttonPinDelete'
                                                        onPress={() => {
                                                            requestAnimationFrame(() => {
                                                                if (pinNo.length > 0) { onButtonPress(-1) }
                                                            });
                                                        }}>
                                                        <View style={[styles.pinBtn,
                                                        { width: Dimensions.get('window').width * 0.05, height: Dimensions.get('window').width * 0.05 },
                                                        { backgroundColor: 'white', marginLeft: Dimensions.get('window').width * 0.025 }]}><Icon name="backspace-outline" size={!isTablet() ? (0.525 * (Dimensions.get('window').width * 0.04)) : isTablet() && Dimensions.get('screen').width < 1350 && Dimensions.get('screen').height < 670 ? 0.525 * (Dimensions.get('window').width * 0.04) : (0.525 * (Dimensions.get('window').width * 0.05))} /></View>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        </View>

                                        <TouchableOpacity
                                            testID='PinLogin.buttonLogin'
                                            disabled={isAuthenticating}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',

                                                marginTop: '0%',
                                                marginBottom: '2%',

                                                width: '40%',
                                            }}
                                            onPress={async () => {
                                                requestAnimationFrame(async () => {
                                                    if (pinNo.length === 4) {
                                                        checkForPinAccess();

                                                        // this._pinLogin();

                                                        // await connectToPrinter('192.168.1.2'); // try to let the bluetooth popup appeared
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Please type the pin number before proceed.')
                                                    }
                                                });
                                            }}>
                                            <View style={[Styles.button, {
                                                marginTop: 0,
                                                marginVertical: 0,
                                                paddingVertical: 10,

                                                width: '100%',
                                            }, !isTablet() ? {
                                                paddingVertical: 5,
                                            } : {}]}>
                                                <Text style={[{ color: '#ffffff', fontSize: 18 }, !isTablet() ? {
                                                    fontSize: Dimensions.get('screen').width / 50,
                                                } : {}]}>
                                                    {isAuthenticating ? 'LOADING...' : 'PROCEED'}
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </ModalView>
                    :
                    <></>
            }
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'row',
    },
    content: {
        width: Dimensions.get('window').width * 0.83,
        padding: 16,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    sidebar: {
        //width: windowWidth * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    floorButton: {
        alignItems: 'center',
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        width: 130,
        borderRadius: 5,
        height: 35,
        marginLeft: 5,
        justifyContent: 'center',
        shadowOffset:
            Platform.OS == 'ios'
                ? {
                    width: 0,
                    height: 3,
                }
                : {
                    width: 0,
                    height: 7,
                },
        shadowOpacity: Platform.OS == 'ios' ? 0.3 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 5 : 9.51,

        elevation: 10,
    },
    addFloorBtn: {
        borderWidth: 1,
        borderColor: Colors.whiteColor,
        backgroundColor: Colors.whiteColor,
        width: 40,
        borderRadius: 5,
        height: 35,
        marginLeft: 5,
        justifyContent: 'center',
        alignItems: 'center',
        shadowOffset:
            Platform.OS == 'ios'
                ? {
                    width: 0,
                    height: 3,
                }
                : {
                    width: 0,
                    height: 7,
                },
        shadowOpacity: Platform.OS == 'ios' ? 0.3 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 5 : 9.51,

        elevation: 10,
    },
    buttonText: {
        fontSize: 14,
        textAlign: 'center',
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
    },
    modal: {
        width: Dimensions.get('window').width * 0.3,
        height: Dimensions.get('window').height * 0.4,
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
        padding: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalOrder: {
        width: Dimensions.get('window').width * 0.6,
        height: Dimensions.get('window').height * 0.9,
        borderRadius: 35,
        backgroundColor: Colors.whiteColor,
    },
    sectionModal: {
        width: 400,
        height: 380,
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    tableIcon: {
        width:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.15
                : Dimensions.get('window').height * 0.165,
        height:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.15
                : Dimensions.get('window').height * 0.165,
        backgroundColor: Colors.fieldtBgColor,
        margin: Platform.OS == 'ios' ? 3 : 10,
        marginTop: Platform.OS == 'ios' ? 10 : 0,
        borderRadius: 20,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: Colors.fieldtTxtColor,
    },
    seatedTableIcon: {
        width:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.15
                : Dimensions.get('window').height * 0.165,
        height:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.15
                : Dimensions.get('window').height * 0.165,
        backgroundColor: Colors.fieldtBgColor,
        margin: Platform.OS == 'ios' ? 3 : 10,
        marginTop: Platform.OS == 'ios' ? 10 : 0,
        borderRadius: 20,
        padding: 10,
        justifyContent: 'space-between',
    },
    textInput: {
        width: 100,
        height: 40,
        backgroundColor: Colors.whiteColor,
        marginRight: 0,

        // shadowOpacity: 0,
        // shadowColor: '#000',
        // shadowOffset:
        //   Platform.OS == 'ios'
        //     ? {
        //       width: 0,
        //       height: 0,
        //     }
        //     : {
        //       width: 0,
        //       height: 2,
        //     },
        // shadowOpacity: Platform.OS == 'ios' ? 0 : 0.22,
        // shadowRadius: Platform.OS == 'ios' ? 0 : 3.22,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    searchIcon: {
        backgroundColor: Colors.whiteColor,
        height: 40,
        padding: 10,

        shadowOffset:
            Platform.OS == 'ios'
                ? {
                    width: 0,
                    height: 0,
                }
                : {
                    width: 0,
                    height: 7,
                },
        shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

        elevation: 15,
    },
    plusIcon: {
        justifyContent: 'center',
        fontWeight: 'bold',
    },
    textTable: {
        height: 50,
        width: 150,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        textAlign: 'center',
        fontSize: 18,
        marginLeft: 30,
    },
    textCap: {
        height: 50,
        width: 150,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        textAlign: 'center',
        fontSize: 18,
        marginLeft: 25,
    },
    textCode: {
        fontFamily: 'NunitoSans-Bold',
        height: 40,
        width: 150,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        textAlign: 'center',
        fontSize: 15,
    },
    searchView: {
        justifyContent: 'center',
        alignItems: 'center',
        // padding: 10,
        flexDirection: 'row',
        // marginLeft: 100
        width: '100%',
    },
    searchBar: {
        marginHorizontal: 16,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        padding: 12,
        borderRadius: 10,
        alignContent: 'center',
    },
    searchIcon: {
        backgroundColor: Colors.whiteColor,
        height: 40,
        padding: 10,
    },
    tableNoText: {
        position: 'absolute',
        top: 10,
        right: 10,
        fontSize: 25,
        fontWeight: '400',
    },
    tableTimeText: {
        position: 'absolute',
        top: 10,
        left: 10,
        fontSize: 15,
    },
    tableSeatText: {
        fontSize: 15,
        fontWeight: 'bold',
        color: Colors.descriptionColor,
        position: 'absolute',
        bottom: 10,
        right: 10,
    },
    pinBtn: {
        backgroundColor: Colors.lightPrimary,
        // width: 115,
        // height: 60,
        width: Dimensions.get('window').width * 0.05,
        height: Dimensions.get('window').width * 0.05,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        borderRadius: 10,
    },
    pinBtn2: {
        backgroundColor: Colors.whiteColor,
        // width: 115,
        // height: 60,
        width: Dimensions.get('window').width * 0.05,
        height: Dimensions.get('window').width * 0.05,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        borderRadius: 10,
    },
    pinNo: {
        // fontSize: 20,
        fontSize: 0.28 * (Dimensions.get('window').width * 0.05),
        fontFamily: 'NunitoSans-Bold',
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.4,
        width: Dimensions.get('window').width * 0.4,
        backgroundColor: Colors.whiteColor,
        //borderRadius: windowWidth * 0.03,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        paddingHorizontal: Dimensions.get('window').width * 0.015,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
    },
    modalBody: {
        // flex: 1.1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 33,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: '20%',
    },
    modalSaveButton: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 55,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },
    modalSaveButton2: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.primaryColor,
        height: 55,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },

    // ///////////////////////////////////////

    container: {
        flex: 1,
        backgroundColor: Colors.lightPrimary,
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
        marginTop: 10,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
        fontFamily: 'NunitoSans-Regular',
    },
    sectionAreaButton: {
        marginLeft: 10,
        // width: windowWidth * 0.085,
        backgroundColor: Colors.whiteColor,
        height: Dimensions.get('window').height * 0.04,
        borderRadius: 8,
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexDirection: 'row',

        paddingLeft: 15,

        paddingRight: 35,

        elevation: 0,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    sectionAreaButtonTxt: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 14,
        color: Colors.primaryColor,
        textAlign: 'center',
    },
    sectionAreaFlatList: {
        height: '100%',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    // modalView: {
    //   height: windowWidth * 1,
    //   width: windowWidth * 0.8,
    //   backgroundColor: Colors.whiteColor,
    //   borderRadius: windowWidth * 0.07,
    //   padding: windowWidth * 0.07,
    //   alignItems: 'center',
    //   justifyContent: 'space-between'
    // },
    // closeButton: {
    //   position: 'absolute',
    //   right: windowWidth * 0.04,
    //   top: windowWidth * 0.04
    // },
    modalTitle: {
        alignItems: 'center',
    },
    modalBody: {
        // flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        // marginBottom: 10,
        fontSize: 22,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 16,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 16,
        width: '20%',
    },
    modalSaveButton: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    },
    tableSlotDisplay: {
        width: Dimensions.get('window').width * 0.12,
        height: Dimensions.get('window').width * 0.12,
        margin: 12,
        borderRadius: 8,
        padding: Dimensions.get('window').width * 0.01,
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyTableDisplay: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.12,
        height: Dimensions.get('window').width * 0.12,
        margin: 10,
        //borderRadius: windowWidth * 0.02,
        padding: Dimensions.get('window').width * 0.01,
    },
    tableCode: {
        fontFamily: 'NunitoSans-Bold',
        fontSize: 18,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },

    addBtn: {
        backgroundColor: Colors.primaryColor,
        width: 45,
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tabletRnPickerViewStyle: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        alignItems: 'flex-start',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: -1,

        width: '80%',
    },

    keypadBox: {
        width: '33%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },

    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logoTxt: {
        color: Colors.whiteColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
    },
    loginTxt: {
        color: Colors.mainTxtColor,
        fontWeight: "600",
        fontSize: 30
    },
    description: {
        color: Colors.descriptionColor,
        paddingVertical: 10
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 20,
    },
    checkBox: {
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: Colors.descriptionColor,
        width: 30,
        height: 30,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    floatbtn: {
        zIndex: 1,
        position: 'absolute',
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3
    },
    loginImg: {
        width: undefined,
        height: '100%',
        resizeMode: 'cover'
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
    },
    loginContainer: {
        backgroundColor: Colors.whiteColor,
        flex: 1
    },
    shiftView: {
        alignSelf: 'center',
        justifyContent: 'center',
        flex: 0.4,
        borderRadius: 12,
    },
    shiftButton: {
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 30,
        paddingHorizontal: 60,
        paddingVertical: 15,
    },
    shiftText: {
        fontSize: 25,
        color: Colors.fieldtTxtColor
    },
    logoContainer: {
        backgroundColor: Colors.darkBgColor,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
    },
    pinBox: {
        backgroundColor: Colors.fieldtBgColor,
        // width: 60,
        // height: 60,
        width: Dimensions.get('window').width * 0.045,
        height: Dimensions.get('window').width * 0.045,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 5,

        borderColor: Colors.secondaryColor,
        borderWidth: 0.5,
    },
    pinContainer: {
        flexDirection: 'row',
        // paddingTop: 25,
        justifyContent: 'space-between'
    },
    pinBtn: {
        backgroundColor: Colors.fieldtBgColor,
        // width: 70,
        // height: 70,
        width: Dimensions.get('window').width * 0.05,
        height: Dimensions.get('window').width * 0.05,
        marginBottom: !isTablet() ? 6 : 16,
        alignContent: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        borderColor: Colors.primaryColor,
        borderWidth: 0.5,
    },
    pinNo: {
        // fontSize: 25,
        fontSize: 0.35 * (Dimensions.get('window').width * 0.04),
        fontWeight: 'bold',
    },
    modalTitle: {
        alignItems: 'center',
    },
    modalBody: {
        // flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.25,
        width: Dimensions.get('window').width * 0.35,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        paddingHorizontal: Dimensions.get('window').width * 0.015,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalBody: {
        // flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default PinUnlockModal;