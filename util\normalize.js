import { PixelRatio, Dimensions, StyleSheet } from 'react-native';

const ratio = PixelRatio.get();

const normalize = (size) => {
  const { width, height } = Dimensions.get('window');
  const aspectRatio = height / width;

  let newSize = size;

  if (typeof size === 'string' && size.includes('%')) {
    const percentage = parseFloat(size) / 100;
    newSize = Math.round(width * percentage);
  }

  if (ratio >= 2 && ratio < 3) {
    if (width < 360) {
      newSize *= 0.8; // scale for small width
    } else if (width >= 360 && width < 768) {
      newSize *= 0.9; // scale for medium width (mobile)
    } else if (width >= 768 && width < 1024) {
      newSize *= 1.1; // scale for large width (tablet)
    } else if (aspectRatio >= 1.8 && aspectRatio <= 2) {
      newSize *= 1.2; // scale for aspect ratio between 1.8 and 2
    }
  } else if (ratio >= 3 && ratio < 3.5) {
    if (width < 360) {
      newSize *= 0.8; // scale for small width
    } else if (width >= 360 && width < 768) {
      newSize *= 0.9; // scale for medium width (mobile)
    } else if (width >= 768 && width < 1024) {
      newSize *= 1.1; // scale for large width (tablet)
    } else if (aspectRatio >= 1.8 && aspectRatio <= 2) {
      newSize *= 1.2; // scale for aspect ratio between 1.8 and 2
    }
  } else if (ratio >= 3.5) {
    if (width < 360) {
      newSize *= 0.8; // scale for small width
    } else if (width >= 360 && width < 768) {
      newSize *= 0.9; // scale for medium width (mobile)
    } else if (width >= 768 && width < 1024) {
      newSize *= 1.1; // scale for large width (tablet)
    } else if (aspectRatio >= 1.8 && aspectRatio <= 2) {
      newSize *= 1.2; // scale for aspect ratio between 1.8 and 2
    }
  }

  if (typeof size === 'string' && size.includes('%')) {
    return `${newSize}%`;
  }

  return newSize;
};

export const create = (
    styles,
    targetProperties = [
      'fontSize',
      'margin',
      'marginHorizontal',
      'marginVertical',
      'padding',
      'paddingVertical',
      'paddingHorizontal',
    //   'height',
       'width',
    ]
  ) => {
    const normalizedStyles = {};
    Object.keys(styles).forEach((key) => {
      normalizedStyles[key] = {};
      Object.keys(styles[key]).forEach((property) => {
        if (targetProperties.includes(property)) {
          normalizedStyles[key][property] = normalize(styles[key][property]);
        } else {
          normalizedStyles[key][property] = styles[key][property];
        }
      });
    });
  
    return StyleSheet.create(normalizedStyles);
  };

export default normalize;
